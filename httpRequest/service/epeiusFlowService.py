# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/12/14 14:43 
# <AUTHOR> z<PERSON><PERSON> 
# @File : epeiusFlowService.py
import jmespath

from httpRequest.api.apiWorkFlow import ApiWorkFlow
from httpRequest.response.epeiusResp import <PERSON>peiusResp
from httpRequest.schemas.workFlowSchema import FlowCompleteIn, FlowTerminateIn, FlowWithdrawIn, EpeiusRedirectIn, \
    WorkFlowData, WorkFlowOut
from utils import log


class FlowHandleService:
    def __init__(self, envType):
        self.epeiusResp = EpeiusResp(ApiWorkFlow(envType))

    def handle(self, processId, handleType, auditToNode=None):
        """
        流程处理
        :param processId:
        :param auditToNode:
        :param handleType: complete-同意、terminate-拒绝、withdraw-撤回
        :return:
        """
        processInfo: dict = self.epeiusResp.getOneProcessInstance(processId)
        processStatus = processInfo.get('processStatus')
        if processStatus == 'PENDING':
            taskId = self.epeiusResp.flowTaskId(processId)
            taskInfo: dict = self.epeiusResp.getOneTask(taskId)
            if auditToNode:
                # 审批至某个节点名停止
                taskName: str = taskInfo.get('taskName')
                if taskName.startswith(str(auditToNode)):
                    log.info("审批到 %s 节点，停止继续审批。", taskName)
                    # 结束外层循环调用
                    return 'audit to node break'
            candidateUser = jmespath.search('candidateUsers[0]', taskInfo)

            startAccountId = processInfo.get('startAccountId')
            if handleType == 'complete':
                # 审批完成
                flowCompleteIn: FlowCompleteIn = FlowCompleteIn(assignee=candidateUser,
                                                                processInstanceId=processId,
                                                                taskId=taskId)
                self.epeiusResp.epeiusComplete(flowCompleteIn)

            elif handleType == 'terminate':
                # 审批拒绝
                flowTerminateIn: FlowTerminateIn = FlowTerminateIn(assignee=candidateUser,
                                                                   processInstanceId=processId,
                                                                   taskId=taskId)
                self.epeiusResp.epeiusTerminate(flowTerminateIn)

            elif handleType == 'withdraw':
                # 审批撤回
                flowWithdrawIn: FlowWithdrawIn = FlowWithdrawIn(assignee=startAccountId,
                                                                processInstanceId=processId)
                self.epeiusResp.epeiusWithdraw(flowWithdrawIn)
            else:
                raise Exception("处理类型错误: %s" % handleType)
            processInfo: dict = self.epeiusResp.getOneProcessInstance(processId)
            processStatus = processInfo.get('processStatus')
        log.info("processStatus: %s", processStatus)
        return processStatus

    def taskRedirect(self, processId, toAssignee, envType):
        """
        任务转交
        :param envType: prod 为线上环境，只能处理客户名称包含esign的客户
        :param toAssignee: 被转交人
        :param processId:
        :return:
        """
        taskId = self.epeiusResp.flowTaskId(processId)
        taskInfo: dict = self.epeiusResp.getOneTask(taskId)
        assignee = jmespath.search('candidateUsers[0]', taskInfo)
        taskName = taskInfo.get('taskName')
        taskStatus = taskInfo.get("status")
        processName = jmespath.search('flowInfo.processInstanceName', taskInfo)
        workFlowOut = WorkFlowOut()

        if envType.startswith('pro') and 'esign' not in processName:
            workFlowOut.message = "%s processName名称不包含esign，线上环境非测试客户禁止脚本操作！" % processName
            return workFlowOut
        if taskStatus != "TODO" or taskName.startswith('抄送'):
            workFlowOut.message = "%s 节点已处理完成，或是抄送节点，无需转交" % taskName
            return workFlowOut
        if assignee == toAssignee:
            workFlowOut.message = "%s 节点当前处理人 %s 与被转交人相同，无需转交" % (taskName, assignee)
            return workFlowOut
        epeiusRedirectIn = EpeiusRedirectIn(assignee=assignee,
                                            processInstanceId=processId,
                                            taskId=taskId,
                                            toAssignee=toAssignee)
        workFlowData = WorkFlowData(assignee=assignee,
                                    taskName=taskName,
                                    toAssignee=toAssignee,
                                    taskStatus=taskStatus)
        workFlowOut.data = workFlowData
        self.epeiusResp.epeiusRedirect(epeiusRedirectIn)
        return workFlowOut
