# -*- coding: utf-8 -*- 
# @Description : 请求公有云api的具体实现
# @Time : 2023/4/20 17:45
# <AUTHOR> wenmin
from datetime import time

from httpRequest.api.apiCloud import ApiCloud, getContenMd5, getSignature
from httpRequest.response.cloudResp import CloudResp
from utils import log


def createOpenHeadersGet(url, appId, appSecret, method, requestBody=None):
    """
    创建公有云开放网关的get请求头信息
    :param url:
    :param requestBody:
    :param appId:
    :param appSecret:
    :param method:
    :return: headers
    """
    accept = "*/*"
    headers = {
        "X-Tsign-Open-App-Id": appId,
        "X-Tsign-Open-Auth-Mode": "Signature",
        "Accept": accept
    }
    if method == 'GET':
        headers['X-Tsign-Open-Auth-Mode'] = 'simple'
    else:
        contentMd5 = getContenMd5(requestBody)
        headers['X-Tsign-Open-Ca-Signature'] = getSignature(contentMd5, url, appSecret, method)
        headers['Content-MD5'] = contentMd5
    return headers

##########################数据工厂专用请求头###########################################
def createDataFactroyHeaders(cloudAppId):
    """
    数据工厂接口专用的请求头信息
    :param cloudAppId:
    :return: headers
    """
    headers = {}
    if cloudAppId.startswith('4'):
        headers['myenvironment'] = "simulation"
    else:
        headers['myenvironment'] = "test"
    return headers

class CloudService:
    headers = {
        "X-Tsign-Open-Auth-Mode": "simple",
        "X-Tsign-Service-GROUP": "DEFAULT"
    }

    def __init__(self, baseUrl):
        self._cloudResp = CloudResp(ApiCloud(baseUrl))

    def createToken(self, cloudAppId, mobile):
        """
        获取saas-PC的登录token
        :param cloudAppId:
        :param mobile:
        :return: token
        """
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        token = self._cloudResp.userCommitLoginResp(headers, mobile)
        code = token[2]
        if code == 1561501:
            self.loginPwdReset(cloudAppId,mobile)
            self.createToken(cloudAppId,mobile)
        return token

    def createSaasWebLoginHeaders(self, cloudAppId, mobile):
        """
        获取saas-PC的登录的头信息
        :param cloudAppId:
        :param mobile:
        :return: headers
        """
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        headers['X-Tsign-Service-Id'] = "footstone-oauth"
        if mobile:
            token = self._cloudResp.userCommitLoginResp(headers, mobile)
            code = token[2]
            if code == 1561501:
                self.loginPwdReset(cloudAppId, mobile)
                self.createSaasWebLoginHeaders(cloudAppId,mobile)
            if code == 1600001:
                self.loginPwdReset(cloudAppId, mobile)
                raise Exception('SAAS-PC登录失败')
            headers['cookie'] = token[0]
            headers['oid'] = token[1]
            log.info('headers %s', headers)
        return headers

    def loginPwdReset(self, cloudAppId, mobile):
        """
        saas-PC的账号重置标准签的登录密码
        :param cloudAppId:
        :param mobile:
        :return:
        """
        headers = self.createSaasWebLoginHeaders(cloudAppId, "")
        challenge = self._cloudResp.v3ApplyResq(headers, mobile)
        serviceId01 = self._cloudResp.loginResetMobile(headers, mobile, challenge)
        serviceId02 = self._cloudResp.loginResetVerifySender(headers, serviceId01)
        code = self._cloudResp.loginResetPwd(headers, mobile, serviceId02)
        if code ==0 :
            log.info('密码就是：abc_123456,不用修改或是修改成功')
            return code

    def identityInfoPsnService(self, cloudAppId, mobile):
        """
        saas个人账号查询
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 实名状态查询
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        realNameRes = self._cloudResp.identityInfoPsnResp(headers, mobile)
        realNameStatus = realNameRes[1]
        psnId = realNameRes[0]
        return psnId,realNameStatus

    def identityInfoOrgService(self, cloudAppId, orgName):
        """
        saas企业账号查询
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 实名状态查询
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        realNameRes = self._cloudResp.identityInfoOrgResp(headers, orgName)
        realNameStatus = realNameRes[1]
        orgId = realNameRes[0]
        return orgId,realNameStatus

    def resetRealName(self, cloudAppId, mobile):
        """
        重置标准签个人账号的实名和授权状态
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 通过手机号重置实名
        headers = self.headers
        headers.update(createDataFactroyHeaders(cloudAppId))
        oid = self._cloudResp.resetIdentityResp(headers, mobile)

    def telecom3OidService(self, cloudAppId, mobile):
        """
        # 通过手机号三要素实名
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 通过手机号三要素实名
        headers = self.headers
        headers.update(createDataFactroyHeaders(cloudAppId))
        accountOid = self._cloudResp.resetIdentityResp(headers, mobile)
        realNameStatus = self._cloudResp.telecom3OidResp(headers, cloudAppId, accountOid, mobile)
        return accountOid,realNameStatus

    def threeFactorsOidService(self, cloudAppId, mobile, orgName):
        """
        # 通过企业oid打款实名
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 通过手机号三要素实名
        headers = self.headers
        headers.update(createDataFactroyHeaders(cloudAppId))
        accountOid = self._cloudResp.resetIdentityResp(headers, mobile)
        orgOid = self._cloudResp.resetOrgIdentityResp(headers , orgName)
        self._cloudResp.telecom3OidResp(headers, cloudAppId, accountOid, mobile)
        realNameStatus = self._cloudResp.threeFactorsOidResp(headers, cloudAppId, accountOid, orgOid)
        return accountOid,orgOid,realNameStatus

    def psnAuthService(self, cloudAppId, mobile):
        """
        标准签个人授权地址
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 通过手机号重置实名
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        res = self._cloudResp.psnAuthUrlResp(headers , mobile)
        authFlowId = res[0]
        if authFlowId:
            return authFlowId

    def orgAuthService(self, cloudAppId, mobile, orgId):
        """
        标准签个人授权地址
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 通过手机号重置实名
        headers = self.headers
        headers['X-Tsign-Open-App-Id'] = cloudAppId
        res = self._cloudResp.orgAuthUrlResp(headers , mobile, orgId)
        authFlowId = res[0]
        if authFlowId:
            return authFlowId

    def confirmAuth(self, cloudAppId, mobile, authFlowId):
        """
        标准签个人账号实名
        :param cloudAppId:
        :param mobile:
        :return:
        """
        # 根据授权url去提交授权申请
        headers = self.createSaasWebLoginHeaders(cloudAppId,mobile)
        oid = headers.get("oid")
        res1 = self._cloudResp.confirmAuthResp(headers, authFlowId, oid, cloudAppId)
        authFlowId = res1[0]
        authUrl = res1[1]
        authShortUrl = res1[2]
        if authUrl.__contains__("authFlowId"):
            log.info('authFlowIdxxxxx %s', authFlowId)
        else:
            willAuthParam = authUrl.split('willAuthParam=',1)[1]
            # 获取意愿地址
            sessionId = self._cloudResp.willAuthResp(willAuthParam)
            # 完成意愿
            headers["sessionId"] = sessionId
            res2 = self._cloudResp.pwdAuthResp(headers)
            if res2 == 1400400:
                #重置saas账号的签署密码，密码为bbbb1111
                self.signPwdResetService(headers)
            # 更新授权信息
            res3 = self._cloudResp.authFlowProcessResp(headers,authFlowId)
            if res3 ==0:
                log.info('更新授权流程完成并成功')
                return oid,res3

    def signPwdResetService(self, headers):
        """
        重置saas账号的签署密码
        :param headers:
        :return: code
        """
        # 重置saas账号的签署密码，密码为bbbb1111
        orgiginAuthApplyRes = self._cloudResp.orgiginAuthApplyResp(headers,
                                                                   "229a729b82ce06972795ed71c00f1dcb")
        code01 = orgiginAuthApplyRes[1]
        if code01 == 1580022:
            orgiginAuthApplyRes = self._cloudResp.orgiginAuthApplyResp(headers,None)
        serviceId = orgiginAuthApplyRes[0]
        self._cloudResp.signPwdResetResp(headers, serviceId)
        self._cloudResp.pwdAuthResp(headers)
        if code01 == 0 :
            log.info('重置签署密码成功')
            return code01

    def cancelAuthService(self, cloudAppId, mobile):
        """
        取消授权
        :param cloudAppId:
        :param mobile:
        :return: code
        """
        # 重置saas账号的签署密码，密码为bbbb1111
        headers = self.createSaasWebLoginHeaders(cloudAppId,mobile)
        code = self._cloudResp.cancelAuthResp(headers,cloudAppId)
        log.info('取消授权')
        return code

    def willSMSService(self, headers,cloudAppId, bizId, mobile):
        """
        公有云意愿-短信意愿
        :param cloudAppId:
        :param bizId:
        :param mobile:
        :return: code
        """
        accountId = headers['oid']
        # 发送验证码
        res = self._cloudResp.createCodeAuthResp(headers, cloudAppId, accountId, bizId)
        willAuthId = res['data']['willAuthId']
        bizId2 = res['data']['bizId']
        res1 = self._cloudResp.verifyCodeAuthResp(headers, bizId2, willAuthId)
        log.info('公有云意愿-短信意愿成功')
        return res1

if __name__ == "__main__":
    cloud_login_url = 'http://ttapi.tsign.cn'
    cloud_h5_url = 'https://testh5.tsign.cn'
    cloud_open_url = 'http://in-test-openapi.tsign.cn'
    data_url = 'http://datafactory.smlk8s.esign.cn'
    cloudAppSecret = "c5ec2796e56f90058e3af3567a4459d4"
    cloudAppId = "**********"
    mobile11 = "***********"
    mobile = "***********"
    orgName = "esigntest混合云全链路一测试公司"
    cloudService3 = CloudService(cloud_login_url)
    cloudService = CloudService(cloud_open_url)
    cloudService2 = CloudService(data_url)
    cloudService.createSaasWebLoginHeaders('**********','***********')
    cloudService.psnAuth('**********','***********')
    cloudService2.threeFactorsOidService('**********','***********',orgName)

    #授权
    # authFlowId = cloudService.psnAuthService(cloudAppId,mobile)
    # cloudService3.confirmAuth(cloudAppId,mobile,authFlowId)

