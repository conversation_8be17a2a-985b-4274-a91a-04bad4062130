# -*- coding: utf-8 -*- 
# @Description : 工作流一建审批
# @Time : 2021/4/7 8:12 下午 
# <AUTHOR> zhuqi 
# @File : workFlowController.py
import datetime

from httpRequest.schemas.workFlowSchema import WorkFlowIn, WorkFlowOut, WorkFlowData
from httpRequest.service.epeiusFlowService import FlowHandleService
from utils import log


def flowHandleController(workFlowIn: WorkFlowIn):
    """
    工作流审批
    :param workFlowIn:
    :return:
    """
    flowHandleService = FlowHandleService(workFlowIn.envType)
    flowHandleService.epeiusResp.apiWorkFlowObj.setEurekaGroup(workFlowIn.eurekaGroup)
    workFlowOut = WorkFlowOut()
    if workFlowIn.envType.startswith('pro'):
        workFlowOut.message = '线上环境非测试客户禁止脚本操作!'
        return workFlowOut
    startTime = datetime.datetime.now()
    timeout = 120
    processStatus = 'PENDING'
    try:
        while processStatus == 'PENDING':
            if (datetime.datetime.now() - startTime).seconds > timeout:
                raise Exception("处理超时！")
            processStatus = flowHandleService.handle(workFlowIn.processId, workFlowIn.auditType, workFlowIn.auditToNode)
        log.info("流程处理结束,status: %s", processStatus)
        workFlowData = WorkFlowData(processId=workFlowIn.processId,
                                    envType=workFlowIn.envType,
                                    eurekaGroup=workFlowIn.eurekaGroup,
                                    auditToNode=workFlowIn.auditToNode,
                                    processStatus=processStatus)
        workFlowOut.data = workFlowData
        return workFlowOut
    except Exception as e:
        workFlowOut.message = "流程异常，请检查流程！%s" % str(e)
        workFlowOut.code = 201
        log.error("流程异常，请检查流程！%s" % str(e))
        return workFlowOut


if __name__ == "__main__":
    eureka_group = ''
    ef = -2
    audit_name = '法务审批'
    process_id = '4ae5ea0c-1dd3-11ed-9c90-7a2b6e385141'
    to_assignee = ''
    audit_type = 'complete'

    workFlow = WorkFlowIn(processId=process_id,
                          auditToNode=audit_name,
                          auditType=audit_type)
    response = flowHandleController(workFlow)
