# -*- coding: utf-8 -*-
# @Description :
# @Time : 2023/2/20 14:10
# <AUTHOR> zhu<PERSON>
# @File : apiHttpBin.py.py

import requests

from utils import log


class EnumMethod(object):
    """ Enum HTTP method    """
    GET, HEAD, POST, PUT, OPTIONS, DELETE \
        = ("GET", "HEAD", "POST", "PUT", "OPTIONS", "DELETE")


class ApiHttpBin(object):
    path = None
    json = {}
    params = {}
    files = None
    cookie = None
    method = None
    data = None

    def __init__(self, baseUrl):
        """
        :param baseUrl: host
        """
        self.baseUrl = baseUrl
        self.__header = {"Content-Type": "application/json"}

    @property
    def url(self):
        """ get response url
        """
        return self.baseUrl + self.path

    @property
    def headers(self):

        if self.files:
            self.__header.pop('Content-Type')
        log.info('headers %s', self.__header)
        return self.__header

    def setEurekaGroup(self, eurekaGroup) -> "ApiHttpBin":
        """
        update response  eurekaGroup
        :param eurekaGroup: 项目标识，有项目标识即为项目环境
        """
        if eurekaGroup:
            setattr(self, 'eurekaGroup', eurekaGroup)
        return self

    def setCookie(self, cookie) -> "ApiHttpBin":
        """ update response  cookie
        """
        if cookie:
            setattr(self, 'cookie', cookie)
        return self

    def setParams(self, params: dict) -> "ApiHttpBin":
        """ update response query params
        """
        self.params.update(params)
        return self

    def setHeaders(self, headers: dict) -> "ApiHttpBin":
        """ update response query headers
        """
        self.__header.update(headers)
        return self

    def setJson(self, json: dict) -> "ApiHttpBin":
        """ update response query body
        """
        self.json.update(json)
        return self

    def run(self):

        response = requests.request(method=self.method, url=self.url, params=self.params, json=self.json,
                                    data=self.data, files=self.files, headers=self.headers)
        log.info("url: %s", self.url)
        log.info("json: %s, params: %s", self.json, self.params)
        if response.status_code != 200:
            raise Exception("请求失败！", response.request.body)
        return response
        # except Exception as e:
        #     logging.error('请求异常 %r，url: %s  ,params: %s ,json: %s', e, self.url, self.params, self.json)
