# -*- coding: utf-8 -*-
# @Description : 请求公有云的接口
# @Time : 2023/4/20 17:05
# <AUTHOR> wenmin
import base64
import hashlib
import hmac
import json
import time

from httpRequest.api.apiHttpBin import *
# from httpRequest.schemas.cloudSchema import AccountLoginIn, LoginIn, SendDynamicCodeIn, ToLoginIn
def getContenMd5(data):
    """
    计算数据的md5值
    :param data:
    :return: md5
    """
    contentMd5Before = json.dumps(data)
    hl = hashlib.md5()
    hl.update(contentMd5Before.encode("UTF-8"))
    contentMd5After = str(base64.b64encode(hl.digest()), "UTF-8")
    return contentMd5After

# 签名
def getSignature(contentMD5, url, appSecret, requestType):
    """
    计算签名
    :param contentMD5:
    :param url:
    :param appSecret:
    :param accept:
    :param contentType:
    :param requestType:
    :return: hash
    """
    date = ""
    headers = ""
    contentType = 'application/json'
    data = requestType \
           + "\n" + "*/*" \
           + "\n" + contentMD5 \
           + "\n" + contentType \
           + "\n" + date \
           + "\n" + headers + url
    appSecret = appSecret.encode('utf-8')  # sha256加密的key
    message = data.encode('utf-8')  # 待sha256加密的内容
    sign = base64.b64encode(hmac.new(appSecret, message, digestmod=hashlib.sha256).digest()).decode()
    return sign

class ApiCloud(ApiHttpBin):

    def apiUserCommitLogin(self, header, mobile):
        """
        saas-pc 用户登录api
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/account-webserver/login/commit/user'
        self.setHeaders(header)
        # self.json = accountLoginIn.dict()
        # 登录密码：abc_123456
        self.json = {"principal":mobile,
                     "credentials":"593f53728678566930be2a982efc5dda",
                     "loginParams":{"endpoint":"PC","env":{"fingerprint":"ed178723575e3cb643e54d53011669af"},"needCheck":False},
                     "data":{"safeCheck":True}}
        return self

# """  重置 saas账号的登录密码 ， 密码固定为abc_123456   """
    def apiV3Apply(self, header, mobile):
        """
        saas-pc 用户发送重置登录密码请求
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/account-webserver/sender/robotAuth/apply/v3'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {"principal": mobile, "client_type": "web"}
        return self

    def apiV3Mobile(self, header, mobile, challenge):
        """
        saas-pc 用户发送短信验证码
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/account-webserver/sender/mobile/v3'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {"bizType":"RESET_PWD","preServiceId":"","principal":mobile,
                     "auth":{"geetest_challenge":challenge,"geetest_validate":"e205c71963de01fd575fbbe7ca6fa34e","geetest_seccode":"e205c71963de01fd575fbbe7ca6fa34e|jordan"}}
        return self

    def apiVerifySender(self, header, serviceId):
        """
        saas-pc 用户发送 验证码校验
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/account-webserver/sender/verify'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {"credentials":"123456","serviceId":serviceId}
        return self

    def apiPwdResetSender(self, header, mobile, serviceId):
        """
        saas-pc 修改密码（与原来密码不能一致） 密码固定设置为：abc_123456
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/account-webserver/sender/pwdReset'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {"encrypter":mobile,"newPassword":"593f53728678566930be2a982efc5dda","serviceId":serviceId}
        return self

# """  重置 saas账号的签署密码 ， 密码固定为 bbbb1111   """
    def apiOriginAuthApply(self, header):
        """
        saas-pc 发送重置签署密码请求
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver/v1/sender/sys_accountId/originAuthApply'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {"bizType":"RESET_SIGN_PWD","password":"229a729b82ce06972795ed71c00f1dcb"}
        return self

#######################saas页面接口#############################################
    def apiConfirmAuth(self, header, authFlowId, authOid, cloudAppId):
        """
        确认saas授权confirm
        :param header:
        :param serviceId: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver/v3/oauth/confirm-auth'
        self.setHeaders(header)
        # 登录密码：abc_123456
        self.json = {
            "authFlowId": authFlowId,
            "authOid": authOid,  # 授权主体:个人-个人oid，企业-企业oid
            "bizAppId": cloudAppId,  # 认证业务appId
            "timestamp": time.time()*1000  # 请求时间戳，单位精确到毫秒
        }
        return self

    def apiWillAuth(self, willAuthParam):
        """
        确认saas授权进行意愿获取意愿地址
        :param header:
        :param serviceId: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.GET
        self.path = '/webserver-will/v1/willingness/login?param=' + willAuthParam
        self.json = {}
        return self

    def apiPwdAuth(self, header):
        """
        完成意愿
        :param header:
        :param serviceId: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver-will/v1/willingness/sys_transId/pwdAuth?transId=sys_transId'
        self.setHeaders(header)
        # 需要事先设置密码为：bbbb1111
        self.json = {"password": "229a729b82ce06972795ed71c00f1dcb"}
        return self

    def apiAuthFlowProcess(self, header, authFlowId):
        """
        更新授权状态
        :param header:
        :param serviceId: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.GET
        self.path = "/webserver/v3/pre/oauth/" + authFlowId + "/auth-flow-process?&authFlowId=" + authFlowId
        self.setHeaders(header)
        self.json = {}
        return self

    def apiOrgiginAuthApply(self, header, password=None):
        """
        重置saas账号的签署密码-发送重置密码申请
        :param header:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver/v1/sender/sys_accountId/originAuthApply'
        self.setHeaders(header)
        # 签署密码为：bbbb1111
        self.json = {"bizType": "RESET_SIGN_PWD", "password": password}
        return self

    def apiSignPwdReset(self, header, serviceId):
        """
        重置saas账号的签署密码
        :param header:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver/sender/signPwdReset'
        self.setHeaders(header)
        # 签署密码为：bbbb1111
        self.json = {"serviceId": serviceId, "password": "229a729b82ce06972795ed71c00f1dcb"}
        return self

    def apiCancelAuth(self, header, cloudAppId):
        """
        取消某个SAAS账号的cloudAppId的授权信息
        :param header:
        :param cloudAppId:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/webserver/v3/oauth/cancel-auth'
        self.setHeaders(header)
        self.json = {"appId": cloudAppId}
        return self

    def apiCreateCodeAuth(self, header, cloudAppId, accountId, bizId):
        """
        公有云意愿认证页面发送短信验证码
        :param header:
        :param cloudAppId:
        :param accountId: saas的oid
        :param bizId:  意愿请求的applyId
        :return:
        """
        self.method = EnumMethod.POST
        self.path = '/v1/willingness/createCodeAuth'
        self.setHeaders(header)
        self.json = {"appId": cloudAppId,"accountId": accountId,"bizId": bizId,"bizType": "SIGN","sendType": "SMS"}
        return self

    def apiVerifyCodeAuth(self, header, bizId, willAuthId):
        """
        公有云意愿认证页面短信验证
        :param header:
        :param bizId:
        :param willAuthId:
        :return:
        """
        self.method = EnumMethod.PUT
        self.path = '/v1/willingness/verifyCodeAuth'
        self.setHeaders(header)
        self.json = {"authCode": "123456","willAuthId": willAuthId,"bizId": bizId,"bizType": "SIGN"}
        return self

#########################公有云开放网关接口#############################################
    def apiIdentityInfoPsn(self, header, mobile):
        """
        saas-pc 查询标准签个人账号实名状态
        :param header:
        :param mobile: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.GET
        self.path = '/v3/persons/identity-info'
        self.params = {"psnAccount":mobile}
        self.setHeaders(header)
        return self

    def apiIdentityInfoOrg(self, header, orgName):
        """
        saas-pc 查询标准签企业账号实名状态
        :param header:
        :param orgName: 请求体
        :return: ApiCloud
        """
        self.method = EnumMethod.GET
        self.path = '/v3/organizations/identity-info'
        self.params = {"orgName":orgName}
        self.setHeaders(header)
        return self

    def apiPsnAuthUrl(self, header, mobile):
        """
        查询SAAS标准签个人账号实名状态
        :param header:
        :param mobile: 个人手机号
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/v3/psn-auth-url'
        self.setHeaders(header)
        self.json = {"authorizeConfig": {"authorizedScopes": ["get_psn_identity_info", "manage_psn_resource"]},
                "notifyUrl": "", "psnAuthConfig": {"psnAccount": mobile}, "repeatableRealName": False}
        return self

    def apiOrgAuthUrl(self, header, mobile, orgId):
        """
        查询SAAS标准签企业账号实名状态
        :param header:
        :param mobile: 个人手机号
        :param orgId: 企业ouid
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/v3/org-auth-url'
        self.setHeaders(header)
        self.json = {"authorizeConfig": {
        "authorizedScopes": ["get_org_identity_info", "get_psn_identity_info", "manage_org_resource",
                             "manage_psn_resource","get_org_seal","get_psn_seal"]},
        "orgAuthConfig": {"orgName": "", "orgId": orgId, "orgInfo": {"orgIDCardType": "", "orgIDCardNum": ""},
                          "transactorInfo": {"psnId": "", "psnAccount": mobile}}, "repeatableRealName": False}
        return self
###########################数据工厂的接口###############################
    def apiResetIdentityDF(self, headers, mobile):
        """
        重置saas个人实名状态
        :param cloudAppId:
        :param mobile:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/sign/resetIdentity/'
        self.setHeaders(headers)
        self.json = {"principal": mobile}
        return self

    def apiResetOrgIdentityDF(self, headers, orgName):
        """
        重置saas企业实名状态
        :param cloudAppId:
        :param mobile:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/sign/resetGIdentity/'
        self.setHeaders(headers)
        self.json = {"principal": orgName}
        return self

    def apiTelecom3Oid(self, headers, cloudAppId, accountOid, mobile):
        """
        saas企业账号三要素实名
        :param cloudAppId:
        :param accountOid:
        :param mobile:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/realname/telecom3Oid/'
        self.setHeaders(headers)
        self.json = {"accountId": accountOid, "input_appid": cloudAppId, "mobile": mobile}
        return self

    def apiThreeFactorsOid(self, headers, cloudAppId, accountOid, orgAccountId):
        """
        saas企业账号对公打款实名
        :param cloudAppId:
        :param accountOid:
        :param orgAccountId:
        :return: ApiCloud
        """
        self.method = EnumMethod.POST
        self.path = '/realname/threeFactorsOid/'
        self.setHeaders(headers)
        self.json = {"agentAccountId": accountOid, "input_appid": cloudAppId, "orgAccountId": orgAccountId}
        return self



