# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/12/19 10:55 
# <AUTHOR> zhu<PERSON> 
# @File : apiEsSearch.py
from httpRequest.api.apiHttpBin import *


class ApiEsSearch(ApiHttpBin):

    def apiEsSearch(self, searchConditions=None, processId=None):
        """
        所有流程列表查询,根据流程id查询
        param process_id: 流程id
        """
        self.method = EnumMethod.POST
        self.path = "/gaea/esSearch/search"
        defaultConditions = {
            "code": "flowable_base_query",
            "currentPage": 0,
            "pageSize": 10,
            "paramMap": {
                "sortType": "DESC",
                "sortKey": "startTime",
                "processVariables": "",
                "processInstanceId": processId,
                "processInstanceName": "",
                "startAccountId": "",
                "processStatus": ""
            }
        }
        if isinstance(searchConditions, dict):
            defaultParamMap = defaultConditions.get('paramMap')
            searchParamMap: dict = searchConditions.get('paramMap', {})
            if searchConditions.get('code') == 'flowable_task_query':
                # 任务查询，默认排序为创建时间
                defaultParamMap['sortKey'] = 'createTime'
            if searchParamMap:
                searchParamMap.update(defaultParamMap)
                searchConditions['paramMap'] = searchParamMap
            defaultConditions.update(searchConditions)

        self.json = defaultConditions
        return self
