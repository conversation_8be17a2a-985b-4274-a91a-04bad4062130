# -*- coding: utf-8 -*- 
# @Description : 公有云单接口调用
# @Time : 2023/4/20 14:52
# <AUTHOR> wenmin
import base64
import hashlib
import hmac
import json
from datetime import time

import jmespath
from httpRequest.api.apiCloud import ApiCloud
from utils import log


class CloudResp:
    def __init__(self, apiCloud: ApiCloud):
        self._apiCloud = apiCloud

    def userCommitLoginResp(self, header, mobile):
        """
        SAAS-PC用户登录
        :param header:
        :param mobile: 公有云用户的登录手机号
        :return:
        """
        response = self._apiCloud.apiUserCommitLogin(header, mobile).run()
        cookie = response.headers["Set-Cookie"]
        resJson = response.json()
        ouid = jmespath.search('ouid', resJson["data"])
        code = resJson["code"]
        log.info('response %s', resJson)
        log.info('cookie %s', cookie)
        log.info('ouid %s', ouid)
        if cookie:
            return cookie , ouid, code
        raise Exception("SAAS-PC用户登录失败！%s" % response)

    def v3ApplyResq(self, header, mobile):
        """
        SAAS-PC用户重置登录密码发送申请
        :param header:
        :param mobile: 公有云用户的登录手机号
        :return:
        """
        response = self._apiCloud.apiV3Apply(header, mobile).run()
        resJson = response.json()
        log.info('resJsonxxxx %s', resJson)
        resData = resJson["data"]
        challenge = None
        if resData:
            challenge = jmespath.search('challenge', resData)
            gt = jmespath.search('gt', resData)
            log.info('challenge %s', challenge)
            log.info('gt %s', gt)
        return challenge
        raise Exception("SAAS-PC用户申请重置密码，申请失败！%s" % response)

    def loginResetMobile(self, header, mobile, challenge):
        """
        SAAS-PC用户重置登录密码发送申请后需要触发验证码
        :param header:
        :param mobile: 公有云用户的登录手机号
        :param challenge: loginResetApply()的出参
        :return:
        """
        response = self._apiCloud.apiV3Mobile(header, mobile, challenge).run()
        resJson = response.json()
        resData = resJson["data"]
        serviceId = jmespath.search('serviceId', resData)
        log.info('serviceId %s', serviceId)
        if serviceId:
            return serviceId
        raise Exception("SAAS-PC用户重置登录密码发送申请后需要触发验证码，申请验证码失败！%s" % response)

    def loginResetVerifySender(self, header, serviceId):
        """
        SAAS-PC用户重置登录密码时的验证码校验
        :param header:
        :param serviceId: loginResetMobile()的出参信息
        :return:
        """
        response = self._apiCloud.apiVerifySender(header, serviceId).run()
        resJson = response.json()
        resData = resJson["data"]
        serviceId01 = jmespath.search('serviceId', resData)
        log.info('serviceId01 %s', serviceId01)
        if serviceId01:
            return serviceId01
        raise Exception("SAAS-PC用户重置登录密码时的验证码校验失败！%s" % response)

    def loginResetPwd(self, header, mobile, serviceId):
        """
        SAAS-PC用户重置登录
        :param header:
        :param mobile:
        :param serviceId: loginResetVerifySender()的出参信息
        :return:
        """
        response = self._apiCloud.apiPwdResetSender(header, mobile, serviceId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('loginResetPwd-[response] %s', resJson)
        log.info('code %s', code)
        if code == 1562006 or code == 0:
            return 0
        else:
            return 1
        raise Exception("SAAS-PC用户重置密码失败！%s" % response)
########################################################################
    def resetIdentityResp(self, header, mobile):
        """
        通过数据工厂重置SAAS标准签账号的用户实名状态
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiResetIdentityDF(header, mobile).run()
        resJson = response.json()
        account = resJson["data"]["account"]
        log.info('resetIdentityResp-[response] %s', resJson)
        if account:
            return account
        raise Exception("接口调用异常：重置标准签下个人实名失败！%s" % response)

    def resetOrgIdentityResp(self, header, orgName):
        """
        通过数据工厂重置SAAS标准签账号的用户实名状态
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiResetOrgIdentityDF(header, orgName).run()
        resJson = response.json()
        if resJson["data"]["total"] != 0:
            account = resJson["data"]["ouid"]
        log.info('resetOrgIdentityResp-[response] %s', resJson)
        if account:
            return account
        raise Exception("接口调用异常：重置标准签下企业实名失败！%s" % response)

    def telecom3OidResp(self, headers, cloudAppId, accountOid, mobile):
        """
        通过数据工厂进行SAAS标准签个人账号的三要素实名
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiTelecom3Oid(headers, cloudAppId, accountOid, mobile).run()
        resJson = response.json()
        realnameStatus = resJson["data"]["realnameStatus"]
        log.info('telecom3OidResp-[response] %s', resJson)
        if realnameStatus:
            return realnameStatus
        raise Exception("通过数据工厂进行SAAS标准签个人账号的三要素实名失败！%s" % response)

    def threeFactorsOidResp(self, headers, cloudAppId, accountOid, orgAccountId):
        """
        通过数据工厂进行SAAS标准签企业账号的实名
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiThreeFactorsOid(headers, cloudAppId, accountOid, orgAccountId).run()
        resJson = response.json()
        realnameStatus = resJson["data"]["realnameStatus"]
        log.info('threeFactorsOidResp-[response] %s', resJson)
        if realnameStatus:
            return realnameStatus
        raise Exception("通过数据工厂进行SAAS标准签企业账号的实名失败！%s" % response)

    def identityInfoPsnResp(self, header, mobile):
        """
        查询SAAS标准签账号的实名状态
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiIdentityInfoPsn(header, mobile).run()
        resJson = response.json()
        resData = resJson["data"]
        realnameStatus = jmespath.search('realnameStatus', resData)
        psnId = jmespath.search('psnId', resData)
        log.info('identityInfoPsnResp-[response] %s', resJson)
        if psnId:
            return psnId,realnameStatus
        raise Exception("查询SAAS标准签账号的实名状态失败！%s" % response)

    def identityInfoOrgResp(self, header, orgName):
        """
        查询SAAS标准签账号的实名状态
        :param header:
        :param mobile:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiIdentityInfoOrg(header, orgName).run()
        resJson = response.json()
        resData = resJson["data"]
        realnameStatus = jmespath.search('realnameStatus', resData)
        orgId = jmespath.search('orgId', resData)
        log.info('identityInfoOrgResp-[response] %s', resJson)
        if orgId:
            return orgId,realnameStatus
        else:
            return resJson["code"],resJson["message"]
        raise Exception("查询SAAS标准签账号的实名状态失败！%s" % response)

    def psnAuthUrlResp(self, header,mobile):
        """
        给SAAS标准签个人账号授权申请
        :param header:
        :param mobile:
        :return:
        """
        response = self._apiCloud.apiPsnAuthUrl(header, mobile).run()
        resJson = response.json()
        resData = resJson["data"]
        authFlowId = jmespath.search('authFlowId', resData)
        authUrl = jmespath.search('authUrl', resData)
        authShortUrl = jmespath.search('authShortUrl', resData)
        log.info('psnAuthUrlResp-[response] %s', resJson)
        if authFlowId:
            return authFlowId,authUrl,authShortUrl
        raise Exception("给SAAS标准签个人账号授权申请失败！%s" % response)

    def orgAuthUrlResp(self, header,mobile, orgId):
        """
        给SAAS标准签企业账号授权申请
        :param header:
        :param mobile:
        :return:
        """
        response = self._apiCloud.apiOrgAuthUrl(header, mobile, orgId).run()
        resJson = response.json()
        resData = resJson["data"]
        authFlowId = jmespath.search('authFlowId', resData)
        authUrl = jmespath.search('authUrl', resData)
        authShortUrl = jmespath.search('authShortUrl', resData)
        log.info('orgAuthUrlResp-[response] %s', resJson)
        if authFlowId:
            return authFlowId,authUrl,authShortUrl
        raise Exception("给SAAS标准签企业账号授权申请失败！%s" % response)

    def cancelAuthResp(self, header, cloudAppId):
        """
        取消授权
        :param header:
        :param cloudAppId:
        :return:
        """
        response = self._apiCloud.apiCancelAuth(header, cloudAppId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('cancelAuthResp-[response] %s', resJson)
        return code
        raise Exception("取消授权失败失败！%s" % response)

    def confirmAuthResp(self, header, authFlowId, authOid, cloudAppId):
        """
        确认saas授权confirm
        :param header:
        :param mobile:
        :return:
        """
        response = self._apiCloud.apiConfirmAuth(header, authFlowId, authOid, cloudAppId).run()
        resJson = response.json()
        resData = resJson["data"]
        authUrl = jmespath.search('authUrl', resData)
        authFlowId = jmespath.search('authFlowId', resData)
        authShortUrl = jmespath.search('authShortUrl', resData)
        log.info('confirmAuth-[response] %s', resJson)
        if authFlowId:
            return authFlowId,authUrl,authShortUrl
        raise Exception("给SAAS标准签账号授权申请失败！%s" % response)

    def willAuthResp(self, willAuthParam):
        """
        确认saas授权进行意愿获取意愿地址
        :param header:
        :param mobile:
        :return:
        """
        response = self._apiCloud.apiWillAuth(willAuthParam).run()
        resJson = response.json()
        resData = resJson["data"]
        sessionId = jmespath.search('sessionId', resData)
        log.info('willAuthResp-[response] %s', resJson)
        if sessionId:
            return sessionId
        raise Exception("确认saas授权进行意愿获取意愿地址失败！%s" % response)

    def pwdAuthResp(self, header):
        """
        完成意愿
        :param header:
        :return:
        """
        response = self._apiCloud.apiPwdAuth(header).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('pwdAuthResp-[response] %s', resJson)
        return code
        raise Exception("完成意愿失败！%s" % response)

    def authFlowProcessResp(self, header, authFlowId):
        """
        更新授权状态
        :param header:
        :return:
        """
        response = self._apiCloud.apiAuthFlowProcess(header, authFlowId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('authFlowProcessResp-[response] %s', resJson)
        return code
        raise Exception("更新授权状态失败！%s" % response)

    def orgiginAuthApplyResp(self, header, password):
        """
        重置saas账号的签署密码-发送重置密码申请
        :param header:
        :return:
        """
        # password = "229a729b82ce06972795ed71c00f1dcb"
        serviceId = ""
        response = self._apiCloud.apiOrgiginAuthApply(header,password).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('OrgiginAuthApplyResp-[response] %s', resJson)
        if code == 0:
            serviceId = resJson["data"]["serviceId"]
        return serviceId,code
        raise Exception("重置saas账号的签署密码-发送重置密码申请失败！%s" % response)

    def signPwdResetResp(self, header, serviceId):
        """
        重置saas账号的签署密码
        :param header:
        :return:
        """
        response = self._apiCloud.apiSignPwdReset(header, serviceId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('signPwdResetResp-[response] %s', resJson)
        return code
        raise Exception("重置saas账号的签署密码失败！%s" % response)

    def createCodeAuthResp(self, header, cloudAppId, accountId, bizId):
        """
        公有云意愿认证页面发送短信验证码
        :param header:
        :param cloudAppId:
        :param accountId:
        :param bizId:
        :return:
        """
        response = self._apiCloud.apiCreateCodeAuth(header, cloudAppId, accountId, bizId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('createCodeAuthResp-[response] %s', resJson)
        return resJson
        raise Exception("公有云意愿认证页面发送短信验证码失败！%s" % response)

    def verifyCodeAuthResp(self, header, bizId, willAuthId):
        """
        公有云意愿认证页面短信验证
        :param header:
        :param bizId:
        :param willAuthId:  createCodeAuthResp()出参
        :return:
        """
        response = self._apiCloud.apiVerifyCodeAuth(header, bizId, willAuthId).run()
        resJson = response.json()
        code = resJson["code"]
        log.info('verifyCodeAuthResp-[response] %s', resJson)
        return resJson
        raise Exception("公有云意愿认证页面短信验证失败！%s" % response)

if __name__ == "__main__":
    cloud_login_url = 'http://ttapi.tsign.cn'
    # cloudService = CloudService(cloud_login_url)
    # cloudService.createToken('7876637714','19112000013')
