# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2023/2/20 17:52 
# <AUTHOR> <PERSON><PERSON><PERSON> 
# @File : loginResp.py
import jmespath

from httpRequest.api.apiLogin import ApiLogin
from httpRequest.schemas.loginSchema import AccountLoginIn, SendDynamicCodeIn, LoginIn, ToLoginIn
from utils import log


class LoginResp:
    def __init__(self, apiLogin: ApiLogin):
        self._apiLogin = apiLogin

    def accountLogin(self, header, accountLoginIn: AccountLoginIn):
        """
        内部用户账密登录
        :param header:
        :param accountLoginIn: 请求体
        :return:
        """
        response = self._apiLogin.apiAccountLogin(header, accountLoginIn).run().json()
        data = jmespath.search('data', response)
        log.info('response %s', response)
        if data:
            return data
        raise Exception("内部用户登录失败！%s" % response)

    def login(self, header, loginIn: LoginIn):
        """
        内/外部用户手机验证码登录
        :param header:
        :param loginIn: 请求体
        :return:
        """
        response = self._apiLogin.apiLogin(header, loginIn).run().json()
        data = jmespath.search('data', response)
        log.info('response %s', response)
        if data:
            return data
        raise Exception("外部用户登录失败！ %s" % response)

    def toLogin(self, header, toLoginIn: ToLoginIn):
        """
        管理平台登录
        :param header:
        :param toLoginIn: 请求体
        :return:
        """
        response = self._apiLogin.apiToLogin(header, toLoginIn).run().json()
        data0 = jmespath.search('data', response)
        data = jmespath.search('token', data0)
        log.info('response %s', response)
        if data:
            return data
        # raise Exception("管理平台登录失败！ %s" % response)
        return response

    def validateCaptcha(self, header, toLoginIn: ToLoginIn):
        """
        管理平台校验验证码
        :param header:
        :param toLoginIn: 请求体
        :return:
        """
        response = self._apiLogin.apiValidateCaptcha(header, toLoginIn).run().json()
        success = response.get('success', False)
        if not success:
            raise Exception("管理平台验证码校验失败！ %s" % response)

    def sendVerifyCode(self):
        """
        统一门户获取图形验证码，取header的信息
        :param :
        :return:
        """
        response = self._apiLogin.apiSendVerifyCode().run()
        if response.status_code == 200:
            return response
        raise Exception("获取图形验证码失败！%s" % response.content)

    def sendDynamicCode(self, header, sendDynamicCodeIn: SendDynamicCodeIn):
        """
        发送短信验证码
        :param header:
        :param sendDynamicCodeIn: 请求体
        :return:
        """
        response = self._apiLogin.apiSendDynamicCode(header, sendDynamicCodeIn).run()
        if response.status_code == 200:
            return response.headers
        raise Exception("发送短信验证码失败！%s" % response.content)

    def verifyCode(self):
        """
        管理后台获取图形验证码，取header的信息
        :param :
        :return:
        """
        response = self._apiLogin.apiVerifyCode().run()
        if response.status_code == 200:
            return response
        raise Exception("获取图形验证码失败！%s" % response.content)

    def getTokenByCode(self, tCode):
        """
        管理后台获取图形验证码，取header的信息
        :param tCode:
        :return:
        """
        response = self._apiLogin.apiGetTokenByCode(tCode).run().json()
        data = jmespath.search('data', response)
        if data:
            return data
        raise Exception("获取token失败！%s" % response.content)
