# -*- coding: utf-8 -*- 
# @Description : 计费库
# @Time : 2022/4/24 5:43 下午 
# <AUTHOR> zhu<PERSON> 
# @File : baseBilling.py
from typing import List, Union

from sqlalchemy import and_

from httpRequest.database.base import CrudBase
from httpRequest.database.model import BsConsumePoolModel
from utils import log
from utils.dateAndTime import currentDate

TEST_DBNAME = 'base_billing'
SML_DBNAME = 'billing'


class BaseBilling(CrudBase):
    def __init__(self, envType):
        dbName = TEST_DBNAME
        if envType.lower() in ['sml', 'simulation']:
            dbName = SML_DBNAME
        CrudBase.__init__(self, dbName, envType)


class BsConsumePool(BaseBilling):
    def deleteByOrderId(self, orderId):
        """
        删除
        :param orderId:
        :return:
        """
        self._deleteBy_(BsConsumePoolModel, BsConsumePoolModel.order_id == orderId)
        log.info("BsConsumePool删除订单：%s" % orderId)

    def queryByOrderId(self, orderId) -> BsConsumePoolModel:
        """
        :param orderId:
        :return:
        """
        result = self._queryFilterOne(BsConsumePoolModel, BsConsumePoolModel.order_id == orderId)
        return result

    def queryBsConsumePool(self, gid, productId, useScopeType='NOT_STRONG') -> List[BsConsumePoolModel]:
        """
        查询bs_consume_pool表满足消费条件的订单
        结束时间大于当前时间，使用场景为0、4 不隔离和弱隔离，订单为普通订单和赠送订单，状态为生效中和续延
        :param gid:
        :param productId:
        :param useScopeType: IS_STRONG 3 强隔离，NOT_STRONG (0，4) 非强隔离， ALL (0,3,4)
        :return:
        """
        useScope: tuple = (0, 4)
        if useScopeType == 'IS_STRONG':
            useScope = (3,)
        if useScopeType == 'ALL':
            useScope = (0, 3, 4)
        try:
            result = self._queryFilterByAsc(BsConsumePoolModel, BsConsumePoolModel.start_time,
                                            and_(BsConsumePoolModel.gid == gid,
                                                 BsConsumePoolModel.product_id == productId,
                                                 BsConsumePoolModel.end_time > currentDate(),
                                                 BsConsumePoolModel.use_scope.in_(useScope),
                                                 BsConsumePoolModel.consume_type.in_((1, 2)),
                                                 BsConsumePoolModel.status.in_((1, 6))))
            return result
        except:
            raise Exception("未查询到可扣费的订单!")

    def resetMargin(self, consumePools: List[BsConsumePoolModel], caseType='SINGLE') -> \
            Union[BsConsumePoolModel, List[BsConsumePoolModel]]:
        """
        重置bs_consume_pool表可用余额为0
        :param consumePools:
        :param caseType: SINGLE 单订单，MORE 多订单
        :return:
        """
        for re in consumePools:
            re.margin = 0
            re.lock_num = 0
        self._rollback()
        if caseType == 'MORE':
            return [consumePools[0], consumePools[1]]
        return consumePools[0]

    def modifyMargin(self, avg, margin) -> BsConsumePoolModel:
        """
        修改bs_consume_pool表可用余额
        强隔离时须订单已绑定隔离码，否则扣费失败
        :param avg: 订单id 或者 obj
        :param margin:
        :return:
        """
        if isinstance(avg, BsConsumePoolModel):
            avg.margin = margin + avg.auth_freeze_num
            self._rollback()
            return avg
        bsConsumePool = self.queryByOrderId(avg)
        bsConsumePool.margin = margin + bsConsumePool.auth_freeze_num
        self._rollback()
        return bsConsumePool

    def modifyAuthMargin(self, avg, margin, useScopeType='NOT_STRONG') -> BsConsumePoolModel:
        """
        修改bs_consume_pool表可用余额
        强隔离时须订单已绑定隔离码，否则扣费失败
        :param avg: 订单id 或者 obj
        :param margin:
        :param useScopeType: IS_STRONG 3 强隔离，NOT_STRONG (0，4) 非强隔离
        :return:
        """
        if isinstance(avg, BsConsumePoolModel):
            avg.margin = margin + avg.auth_freeze_num
            avg.use_scope = 0
            if useScopeType == 'IS_STRONG':
                avg.use_scope = 3
            self._rollback()
            return avg
        bsConsumePool = self.queryByOrderId(avg)
        bsConsumePool.margin = margin + bsConsumePool.auth_freeze_num
        bsConsumePool.use_scope = 0
        if useScopeType == 'IS_STRONG':
            bsConsumePool.use_scope = 3
        self._rollback()
        return bsConsumePool
