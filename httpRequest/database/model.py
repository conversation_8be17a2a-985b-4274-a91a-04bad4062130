# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/3/23 5:27 下午 
# <AUTHOR> zhuqi 
# @File : model.py

from sqlalchemy import Column, String, Boolean, Foreign<PERSON>ey, Integer, DateTime, Date, Float
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BsOrderModel(Base):
    __tablename__ = 'bs_order'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, nullable=False)
    show_id = Column(String, nullable=False)
    status = Column(Integer)
    gid = Column(String(50), nullable=False)
    is_unlimit_num = Column(Integer, nullable=False)
    start_time = Column(Date, nullable=False)
    end_time = Column(Date, nullable=False)


class BsGiftLogModel(Base):
    """
    赠送订单表
    """
    __tablename__ = 'bs_gift_log'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, nullable=False)
    show_id = Column(String, nullable=False)
    status = Column(Integer)
    gid = Column(String(50), nullable=False)
    is_unlimit_num = Column(Integer, nullable=False)
    start_time = Column(Date, nullable=False)
    end_time = Column(Date, nullable=False)


class BsConsumePoolModel(Base):
    __tablename__ = 'bs_consume_pool'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, nullable=False)
    order_id = Column(String, nullable=False)
    status = Column(Integer)
    gid = Column(String(50), nullable=False)
    margin = Column(Integer, nullable=False)
    auth_freeze_num = Column(Integer, nullable=False)
    lock_num = Column(Integer, nullable=False)
    use_scope = Column(Integer, nullable=False)
    consume_type = Column(Integer, nullable=False)
    start_time = Column(Date, nullable=False)
    end_time = Column(Date, nullable=False)
    create_time = Column(Date, nullable=False)


class BsOrderAuthConsumePoolModel(Base):
    """
    授权固定配额消费记录表
    """
    __tablename__ = 'bs_order_auth_consume_pool'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    auth_id = Column(Integer, primary_key=True)
    pool_id = Column(String, nullable=False)
    product_id = Column(Integer, nullable=False)
    gid = Column(String, nullable=False)
    order_id = Column(String, nullable=False)
    total_amount = Column(Integer, nullable=False)
    margin = Column(Integer, nullable=False)
    used = Column(Integer, nullable=False)
    cancel_amount = Column(Integer, nullable=False)
    lock_num = Column(Integer, nullable=False)
    use_scope = Column(Integer, nullable=False)
    consume_type = Column(Integer, nullable=False)


class BsOrderAuthModel(Base):
    """
    订单授权表
    """
    __tablename__ = 'bs_order_auth'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    auth_gid = Column(String, nullable=False)
    auth_order_id = Column(String, nullable=False)
    auth_product_id = Column(Integer, nullable=False)
    authed_gid = Column(String, nullable=False)
    is_delete = Column(Integer, nullable=False)
    auth_type = Column(Integer, nullable=False)


class BsIsolationCodeModel(Base):
    """
    计费隔离码表
    """
    __tablename__ = 'bs_isolation_code'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    isolation_code = Column(String, nullable=False)
    binding_status = Column(Integer, nullable=False)
    order_bind_status = Column(Integer, nullable=False)


class BsSceneIsolationCodeRelationModel(Base):
    """
    计费场景隔离码关系表
    """
    __tablename__ = 'bs_scene_isolation_code_relation'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    isolation_code = Column(String, nullable=False)
    scene = Column(String, nullable=False)
    content = Column(String, nullable=False)
    status = Column(Integer, nullable=False)
    is_delete = Column(Integer, nullable=False)


class BsOrderIsolationRelModel(Base):
    """
    订单隔离码关联表
    """
    __tablename__ = 'bs_order_isolation_rel'
    __table_args__ = {"useexisting": True}
    id = Column(Integer, primary_key=True)
    order_id = Column(String, nullable=False)
    order_type = Column(Integer, nullable=False)  # 订单类型 0-普通 1-赠送
    isolation_code = Column(String, nullable=False)
    is_delete = Column(Integer, nullable=False)
