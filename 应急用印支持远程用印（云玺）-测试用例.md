# 应急用印支持远程用印（云玺）-测试用例

## 功能测试

### 远程用印功能

#### P1TL-云玺章桶设备在线时远程用印执行验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有远程用印权限；4、设备支持远程用印功能；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、查看设备卡面按钮显示；4、点击【远程用印】按钮；5、查看二次确认弹窗内容；6、点击确定执行远程用印；7、等待操作结果；8、查看用印执行状态；

##### ER-预期结果：1、设备卡面显示【远程用印】按钮；2、二次确认弹窗正常显示；3、系统向设备发送远程用印指令；4、远程用印操作成功执行；5、设备完成用印动作；6、操作结果反馈正常；

#### P1TL-云玺章桶设备离线时远程用印错误提示验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备离线；3、具有远程用印权限；

##### 操作步骤：1、进入印控设备页面；2、选择离线的云玺章桶设备；3、点击【远程用印】按钮；4、查看系统响应结果；

##### ER-预期结果：1、系统检测到设备离线状态；2、显示错误提示"设备不在线"；3、远程用印操作被阻止；4、用户能够清楚了解失败原因；

#### P1TL-远程用印二次确认机制验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有远程用印权限；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、点击【远程用印】按钮；4、查看二次确认弹窗；5、点击取消按钮；6、再次点击【远程用印】按钮；7、点击确定按钮；

##### ER-预期结果：1、点击远程用印后显示二次确认弹窗；2、弹窗内容说明远程用印操作；3、点击取消时不执行远程用印；4、点击确定时正常执行远程用印；5、二次确认机制有效防止误操作；

#### P1TL-非云玺章桶设备远程用印功能限制验证

##### PD-前置条件：1、用户已登录系统；2、系统中存在非云玺章桶设备（如思格特章桶）；3、具有设备管理权限；

##### 操作步骤：1、进入印控设备页面；2、选择非云玺章桶设备；3、查看设备卡面按钮显示；4、检查是否有远程用印功能；

##### ER-预期结果：1、非云玺章桶设备不显示【远程用印】按钮；2、功能限制符合设备能力；3、界面显示合理；4、不会出现功能混淆；

### 应急用印记录显示

#### P1TL-远程用印应急记录列表显示验证

##### PD-前置条件：1、已执行远程用印操作；2、具有查看应急用印记录权限；3、远程用印操作成功完成；

##### 操作步骤：1、进入应急用印记录列表页面；2、查找远程用印产生的记录；3、检查记录中的用印人字段显示；4、检查用户核验方式字段显示；5、验证记录信息完整性；

##### ER-预期结果：1、应急用印记录成功生成；2、用印人字段显示"-"；3、用户核验方式显示"远程用印"；4、其他记录信息显示正确；5、记录时间准确；

#### P1TL-远程用印应急记录详情显示验证

##### PD-前置条件：1、存在远程用印的应急记录；2、具有查看用印详情权限；

##### 操作步骤：1、进入应急用印记录列表；2、选择远程用印记录；3、点击查看详情；4、检查用印详情页面信息；5、重点查看用印人和用户核验方式字段；

##### ER-预期结果：1、用印详情页面正常打开；2、用印人字段显示"-"；3、用户核验方式字段显示"远程用印"；4、其他详情信息显示正确；5、页面布局和字段显示符合设计要求；

#### P1TL-远程用印记录与其他核验方式记录区分验证

##### PD-前置条件：1、系统中存在多种核验方式的应急用印记录；2、包含远程用印、指纹验证、密码验证等；3、具有查看权限；

##### 操作步骤：1、进入应急用印记录列表；2、查看不同核验方式的记录；3、对比远程用印记录与其他记录的显示差异；4、验证记录分类和标识；

##### ER-预期结果：1、远程用印记录用户核验方式显示"远程用印"；2、其他核验方式记录显示相应的验证方式；3、不同核验方式记录能够清晰区分；4、记录分类准确；

#### P1TL-远程用印记录合并规则验证

##### PD-前置条件：1、存在多条相同条件的远程用印记录；2、记录包含相同的印章、设备、日期等信息；

##### 操作步骤：1、进入应急用印记录列表；2、查看远程用印记录合并显示情况；3、检查合并规则是否包含用户核验方式；4、验证相同条件记录的合并逻辑；

##### ER-预期结果：1、记录按用印人、印章、设备、日期、核验方式进行合并；2、相同条件的远程用印记录合并为一条显示；3、用印次数统计准确；4、合并逻辑与其他核验方式一致；

## 边界测试

### 设备状态边界

#### P1TL-设备在线离线状态快速切换时远程用印处理验证

##### PD-前置条件：1、云玺章桶设备状态可快速切换；2、用户正在操作远程用印功能；

##### 操作步骤：1、设备在线时点击远程用印按钮；2、在确认弹窗显示时将设备切换为离线；3、点击确定执行操作；4、观察系统处理结果；5、设备重新上线后再次尝试；

##### ER-预期结果：1、状态切换时系统能正确识别；2、设备离线时操作被阻止并提示；3、设备重新上线后功能恢复正常；4、不会出现系统异常或数据错误；

#### P1TL-远程用印操作过程中设备状态变化处理验证

##### PD-前置条件：1、云玺章桶设备在线；2、远程用印操作正在执行中；

##### 操作步骤：1、开始远程用印操作；2、在操作执行过程中模拟设备异常；3、观察系统处理结果；4、检查用印记录生成情况；

##### ER-预期结果：1、操作过程中设备异常时系统能检测到；2、显示相应的错误提示；3、操作被中断或重试；4、不会生成不完整的用印记录；

## 异常测试

### 设备通信异常

#### P1TL-云玺章桶通信异常时远程用印处理验证

##### PD-前置条件：1、云玺章桶设备显示在线但通信异常；2、用户尝试执行远程用印；

##### 操作步骤：1、模拟设备通信异常情况；2、点击远程用印按钮；3、确认执行操作；4、观察错误处理机制；

##### ER-预期结果：1、系统检测到通信异常；2、显示相应的错误提示信息；3、不会导致系统崩溃或长时间无响应；4、用户能够理解异常原因并采取相应措施；

#### P1TL-远程用印指令执行失败处理验证

##### PD-前置条件：1、云玺章桶设备在线；2、模拟远程用印指令执行失败；

##### 操作步骤：1、模拟设备指令执行异常；2、执行远程用印操作；3、观察系统错误处理；4、检查用印记录生成情况；

##### ER-预期结果：1、系统检测到指令执行失败；2、显示操作失败提示；3、不会生成错误的用印记录；4、用户能够重新尝试操作；

### 系统异常

#### P1TL-应急用印记录服务异常时远程用印处理验证

##### PD-前置条件：1、远程用印操作成功执行；2、应急用印记录服务异常；

##### 操作步骤：1、模拟记录服务异常；2、执行远程用印操作；3、观察系统处理结果；4、检查数据一致性；

##### ER-预期结果：1、系统能够检测到记录服务异常；2、给出合适的错误提示；3、不会丢失用印操作数据；4、服务恢复后能够正常处理积压数据；

## 性能测试

### 响应时间测试

#### P1TL-远程用印操作响应时间验证

##### PD-前置条件：1、云玺章桶设备在线且通信正常；2、网络环境稳定；

##### 操作步骤：1、记录开始时间；2、点击远程用印按钮；3、确认执行操作；4、等待操作完成；5、记录结束时间；6、计算响应时间；7、重复测试多次；

##### ER-预期结果：1、远程用印操作响应时间在10秒以内；2、响应时间稳定，波动不超过3秒；3、用户体验良好；4、符合性能要求标准；

### 并发性能测试

#### P1TL-多用户同时执行远程用印性能验证

##### PD-前置条件：1、多台云玺章桶设备在线；2、多个用户账号；3、系统负载正常；

##### 操作步骤：1、模拟多用户同时登录；2、同时执行远程用印操作；3、观察系统响应情况；4、检查每个用户的操作结果；5、监控系统资源使用情况；

##### ER-预期结果：1、所有用户都能成功执行远程用印；2、系统响应时间不会显著增加；3、不会出现系统卡顿或崩溃；4、资源使用在合理范围内；

## 安全测试

### 权限验证测试

#### P1TL-无远程用印权限用户访问控制验证

##### PD-前置条件：1、创建无远程用印权限的测试用户；2、系统权限控制正常；

##### 操作步骤：1、使用无权限用户登录；2、尝试访问云玺章桶页面；3、查看远程用印按钮显示；4、尝试执行远程用印操作；

##### ER-预期结果：1、无权限用户无法看到远程用印按钮；2、或显示权限不足提示；3、无法执行远程用印操作；4、权限控制机制有效；

### 操作安全测试

#### P1TL-远程用印二次确认安全性验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有远程用印权限；

##### 操作步骤：1、点击远程用印按钮；2、在确认弹窗中点击取消；3、验证操作是否执行；4、重新点击远程用印并确认；5、验证操作执行结果；

##### ER-预期结果：1、取消操作时不执行远程用印；2、确认操作时正常执行远程用印；3、二次确认机制有效防止误操作；4、操作安全性得到保障；

## 兼容性测试

### 设备兼容性

#### P1TL-云玺章桶与其他设备远程用印功能差异验证

##### PD-前置条件：1、系统中同时存在云玺章桶和其他类型设备；

##### 操作步骤：1、查看云玺章桶设备的功能按钮；2、查看其他类型设备的功能按钮；3、对比功能差异；4、验证功能限制正确性；

##### ER-预期结果：1、云玺章桶显示远程用印功能按钮；2、其他设备不显示远程用印功能；3、功能差异化处理正确；4、符合设备能力限制；

### 多端兼容性

#### P1TL-PC端和移动端远程用印功能兼容性验证

##### PD-前置条件：1、PC端和移动端环境正常；2、具有远程用印权限；

##### 操作步骤：1、在PC端测试远程用印功能；2、在移动端测试相同功能；3、对比功能表现和界面显示；4、验证操作流程一致性；

##### ER-预期结果：1、PC端和移动端功能完整性一致；2、界面适配良好；3、操作流程相同；4、用户体验统一；

## 批量操作测试

### 批量远程用印

#### P1TL-多设备批量远程用印操作验证

##### PD-前置条件：1、系统中存在多台云玺章桶设备；2、设备均处于在线状态；3、具有批量远程用印权限；

##### 操作步骤：1、进入印控设备页面；2、选择多台云玺章桶设备；3、执行批量远程用印操作；4、观察批量处理过程；5、检查每台设备的用印结果；

##### ER-预期结果：1、支持批量选择设备；2、批量远程用印操作能够正常执行；3、每台设备用印都有相应提示；4、操作完成后记录正确生成；

#### P1TL-批量操作中部分设备异常处理验证

##### PD-前置条件：1、选择了多台设备进行批量远程用印；2、其中部分设备离线或异常；

##### 操作步骤：1、选择包含异常状态设备的批量操作；2、执行批量远程用印；3、观察异常设备的处理结果；4、检查正常设备的操作结果；

##### ER-预期结果：1、正常设备成功执行远程用印；2、异常设备显示相应错误提示；3、批量操作结果明确区分成功和失败；4、不会因部分失败影响整体操作；

## 日志记录测试

### 操作日志记录

#### P1TL-远程用印操作日志记录验证

##### PD-前置条件：1、系统日志功能正常；2、具有远程用印权限；3、云玺章桶设备在线；

##### 操作步骤：1、执行远程用印操作；2、查看系统操作日志；3、检查日志记录内容；4、验证日志记录完整性；

##### ER-预期结果：1、远程用印操作被完整记录在日志中；2、日志包含操作时间、操作人、设备信息；3、日志包含操作结果和状态；4、日志记录准确详细；

#### P1TL-异常操作日志记录验证

##### PD-前置条件：1、系统日志功能正常；2、模拟各种异常情况；

##### 操作步骤：1、执行设备离线时的远程用印操作；2、执行通信异常时的远程用印操作；3、查看异常操作日志记录；4、验证日志完整性；

##### ER-预期结果：1、异常操作被记录在日志中；2、日志包含异常原因和错误信息；3、日志有助于问题排查；4、异常日志记录完整；

## 集成测试

### 端到端流程测试

#### P1TL-完整远程用印端到端流程验证

##### PD-前置条件：1、云玺章桶设备正常；2、用户具有完整权限；3、系统功能正常；

##### 操作步骤：1、查看设备初始状态；2、执行远程用印操作；3、查看用印执行结果；4、检查应急用印记录生成；5、验证记录详情信息；6、查看完整操作日志；

##### ER-预期结果：1、远程用印操作成功执行；2、应急用印记录正确生成；3、记录信息准确完整；4、操作日志记录详细；5、整个流程无缝衔接；

#### P1TL-远程用印与其他核验方式集成验证

##### PD-前置条件：1、系统支持多种核验方式；2、具有相应权限；3、设备功能正常；

##### 操作步骤：1、分别执行远程用印、指纹验证、密码验证等操作；2、查看不同核验方式的记录；3、验证记录区分和显示；4、检查数据一致性；

##### ER-预期结果：1、不同核验方式都能正常执行；2、记录能够正确区分核验方式；3、数据显示一致准确；4、功能集成正确；

## 用户体验测试

### 交互体验

#### P1TL-远程用印操作用户引导验证

##### PD-前置条件：1、用户首次使用远程用印功能；2、设备在线；

##### 操作步骤：1、查看远程用印按钮的提示信息；2、点击远程用印按钮查看确认弹窗；3、观察操作过程中的用户提示；4、检查操作完成后的反馈；

##### ER-预期结果：1、按钮功能说明清晰；2、确认弹窗内容易于理解；3、操作过程有适当的进度提示；4、操作结果反馈明确；

#### P1TL-远程用印记录显示用户体验验证

##### PD-前置条件：1、存在远程用印记录；2、用户具有查看权限；

##### 操作步骤：1、查看应急用印记录列表；2、检查远程用印记录的视觉标识；3、查看记录详情页面；4、验证信息显示的清晰度；

##### ER-预期结果：1、远程用印记录有明显的标识；2、记录信息显示清晰易读；3、详情页面布局合理；4、用户体验良好；

## 数据一致性测试

### 记录数据一致性

#### P1TL-远程用印记录数据一致性验证

##### PD-前置条件：1、执行了远程用印操作；2、具有数据查看权限；

##### 操作步骤：1、在应急用印记录列表查看记录；2、在用印详情页面查看记录；3、在系统日志中查看记录；4、对比各处数据一致性；

##### ER-预期结果：1、列表、详情、日志中的数据一致；2、用印人都显示为"-"；3、核验方式都显示为"远程用印"；4、时间和设备信息一致；

#### P1TL-远程用印记录与设备状态同步验证

##### PD-前置条件：1、远程用印操作成功执行；2、设备状态可监控；

##### 操作步骤：1、执行远程用印操作；2、查看设备状态变化；3、检查用印记录生成时间；4、验证状态同步的及时性；

##### ER-预期结果：1、设备状态与用印记录同步；2、记录生成时间准确；3、状态变化及时反映；4、数据同步一致；

## 冒烟测试用例

### 核心功能验证

#### MYTL-云玺章桶远程用印基本功能验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有远程用印权限；

##### 操作步骤：1、进入印控设备页面；2、点击【远程用印】按钮；3、确认执行操作；4、查看用印结果；

##### ER-预期结果：1、远程用印按钮正常显示；2、操作成功执行；3、设备完成用印动作；4、操作结果反馈正常；

#### MYTL-设备离线时远程用印错误提示验证

##### PD-前置条件：1、云玺章桶设备离线；2、具有远程用印权限；

##### 操作步骤：1、选择离线设备；2、点击【远程用印】按钮；3、观察系统提示；

##### ER-预期结果：1、显示"设备不在线"错误提示；2、远程用印操作被阻止；3、提示信息准确；

#### MYTL-远程用印二次确认机制验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有远程用印权限；

##### 操作步骤：1、点击【远程用印】按钮；2、查看确认弹窗；3、点击取消；4、验证操作结果；

##### ER-预期结果：1、显示二次确认弹窗；2、弹窗内容清晰；3、取消操作不执行远程用印；

#### MYTL-非云玺设备远程用印功能限制验证

##### PD-前置条件：1、系统中存在非云玺章桶设备；

##### 操作步骤：1、选择非云玺设备；2、查看功能按钮显示；3、检查远程用印功能；

##### ER-预期结果：1、非云玺设备不显示远程用印按钮；2、功能限制正确；3、界面显示合理；

#### MYTL-远程用印应急记录列表显示验证

##### PD-前置条件：1、已执行远程用印操作；2、具有查看记录权限；

##### 操作步骤：1、进入应急用印记录列表；2、查找远程用印记录；3、检查记录显示；

##### ER-预期结果：1、记录成功生成；2、用印人显示"-"；3、核验方式显示"远程用印"；

#### MYTL-远程用印应急记录详情显示验证

##### PD-前置条件：1、存在远程用印记录；2、具有查看详情权限；

##### 操作步骤：1、选择远程用印记录；2、查看详情页面；3、检查字段显示；

##### ER-预期结果：1、详情页面正常打开；2、用印人显示"-"；3、核验方式显示"远程用印"；

#### MYTL-远程用印记录与其他核验方式区分验证

##### PD-前置条件：1、存在多种核验方式的记录；

##### 操作步骤：1、查看不同核验方式记录；2、对比显示差异；3、验证记录分类；

##### ER-预期结果：1、远程用印记录标识清晰；2、与其他核验方式能够区分；3、记录分类准确；

#### MYTL-远程用印操作响应时间验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络正常；

##### 操作步骤：1、执行远程用印操作；2、记录响应时间；3、验证用户体验；

##### ER-预期结果：1、响应时间在10秒以内；2、操作流程顺畅；3、用户体验良好；

#### MYTL-权限控制基本验证

##### PD-前置条件：1、无远程用印权限的测试用户；

##### 操作步骤：1、使用无权限用户登录；2、查看设备管理页面；3、检查远程用印功能访问；

##### ER-预期结果：1、无法看到远程用印按钮；2、或显示权限不足提示；3、权限控制有效；

#### MYTL-远程用印记录合并规则验证

##### PD-前置条件：1、存在相同条件的远程用印记录；

##### 操作步骤：1、查看应急用印记录列表；2、检查记录合并显示；3、验证合并逻辑；

##### ER-预期结果：1、相同条件记录正确合并；2、合并规则包含核验方式；3、次数统计准确；

#### MYTL-设备兼容性基本验证

##### PD-前置条件：1、系统中存在云玺和其他类型设备；

##### 操作步骤：1、分别查看不同设备功能；2、对比远程用印功能显示；3、验证兼容性处理；

##### ER-预期结果：1、云玺设备显示远程用印功能；2、其他设备不显示该功能；3、兼容性处理正确；

#### MYTL-远程用印操作日志记录验证

##### PD-前置条件：1、系统日志功能正常；2、具有远程用印权限；

##### 操作步骤：1、执行远程用印操作；2、查看系统日志；3、检查日志记录；

##### ER-预期结果：1、操作被完整记录；2、日志信息准确；3、包含关键操作信息；

## 线上验证用例

### 核心业务流程验证

#### PATL-远程用印完整业务流程验证

##### PD-前置条件：1、生产环境云玺章桶设备在线；2、用户具有远程用印权限；

##### 步骤一：登录系统并进入印控设备页面

##### 步骤二：选择云玺章桶设备并执行远程用印操作

##### 步骤三：验证远程用印执行结果

##### 步骤四：查看应急用印记录生成情况

##### ER-预期结果1：远程用印操作流程完整顺畅

##### 2：设备响应正常，用印执行成功

##### 3：应急用印记录准确生成

#### PATL-设备异常情况处理验证

##### PD-前置条件：1、生产环境中存在不同状态的设备；

##### 步骤一：测试离线设备的远程用印操作处理

##### 步骤二：测试通信异常设备的操作处理

##### 步骤三：验证异常提示信息的准确性

##### ER-预期结果1：离线设备操作提示准确

##### 2：通信异常时错误处理正确

##### 3：异常处理机制有效

#### PATL-应急用印记录管理验证

##### PD-前置条件：1、生产环境中存在各种核验方式的用印记录；

##### 步骤一：查看应急用印记录列表

##### 步骤二：验证远程用印记录显示

##### 步骤三：检查记录详情信息准确性

##### 步骤四：验证记录合并和分类逻辑

##### ER-预期结果1：记录列表显示正确

##### 2：远程用印记录标识清晰

##### 3：记录详情信息准确完整

#### PATL-系统性能和稳定性验证

##### PD-前置条件：1、生产环境正常运行；2、有一定用户访问量；

##### 步骤一：在业务高峰期测试远程用印功能

##### 步骤二：执行批量远程用印操作

##### 步骤三：监控系统响应和稳定性

##### ER-预期结果1：高负载下功能正常

##### 2：批量操作性能稳定

##### 3：系统运行无异常

#### PATL-权限控制线上验证

##### PD-前置条件：1、生产环境权限配置正常；2、不同权限级别用户；

##### 步骤一：测试不同权限用户的功能访问

##### 步骤二：验证权限控制的严格性

##### 步骤三：检查敏感操作的保护机制

##### ER-预期结果1：权限控制严格有效

##### 2：无权限用户无法访问远程用印功能

##### 3：操作安全性得到保障

#### PATL-设备兼容性线上验证

##### PD-前置条件：1、生产环境中存在多种类型设备；

##### 步骤一：测试云玺章桶的远程用印功能

##### 步骤二：验证其他设备的功能限制

##### 步骤三：检查设备差异化处理

##### ER-预期结果1：云玺设备远程用印功能正常

##### 2：其他设备正确限制该功能

##### 3：设备兼容性处理准确

#### PATL-数据一致性和审计验证

##### PD-前置条件：1、生产环境日志系统正常；

##### 步骤一：执行远程用印操作

##### 步骤二：检查各系统模块数据一致性

##### 步骤三：验证操作日志记录完整性

##### ER-预期结果1：各模块数据保持一致

##### 2：操作日志记录完整准确

##### 3：便于审计和问题排查
