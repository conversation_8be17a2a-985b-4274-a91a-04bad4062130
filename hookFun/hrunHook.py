# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2023/2/14 15:38
# <AUTHOR> zhu<PERSON> 
# @File : hrunHook.py
from httpRequest.service.loginService import LoginService
from utils import log, ENV


def getTokenWeb(account=None, password=None):
    loginService = LoginService(ENV('esign.projectHost'))
    if not (account or password):
        account = ENV('ceswdzxzdhyhwgd1.account')
        password = ENV('ceswdzxzdhyhwgd1.password')
    token = loginService.createToken(ENV('esign.projectId'), account, password)
    return token


if __name__ == '__main__':

    pass
