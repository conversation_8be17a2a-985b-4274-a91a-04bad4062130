# openapi接口支持部分作废状态-测试用例

## 功能测试

### 作废签署流程接口

#### TL-部分文件作废功能验证

##### PD-前置条件：签署流程已完成；存在多个已签署文件；用户具有作废权限；

##### 步骤一：调用作废签署流程接口/esign-docs/v1/signFlow/revoke

##### 步骤二：设置reason为"测试部分作废"

##### 步骤三：设置revokeSignFiles数组包含部分文件的signedFileKey

##### 步骤四：发送POST请求

##### 步骤五：检查响应结果

##### ER-预期结果：1：接口调用成功返回200；2：指定文件状态变更为作废中；3：未指定文件保持正常状态；4：签署流程状态更新为部分作废；

#### TL-全量文件作废兼容性验证

##### PD-前置条件：签署流程已完成；存在多个已签署文件；用户具有作废权限；

##### 步骤一：调用作废签署流程接口

##### 步骤二：设置reason为"测试全量作废"

##### 步骤三：revokeSignFiles包含所有文件的signedFileKey

##### 步骤四：发送POST请求

##### 步骤五：验证处理结果

##### ER-预期结果：1：接口调用成功；2：所有文件状态变更为作废中；3：签署流程状态更新为已作废；4：保持向后兼容性；

#### TL-空文件列表作废验证

##### PD-前置条件：签署流程已完成；用户具有作废权限；

##### 步骤一：调用作废签署流程接口

##### 步骤二：设置reason为"测试空列表作废"

##### 步骤三：revokeSignFiles设置为空数组或不传递

##### 步骤四：发送POST请求

##### 步骤五：检查处理逻辑

##### ER-预期结果：1：按照原有逻辑处理；2：全量作废所有文件；3：签署流程状态更新为已作废；4：保持兼容性；

### 获取签署流程进度详情接口

#### TL-部分作废状态详情查询验证

##### PD-前置条件：签署流程存在部分作废文件；流程状态为部分作废；

##### 步骤一：调用获取签署流程进度详情接口/esign-signs/v1/signFlow/signDetail

##### 步骤二：设置signFlowId为部分作废的流程ID

##### 步骤三：发送POST请求

##### 步骤四：解析响应数据

##### 步骤五：验证newRevokSignFlows字段内容

##### ER-预期结果：1：接口调用成功；2：返回newRevokSignFlows数组；3：包含作废流程ID和作废原因；4：revokeSignFiles包含作废文件信息和状态；

#### TL-文件状态字段验证

##### PD-前置条件：签署流程包含不同状态的文件；

##### 步骤一：调用获取签署流程进度详情接口

##### 步骤二：设置有效的signFlowId

##### 步骤三：发送请求获取详情

##### 步骤四：检查revokeSignFiles中的fileStatus字段

##### 步骤五：验证状态值含义

##### ER-预期结果：1：fileStatus字段存在；2：状态值1表示正常；3：状态值2表示作废中；4：状态值3表示已作废；

### 获取签署流程列表接口

#### TL-部分作废状态筛选验证

##### PD-前置条件：系统中存在部分作废状态的签署流程；

##### 步骤一：调用获取签署流程列表接口/esign-signs/v1/signFlow/list

##### 步骤二：设置signFlowStatus为9（部分作废状态）

##### 步骤三：发送POST请求

##### 步骤四：解析返回的流程列表

##### 步骤五：验证筛选结果

##### ER-预期结果：1：接口调用成功；2：返回的流程状态均为部分作废；3：列表数据准确；4：分页功能正常；

## 功能闭环场景验证

### 完整部分作废业务流程

#### TL-新用户视角完整部分作废流程验证

##### PD-前置条件：用户已登录；具有签署和作废权限；存在已完成的多文件签署流程；

##### 步骤一：查询签署流程列表确认流程状态

##### 步骤二：获取流程详情查看文件信息

##### 步骤三：选择部分文件进行作废操作

##### 步骤四：调用作废接口传入部分文件Key

##### 步骤五：再次查询流程详情验证状态变更

##### 步骤六：查询流程列表验证状态筛选

##### 步骤七：等待回调通知验证

##### ER-预期结果：1：整个流程操作顺畅；2：状态流转正确；3：文件状态准确；4：回调通知及时；5：数据一致性良好；

## 边界测试

### 输入边界测试

#### TL-单文件作废边界验证

##### PD-前置条件：签署流程只包含一个已签署文件；

##### 步骤一：调用作废接口

##### 步骤二：revokeSignFiles只包含唯一文件

##### 步骤三：执行作废操作

##### 步骤四：验证流程状态

##### ER-预期结果：1：作废成功；2：流程状态变为已作废；3：处理逻辑正确；

#### TL-最大文件数量作废验证

##### PD-前置条件：签署流程包含系统支持的最大文件数量；

##### 步骤一：准备包含最大数量文件的签署流程

##### 步骤二：调用作废接口作废所有文件

##### 步骤三：监控处理性能

##### 步骤四：验证处理结果

##### ER-预期结果：1：所有文件作废成功；2：处理时间在可接受范围内；3：系统稳定运行；

## 异常测试

### 错误输入测试

#### TL-无效文件Key作废验证

##### PD-前置条件：用户具有作废权限；

##### 步骤一：调用作废接口

##### 步骤二：revokeSignFiles包含不存在的signedFileKey

##### 步骤三：发送请求

##### 步骤四：检查错误处理

##### ER-预期结果：1：返回明确错误信息；2：错误码准确；3：不影响有效文件处理；

#### TL-重复作废验证

##### PD-前置条件：文件已处于作废状态；

##### 步骤一：对已作废文件再次调用作废接口

##### 步骤二：检查系统响应

##### 步骤三：验证幂等性

##### ER-预期结果：1：系统正确处理重复请求；2：返回适当提示信息；3：不产生异常状态；

### 权限异常测试

#### TL-无作废权限验证

##### PD-前置条件：用户无签署流程作废权限；

##### 步骤一：使用无权限用户调用作废接口

##### 步骤二：传入有效的作废参数

##### 步骤三：检查权限校验

##### ER-预期结果：1：返回权限不足错误；2：错误码为403；3：不执行作废操作；

## 性能测试

### 响应时间测试

#### TL-大量文件作废性能验证

##### PD-前置条件：签署流程包含大量已签署文件；

##### 步骤一：准备包含100个文件的签署流程

##### 步骤二：调用作废接口作废所有文件

##### 步骤三：记录响应时间

##### 步骤四：监控系统资源使用

##### ER-预期结果：1：响应时间小于5秒；2：系统资源使用正常；3：作废操作成功完成；

### 并发性能测试

#### TL-并发作废请求验证

##### PD-前置条件：多个签署流程处于可作废状态；

##### 步骤一：准备10个并发作废请求

##### 步骤二：同时发送作废请求

##### 步骤三：监控系统处理能力

##### 步骤四：验证处理结果

##### ER-预期结果：1：所有请求正确处理；2：无数据竞争问题；3：系统稳定运行；

## 安全测试

### 权限验证测试

#### TL-跨租户作废权限验证

##### PD-前置条件：存在不同租户的签署流程；

##### 步骤一：使用租户A的用户

##### 步骤二：尝试作废租户B的签署流程

##### 步骤三：检查权限隔离

##### ER-预期结果：1：拒绝跨租户操作；2：返回权限错误；3：数据安全隔离；

### 参数校验测试

#### TL-恶意参数注入验证

##### PD-前置条件：用户具有作废权限；

##### 步骤一：在reason字段注入恶意脚本

##### 步骤二：在signedFileKey中注入SQL语句

##### 步骤三：发送请求

##### 步骤四：检查安全防护

##### ER-预期结果：1：恶意参数被过滤；2：系统安全运行；3：返回参数错误提示；

## 兼容性测试

### 向后兼容性测试

#### TL-原有作废接口兼容性验证

##### PD-前置条件：使用原有作废接口调用方式；

##### 步骤一：按照原有方式调用作废接口

##### 步骤二：不传递revokeSignFiles参数

##### 步骤三：验证处理逻辑

##### 步骤四：检查响应格式

##### ER-预期结果：1：保持原有功能不变；2：全量作废逻辑正常；3：响应格式兼容；

## 回调通知测试

### SIGN_FLOW_CANCEL回调事件

#### TL-部分作废回调通知验证

##### PD-前置条件：配置了回调地址；签署流程支持回调；

##### 步骤一：执行部分文件作废操作

##### 步骤二：等待回调通知

##### 步骤三：验证回调数据结构

##### 步骤四：检查revokeSignFiles字段内容

##### ER-预期结果：1：及时收到回调通知；2：callBackEnum为6；3：revokeSignFiles包含作废文件信息；4：下载地址有效；

### 网络异常场景

#### TL-网络中断时作废操作处理验证

##### PD-前置条件：签署流程已完成；网络环境可控制；

##### 步骤一：开始调用作废接口

##### 步骤二：在请求处理过程中模拟网络中断

##### 步骤三：网络恢复后检查数据状态

##### 步骤四：验证事务处理机制

##### ER-预期结果：1：数据状态一致；2：无脏数据产生；3：支持重试机制；4：异常日志记录完整；

### 数据一致性测试

#### TL-作废过程数据一致性验证

##### PD-前置条件：签署流程包含多个文件；数据库支持事务；

##### 步骤一：并发执行作废和查询操作

##### 步骤二：监控数据库事务处理

##### 步骤三：验证中间状态数据

##### 步骤四：检查最终数据一致性

##### ER-预期结果：1：数据状态始终一致；2：无中间状态泄露；3：事务隔离正确；4：并发安全保证；

### 监控告警测试

#### TL-异常情况监控告警验证

##### PD-前置条件：监控系统已配置；告警规则已设置；

##### 步骤一：触发作废接口异常情况

##### 步骤二：观察监控指标变化

##### 步骤三：验证告警触发机制

##### 步骤四：检查告警内容准确性

##### ER-预期结果：1：异常及时被监控捕获；2：告警信息准确详细；3：告警级别合理；4：通知渠道正常；
