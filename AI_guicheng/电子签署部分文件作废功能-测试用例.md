# 电子签署部分文件作废功能-测试用例

## 功能测试

### 作废弹窗展示功能

#### TL-业务模板允许部分作废时弹窗正确展示文件列表

##### PD-前置条件：用户已登录；存在已完成的签署流程；业务模板配置允许部分作废；

##### 步骤一：进入"我管理的/我发起的"列表页面

##### 步骤二：选择已完成的签署流程点击作废

##### 步骤三：查看作废弹窗内容

##### ER-预期结果

###### 1：弹窗正确展示"作废签署文件"字段

###### 2：显示原流程所有签署文件列表

###### 3：显示全选和复选框功能

###### 4：默认选中全部未作废的签署文件

#### TL-业务模板不允许部分作废时弹窗不显示选择功能

##### PD-前置条件：用户已登录；存在已完成的签署流程；业务模板配置不允许部分作废；

##### 步骤一：进入"我管理的/我发起的"列表页面

##### 步骤二：选择已完成的签署流程点击作废

##### 步骤三：查看作废弹窗内容

##### ER-预期结果

###### 1：弹窗不显示全选和复选框

###### 2：表示作废所有签署文件

###### 3：文件列表正常展示但无选择功能

#### TL-文件状态正确展示和控制

##### PD-前置条件：用户已登录；存在包含不同状态文件的签署流程；业务模板允许部分作废；

##### 步骤一：进入作废弹窗

##### 步骤二：查看不同状态文件的展示

##### 步骤三：尝试勾选不同状态的文件

##### ER-预期结果

###### 1：未作废且无关联作废流程的文件可正常勾选

###### 2：未作废但有关联作废流程的文件标记为"作废中"且无法勾选

###### 3：已作废的文件标记为"已作废"且无法勾选

### 文件选择交互功能

#### TL-全选功能正确工作

##### PD-前置条件：用户已登录；业务模板允许部分作废；存在多个可选择文件；

##### 步骤一：打开作废弹窗

##### 步骤二：点击全选按钮

##### 步骤三：再次点击全选按钮取消全选

##### ER-预期结果

###### 1：点击全选后所有可选择文件被选中

###### 2：不可选择文件保持不可选状态

###### 3：取消全选后所有文件取消选中

#### TL-单个文件选择功能正确工作

##### PD-前置条件：用户已登录；业务模板允许部分作废；存在多个可选择文件；

##### 步骤一：打开作废弹窗

##### 步骤二：选择部分文件

##### 步骤三：取消选择部分文件

##### ER-预期结果

###### 1：可选择文件能正常勾选和取消勾选

###### 2：选择状态实时更新

###### 3：全选状态根据单选状态联动更新

#### TL-混合状态文件列表展示验证

##### PD-前置条件：用户已登录；存在包含未作废、作废中、已作废文件的签署流程；

##### 步骤一：打开作废弹窗

##### 步骤二：查看文件列表状态

##### 步骤三：尝试操作不同状态文件

##### ER-预期结果

###### 1：未作废文件显示可选择状态

###### 2：作废中文件显示"作废中"标记且不可选

###### 3：已作废文件显示"已作废"标记且不可选

### 提交校验功能

#### TL-未选择文件提交校验

##### PD-前置条件：用户已登录；业务模板允许部分作废；

##### 步骤一：打开作废弹窗

##### 步骤二：取消选择所有文件

##### 步骤三：点击确定按钮

##### ER-预期结果

###### 1：系统提示"至少选择一个未作废的签署文件"

###### 2：作废操作不执行

###### 3：弹窗保持打开状态

#### TL-选择文件后成功提交

##### PD-前置条件：用户已登录；业务模板允许部分作废；存在可选择文件；

##### 步骤一：打开作废弹窗

##### 步骤二：选择至少一个未作废文件

##### 步骤三：点击确定按钮

##### ER-预期结果

###### 1：校验通过

###### 2：成功发起作废流程

###### 3：弹窗关闭并返回列表页面

## 边界测试

### 文件数量边界测试

#### TL-单个文件作废验证

##### PD-前置条件：用户已登录；签署流程只包含一个文件；业务模板允许部分作废；

##### 步骤一：打开作废弹窗

##### 步骤二：查看文件选择状态

##### 步骤三：提交作废

##### ER-预期结果

###### 1：单个文件默认被选中

###### 2：可以取消选择

###### 3：选中状态下可成功提交

#### TL-大量文件作废验证

##### PD-前置条件：用户已登录；签署流程包含大量文件（50个以上）；业务模板允许部分作废；

##### 步骤一：打开作废弹窗

##### 步骤二：测试全选和取消全选

##### 步骤三：测试部分选择

##### ER-预期结果

###### 1：文件列表正常加载和展示

###### 2：全选功能正常工作

###### 3：选择操作响应及时

#### TL-全部文件已作废或作废中场景

##### PD-前置条件：用户已登录；签署流程所有文件都是已作废或作废中状态；

##### 步骤一：打开作废弹窗

##### 步骤二：查看文件选择状态

##### 步骤三：尝试提交

##### ER-预期结果

###### 1：所有文件都不可选择

###### 2：提交时提示"至少选择一个未作废的签署文件"

###### 3：无法完成作废操作

## 异常测试

### 配置异常测试

#### TL-业务模板配置获取失败处理

##### PD-前置条件：用户已登录；业务模板配置服务异常；

##### 步骤一：尝试打开作废弹窗

##### 步骤二：查看系统处理结果

##### ER-预期结果

###### 1：系统给出友好错误提示

###### 2：不影响其他功能正常使用

###### 3：可重试操作

#### TL-文件状态获取异常处理

##### PD-前置条件：用户已登录；文件状态服务异常；

##### 步骤一：打开作废弹窗

##### 步骤二：查看文件列表展示

##### ER-预期结果

###### 1：系统给出适当错误提示

###### 2：不影响弹窗基本展示

###### 3：支持重新加载

#### TL-网络异常时的处理

##### PD-前置条件：用户已登录；网络连接不稳定；

##### 步骤一：在网络异常情况下打开作废弹窗

##### 步骤二：尝试进行文件选择操作

##### 步骤三：尝试提交作废

##### ER-预期结果

###### 1：网络异常时给出明确提示

###### 2：操作失败后支持重试

###### 3：不会出现数据不一致

## 性能测试

### 响应时间测试

#### TL-作废弹窗加载性能验证

##### PD-前置条件：用户已登录；存在包含大量文件的签署流程；

##### 步骤一：记录打开作废弹窗的时间

##### 步骤二：测量文件列表加载完成时间

##### 步骤三：验证交互响应时间

##### ER-预期结果

###### 1：弹窗打开时间不超过2秒

###### 2：文件列表加载时间不超过3秒

###### 3：选择操作响应时间不超过500毫秒

#### TL-大量文件选择操作性能

##### PD-前置条件：用户已登录；签署流程包含100个以上文件；

##### 步骤一：执行全选操作

##### 步骤二：执行取消全选操作

##### 步骤三：执行批量选择操作

##### ER-预期结果

###### 1：全选操作响应时间不超过1秒

###### 2：界面不出现卡顿现象

###### 3：选择状态更新及时准确

## 安全测试

### 权限验证测试

#### TL-无作废权限用户访问控制

##### PD-前置条件：用户已登录但无作废权限；存在已完成签署流程；

##### 步骤一：尝试访问作废功能

##### 步骤二：查看系统响应

##### ER-预期结果

###### 1：系统拒绝访问作废功能

###### 2：给出权限不足提示

###### 3：不显示作废相关操作按钮

#### TL-跨用户数据访问控制

##### PD-前置条件：用户A已登录；尝试作废用户B的签署流程；

##### 步骤一：尝试访问其他用户的签署流程作废功能

##### 步骤二：查看系统响应

##### ER-预期结果

###### 1：系统拒绝跨用户操作

###### 2：给出权限验证失败提示

###### 3：不泄露其他用户数据

#### TL-数据传输安全验证

##### PD-前置条件：用户已登录；网络监控工具已准备；

##### 步骤一：执行作废操作

##### 步骤二：监控数据传输过程

##### 步骤三：验证数据加密情况

##### ER-预期结果

###### 1：敏感数据传输加密

###### 2：不在客户端暴露敏感信息

###### 3：符合数据安全规范

## 兼容性测试

### 多端验证测试

#### TL-PC端作废功能验证

##### PD-前置条件：用户在PC端已登录；使用主流浏览器；

##### 步骤一：在PC端执行作废操作

##### 步骤二：验证弹窗展示效果

##### 步骤三：验证交互功能

##### ER-预期结果

###### 1：PC端弹窗展示正常

###### 2：鼠标操作响应正确

###### 3：界面布局合理美观

#### TL-H5端作废功能验证

##### PD-前置条件：用户在移动端H5已登录；

##### 步骤一：在H5端执行作废操作

##### 步骤二：验证弹窗展示效果

##### 步骤三：验证触摸交互功能

##### ER-预期结果

###### 1：H5端弹窗适配移动端屏幕

###### 2：触摸操作响应正确

###### 3：界面元素大小适合触摸操作

#### TL-微信小程序端作废功能验证

##### PD-前置条件：用户在微信小程序端已登录；

##### 步骤一：在小程序端执行作废操作

##### 步骤二：验证弹窗展示效果

##### 步骤三：验证小程序特有交互

##### ER-预期结果

###### 1：小程序端功能正常

###### 2：符合小程序交互规范

###### 3：性能表现良好

## 冒烟测试

### 核心功能验证

#### MYTL-业务模板允许部分作废时基本功能验证

##### PD-前置条件：用户已登录；存在已完成的签署流程；业务模板配置允许部分作废；

##### 步骤一：进入"我管理的/我发起的"列表页面

##### 步骤二：选择已完成的签署流程点击作废

##### 步骤三：在作废弹窗中选择部分文件

##### 步骤四：点击确定提交

##### ER-预期结果

###### 1：弹窗正确展示文件选择功能

###### 2：文件选择操作正常

###### 3：成功发起部分文件作废流程

#### MYTL-业务模板不允许部分作废时功能验证

##### PD-前置条件：用户已登录；存在已完成的签署流程；业务模板配置不允许部分作废；

##### 步骤一：进入作废流程

##### 步骤二：查看作废弹窗

##### 步骤三：提交作废

##### ER-预期结果

###### 1：不显示文件选择功能

###### 2：直接作废所有文件

###### 3：作废流程正常完成

#### MYTL-文件状态控制验证

##### PD-前置条件：用户已登录；存在包含不同状态文件的签署流程；

##### 步骤一：打开作废弹窗

##### 步骤二：查看文件状态展示

##### 步骤三：尝试选择不同状态文件

##### ER-预期结果

###### 1：未作废文件可正常选择

###### 2：作废中和已作废文件不可选择

###### 3：状态标记正确显示

#### MYTL-提交校验功能验证

##### PD-前置条件：用户已登录；业务模板允许部分作废；

##### 步骤一：打开作废弹窗

##### 步骤二：不选择任何文件

##### 步骤三：点击确定提交

##### ER-预期结果

###### 1：系统提示"至少选择一个未作废的签署文件"

###### 2：作废操作被阻止

###### 3：用户可重新选择文件

#### MYTL-全选功能验证

##### PD-前置条件：用户已登录；业务模板允许部分作废；存在多个可选择文件；

##### 步骤一：打开作废弹窗

##### 步骤二：点击全选

##### 步骤三：提交作废

##### ER-预期结果

###### 1：所有可选择文件被选中

###### 2：不可选择文件保持原状态

###### 3：作废流程成功发起

#### MYTL-PC端基本功能验证

##### PD-前置条件：用户在PC端已登录；使用主流浏览器；

##### 步骤一：在PC端执行完整作废流程

##### 步骤二：验证界面展示和交互

##### ER-预期结果

###### 1：PC端功能完全正常

###### 2：界面展示美观合理

###### 3：交互响应及时准确

## 线上验证测试

### 核心业务流程验证

#### PATL-完整部分文件作废流程验证

##### PD-前置条件：线上环境正常；用户已登录；存在已完成的签署流程；

##### 步骤一：进入"我管理的/我发起的"列表

##### 步骤二：选择签署流程进行作废

##### 步骤三：在弹窗中选择部分文件

##### 步骤四：提交作废并跟踪流程状态

##### ER-预期结果

###### 1：作废弹窗正常展示

###### 2：文件选择功能正常

###### 3：作废流程成功发起并正常流转

#### PATL-业务模板配置生效验证

##### PD-前置条件：线上环境正常；存在不同配置的业务模板；

##### 步骤一：测试允许部分作废的模板

##### 步骤二：测试不允许部分作废的模板

##### 步骤三：验证配置生效情况

##### ER-预期结果

###### 1：允许部分作废时显示选择功能

###### 2：不允许部分作废时隐藏选择功能

###### 3：配置切换生效及时

#### PATL-文件状态判断准确性验证

##### PD-前置条件：线上环境正常；存在各种状态的签署文件；

##### 步骤一：查看不同状态文件的展示

##### 步骤二：验证可选择性控制

##### 步骤三：确认状态标记准确性

##### ER-预期结果

###### 1：文件状态判断准确

###### 2：可选择性控制正确

###### 3：状态标记显示准确

#### PATL-校验规则生效验证

##### PD-前置条件：线上环境正常；用户已登录；

##### 步骤一：尝试不选择文件提交

##### 步骤二：验证错误提示

##### 步骤三：选择文件后重新提交

##### ER-预期结果

###### 1：校验规则正确生效

###### 2：错误提示准确友好

###### 3：正常流程可顺利完成

#### PATL-多端兼容性验证

##### PD-前置条件：线上环境正常；准备PC和移动端设备；

##### 步骤一：在PC端执行作废操作

##### 步骤二：在H5端执行作废操作

##### 步骤三：对比功能表现

##### ER-预期结果

###### 1：PC端功能完全正常

###### 2：H5端适配良好

###### 3：功能一致性良好

#### PATL-性能表现验证

##### PD-前置条件：线上环境正常；存在包含较多文件的签署流程；

##### 步骤一：打开包含大量文件的作废弹窗

##### 步骤二：执行文件选择操作

##### 步骤三：提交作废请求

##### ER-预期结果

###### 1：弹窗加载时间合理

###### 2：选择操作响应及时

###### 3：提交处理速度正常