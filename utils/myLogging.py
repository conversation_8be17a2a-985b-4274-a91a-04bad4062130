# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2021/8/24 18:09 
# <AUTHOR> <PERSON><PERSON><PERSON> 
# @File : myLogging.py

import logging
import os
from logging.handlers import TimedRotatingFileHandler


class Logger(object):
    # 设置日志格式
    FORMAT = '%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s'

    # log文件路径 /esign-docs/logs/service.log
    LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')

    # 日志级别关系映射
    kv = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'critical': logging.CRITICAL
    }

    def __init__(self, filename, level='info', when='D', backupCount=20, fmt=FORMAT):
        """
        每天记录一个日志文件，保存20天
        :param filename: 写入日志的文件名
        :param level: 日志级别
        :param when: 日志切分周期
        :param backupCount: 每完成一个周期的日志写入文件，最多保留的 日志文件数，旧的日志文件将被删除
        :param fmt: 日志格式
        """
        if not os.path.exists(self.LOG_DIR):
            os.makedirs(self.LOG_DIR)
        file = os.path.join(self.LOG_DIR, filename)
        if not os.path.exists(file):
            open(file, 'w', encoding='utf-8')
        self.logger = logging.getLogger(file)
        fmtStr = logging.Formatter(fmt)  # 日志格式
        sh = logging.StreamHandler()  # 输出到控制台
        sh.setFormatter(fmtStr)  # 日志在控制台的输出格式
        th = TimedRotatingFileHandler(file, when=when, interval=1, backupCount=backupCount, encoding='utf-8')
        th.setFormatter(fmtStr)

        self.logger.setLevel(self.kv.get(level))  # 日志级别
        self.logger.addHandler(sh)
        self.logger.addHandler(th)


if __name__ == '__main__':
    log = Logger('service.log', level='info').logger
    log.debug('debug')
    log.info('info')
    log.warning('警告')
    log.error('error')
