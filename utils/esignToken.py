# -*- coding: utf-8 -*-
# @Description :
# @Time : 2023/4/12 16:53
# <AUTHOR> zhu<PERSON>
# @File : esignToken.py
import hmac
import json
from functools import lru_cache
from hashlib import sha256

from httpRequest.schemas.loginSchema import TokenObj
from httpRequest.service.loginService import LoginService
from utils import ENV, log
from utils.common import encryptKey
import json
MAIN_HOST = ENV('esign.projectHost')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')

"""aes解密 key"""
AES_KEY = '&eUTYc872yx*@Q%0'


def createPortalTokenObj(account, password) -> TokenObj:
    """
    统一门户token
    :param account:
    :param password:
    :return:
    """
    loginService = LoginService(MAIN_HOST)
    tokenObj = loginService.createToken(PROJECT_ID, account, password)
    return tokenObj

def openApiSignature(data):
    """
    openapi 请求体加密
    :param data: 请求体
    :return:
    """
    appSecret = PROJECT_SECRET.encode('utf-8')  # 秘钥
    try:
        if type(data) == dict or type(data) == list:
            data = json.dumps(data).encode('utf-8')
        else:
            data = data.encode('utf-8')
        signature = hmac.new(appSecret, data, digestmod=sha256).hexdigest()
        return signature
    except Exception as e:
        log.info('加密请求体为：%r' % data)
        log.error('openapi请求体加密失败，%s' % str(e))

def openApiSignature2(data,appSecret):
    """
    openapi 请求体加密
    :param data: 请求体
    :param appSecret: 项目密钥
    :return:
    """
    appSecret0 = appSecret.encode('utf-8')  # 秘钥
    try:
        if type(data) == dict or type(data) == list:
            data = json.dumps(data).encode('utf-8')
        else:
            data = data.encode('utf-8')
        signature = hmac.new(appSecret0, data, digestmod=sha256).hexdigest()
        return signature
    except Exception as e:
        log.info('加密请求体为：%r' % data)
        log.error('openapi请求体加密失败，%s' % str(e))

def createVerCodeToken(phoneOrMail, userTerritory=None):
    """
    外部/内部签署人，通过验证码登录获取token
    :param phoneOrMail: 手机号或者邮箱, 明文
    :param userTerritory: '2'-外部，None-内部
    :return: token
    """

    # 明文加密
    encryptPhoneOrMail = encryptKey(phoneOrMail)

    loginService = LoginService(MAIN_HOST)
    token = loginService.verCodeLogin(encryptPhoneOrMail, userTerritory)
    return token

@lru_cache(128)
def createManageToken(account, password):
    """
    管理平台token
    :param account:
    :param password:
    :return:
    """
    loginService = LoginService(MAIN_HOST)
    token = loginService.getManageToken(account, password)
    if type(token) == dict :
        if token.get('status') == 906:
            token = createManageToken(account, password)
    return token


def openApiSignature(data):
    """
    openapi 请求体加密
    :param data: 请求体
    :return:
    """
    appSecret = PROJECT_SECRET.encode('utf-8')  # 秘钥
    try:
        if isinstance(data, (list, dict)):
            data = json.dumps(data)
        data = data.encode('utf-8')
        signature = hmac.new(appSecret, data, digestmod=sha256).hexdigest()
        return signature
    except Exception as e:
        log.info('加密请求体为：%r' % data)
        log.error('openapi请求体加密失败，%s' % str(e))

def openApiSignature2(data,appSecret):
    """
    openapi 请求体加密
    :param data: 请求体
    :param appSecret: 项目密钥
    :return:
    """
    appSecret0 = appSecret.encode('utf-8')  # 秘钥
    try:
        if type(data) == dict or type(data) == list:
            data = json.dumps(data).encode('utf-8')
        else:
            data = data.encode('utf-8')
        signature = hmac.new(appSecret0, data, digestmod=sha256).hexdigest()
        return signature
    except Exception as e:
        log.info('加密请求体为：%r' % data)
        log.error('openapi请求体加密失败，%s' % str(e))

def createVerificationCode():
    """
    统一登录的图形验证码
    :return:
    """
    loginService = LoginService(MAIN_HOST)
    res = loginService.getVerificationCode()
    return res.headerCode


def parseToken(willingUrl):
    """
    code获取到的加密token，aes解密 作为密码授权的接口token
    :param willingUrl:  auth/willing接口返回的url
    :return:
    """
    loginService = LoginService(MAIN_HOST)
    token = loginService.parseTokenByCode(AES_KEY, willingUrl)
    return token

def epaasHeader(epaasUrl):
    """
    epaas模板文档对接天印使用的接口的请求头信息
    :param epaasUrl: 模板详情返回的编辑地址/查看地址
    :return:
    """
    headers = {
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": getTplToken(epaasUrl)
    }
    return headers

def getTplToken(epaasUrl):
    """
    epaas模板文档对接天印使用的接口的token
    :param epaasUrl: 模板详情返回的编辑地址/查看地址
    :return:
    """
    from urllib.parse import parse_qs, urlparse
    parsed_url = urlparse(epaasUrl)
    query_params = parse_qs(parsed_url.query)  # 返回字典（值可能是列表）

    return query_params.get("tplToken", [None])[0]

if __name__ == "__main__":
    ur = "/ec-manage-web/authenticationPc?tokenKey=cC83emhYUVBEQzZIQ1FRWE1DMnhyOG9YdUM0ZmRWMjNEY2tLL0M0VDlKSHBJRThkeW1vTGhwTXE3cC9NNGVRUzluNU1CakdYdmpPY1hlclZqWFI5ejdZdHEweTB4L0FxOXFDeUk5dnVyREVSOS9keVBKZDk4QW1idmwxaUxQdmw3MG91NStpdDhpbzdQdXFtUGV1QnB0TUFjUUgzeGhkblJqMm8yN0h5cmlnY3pSeldpc25TeW1iQ0hXdDB6MkNUMVBTL0ZNYzZnaEpCVWcvblFwQU9hR3hoaXdOTm5VcTQ3TEFsWi9Vb0NLOEVyODFGNDhZN3JqdlhodHlYbk03cmZONXN3b2xZOWNkN2FQY1lHQ1N1c0dXbHFtVWcxZnNleE5QUlk1MlBFRHNBdyt4U1JscTVweDYrZGFUNHppWDl6djVSclVtQ3dpR0lMczhpdGE4T3dBPT0="
    token4 = "cC83emhYUVBEQzZIQ1FRWE1DMnhyOG9YdUM0ZmRWMjNEY2tLL0M0VDlKSHBJRThkeW1vTGhwTXE3cC9NNGVRUzluNU1CakdYdmpPY1hlclZqWFI5ejdZdHEweTB4L0FxOXFDeUk5dnVyREVSOS9keVBKZDk4QW1idmwxaUxQdmw3MG91NStpdDhpbzdQdXFtUGV1QnB0TUFjUUgzeGhkblJqMm8yN0h5cmlpUVZsQmhFZVVHNHJxL0tvekVzbnc3bkVaQm90NGpsYzVXNy9iUUpiZVVWR3hoaXdOTm5VcTQ3TEFsWi9Vb0NLOE45dmFiRGdvMnBoNlNDSzFmbUNScFA5VDFubWNmSjVCYm4rYmVCRzBaNE52Zlp1ZWtWa0xERER2M2Q0RU5HWFFFRVM1clJ0TUR2blhrVmZWL2RkaHVLNCt0Y21iUnNFdHU5R2ZVUm1XZXNnPT0="
    code = '+iaZlwTnJC3+6CKHRK/G/KCp1zzmhPYlXUTkMOi/RdeKvnFhGxPaAomjieMvmJ7A'
    enPwd = 'Tsign@123'
    pwd1 = '19112100006'

    dePwd = "H/7VUSl8DDkh9KM9M/MgDlJEJh/vJcO8gFiIXT6md69pBrwC1HiouS2nOatIhSatVCa0D8Iwn9YZKg6HdnZ8UzvkR7Z+ZXx3R+6zWv+NG8yvXXP85SMXOcWAO0H6qWTsg+DwvGPMgnqUtt2iloIJ+2rzG0JweXkU9koJ3ckzr2fbr+4wv9shVpCF2xMop/J8McBfzO9eCOSyglAtX3xS9MwP6Zii02KS2hb3FTBfp1AVE23dX/V7xcCf+tC10O+6yQdk1l+yhrc1lIeu/6ZZeuGCYR9Zh/QZpK2VMh7aO2BEA2wIA2FX/aQPSbDLi5FfMsjLGWGMIx/a6NdnPIDlTw=="
    parseToken(code)