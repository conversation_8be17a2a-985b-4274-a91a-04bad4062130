import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.padding import PKCS7
import os
import requests
from utils import ENV
from utils.esignToken import openApiSignature

MAIN_HOST = ENV('esign.projectHost')
OPENAPI_HOST = ENV('esign.gatewayHost')
PROJECT_ID = ENV("esign.projectId")
PROJECT_SECRET = ENV('esign.projectSecret')

def get_aes_encrypt(param):
    """
    AES加密，适用于各种手机号证件号明文转密文
    :param param: 明文
    :return: 密文
    """
    encryption_key = b'&eU123n5ryx*@Qum'
    cipher = Cipher(algorithms.AES(encryption_key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()
    padder = PKCS7(128).padder()
    padded_data = padder.update(param.encode('utf-8')) + padder.finalize()
    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    encrypted_data = base64.b64encode(ciphertext).decode('utf-8')
    return encrypted_data

def get_aes_decrypt(param):
    """
    AES解密，适用于各种手机号证件号密文转明文
    :param param: 密文
    :return: 明文
    """
    decryption_key = b'&eU123n5ryx*@Qum'
    cipher = Cipher(algorithms.AES(decryption_key), modes.ECB(), backend=default_backend())
    decryptor = cipher.decryptor()
    ciphertext = base64.b64decode(param.encode('utf-8'))
    decrypted_data = decryptor.update(ciphertext) + decryptor.finalize()
    unpadder = PKCS7(128).unpadder()
    unpadded_data = unpadder.update(decrypted_data) + unpadder.finalize()
    return unpadded_data.decode('utf-8')

def detailInnerUsers(headers, data):
    """
    查询内部用户的详情
    :param headers:
    :param data: {"customAccountNo": customAccountNo,"userCode": userCode}
    :return:
    """
    # data = {
    #     "customAccountNo": customAccountNo,
    #     "userCode": userCode
    # }
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': headers}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/detail',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response

def getInnerUserCustomAccountNo(userCode):
    """
    查询内部用户账号
    """
    data = {
        "customAccountNo": "",
        "userCode": userCode
    }
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': openApiSignature(data)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/detail',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response.get('data')[0].get('customAccountNo')

def detailOuterUsers(headers, data):
    """
    查询外部用户的详情
    :param headers:
    :param data: {"customAccountNo": customAccountNo,"userCode": userCode}
    :return:
    """
    # data = {
    #     "customAccountNo": customAccountNo,
    #     "userCode": userCode
    # }
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': headers}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerUsers/detail',
                             json=data, headers=headers)
    json_response = response.json()
    print('detailOuterUsers-[request]:', data)
    print('detailOuterUsers-[respone]:', json_response)
    return json_response

def updateOuterUsers(headers, data):
    """
    修改相对方企业成员的信息，手机号和邮箱不传则不修改，传空字符串则置空
    :param headers:
    :param data: {"customAccountNo": customAccountNo,"userCode": userCode}
    :return:
    """
    # data = {
    #   "customAccountNo": "wzhengml",
    #   "mobile": "",
    #   "otherOrganization": [
    #     {
    #       "otherCustomOrgNo": "919900212351852455"
    #     }
    #   ]
    # }
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': headers}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerUsers/update',
                             json=data, headers=headers)
    json_response = response.json()
    print('updateOuterUsers-[request]:', data)
    print('updateOuterUsers-[respone]:', json_response)
    return json_response

def detailInnerOrg(headers, data):
    """
    查询内部组织的详情
    :param headers:
    :param data: {"customOrgNo": customOrgNo,"organizationCode": organizationCode}
    :return:
    """
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': headers}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/detail',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response

def getInnerOrgCustomAccountNo(orgCode):
    """
    查询内部组织账号
    """
    data = {
        "customOrgNo": "",
        "organizationCode": orgCode
    }
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': openApiSignature(data)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/detail',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response.get('data')[0].get('customOrgNo')

def detailOuterOrg(headers, data):
    """
    查询外部组织的详情
    :param headers:
    :param data: {"customOrgNo": customOrgNo,"organizationCode": organizationCode}
    :return:
    """
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'x-timevale-signature': headers}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerOrganizations/detail',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response

def detailInnerOrgWebApi(headers, organizationName):
    """
    查询内部组织的详情
    :param headers:
    :param data: {"customOrgNo": customOrgNo,"organizationCode": organizationCode}
    :return:
    """
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'token': headers}
    data = {"params":{"organizationName": organizationName,"organizationTerritory":"1"},"domain":"admin_platform"}
    response = requests.post(url=MAIN_HOST + '/manage/orguser/org/getOrganizationListByOrgCodeName',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response

def addOuterOtherOrg(userCode, organizationCode):
    """
    给相对方企业成员添加兼职企业
    :param userCode:
    :param organizationCode:
    :return:
    """
    from utils.esignToken import openApiSignature

    query01 = {"userCode": userCode}
    signature01 = openApiSignature(query01)
    data01 = detailOuterUsers(signature01, query01)
    otherOrganization = data01.get('data')[0].get('otherOrganization')
    data02 = {"otherOrganizationCode": organizationCode}
    orgCode = []
    orgCode.append(data02)
    if otherOrganization:
        for item01 in otherOrganization:
            data03 = {"otherOrganizationCode": item01.get('otherOrganizationCode')}
            orgCode.append(data03)
    query02 = {"userCode": userCode, "otherOrganization": orgCode}
    signature02 = openApiSignature(query02)
    data02 = updateOuterUsers(signature02, query02)
    return data02.get('data').get('userCode')


def delete_inner_org(orgCode):
    """
    删除内部企业
    """
    data01 = {"organizationCode": orgCode}
    headers01 = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(data01)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/delete',
                             json=data01, headers=headers01)
    json_response = response.json()
    return json_response

def delete_outer_org(orgCode):
    """
    删除相对方企业
    """
    data01 = {"organizationCode": orgCode}
    headers01 = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(data01)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerOrganizations/delete',
                             json=data01, headers=headers01)
    json_response = response.json()
    return json_response

def delete_inner_user(userCode):
    """
    删除内部用户
    """
    data01 = {"userCode": userCode}
    headers01 = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(data01)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/delete',
                             json=data01, headers=headers01)
    json_response = response.json()
    return json_response

def delete_inner_user_by_name(userName,userCode=None):
    """
    通过用户名称或是code删除符合条件的用户
    """
    if userName:
        data01 = {"name":  userName }
        res01 = detailInnerUsers(openApiSignature(data01), data01)
        list01 = res01.get('data')
        if len(list01) > 0:
            for item1 in list01:
                delete_inner_user(item1.get('organizationCode'))
    if userCode:
        delete_inner_user(userCode)

def delete_inner_org_by_name(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的企业
    """
    if orgName:
        data01 = {"name":  orgName }
        res01 = detailInnerOrg(openApiSignature(data01), data01)
        list01 = res01.get('data')
        if len(list01) > 0:
            for item1 in list01:
                res02 = delete_inner_org(item1.get('organizationCode'))
                if res02.get('code')==1111117:
                    #todo 组织下有用户需要先删除用户，才能删除企业
                    print('组织下面有用户，需要手动删除')
    if orgCode:
        delete_inner_org(orgCode)

def delete_outer_org_by_name(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的相对方企业
    """
    if orgName:
        data01 = {"name":  orgName }
        res01 = detailOuterOrg(openApiSignature(data01), data01)
        list01 = res01.get('data')
        if len(list01) > 0:
            for item1 in list01:
                res02 = delete_outer_org(item1.get('organizationCode'))
                if res02.get('code')==1111117:
                    #todo 组织下有用户需要先删除用户，才能删除企业
                    print('组织下面有用户，需要手动删除')
    if orgCode:
        delete_outer_org(orgCode)

def getCmcKeyId(token, configCode, groupId=None, moduleId=None):
    """
    获取系统参数的key
    :param headers:
    :param configCode:
    :param groupId:
    :param moduleId:
    :return:
    """
    if moduleId == None:
        moduleId = "fe9053c9992311ec87635254002d4645"
    if groupId == None:
        groupId = "171001"
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'token': token}
    str0 = "/manage/v1/cmc/values/get?moduleId="+moduleId+"&groupId="+str(groupId)+"&thirdId=&lang=zh-CN"
    res1 = requests.get(url=MAIN_HOST + str0, json=None, headers=headers)
    res2 = res1.json()
    if res2.get('code') == 10000000:
        valueLists = res2.get('data').get('configValues')
        for item in valueLists:
            if item.get('code') == configCode:
                return item.get('keyId')
    return None


def download_file_to_fixed_path(url, filename):
    """
    通过下载链接下载文件到指定路径（硬编码）。

    参数:
    url (str): 文件的下载链接。

    返回:
    bool: 下载成功返回 True，失败返回 False。
    """
    fixed_path = "tests/data/download/" + filename  # 拼接硬编码路径和文件名
    try:
        # 发送HTTP GET请求获取文件
        response = requests.get(url, stream=True)
        response.raise_for_status()  # 检查请求是否成功

        # 创建文件路径的目录（如果不存在）
        os.makedirs(os.path.dirname(fixed_path), exist_ok=True)

        with open(fixed_path, 'wb') as file:
            for data in response.iter_content(1024):  # 每次下载1KB的数据块
                file.write(data)

        print(f"文件已成功下载到 {fixed_path}")
        return True

    except requests.RequestException as e:
        print(f"下载失败: {e}")
        return False

def delete_file(filename):
    """
    删除工程tests/data/download/路径的文档
    """
    path = "tests/data/download/" + filename  # 拼接硬编码路径和文件名
    if os.path.exists(path):
        os.remove(path)
        print(f"Deleted file: {path}")
    else:
        print(f"File not found: {path}")

if __name__ == "__main__":
    a = '7qGk89JzPZHp3/RUDIsO5Q=='
    # print(addOuterOtherOrg('zhengml2 ', '919900212351852455 '))