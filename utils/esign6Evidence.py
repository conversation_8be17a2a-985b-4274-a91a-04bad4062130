import time

from httpRequest.api.apiEvidence import ApiEvidence
from httpRequest.response.evidenceResp import EvidenceResp
from utils import ENV, log

"""常量/全局变量大写，放前面"""
MAIN_HOST = ENV('esign.projectHost')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')
OPENAPI_HOST = ENV('esign.gatewayHost')


def query_link(header, bizId, linkTemplateCode):

    apiEvidence = ApiEvidence(MAIN_HOST)
    query01 = EvidenceResp(apiEvidence)
    header['navId'] = '1523545067669819394'
    i = 0
    totalCount = 0
    while totalCount == 0 and i < 11:
        res = query01.queryLinkResp(header, bizId, linkTemplateCode)
        i = i + 1
        totalCount = res['totalCount']
        if totalCount > 0:
            return res
        time.sleep(6)

