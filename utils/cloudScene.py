# -*- coding: utf-8 -*-
# @Description : 公有云请求需要使用的头信息。 （如实名授权请求）
# @Time : 2023/4/20 13:09
# <AUTHOR> wenmin

import hmac
import json
from hashlib import sha256
from time import sleep

from httpRequest.service.cloudService import CloudService
from utils import ENV, log

cloudLoginUrl = ENV('cloud.login.Url')
cloudSaasUrl = ENV('cloud.saas.Url')
cloudH5Url = ENV('cloud.h5.Url')
cloudFootStoneWillUrl = ENV('cloud.will.Url')
cloudUrl = ENV('cloudUrl')
datafactoryUrl = ENV('datafactory.Url')
appId = ENV('appId')
appSecret = ENV('appSecret')


def telecom3Oid(mobile):
    """
    saas已有个人账号的实名
    :param mobile:
    :param password:
    :return:
    """
    # 数据工厂实例
    cloudService01 = CloudService(datafactoryUrl)
    # 开放网关实例
    cloudService02 = CloudService(cloudUrl)
    res = cloudService02.identityInfoPsnService(appId,mobile)
    if res[1] == 0:
        # 未实名需要进行实名
        res = cloudService01.telecom3OidService(appId, mobile)
    return res

def threeFactorsOid(mobile,orgName):
    """
    saas已有企业账号的实名
    :param mobile:
    :param orgName:
    :return:
    """
    # 数据工厂实例
    cloudService01 = CloudService(datafactoryUrl)
    # 开放网关实例
    cloudService02 = CloudService(cloudUrl)
    res0 = cloudService02.identityInfoPsnService(appId,mobile)
    psnOid = res0[0]
    if res0[1] == 0:
        # 未实名需要进行实名：会重置经办人时候后进行个人实名
        res00 = cloudService01.telecom3OidService(appId, mobile)
    res1 = cloudService02.identityInfoOrgService(appId,orgName)
    orgOid = res1[0]
    if res1[1] == 0 or orgOid == 1435203: #企业未实名或是企业账号不存在或已注销
        # 未实名需要进行实名：会重置企业和经办人时候后进行个人实名和企业实名
        res11 = cloudService01.threeFactorsOidService(appId, mobile, orgName)
        orgOid = res11[1]
    return psnOid,orgOid

def authPsn(mobile):
    """
    saas已有个人账号的授权
    :param mobile:
    :param password:
    :return:
    """
    # saas页面实例
    cloudService01 = CloudService(cloudLoginUrl)
    # 开放网关实例
    cloudService02 = CloudService(cloudUrl)
    # 授权
    authFlowId = cloudService02.psnAuthService(appId, mobile)
    code = cloudService01.confirmAuth(appId, mobile, authFlowId)

def authOrg(mobile,orgName,orgOid=None):
    """
    saas已有企业账号的授权
    :param mobile:
    :param password:
    :return:
    """
    # saas页面实例
    cloudService01 = CloudService(cloudLoginUrl)
    # 开放网关实例
    cloudService02 = CloudService(cloudUrl)
    if orgOid==None:
        res1 = cloudService02.identityInfoOrgService(appId, orgName)
        orgOid = res1[0]
    # 个人授权
    authFlowId = cloudService02.psnAuthService(appId, mobile)
    code = cloudService01.confirmAuth(appId, mobile, authFlowId)
    # 企业授权
    authFlowId = cloudService02.orgAuthService(appId, mobile, orgOid)
    code = cloudService01.confirmAuth(appId, mobile, authFlowId)

def cloudWillSMS(mobile,applyId):
    """
    公有云意愿-短信意愿
    :param mobile:
    :param password:
    :return:
    """
    # saas页面实例
    cloudService01 = CloudService(cloudLoginUrl)
    headers = cloudService01.createSaasWebLoginHeaders(appId, mobile)
    # 2实例
    cloudService02 = CloudService(cloudFootStoneWillUrl)
    res = cloudService02.willSMSService(headers,appId, applyId, mobile)
    return res

def cloudRealNameAndAuthScene(type,param):
    """
    集合场景
    """
    #个人用户未实名的时候，去实名
    if type==0:
        res = telecom3Oid(param.get("mobile"))
        return res[0]
    # 个人用户已实名未授权，去授权
    if type==1:
        res = authPsn(param.get("mobile"))
        return res[0]
    # 个人用户未实名未授权，去实名+授权
    if type == 2:
        res = telecom3Oid(param.get("mobile"))
        psnOid = res[0]
        authPsn(param.get("mobile"))
        return psnOid
    # 企业经办人实名
    if type == 3:
        res = threeFactorsOid(param.get("mobile"),param.get("orgName"))
        psnOid = res[0]
        orgOid = res[1]
        return psnOid,orgOid
    # 企业经办人实名并授权
    if type == 4:
        res = threeFactorsOid(param.get("mobile"),param.get("orgName"))
        psnOid = res[0]
        orgOid = res[1]
        authOrg(param.get("mobile"),param.get("orgName"),orgOid)
        return psnOid, orgOid
    if type == 5:
        authOrg(param.get("mobile"), param.get("orgName"))


def cloudWillScene(type,param):
    #相对方个人短信意愿认证
    if type == 0:
        cloudWillSMS(param.get("mobile"), param.get("applyId"))

def realNameReset(appId,mobile):
    cloudService = CloudService(datafactoryUrl)
    cloudService.resetRealName(appId,mobile)

# if __name__ == "__main__":
#     mobile = '19112100001'
#     tmp0= {"mobile": mobile}
#     res =  cloudRealNameAndAuthScene(2, tmp0)
#     log.info('XXXXX：%r' % res)