#下载
import re
import zipfile
import requests

import os
import time
print("开始执行安装任务")
val = os.system('pip install -i  https://pypi.tuna.tsinghua.edu.cn/simple pdfplumber')
print(val)
import pdfplumber
def compareFile(originFile,zipurl):
    #下载文件
    name = str(time.time()).replace(".","")
    downUrl = zipurl  #下载的url
    os.mkdir(name)
    filePwd = "./{}/".format(name)
    fileName = "./{}/aaa.zip".format(name)
    downRes = requests.get(url=downUrl,stream=True)
    with open(fileName,"wb") as code:
      code.write(downRes.content)

    #解压缩
    print(1111)
    print(fileName)
    f = zipfile.ZipFile(fileName,"r")
    for file in f.namelist():
        # print(file)
        f.extract(file,filePwd)

    #查找公证处的pdf
    for pdfN in os.listdir(filePwd):
        if "C" in pdfN:
            name = pdfN
            break
    # print(os.listdir(filePwd))
    #打开后对比
    pageA = []
    pageB = []
    #下载后的公证处证明
    path = filePwd+name
    print("path1")
    print(path)
    pdf = pdfplumber.open(path)  # 打开pdf文件
    content1 = ""
    for page in pdf.pages:
        pageC = page.extract_text()
        # print(aa)
        content1 += pageC
        pageA.append(pageC)

    #原版证明
    path = "./data/" + originFile
    print("path2")
    print(path)
    content2 = ""
    pdf = pdfplumber.open(path) #打开pdf文件
    for page in pdf.pages:
        pageC = page.extract_text()
        content2 += pageC
        pageB.append(pageC)

    #将md5 和  riqi 转换成  统一的字符
    # print(content2)
    content1 = reContent(content1)
    content2 = reContent(content2)
    content1 = content1.split()
    content2 = content2.split()
    content1 = "".join(content1)
    content2 = "".join(content2)
    res = (content1 == content2)
    if not res :
        print(content1)
        print(content2)
    return res

def reContent(con):
#     con2 = re.sub("202.*-?[0-9]", "替换数据", con)
#     # print(bb)
#     # 替换事务id
#     res = re.sub("：(.*)", "替换数据", con2)
    res = re.sub("[0-9A-Za-z]", "*", con)
    #print(res)
    return res

if __name__ == '__main__':
    originFile=  "C1712009096993001473.pdf"
    zipurl = "https://esignoss.esign.cn/1111563841/1378cc14-6b06-4e0f-9f81-d5623ed80c68/C1712009096993001473.zip?Expires=1697108439&OSSAccessKeyId=LTAI4G23YViiKnxTC28ygQzF&Signature=5jBv1%2FFUpdpB2ZMUSZsyM8X3RmY%3D"

    res = compareFile(originFile,zipurl)
   # print(res )