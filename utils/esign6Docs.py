import json
import random
import requests
import os
from utils import ENV
from utils import common
# 通用自动化测试文件类型
from utils.common import get_randomNo_32
from utils.esignToken import openApiSignature, getTplToken

COMMON_DOCNAME_TYPE = "autotest通用文件类型*************"
COMMON_DOCNAME_TYPE_CODE = "autotesttywjlx*************"
# 通用自动化测试文件夹
COMMON_DOCNAME_FOLDER = 'autotest通用文件夹*************'
COMMON_DOCNAME_FOLDER_CODE = 'autotesttywjj*************'
COMMON_TEMPLATE_NAME = "autotest通用模板*************"
sign01Code = ENV('sign01.userCode')
sign01No = ENV('sign01.accountNo')
org01Code = ENV('sign01.main.orgCode')
mainHost = ENV("esign.projectHost")
OPENAPI_HOST = ENV('esign.gatewayHost')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')
FILEKEY = ENV('fileKey')

def get_a_docConfig_type(headers):
    """
    文档中心-文件类型配置
    获取一个自动化测试专用的文件类型，若该文件类型不存在，则新建一个后返回
    """
    folder_list = search_docConfig_list(headers, COMMON_DOCNAME_FOLDER, 1)
    if folder_list.get('status') ==200:
        docfolder_list = folder_list.get('data')
        if len(docfolder_list) == 0:
            add_folder_response = add_docConfig(headers,COMMON_DOCNAME_FOLDER, 1, COMMON_DOCNAME_FOLDER_CODE)
            assert (add_folder_response['status'] == 200)
            folder_list = search_docConfig_list(headers,COMMON_DOCNAME_FOLDER, 1)
        folder_docUuid = folder_list['data'][0]['docUuid']
        Type_list = search_docConfig_list(headers,COMMON_DOCNAME_TYPE, 2)
        assert (Type_list['status'] == 200)
        docType_list = Type_list['data']
        if len(docType_list) == 0:
            add_type_response = add_docConfig(headers,COMMON_DOCNAME_TYPE, 2, COMMON_DOCNAME_TYPE_CODE, folder_docUuid)
            assert (add_type_response['status'] == 200)
            Type_list = search_docConfig_list(headers,COMMON_DOCNAME_TYPE, 2)
        try:
            type_docUuid = Type_list['data'][0]['docUuid']
            return type_docUuid
        except:
            raise Exception("调用方法get_a_docConfig_type获取通用自动化测试的文件类型失败")

def get_a_templateId(headers, contentNum, signatoryNum, fileType=None,addNewOne=0):
    """
    【文档中心】根据传参获取一个符合条件的模板
    param:contentNum: 内容域个数
    param:signatoryNum：签署区个数
    param:fileType：模板类型，值为pdf/word
    param:addNewOne 是否每次调用都新增一个模板，如果为0:则只会返回一个固定的模板，如果为1：则每次调用都返回一个新的模板
    """
    if fileType is None:
        fileType = 'pdf'
    templateName = COMMON_TEMPLATE_NAME+"-"+str(contentNum)+"-"+str(signatoryNum)+"-"+str(fileType)
    if addNewOne == 0:
        #查询符合该名称的模板是否已经存在且为发布状态
        template_data_query = {"params": {"docUuid": "", "page": 1, "size": 10, "status": "PUBLISH", "templateName":templateName}}
        templateRes = requests.post(url=mainHost + '/esign-docs/template/manage/list',
                                    json= template_data_query,
                                    headers=headers)
        templateResJson = templateRes.json()
        print('查询文档模板-[request]:', template_data_query)
        print('查询文档模板-[response]:', templateResJson)
        if templateResJson.get('status') == 200:
            if templateResJson.get('data').get('total') >= 1:
                return templateResJson.get('data').get('list')[0].get('templateUuid')
            else:
                if fileType == "pdf":
                    templateId = newTemplateIdV2(headers,contentNum, signatoryNum,templateName)
                    return templateId
                elif fileType =="word":
                    templateId = newTemplateId_word(headers,contentNum,signatoryNum,templateName)
                    return templateId
    else:
        templateName = templateName+"-"+str(common.get_randomNo_16())
        if fileType == "pdf":
            # templateId = newTemplateId(headers, contentNum, signatoryNum,templateName)
            templateId = newTemplateIdV2(headers, contentNum, signatoryNum,templateName)
            return templateId
        elif fileType == "word":
            templateId = newTemplateId_word(headers, contentNum, signatoryNum,templateName)
            return templateId

def newTemplateId(headers,contentNum, signatoryNum,templateName):
    fileKey = ENV('fileKey')
    response = requests.post(url=mainHost + '/esign-docs/template/owner/add',
                             json={"params": {"fileKey": fileKey,
                                              "templateName": templateName,
                                              "createUserOrg": org01Code,
                                              "docUuid": get_a_docConfig_type(headers),
                                              "allRange": 1}},
                             headers=headers)
    jsonResponse = response.json()
    print('创建文档模板-[response]:',jsonResponse)
    templateUuid = jsonResponse['data']['templateUuid']
    version = jsonResponse['data']['version']
    while contentNum > 0:
        contentNameCommon = "自动化测试-文本" + str(contentNum)
        if contentNum == 2:
            contentNameCommon = "自动化测试-文本0508"
        if contentNum == 1:
            contentNameCommon = "自动化测试-文本0507"
        requests.post(url=mainHost + '/esign-docs/template/content_domain/add',
                      json={"params": {"templateUuid": templateUuid, "version": version,
                                       "contents": [{"font": "SimSun", "fontColor": "BLACK",
                                                     "fontSize": 14, "required": 0,
                                                     "fontStyle": "Normal", "formatType": 0,
                                                     "textAlign": "Left", "length": 11,
                                                     "leaveGroup": True, "formatRule": "",
                                                     "offsetX": 0, "offsetY": 0,
                                                     "contentName": contentNameCommon,
                                                     "position": {
                                                         "edgeScope": 0, "height": 36,
                                                         "pageNo": "1", "posY": 200,
                                                         "posX": 200, "width": 200
                                                     }}]
                                       }},
                      headers=headers)
        contentNum = contentNum - 1
    while signatoryNum > 0:
        res = requests.post(url=mainHost + '/esign-docs/template/signatory_domain/add',
                      json={"params": {"templateUuid": templateUuid, "version": version, "addSignTime": 0,
                                       "signatories": [{"addSignTime": 0,
                                                        "name": "签署区-" + str(common.get_randomNo_16()),
                                                        "pageNo": "1", "signType": 1,
                                                        "posX": 300, "posY": 400,
                                                        "allowMove": False
                                                        }]}},
                      headers=headers)
        signatoryNum = signatoryNum - 1
    requests.post(url=mainHost + '/esign-docs/template/owner/saveOrPublish',
                  json={"params": {"templateUuid": templateUuid, "version": version, "isPublish": "true"}},
                  headers=headers)
    return templateUuid

# todo 根据内容域和签署区
def newTemplateId_word(headers, contentNum, signatoryNum,templateName):
    wordFileKey = os.environ.get("htmlDocxFileKey")
    response = requests.post(url=mainHost + '/esign-docs/template/owner/add',
                             json={"params": {"fileKey": wordFileKey,
                                              "templateName": templateName,
                                              "createUserOrg": os.environ.get("ORG-DOCS.orgCode"),
                                              "docUuid": get_a_docConfig_type(headers),
                                              "allRange": 1}},
                             headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['templateUuid']
    version = jsonResponse['data']['version']
    requests.post(url=mainHost + '/esign-docs/template/owner/saveOrPublish',
                  json={"params": {"templateUuid": templateUuid, "version": version, "isPublish": "true"}},
                  headers=headers)
    return templateUuid

def add_docConfig(headers, doc_name, doc_type, doc_code, parentUuid=""):
    """
    配置-文件类型配置
    新增文件类型
    """
    data = {"params": {
        "docName": doc_name,
        "docType": doc_type,
        "docCode": doc_code,
        "parentUid": parentUuid
    }}
    response = requests.post(url=mainHost + '/esign-docs/docConfigure/addDocConfigure',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response


def search_docConfig_list(headers, doc_name, doc_type):
    """
    doc_name:文件类型名称
    doc_type:文件夹/文件类型
    用于配置-文件类型配置-搜索文件类型
    """
    data = {"params": {
        "docName": doc_name,
        "docType": doc_type
    }}
    url1 = mainHost + '/esign-docs/docConfigure/getDocConfigureListWithTemplatePermission'
    headers['navid'] ='1523545065937572842'

    response = requests.post(url=url1,json=data, headers=headers)
    json_response = response.json()
    return json_response


def attachmentUpload(baseUrl, file, header):
    """
    文档中心：附件上传接口
    """
    url = baseUrl + '/esign-docs/fileSystem/attachmentUpload'
    response = requests.post(url=url, files=file, headers=header)
    print(response)
    jsonResponse = response.json()
    fileKey = jsonResponse['data']['fileKey']
    print(fileKey)
    return fileKey

def getSignerListTosubmit(params: list):
    """
    文档中心：组装发起时得签署方设置，若是参数为空，提供一个默认得内部个人签署方
    params e.g. [{"signNode":1,"signMode":1,"userType":1,"userCode":"zhengml","organizeCode":"f877ea68f18b47f29f775eb66363938b","departmentCode":"xxxx"}]
    """
    signersList = []
    paramsA = params
    if paramsA:
        for item0 in paramsA:
            signers = {"signMode": item0.get('signMode')}
            node = item0.get('signNode')
            signers['id'] = 'add-' + str(node)
            signers['nodeX'] = node
            if signersList and node == signersList[-1].get('nodeX'):
                continue
            signerList = []
            for item in params:
                order = item.get('signNode')
                if node == order:
                    signer = {"userCode": item.get('userCode'),"userName": item.get('userName'), 'id': 'add-' + str(order),"signerSnapshotId": str(get_randomNo_32())}
                    if "userType" in item.keys() :
                        signer['signerTerritory'] = item.get('userType')
                    else:
                        signer['signerTerritory'] = 1
                    if "organizationCode" in item.keys():
                        signer['signerType'] = 2  # 内部企业签署方
                        signer['organizationCode'] = item.get('organizationCode')
                        signer['organizationName'] = item.get('organizationName')
                        if item.get('userType') == 2:
                            signer['departmentCode'] = item.get('organizationCode')
                            signer['departmentName'] = item.get('organizationName')
                        else:
                            signer['departmentName'] = item.get('departmentName')
                            if "departmentCode" in item.keys():
                                signer['departmentCode'] = item.get('departmentCode')
                            else:
                                signer['departmentCode'] = item.get('organizationCode')
                    else:
                        signer['signerType'] = 1  # 内部个人签署方
                    signer['assignSigner'] = 1  # 1-指定,0-不指定
                    signerList.append(signer)
                else:
                    continue
            signers['signerList'] = signerList
            signersList.append(signers)
    else:
        signersList = [{"signMode":1,"id":"node-1","signerList":[{"signerType":1,"signerTerritory":1,"userCode":"$sign01Code","id":"add-1","signerSnapshotId": str(get_randomNo_32()),"autoSign":0,"legalSign":0,"onlyUkeySign":0,"assignSigner":0,"userName":ENV('sign01.userName')}]}]
    return signersList

def getSaveSignature(batchTemplateInitiationUuid, free: int,signerInfos: list,fileInfos: list):
    """
    获取签署区设置页的save接口的参数
    :param batchTemplateInitiationUuid:  发起电子列表的数据
    :param free:  是否自由签署. 0-自由签;1-指定签署
    :param signerInfos:  签署方设置 e.g.[{'signMode': 1, 'id': 'add-2', 'nodeX': 2, 'signerList': [{'userCode': 'zhengml', 'userName': None, 'id': 'add-2', 'signerSnapshotId': '51158456243987001070123714630551', 'signerTerritory': 1, 'signerType': 1, 'assignSigner': 1}]}]
    :param fileInfos: 签署文档 e.g. [{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey0"}},{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey1"}}]
    :return:
    """
    if free:
        filePreTaskInfos = []
        for item1 in signerInfos:
            for item2 in item1.get('signerList'):
                obj2 = { "signerId": item2.get('signerSnapshotId'), "userType": item2.get('signerTerritory'),
                        "signerType": item2.get('signerType'), "userCode": item2.get('userCode'),
                        "userName": item2.get('userName'), "legalSignFlag": 0, "personRealnameStatus": True, "orgRealnameStatus": True}

                if item2.get('signerType') == 2 :  #企业签署
                    signPosName = item2.get('organizationName')
                    signatureType = "COMMON-SEAL"
                    obj2['organizationCode'] = item2.get('organizationCode')
                    obj2['organizationName'] = item2.get('organizationName')
                    obj2['departmentName'] = item2.get('departmentName')
                    obj2['departmentCode'] = item2.get('departmentCode')
                else: #个人签署
                    signatureType = "PERSON-SEAL"
                    signPosName = item2.get('userName')
                sealInfos = []
                for item3 in fileInfos:
                    obj1 = {
                        "fileKey": item3.get("attachmentInfo").get("fileKey"),"signConfigs":[
                        {"addSignDate": False, "keywordInfo": None, "pageNo": "1", "posX": random.randint(100, 400), "posY": random.randint(100, 700), "edgeScope": None, "sealSignDatePositionInfo": None, "signPosName":signPosName, "signType":"COMMON-SIGN", "signatureType":signatureType, "allowMove": True}
                    ], "signatureTypeList":["PERSON-SEAL"], "sealIdList":[]
                            }
                    if signatureType == 'COMMON-SEAL':
                        obj1['signatureTypeList'].append(signatureType)
                    sealInfos.append(obj1)

                obj2["sealInfos"] = sealInfos
                filePreTaskInfos.append(obj2)
        obj3 = {"batchTemplateInitiationUuid": batchTemplateInitiationUuid , "filePreTaskInfos": filePreTaskInfos}
    else:
        obj3 = {"batchTemplateInitiationUuid": batchTemplateInitiationUuid, "filePreTaskInfos": []}
    print('[签署区设置]-response:', obj3)
    return obj3

def getSaveSignatureToSubmit(signerInfos: list,detailData: dict):
    """
    组装数据主要处理签署方和签署区之间的关联
    :param signerInfos:  getSignerListTosubmit()的出参
    :param detailData:  getInfo接口的出参的data对象信息
    :return:
    """
    arr1 = signerInfos
    for item1 in signerInfos:
        for item2 in item1.get('signerList'):
            signatoryList = []
            for item3 in detailData.get('docList'):
                for item4 in item3.get('templateSignConfigs'):
                    obj1 = {'signatoryId':item4.get('uuid')}
                    signatoryList.append(obj1)
            item2['signatoryList'] = signatoryList
    print('[带签署区的签署信息处理]-response:', signerInfos)
    return arr1

def getInfoDeal(params):
    """
    为了组装submit提交接口的发起数据
    :param params: 来源域getInfo接口的signerNodeList值
    :return:
    """
    if params:
        for item0 in params:
            signerList = item0.get('signerList')
            for item1 in signerList:
                if "organizeCode" in item1.keys():
                    item1['organizationCode'] = item1.get('organizeCode')
                    item1['organizationName'] = item1.get('organizeName')

    print('[带签署区的签署信息处理]-response:', params)
    return params

"""===============业务模板=============="""
def get_preset_list(headers, presetName, businessTypeId=None, status=None):
    """
    webapi-查询业务模板
    :param headers: 业务系统登录token
    :param presetName: 业务模板名称
    :param businessTypeId: 业务模板编号
    :param status: 业务状态 1-启动
    :return:
    """

    data1 = {"params":{"presetName": presetName,"status": status ,"businessTypeId": businessTypeId ,"fileFormat":"","presetType":"","page":1,"size":10}}
    res1 = requests.post(url = mainHost + '/esign-docs/businessPreset/list', json=data1 , headers=headers)
    res1 = res1.json()
    print("list-[response]:",res1)
    return res1

def create_businessPreset(headers, presetName):
    # 创建业务模板
    data2 = {"params": {"presetName": presetName, "presetType": 0}}
    res2 = requests.post(url=mainHost + '/esign-docs/businessPreset/create', json=data2, headers=headers)
    res2 = res2.json()
    print("创建业务模板-[response]:", res2)
    if res2.get('status') == 200:
        presetId = res2.get('data')
        return presetId
    return None

def detail_businessPreset(headers, presetId):
    # 获取文档模板
    headers2 = headers
    headers2['Navid'] = "1523545065937572841"
    res22 = requests.get(url=mainHost + '/esign-docs/businessPreset/detail/' + presetId, json=None,
                         headers=headers2)
    res22 = res22.json()
    print("查询业务模板详情-[response]:", res22)
    return res22

def detail_withoutPermission(headers, templateId, version):
    # 获取模板详情
    data3 = {"params": {"templateUuid": templateId, "version": version}}
    res3 = requests.post(url=mainHost + '/esign-docs/template/withoutPermission/detail', json=data3, headers=headers)
    res3 = res3.json()
    return res3

def detail_template(headers, presetId, templateId, version):
    # 获取模板详情
    data10 = {"params": {"presetId":presetId, "templateId": templateId, "version": version}}
    res10 = requests.post(url=mainHost + '/esign-docs/businessPreset/template/detail', json=data10, headers=headers)
    res10 = res10.json()
    print('[detail_template]: ',res10)
    return res10

def addDetail(headers, presetId,presetName,templateId=None,templateInfo=None,workFlowModelKey=None,fileFormat0=None,isInitiator=None,isAuthUserList=None,allowAddFile=None):
    ##业务模板：第一步-关联文档模板 (允许为空)
    if templateId == None:
        if fileFormat0 == 2:
            _fileFormat = 2
        else:
            _fileFormat = 1
        if isInitiator == None:
            _initiatorAll = 1
            initiatorList0 = []
        else:
            _initiatorAll = 0
            initiatorList0 = [{"organizationCode": org01Code,"userCode": sign01Code}] #指定发起人只能测试签署一用户
        if allowAddFile == None:
            allowAddFile = 1
        data4 = {"params": {"checkRepetition": 1, "templateList": [], "initiatorAll": _initiatorAll, "allowAddFile": allowAddFile, "supplementModelKey": None, "allowSupplement": None,
                        "workFlowModelName": None, "presetName": presetName, "supplementModelName": None,
                        "workFlowModelKey": workFlowModelKey, "presetId": presetId, "fileFormat": _fileFormat, "initiatorList": initiatorList0,
                        "presetType": 0}}
    else:
        if isInitiator == None:
            _initiatorAll = 1
            initiatorList0 = []
        else:
            _initiatorAll = 0
            initiatorList0 = [{"organizationCode": org01Code,"userCode": sign01Code}] #指定发起人只能测试签署一用户
        _authAll = 1
        authUserList0 = None
        if isAuthUserList != None:
            _initiatorAll = 0
            initiatorList0 = [{"organizationCode": org01Code}]
            _authAll = 0
            authUserList0 = [{"organizationCode": org01Code}]

        res3 = templateInfo
        templateName = res3.get('data').get('templateName')
        version = res3.get('data').get('version')
        fileKey = res3.get('data').get('fileKey')
        templateType = res3.get('data').get('templateType')
        includeSpecialContent = res3.get('data').get('includeSpecialContent')
        if allowAddFile == None:
            allowAddFile = 0
        data4 = {"params": {"checkRepetition": 1, "templateList": [
            {"fileKey": fileKey, "templateId": templateId, "templateName": templateName, "version": version,
             "templateType": templateType, "contentDomainCount": 0, "includeSpecialContent": includeSpecialContent}
        ], "initiatorAll": _initiatorAll, "allowAddFile": allowAddFile, "supplementModelKey": None, "allowSupplement": None,
                            "workFlowModelName": None, "presetName": presetName, "supplementModelName": None,
                            "workFlowModelKey": workFlowModelKey, "presetId": presetId, "fileFormat": 1, "authAll": _authAll,"authUserList": authUserList0,"initiatorList": initiatorList0,
                            "presetType": 0}}
    res4 = requests.post(url=mainHost + '/esign-docs/businessPreset/addDetail', json=data4,
                         headers=headers)
    return res4.json()

def addBusinessType(headers, presetId, businessPresetDetail, signAreaSignEnable=None, personalSignatureWay=None, forceReadingTime=None, waterMarkEnable=None):
    """
    业务模板第二步
    :param headers:
    :param presetId:
    :param businessPresetDetail: （查询详情接口的出参）
    :return:
    """
    res22 = businessPresetDetail
    signBusinessType = res22.get('data').get('signBusinessType')
    signBusinessType['attachForceReadingTime'] = 0
    signBusinessType['forceReadingTime'] = 0
    signBusinessType['presetId'] = presetId
    if signAreaSignEnable !=None: #需要设置非必签可移动
        signBusinessType['signAreaSignEnable'] = 0
        signBusinessType['signAreaMoveEnable'] = 1
    if personalSignatureWay !=None: #个人签署方式
        signBusinessType['limitTemplateSealEnable'] = 1
        signBusinessType['personalSignatureWay'] = {
            "name": "个人签署方式",
            "role": "personalSignatureWay",
            "type": "0",
            "enable": 1,
            "eventList": [{"eventName": "AI手绘",
                    "eventCode": "AISignature","eventDesc": "手绘签名内容需与个人姓名一致，该签名仅可使用一次",
                    "typeList": [{"typeName": "内部个人","typeCode": 1,
                            "flag": 0
                        },{"typeName": "个人相对方","typeCode": 2,
                            "flag": 0
                        }]},{
                    "eventName": "普通手绘",
                    "eventCode": "handwrittenSignature",
                    "eventDesc": "该签名仅可使用一次",
                    "typeList": [{"typeName": "内部个人","typeCode": 1,
                            "flag": 1
                        },{"typeName": "个人相对方","typeCode": 2,"flag": 0
                        }]},{"eventName": "模板印章",
                    "eventCode": "templateSeal",
                    "eventDesc": "个人已在系统中创建的模板印章",
                    "typeList": [{"typeName": "内部个人",
                            "typeCode": 1,
                            "flag": 1},{
                            "typeName": "个人相对方","typeCode": 2,
                            "flag": 1}]},{
                    "eventName": "手绘印章/图片印章",
                    "eventCode": "handOrImageSeal",
                    "eventDesc": "个人已在系统中创建的手绘印章、自定义图片印章",
                    "typeList": [{
                            "typeName": "内部个人","typeCode": 1,
                            "flag": 0
                        },{"typeName": "个人相对方",
                            "typeCode": 2,"flag": 1}]}]}
        ### 只选择蓝色
        signBusinessType['handColor'] = "[{\"name\":\"黑色\",\"color\":\"#000000\",\"enable\":0},{\"name\":\"蓝色\",\"color\":\"#0000ff\",\"enable\":1},{\"name\":\"红色\",\"color\":\"#ff0000\",\"enable\":0}]"
        ### 只选择蓝色
        signBusinessType['templateSealColor'] = "[{\"name\":\"黑色\",\"code\":\"3\",\"color\":\"#000000\",\"enable\":0},{\"name\":\"蓝色\",\"code\":\"2\",\"color\":\"#0000ff\",\"enable\":1},{\"name\":\"红色\",\"code\":\"1\",\"color\":\"#ff0000\",\"enable\":0},{\"name\":\"紫色\",\"code\":\"4\",\"color\":\"#800080\",\"enable\":0}]"
    if forceReadingTime!= None:
        signBusinessType['forceReadingTime']= 4
        signBusinessType['attachForceReadingTime'] = 4
        signBusinessType['readComplete'] = 1
    if waterMarkEnable != None:
        signBusinessType['watermarkEnable'] = 1
        # from tests.tools import snowFlake
        # id0 = snowFlake.Snow().get_guid()
        id0 = str(common.get_randomNo_16())
        signBusinessType['watermarkList'] = [{"id":id0,"type":0,"qrWidth":103,"position":2,"offsetX":20,"qrPermission":2,"page":3,"offsetY":20,"qrExpire":33}]
    data5 = {"params": signBusinessType}
    print('xxxxx',data5)
    res5 = requests.post(url=mainHost + '/esign-docs/businessPreset/addBusinessType', json=data5,
                         headers=headers)
    return res5.json()

def addSignersV2(headers, presetId, status, signerTypeStr0):
    """
    业务模板第三步
    :param headers:
    :param presetId:
    :param status:
    :param allowAddSigner:
    :param signerTypeStr0:
    自定义类型：
        0-2个签署方顺序签署-内部企业+内部个人-不指定；
        1-2个签署方顺序签署-相对方企业+个人-不指定；
        2-2个签署方顺序签署-内部企业+内部个人-指定；
        3-2个签署方无序签署-相对方企业+个人-不指定；
        4-2个签署方无序签署-内部个人(静默)+相对方个人-指定；
        5-2个签署方或签-内部企业+内部个人-指定；
        6-2个签署方无序签署-内部企业+内部个人-不指定；
        7-2个签署方无序签署-内部企业+内部个人-指定；
        10-1个签署方-内部企业-指定；
        20-1个签署方-相对方企业;
        30-允许添加签署方;
        40-1个签署方-内部个人（静默）-指定；
        41-1个签署方-内部个人（静默）-不指定；
    :param templateId:
    :param templateInfo:
    :return:
    """
    sign01OrgName0 = os.environ['sign01.main.orgName']
    sign01UserName0 = os.environ['sign01.userName']
    sign01UserCode0 = os.environ['sign01.userCode']
    org01OrgCode0 = org01Code
    ####2个签署方场景
    signerType0 = 2
    allowAddSigner = 0
    if signerTypeStr0 < 10:
        signMode = 0
        signerTerritory0 = 1
        signerTerritory1 = 1
        org01OrgCode = ""
        sign01UserName = ""
        sign01OrgName = ""
        sign01UserCode = ""
        autoSign0 = 0
        assignSigner0 = 0
        assignSigner1 = 0
        if signerTypeStr0 == 6:  ##0-2个签署方无序签署-内部企业+内部个人-不指定；
            signMode = 1
        if signerTypeStr0 == 1: ##1-2个签署方顺序签署-相对方企业+个人-不指定；
            signerTerritory0 = 2
            signerTerritory1 = 2
        if signerTypeStr0 == 2: #2-2个签署方顺序签署-内部企业+内部个人-指定；
            org01OrgCode = org01OrgCode0
            sign01UserName = sign01UserName0
            sign01OrgName = sign01OrgName0
            sign01UserCode = sign01UserCode0
            assignSigner0 = 1
            assignSigner1 = 1
        if signerTypeStr0 == 7: #7-2个签署方无序签署-内部企业+内部个人-指定；
            org01OrgCode = org01OrgCode0
            sign01UserName = sign01UserName0
            sign01OrgName = sign01OrgName0
            sign01UserCode = sign01UserCode0
            assignSigner0 = 1
            assignSigner1 = 1
            signMode = 1
        if signerTypeStr0 == 3: #3-2个签署方无序签署-相对方企业+个人-不指定；
            signMode = 1
            signerTerritory0 = 2
            signerTerritory1 = 2
        if signerTypeStr0 == 4: #4-2个签署方无序签署-内部个人(静默)+相对方个人-指定；
            signMode = 1
            signerTerritory0 = 1
            signerTerritory1 = 2
            assignSigner0 = 1
            autoSign0 = 1
            signerType0 = 1
        if signerTypeStr0 == 5: #5-2个签署方或签-内部企业+内部个人-指定；
            signMode = 2
            org01OrgCode = org01OrgCode0
            sign01UserName = sign01UserName0
            sign01OrgName = sign01OrgName0
            sign01UserCode = sign01UserCode0
            assignSigner0 = 1
            assignSigner1 = 1
        signerList = [{"autoSign": autoSign0, "assignSigner": assignSigner0, "organizeCode": org01OrgCode, "userName": sign01UserName, "organizeName": sign01OrgName,
                                 "departmentName": sign01OrgName, "sealTypeCode": None, "signerTerritory": signerTerritory0,
                                 "signerType": signerType0, "userCode": sign01UserCode, "sealTypeName": "",
                                 "departmentCode": org01OrgCode, "signatoryList": [] },{"autoSign": 0, "assignSigner": assignSigner1, "organizeCode": "", "userName": sign01UserName,
                                 "organizeName": "", "departmentName": "", "sealTypeCode": None, "signerTerritory": signerTerritory1,
                                 "signerType": 1, "userCode": sign01UserCode, "sealTypeName": "", "departmentCode": "",
                                 "signatoryList": []}]
    else:
        signMode = 0
        if signerTypeStr0 == 10:  # 1个签署方-内部企业-指定；(默认设置法人和企业签署)
            signerList = [{"autoSign": 0, "assignSigner": 1, "organizeCode": org01OrgCode0,
                           "userName": sign01UserName0, "organizeName": sign01OrgName0,
                           "departmentName": sign01OrgName0, "sealTypeCode": None, "signerTerritory": 1,
                           "signerType": 2, "userCode": sign01UserCode0, "sealTypeName": "",
                           "departmentCode": org01OrgCode0, "signatoryList": []}]

        if signerTypeStr0 == 20:  # 1个签署方-相对方企业；
            signerList = [{"autoSign": 0, "assignSigner": 0, "organizeCode": "",
                           "userName": "", "organizeName": "",
                           "departmentName": "", "sealTypeCode": None, "signerTerritory": 1,
                           "signerType": 2, "userCode": "", "sealTypeName": "",
                           "departmentCode": "", "signatoryList": []}]
        if signerTypeStr0 == 30: #允许添加签署方；
            signerList = []
        if signerTypeStr0 == 40: #1个签署方-内部个人（静默）-指定；
            signerList = [{"autoSign": 1, "assignSigner": 1, "organizeCode": "",
                           "userName": sign01UserName0, "organizeName": "",
                           "departmentName": "", "sealTypeCode": None, "signerTerritory": 1,
                           "signerType": 1, "userCode": sign01UserCode0, "sealTypeName": "",
                           "departmentCode": org01OrgCode0, "signatoryList": []}]
        if signerTypeStr0 == 41: #1个签署方-内部个人（静默）-不指定；
            signerList = [{"autoSign": 1, "allowAddAttachment": 1,"assignSigner": 0, "organizeCode": "",
                           "userName": "", "organizeName": "",
                           "departmentName": "", "sealTypeCode": None, "signerTerritory": 1,
                           "signerType": 1, "userCode": "", "sealTypeName": "",
                           "departmentCode": "", "signatoryList": []}]
    if signerList == []:
        allowAddSigner = 1
        signerNodeList0 = None
    else:
        allowAddSigner = 0
        signerNodeList0 =  [{"signMode": signMode, "signerList": signerList }]
    data7 = {"params": {"allowAddSigner": allowAddSigner, "allowAddSealer": 1, "presetId": presetId, "signerNodeList": signerNodeList0, "sort": 0, "status": status, "allowAddFile": 0, "initiatorAll": 1, "initiatorEdit": 0,
                        "fileFormat": 1, "presetVersion": 0}}
    res7 = requests.post(url=mainHost + '/esign-docs/businessPreset/addSigners', json=data7,
                         headers=headers)
    return res7.json()

def updateFillingUsers(headers, presetId,fillingUserNum,userTerritory):
    """
    业务模板第四步：添加其他填写方
    :param headers:
    :param presetId:
    :param fillingUserNum: 填写方数量：1，2 (枚举只有2个值)
    :param userTerritory:  1-内部填写方；2-相对方填写；3-混合（只对2个填写方有效）
    :return:
    """
    random_number = random.randint(10 ** 11, 10 ** 12 - 1)
    fillingList = []
    fillingList0 = {"name":"填写方1","userTerritory":"1","key":random_number}
    fillingList[0] = fillingList0
    if fillingUserNum ==2:
        random_number = random.randint(10 ** 11, 10 ** 12 - 1)
        fillingList1 = {"name": "填写方2", "userTerritory": "1", "key": random_number}
        fillingList[1] = fillingList1
        if userTerritory ==2:
            fillingList[0]['userTerritory']=2
            fillingList[1]['userTerritory']=2
        if userTerritory == 3:
            fillingList[1]['userTerritory'] = 2
    else:
        if userTerritory == 2:
            fillingList[0]['userTerritory'] = 2
    data8 = {"presetId": presetId, "fillingList": fillingList}
    res8 = requests.post(url=mainHost + '/esign-docs/businessPreset/updateFillingUsers', json=data8,
                         headers=headers)
    return res8.json()

def editTemplateContentDomainV2(headers, presetId, customType):
    '''
    控件参与方设置并启用业务模板
        1-发起方+签署方
        2-只有发起方
        3-只有签署方
        4-只有其他填写方
    :param headers:
    :param presetId:
    :param templateId:
    :return:
    '''
    res1 = detail_businessPreset(headers, presetId)
    editUrl1 = res1.get('data').get('editUrl')
    tplToken = getTplToken(editUrl1)
    res2 = epaasServiceConfig(tplToken)
    contentId = res2.get('contents')[0].get('id')
    entityId = res2.get('contents')[0].get('entityId')
    res3 = epaasGetContentDetail(tplToken, contentId, entityId) #获取文档模板中控件
    fields = res3.get('fields')
    print("3333")
    print(fields)
    baseFile = res3.get('baseFile')
    originFile = res3.get('originFile')
    templateName = res3.get('name')
    res4 = epaasGetRole(tplToken)  ##获取参与方列表
    templateRoleId0 = res4[0].get('id') #发起方填写

    fillParams = None
    signParams = None
    if customType == 0: #特指处理2个签署区的情况
        signParams = []
        i = 0
        for item in res3.get('fields'):
            if "SIGN" in item.get('type'):
                if len(res4) == 3:
                    if i == 0:
                        if res4[1].get('enterprise'):
                            tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes":["ORG"]}
                        else:
                            tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes":["PSN"]}
                        signParams.append(tmp)
                    if i == 1:
                        if res4[2].get('enterprise'):
                            tmp = {"label": item.get('label'), "templateRoleId": res4[2].get('id'),"sealTypes":["ORG"]}
                        else:
                            tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes":["PSN"]}
                        signParams.append(tmp)
                else:
                    if res4[1].get('enterprise'):
                        tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes":["ORG"]}
                    else:
                        tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'), "sealTypes": ["PSN"]}
                    signParams.append(tmp)
                i = i + 1
    if customType == 10: #只有签署方填写,2个签署区
        fillParams = []
        signParams = []
        for item in res3.get('fields'):
            if "SIGN" not in item.get('type'):
                tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id')}
                fillParams.append(tmp)
            else:
                tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes":["ORG"]}
                signParams.append(tmp)
    if customType == 9: #1个签署方，2个签署区，无填写
        signParams = []
        for item in res3.get('fields'):
            tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'),"sealTypes": ["PSN"]}
            signParams.append(tmp)
    if customType == 13:
        templateRoleId1 = res4[1].get('id')
        label2 = res3.get('fields')[1].get('label')
        signParams = [{"label": label2,"templateRoleId": templateRoleId1,"sealTypes": ["ORG_LEGAL"] }]
    if customType == 15:
        signParams = []
        i = 0
        x = 0
        if res3.get('fields'):
            count = len(res3.get('fields'))
            if count >= 2:
                fillParams = []
                for item in res3.get('fields'):
                    if "SIGN" in item.get('type'):
                        if i == 0:
                            if res4[1].get('enterprise'):
                                tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id'), "sealTypes": ["ORG"]}
                            else:
                                tmp = {"label": item.get('label'), "templateRoleId": res4[1].get('id')}
                            signParams.append(tmp)
                        if i == 1:
                            if res4[2].get('enterprise'):
                                tmp = {"label": item.get('label'), "templateRoleId": res4[2].get('id'), "sealTypes": ["ORG"]}
                            else:
                                tmp = {"label": item.get('label'), "templateRoleId": res4[2].get('id')}
                            signParams.append(tmp)
                        i = i+1
                    if count > 3:
                        if "SIGN" not in item.get('type'):
                            if x == 1:
                                tmp2 = {"label": item.get('label'), "templateRoleId": res4[1].get('id')}
                                fillParams.append(tmp2)
                        x = x + 1
            else:
                fields = None
    if fields:
        fields = getEpaasTemplateContentWithRoleId(fields, fillParams, signParams)
    epassDraft = {"data": [{
                        "baseFile": baseFile,
                        "originFile": originFile,
                        "fields": fields,
                        "pageFormatInfoParam": None,
                        "name": templateName,
                        "contentId": contentId,
                        "entityId": entityId
                    }]}
    print(epassDraft)
    epaasBatchSaveDraft(tplToken, epassDraft)

    ################启动业务模板###########################
    data9 = {"params": {"presetId": presetId,"status":1}}
    res9 = requests.post(url=mainHost + '/esign-docs/businessPreset/editTemplateContentDomain', json=data9,
                         headers=headers)
    return res9.json()

def create_businessPreset_All(headers, presetName, allowSetSigners=0, allowFill=0,outerFlag=None):
    # 创建业务模板
    presetId = create_businessPreset(headers, presetName)
    # 获取文档模板
    res22 = detail_businessPreset(headers, presetId)
    businessTypeId = res22.get('data').get('signBusinessType').get('businessTypeId')
    if allowFill == 0: #是否需要填写0-不需要；1-则有2个内容域填写
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
    else:
        templateId = get_a_templateId(headers, 2, 2, "pdf", 0)
    # 获取模板详情
    res3 = detail_withoutPermission(headers, templateId, 1)
    ##业务模板：第一步-关联文档模板
    res4 = addDetail(headers, presetId,presetName,templateId,res3)
    print('业务模板：第一步: ',res4)
    ##业务模板：第二步
    res5 = addBusinessType(headers, presetId, res22)
    print('业务模板：第二步: ', res5)
    status = 0
    if allowSetSigners == 0:
        # 不指定签署方
        if outerFlag == None:
            #6-2个签署方无序签署-内部企业+内部个人-不指定；
            res7 = addSignersV2(headers, presetId, status, 6)
        else:
            # 1-2个签署方顺序签署-相对方企业+个人-不指定；
            res7 = addSignersV2(headers, presetId, status, 1)
    if allowSetSigners == 1:
        #7-2个签署方无序签署-内部企业+内部个人-指定；
        res7 = addSignersV2(headers, presetId, status, 7)
    print('业务模板：第三步: ', res7)
    res8 = editTemplateContentDomainV2(headers, presetId, 15)
    print('业务模板：第四步: ', res8)
    return presetId, businessTypeId

def create_businessPreset_other(headers, presetName, customType):
    # 创建业务模板
    presetId = create_businessPreset(headers, presetName)
    # 获取文档模板
    res22 = detail_businessPreset(headers, presetId)
    businessTypeId = res22.get('data').get('signBusinessType').get('businessTypeId')
    if customType == 6: #自动化勿动6-带审批带文档模板带内部企业和个人指定或签不填写（pdf）
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        # DOC_BATCH_SIGN:默认的【电子签署】-发起电子签署流程
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "DOC_BATCH_SIGN")
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 5)
        print('业务模板：第三步: ', res7.get('message'))

        res8 = editTemplateContentDomainV2(headers, presetId, 0)
        print('业务模板：第四步: ', res8)
    if customType == 7:  # 自动化勿动7-指定发起人带审批OFD带内部企业和个人指定无序
        # DOC_BATCH_SIGN:默认的【电子签署】-发起电子签署流程
        res4 = addDetail(headers, presetId, presetName, None, None, "DOC_BATCH_SIGN",2,1)
        print('业务模板：第一步: ', res4)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 7)
        print('业务模板：第三步: ', res7.get('message'))
    if customType == 8:  # 自动化勿动8-指定发起人不带模板文件内部企业指定
        res4 = addDetail(headers, presetId, presetName, None, None, "", 1, 1)
        print('业务模板：第一步: ', res4)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 10)
        print('业务模板：第三步: ', res7.get('message'))
    if customType == 9:  # 自动化勿动9-指定发起人内部个人指定静默签
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "", 1, 1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 40)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId,9)
        print('业务模板：第四步: ', res8)
    if customType == 10:  # 自动化勿动10 - 非必签内部企业指定带只有签署方填写
        templateId = get_a_templateId(headers, 2, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "", 1, 1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, 1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 10)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId,10)
        print('业务模板：第四步: ', res8)
    if customType == 11: #自动化勿动11-带审批追加签署方简单模板
        # DOC_BATCH_SIGN:默认的【电子签署】-发起电子签署流程
        res4 = addDetail(headers, presetId, presetName, None, None, "DOC_BATCH_SIGN")
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 30)
        print('业务模板：第三步: ', res7.get('message'))
    if customType == 12: #自动化勿动12-校验个人页面签署方式只能普通手绘和蓝色印章
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, 1, 1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 5)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId, 0)
        print('业务模板：第四步: ', res8)
    if customType == 13:  # 自动化勿动13-开启强制阅读时间内部指定签署方不填写
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "", 1, 1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, 1,None,1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 10)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId, 13)
        print('业务模板：第四步: ', res8)
    if customType == 14:  # 自动化勿动14-指定发起方和管理员为组织内部不指定用户静默签有填写
        templateId = get_a_templateId(headers, 2, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "", 1, 1, 1,1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, 1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 41)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId, 0)
        print('业务模板：第四步: ', res8)
    if customType == 15:  # 自动化勿动15-允许追加文件且指定签署方
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3, "", 1, None,None,1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 0, 7)
        print('业务模板：第三步: ', res7.get('message'))
        res8 = editTemplateContentDomainV2(headers, presetId, 15)
        print('业务模板：第四步: ', res8)
    if customType == 16:  # 自动化勿动16-有模板文件允许追加文件和发起时设置签署方
        templateId = get_a_templateId(headers, 0, 2, "pdf", 0)
        res3 = detail_withoutPermission(headers, templateId, 1)
        res4 = addDetail(headers, presetId, presetName, templateId, res3 ,"", 1, None,None,1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 30)
        print('业务模板：第三步: ', res7.get('message'))
    if customType == 17:  # 自动化勿动17-OFD带个人静默签
        res4 = addDetail(headers, presetId, presetName, None, None, None, 2, None,None,1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, None, None, None, 1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 40)
        print('业务模板：第三步: ', res7.get('message'))
    if customType == 18:  # 自动化勿动18-OFD通用业务模板
        res4 = addDetail(headers, presetId, presetName, None, None, None, 2, None,None,1)
        print('业务模板：第一步: ', res4.get('message'))
        res5 = addBusinessType(headers, presetId, res22, None, None, None, 1)
        print('业务模板：第二步: ', res5.get('message'))
        res7 = addSignersV2(headers, presetId, 1, 30)
        print('业务模板：第三步: ', res7.get('message'))
    return presetId, businessTypeId

def get_a_businessPresetId(headers, presetName, allowSetSigners=0, allowFill=0,outerFlag=None):
    """
    【文档中心】根据传参获取一个符合条件的电子签署的业务模板
    param:presetName: 业务模板名称
    param:allowSetSigners: 0-不指定签署方，1-指定签署方（自动关联sign01和企业）
    param:allowFill: 0-不需要填写，1-需要填写（自动关联2个内容域-一个发起方填一个签署方填）
    """
    res0 = get_preset_list(headers, presetName, "", 0)
    if res0.get('data').get('total') >= 1 : #业务模板配置名称已存在
        presetId3 = res0.get('data').get('list')[0].get('presetId')
        presetName3 = "废弃自动化数据-"+ str(common.get_randomNo_16())
        res3 = addDetail(headers, presetId3, presetName3)
    res1 = get_preset_list(headers, presetName, "", 1)
    if res1.get('data').get('total') == 0:
        return create_businessPreset_All(headers, presetName, allowSetSigners, allowFill,outerFlag)
    else:
        presetId = res1.get('data').get('list')[0].get('presetId')
        businessTypeId = res1.get('data').get('list')[0].get('businessTypeId')
        return presetId, businessTypeId

def get_a_businessPresetId_other(headers, presetName, customType):
    """
    获取固定名称的业务模板
    :param headers:
    :param presetName:
    :return:
    """
    res0 = get_preset_list(headers, presetName, "", 0)
    if res0.get('data').get('total') >= 1 : #业务模板配置名称已存在
        presetId3 = res0.get('data').get('list')[0].get('presetId')
        presetName3 = "废弃自动化数据-"+ str(common.get_randomNo_16())
        res3 = addDetail(headers, presetId3, presetName3)
    res1 = get_preset_list(headers, presetName, "", 1)
    if res1.get('data').get('total') == 0:
        return create_businessPreset_other(headers, presetName, customType)
    else:
        presetId = res1.get('data').get('list')[0].get('presetId')
        businessTypeId = res1.get('data').get('list')[0].get('businessTypeId')
        return presetId, businessTypeId

def get_business_attr(businessTypeId, paramKey, index=0):
    """
    【文档中心】查询业务模板并返回对应的值
    param:businessTypeId: 业务模板编号
    param:paramKey：需要获取值对应的key
    param:index：获取数组对象中的第几个，默认首个
    """
    obj1 = {}
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    res1 = requests.get(url=OPENAPI_HOST + '/esign-docs/v1/bizTemplates/'+businessTypeId,
                             json=obj1, headers=headers)
    resJon1 = res1.json()
    try:
        if paramKey == 'businessTypeCode':
            return resJon1.get('data').get('businessTypeCode')
        if paramKey == 'bizTemplateName':
            return resJon1.get('data').get('bizTemplateName')
        if paramKey == 'allowInitiatorSetSigners':
            return resJon1.get('data').get('bizTemplateConfig').get('allowInitiatorSetSigners')
        if paramKey == 'allowInitiatorAddSignFiles':
            return resJon1.get('data').get('bizTemplateConfig').get('allowInitiatorAddSignFiles')
        if paramKey == 'initiatorRangeType':
            return resJon1.get('data').get('initiatorInfo').get('initiatorRangeType')
        if paramKey == 'docTemplateId':
            return resJon1.get('data').get('docTemplateInfos')[index].get('docTemplateId')
        if paramKey == 'signerId':
            return resJon1.get('data').get('signerInfos')[index].get('signerId')
    except:
        return None

def deal_collection_pageConfig(fields,fieIdNames):
    """
    【低代码采集】处理数据：创建采集模板的时候设置单行文本的模糊搜索开关
    param:fields: 当前采集模板的所有字段
    param:fieIdNames：需要进行模糊搜索的单行文本的名称 （注意制作采集模板表单的时候控件名称尽量不要相同）
    """
    newFields = []
    if fields:
        for item in fields:
            for name0 in fieIdNames:
                if item.get('fieldName') == name0 and item.get('multiFuzzySelectEnable') == True:
                    item['multiFuzzySelect'] = True
            if item.get('fieldType') == "input":
                item['isSelect'] = True #单行文本控件都勾选为查询条件
            newFields.append(item)
        return newFields

def compare_collection(keyParams, valueParams):
    """
    处理json: key是动态变更的数据
    :param keyParams:  e.g:['key_001', '_key_002']
    :param valueParams:  e.g:['测试签署一', '19112000001']
    :return:  e.g:{'key_001': '测试签署一', '_key_002': '19112000001'}
    """
    res = {}
    if type(keyParams) == list:
        index = 0
        for i in keyParams:
            res[i] = valueParams[index]
            index = index + 1
    return res

def configForm(headers, formJsonConfig=None):
    """
    配置采集模板表单
    :param formJsonConfig:  （默认配置4个控件的模板）
    :return:
    """
    if formJsonConfig == None:
        formJsonConfig = "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"测试全链路一\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"姓名\",\"key\":\"r105pwcg\",\"model\":\"input_r105pwcg\",\"alias\":\"input_r105pwcg\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"19112100001\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"手机号\",\"key\":\"y9sirtub\",\"model\":\"cellphone_y9sirtub\",\"alias\":\"cellphone_y9sirtub\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\"}}"

    obj1 = {
      "formJson": formJsonConfig
    }
    headers['esa-token'] = headers.get('authorization')
    headers['esa-token-source'] = 'PORTAL'
    response = requests.post(url=mainHost + '/etl-integrate/v1/lc/collection/form/config',
                             json=obj1, headers=headers)
    json_response = response.json()

    # print('configForm-[request]:', obj1)
    # print('configForm-[respone]:', json_response)
    return json_response

def createCollectionTemplate(headers, collectionTemplateName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    formJsonCommon = "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"测试全链路一\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"姓名\",\"key\":\"r105pwcg\",\"model\":\"input_r105pwcg\",\"alias\":\"input_r105pwcg\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"19112100001\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"手机号\",\"key\":\"y9sirtub\",\"model\":\"cellphone_y9sirtub\",\"alias\":\"cellphone_y9sirtub\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"txt2\",\"key\":\"kxpis4jg\",\"model\":\"input_kxpis4jg\",\"alias\":\"input_kxpis4jg\",\"rules\":[],\"row\":2,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"txt3-不支持模糊搜索\",\"key\":\"djdgq47p\",\"model\":\"input_djdgq47p\",\"alias\":\"input_djdgq47p\",\"rules\":[],\"row\":3,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://tianyin6-stable.tsign.cn/esign-docs/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"https://tianyin6-stable.tsign.cn/esign-docs/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\"}}"
    if collectionTemplateName == None:
        collectionTemplateName = "自动化全链路采集模板（勿动）"
    obj1 = {}
    res4 = configForm(headers)
    tmp_scene2_001 = res4.get('data').get('fields')
    tmp_scene2_002 =  ["姓名", "txt2"]
    formFields = deal_collection_pageConfig(tmp_scene2_001,tmp_scene2_002)
    formOperations = [{"key":"collection_list","code":"lc_eform_collection_list","label":"登记信息表","type":"SYSTEM"},
        {"key":"collection_add","code":"lc_eform_collection_add","label":"新建登记表","type":"SYSTEM"},
        {"key":"collection_task_add","code":"lc_eform_collection_task_add","label":"新建采集任务","type":"SYSTEM"},
        {"key":"collection_template_relation","code":"lc_eform_collection_template_relation","label":"关联业务模版","type":"CUSTOM"},
        {"key":"collection_edit","code":"lc_eform_collection_edit","label":"编辑登记表","type":"SYSTEM"},
        {"key":"collection_view","code":"lc_eform_collection_view","label":"查看登记表","type":"SYSTEM"},
        {"key":"collection_delete","code":"lc_eform_collection_delete","label":"删除登记表","type":"SYSTEM"},
        {"key":"collection_task_start_list","code":"lc_eform_collection_task_start_list","label":"我发起的","type":"SYSTEM"},
        {"key":"collection_task_manage_list","code":"lc_eform_collection_task_manage_list","label":"我管理的","type":"SYSTEM"},
        {"key":"collection_task_view","code":"lc_eform_collection_task_view","label":"任务详情","type":"SYSTEM"},
        {"key":"collection_task_enable","code":"lc_eform_collection_task_enable","label":"启用","type":"SYSTEM"},
        {"key":"collection_task_disable","code":"lc_eform_collection_task_disable","label":"停用","type":"SYSTEM"},
        {"key":"collection_task_delete","code":"lc_eform_collection_task_delete","label":"删除","type":"SYSTEM"},
        {"key":"collection_task_url","code":"lc_eform_collection_task_url","label":"复制链接","type":"SYSTEM"},
        {"key":"collection_data_start_list","code":"lc_eform_collection_data_start_list","label":"我发起的","type":"SYSTEM"},
        {"key":"collection_data_manage_list","code":"lc_eform_collection_data_manage_list","label":"我管理的","type":"SYSTEM"},
        {"key":"collection_data_sign","code":"lc_eform_collection_data_sign","label":"选择数据发起签署","type":"SYSTEM"},
        {"key":"collection_data_export","code":"lc_eform_collection_data_export","label":"导出数据","type":"SYSTEM"},
        {"key":"collection_data_view","code":"lc_eform_collection_data_view","label":"查看","type":"SYSTEM"},
        {"key":"collection_data_approve","code":"lc_eform_collection_data_approve","label":"审核","type":"SYSTEM"},
        {"key":"collection_data_delete","code":"lc_eform_collection_data_delete","label":"删除","type":"SYSTEM"},
        {"key":"collection_data_edit","code":"lc_eform_collection_data_edit","label":"编辑","type":"SYSTEM"}]

    obj1['formJson'] = formJsonCommon
    obj1['name'] = collectionTemplateName
    obj1['operations'] = formOperations
    pageConfig = {'fields': formFields}
    obj1['pageConfig'] = pageConfig
    response = requests.post(url=mainHost + '/etl-integrate/v1/lc/collection/form',
                             json=obj1, headers=headers)
    json_response = response.json()

    # print('createCollectionTemplate-[request]:', obj1)
    print('createCollectionTemplate-[respone]:', json_response)
    return json_response

def taskCreateCollectionOpenApi(collectionTemplateId, collectionTaskType,collectionTaskApprove=0,automaticInitiation=0,businessTemplateCode=None):
    """
    新建采集任务
    :param collectionTemplateId:
    :param collectionTaskType: 任务类型，包括0-公开采集，1-指定人员采集，默认选择0-公开采集
    :param collectionTaskApprove:
    :param automaticInitiation:
    :param businessTemplateCode:
    :return:
    """
    if collectionTaskType == 1:
        assignUsers = [
            {"assignUserName": ENV('sign01.userName'), "contactContent": ENV('sign01.mobile'), "contactType": 0}]
    else:
        assignUsers= None
    obj1 = {
      "collectionTemplateId": collectionTemplateId,
      "collectionTaskName": "自动化创建采集任务-"+str(common.get_randomNo_16()),
      "collectionTaskType": collectionTaskType,
      "collectionTaskCreatorUserCode": sign01Code,
      "collectionTaskCreatorCustomAccountNo": "",
      "collectionTaskCreatorOrganizationCode": org01Code,
      "collectionTaskCreatorCustomOrgNo": "",
      "collectionTaskExpireTime": "2099-12-19 20:15:16",
      "collectionTaskApprove": collectionTaskApprove,
      "automaticInitiation": automaticInitiation,
      "businessTemplateCode": businessTemplateCode,
      "assignUsers": assignUsers
    }

    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    res1 = requests.post(url=OPENAPI_HOST + '/esign-docs/v1/collection/taskCreate',
                             json=obj1, headers=headers)
    resJon1 = res1.json()
    print('taskCreateCollection-[request]:', obj1)
    print('taskCreateCollection-[respone]:', resJon1)
    return resJon1

def taskCollectionWebApi(headers,collectionTemplateId, collectionTaskType,collectionTaskApprove=False,automaticInitiation=False,businessTemplateCode=None):
    """
    新建采集任务
    :param collectionTemplateId:
    :param collectionTaskType:
    :param collectionTaskApprove:
    :param automaticInitiation:
    :param businessTemplateCode:
    :return:
    """
    if collectionTaskType == 0:
        collectionTaskType = "PUBLIC"
    if collectionTaskType == 1:
        collectionTaskType = "ASSIGN"
    if collectionTaskType == 2:
        collectionTaskType = "REMARK_SIGN"
    if collectionTaskType == 1:
        assignUsers = [
            {"assignUserName": ENV('sign01.userName'), "contactContent": ENV('sign01.mobile')}]
    else:
        assignUsers= None
    obj1 = {
        "formKey": collectionTemplateId,
        "taskType": collectionTaskType,
        "creator": sign01Code,
        "creatorName": ENV('sign01.userName'),
        "organizationCode": org01Code,
        "name": "自动化WEB创建采集任务-"+str(common.get_randomNo_16()),
        "endTime": 4885891199000,
        "audit": collectionTaskApprove,
        "start": automaticInitiation,
        "templateKey": "",
        "owner": sign01Code
    }
    if assignUsers != None:
        obj1['assignUsers'] = assignUsers
    res1 = requests.post(url=OPENAPI_HOST + '/etl-integrate/v1/lc/collection/task',
                             json=obj1, headers=headers)
    resJon1 = res1.json()
    print('taskCollectionWebApi-[request]:', obj1)
    print('taskCollectionWebApi-[respone]:', resJon1)
    return resJon1

def taskListCollection(collectionTemplateId, collectionTaskName=None, collectionTaskId=None):
    """
    查询采集任务列表
    :param collectionTemplateId:
    :param collectionTaskId:
    :return:
    """
    if collectionTaskName == None:
        collectionTaskName = "自动化创建采集任务"
    obj1 = {
      "collectionTemplateId": collectionTemplateId,
      "collectionTaskName": collectionTaskName,
      "collectionTaskStatus": 0,
      "pageNo": 1,
      "pageSize": 50,
      "collectionTaskId": collectionTaskId
    }

    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    res1 = requests.post(url=OPENAPI_HOST + '/esign-docs/v1/collection/taskList',
                             json=obj1, headers=headers)
    resJon1 = res1.json()
    print('taskListCollection-[request]:', obj1)
    print('taskListCollection-[respone]:', resJon1)
    return resJon1

def taskListCollectionWebApi(headers, taskType=None,status=None):
    """
    查询采集任务列表(默认只查询已启用的)
    :param collectionTemplateId:
    :param collectionTaskId:
    :return:
    """
    if taskType == 0:
        taskType = "PUBLIC"
    if taskType == 1:
        taskType = "ASSIGN"
    if taskType == 2:
        taskType = "REMARK_SIGN"
    if status== None:
        status = "ENABLE"
    obj1 = "status="+status+"&taskType="+taskType+"&currentPage=1&pageSize=20&type=MANAGE"

    response = requests.get(url=mainHost + '/etl-integrate/v1/lc/collection/task?'+obj1,
                             json=None, headers=headers)
    resJon1 = response.json()
    return resJon1

def getRemarkCollectionTask(headers,collectionTaskName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    obj1 = {
            "params":{
                "collectionTaskName": collectionTaskName
            }
        }
    response = requests.post(url=mainHost + '/esign-docs/batchTemplateInitiation/signature/remarkCollectionTaskList',
                             json=obj1, headers=headers)
    json_response = response.json()

    print('getRemarkCollectionTaskId-[request]:', obj1)
    print('getRemarkCollectionTaskId-[respone]:', json_response)
    if json_response.get('status') == 200 and len(json_response.get('data')) > 0:
        return json_response.get('data')[0].get('collectionTaskId'),json_response.get('data')[0].get('collectionTaskName')
    else:
        res2 = createCollectionTemplate(headers)
        collectionTemplateId =res2.get('data').get('key')
        res4 = taskCollectionWebApi(headers,collectionTemplateId, 2,False,False)
        if res4.get('code') == 0:
            collectionTaskId = res4.get('data').get('key')
            res5 = taskListCollection(collectionTemplateId, "", collectionTaskId)
            return collectionTaskId,res5.get('data').get('collectionTaskInfos')[0].get('collectionTaskName')
    return None,None

def getRemarkCollectionTaskId(headers,collectionTaskName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    res = getRemarkCollectionTask(headers,collectionTaskName)
    return res[0]

def getRemarkCollectionTaskName(headers,collectionTaskName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    res = getRemarkCollectionTask(headers, collectionTaskName)
    return res[1]


from urllib.parse import urlparse, parse_qs
def extract_parameter_value(url, parameter_name):
    """
    处理url中的参数
    :param url: 长链
    :param parameter_name: 需要获取的参数key
    :return: 对应的值
    """
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    if parameter_name in query_params:
        print(parameter_name+"=",query_params[parameter_name][0])
        return query_params[parameter_name][0]
    else:
        return None

def get_businessPreset_type(headers, type0):
    """
    创建固定的业务模板（没有特殊说明都是带了模板文件的pdf业务模板）
    :param headers:
    :param type0: 自定义类型
    :return:
    """
    if type0 == 1:
        presetName = '自动化勿动1-内部企业和个人无序不指定不填写'
        return get_a_businessPresetId(headers, presetName, 0, 0)
    if type0 == 2:
        presetName = '自动化勿动2-内部企业和个人无序指定不填写'
        return get_a_businessPresetId(headers, presetName, 1, 0)
    if type0 == 3:
        presetName = '自动化勿动3-内部企业和个人无序指定填写'
        return get_a_businessPresetId(headers, presetName, 1, 1)
    if type0 == 4:
        presetName = '自动化勿动4-外部企业和个人顺序不指定不填写'
        return get_a_businessPresetId(headers, presetName, 0, 0, 1)
    if type0 == 5:
        presetName = '自动化勿动5-外部企业和个人顺序不指定填写'
        return get_a_businessPresetId(headers, presetName, 0, 1, 1)
    if type0 == 6:
        presetName = '自动化勿动6-带审批内部企业和个人指定或签不填写'
        return get_a_businessPresetId_other(headers, presetName, 6)
    if type0 == 7:
        presetName = '自动化勿动7-指定发起人带审批OFD带内部企业和个人指定无序'
        return get_a_businessPresetId_other(headers, presetName, 7)
    if type0 == 8:
        presetName = '自动化勿动8-指定发起人不带文档模板内部企业指定'
        return get_a_businessPresetId_other(headers, presetName, 8)
    if type0 == 9:
        presetName = '自动化勿动9-指定发起人内部个人指定静默签'
        return get_a_businessPresetId_other(headers, presetName, 9)
    if type0 == 10:
        presetName = '自动化勿动10-非必签内部企业指定带填写'
        return get_a_businessPresetId_other(headers, presetName, 10)
    if type0 == 11:
        presetName = '自动化勿动11-带审批追加签署方简单模板'
        return get_a_businessPresetId_other(headers, presetName, 11)
    if type0 == 12:
        presetName = '自动化勿动12-校验个人页面签署方式只能普通手绘和蓝色印章'
        return get_a_businessPresetId_other(headers, presetName, 12)
    if type0 == 13:
        presetName = '自动化勿动13-开启强制阅读时间内部指定签署方不填写'
        return get_a_businessPresetId_other(headers, presetName, 13)
    if type0 == 14:
        presetName = '自动化勿动14-指定发起方和管理员为组织内部不指定用户静默签有填写'
        return get_a_businessPresetId_other(headers, presetName, 14)
    if type0 == 15:
        presetName = '自动化勿动15-允许追加文件且指定签署方'
        return get_a_businessPresetId_other(headers, presetName, 15)
    if type0 == 16:
        presetName = '自动化勿动16-有模板文件允许追加文件和发起时设置签署方'
        return get_a_businessPresetId_other(headers, presetName, 16)
    if type0 == 17:
        presetName = '自动化勿动17-OFD带个人静默签'
        return get_a_businessPresetId_other(headers, presetName, 17)
    if type0 == 18:
        presetName = '自动化勿动18-OFD通用业务模板'
        return get_a_businessPresetId_other(headers, presetName, 18)

def deal_data_tsp(paramsDatas, targetId, targetKey, targetValue):
    """
    数据处理：轮询数组并替换值
    :param paramsDatas: 待处理的数据
    :param targetId: 数组的第几个元素
    :param targetKey: 需要被替换的key
    :param targetValue: 需要替换值
    :return:
    """
    # 轮询数组并替换值
    for index, item in enumerate(paramsDatas):
        if index == targetId:
            paramsDatas[index][targetKey] = targetValue
            break
    print(paramsDatas)
    return paramsDatas

def getCreateByBizTemplate(businessTypeCode=None):
    """
    通过业务模板发起流程
    :param businessTypeCode: 业务模板（不支持带有审批，不支持物理的）
    :return:
    """
    if businessTypeCode == None:
        businessTypeCode = ENV('businessTypeCode')

    obj1 = {
            "businessTypeCode": businessTypeCode,
            "initiatorInfo": {
                "customAccountNo": sign01No
            },
            "flowConfigs": {
                "flowConfig": {
                    "subject": "python-测试流程-"+str(common.get_randomNo_16()),
                    "signFlowExpireTime": "",
                    "chargingType": 1,
                    "startMode": 1
                },
                "fillConfig": {
                    "autoFillAndSubmit": 0,
                    "editComponentValue": 1
                },
                "integrationConfig": {
                    "fillNotifyUrl": "www.wm-fill.com",
                    "fillRedirectUrl": "www.baidu.com",
                    "signNotifyUrl": "www.wm-sign.com",
                    "signRedirectUrl": "www.wm22-sign.com"
                }
            },
            "fillingUserInfos": [

            ]
        }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/esign-signs/v1/signFlow/createByBizTemplate',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('getCreateByBizTemplate-[request]:', obj1)
    print('getCreateByBizTemplate-[respone]:', json_response)
    return json_response.get('data').get('signFlowId')

#############epass文档模板对接#####################
def getTemplateOwnerAdd(headers,templateName,fileKey=None):
    if fileKey == None:
        fileKey = FILEKEY
    data01 = {"params": {"fileKey": fileKey,
                                              "templateName": templateName,
                                              "createUserOrg": org01Code,
                                              "docUuid": get_a_docConfig_type(headers),
                                              "allRange": 1}}
    response = requests.post(url=mainHost + '/esign-docs/template/owner/add',
                             json=data01,
                             headers=headers)
    jsonResponse = response.json()
    print('创建文档模板-[request]:',data01)
    print('创建文档模板-[response]:',jsonResponse)
    return jsonResponse

def getTemplateOwnerDetail(headers,templateUuid, version=1):
    data01 = {"params": {"templateUuid": templateUuid, "version": version}}
    response = requests.post(url=mainHost + '/esign-docs/template/owner/detail',
                             json= data01,
                             headers=headers)
    jsonResponse = response.json()
    print('getTemplateOwnerDetail-[request]:',data01)
    print('getTemplateOwnerDetail-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasServiceConfig(tplToken):
    headers = {
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": tplToken
    }
    response = requests.get(url=mainHost + '/epaas-template/v1/doc-template/content/service-config',
                             json=None,
                             headers=headers)
    jsonResponse = response.json()
    print('epaasServiceConfig-[tplToken]:',tplToken)
    print('epaasServiceConfig-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasGetResourceIdByContent(tplToken,contentId,entityId):
    headers = {
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": tplToken
    }
    url0 = '/epaas-template/v1/doc-template/content/draft?contentId=' + contentId + '&entityId=' + entityId
    response = requests.get(url=mainHost + url0,
                             json=None,
                             headers=headers)
    jsonResponse = response.json()
    print('epaasGetResourceIdByContent-[tplToken]:',tplToken)
    print('epaasGetResourceIdByContent-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasGetContentDetail(tplToken,contentId,entityId):
    headers = {
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": tplToken
    }
    url0 = f'/epaas-template/v1/doc-template/content/detail?contentId={contentId}&entityId={entityId}'
    response = requests.get(url=mainHost + url0,
                             json=None,
                             headers=headers)
    jsonResponse = response.json()
    print('epaasGetContentDetail-[tplToken]:',tplToken)
    print('epaasGetContentDetail-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasGetRole(tplToken):
    headers = {
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": tplToken
    }
    url0 = '/epaas-template/v1/doc-template/list-template-role'
    response = requests.get(url=mainHost + url0,
                             json=None,
                             headers=headers)
    jsonResponse = response.json()
    print('epaasGetRole-[tplToken]:',tplToken)
    print('epaasGetRole-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasBatchSaveDraft(tplToken,params):
    headers = {
        "Content-Type": "application/json",
        "X-Tsign-Client-AppName": "epaas-template-front",
        "X-Tsign-Client-Id": "pc",
        "X-Tsign-Open-Operator-Id": "XXXtest",
        "X-Tsign-Tenant-ID": "XXXtest",
        "X-Tsign-Tpl-Token": tplToken
    }
    response = requests.post(url=mainHost + '/epaas-template/v1/doc-template/content/batch-save-draft',
                             json= params,
                             headers=headers)
    jsonResponse = response.json()
    print('epaasBatchSaveDraft-[request]:',params)
    print('epaasBatchSaveDraft-[response]:',jsonResponse)
    return jsonResponse.get('data')

def epaasFillContent(num,type=None,labelName=None,isrequied=0):
    '''
    epaas填写控件属性设置
    :param num:
    :param type:
    :param labelName:
    :param FIELD_ID:  人名币控件关联的数字控件的ID
    :return:
    '''
    fields = []
    options = None
    subType = None
    formula = None
    fieldKey = ""
    fractionDigits = None
    defaultValue = None
    if isrequied == 0:
        required = False
    else:
        required = True
    dateFormat = None
    linkedId = None
    tickOptions = None
    height = 30
    width = 220

    if type is None:
        type = "MULTILINE_TEXT" #默认设置多行文本填写控件
    if type == "CNDECIMAL":
        type = "TEXT"
        subType = "CNDECIMAL"
    if type == "DATE":
        dateFormat = "yyyy/MM/dd"
    if type == "IMAGE":
        height = 150
    if type == "RADIO_BOX" or type == "CHECK_BOX" or type == "PULL_DOWN":
        options = [{"index":0,"label":"选项一","selected":True,"position":{"x":10,"y":60,"page":"1"},"style":{"width":124,"height":15}},{"index":1,"label":"选项二","selected":False,"position":{"x":10,"y":35,"page":"1"},"style":{"width":124,"height":15}},{"index":2,"label":"选项三","selected":False,"position":{"x":10,"y":10,"page":"1"},"style":{"width":124,"height":15}}]
    if type == "TICK_BOX" :
        tickOptions = [1,2]
        height = 15
        width = 15
    if type == "PHONE_NUM" :
        fieldKey = labelName
        defaultValue = "19120200001"
    if type == "NUM" :
        fieldKey = labelName
        defaultValue = "100"
        required = False
        fractionDigits = 0
    while num > 0:
        randIndex = str(get_randomNo_32())
        if labelName is None:
            label = f"自动化控件_{type}_{num}_{randIndex}"
            if num == 2:
                label = "自动化测试-文本0508"
            if num == 1:
                label = "自动化测试-文本0507"
        else:
            label =labelName
        styleExt = {
            "signDatePos": None,
            "units": "px",
            "imgType": None,
            "hideTHeader": None,
            "selectLayout": None,
            "borderWidth": "1",
            "borderColor": "#000",
            "groupKey": "",
            "linkedId": linkedId,
            "tickOptions": None
        }
        style = {
            "font": "1",
            "fontSize": 20,
            "textColor": "#000",
            "width": width,
            "height": height,
            "bold": False,
            "italic": False,
            "underLine": False,
            "lineThrough": False,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": styleExt
        }
        settings = {
            "defaultValue": None,
            "required": required,
            "dateFormat": dateFormat,
            "validation": {
                "type": "REGEXP",
                "pattern": ""
            },
            "selectableDataSource": [],
            "numberFormat": {
                "integerDigits": None,
                "fractionDigits": fractionDigits,
                "thousandsSeparator": ""
            },
            "editable": True,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": None,
            "content": None,
            "remarkAICheck": None,
            "dateRule": None,
            "tickOptions": tickOptions,
            "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-text",
                "assignedPosbean": None,
                "fastCheck": None,
                "addSealRule": "",
                "ext": "{}",
                "mergeId": None
            }, "sealTypes": []
        }
        if subType == "CNDECIMAL":
            RNBNumFieldId = str(get_randomNo_32())
            fieldJson = {
                "fieldId": "",
                "label": "人民币控件专用数字控件",
                "custom": False,
                "type": "NUM",
                "subType": None,
                "sort": 1,
                "formula": None,
                "style": style,
                "settings": settings,
                "options": None,
                "instructions": "",
                "contentFieldId": RNBNumFieldId,
                "bizId": RNBNumFieldId,
                "fieldKey": None,
                "fillGroupKey": "",
                "fieldValue": None,
                "defaultValue": "100",
                "position": {
                    "x": random.randint(50, 450),
                    "y": random.randint(100, 600),
                    "page": "1",
                    "scope": "default",
                    "intervalType": None
                },
                "formField": False
            }
            fields.append(fieldJson)
            formula = "toCnDecimal(toFixedNumber([FIELD_ID:" + RNBNumFieldId + "],2,4))"
            styleExt['linkedId'] = RNBNumFieldId
            style = {
                "font": "1",
                "fontSize": 20,
                "textColor": "#000",
                "width": width,
                "height": height,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": styleExt
            }
        fieldJson = {
            "fieldId": "",
            "label": label,
            "custom": False,
            "type": type,
            "subType": subType,
            "sort": 1,
            "formula": formula,
            "style": style,
            "settings": settings,
            "options": options,
            "instructions": None,
            "contentFieldId": randIndex,
            "bizId": randIndex,
            "fieldKey": fieldKey,
            "fillGroupKey": "",
            "fieldValue": None,
            "defaultValue": defaultValue,
            "position": {
                "x": random.randint(50, 480),
                "y": random.randint(100, 750),
                "page": "1",
                "scope": "default",
                "intervalType": None
            },
            "formField": None
        }
        fields.append(fieldJson)
        num = num - 1
    return fields

def epaasSignContent(num,type=None):
    fields = []
    style = None
    settings = None
    if type is None:
        type = "SIGN" #默认设置多行文本填写控件
        style = {
            "font": "1",
            "fontSize": 12,
            "textColor": "#000",
            "width": 150,
            "height": 150,
            "bold": False,
            "italic": False,
            "underLine": False,
            "lineThrough": False,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
                "signDatePos": None,
                "units": "px",
                "imgType": None,
                "hideTHeader": None,
                "selectLayout": None,
                "borderWidth": "1",
                "borderColor": "#000",
                "groupKey": "",
                "tickOptions": None
            }
        }
        settings = {
            "defaultValue": None,
            "required": True,
            "dateFormat": None,
            "validation": {
                "type": "REGEXP",
                "pattern": ""
            },
            "selectableDataSource": [],
            "numberFormat": {
                "integerDigits": None,
                "fractionDigits": 0,
                "thousandsSeparator": ""
            },
            "editable": True,
            "encryptAlgorithm": "",
            "fillLengthLimit": "",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": None,
            "content": None,
            "remarkAICheck": None,
            "dateRule": "2",
            "tickOptions": None,
            "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "assignedPosbean": None,
                "fastCheck": None,
                "addSealRule": "followSeal",
                "ext": "{}",
                "mergeId": None
            },"sealTypes": []}

    while num > 0:
        randpox = random.randint(50, 500)
        label = "自动化签署区_" + str(randpox)+"_"+str(num)
        randIndex = str(get_randomNo_32())

        fieldJson = {
            "fieldId": None,
            "label": label,
            "custom": False,
            "type": type,
            "subType": None,
            "sort": 1,
            "formula": None,
            "style": style,
            "settings": settings,
            "options": None,
            "instructions": None,
            "contentFieldId": randIndex,
            "bizId": randIndex,
            "fieldKey": "",
            "fillGroupKey": "",
            "fieldValue": None,
            "defaultValue": None,
            "position": {
                "x": randpox,
                "y": randpox,
                "page": "1",
                "scope": "default",
                "intervalType": None
            },
            "formField": None
        }
        fields.append(fieldJson)
        num = num - 1
    return fields


def epaasSignContentField(signtype,templateRoleId,sealType,defaultValue=None,addSealRule=None):
    '''signtype=1:正文签署区
       signtype=2：骑缝章签署区
       signtype=3：备注签署区
       sealType=1:个人签署区
       sealType=2:企业签署区
       sealType=3:企业经办人签署区
       sealType=4:法人签署区
    '''
    randlabel = random.randint(0, 100000)
    style = {
            "font": "1",
            "fontSize": 12,
            "textColor": "#000",
            "width": 150,
            "height": 150,
            "bold": False,
            "italic": False,
            "underLine": False,
            "lineThrough": False,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
                "signDatePos": None,
                "units": "px",
                "imgType": None,
                "hideTHeader": None,
                "selectLayout": None,
                "borderWidth": "1",
                "borderColor": "#000",
                "groupKey": "",
                "tickOptions": None
            }
        }
    settings = None
    randIndex = str(get_randomNo_32())
    scope = "default"
    addSealRule = "followSeal"
    page = "1"
    type = "SIGN"
    if signtype == 1:
        type = "SIGN" #默认设置多行文本填写控件
    if signtype == 2:
        type = "QF_SIGN"
        scope = "all"
        page = "1-14"
    if sealType == 1:
        label = "个人签署区"+str(randlabel)
        sealTypes = "PSN"
    if sealType == 2:
        label = "企业签署区"+str(randlabel)
        sealTypes = "ORG"
    if sealType == 3:
        label = "企业经办人签署区"+str(randlabel)
        sealTypes = "ORG_PSN"
    if sealType == 4:
        label = "法人签署区"+str(randlabel)
        sealTypes = "ORG_LEGAL"
    settings = {
            "defaultValue": defaultValue,
            "required": True,
            "dateFormat": None,
            "validation": {
                "type": "REGEXP",
                "pattern": ""
            },
            "selectableDataSource": [],
            "numberFormat": {
                "integerDigits": None,
                "fractionDigits": None,
                "thousandsSeparator": ""
            },
            "editable": True,
            "encryptAlgorithm": "",
            "fillLengthLimit": "",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": None,
            "content": None,
            "remarkAICheck": None,
            "dateRule": "2",
            "tickOptions": None,
            "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "assignedPosbean": None,
                "fastCheck": None,
                "addSealRule": addSealRule,
                "ext": "{}",
                "mergeId": None
            },"sealTypes": [sealTypes]}

        # settings = {
        #                 "defaultValue": defaultValue,
        #                 "required": True,
        #                 "dateFormat": None,
        #                 "validation": {
        #                     "type": "REGEXP",
        #                     "pattern": ""
        #                 },
        #                 "selectableDataSource": [
        #
        #                 ],
        #                 "numberFormat": {
        #                     "integerDigits": None,
        #                     "fractionDigits": None,
        #                     "thousandsSeparator": ""
        #                 },
        #                 "editable": True,
        #                 "encryptAlgorithm": "",
        #                 "fillLengthLimit": None,
        #                 "overflowType": "2",
        #                 "minFontSize": "8",
        #                 "remarkInputType": 2,
        #                 "content": "",
        #                 "remarkAICheck": 0,
        #                 "dateRule": None,
        #                 "tickOptions": None,
        #                 "configExt": {
        #                     "signRequirements": "1,2,3",
        #                     "cooperationerSubjectType": "",
        #                     "icon": "epaas-icon-remarks-signature",
        #                     "fastCheck": None,
        #                     "addSealRule": "",
        #                     "keyPosX": 0,
        #                     "keyPosY": 0,
        #                     "ext": "{}",
        #                     "version": None,
        #                     "mergeId": None
        #                 },
        #                 "sealTypes": [
        #                 ],
        #                 "columnMapping": None,
        #                 "autoFillRemark": False,
        #                 "positionMovable": False
        #             }
        # style = {
        #                 "font": "1",
        #                 "fontSize": 10.5,
        #                 "textColor": "#000",
        #                 "width": "189.74",
        #                 "height": "51.28",
        #                 "bold": False,
        #                 "italic": False,
        #                 "underLine": False,
        #                 "lineThrough": False,
        #                 "verticalAlignment": "TOP",
        #                 "horizontalAlignment": "LEFT",
        #                 "styleExt": {
        #                     "signDatePos": None,
        #                     "units": "px",
        #                     "imgType": None,
        #                     "hideTHeader": None,
        #                     "selectLayout": None,
        #                     "borderWidth": "1",
        #                     "borderColor": "#000",
        #                     "groupKey": "",
        #                     "tickOptions": None,
        #                     "imgCrop": None,
        #                     "paddingLeftAndRight": "0"
        #                 }
        #             }
    randpox = random.randint(50, 500)
    if signtype == 3:
        fieldJson = {
                    "fieldId": "",
                    "label": "备注签署区1"+str(randlabel),
                    "custom": False,
                    "type": "REMARK_SIGN",
                    "subType": None,
                    "sort": 1,
                    "formula": None,
                    "style": {
                        "font": "1",
                        "fontSize": 10.5,
                        "textColor": "#000",
                        "width": "189.74",
                        "height": "51.28",
                        "bold": False,
                        "italic": False,
                        "underLine": False,
                        "lineThrough": False,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": None,
                            "units": "px",
                            "imgType": None,
                            "usePageTypeGroupId": "",
                            "hideTHeader": None,
                            "selectLayout": None,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": None,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": None,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": "",
                        "required": True,
                        "dateFormat": None,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [

                        ],
                        "numberFormat": {
                            "integerDigits": None,
                            "fractionDigits": None,
                            "thousandsSeparator": ""
                        },
                        "editable": True,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": None,
                        "overflowType": "2",
                        "minFontSize": "8",
                        "remarkInputType": 2,
                        "content": "",
                        "remarkAICheck": 0,
                        "dateRule": None,
                        "tickOptions": None,
                        "configExt": {
                            "signRequirements": "",
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-remarks-signature",
                            "fastCheck": None,
                            "addSealRule": "",
                            "keyPosX": "0",
                            "keyPosY": "0",
                            "ext": "{}",
                            "version": None,
                            "mergeId": None
                        },
                        "sealTypes": [
                            "PSN"
                        ],
                        "columnMapping": None,
                        "autoFillRemark": False,
                        "positionMovable": False,
                        "signDatePosition": None
                    },
                    "options": None,
                    "instructions": "",
                    "contentFieldId": randIndex,
                    "bizId": randIndex,
                    "fieldKey": None,
                    "fillGroupKey": "",
                    "fieldValue": None,
                    "defaultValue": None,
                    "position": {
                        "x": 264.21908977794794,
                        "y": 685.4599291300518,
                        "page": "1",
                        "scope": "default",
                        "intervalType": None
                    },
                    "formField": False,
                    "templateRoleId": templateRoleId
                }
    else:
        fieldJson = {
            "fieldId": "",
            "label": label,
            "custom": False,
            "type": type,
            "subType": None,
            "sort": 1,
            "formula": None,
            "style": style,
            "settings": settings,
            "options": None,
            "instructions": None,
            "contentFieldId": randIndex,
            "bizId": randIndex,
            "fieldKey": None,
            "fillGroupKey": "",
            "fieldValue": defaultValue,
            "defaultValue": defaultValue,
            "position": {
                "x": randpox,
                "y": randpox,
                "page": page,
                "scope": scope,
                "intervalType": None
            },
            "formField": None,
            "templateRoleId": templateRoleId
        }
    return fieldJson


def newTemplateIdV2(headers,contentNum, signatoryNum,templateName=None):
    if templateName is None:
        templateName =  '自动化测试模板' + str(get_randomNo_32())
    res01 = getTemplateOwnerAdd(headers,templateName)
    if res01.get('status') != 200:
        return None
    templateUuid = res01.get('data').get('templateUuid')
    version = res01.get('data').get('version')
    res02 = getTemplateOwnerDetail(headers,templateUuid, version)
    docuuid = res02.get('docUid')
    templateName = res02.get('templateName')
    status = res02.get('status')
    editUrl = res02.get('editUrl')
    previewUrl = res02.get('previewUrl')

    tplToken = getTplToken(editUrl)

    res03 = epaasServiceConfig(tplToken)
    contentId = res03.get('contents')[0].get('id')
    entityId = res03.get('contents')[0].get('entityId')

    res04 = epaasGetResourceIdByContent(tplToken,contentId,entityId)
    baseFile = res04.get('baseFile')
    fields = res04.get('fields')
    originFile = res04.get('originFile')

    fillFields = epaasFillContent(contentNum)
    signFields = epaasSignContent(signatoryNum)
    fields = [*fillFields, *signFields]
    epassDraft = {
                "data": [
                    {
                        "baseFile": baseFile,
                        "originFile": originFile,
                        "fields": fields,
                        "pageFormatInfoParam": None,
                        "name": templateName,
                        "contentId": contentId,
                        "entityId": entityId

                    }
                ]
            }
    epaasBatchSaveDraft(tplToken, epassDraft)
    requests.post(url=mainHost + '/esign-docs/template/owner/saveOrPublish',
                  json={"params": {"templateUuid": templateUuid, "version": version, "isPublish": "true"}},
                  headers=headers)
    return templateUuid

def getTemplateContent(headers,templateUuid, version=1):
    res02 = getTemplateOwnerDetail(headers,templateUuid, version)
    docuuid = res02.get('docUid')
    templateName = res02.get('templateName')
    status = res02.get('status')
    editUrl = res02.get('editUrl')
    previewUrl = res02.get('previewUrl')

    from urllib.parse import parse_qs, urlparse
    parsed_url = urlparse(editUrl)
    query_params = parse_qs(parsed_url.query)  # 返回字典（值可能是列表）
    tplToken = query_params.get("tplToken", [None])[0]

    res03 = epaasServiceConfig(tplToken)
    contentId = res03.get('contents')[0].get('id')
    entityId = res03.get('contents')[0].get('entityId')

    res04 = epaasGetResourceIdByContent(tplToken,contentId,entityId)
    baseFile = res04.get('baseFile')
    fields = res04.get('fields')
    originFile = res04.get('originFile')

    epassInfo = {
                "docuuid": docuuid,
                "status": status,
                "previewUrl": previewUrl,
                "baseFile": baseFile,
                "originFile": originFile,
                "fields": fields,
                "name": templateName,
                "contentId": contentId,
                "entityId": entityId
            }
    return epassInfo

def getEpaasTemplateContent(fillCotentNum,signContentNum,type):
    '''
    组装epaas模板添加的控件数据
    :param fillCotentNum: 填写控件个数
    :param signContentNum: 签署控件个数
    :param type: 自定义类型 0-全部PDF的填写控件,1-任意多的PDF多行文本控件
    :return:


CHECK_BOX
CNDECIMAL
DATE
EMAIL
ID_CARD
IMAGE
MULTILINE_TEXT
NUM
PHONE_NUM
PULL_DOWN
RADIO_BOX
TEXT
TICK_BOX
UNIFY_THE_SOCIAL_CREDIT_CODE
    '''
    flag0 = str(common.get_randomNo_16())
    if type == 0:
        fill_text = epaasFillContent(1,"TEXT","单行文本_"+flag0)
        fill_multiline_text = epaasFillContent(1,"MULTILINE_TEXT","多行文本_"+flag0)
        fill_num = epaasFillContent(1,"NUM","数字_"+flag0)
        fill_date = epaasFillContent(1,"DATE","日期_"+flag0)
        fill_phone = epaasFillContent(1,"PHONE_NUM","手机号_"+flag0)
        fill_ID_card = epaasFillContent(1,"ID_CARD","身份证号_"+flag0)
        fill_tick_box = epaasFillContent(1,"TICK_BOX","勾选_"+flag0)
        fill_radio_box = epaasFillContent(1,"RADIO_BOX","单选_"+flag0)
        fill_check_box = epaasFillContent(1,"CHECK_BOX","多选_"+flag0)
        fill_pull_down = epaasFillContent(1,"PULL_DOWN","下拉选择_"+flag0)
        fill_image = epaasFillContent(1,"IMAGE","图片_"+flag0)
        fill_credit_code = epaasFillContent(1,"UNIFY_THE_SOCIAL_CREDIT_CODE","统一社会信用代码_"+flag0)
        fill_email = epaasFillContent(1,"EMAIL","邮箱_"+flag0)
        fill_cndecimal = epaasFillContent(1,"CNDECIMAL","人民币_"+flag0)
        signFields = epaasSignContent(signContentNum)
        fields = [*signFields,*fill_text,*fill_multiline_text,*fill_num,*fill_date,*fill_phone,*fill_ID_card,*fill_pull_down,*fill_tick_box,*fill_radio_box,*fill_check_box,*fill_image,*fill_credit_code,*fill_email,*fill_cndecimal]

    if  type == 1:
        fill_multiline_text = epaasFillContent(fillCotentNum)
        signFields = epaasSignContent(signContentNum)
        fields = [*signFields,*fill_multiline_text]
    return fields

def update_template_role_id(data, label, new_template_role_id):
    """
    Update the templateRoleId for the dictionary with the specified label.

    :param data: List of dictionaries containing field information.
    :param label: The label of the field to update.
    :param new_template_role_id: The new templateRoleId to set.
    :return: None (the data list is modified in place)
    """
    for item in data:
        if item.get('label') == label:
            item['templateRoleId'] = new_template_role_id
            break

def getEpaasTemplateContentWithRoleId(fields,fillParams=None,signParams=None):
    '''
    通过控件名称指定参与方
    :param fields: 控件列表
    :param fillParams: 填写控件关联 [ {"label": "XXX","templateRoleId": "xxx" }]
    :param signParams: 签署控件关联
    :return:
    '''

    if fillParams:
        update_map = {update['label']: update['templateRoleId'] for update in fillParams}
        for item in fields:
            if item['label'] in update_map and "SIGN" not in item['type']:
                item['templateRoleId'] = update_map[item['label']]
    if signParams:
        for item1 in signParams:
            for item in fields:
                if item['label'] == item1['label'] and "SIGN" in item['type']:
                    item['templateRoleId'] = item1['templateRoleId']
                    if "sealTypes" in item1:
                        item['settings']['sealTypes'] = item1['sealTypes']
    return fields


def replace_template_role_id(json_data, new_value):
    """
    使用迭代方式替换JSON数据中所有templateRoleId的值
    适用于处理任意深度的嵌套结构，避免递归错误
    """
    # 处理非字典/列表类型数据（直接返回）
    if not isinstance(json_data, (dict, list)):
        return json_data

    # 使用栈进行深度优先遍历
    stack = [json_data]

    while stack:
        current = stack.pop()

        # 处理字典类型数据
        if isinstance(current, dict):
            for key, value in list(current.items()):
                # 找到templateRoleId字段并替换
                if key == "templateRoleId":
                    current[key] = new_value
                # 将值加入栈继续处理嵌套结构
                stack.append(value)

        # 处理列表类型数据
        elif isinstance(current, list):
            for item in current:
                stack.append(item)

    return json_data


def getEpaasTemplateContentWithRoleIdByBusinessTypeCode(businessTypeCode,fillType=None,signType=None):
    '''
    业务模板第四步，设置控件的填写方和参与方
    :param businessTypeCode:
    :param fillType:  默认发起方填写
    :param signType:
    :return:
    '''
    roles = ""
    fields = ""
    #
    # if fillParams:
    #     update_map = {update['label']: update['templateRoleId'] for update in fillParams}
    #     for item in fields:
    #         if item['label'] in update_map and "SIGN" not in item['type']:
    #             item['templateRoleId'] = update_map[item['label']]
    # if signParams:
    #     for item1 in signParams:
    #         for item in fields:
    #             if item['label'] == item1['label'] and "SIGN" in item['type']:
    #                 item['templateRoleId'] = item1['templateRoleId']
    #                 if "sealTypes" in item1:
    #                     item['settings']['sealTypes'] = item1['sealTypes']
    # return fields


def generate_field_object(label, field_type,fieldId=None):
    """
    根据label和type生成fields对象
    :param label: 字段标签
    :param field_type: 字段类型 (SIGN, QF_SIGN, REMARK_SIGN, TEXT, MULTILINE_TEXT, NUM, DATE, PHONE_NUM, ID_CARD, TICK_BOX, PULL_DOWN, IMAGE, UNIFY_THE_SOCIAL_CREDIT_CODE等)
    :return: 生成的field对象
    """
    import uuid

    # 生成随机ID
    content_field_id = str(uuid.uuid4()).replace('-', '')
    if fieldId is None:
        fieldId = ""
    # 基础字段结构
    base_field = {
        "fieldId": fieldId,
        "label": label,
        "custom": False,
        "type": field_type,
        "subType": None,
        "sort": 1,
        "formula": None,
        "instructions": "",
        "contentFieldId": content_field_id,
        "bizId": content_field_id,
        "fieldKey": None,
        "fillGroupKey": "",
        "fieldValue": None,
        "defaultValue": None,
        "formField": False
    }

    # 根据不同类型设置特定的style、settings、options和position
    if field_type == "SIGN":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 12,
                "textColor": "#000",
                "width": 119,
                "height": 119,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": True,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": "2",
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-stamp",
                    "fastCheck": "true",
                    "addSealRule": "followSeal",
                    "keyPosX": "0",
                    "keyPosY": "0",
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": False,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 150, "y": 714, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "QF_SIGN":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 12,
                "textColor": "#000",
                "width": 120,
                "height": 120,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": True,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-paging-seal",
                    "fastCheck": "true",
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": False,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 385, "y": 690, "page": "1-30", "scope": "all", "intervalType": None}
        })

    elif field_type == "REMARK_SIGN":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": "277.10",
                "height": "118.18",
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": "",
                "required": True,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": 2,
                "content": "",
                "remarkAICheck": 0,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-remarks-signature",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": "0",
                    "keyPosY": "0",
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "autoFillRemark": False,
                "positionMovable": False,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 385, "y": 595, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "TEXT":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 160,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "20",
                "overflowType": "1",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-text",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 48, "y": 439, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "MULTILINE_TEXT":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 226.58,
                "height": 116.12,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "leadingRate": 1,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "224",
                "overflowType": "1",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-multiline",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 329, "y": "369.17", "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "NUM":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 160,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": "0",
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": 0, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "7",
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-number",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 31, "y": 289, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "DATE":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 294,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": "yyyy-MM-dd",
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-date",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 261, "y": 288, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "PHONE_NUM":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 315,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "11",
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-telephone",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 11, "y": 163, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "ID_CARD":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 436,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "18",
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-id-card",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 141, "y": 98, "page": "1", "scope": "default", "intervalType": None}
        })

    elif field_type == "TICK_BOX":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 15,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "hideBorder": False,
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": "[1]",
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": "0",
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": [1],
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-check-square",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "defaultValue": 0,
            "position": {"x": 120, "y": 502, "page": "2", "scope": "default", "intervalType": None}
        })

    elif field_type == "PULL_DOWN":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 160,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": None,
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": None,
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": "3",
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-select",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": [
                {"index": 0, "label": "选项一", "selected": False, "position": {"x": 0, "y": 50, "page": "2"},
                 "style": {"width": 124, "height": 14}},
                {"index": 1, "label": "选项二", "selected": False, "position": {"x": 0, "y": 25, "page": "2"},
                 "style": {"width": 124, "height": 14}},
                {"index": 2, "label": "选项三", "selected": False, "position": {"x": 0, "y": 0, "page": "2"},
                 "style": {"width": 124, "height": 14}}
            ],
            "position": {"x": 277, "y": 421, "page": "2", "scope": "default", "intervalType": None}
        })

    elif field_type == "IMAGE":
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 12,
                "textColor": "#000",
                "width": 245,
                "height": 154,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "imageFillType": "FILL",
                "styleExt": {
                    "signDatePos": None,
                    "units": "px",
                    "imgType": "1",
                    "usePageTypeGroupId": "",
                    "hideTHeader": None,
                    "selectLayout": None,
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "keyword": "",
                    "groupKey": "",
                    "tickOptions": None,
                    "elementId": "",
                    "posKey": "",
                    "imgCrop": "true",
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "dateFormat": None,
                "validation": {"type": "REGEXP", "pattern": ""},
                "selectableDataSource": [],
                "numberFormat": {"integerDigits": None, "fractionDigits": None, "thousandsSeparator": ""},
                "editable": True,
                "encryptAlgorithm": "",
                "fillLengthLimit": None,
                "overflowType": "2",
                "minFontSize": "8",
                "remarkInputType": None,
                "content": None,
                "remarkAICheck": None,
                "dateRule": None,
                "tickOptions": None,
                "configExt": {
                    "cooperationerSubjectType": "",
                    "icon": "epaas-icon-image",
                    "fastCheck": None,
                    "addSealRule": "",
                    "keyPosX": None,
                    "keyPosY": None,
                    "ext": "{}",
                    "version": None,
                    "mergeId": None
                },
                "sealTypes": [],
                "columnMapping": None,
                "positionMovable": None,
                "cellEditableOnFilling": True,
                "signDatePosition": None
            },
            "options": None,
            "position": {"x": 32, "y": 335, "page": "2", "scope": "default", "intervalType": None}
        })

    # 可以继续添加其他类型的字段配置...
    else:
        # 默认配置，适用于未明确定义的类型
        base_field.update({
            "style": {
                "font": "1",
                "fontSize": 42,
                "textColor": "#000",
                "width": 160,
                "height": 49,
                "bold": False,
                "italic": False,
                "underLine": False,
                "lineThrough": False,
                "verticalAlignment": "TOP",
                "horizontalAlignment": "LEFT",
                "styleExt": {
                    "units": "px",
                    "borderWidth": "1",
                    "borderColor": "#000",
                    "paddingLeftAndRight": "0"
                }
            },
            "settings": {
                "defaultValue": None,
                "required": False,
                "validation": {"type": "REGEXP", "pattern": ""},
                "editable": True,
                "overflowType": "2",
                "minFontSize": "8",
                "configExt": {"ext": "{}"}
            },
            "options": None,
            "position": {"x": 100, "y": 100, "page": "1", "scope": "default", "intervalType": None}
        })

    return base_field


def extract_table_cell(data: str, row: int, column: int) -> str:
    """
    从JSON数据中提取指定行列的单元格内容

    Args:
        data (str): JSON格式的数据字符串
        row (int): 行号，从1开始
        column (int): 列号，从1开始

    Returns:
        str: 指定位置的单元格内容，如果不存在则返回空字符串
    """
    try:
        # 解析JSON数据
        print_table(data)
        parsed_data = json.loads(data)

        # 检查行号是否有效
        if row < 1 or row > len(parsed_data):
            return ""

        # 获取指定行的数据
        row_data = parsed_data[row - 1]["row"]

        # 构建列名（column1, column2, ...）
        column_key = f"column{column}"
        # 返回指定列的值，如果不存在则返回空字符串
        return str(row_data.get(column_key, ""))

    except (json.JSONDecodeError, KeyError, IndexError, TypeError) as e:
        print(f"解析数据时出错: {e}")
        return ""


def print_table(data: str) -> None:
    """
    打印表格数据，用于调试和查看数据结构

    Args:
        data (str): JSON格式的数据字符串
    """
    try:
        parsed_data = json.loads(data)

        # 获取所有可能的列
        all_columns = set()
        for item in parsed_data:
            all_columns.update(item["row"].keys())

        # 按列号排序
        sorted_columns = sorted(all_columns, key=lambda x: int(x.replace("column", "")))

        # 打印表头
        print("行号\t" + "\t".join(sorted_columns))
        print("-" * 50)

        # 打印每一行数据
        for i, item in enumerate(parsed_data, 1):
            row_values = [str(item["row"].get(col, "")) for col in sorted_columns]
            print(f"{i}\t" + "\t".join(row_values))

    except (json.JSONDecodeError, KeyError) as e:
        print(f"解析数据时出错: {e}")

def remove_key_from_dict_list(dict_list, key_to_remove):
    """
    从字典列表中移除指定的键值对。

    :param dict_list: 包含字典对象的列表。
    :param key_to_remove: 需要移除的键。
    :return: 处理后的字典列表。
    """
    for item in dict_list:
        if key_to_remove in item:
            del item[key_to_remove]
    return dict_list

def get_value_by_fieldId_fieldValue(data, target_key, target_value,  target_key2):
    for item in data:
        if item.get(target_key) == target_value:
            return item.get(target_key2)
    return None

if __name__ == "__main__":
    x = epaasSignContentField(3,"222222222222",1,"383838833")
    # print('esign6Docs')
    # rand01 = random.randint(1, 500)
    # sign01Code = "sign01"
    # org01Code = "58bec88e506142b0bf05342c68518827"
    # sign01OrgName0 = "esigntest自动化签署中心CI测试"
    # sign01UserName0 = "测试签署一"
    # mainHost = "http://lp-epaaswjmbdj.projectk8s.tsign.cn"
    # OPENAPI_HOST = "http://lp-epaaswjmbdj.projectk8s.tsign.cn"
    # PROJECT_ID = 1000000
    # PROJECT_SECRET = "EIQd3tGxlKjw1iMb"
    # FILEKEY = "$aef75729-8aa9-4db3-9059-9b73772989ab$3519866241"
    # token0 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcmdhbml6YXRpb25JZCI6IjE1MjYxNDI5ODE2OTA4MTg1NjIiLCJzdWIiOiJlc2lnbl9sb2dpbiIsIm9yZ2FuaXphdGlvbk5hbWUiOiJlc2lnbnRlc3Toh6rliqjljJbnrb7nvbLkuK3lv4NDSea1i-ivlSIsIm9yZ2FuaXphdGlvbkNvZGUiOiI1OGJlYzg4ZTUwNjE0MmIwYmYwNTM0MmM2ODUxODgyNyIsInVzZXJUZXJyaXRvcnkiOiIxIiwidGVuYW50Q29kZSI6IjEwMDAiLCJ1c2VyTmFtZSI6Iua1i-ivleetvue9suS4gCIsInVzZXJSb2xlIjoiMCIsInVzZXJJZCI6IjE1MjYxNDM5NzA5ODAzMzE1MjEiLCJpYXQiOjE3NDU4MjMxMjMsInVzZXJDb2RlIjoic2lnbjAxIiwicGxhdGZvcm0iOiJwYyJ9.2GwkjCThAgkT-_vDCfbMZosa0vMq6VzE4-zGtpGYOok"
    # headers = {"Content-Type": "application/json","X-timevale-project-id": "1000000",
    #            "authorization": token0, "navId": "1523545065937572842"}
    # newTemplateIdV2(headers,20, 20, "WM-TEST-0429-40个控件-"+str(rand01))
    print(x)