import random
import requests
from utils import ENV, log

MAIN_HOST = ENV('esign.projectHost')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')
OPENAPI_HOST = ENV('esign.gatewayHost')
USER01 = ENV('ceswdzxzdhyhwgd1.userCode')

def theoryMessageInfo(token, signFlowId):
    """
    获取签署日志的理论值
    :param token: 业务系统的token
    :param signFlowId:
    :return:
    """
    headers = {"Content-Type": "application/json", "authorization": token,
               "X-timevale-project-id": PROJECT_ID}
    response = requests.get(url=MAIN_HOST + '/esign-signs/messageMock/theoryMessageInfo/'+signFlowId,
                             json=None, headers=headers)
    json_response = response.json()
    print("[获取消息mock的理论值-request]: ",signFlowId)
    print("[获取消息mock的理论值-response]: ",json_response)
    if json_response.get('status') == 200:
        return json_response
    return None

def getMessageMockActualDetail(token, signFlowId):
    """
    获取消息mock的实际值
    :param token: 管理平台的token
    :param signFlowId:
    :return:
    """
    headers = {"Content-Type": "application/json", "token": token,
               "X-timevale-project-id": PROJECT_ID}
    response = requests.get(url=MAIN_HOST + '/manage/message/messageMock/getMessageMockActualDetail/'+signFlowId,
                             json=None, headers=headers)
    json_response = response.json()
    print("[获取消息mock的实际值-request]: ",signFlowId)
    print("[获取消息mock的实际值-response]: ",json_response)
    if json_response.get('status') == 200:
        return json_response
    return None

def compareMessageCount(signFlowId,token, token2):
    """
    比较消息在业务上的理论发送消息数量和实际在管理平台发送数量比较，比对成功是true,比对失败false
    :param signFlowId:
    :param token: 业务平台token
    :param token2: 管理平台token
    :return:
    """
    res = {}
    res1 = theoryMessageInfo(token, signFlowId)
    res2 = getMessageMockActualDetail(token2, signFlowId)
    if res1 and res1.get('status')==200 and res1.get('data')!=None:
        data1 = res1.get('data').get('total')
        res['msgTheoryInfo'] = res1.get('data')
    else:
        data1 = -1
    if res1 and res1.get('status')==200 and res1.get('data') ==None:
        data1 = 0
    if res2 and res2.get('status')==200  and res2.get('data')!=None:
        data2 = res2.get('data').get('total')
        res['msgRealInfo'] = res2.get('data')
    else:
        data2 = -1
    print("[compareMessageCount] 理论值：", data1, "实际值：", data2)

    res['msgTheory'] = data1
    res['msgReal'] = data2
    if data1 == data2:
        res['msg'] = True
    else:
        res['msg'] = False
    return res

def compareMessageChannel(signFlowId,token, token2):
    """
    比较消息在业务上的理论发送消息数量和实际在管理平台发送的每个消息通道的数量
    :param signFlowId:
    :param token: 业务平台token
    :param token2: 管理平台token
    :return:
    """
    res1 = theoryMessageInfo(token, signFlowId)
    res2 = getMessageMockActualDetail(token2, signFlowId)
    if res1:
        data1 = res1.get('data').get('details')
        for item0  in  data1:
            item = item0['channel']
            if 'weChat' in item:  # Check if the key exists
                del item['weChat']
            ###特殊处理(由于目前程序对于邮箱处理有bug,已经提交bug单了，所以邮箱暂时统一理论值变成0，修复bug之后，去掉下方代码)
            # item['email'] = 0
            item['other'] = 0
    if res2:
        data2 = res2.get('data').get('details')
    return compareMessageChannelDetail(data1, data2)

def compareMessageChannelDetail(detail1, detail2, isDeal=None):
    """
    比对2个数组里面的对象一致性
    :param detail1: 消息理论值
    :param detail2: 消息实际值
    :return:
    """
    if isDeal != None: ##是否需要特殊处理/目前消息是需要特殊处理的
        for item0  in  detail1:
            item = item0['channel']
            if 'weChat' in item:  # Check if the key exists
                del item['weChat']
            ###特殊处理(由于目前程序对于邮箱处理有bug,已经提交bug单了，所以邮箱暂时统一理论值变成0，修复bug之后，去掉下方代码)
            # item['email'] = 0
            item['other'] = 0
    res = {}
    res['msgTheory'] = detail1
    res['msgReal'] = detail2
    res['msg'] = True
    if len(detail1) != len(detail2):
        res['msg'] = False
        return res

    for dict1, dict2 in zip(detail1, detail2):
        if dict1['templateGroupName'] != dict2['templateGroupName'] or dict1['templateGroupId'] != dict2['templateGroupId']:
            res['msg'] = False
            return res

        if dict1['channel'] != dict2['channel']:
            res['msg'] = False
            return res

    return res

def listSignFlow(pageNo=None,date0=None,subject=None,signFlowId=None,signFlowStatus=None):
    """
    获取签署流程列表
    :param pageNo:
    :param date0:
    :return:
    """
    if date0 == None:
        import datetime
        today = datetime.datetime.now()  # 获得今天的日期
        datetimeStr = today + datetime.timedelta(-1)
        date0 = datetime.datetime.strftime(datetimeStr, '%Y-%m-%d')
    createStartTime0 = date0 + " 00:00:00";
    createEndTime0 = date0 + " 23:59:59";
    if pageNo == None:
        pageNo = 1
    obj1 = {
        "subject": subject,
        "signFlowId": signFlowId,
        "signFlowStatus": signFlowStatus,
        "pageNo": pageNo,
        "pageNo": pageNo,
        "pageSize": 100,
        "createStartTime": createStartTime0,
        "createEndTime": createEndTime0
    }
    from utils.esignToken import openApiSignature
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/esign-signs/v1/signFlow/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    if json_response.get('code') == 200:
        print('listSignFlow-[request]:', obj1)
        print('listSignFlow-[respone]:', json_response)
        return json_response
    else:
        return None

def getSignFlowIds(date0=None):
    """
    获取某一天的流程ID集合
    :param date: 格式%Y-%m-%d
    :return:
    """
    res1 = listSignFlow(1,date0)
    signFlowIds = []
    if res1.get('code') == 200:
        total = res1.get('data').get('total')
        listSize = len(res1.get('data').get('signFlowInfos'))
        for i in range(listSize):
            signFlowId0 = res1.get('data').get('signFlowInfos')[i].get('signFlowId')
            signFlowIds.append(signFlowId0)
        if total > 100:
            count = (total // 100 + 1)
            if count >1:
                for x in range(2,count+1):
                    res2 = listSignFlow(x,date0)
                    if res2.get('code') == 200:
                        listSize2 = len(res2.get('data').get('signFlowInfos'))
                        for j in range(listSize2):
                            signFlowId0 = res2.get('data').get('signFlowInfos')[j].get('signFlowId')
                            signFlowIds.append(signFlowId0)
    print('getSignFlowIds-[respone]:', len(signFlowIds))
    return signFlowIds

def getSignFlowInfo(date0=None):
    """
    查询某一天的流程信息集合
    :param date: 格式%Y-%m-%d
    :return:
    """
    res1 = listSignFlow(1,date0)
    signFlowInfos = []
    if res1.get('code') == 200:
        total = res1.get('data').get('total')
        listSize = len(res1.get('data').get('signFlowInfos'))
        for i in range(listSize):
            signFlowInfo0= {}
            signFlowInfo0['signFlowId'] = res1.get('data').get('signFlowInfos')[i].get('signFlowId')
            signFlowInfo0['subject'] = res1.get('data').get('signFlowInfos')[i].get('subject')
            signFlowInfo0['status'] = res1.get('data').get('signFlowInfos')[i].get('signFlowStatus')
            signFlowInfos.append(signFlowInfo0)
        if total > 100:
            count = (total // 100 + 1)
            if count >1:
                for x in range(2,count+1):
                    res2 = listSignFlow(x,date0)
                    if res2.get('code') == 200:
                        listSize2 = len(res2.get('data').get('signFlowInfos'))
                        for j in range(listSize2):
                            signFlowInfo0 = {}
                            signFlowInfo0['signFlowId'] = res2.get('data').get('signFlowInfos')[j].get('signFlowId')
                            signFlowInfo0['subject'] = res2.get('data').get('signFlowInfos')[j].get('subject')
                            signFlowInfo0['status'] = res2.get('data').get('signFlowInfos')[j].get('signFlowStatus')
                            signFlowInfos.append(signFlowInfo0)
    print('getSignFlowInfo-[respone]:', len(signFlowInfos))
    return signFlowInfos

def checkSignFlowIdByDay(token,token2,date0=None):
    """
    查询T-1的流程列表，比对消息 (可以指定日期)
    :param date: 格式%Y-%m-%d
    :return:
    """
    if date0 == None:
        import datetime
        today = datetime.datetime.now()  # 获得今天的日期
        datetimeStr = today + datetime.timedelta(-1)
        date0 = datetime.datetime.strftime(datetimeStr, '%Y-%m-%d')
    res1 = getSignFlowInfo(date0)
    if len(res1) > 0:
        rows = []
        for i in range(len(res1)):
            signFlowId0 = res1[i].get('signFlowId')
            c1 = compareMessageCount(signFlowId0, token, token2)
            print("消息数量比对结果:",c1)
            row = [signFlowId0,res1[i].get('subject'),res1[i].get('status'),c1.get('msgTheory'),c1.get('msgReal'),c1.get('msg')]
            rows.append(row)

        import csv
        import os
        header  = ['signFlowId', 'subject', 'signFlowStatus', 'messageTheory', 'messageReal','compareResult']
        # Specify the name of the CSV file
        import random
        random_number = random.randint(100, 999)
        csv_file_name = date0 +'-'+ str(random_number) + '-message-output.csv'
        csv_file_path = os.path.join(os.getcwd(), csv_file_name)
        if not os.path.isfile(csv_file_path):
            with open(csv_file_path, mode='w', encoding='gbk', errors='replace',newline='') as file:
                writer = csv.writer(file)
                writer.writerow(header)

        with open(csv_file_name, mode='a', encoding='gbk', errors='replace', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(rows)

        print(f"消息报告比对请查看文件： {csv_file_path}")
        return csv_file_path
    else:
        print(date0 ,' 没有发起成功的流程')
        return None

def checkSignFlowIdByDayWithDetail(token,token2,date0=None):
    """
    查询T-1的流程列表，比对消息 (可以指定日期)
    :param date: 格式%Y-%m-%d
    :return:
    """
    if date0 == None:
        import datetime
        today = datetime.datetime.now()  # 获得今天的日期
        datetimeStr = today + datetime.timedelta(-1)
        date0 = datetime.datetime.strftime(datetimeStr, '%Y-%m-%d')
    res1 = getSignFlowInfo(date0)
    if len(res1) > 0:
        rows = []
        for i in range(len(res1)):
            signFlowId0 = res1[i].get('signFlowId')
            c1 = compareMessageCount(signFlowId0, token, token2)
            print("消息数量比对结果:",c1)
            if c1.get('msg') == False and c1.get('msgTheory')>0:
                detail1 = c1.get('msgTheoryInfo').get('details')
                detail2 = c1.get('msgRealInfo').get('details')
                res2 = compareMessageChannelDetail(detail1, detail2, 1)
                c1['msg'] = res2['msg']
                print ('xxxx',signFlowId0)
            row = [signFlowId0,res1[i].get('subject'),res1[i].get('status'),c1.get('msgTheory'),c1.get('msgReal'),c1.get('msg')]
            rows.append(row)

    if len(rows) >0:
        headerFile = ['signFlowId', 'subject', 'signFlowStatus', 'messageTheory', 'messageReal', 'compareResult']
        fileName = date0 + '-message-output.csv'
        f0 = writerCsvFile(fileName, headerFile, rows)
        return f0
    else:
        print(date0, ' 没有发起成功的流程')
        return '没有发起成功的流程'

def writerCsvFile(fileName,header,rows):
    """
    内容写进csv文件
    :param fileName: 文件名称 （会随机拼接一个3位随机数）
    :param header:  文件的表头
    :param rows: 文件的数据
    :return:  文件路径
    """
    import csv
    import os
    import random
    random_number = random.randint(100, 999)
    csv_file_name = str(random_number) + '-' + fileName
    csv_file_path = os.path.join(os.getcwd(), csv_file_name)
    if not os.path.isfile(csv_file_path):
        with open(csv_file_path, mode='w', encoding='gbk', errors='replace', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(header)

    with open(csv_file_name, mode='a', encoding='gbk', errors='replace', newline='') as file:
        writer = csv.writer(file)
        writer.writerows(rows)

    print(f"消息报告比对请查看文件： {csv_file_path}")
    return csv_file_path



if __name__ == "__main__":
    ##
    MAIN_HOST = "http://tianyin6-stable.tsign.cn"
    OPENAPI_HOST = "http://tianyin6-stable.tsign.cn"
    PROJECT_ID = "1000000"
    signFlowId = "74545a36d239ce7c7c7dac5ffdfceca8"
    signFlowId1 = "c74e1a2abc2b31fef091f91e546fd703"
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlc2lnbl9sb2dpbiIsIm9yZ2FuaXphdGlvbk5hbWUiOiJlc2lnbnRlc3TlpKnljbBQT-a1i-ivleS8geS4miIsInRlbmFudENvZGUiOiIxMDAwIiwidXNlck5hbWUiOiLpg5HmlY_kuL0iLCJ1c2VySWQiOiIxNzQzMDk0ODM5MjM3OTQzMjk3IiwidXNlckNvZGUiOiJ6aGVuZ21sIiwicGxhdGZvcm0iOiJwYyIsImV4cGlyZWRUaW1lIjoxNzI5MjIwMDkyLCJvcmdhbml6YXRpb25JZCI6IjE3NDMwOTQ3OTYwNzQzNjA4MzMiLCJvcmdhbml6YXRpb25Db2RlIjoiODdkZDU0NjZiZmJmNGJiMDkxZTIzNDRkYWMxY2ZhZDMiLCJ1c2VyVGVycml0b3J5IjoiMSIsInVzZXJSb2xlIjoiMCIsImV4cCI6MTcyOTIyMDA5MiwiaWF0IjoxNzI5MTMzNjkyfQ.UyhWjOJSXZ6KPYjEegmrDvtnt07-0vn3d_vRNBcOV0Q"
    token2 = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXNzd29yZCI6IiIsImxvZ2luVHlwZSI6Im5vcGFzc3dvcmQiLCJ0ZW5hbnRDb2RlIjoiTmNWb2JuRWo2ckFfIiwiZXhwIjoxNzI5MTU0NzkyLCJ1c2VyQ29kZSI6ImNvdzk5RWhhb0RnXyJ9.ZupWlXjGDHaxG9cuNqbN9ywXo1KrPWdJ8YFjt2caHnc"
    res = compareMessageChannel(signFlowId1, token, token2)
    print('xxxx',res)
    # theoryMessageInfo(token,signFlowId)
    # getMessageMockActualDetail(token2,signFlowId)
    # compareMessageChannel(signFlowId, token, token2)
    # getSignFlowIds("2024-10-14")
    # checkSignFlowIdByDay(token, token2, "2024-10-14")
    checkSignFlowIdByDayWithDetail(token, token2, "2024-10-14")