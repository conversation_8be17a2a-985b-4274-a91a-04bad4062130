# -*- coding: utf-8 -*-
# @Description :
# @Time : 2021/7/24 18:09
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : dateAndTime.py

import datetime
import time
from datetime import timedelta


def dateToStamp(date: str, timeFormat):
    """
    将字符串类型的日期转为时间戳，毫秒级
    timeFormat: "%Y-%m-%d %H:%M:%S.%f"
    return: str 1588932797000
    """
    timeArray = time.strptime(date, timeFormat)
    timeStamp = str(int(time.mktime(timeArray)) * 1000)
    return timeStamp


def somedayTime(day, timeFormat=None):
    """
    :param timeFormat: '%Y-%m-%d %H:%M:%S' 、'%Y-%m-%d'
    :param day: 未来距今天多少天
    :return: 默认返回时间 20220616163258.767022
    """
    now = datetime.datetime.now()
    afterToday = now + datetime.timedelta(days=int(day))
    if not timeFormat:
        timeFormat = '%Y%m%d%H%M%S.%f'
    else:
        if timeFormat==1:
            timeFormat = '%Y-%m-%d %H:%M:%S'
        if timeFormat==2:
            timeFormat = '%Y-%m-%d'
        if timeFormat==3:
            timeFormat = '%Y%m%d%H%M%S.%f'
    return afterToday.strftime(timeFormat)


def somedayTimestamp(day=0):
    """
    :param day: 未来距今天多少天
    return: float 1677218631932.335，微秒
    """
    now = datetime.datetime.now()
    afterToday = now + datetime.timedelta(days=int(day))
    return afterToday.timestamp() * 1000

def get_future_time(interval=0):
    """
    :param interval: 时间间隔值  秒
    :return: 例2022-01-01 23：56：11
    """
    now = datetime.datetime.now()
    later = now + datetime.timedelta(seconds=int(interval))
    return later.strftime('%Y-%m-%d %H:%M:%S')




if __name__ == "__main__":
    print(dateToStamp('2020-05-08 18:13:17.345', '%Y-%m-%d %H:%M:%S.%f'))
    print(somedayTime(30, '%Y-%m-%d'))
    print(somedayTimestamp(-60))
    # print(heyunDeceng(5))