import random

import requests

from utils import ENV, log


def get_signDocInfoRequestsData(detailData, sealParams):
    """
    组装签署数据
    :param detailData:
    :param sealParams: 要用到的签署印章 企业格式{"personSealId":xxx,"orgSealId":xxx,"legalSealId":xxxx,"orgSealCode":xxxx}
    :return:
    """
    signDocInfoRequests = []
    print('signDocInfoRequests-detailData-xxxxxx', detailData)
    print('signDocInfoRequests-detailData-xxxxxx22', sealParams)
    try:
        docInfoList = detailData.get('docInfo')
        signConfigInfoList = detailData.get('signConfigInfo')
        signAreaSignEnable = detailData.get('signAreaSignEnable') #是否必签；0-非必签；1-必签
        for item1 in docInfoList:
            detailRequests = []
            if item1.get('taskId'):
                signDocInfoRequest = {}
                for item2 in signConfigInfoList:
                    if item2.get('docId') == item1.get('id'):
                        print('signDocInfoRequests-000', item2.get('docId'))
                        ##增加备注签署的能力====目前直接合成固定的图片作为备注签内容
                        remarkSignConfigList0 = []
                        if len(item2.get('remarkSignConfigList')) >0:
                            remarkSignConfigList0 = get_remarkSignConfig(item2)
                        actorSignConfigList0 = item2.get('actorSignConfigList')
                        if actorSignConfigList0:
                            print('signDocInfoRequests-4444', actorSignConfigList0)
                            if 'orgSealCode' not in sealParams.keys():
                                actorSignConfigList0 = [item_0 for item_0 in actorSignConfigList0 if
                                                 item_0.get("sealIdentityType") != 2]
                            if 'legalSealId' not in sealParams.keys():
                                actorSignConfigList0 = [item_0 for item_0 in actorSignConfigList0 if
                                                        item_0.get("sealIdentityType") != 3]
                            for item3 in actorSignConfigList0:  # 指定了多个签署区详情
                                detailRequest = item3
                                print('signDocInfoRequests-111111', detailRequests)
                                detailRequest['sealTypeCode'] = 'COMMON-SEAL'
                                if item3.get('sealIdentityType') == 1:  # 签署区是个人签署区
                                    if 'personSealId' in sealParams.keys():
                                        detailRequest['sealId'] = sealParams.get('personSealId')
                                        # 印章类型(open-api 可指定必须使用手绘) 1手绘 2个人模板印章 3机构模板印章 4法人印章 多个逗号隔开
                                        detailRequest['sealType'] = 2
                                        if detailRequest['freeMode'] == 1:
                                            detailRequest['posX'] = 200
                                            detailRequest['posY'] = 200
                                            detailRequest['freeMode'] = 0
                                    else:
                                        continue
                                if item3.get('sealIdentityType') == 2 and 'orgSealCode' in sealParams.keys():  # 签署区是企业签署区
                                    detailRequest['sealId'] = sealParams['orgSealId']
                                    detailRequest['sealTypeCode'] = sealParams['orgSealCode']
                                    detailRequest['sealType'] = 3
                                if item3.get('sealIdentityType') == 3 :  # 签署区是法人签署区
                                    if 'legalSealId' in sealParams.keys():
                                        detailRequest['sealId'] = sealParams.get('legalSealId')
                                        detailRequest['sealType'] = 4
                                        detailRequest['sealTypeCode'] = 'LEGAL-PERSON-SEAL'
                                    else:
                                        continue
                                if item3.get('sealIdentityType') == 1:  # 签署区是个人签署区
                                    detailRequest['sealTypeCode'] = ''
                                    if item3.get('isHandEnable') == 1:
                                        detailRequest['sealType'] = 1
                                if item3.get('detailId'):
                                    detailRequest['detailUUID'] = item3.get('detailId')
                                if item3.get('isAddSealSignTime') == None:
                                    detailRequest['isAddSealSignTime'] = 0
                                    detailRequest['assignPosition'] = 1
                                if item3.get('posY') == None:
                                    detailRequest['posX'] = 300
                                    detailRequest['posY'] = 300
                                if item3.get('pageNo') == None:
                                    detailRequest['pageNo'] = 1
                                # 签署区类型 0普通签署区 1备注签署区
                                detailRequest['signFieldType'] = 0
                                detailRequest['signType'] = detailRequest.get('signType') + 1
                                detailRequests.append(detailRequest)
                        else:  # 自由签署区的时候，签署默认设置成单页签署
                            # sealType: 1手绘 2个人 3机构 4法人
                            detailRequest = {"isHandEnable": 0, "signType": 2, "edgeScope": 0, "pageNo": "1",
                                             "isAddSealSignTime": 0, "sealSignTimeFormat": None, "posX": random.randint(100, 400),
                                             "posY": random.randint(100, 700), "timePosX": None, "timePosY": None, "fontSize": None,
                                             "curKey": None, "rotate": 0, "allowMove": 1, "sealTypeCode": "COMMON",
                                             "signFieldType": 0, "sealType": 2}
                            if 'orgSealCode' in sealParams.keys():
                                detailRequest['sealId'] = sealParams['orgSealId']
                                detailRequest['sealTypeCode'] = sealParams['orgSealCode']
                                detailRequest['sealType'] = 3
                            else:
                                detailRequest['sealId'] = sealParams['personSealId']
                            detailRequests.append(detailRequest)
                            # print('signDocInfoRequests-33333', detailRequests)
                detailRequests = detailRequests + remarkSignConfigList0
                signDocInfoRequest['detailRequests'] = detailRequests
                signDocInfoRequest['taskUUID'] = item1['taskId']
                signDocInfoRequest['processDocId'] = item1['id']
                signDocInfoRequests.append(signDocInfoRequest)
                print('signDocInfoRequests-xxxxxx', signDocInfoRequests)

        return signDocInfoRequests
    except Exception as e:
        log.info('getSignDocInfoRequestsDataByDetail-通过签署详情组装签署信息失败：%r' % e.args)
        return None

def getWilling(authorization, processId, organizeCode=None):
    """
    获取相对方的验证码意愿的意愿信息
    :param authorization:
    :param processId:
    :param organizeCode:
    :return:
    """
    baseUrl = ENV("esign.projectHost")
    headers = {'x-timevale-project-id': ENV("esign.projectId"), 'authorization': authorization}
    redirectUrl = baseUrl + "/sign-manage-web/sign-page?mcok"
    data = {'clientType': 0, 'processId': processId, 'redirectUrl': redirectUrl, 'organizeCode': organizeCode}
    response = requests.post(url=ENV("esign.projectHost") + '/esign-signs/auth/willing',
                             json=data, headers=headers)
    json_response = response.json()
    return json_response

def get_remarkSignConfig(signConfigInfoObject):
    """
    从detail详情接口获取备注签的内容，组装签署数据
    :param signConfigInfoObject: data.signConfigInfo.#index#
    :return:
    """
    remarkSignConfigList = signConfigInfoObject.get('remarkSignConfigList')
    datas = []
    for item in remarkSignConfigList:
        data = {}
        data['detailUUID'] = item.get('detailId')
        data['remarkUUId'] = item.get('remarkUUId')
        data['signFieldType'] = 1
        data['sealId'] = ENV('pngPageFileKey')
        data['posX'] = item.get('remarkPosX')
        data['posY'] = item.get('remarkPosY')
        data['pageNo'] = item.get('remarkPageNo')
        datas.append(data)
    print('[备注签]-get_remarkSignConfig', datas)
    return datas


def data_array_sort(json_array, sort_str,index, key_str):
    """
    给数组按照某个字段排序后再返回第index个对象里面的某个字段值
    :param json_array: 需要处理的数组数据
    :param field_to_sort: 用于排序的字段
    :param index: 获取第N个对象
    :param key_str: 获取json中的键为key_str的值
    :return:
    """
    sorted_json_array = sorted(json_array, key=lambda x: x[sort_str])
    target_object = sorted_json_array[index]
    print('[data_array_sort]数据排序后的json取值：',target_object.get(key_str))
    return target_object.get(key_str)


if __name__ == "__main__":
    # a =  {'processId': '71d2d3e38978f1de797c98926e3d08f8', 'isClean': 0, 'subject': '全链路用例自动化业务模板**************.827054-测试全链路一,测试全链路批量外部一等', 'businessNo': None, 'remark': None, 'signNotice': 0, 'signOffTime': '', 'isReadComplete': 1, 'currentReadComplete': 1, 'currentGovSignFlag': 0, 'businessTypeId': '', 'isSort': 0, 'docInfo': [{'fileKey': '1686573974118_sFo1Hgpc.pdf', 'cryptoFileKey': '958158eaa1e05759e875d43fd43c7489b67b411209a02e1dd69315a4f4308e9e', 'fileName': 'key30page.pdf', 'fileStatus': None, 'fileType': 1, 'fileEncrypt': None, 'pageNumber': 30, 'operationType': 2, 'openFile': None, 'taskId': 'eb208c167c54edf65b9ee7a9d51ecf62', 'id': 1226752, 'isSplit': 1, 'pageSize': 10, 'qrPermission': None, 'originFileKey': '1686573974118_sFo1Hgpc.pdf'}], 'attachmentDocInfo': [], 'signInfo': [{'signModel': 1, 'list': [{'signNode': 1, 'signOrder': 1, 'userType': 1, 'signerType': 2, 'organizeCode': '4a0ea22d6ad44675b21ae3516e87a96f', 'userCode': 'cesqlly2', 'userName': '测试全链路一', 'organizeName': 'esigntest混合云全链路一测试公司', 'telOrEmail': None, 'sealTypeId': None, 'sealTypeName': None, 'legalSignFlag': 0, 'accountList': [{'id': 'cesqlly2', 'name': '测试全链路一'}], 'organizeList': None, 'sealTypeList': None, 'signModel': 1, 'signStatus': 1, 'isRead': 0, 'readTime': None, 'actorId': '87c564ca5573c85727ee54acfe4fa7e9', 'processId': '71d2d3e38978f1de797c98926e3d08f8', 'departmentCode': '4a0ea22d6ad44675b21ae3516e87a96f', 'departmentName': 'esigntest混合云全链路一测试公司', 'legalSealAuthFlag': None, 'govSignFlag': 0, 'ukeyOnly': 0, 'orgRetroactiveMainSealFlag': 0}, {'signNode': 1, 'signOrder': 2, 'userType': 2, 'signerType': 1, 'organizeCode': '', 'userCode': 'cesqllplwby', 'userName': '测试全链路批量外部一', 'organizeName': '', 'telOrEmail': None, 'sealTypeId': None, 'sealTypeName': None, 'legalSignFlag': 0, 'accountList': [{'id': 'cesqllplwby', 'name': '测试全链路批量外部一'}], 'organizeList': None, 'sealTypeList': None, 'signModel': 1, 'signStatus': 1, 'isRead': 0, 'readTime': None, 'actorId': '7364db68fab51ecfe685d0b87c2bc917', 'processId': '71d2d3e38978f1de797c98926e3d08f8', 'departmentCode': '2d31ab4405f24f549b0c62e1dde0b0ac', 'departmentName': 'esigntest混合云全链路批量外部一测试公司', 'legalSealAuthFlag': None, 'govSignFlag': 0, 'ukeyOnly': 0, 'orgRetroactiveMainSealFlag': 0}, {'signNode': 1, 'signOrder': 3, 'userType': 2, 'signerType': 2, 'organizeCode': '2d31ab4405f24f549b0c62e1dde0b0ac', 'userCode': 'cesqllplwby', 'userName': '测试全链路批量外部一', 'organizeName': 'esigntest混合云全链路批量外部一测试公司', 'telOrEmail': None, 'sealTypeId': None, 'sealTypeName': None, 'legalSignFlag': 0, 'accountList': [{'id': 'cesqllplwby', 'name': '测试全链路批量外部一'}], 'organizeList': None, 'sealTypeList': None, 'signModel': 1, 'signStatus': 1, 'isRead': 0, 'readTime': None, 'actorId': '6f1f210b445776894e8496cc9c52a62d', 'processId': '71d2d3e38978f1de797c98926e3d08f8', 'departmentCode': '2d31ab4405f24f549b0c62e1dde0b0ac', 'departmentName': 'esigntest混合云全链路批量外部一测试公司', 'legalSealAuthFlag': None, 'govSignFlag': 0, 'ukeyOnly': 0, 'orgRetroactiveMainSealFlag': 0}]}], 'noticeInfo': [], 'signConfigInfo': [{'fileKey': '1686573974118_sFo1Hgpc.pdf', 'isFreeSign': True, 'sealTypes': 'personSeal', 'docId': 1226752, 'actorSignConfigList': [], 'remarkSignConfigList': []}], 'businessTypeName': '全链路用例自动化业务模板**************.827054', 'signStatus': 1, 'description': None, 'accountName': '测试文档中心自动化用户勿改动', 'organizeName': 'esigntest文档中心自动化测试公司勿改动', 'organizeCode': '6ac62c5fb7664cdf95b89469dab8af2e', 'parentOrganizationCode': '6ac62c5fb7664cdf95b89469dab8af2e', 'parentOrganizationName': 'esigntest文档中心自动化测试公司勿改动', 'noticeStr': '', 'signerTables': None, 'readTime': 1, 'isReadTimeComplete': 0, 'userType': 2, 'actorId': '6f1f210b445776894e8496cc9c52a62d', 'currentUserCode': 'cesqllplwby', 'currentActorId': 2167203, 'apprvalStatus': None, 'aiHandEnable': 1, 'downloadEnable': 1, 'handEnable': 1, 'processType': 1, 'currentSignStatus': 1, 'currentExecuteStatus': None, 'redirectUrl': None, 'signSource': None, 'organizeChangeFlag': 0, 'signNodeActorIdList': None, 'otherCostEnable': 1, 'chargingType': 1, 'fileFormat': 1, 'appRedirectInfo': [], 'signAreaMoveEnable': 1, 'signAreaSignEnable': 1}
    #
    sealParams = {"personSealId":"xxx","orgSealId":"xxx","legalSealId":"xxxx","orgSealCode":"xxxx"}
    # get_signDocInfoRequestsData(a, sealParams)
