import random
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import base64
from utils import log

"""RSA加密公钥"""
RSA_PUB_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi623AW1H9Q8jha6a6gWe2dxNOpZRxyIwbV0wCf0JWXVXn20FB1mxDOnMLKNZ" \
              "F/TRDvkBmjpGtNKl74BhW8HdTGKC6CvsvExBUbV5p+K4b97o2wp2cENw0n3NX7TPZgUVYrRmSI10MniVF9hdsqJ+4Nha6yslMhM5CHgh" \
              "gGXbfHR1a6n4tteUfSAS5YzataqkNqaXbFN8t5crxAFxgQ4eY+u1nKmy14PysXgcPb1f3rCKFW4fU3RN4nQEDXrbJATr4rs0AW7nyag" \
              "Bg7AjmSDc3oVzT+E26q/z8M3AaS+Ymoseg3HZNbf/+8D+df6jktI/FJcl0LGA/TqM/0RmSAFtbQIDAQAB"


def encryptKey(msg):
    """
    RSA加密
    :param msg: 需要加密的数据 登录手机号等
    :param rsa_pub_key: RSA公钥字符串
    :return: 加密后的数据
    """
    log.info('--加密前 %s', msg)
    pubKey = '-----BEGIN PUBLIC KEY-----\n' + RSA_PUB_KEY + '\n-----END PUBLIC KEY-----'
    rsa_public_key = serialization.load_pem_public_key(pubKey.encode(), backend=default_backend())
    encryptBytes = rsa_public_key.encrypt(
        str(msg).encode(),
        padding.PKCS1v15(),
    )
    encryptMsg = base64.b64encode(encryptBytes).decode()
    log.info('--加密完成：%s', encryptMsg)
    return encryptMsg

def get_randomNo_8():
    """
    生成8位随机数
    """
    return random.randint(10000000, 99999999)


def get_randomNo_16():
    """
    生成16位随机数
    """
    return random.randint(1000000000000000, 9999999999999999)


def get_randomNo_32():
    """
    生成32位随机数
    """
    return random.randint(10000000000000000000000000000000, 99999999999999999999999999999999)

def get_regular_str(str,key):
    """
    正则匹配字符串str,获取key相应的值
    :param str:
    :param key:
    :return:
    """
    import re
    pattern = rf'{key}=([^&]+)'
    match = re.search(pattern, str)
    if match:
        return match.group(1)
    else:
        return None

if __name__ == "__main__":
    msg = "16666666666"