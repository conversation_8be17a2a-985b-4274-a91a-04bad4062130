import random
from time import sleep

import requests
from utils import ENV
from utils.esignToken import openApiSignature
from utils.common import get_randomNo_32

MAIN_HOST = ENV('esign.projectHost')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')
OPENAPI_HOST = ENV('esign.gatewayHost')
USER01 = ENV('ceswdzxzdhyhwgd1.userCode')

def createEnterpriseSeal(data):
    """
    获取 或是 创建企业印章 或是 法人章
    :param headers:
    :param data:
    e.g.
    {'organizationCode': '4a0ea22d6ad44675b21ae3516e87a96f', 'sealTypeCode': '1682663282631', 'sealPatternSlected': 2, 'sealManagerName': '测试文档中心自动化用户勿改动', 'sealChargeOrganizationName': 'esigntest文档中心自动化测试公司勿改动', 'sealChargeOrganizationCode': '6ac62c5fb7664cdf95b89469dab8af2e', 'sealManagerCode': 'ceswdzxzdhyhwgd1', 'organizationName': 'esigntest混合云全链路一测试公司', 'sealName': 1687952206524, 'organizationId': '1651488482549477377',
    'personalSealId': None, 'signSealCertIds': ['1674019014045446145'], 'makeSealCertId': '1674019023696539649'}
    :return:
    """
    sealTypeCode = data.get('sealTypeCode')
    sealName = data.get('sealName')
    userCode = data.get('userCode')
    organizationCode = data.get('organizationCode')
    sealPatternSlected = data.get('sealPatternSlected') #1-云商密 2-云国密
    # 先查印章是否已经存在，如是已经存在则返回已有的印章信息
    obj0 = listOrganizationSeals(organizationCode, "", sealPatternSlected, sealTypeCode, sealName)
    if obj0.get('data').get('total') >0:
        sealId = obj0.get('data').get('records')[0].get('sealId')
    else:
        personalSealId = data.get('personalSealId')
        obj5 = createOrgSeals(organizationCode, sealPatternSlected, personalSealId, 1, userCode, sealTypeCode, sealName)
        sealId = obj5.get('data').get('sealInfos')[0].get('sealId')
        print('创建企业印章：',obj5)
        # 给印章授权
        sealssignersAll(sealId)
    print('获取到企业印章：', sealId)
    return sealId


def createSealType(headers, sealTypeCode):
    """
    创建印章类型
    :param headers:
    :param sealTypeCode:
    :return:
    """
    obj1 = {
        "params": {
            "id": None,
            "sealTypeCode": sealTypeCode,
            "sealTypeName": sealTypeCode,
            "sealTypeStatus": 0
        },
        "domain": "seal_system"
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/sealtype/saveSealType',
                             json=obj1, headers=headers)
    json_response = response.json()
    return json_response


def detailEnterpriseSeals(headers, sealIdLong):
    """
    查询印章详情
    :param headers:
    :param sealId:
    :return:
    """
    headers['navId'] = '1523545066692546562'
    obj1 = {"params": {"sealId": sealIdLong}, "domain": "seal_system"}
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/detail',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('detailEnterpriseSeals-[request]:', obj1)
    print('detailEnterpriseSeals-[respone]:', json_response)
    return json_response


def getSmcSealManagerList(headers, sealTypeCode, organizationName, sealManagerName):
    """
    查询企业的印章管理员
    :param headers:
    :param sealTypeCode:
    :param sealManagerName:
    :return:
    """
    headers['navId'] = '1523545066692546562'
    obj1 = {
        "params": {"pageSize": 10, "currPage": 1, "organizationName": organizationName, "sealTypeCode": sealTypeCode,
                   "sealManagerName": sealManagerName}, "domain": "seal_system"}
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/sealmanager/getSmcSealManagerList',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('查询印章管理员-[request]:', obj1)
    print('查询印章管理员-[respone]:', json_response)
    return json_response


### 制作云国际标准印章
def createGroupEnterpriseSeals(headers, sealTypeCode, organizationCode, organizationName, sealThumbnailUrl,
                               cryptoFileKey, sealManagerName, sealManagerCode, sealManagerOrgName, sealManagerOrgCode,
                               personalSealId=None, sealPatternSlected=None, makeSealCertId=None, signSealCertIds=None):
    ###自定义 印章分组名称=印章名称
    sealName = random.randint(1000000000000000, ****************)
    headers['navId'] = '1523545066692546562'
    subjson = {
        "sealBodyStructure": 1,
        "reuseSealId": None,
        "sealStandardsTranslation": "国际标准"
    }
    sealTypeName = "公章"
    if len(sealTypeCode) == 0 and personalSealId is None:
        sealTypeCode = "COMMON-SEAL"
    if personalSealId:
        sealTypeCode = "LEGAL-PERSON-SEAL"
        sealTypeName = "法人章"
        subjson["legalFlag"] = "1"
        subjson["personalSealId"] = personalSealId
        subjson["customSealType"] = 1
    else:
        ### 企业章章面需要的
        subjson["sealSurroundword"] = organizationName
        subjson["sealBottomword"] = ""
        subjson["sealHorizontalOneText"] = ""
        subjson["sealHorizontalText"] = ""
        subjson["sealHorizontalTwoText"] = ""
        subjson["innerTopSurroundText"] = ""
        subjson["innerWidth"] = 33
        subjson["innerHeight"] = 18
        subjson["innerBorderWidth"] = 0.5
        subjson["sealSource"] = 2

    ## 印章组信息
    sealGroupConfig = {
        "organizationCode": organizationCode,
        "organizationName": organizationName,
        "sealTypeCode": sealTypeCode,
        "sealTypeName": sealTypeName,
        "sealGroupId": "",
        "sealGroupName": sealName,
        "sealGroupDesc": "印章分组说明信息：来源于集测的公共方法createGroupEnterpriseSeals创建"
    }
    ## 印章管理员信息
    sealAdminList = [
        {
            "userCode": sealManagerCode,
            "userName": sealManagerName,
            "organizationCode": sealManagerOrgCode,
            "organizationName": sealManagerOrgName
        }
    ]
    ## 云中国标准印章相关信息
    if sealPatternSlected == 3:
        subjson["sealBodyStructure"] = 2
        subjson["sealStandardsTranslation"] = "中国标准"
        subjson["makeSealCertId"] = makeSealCertId
        subjson["signSealCertIds"] = signSealCertIds

    sealConfig = {
        "sealMedium": "1",
        "sealCategory": "1",
        "organizationCode": organizationCode,
        "organizationName": organizationName,
        "sealTypeCode": sealTypeCode,
        "sealTypeName": sealTypeName,
        "sealGroupId": "",
        "sealGroupName": sealName,
        "sealGroupDesc": sealName,
        "sealStatus": "1",
        "sealType": "1",
        "sealName": sealName,
        "canDelete": True,
        "canView": True,
        "sealCenterImg": "2",
        "sealHeight": 42,
        "sealWidth": 42,
        "sealSize": 42,
        "borderWidth": 1.2,
        "sealUserName": "",
        "sealShape": "1",
        "sealColour": "2",
        "sealOpacity": 1,
        "stampRule": "0",
        "oldStyle": "",
        "fileKey": "",
        "cryptoFileKey": cryptoFileKey,
        "sealThumbnailUrl": sealThumbnailUrl,
        "sealUrl": "",
        "sealWidthPixels": "158.740157",
        "sealHeightPixels": "158.740157",
        "sealMediumTranslation": "云印章",
        "oId": sealName,
        "sealUserList": [],
        "sealAdminList": sealAdminList
    }
    sealConfigs = {**sealConfig, **subjson}
    obj1 = {
        "params": {
            "sealGroupConfig": sealGroupConfig,
            "sealConfigList": [sealConfigs],
            "autoPushSeal": "1",
            "draftOrProduction": "1"
        },
        "domain": "seal_system"
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/group/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('创建分组并制章-[request]:', obj1)
    print('创建分组并制章-[respone]:', json_response)
    return json_response


def saveSealManager(headers, sealTypeCode, sealManagerCode, sealManagerName, organizationCode, organizationName,
                    organizationId, enableMultipleSeals):
    """
    分配企业印章管理员
    :param headers:
    :param sealTypeCode:
    :param sealManagerCode:
    :param sealManagerName:
    :param organizationCode:
    :param organizationName:
    :param organizationId:
    :param enableMultipleSeals:
    :return:
    """
    obj1 = {
        "params": {
            "organizationId": organizationId,
            "sealTypeName": sealTypeCode,
            "organizationName": organizationName,
            "organizationCode": organizationCode,
            "sealTypeCode": sealTypeCode,
            "enableMultipleSeals": enableMultipleSeals,  ###0-不能创建多个；1-支持创建多个
            "id": None,
            "sealManagerCode": sealManagerCode,
            "sealManagerName": sealManagerName
        },
        "userCode": None,
        "domain": "seal_system"
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/sealmanager/saveSealManager',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('saveSealManager-[request]:', obj1)
    print('saveSealManager-[respone]:', json_response)
    return json_response


def previewElectronicSeal(headers, sealSurroundword):
    """
    生成印章图片-印模
    :param headers:
    :param sealSurroundword:
    :return:
    """
    obj1 = {
        "params": {
            "sealBodyStructure": 1,
            "sealSurroundword": sealSurroundword,
            "sealBottomword": None,
            "sealHorizontalText": None,
            "sealHorizontalOneText": None,
            "sealHorizontalTwoText": None,
            "sealShape": 1,
            "sealCenterImg": 2,
            "sealColour": 1,
            "sealOpacity": 1,
            "sealHeight": 42,
            "sealWidth": 42,
            "innerTopSurroundText": None,
            "oldStyle": None
        },
        "domain": "seal_system"
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/sealmodel/previewElectronicSeal',
                             json=obj1, headers=headers)
    json_response = response.json()
    return json_response


def saveElectronicSeal(headers, sealName, sealTypeCode, sealThumbnailUrl, organizationCode, organizationName,
                       organizationId,
                       signSealCertIds, sealChargeOrganizationCode, sealChargeOrganizationName,
                       sealPatternSlected, makeSealCertId, personalSealId):
    """
    创建企业印章，包括可以创建法人印章
    :param headers:
    :param sealName:
    :param sealTypeCode:
    :param sealThumbnailUrl:
    :param organizationCode:
    :param organizationName:
    :param organizationId:
    :param signSealCertIds:
    :param sealChargeOrganizationCode:
    :param sealChargeOrganizationName:
    :param sealPatternSlected:
    :param makeSealCertId:
    :param personalSealId:
    :return:
    """
    obj1 = saveElectronicSealData(None, sealName, sealTypeCode, sealThumbnailUrl, organizationCode, organizationName,
                                  organizationId,
                                  signSealCertIds, sealChargeOrganizationCode, sealChargeOrganizationName,
                                  sealPatternSlected, makeSealCertId, personalSealId)
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/electronic/saveElectronicSeal',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('saveElectronicSeal-[request]:', obj1)
    print('saveElectronicSeal-[respone]:', json_response)
    return json_response


def saveElectronicSealData(sealId, sealName, sealTypeCode, sealThumbnailUrl, organizationCode, organizationName,
                           organizationId,
                           signSealCertIds, sealChargeOrganizationCode, sealChargeOrganizationName,
                           sealBodyStructure, makeSealCertId, personalSealId):
    """
    企业印章的数据处理
    :param sealId:
    :param sealName:
    :param sealTypeCode:
    :param sealThumbnailUrl:
    :param organizationCode:
    :param organizationName:
    :param organizationId:
    :param signSealCertIds:
    :param sealChargeOrganizationCode:
    :param sealChargeOrganizationName:
    :param sealBodyStructure:
    :param makeSealCertId:
    :param personalSealId:
    :return:
    """
    if sealTypeCode == "LEGAL-PERSON-SEAL":
        sealTypeName = "法人章"
    else:
        sealTypeName = sealTypeCode
    if sealBodyStructure == 2:
        sealPatternSlected = "1,2"
    else:
        sealPatternSlected = "1"
    obj1 = {
        "params": {
            "signSealCertIds": signSealCertIds,
            "sealChargeOrganizationCode": sealChargeOrganizationCode,
            "businessId": None,
            "sealUserList": [],
            "sealHeight": 42,
            "sealBodyStructure": sealBodyStructure,
            "logicFlag": None,
            "organizationId": organizationId,
            "sealScale": None,
            "sealHorizontalText": None,
            "ukeyAsn1SealId": None,
            "originalSealId": None,
            "physicalSealCode": None,
            "sealSurroundword": organizationName,
            "sealWidthPixels": 158.740157,
            "sendNotice": None,
            "id": sealId,
            "sealOpacity": 1,
            "sealSource": 2,
            "sealCode": sealName,
            "makeSealCertId": makeSealCertId,
            "processInstanceId": None,
            "organizationName": organizationName,
            "sealName": sealName,
            "sealHorizontalOneText": None,
            "organizationCode": organizationCode,
            "sealAngle": None,
            "depId": None,
            "sealDesc": None,
            "workFlowFlag": 2,
            "PROJECT_ID": None,
            "sealColour": 1,
            "sealDefinitionType": None,
            "sealHeightPixels": 158.740157,
            "sealTypeName": sealTypeName,
            "managerOrgCode": organizationCode,
            "sealCenterImg": 2,
            "sealTypeCode": sealTypeCode,
            "sealWidth": 42,
            "managerOrgName": organizationName,
            "sealStatus": "g",
            "distTodoTaskId": None,
            "sealThumbnailUrl": sealThumbnailUrl,
            "sealChargeOrganizationName": sealChargeOrganizationName,
            "sealPatternSlected": sealPatternSlected,
            "sealShape": 1,
            "personalSealId": personalSealId
        },
        "userCode": None,
        "domain": "seal_system"
    }
    return obj1


def authSealUser(headers, sealIdLong):
    '''
    为企业印章授权
    :param headers:
    :param sealId: 印章Id
    :return:
    '''
    obj1 = {
        "params": {
            "sealId": sealIdLong,
            "sealUseRange": 1,
            "sealUserList": []
        },
        "domain": "seal_system"
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/electronic/authSealUser',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('授权给所有人: ', json_response)
    return json_response


def pageElectronicSealGroupList(headers, sealId, sealTypeCode=None, departmentCode=None, sealPattern=None,
                                sealStatus=None):
    """
    查询企业印章列表信息
    :param headers:
    :param sealId:
    :param sealTypeCode:
    :param organizationName:
    :return:
    """
    if sealStatus == None:
        sealStatus = ["g"]
    else:
        sealStatus = [sealStatus]
    if sealPattern == None:
        sealPattern = ["1"]
    obj1 = {
        "params": {
            "currPage": 1,
            "pageSize": 10,
            "remoteSealId": sealId,
            "sealName": None,
            "sealTypeCode": sealTypeCode,
            "departmentCode": departmentCode,
            "sealStatusList": sealStatus,
            "showChildOrganizeSeal": False,
            "sealPatterns": sealPattern
        },
        "domain": "seal_system"
    }
    headers['navId'] = '1523545066692546562'
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/group/page/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('sealGroupList-[request]:', obj1)
    print('sealGroupList-[respone]:', json_response)
    return json_response


### 在印章组内查询印章列表信息
def pageElectronicSealList(headers, sealGroupId, sealPattern=None, sealId=None, sealStatus=None):
    if sealStatus == None:
        sealStatus = ['g']
    else:
        sealStatus = [sealStatus]
    if sealPattern == None:
        sealPattern = ["1"]
    obj1 = {
        "params": {
            "sealGroupId": sealGroupId,
            "currPage": 1,
            "pageSize": 10,
            "sealTypeCode": "",
            "sealStatus": sealStatus,
            "sealPatterns": sealPattern,
            "ukeySn": "",
            "sealName": "",
            "remoteSealId": sealId
        },
        "domain": "seal_system"
    }
    headers['navId'] = '1523545066692546562'
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/page/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('pageElectronicSealList-[request]:', obj1)
    print('pageElectronicSealList-[respone]:', json_response)
    return json_response


def createElectronicCert(headers, params):
    """
    查询 或 创建企业证书
    :param headers:
    :param params: e.g. {"organizationName":"xxxx","certAlgorithm":1,"licenseNumber":"xxxx无需加密","organizationCode":"xxxx"}
    :return:
    """
    organizationName = params.get('organizationName')
    certAlgorithm = params.get('certAlgorithm')
    obj1 = getEnterpriseCertList(headers, organizationName, certAlgorithm)
    if obj1.get('data').get('totalCount') > 0:
        return obj1.get('data').get('list')[0].get('id')
    else:
        obj2 = saveEnterpriseCert(headers, params.get('organizationCode'), organizationName,
                                  params.get('licenseNumber'), certAlgorithm)
        return obj2.get('message')


def createPersonCert(headers, params):
    """
    查询 或 创建个人证书
    :param headers:
    :param params: e.g. {"userName":"测试全链路四一","certAlgorithm":1,"licenseNumber":"xxxx加密后的数据","userCode":"cesqllsy12"}
    :return:
    """
    userName = params.get('userName')
    certAlgorithm = params.get('certAlgorithm')
    obj1 = getPersonalCertList(headers, userName, certAlgorithm)
    if obj1.get('data').get('totalCount') > 0:
        return obj1.get('data').get('list')[0].get('id')
    else:
        obj2 = savePersonalCert(headers, params.get('userCode'), userName, params.get('licenseNumber'), certAlgorithm)
        return obj2.get('message')


def saveEnterpriseCert(headers, organizationCode, organizationName, licenseNumber, certAlgorithm):
    """
    创建企业证书
    :param headers:
    :param organizationCode:
    :param organizationName:
    :param licenseNumber:
    :param certAlgorithm:
    :return:
    """
    obj1 = {
        "params": {
            "licenseType": 12,
            "certType": 1,
            "organizationName": organizationName,
            "organizationCode": organizationCode,
            "certName": organizationName,
            "certAlgorithm": certAlgorithm,  # 1-rsa证书；2-sm2证书
            "applyMethod": 1,
            "licenseNumber": licenseNumber
        },
        "userCode": ""
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/certs/enterprise/saveEnterpriseCert',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('saveEnterpriseCert-[request]:', obj1)
    print('saveEnterpriseCert-[respone]:', json_response)
    return json_response


def savePersonalCert(headers, userCode, userName, licenseNumber, certAlgorithm):
    """
    创建个人证书
    :param headers:
    :param userCode:
    :param userName:
    :param licenseNumber:
    :param certAlgorithm:
    :return:
    """
    obj1 = {
        "params": {
            "licenseType": "IDCard",
            "certType": 1,
            "certName": userName,
            "certAlgorithm": certAlgorithm,
            "applyMethod": 1,
            "licenseNumber": licenseNumber,  # 需要加密过的
            "userCode": userCode
        },
        "userCode": userCode
    }
    response = requests.post(url=MAIN_HOST + '/seals/smc/certs/personal/savePersonalCert',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('savePersonalCert-[request]:', obj1)
    print('savePersonalCert-[respone]:', json_response)
    return json_response


def getEnterpriseCertList(headers, organizationName, certAlgorithm):
    """
    查询企业证书列表
    :param headers:
    :param organizationName:
    :param certAlgorithm:
    :return:
    """
    obj1 = {
        "params": {
            "currPage": 1,
            "pageSize": 10,
            "certName": "",
            "certType": 1,
            "id": "",
            "certAlgorithm": certAlgorithm,
            "certStatus": "1",
            "organizationName": organizationName
        },
        "domain": "seal_system"
    }
    headers['navId'] = '1523545066688352256'
    response = requests.post(url=MAIN_HOST + '/seals/smc/certs/enterprise/getEnterpriseCertList',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('getEnterpriseCertList-[request]:', obj1)
    print('getEnterpriseCertList-[respone]:', json_response)
    return json_response


def getPersonalCertList(headers, userName, certAlgorithm):
    """
    查询个人证书
    :param headers:
    :param userName:
    :param certAlgorithm:
    :return:
    """
    obj1 = {
        "params": {
            "currPage": 1,
            "pageSize": 10,
            "certName": userName,
            "certType": 1,
            "id": "",
            "certAlgorithm": certAlgorithm,
            "userName": userName,
            "certStatus": "1"
        },
        "domain": "seal_system"
    }
    headers['navId'] = '1523545066688353257'
    response = requests.post(url=MAIN_HOST + '/seals/smc/certs/personal/ownerManager/getPersonalCertList',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('getPersonalCertList-[request]:', obj1)
    print('getPersonalCertList-[respone]:', json_response)
    return json_response


def listOrganizationSeals(organizationCode, userCode, sealPattern=None, sealTypeCode=None,sealName=None):
    """
    查询用户的企业授权印章列表（分页）
    :param organizationCode:
    :param userCode:
    :param sealPattern:
    :param sealTypeCode:
    :return:
    """
    if sealPattern == None:
        sealPattern = 1
    obj1 = {
        "organizationCode": organizationCode,
        "pageNo": 1,
        "pageSize": 10,
        "sealPattern": sealPattern,  # 印章形态(1-商密印章 2-物理印章 3-国密印章 4-UKey商密印章 5-UKey国密印章 )
        "sealTypeCode": sealTypeCode,
        "userCode": userCode,
        "sealName": sealName
    }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('listOrganizationSeals-[request]:', obj1)
    print('listOrganizationSeals-[respone]:', json_response)
    return json_response


def listUserseals(userCode, sealPattern=None):
    """
    查询用户的生效个人印章列表
    :param userCode:
    :param sealPattern:
    :return:
    """
    if sealPattern == None:
        sealPattern = 1
    obj1 = {
        "pageNo": 1,
        "pageSize": 10,
        "sealPattern": sealPattern,  # 印章形态(1-商密印章 2-物理印章 3-国密印章 4-UKey商密印章 5-UKey国密印章 )
        "userCode": userCode
    }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userseals/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('listUserseals-[request]:', obj1)
    print('listUserseals-[respone]:', json_response)
    return json_response


def PagePersonalSealList(headers, sealId, sealBodyStructure=None, sealStatus=None, ownerName=None):
    """
    查询个人印章列表信息，默认查询已发布的云国际标准印章
    :return:
    """
    if sealStatus == None:
        sealStatus = 'g'
    if sealBodyStructure == None:
        sealBodyStructure = 1
    obj1 = {
        "params": {
            "currPage": 1,
            "pageSize": 10,
            "sourceTab": "2",
            "sealId": sealId,
            "sealName": "",
            "sealStatus": sealStatus,
            "sealBodyStructure": sealBodyStructure,
            "keySn": "",
            "createUserName": "",
            "ownerName": ownerName
        },
        "domain": "seal_system"
    }
    headers['navId'] = '1523545066696741864'
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/personal/ownerManager/pagePersonalSealList',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('PagePersonalSealList-[request]:', obj1)
    print('PagePersonalSealList-[respone]:', json_response)
    return json_response


######清理数据##########
def DeleteEnterpriseSeal(headers, sealId):
    """
    删除某个企业印章
    :param headers: 企业印章管理员的登录头信息
    :param sealId: 要删除的印章
    :return:
    """
    headers['navId'] = '1523545066692546562'

    obj1 = {"params": {"sealId": sealId, "disablePhysicalSeal": False}, "domain": "seal_system"}
    # 停用企业印章
    response1 = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/electronic/disableSeal',
                              json=obj1, headers=headers)
    json_response1 = response1.json()

    obj2 = {"params": {"sealId": sealId}, "domain": "seal_system"}
    # 停用企业印章
    response2 = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/electronic/deleteSealById',
                              json=obj2, headers=headers)
    json_response2 = response2.json()
    print('停用印章:', json_response1)
    print('删除印章:', json_response2)
    print('删除印章:', sealId)
    return sealId


#####openapi##########
def getUserSealsByStatus(headers, userCode, sealStatus=None, sealBodyStructure=None):
    """
    默认查询用户的发布状态的印章信息
    :param userCode:
    :param sealPattern:
    :return:
    """
    userName = "" ##TODO---根据userCode查询用户名称
    obj1 = PagePersonalSealList(headers, "", sealBodyStructure, sealStatus, userName)
    if obj1.get('data').get('totalPage') > 0:
        return obj1.get('data').get('list')[0].get('sealId')
    else:
        ####新建印章###
        obj2 = createUserseals(userCode, sealBodyStructure)
        return obj2.get('data').get('sealInfos')[0].get('sealId')

    return None

def getUserseals(userCode,sealPattern=None):
    """
    获取或是创建个人印章 (只能查询有效的个人印章)
    :param userCode:
    :param sealPattern:
    :return: sealId
    """
    obj1 = listUserseals(userCode,sealPattern)
    if obj1.get('data').get('total') > 0 :
        return obj1.get('data').get('records')[0].get('sealId')
    else:
        obj2 = createUserseals(userCode,sealPattern)
        return obj2.get('data').get('sealInfos')[0].get('sealId')

def createUserseals(userCode,sealPattern=None):
    """
    创建个人印章
    :param userCode:
    :param sealPattern:
    :return:
    """
    if sealPattern == None:
        sealPattern = 1
    sealName = random.randint(1, ****************)
    obj1 = {
          "customAccountNo": "",
          "description": "用户创建内部个人印章",
          "sealInfos": [
            {
              "sealHeight": 20,
              "sealPattern": sealPattern,  #印章形态,1-云国际标准印章,3-云中国标准印章
              "sealSource": 2,
              "sealTemplateStyle": 3,
              "sealWidth": 20
            }
          ],
          "sealName": "CUS"+str(sealName),
          "userCode": userCode
        }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userseals/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('createUserseals-[request]:', obj1)
    print('createUserseals-[respone]:', json_response)
    return json_response

def getUserCertsByStatus(userCode, certStatus=None, algorithm=None, headers=None):
    """
    默认查询用户的发布状态的证书信息
    """
    if certStatus == None:
        certStatus =1
    if algorithm == None:
        algorithm =1
    if certStatus ==1:
        obj1 = listUsercerts(userCode, algorithm)
        if len(obj1.get('data')) > 0:
            return obj1.get('data')[0].get('certId')
        else:
                ####新建证书###
            obj2 = createUsercerts(userCode, algorithm)
            if obj2.get('code') == 200:
                return obj2.get('data').get('certId')
    else:
        ###TODO 查询其他状态的证书，只能通过页面接口
        obj1 = ""
        # if obj1.get('data').get('totalPage') > 0:
        #     return obj1.get('data').get('list')[0].get('sealId')
    return None

def getUsercerts(userCode,sealPattern=None):
    """
    获取或是创建个人证书
    :param userCode:
    :param sealPattern:
    :return: sealId
    """
    obj1 = listUsercerts(userCode,sealPattern)
    if len(obj1.get('data'))>0:
        return obj1.get('data')[0].get('certId')
    else:
        obj2 = createUsercerts(userCode,sealPattern)
        return obj2.get('data').get('certId')

def createUsercerts(userCode, algorithm=None):
    """
    创建个人证书，默认是rsa证书
    :param userCode:
    :param algorithm: 证书算法类型 1-RSA 2-SM2 默认1
    :return:
    """
    if algorithm == None:
        algorithm = 1
    obj1 = {
              "algorithm": algorithm,
              "certName": "",
              "customAccountNo": "",
              "userCode": userCode
            }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userCerts/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('createUsercerts-[request]:', obj1)
    print('createUsercerts-[respone]:', json_response)
    return json_response

def listUsercerts(userCode, algorithm=None):
    """
    查询个人证书，默认是rsa证书
    :param userCode:
    :param algorithm: 证书算法类型 1-RSA 2-SM2 默认1
    :return:
    """
    if algorithm == None:
        algorithm = 1
    obj1 = {
              "algorithm": algorithm,
              "certPattern": "",
              "certSN": "",
              "customAccountNo": "",
              "keySNList": [],
              "userCode": userCode
            }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userCerts/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('listUsercerts-[request]:', obj1)
    print('listUsercerts-[respone]:', json_response)
    return json_response

def getOrganizationCertsByStatus(organizationCode, certStatus=None, algorithm=None, headers=None):
    """
    默认查询企业的发布状态的证书信息
    """
    if certStatus == None:
        certStatus =1
    if algorithm == None:
        algorithm =1
    if certStatus ==1:
        obj1 = openapiListOrganizationCerts(organizationCode, algorithm)
        if len(obj1.get('data')) > 0:
            return obj1.get('data')[0].get('certId')
        else:
                ####新建证书###
            obj2 = openapiCreateOrganizationCerts(organizationCode, algorithm)
            if obj2.get('code') == 200:
                return obj2.get('data').get('certId')
    else:
        ###TODO 查询其他状态的证书，只能通过页面接口
        obj1 = ""
        # if obj1.get('data').get('totalPage') > 0:
        #     return obj1.get('data').get('list')[0].get('sealId')
    return None

def openapiCreateOrganizationCerts(organizationCode, algorithm=None):
    """
    创建企业证书，默认是rsa证书
    :param organizationCode:
    :param algorithm: 证书算法类型 1-RSA 2-SM2 默认1
    :return:
    """
    if algorithm == None:
        algorithm = 1
    obj1 = {
              "algorithm": algorithm,
              "certName": "",
              "customOrgNo": "",
              "organizationCode": organizationCode
            }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationCerts/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('openapiCreateOrganizationCerts-[request]:', obj1)
    print('openapiCreateOrganizationCerts-[respone]:', json_response)
    return json_response

def openapiListOrganizationCerts(organizationCode, algorithm=None):
    """
    查询企业证书，默认是rsa证书
    :param organizationCode:
    :param algorithm: 证书算法类型 1-RSA 2-SM2 默认1
    :return:
    """
    if algorithm == None:
        algorithm = 1
    obj1 = {
            "algorithm": algorithm,
            "certPattern": "",
            "certSN": "",
            "customOrgNo": "",
            "keySNList": [],
            "organizationCode": organizationCode
            }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationCerts/list',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('openapiListOrganizationCerts-[request]:', obj1)
    print('openapiListOrganizationCerts-[respone]:', json_response)
    return json_response

def getOrganizationSealsByStatus(organizationCode, sealStatus=None, sealPattern=None, sealTypeCode=None, headers=None):
    """
    默认查询企业的发布状态的印章信息
    :param userCode:
    :param sealPattern:
    :return:
    """
    # sealStatus: 页面印章状态(1草稿 g发布 h停用 5吊销)， openapi企业印章状态： 1-待发布 2-已发布 3-已停用 4-已吊销
    if sealStatus == None:
        sealStatus = 2
    if sealStatus == 2:
        # 先查印章是否已经存在，如是已经存在则返回已有的印章信息
        obj0 = listOrganizationSeals(organizationCode, "", sealPattern, sealTypeCode)
        if obj0.get('data').get('total') > 0:
            return  obj0.get('data').get('records')[0].get('sealId')
        else: #若是查不到有效的，则去创建新的已发布的印章
            obj1 = createOrgSeals(organizationCode, sealPattern, "", 1, USER01)
            return obj1.get('data').get('sealInfos')[0].get('sealId')
    else:
        if sealStatus == 3:
            sealStatus = "h"
        if sealStatus == 4:
            sealStatus = 5
        obj2 = pageElectronicSealGroupList(headers, "", sealTypeCode, organizationCode, sealPattern,
                                    sealStatus)
        sealGroupId = obj2.get('data').get('list')[0].get('sealGroupId')
        obj3 = pageElectronicSealList(headers, sealGroupId, sealPattern, "", sealStatus)
        return obj3.get('data').get('list')[0].get('remoteSealId')

    return None

def createOrgSeals(organizationCode,sealPattern=None,legalSealId=None,sealRelease=None,userCode=None,sealTypeCode=None,sealName=None):
    """
    创建企业印章
    :param userCode:
    :param organizationCode:
    :param sealPattern:
    :param legalSealId:
    :param sealRelease:
    :param needSeals: #是否需要创建印章，不创建印章就是创建一个空分组
    :return:
    """
    if legalSealId is None:
        if sealTypeCode is None:
            sealTypeCode = "COMMON-SEAL"
        else:
            sealTypeCode = sealTypeCode
        sealGroupName = "全链路制章公章（勿动）"
    else:
        sealTypeCode = "LEGAL-PERSON-SEAL"
        sealGroupName = "全链路制章法人章（勿动）"
    if userCode is None or userCode == "":
        userCode = ENV('csqs.userCode')
    if sealRelease is None:
        sealRelease = 1 #制作后是否直接发布 0否，1是
    if sealPattern is None:
        sealPattern = 1 #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章
    if sealName is None:
        sealName = "印章_"+str(get_randomNo_32())
    obj1 = {
          "sealTypeCode": sealTypeCode,
          "sealRelease": sealRelease,
          "organizationCode": organizationCode,
          "sealGroupName": sealGroupName,
          "userCode": userCode,
          "sealInfos": [{
              "sealName": sealName,
              "sealPattern": sealPattern,
              "legalSealId": legalSealId
          }]
    }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('createOrgSeals-[request]:', obj1)
    print('createOrgSeals-[respone]:', json_response)
    return json_response

def createGroupNoSealWF(organizationCode,sealGroupName,headers):
    """
    Webapi接口创建印章空分组
    """
    createSealTypeWorkFlow(headers)
    obj1 = {
    "params":{
        "sealGroupConfig":{
            "organizationCode": organizationCode,
            "organizationName": "",
            "sealTypeCode": 'WF-SEAL',
            "sealTypeName": "",
            "sealGroupId":"",
            "sealGroupName": sealGroupName,
            "sealGroupDesc":"",
            "sealNumLimit":0,
            "sealTypeModelKey": "WDYT-YZZZ-DZ"
        },
        "sealConfigList":[ ],
        "autoPushSeal":"1",
        "draftOrProduction":"1",
        "requestUrl": MAIN_HOST + "/smc-seals-web/homeWorkFlow?workflowId=undefined&noWorkflowCodePageName=YKDZLC"
    },    "domain":"seal_system"}
    response = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/group/create',
                              json=obj1, headers=headers)
    json_response = response.json()
    print('createGroupNoSealWF-[request]:', obj1)
    print('createGroupNoSealWF-[respone]:', json_response)
    return json_response

def updateStatusOrgSeals(sealId, sealStatus):
    """
    更新企业印章的状态
    sealStatus:1-待发布 2-已发布 3-已停用 4-已吊销
    待发布状态印章可以变成已发布、已停用；已发布状态印章可以变成已停用；已停用状态印章可以变成已吊销；已吊销的印章状态不可变更
    """

    obj1 = { "sealId": sealId,
              "sealStatus": sealStatus }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/updateStatus',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('updateStatusOrgSeals-[request]:', obj1)
    print('updateStatusOrgSeals-[respone]:', json_response)
    return json_response

def sealssignersAll(sealId,sealsignersInfos=None):
    """
    授权电子印章的用印人
    :param sealId:
    :param sealsignersInfos:
    :return:
    """
    authorizationScope = 3
    if sealsignersInfos == None:
        authorizationScope = 2
    obj1 = {
          "authorizationScope": authorizationScope, #授权范围 2-授权所有用户 3-授权指定用户 默认为3
          "sealId": sealId,
          "sealsignerType": 1, #授权用印人类型 1-电子印章用印人 2-物理印章用印人 3-应急用印人 默认为1
          "sealsignersInfos": sealsignersInfos
        }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/sealsigners',
                             json=obj1, headers=headers)
    json_response = response.json()
    return json_response

def deleteOrgSeal(sealId):
    """
    删除企业印章（只能删除草稿态，待发布状态）
    """
    obj1 = {
          "sealId": sealId
        }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/delete',
                             json=obj1, headers=headers)
    json_response = response.json()
    return json_response

def listPageGroup(headers,groupName,sealTypeCode=None,sealStatus=None,sealPatterns=None,defaultSeal=None):
    """
    查询印章组信息
    """
    headers['navId'] = '1523545066692546562'
    if defaultSeal == None:
        defaultSeal = False
    obj1 = {"params":{"currPage":1,"pageSize":10,"remoteSealId":"",
                      "sealName":"","sealGroupName": groupName ,
                      "sealTypeCode":sealTypeCode,"sealStatus":sealStatus,
                      "sealPatterns":sealPatterns,"showChildOrganizeSeal":True,
                      "ukeySn":"","defaultSeal":defaultSeal},"domain":"seal_system"}
    response1 = requests.post(url=MAIN_HOST + '/seals/smc/seals/enterprise/group/page/list',
                              json=obj1, headers=headers)
    json_response = response1.json()
    print('listPageGroup-[request]:', obj1)
    print('listPageGroup-[respone]:', json_response)
    return json_response

def getGroupInfo(headers,groupName,organizationCode=None,userCode=None,wfGroup=None):
    """
    查询分组信息，若是没有对应的分组名称则新建空分组并查询
    """
    obj1 = listPageGroup(headers,groupName)
    if obj1.get('data').get('totalCount') > 0:
        return obj1.get('data').get('list')[0]
    else:
        if userCode==None:
            userCode = ENV('csqs.userCode')
        if wfGroup:
            createGroupNoSealWF(organizationCode, groupName, headers)
        else:
            createOrgSeals(organizationCode, "", "", "", userCode,  False,groupName)
        obj2 = listPageGroup(headers, groupName)
        if obj2.get('data').get('totalCount') > 0:
            return obj2.get('data').get('list')[0]

def createSealType(headers,sealTypeName, sealTypeCode):
    """
    创建一个固定的带有流程引擎的印章类型
    """
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/saveSealType"
    obj1 = {"params":{"id":"","sealTypeName": sealTypeName,"sealTypeCode":sealTypeCode,"sealTypeStatus":"0"},"domain":"seal_system"}
    res = requests.post(url=Url, headers=headers, json=obj1)
    json_response = res.json()
    print('createSealType-[request]:', obj1)
    print('createSealType-[respone]:', json_response)
    return json_response

def saveFlowInfoDetail(headers,sealTypeId, modelKey):
    """
    更新印章类型（可以带有审批流程）
    """
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/saveFlowInfoDetail"
    obj1 = {"params":{"id": sealTypeId,"electronicModelKey": modelKey,"physicalModelKey":""},"domain":"seal_system"}
    res = requests.post(url=Url, headers=headers, json=obj1)
    json_response = res.json()
    print('saveFlowInfoDetail-[request]:', obj1)
    print('saveFlowInfoDetail-[respone]:', json_response)
    return json_response

def getFlowInfoList(headers):
    """
    创建印章类型的时候，查询的印章制作审批流程的信息
    """
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/getFlowInfoList"
    obj1 = {"params":{"businessClass":[1],"flowTypeCode":"WDYT-YZZZ"},"domain":"seal_system"}
    res = requests.post(url=Url, headers=headers, json=obj1)
    json_response = res.json()
    print('saveFlowInfoDetail-[request]:', obj1)
    print('saveFlowInfoDetail-[respone]:', json_response)
    return json_response

def getSmcSealTypeList(headers):
    """
    获取环境中已经存在的印章类型
    """
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/getSmcSealTypeList"
    obj1 = {"params":{"isShowPersonalSeal":"0"},"domain":"seal_system"}
    res = requests.post(url=Url, headers=headers, json=obj1)
    json_response = res.json()
    print('getSmcSealTypeList-[request]:', obj1)
    print('getSmcSealTypeList-[respone]:', json_response)
    return json_response

def createSealTypeWorkFlow(headers):
    """
    创建一个固定的带有流程引擎的印章类型
    """
    obj1 = getSmcSealTypeList(headers)
    sealTypeInfo = {}
    if len(obj1.get('data'))>0:
        for item in obj1.get('data'):
            if item.get('sealTypeName') == "自动化带流程引擎（勿动）":
                sealTypeInfo['id'] = item.get('id')
                sealTypeInfo['sealTypeCode'] = item.get('sealTypeCode')
                sealTypeInfo['sealTypeName'] = item.get('sealTypeName')
                sealTypeInfo['electronicModelKey'] = item.get('electronicModelKey')
            else:
                break
    if len(sealTypeInfo) == 0:
        obj2 = createSealType(headers,"自动化带流程引擎（勿动）", "WF-SEAL")
        sealTypeId = obj2.get('message')
        saveFlowInfoDetail(headers, sealTypeId, "WDYT-YZZZ-DZ")
        sealTypeInfo['id'] = sealTypeId
        sealTypeInfo['sealTypeCode'] = "WF-SEAL"
        sealTypeInfo['sealTypeName'] = "自动化带流程引擎（勿动）"
        sealTypeInfo['electronicModelKey'] = "WDYT-YZZZ-DZ"
    return sealTypeInfo

def getInnerOrgInfo(customOrgNo):
    """
    查询内部组织的详情
    :param customOrgNo: 指定企业账号
    :return:
    """
    obj = {"customOrgNo": customOrgNo}
    print('xxxxx',obj)
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/detail',
                              json=obj, headers=headers)
    json_response = response.json()
    return json_response

def certRevoke(certId):
    """
    吊销企业证书
    :param certId:
    :return:
    """
    obj = {"certId": certId}
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationCerts/certRevoke',
                              json=obj, headers=headers)
    json_response = response.json()
    return json_response

def userCertRevoke(certId):
    """
    吊销个人证书
    :param certId:
    :return:
    """
    obj = {"certId": certId}
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userCerts/certRevoke',
                              json=obj, headers=headers)
    json_response = response.json()
    return json_response

def certOrgAllRevoke(orgCode,algorithm):
    """
    清空某个企业下所有证书
    :param orgCode:  企业code
    :param algorithm: 1-rsa, 2-sm2
    :return:
    """
    res = openapiListOrganizationCerts(orgCode, algorithm)
    if res.get('code') == 200:
        certCount = len(res.get('data'))
        if certCount > 0:
            certInfos = res.get('data')
            while certInfos:
                item = certInfos.pop(0)
                certRevoke(item.get('certId'))
                sleep(3)
            certOrgAllRevoke(orgCode, algorithm)
        else:
            return None

def sealOrgAllRevoke(orgCode,sealPattern=None):
    """
    清空/停用某个企业下所有印章
    :param orgCode:
    :param sealPattern: 1-国际标准，3-中国标准
    :return:
    """
    if sealPattern is None:
        res = listOrganizationSeals(orgCode, "", 1, "", "")
        res1 = listOrganizationSeals(orgCode, "", 1, "LEGAL-PERSON-SEAL", "")
        res2 = listOrganizationSeals(orgCode, "", 3, "", "")
        res3 = listOrganizationSeals(orgCode, "", 3, "LEGAL-PERSON-SEAL", "")
    sealList = []
    sealInfos = []
    sealInfos2 = []
    if res.get('code') == 200:
        count = res.get('data').get('total')
        count2 = res1.get('data').get('total')
        if (count - count2) > 0:
            sealInfos = res.get('data').get('records')
    if res2.get('code') == 200:
        count3 = res2.get('data').get('total')
        count4 = res3.get('data').get('total')
        if (count3 - count4) > 0:
            sealInfos2 = res2.get('data').get('records')
    sealList = [*sealInfos, *sealInfos2]
    if sealList:
        for item in sealList:
            if item.get('sealTypeCode') != "LEGAL-PERSON-SEAL":
                sealId = item.get('sealId')
                updateStatusOrgSeals(sealId, 3) #更新印章状态从已发布-已停用
        sealOrgAllRevoke(orgCode)
    else:
        return None

def sealLegalAllRevoke(orgCode,sealPattern=None):
    """
    清空/停用某个企业下所有法人印章
    :param orgCode:
    :param sealPattern: 1-国际标准，3-中国标准
    :return:
    """
    res = listOrganizationSeals(orgCode, "", 1, "LEGAL-PERSON-SEAL", "")
    res1 = listOrganizationSeals(orgCode, "", 3, "LEGAL-PERSON-SEAL", "")
    sealList = []
    sealInfos = []
    sealInfos1 = []
    if res.get('code') == 200:
        count = res.get('data').get('total')
        if count > 0:
            sealInfos = res.get('data').get('records')
    if res1.get('code') == 200:
        count = res1.get('data').get('total')
        if count > 0:
            sealInfos1 = res1.get('data').get('records')
    sealList = [*sealInfos, *sealInfos1]
    if sealList:
        for item in sealList:
            sealId = item.get('sealId')
            updateStatusOrgSeals(sealId, 3) #更新印章状态从已发布-已停用
        sealLegalAllRevoke(orgCode)
    else:
        return None

def check_org_tosign(orgNo,sealPattern,isNeedCert,isNeedOrgSeal,isNeedLegalSeal):
    """
    检查用于签署的内部企业的基本数据情况
    :param orgNo:  需要检查的企业账号
    :param isNeedCert:  是否需要证书，需要则删除，需要则确保有，无则创建
    :param isNeedOrgSeal:  是否需要企业印章，不需要则删除，需要则确保有，无则创建
    :param isNeedLegalSeal: 是否需要法人印章，不需要则删除，需要则确保有，无则创建
    :return:
    """
    res1 = getInnerOrgInfo(orgNo)
    if res1.get('code') == 200:
        orgCode = res1.get('data')[0].get('organizationCode')
    else:
        print('企业账号查询异常： ',orgNo)
        return None
    if sealPattern==3:
        algorithm = 2
    else:
        algorithm =1
    ##第一步：判断企业证书的情况
    res2 = openapiListOrganizationCerts(orgCode, algorithm)
    if res2.get('code') == 200:
        certSM2Count = len(res2.get('data'))
    else:
        certSM2Count = 0
    if isNeedCert == True :
        if certSM2Count == 0:
            res3 = openapiCreateOrganizationCerts(orgCode, algorithm)
    if isNeedCert == False :
        ##企业无需证书，则需要删除原有的证书
        certOrgAllRevoke(orgCode,algorithm)
    ##第二步：判断企业印章的情况
    res5 = listOrganizationSeals(orgCode, None, sealPattern, None, None)
    res5_legal = listOrganizationSeals(orgCode, "", sealPattern, "LEGAL-PERSON-SEAL")
    if res5.get('code') == 200:
        sealOrgCount0 = res5.get('data').get('total')
        sealOrgCount1 = res5_legal.get('data').get('total')
        if sealOrgCount0 > sealOrgCount1:
            sealOrgCount = sealOrgCount0
        else:
            sealOrgCount = 0
    if isNeedOrgSeal == True:
        if sealOrgCount == 0:
            res6 = createOrgSeals(orgCode,sealPattern,None,None,None,None,None)
            sealId = res6.get('data').get('sealInfos')[0].get('sealId')
            sealssignersAll(sealId)
    else:
        #不需要企业印章，则清空企业印章信息（除法人章外）
        sealOrgAllRevoke(orgCode)
    ##第三步：判断企业法人印章的情况
    res8 = listOrganizationSeals(orgCode, None, sealPattern, "LEGAL-PERSON-SEAL", None)
    if res5.get('code') == 200:
        sealOrgCount = res8.get('data').get('total')
    else:
        sealOrgCount = 0
    if isNeedLegalSeal == True:
        if sealOrgCount == 0:
            legalUserCode = res1.get('data')[0].get('legalRepUserCode')
            if len(legalUserCode) > 0:
                res9 = listUserseals(legalUserCode, sealPattern)
                if res9.get('code') == 200:
                    if res9.get('data').get('total')>0:
                        legalSealId = res9.get('data').get('records')[0].get('sealId')
                    else:
                        legalSealId = createUserseals(legalUserCode,sealPattern)
                else:
                    print('法人的个人印章查询异常，请检查接口运行环境')
                    return None
            res10 = createOrgSeals(orgCode, sealPattern, legalSealId, None, None, None, None)
            sealId10 = res10.get('data').get('sealInfos')[0].get('sealId')
            sealssignersAll(sealId10)
            if res10.get('code') == 200:
                legalSealId0 = res10.get('data').get('sealInfos')[0].get('sealId')
                sealssignersAll(legalSealId0, None)
    else:
        # 不需要企业法人印章，则清空企业法人印章信息
        sealLegalAllRevoke(orgCode)

def getInnerUserInfo(customAccountNo):
    """
    查询内部用户的详情
    :param customAccountNo: 指定企业账号
    :return:
    """
    obj = {"customAccountNo": customAccountNo}
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj)}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/detail',
                              json=obj, headers=headers)
    json_response = response.json()
    return json_response

def certUserAllRevoke(userCode,algorithm):
    """
    清空个人所有证书
    :param userCode:
    :param algorithm: 1-rsa, 2-sm2
    :return:
    """
    res = listUsercerts(userCode, algorithm)
    if res.get('code') == 200:
        certCount = len(res.get('data'))
        if certCount > 0:
            certInfos = res.get('data')
            while certInfos:
                item = certInfos.pop(0)
                userCertRevoke(item.get('certId'))
                sleep(3)
            certUserAllRevoke(orgCode, algorithm)
        else:
            return None

def sealUserAllRevoke(userCode,sealPattern):
    """
    清空/停用某个用户下所有印章
    :param userCode:
    :param sealPattern: 1-国际标准，3-中国标准
    :return:
    """
    res = listUserseals(userCode, sealPattern)
    if res.get('code') == 200:
        count = res.get('data').get('total')
        if count > 0:
            sealInfos = res.get('data').get('records')
            for item in sealInfos:
                sealId = item.get('sealId')
                updateStatusUserSeals(sealId, 2) #更新印章状态从已发布-已停用
            sealUserAllRevoke(userCode,sealPattern)
        else:
            return None

def updateStatusUserSeals(sealId, sealStatus):
    """
    更新个人印章的状态
    sealStatus:1-待发布 2-已发布 3-已停用 4-已吊销
    待发布状态印章可以变成已发布、已停用；已发布状态印章可以变成已停用；已停用状态印章可以变成已吊销；已吊销的印章状态不可变更
    """

    obj1 = { "sealId": sealId,
              "sealStatus": sealStatus }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/userseals/updateStatus',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('updateStatusUserSeals-[request]:', obj1)
    print('updateStatusUserSeals-[respone]:', json_response)
    return json_response

def check_user_tosign(userNo,sealPattern,isNeedCert,isNeedSeal):
    """
    检查用户签署的账号的基本情况
    :param userNo:  检查的账号
    :param sealPattern:  需要国密形态还是商密形态
    :param isNeedCert:  是否需要证书
    :param isNeedSeal:  是否需要印章
    :return:
    """
    res1 = getInnerUserInfo(userNo)
    if res1.get('code') == 200:
        print('xxxx',res1)
        userCode = res1.get('data')[0].get('userCode')
    else:
        print('用户账号查询异常： ',userNo)
        return None
    if sealPattern==3:
        algorithm = 2
    else:
        algorithm =1
    ##第一步：判断企业证书的情况
    res2 = listUsercerts(userCode, algorithm)
    if res2.get('code') == 200:
        certSM2Count = len(res2.get('data'))
    else:
        certSM2Count = 0
    if isNeedCert == True :
        if certSM2Count == 0:
            res3 = createUsercerts(userCode, algorithm)
    if isNeedCert == False :
        ##无需证书，则需要删除原有的证书
        certUserAllRevoke(orgCode, algorithm)
    ##第二步：判断印章的情况
    res5 = listUserseals(userCode, sealPattern)
    if res5.get('code') == 200:
        sealUserCount = res5.get('data').get('total')
    else:
        sealUserCount = 0
    if isNeedSeal == True:
        if sealUserCount == 0:
            res6 = createUserseals(userCode,sealPattern)
    else:
        #不需要印章，则清空印章信息
        sealUserAllRevoke(userCode, sealPattern)

def create_org_seal(organizationCode,sealPattern=None,legalSealId=None,sealRelease=None,userCode=None,needSeals=None,sealGroupName=None):
    """
    创建企业印章
    :param userCode:
    :param organizationCode:
    :param sealPattern:
    :param legalSealId:
    :param sealRelease:
    :param needSeals: #是否需要创建印章，不创建印章就是创建一个空分组
    :return:
    """
    if legalSealId == "":
        sealTypeCode = "COMMON-SEAL"
        sealGroupName = "全链路制章公章（勿动）"
    else:
        sealTypeCode = "LEGAL-PERSON-SEAL"
        sealGroupName = "全链路制章法人章（勿动）"
    if userCode == None or userCode == "":
        userCode = ENV('csqs.userCode')
    if sealRelease == None:
        sealRelease = 1 #制作后是否直接发布 0否，1是
    if sealPattern == None:
        sealPattern = 1 #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章
    if needSeals == False:
        sealInfos = []
        sealGroupName = sealGroupName
    else:
        sealInfos = [{ "sealPattern": sealPattern, "legalSealId": legalSealId }]
    obj1 = {
          "sealTypeCode": sealTypeCode,
          "sealRelease": sealRelease,
          "organizationCode": organizationCode,
          "sealGroupName": sealGroupName,
          "userCode": userCode,
          "sealInfos": sealInfos
    }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('createOrgSeals-[request]:', obj1)
    print('createOrgSeals-[respone]:', json_response)
    return json_response

def gen_saveOrUpdatePersonalSeal_data(sealId,sealStatus,ownerCode,ownerName,ownerOrganizationCode,ownerOrganizationName,sealName):
    if sealName is None:
        sealName = random.randint(1000000000000000, ****************)
    saveOrUpdatePersonalSeal_data = {
        "params": {
            "id": sealId,
            "managerOrgCode": ownerOrganizationCode,
            "managerOrgName": ownerOrganizationName,
            "sealAngle": "0",
            "sealColour": "1",
            "sealDefinition": "20",
            "sealHeight": "10",
            "sealName": sealName,
            "sealBodyStructure": "1",
            "sealOpacity": "1",
            "sealScale": "0.5",
            "sealShape": "1",
            "sealSource": "2",
            "sealStatus": sealStatus,
            "sealUserName": ownerName,
            "sealWidth": "20",
            "ownerCode": ownerCode,
            "ownerName": ownerName,
            "ownerOrganizationName": ownerOrganizationName,
            "ownerOrganizationCode": ownerOrganizationCode,
            "stampRule": "0",
            "oldStyle": "2",
            "sealWidthPixels": "75",
            "sealHeightPixels": "75"
        },
        "domain": "seal_system"
    }
    return saveOrUpdatePersonalSeal_data

if __name__ == "__main__":
    MAIN_HOST = "http://tianyin6-stable.tsign.cn"
    OPENAPI_HOST = "http://tianyin6-stable.tsign.cn"
    PROJECT_ID = "1000000"
    signFlowId = "74545a36d239ce7c7c7dac5ffdfceca8"
    orgCode = "91e6283bd38243e18fbb667b73719f57"
    sealOrgAllRevoke(orgCode,1)