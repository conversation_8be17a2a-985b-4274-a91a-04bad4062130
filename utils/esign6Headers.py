from utils import esignToken as tokens
import os


def get_manage_header():
    """
    获取管理平台登录的header
    账号：admin
    """
    token = tokens.createManageToken(os.environ.get("manage.account"), os.environ.get("manage.password"))
    content_type = "application/json;charset=UTF-8"
    header = {
        "token": token,
        "Content-Type": content_type
    }
    return header

def gen_main_headers_navid(token,projectId, navid_key=None):
    """
    统一登录平台的headers
    :param navIdKey:
    :return: dict
    """
    headers = {"Content-Type": "application/json",
               "authorization": token,
               "X-timevale-project-id": projectId}
    if navid_key:
        headers['navId'] = os.environ.get(navid_key)
    return headers