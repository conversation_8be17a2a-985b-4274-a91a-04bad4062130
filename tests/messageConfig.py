"""
@author:ming<PERSON><PERSON>
@file:messageConfig.py
@time:2022/05/17
@desc: 消息全局共享常量文件，常量使用的正确姿势
"""


class Const(object):
    """
    常量类
    """

    class ConstError(TypeError):
        pass

    class ConstCaseError(ConstError):
        pass

    def __setattr__(self, name: str, value: any):
        """
        设置常量内置方法
        :param name: 常量名称
        :type name: str
        :param value: 常量值
        :type value: any
        """
        if name in self.__dict__:
            raise self.ConstError("已被赋值得常量不允许修改.%s" % name)
        if not name.isupper():
            raise self.ConstCaseError('常量名"%s"必须大写' % name)

        self.__dict__[name] = value


const = Const()
# sql语句定义

# 根据流程uuid获取流程
const.GET_PROCESS_BY_UUID = "select * from process where uuid = '{processUuid}'"

# 根据流程uuid获取流程扩展信息
const.GET_PROCESS_EXT_BY_UUID = "select * from process_ext where id = {processUuid}"

# 根据流程id和角色类型获取流程发起人
const.GET_PROCESS_INITIATOR = \
    "select * from process_actor actor left join process_signer signer on actor.id = signer.id " \
    "where actor.process_id = {processId} and actor.role_type = {roleType} and " \
    "actor.deleted = 0 order by signer.sign_model, signer.sign_order "

# 根据流程id获取流程快照配置
const.GET_PROCESS_CONFIG_BY_PROCESS_ID = "select * from process_config where process_id = {processId}"

# 根据业务类型id获取业务类型信息
const.GET_BUSINESS_BY_ID = "select * from business_type where id = {businessId}"

# 根据流程id获取流程参与人
const.GET_ACTOR_LIST_BY_PROCESS_ID = \
    "select actor.id as id, uuid, actor.process_id processId, user_code as userCode, " \
    "user_type as userType, user_name as userName, department_name as " \
    "departmentName, department_code as departmentCode, organize_code as " \
    "organizeCode, organize_name as organizeName, role_type as roleType, wf_task_id " \
    "as wfTaskId, status, actor.gmt_create as  gmtCreate, actor.gmt_modified as " \
    "gmtModified, is_auto_sign as isAutoSign, " \
    "sign_node as signNode, sign_order as signOrder, sign_model as signModel, " \
    "seal_type_id as sealTypeId, execute_status as executeStatus, approve_status as " \
    "approveStatus, legal_sign_flag as legalSignFlag, apply_id as applyId, " \
    "is_read_complete as isReadComplete, is_read_time as isReadTime, last_urge_time " \
    "as lastUrgeTime, read_status as readStatus, read_time as readTime, " \
    "receive_invite_flag as receiveInviteFlag, legal_seal_auth_flag as " \
    "legalSealAuthFlag from process_actor actor left join process_signer signer on " \
    "actor.id = signer.id and actor.process_id = signer.process_id where " \
    "actor.process_id = {processId} "

    # "select actor.id as id, uuid, actor.process_id processId, user_code as userCode, " \
    # "user_type as userType, user_name as userName, department_name as " \
    # "departmentName, department_code as departmentCode, organize_code as " \
    # "organizeCode, organize_name as organizeName, role_type as roleType, wf_task_id " \
    # "as wfTaskId, status, actor.gmt_create as  gmtCreate, actor.gmt_modified as " \
    # "gmtModified, is_auto_sign as isAutoSign, is_ukey_sign as isUkeySign, " \
    # "sign_node as signNode, sign_order as signOrder, sign_model as signModel, " \
    # "seal_type_id as sealTypeId, execute_status as executeStatus, approve_status as " \
    # "approveStatus, legal_sign_flag as legalSignFlag, apply_id as applyId, " \
    # "is_read_complete as isReadComplete, is_read_time as isReadTime, last_urge_time " \
    # "as lastUrgeTime, read_status as readStatus, read_time as readTime, " \
    # "receive_invite_flag as receiveInviteFlag, legal_seal_auth_flag as " \
    # "legalSealAuthFlag from process_actor actor left join process_signer signer on " \
    # "actor.id = signer.id and actor.process_id = signer.process_id where " \
    # "actor.process_id = {processId} "

# 查询作废流程对应的作废协议
const.GET_BY_INVALID_PROCESS_ID = "select * from process where id = {invalidId} limit 1"

# 查询用户信息
const.GET_USER_BY_CODE = \
    "select * from ec_user user left join ec_organization org on user.organization_id = org.id " \
    "where user.user_code = '{userCode}' and user.user_status in ('1','3') and user.deleted =0 and user.tenant_code =1000"

# 查询站内信
const.GET_NOTICE_BY_USER_CODE = \
    "select * from ec_notice where receive_user_code = '{userCode}' and receive_organization_code = '{orgCode}' " \
    "and notice_title = '{messageTypeDesc}' and notice_content like '%s'" % ('%%%s%%' % '{timestamp}')

# 签署角色类型
const.ACTOR_ROLE_TYPE = {
    "owner": 0,
    "signer": 1,
    "copyer": 2
}

# 消息类型
const.MESSAGE_TYPE = {
    "SIGN_INVITE": "邀请签署通知",
    "SIGN_SIGNER_SIGNED": "签署人签署完成通知",
    "SIGN_FLOW_FINISH": "签署流程完成通知",
    "SIGN_FLOW_OVERTIME": "签署流程过期通知",
    "SIGN_FLOW_EXPIRE_REMIND": "签署截止前通知",
    "SIGN_FLOW_REFUSE": "签署人拒签通知",
    "SIGN_FLOW_CANCEL": "签署流程作废通知",
    "SIGNED_FLOW_CANCEL": "签署流程作废通知"
}

const.SIGN_WEB_DOMAIN_URL = "https://test-esign6.tsign.cn/sign-manage-web"
const.SIGN_WEB_URL = "http://172.20.22.23:8122/sign-manage-web"
const.SIGN_PAGE = "/sign-page"
const.CHARS = [
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
    'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
    'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd',
    'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
    'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x',
    'y', 'z'
]

if __name__ == '__main__':
    pass
