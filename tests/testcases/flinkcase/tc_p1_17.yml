- config:
    name: "管理平台已有用户重置实名内外部混合OFD个人手绘印章签署"
    variables:
      tc17_innerUserName: "测试全链路十七内部用户"   #需要每次都重置实名
      tc17_innerUserMobile: "***********"  #内部用户手机号
      tc17_innerUserMobileCrypt: "${get_encrypt($tc17_innerUserMobile)}"
      tc17_licenseNumber:  "432427189209237216"
      tc17_licenseNumberCrypt: "${get_encrypt($tc17_licenseNumber)}"
      tc17_innerUserCode: "flinkp117Code"
      tc17_accountNumber: "flinkp117"
      tc17_innerUserTokenEnvKey: "flinkp117TokenKey"
      orgNameInnerParam: "esigntest混合云全链路一测试公司"
      cutsomOrgNoInnerParam: "ORG-FLINK-01"
      organizationNameOuter0: "esigntest混合云全链路外部一测试公司"
      cutsomOrgNoOuter0: "WORG-FLINK-01"
      tc17_outerUserName: "测试全链路十七外部用户"   #需要每次都重置实名
      tc17_outerUserMobile: "***********"  #相对方用户手机号
      tc17_outerUserMobileCrypt: "${get_encrypt($tc17_outerUserMobile)}"
      tc17_outerLicenseNumber: "43242718941202913x"    #身份证号
      tc17_outerLicenseNumberCrypt: "${get_encrypt($tc17_outerLicenseNumber)}"  #加密的身份证号
      tc17_outerUserCode: "flinkp117outerCode"
      tc17_outerAccountNumber: "flinkp117outer"
      tc17_ofdFileKey: "${ENV(ofdFileKey)}"
      signFlowSubject: "全链路自动化-ofd内部企业+外部个人-${getRandomNo()}"
      innerUserShouHuiSeal: "${ENV(erWeiMaFileKey)}"
      tc17outUserTokenParam: "tc17outUserTokenParam"



- test:
    name: "setup-查询企业'esigntest混合云全链路一测试公司'的信息"
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    variables:
      apiOrganizationName: $orgNameInnerParam
      apiOrganizationTerritory: 1
    extract:
      - organizationIdInner17: content.data.0.id
      - organizationCodeInner17: content.data.0.organizationCode
      - organizationNameInner17: content.data.0.organizationName
      - parentOrganizationNameInner17: content.data.0.parentOrganizationName
      - parentOrganizationIdInner17: content.data.0.parentOrganizationId
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - gt: [ content.data.0.organizationCode, "1" ]
      - eq: [ content.data.0.organizationTerritory, "1" ]
      - eq: [ content.data.0.organizationType, "1" ]

- test:
    name: "TC-在管理平台上新建一个内部用户：测试全链路十七用户(所属企业为：esigntest混合云全链路一测试公司)"
    api: api/esignManage/OrgUser/user/saveUser.yml
    variables:
      userName: $tc17_innerUserName    #用户姓名不能为空   ^[a-zA-Z\u4e00-\u9fa5\s·]{226}$
      userType: 2          #用户类型不能为空    [12]用户类型(1管理员、2普通用户)
      userMobile: $tc17_innerUserMobileCrypt
      licenseType: 19          #$|[1][3789]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
      licenseNumber: $tc17_licenseNumberCrypt
      userStatus: 1        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
      userTerritory: 1  #用户组织类型不能为空   用户组织类型(1内部 2外部)
      userCode: $tc17_innerUserCode         #用户编码支持输入2-30字 不能为空
      accountNumber: $tc17_accountNumber  #账号支持输入2-30字     不能为空
      organizationId: $organizationIdInner17  #所属组织支持输入1-36字
      organizationCode: $organizationCodeInner17  #所属组织编码支持输入1-36字 所属组织编码不能为空

- test:
    name: "TC-查询内部用户：测试全链路十七内部用户，并重置实名"
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    variables:
      accountNumber: $tc17_accountNumber
      organizationId: $organizationIdInner17
    extract:
      - tc17_userIdInner: content.data.list.0.id
      - tc17_userCodeInner: content.data.list.0.userCode
      - tc17_userNameInner: content.data.list.0.userName
      - tc17_userMobileInner: content.data.list.0.userMobile
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ content.data.list.0.userCode, "" ]
      - gt: [ content.data.list.0.accountNumber, "1" ]
      - gt: [ content.data.list.0.userName, "1" ]
      - gt: [ content.data.list.0.organizationName, "1" ]
    teardown_hooks:
      - ${resetRealName($tc17_innerUserMobile)}

- test:
    name: "setup-查询外部企业：esigntest混合云全链路外部一测试公司"
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    variables:
      apiOrganizationName: $organizationNameOuter0
      apiOrganizationTerritory: 2
    extract:
      - tc17_organizationIdExternal: content.data.0.id
      - tc17_organizationCodeExternal: content.data.0.organizationCode
      - tc17_organizationNameExternal: content.data.0.organizationName
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - gt: [ content.data.0.organizationCode, "1" ]
      - eq: [ content.data.0.organizationTerritory, "2" ]
      - eq: [ content.data.0.organizationType, "1" ]

- test:
    name: "TC-在管理平台上新建一个外部用户：测试全链路十七外部用户"
    api: api/esignManage/OrgUser/user/saveUser.yml
    variables:
      userName: $tc17_outerUserName        #用户姓名不能为空
      userType: 2          #用户类型不能为空    [12]用户类型(1管理员、2普通用户)
      userMobile: $tc17_outerUserMobileCrypt
      licenseType: 19          #$|[1][3789]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
      licenseNumber: $tc17_outerLicenseNumberCrypt
      userStatus: 3        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
      userTerritory: 2  #用户组织类型不能为空   用户组织类型(1内部 2外部)
      userCode: $tc17_outerUserCode            #用户编码支持输入2-30字 不能为空
      accountNumber: $tc17_outerAccountNumber   #账号支持输入2-30字     不能为空
      organizationId: $tc17_organizationIdExternal   #所属组织支持输入1-36字
      organizationCode: $tc17_organizationCodeExternal  #所属组织编码支持输入1-36字 所属组织编码不能为空

- test:
    name: "TC-查询外部用户：测试全链路十七外部用户"
    api: api/esignManage/OrgUser/user/pageOutsideUserList.yml
    variables:
      accountNumber: $tc17_outerAccountNumber
      userName: ''
    extract:
      - tc17_userIdExternal: content.data.list.0.id
      - tc17_userCodeExternal: content.data.list.0.userCode
      - tc17_userNameExternal: content.data.list.0.userName
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ content.data.list.0.userCode, "" ]
      - gt: [ content.data.list.0.accountNumber, "1" ]
      - gt: [ content.data.list.0.userName, "1" ]
      - gt: [ content.data.list.0.organizationName, "1" ]
    teardown_hooks:
      - ${resetRealName($tc17_outerUserMobile)}

- test:
    name: "setup-创建这个场景专用的业务模板:签署区设置为签署区不必签和不可移动，支持手绘"
    testcase: common/docs/webapiCreatebusinessPreset.yml
    variables:
      presetName: "P1-17全链路业务模板-${getDateTime()"
      fileFormat: 2 #1-PFD;2-OFD
      signAreaSignEnable: 0 #1-必签；0-非必签
      signAreaMoveEnable: 0 #1-可移动；0-不可移动
      signEndTimeEnable: 1
      forceReadingTime: 0
      downloadEnable: 0
      allowAddSigner: 1
      signerNodeList: null
    extract:
      - presetId00
      - businessTypeId00

- test:
    name: "TC-openapi发起指定关键字ofd顺序签署，签署方1：内部企业'esigntest混合云全链路一测试公司'，经办人内部用户'测试全链路十七内部用户'，签署方2：外部个人，businessTypeCode设置为签署区不必签和不可移动，支持手绘"
    api: api/esignSigns/signFlow/createAndStartFlink.yml
    variables:
       subject: $signFlowSubject
       businessTypeCode: $businessTypeId00
       fileKey0: $tc17_ofdFileKey
       signerInfos: [
         {
           "sealInfos": [
             {
               "fileKey": $fileKey0 ,
               "signConfigs": [
                 {
                   "pageNo": "1-4",
                   "signType": "KEYWORD-SIGN",
                   "handEnable": 1,
                   "keywordInfo": {
                     "keyword": "劳动合同",
                     "keywordIndex": "",
                     "offsetPosY": ""
                   },
                   "signatureType": "PERSON-SEAL"
                 },
                 {
                   "pageNo": "5-8",
                   "signType": "KEYWORD-SIGN",
                   "keywordInfo": {
                     "keyword": "劳动合同",
                     "keywordIndex": "",
                     "offsetPosY": ""
                   },
                   "signatureType": "COMMON-SEAL"
                 }

               ]
             }
           ],
           "signNode": 1,
           "signMode": 0,
           "userType": 1,
           "userCode": $tc17_userCodeInner,
           "organizationCode": $organizationCodeInner17
         },
         {
           "sealInfos": [
             {
               "fileKey": $fileKey0 ,
               "signConfigs": [
                 {
                   "pageNo": "9-30",
                   "signType": "KEYWORD-SIGN",
                   "keywordInfo": {
                     "keyword": "劳动合同",
                     "keywordIndex": "",
                     "offsetPosY": ""
                   },
                   "signatureType": "PERSON-SEAL"
                 }
               ]
             }
           ],
           "signNode": 2,
           "signMode": 0,
           "userType": 2,
           "userCode": $tc17_userCodeExternal
         }
       ]
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.signFlowId, "" ]
      - contains: [ content.data.signFiles.0, "fileKey" ]
      - contains: [ content.data.signUrlInfos.0, "signUrlShort" ]
      - ne: [ content.data.signUrlInfos.0.userCode, "" ]
      - contains: [ content.data.signUrlInfos.0, "signMode" ]

- test:
    name: "setup-通过电子签署列表查询所发起的流程的流程信息"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $signFlowSubject  #批量发起的流程名称已设置为业务模板名称
    extract:
      - tc17_signFlowId: content.data.list.0.processId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.data.total,1 ]
      - contains: [ content.data.list.0,'flowId' ]
      - eq: [ content.data.list.0.flowStatus, '2' ] #签署中
      - eq: [ content.data.list.0.signStatus, '1' ] #签署中
      - eq: [ content.data.list.0.flowStatusName, "签署中" ]

#签署,通过邮件签署链接访问签署页，点击【去实名】按钮进行实名和授权
- test:
    name: "TC-签署节点1-签署方1-个人和企业实名并授权"
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      tmp0: { "mobile": $tc17_innerUserMobile,"orgName": $orgNameInnerParam }
      tmp1: '${getCloudRealNameAndAuth(4,$tmp0)}'  #企业经办人实名并授权
      accountOid: tmp1[0]
      orgOid: tmp1[1]
      mobile: $tc17_innerUserMobile
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message, '成功' ]
      - ne: [ content.data, '' ]

- test:
    name: "TC-创建内部个人:测试全链路十七内部用户 的sm2证书"
    api: api/esignSeals/personal/savePersonalCert.yml
    variables:
      certName: $tc17_innerUserName
      userCode: $tc17_userCodeInner
      userName: $tc17_userNameInner
      certAlgorithm: 2
    extract:
      - userCerIdSm2: content.data
    validate:
      - eq: [ content.status,200 ]
#      - contains: [ content.message, "成功" ]
      - len_eq: [ content.data, 19 ]

- test:
    name: "TC-签署节点1-签署方1-登录token+查询流程的印章列表"
    api: api/esignSigns/seals/list.yml
    variables:
      authorization0: '${getVerCodeToken($tc17_innerUserMobile)}'
      tmp0: ${putTempEnv($tc17_innerUserTokenEnvKey, $authorization0)} #添加变量到env
      processId: $tc17_signFlowId
      organizeCode: $organizationCodeInner17
    extract:
#      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
      - innerOrgSealId0: content.data.officialSeal.seals.0.sealId
      - innerOrgSealCode0: content.data.officialSeal.seals.0.sealTypeCode
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]

- test:
    name: "TC-签署节点1-签署方1-添加签署密码"
    api: api/esignPortal/user/addSignPwd.yml
    variables:
      authorization0: ${ENV($tc17_innerUserTokenEnvKey)}
    validate:
      - gt: [ content.status,1 ]
      - contains: [ content,'success' ]
      - contains: [ content,'message' ]
      - contains: [ content,'data' ]

- test:
    name: "TC-签署节点1-签署方1-修改签署密码"
    api: api/esignPortal/user/updateSignPwdByOldPwd.yml
    variables:
      authorization0: ${ENV($tc17_innerUserTokenEnvKey)}
    validate:
      - gt: [ content.status,1 ]
      - contains: [ content,'success' ]
      - contains: [ content,'message' ]
      - contains: [ content,'data' ]

- test:
    name: "TC-签署节点1-签署方1-获取意愿"
    api: api/esignSigns/auth/willing.yml
    variables:
      authorization0: ${ENV($tc17_innerUserTokenEnvKey)}
      processId: $tc17_signFlowId
      organizeCode: $organizationCodeInner17
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: "TC-签署节点1-签署方1-获取签署流程详情"
    api: api/esignSigns/process/detail.yml
    variables:
      authorization0: ${ENV($tc17_innerUserTokenEnvKey)}
      processId: $tc17_signFlowId
      organizeCode: $organizationCodeInner17  #注意企业签署方的需要传organizeCode，否则detailData获取的是流程的
    extract:
      - detailData0: content.data
      - taskUUID0: content.data.docInfo.0.taskId
      - processDocId0: content.data.docInfo.0.id
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,成功 ]
      - ne: [ content.data,"" ]

- test:
    name: "TC-签署节点1-签署方1-进行账密意愿"
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,"成功" ]
      - eq: [ content.data,'' ]

- test:
    name: "TC-签署节点1-签署方1-签署"
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      authorization0: ${ENV($tc17_innerUserTokenEnvKey)}
      accountCode: $tc17_userCodeInner
      organizeCode: $organizationCodeInner17
      applyId: $willApplyIdInner0
      processUUId: $tc17_signFlowId
      tmpSealId: { "personSealId": $innerUserShouHuiSeal ,"orgSealId": $innerOrgSealId0,"legalSealId": "" ,"orgSealCode": $innerOrgSealCode0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData0, $tmpSealId)}
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - contains: [ content.data,'executeStatus' ]
      - contains: [ content.data,'flowStatus' ]

- test:
    name: "TC-签署节点2-相对方个人实名并授权"
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      tmp0: { "mobile": $tc17_outerUserMobile}
      tmp1: '${getCloudRealNameAndAuth(2,$tmp0)}'
      accountOid: tmp1[0]
      orgOid: tmp1[1]
      mobile: $tc17_outerUserMobile
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message, '成功' ]
      - ne: [ content.data, '' ]

- test:
    name: "TC-签署节点2-登录token+查询流程的印章列表（相对方个人签署方）"
    api: api/esignSigns/seals/list.yml
    variables:
      authorization0: '${getVerCodeToken($tc17_outerUserMobile,2)}'
      tmp0: ${putTempEnv($tc17outUserTokenParam, $authorization0)}
      processId: $tc17_signFlowId
#      organizeCode: $organizationCodeExternalTmp
    extract:
      - externalPsnSealId: content.data.personalSeal.seals.0.sealId
#      - externalOrgSealId: content.data.officialSeal.seals.0.sealId
#      - externalOrgSealCode: content.data.officialSeal.seals.0.sealTypeCode
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]

- test:
    name: "TC-签署节点2-获取意愿"
    api: api/esignSigns/auth/willing.yml
    variables:
      authorization0: '${ENV($tc17outUserTokenParam)}'
      processId: $tc17_signFlowId
    extract:
      - willUrl1: content.data.url
      - willApplyIdExternal1: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: "TC-签署节点2-签署方1-短信验证码意愿"
    api: api/esignSigns/process/detail.yml
    variables:
      authorization0: '${ENV($tc17outUserTokenParam)}'
      tmp1_0: { "mobile": $tc17_outerUserMobile,"applyId": $willApplyIdExternal1 }
      tmp1_1: '${getCloudWillScene(0,$tmp1_0)}'
#      organizeCode: $organizationCodeExternalTmp
      processId: $tc17_signFlowId
      organizeCode: $organizationCodeInner17
    extract:
      - detailData1: content.data
      - taskUUID1: content.data.docInfo.0.taskId
      - processDocId1: content.data.docInfo.0.id
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'processId' ]
      - contains: [ content.data,'docInfo' ]
      - contains: [ content.data,'signInfo' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]

#相对方个人签署
- test:
    name: "TC-签署节点2-签署"
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      authorization0: '${ENV($tc17outUserTokenParam)}'
      accountCode: $tc17_outerUserCode
      applyId: $willApplyIdExternal1
      tmpSealId: { "personSealId": $externalPsnSealId,"orgSealId": "","orgSealCode": "" }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData1, $tmpSealId)}
      processUUId: $tc17_signFlowId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2]
      - contains: [ content.data,'executeStatus' ]
      - contains: [ content.data,'flowStatus' ]

#文档验签
- test:
    name: "TC-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $tc17_signFlowId
    extract:
      - tc17_signedFileKey: content.data.signFiles.0.signedFileKey
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 2 ]

- test:
    name: "TC-openapi验签"
    api: api/esignSigns/signTools/verifySignatureFiles-api.yml
    variables:
      fileKey: $tc17_signedFileKey
    validate:
      - eq: [ content.code,200 ]
      - contains: [ content.data.validateResult,'签名有效' ]
      - len_gt: [ content.data.signatureInfos.0.cert,1 ]
