- config:
    name: "物理用印场景--都是发起方填写场景"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - fileKey0: ${ENV(fileKey)}
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode0: ${ENV(sign01.userCode)}
      - userName0: ${ENV(sign01.userName)}
      - userCodeInit: ${ENV(csqs.userCode)}
      - subject: '获取签署流程进展明细'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - commonTemplateName: "自动化pdf模板文件${get_randomNo()}"
      - presetName: "自动化业务模板${getDateTime()}"
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - autoTestDocUuid1: ${get_docConfig_type()}
      - businessTypeId_businessPresetList1: ${ENV(physicalPreset)}
      - batchTemplateInitiationName: "自动化页面发起物理用印${getDateTime()}"
      - initiatorOrganizeCode1: ${ENV(csqs.orgCode)}
      - initiatorOrganizeName1: ${ENV(csqs.orgName)}
      - initiatorUserName1: "测试签署"
      - initiatorDepartmentName1: ${ENV(csqs.orgName)}
      - initiatorDepartmentCode1: ${ENV(csqs.orgCode)}
      - physicalSealId: ${ENV(org01.physical.sealId)}
      - maimHost: ${ENV(esign.projectHost)}
      - templateIdCommon: ${getTemplateId(2,0,pdf)}
- test:
    name: 通过id查询模板信息
    api: api/esignDocs/businessPreset/list.yml
    variables:
        businessTypeId_businessPresetList: $businessTypeId_businessPresetList1
    extract:
      - presetId_physical_commonCase: content.data.list.0.presetId
      - businessTypeCode_physical_commonCase: content.data.list.0.businessTypeId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
#      - eq: [ content.data.list.0.presetName, $presetName_physical_commonCase ]
      - eq: [ content.data.total, 1 ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId_physical_commonCase
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      - params: {"physicalSeal":1,"createNewDraft":0}
    extract:
      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "setup-用户信息"
    api: api/esignDocs/user/getUserListByUserCodeName.yml
    variables:
      - userName: $userName0
    extract:
      - userId: content.data.userList.0.id
      - organizationId: content.data.userList.0.organizationId
      - organizationCode11: content.data.userList.0.organizationCode
      - getOrganizationName11: content.data.userList.0.companyName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取模板详情"
    api: api/esignDocs/template/owner/templateInfo.yml
    variables:
      - templateUuid: $templateIdCommon
      - version: 1
    extract:
      - templateNameCommon: content.data.templateName
      - templatefileKeyCommon: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    variables:
      {
        "params": {
#          "presetVersion": 24,
          "fileFormat": 1,
          "batchTemplateInitiationName": "$batchTemplateInitiationName",
          "batchTemplateInitiationUuid": "$batchTemplateInitiationUuid0",
          "excelFileKey": null,
          "initiatorOrganizeCode": $orgCode0,
          "initiatorOrganizeName": $orgName0,
          "initiatorUserName": "$userName0",
          "initiatorDepartmentName": null,
          "initiatorDepartmentCode": null,
          "businessPresetUuid": $presetId_physical_commonCase,
          "businessPresetSnapshotUuid": null,
          "wordList": [

          ],
          "saveSigners": null,
          "fillList": [

          ],
          "type": 3,
          "contentList": [

          ],
          "sort": 0,
          "sealReason": null,
          "physicsSealUserList": [

          ],
          "physicalSeal": 1,
          "remark": null,
          "signFlowExpireTime": null,
          "businessNo": null,
          "appendList": [

          ],
          "attachments": [

          ],
          "ccInfos": [

          ],
          "auditInfo": null,
          "clickStaging": 0
        }
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取businessPresetSnapshotUuid"
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid0
      }
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
#      - templateSnapshotId0: content.data.appendList.0.templateInfo.templateSnapshotId
      - businessPresetSnapshotUuid0: content.data.businessPresetDetail.presetSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#
##填写方下一步--staging
- test:
    name: "暂存任务"
    variables:
      {
        "params": {
          "fileFormat": 1,
          "batchTemplateInitiationName": "$batchTemplateInitiationName",
          "batchTemplateInitiationUuid": "$batchTemplateInitiationUuid0",
          "excelFileKey": null,
          "initiatorOrganizeCode": $orgCode0,
          "initiatorOrganizeName": $orgName0,
          "initiatorUserName": "$userName0",
          "initiatorDepartmentName": null,
          "initiatorDepartmentCode": null,
          "businessPresetUuid": $presetId_physical_commonCase,
          "businessPresetSnapshotUuid": $businessPresetSnapshotUuid0,
        "wordList": [

        ],
        "saveSigners": null,
        "signersList": [

        ],
        "fillList": [

        ],
        "type": 3,
        "contentList": [

        ],
        "sort": 0,
        "sealReason": "ddddd",
        "physicsSealUserList": [
          {
            "sealId": "$physicalSealId",
            "sealName": "物理章",
            #                "sealUrl": "$sealThumbnailFileKey",
            "sealOrgName": "$orgName0",
            "sealTypeName": "公章",
            "userCode": "$userCode0",
            "userName": "$userName0",
            "organizeCode": "$orgCode0",
            "organizeName": "$orgName0",
            "sealNum": 1,
            "printVerify": 0,
            "take": 0,
            "outerTimeStart": "2024-10-22 16:25:22",
            "outerTimeEnd": "2024-11-21 16:25:22",
            "outerPlace": "",
            "outerPlaceCoordinate": "",
            "takeout": 1
          }
        ],
        "physicalSeal": 1,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "fileKey": "$templatefileKeyCommon",
              "fileName": "",
              "templateId": "$templateIdCommon",
              "version": 1,
              "templateType": 1
                #                    "cryptoFileKey": "https://tianyin6-pre.tsign.cn/esign-docs/fileSystem/commonDownload?fileKey=$eeee52e0-792d-42f1-9536-4a8c0edec360$1527982783&sign=7c38a79dca532c886ccdb3a784834d97aa39e6edb8e1ff953ba7f7e8aed553fd"
            }
          }
        ],
        "attachments": [

        ],
        "ccInfos": [

        ],
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [

            ],
            "nextAssigneeList": [
                {
                    "nextAssignee": "$userCode0",
                    "nextAssigneeOrganizationCode": "$organizationCode11",
                    "nextAssigneeId": "$userId",
                    "nextAssigneeName": "$userName0",
                    "nextAssigneeOrganizationName": "$organizationCode11"
                }
            ],
            "nodeConfigCode": "YWLDSP",
            "requestUrl": "/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=WLYYSQ&batchTemplateInitiationUuid=$batchTemplateInitiationUuid0",
            "sendNotice": "0",
            "variables": {

            }
        },
        "needCheckSignArea": true,
        "clickStaging": 0
    }
}
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


#查询印章授权用印人信息
- test:
    name: "setup-查询印章授权用印人信息"
    api: api/esignDocs/user/getSealUser.yml
    variables:
      - sealId: $physicalSealId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - len_gt: [content.data,1]


- test:
    name: "TC7-从业务模板配置页获取模板详情"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $templateIdCommon
      - version: 1
    extract:
      - contentName1: content.data.contentList.0.contentName
      - contentName2: content.data.contentList.1.contentName
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]


- test:
    name: "获取businessPresetSnapshotUuid"
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid0
      }
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - templateSnapshotId0: content.data.appendList.0.templateInfo.templateSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "提交发起任务--成功"
    variables:
      batchTemplateInitiationName: $batchTemplateInitiationName
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid0
      initiatorOrganizeCode: $orgCode0
      initiatorOrganizeName: $orgName0
      initiatorDepartmentName: $orgName0
      initiatorDepartmentCode: $orgCode0
      businessPresetUuid: $presetId_physical_commonCase
      fileFormat: 1
      signersList: []
      physicalSeal: 1
      type: 3
      contentList: [{
                "contentName": $contentName1,
                "contentValue": "qqqq"
            },
            {
                "contentName": "$contentName2",
                "contentValue": "qqqq"
            }]
      sealReason: "物理用印"
      auditInfo: null
      physicsSealUserList: [
            {
                "sealId": "$physicalSealId",
                "sealName": "物理章",
#                "sealUrl": "$sealThumbnailFileKey",
                "sealOrgName": "$orgName0",
                "sealTypeName": "公章",
                "userCode": "$userCode0",
                "userName": "$userName0",
                "organizeCode": "$orgCode0",
                "organizeName": "$orgName0",
                "sealNum": 1,
                "printVerify": 0,
                "take": 0,
                "outerTimeStart": "2024-10-22 16:25:22",
                "outerTimeEnd": "2024-11-21 16:25:22",
                "outerPlace": "",
                "outerPlaceCoordinate": "",
                "takeout": 1
            }
        ]
      appendList: [
            {
                "appendType": 2,
                "templateInfo": {
                    "fileKey": "$templatefileKeyCommon",
                    "fileName": "",
                    "templateId":"$templateIdCommon",
                    "version": 1,
                    "templateType": 1,
                    "templateSnapshotId": "$templateSnapshotId0"
#                    "cryptoFileKey": "https://tianyin6-pre.tsign.cn/esign-docs/fileSystem/commonDownload?fileKey=$eeee52e0-792d-42f1-9536-4a8c0edec360$1527982783&sign=7c38a79dca532c886ccdb3a784834d97aa39e6edb8e1ff953ba7f7e8aed553fd"
                }
            }
        ]
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: 物理用印签署列表
    api:  api/esignDocs/physical/getDocFlowListsManage.yml
    variables:
      flowName: "$batchTemplateInitiationName"
    extract:
      - _flowId: content.data.list.0.flowId
      - _processId: content.data.list.0.processInstanceId
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ne: [ json.data.list.0.flowId, "" ]
      - eq: [ json.data.list.0.flowTypeName,  "物理用印申请" ]
      - eq: [ json.data.list.0.signStatus,  "0" ]
      - eq: [ json.data.list.0.initiatorUserName,  $userName0 ]
      - eq: [ json.data.list.0.currentHandler,  $userCode0 ]
      - len_gt: [ json.data.list.0.signerUserNameList, 0 ]
      - eq: [ json.data.list.0.flowStatus,  "2" ]
- test:
    name: 物理用印详情
    api:  api/esignDocs/physical/getDocFlowDetail.yml
    variables:
      flowId: "$_flowId"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.initiatorUserName, $userName0 ]
      - eq: [ content.data.initiatorUserCode, $userCode0 ]
      - eq: [ content.data.initiatorOrganizeName, $orgName0 ]
      - eq: [ content.data.physicsSealUserList.0.applyCount, 1 ]
      - eq: [ content.data.physicsSealUserList.0.organizeCode, $orgCode0 ]
      - eq: [ content.data.physicsSealUserList.0.organizeName, $orgName0 ]
      - eq: [ content.data.physicsSealUserList.0.sealOrgName, $orgName0]
      - ne: [ content.data.signedFileVOList.0.fileKey, "" ]

- test:
    name: "查询流程实例--物理用印审批"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $_processId
    extract:
      - taskIdCommon: content.data.nodeInstance.taskId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取流程实例处理信息"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
        processInstanceId: $_processId
    extract:
        processDefinitionKeyCommon: content.data.processDefinitionKey
        businessIdCommon: content.data.nodeInstance.businessId
        taskIdCommon: content.data.nodeInstance.taskId
        nodeConfigCodeCommon: content.data.nextNodeConfigList.0.nodeConfigCode
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "审核提交--下一环节处理人不能为空"
    api: api/esignDocs/flow/submitTaskForAudit.yml
    variables:
      auditOpinion: "自动化测试发起人审核提交"
      businessId: $businessIdCommon
      carbonCopyList: []
      nextAssigneeList: []
      processInstanceId: $_processId
      requestUrl: "$maimHost/doc-manage-web/home-workFlow?workflowId=$_processId&bussinessId=$businessIdCommon&noWorkflowCodePageName=FQDZQS"
      sendNotice: 0
      subBusinessId: $businessIdCommon
      todoTaskId: $taskIdCommon
      processDefinitionKey: $processDefinitionKeyCommon
      nodeConfigCode: $nodeConfigCodeCommon
    validate:
      - eq: [ "content.message","下一环节处理人不能为空" ]
      - eq: [ "content.status",1212036 ]
      - eq: [ "content.data",null ]

- test:
    name: "审核提交--成功"
    api: api/esignDocs/flow/submitTaskForAudit.yml
    variables:
      auditOpinion: "自动化测试发起人审核提交"
      businessId: $businessIdCommon
      carbonCopyList: []
      nextAssigneeList: [
        {
          "nextAssignee": "$userCode0",
          "nextAssigneeOrganizationCode": "$orgCode0"
        }
      ]
      processInstanceId: $_processId
      requestUrl: "$maimHost/doc-manage-web/home-workFlow?workflowId=$_processId&bussinessId=$businessIdCommon&noWorkflowCodePageName=WLYYSQ"
      sendNotice: 0
      subBusinessId: $businessIdCommon
      todoTaskId: $taskIdCommon
      processDefinitionKey: $processDefinitionKeyCommon
      nodeConfigCode: $nodeConfigCodeCommon
    teardown_hooks:
      - ${sleep(3)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",true ]

- test:
    name: 物理用印签署列表--印章管理员审批
    api:  api/esignDocs/physical/getDocFlowListsManage.yml
    variables:
      flowName: "$batchTemplateInitiationName"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ne: [ json.data.list.0.flowId, "" ]
      - eq: [ json.data.list.0.flowStatusName,  "印章管理员审批" ]
      - eq: [ json.data.list.0.signStatus,  "0" ]
      - eq: [ json.data.list.0.flowStatus,  "2" ]
      - eq: [ json.data.list.0.initiatorUserName,  $userName0 ]
      - eq: [ json.data.list.0.currentHandler,  $userCode0 ]
      - len_gt: [ json.data.list.0.signerUserNameList, 0 ]
      - eq: [ json.data.list.0.flowTypeName,  "物理用印申请" ]

- test:
    name: 物理用印签署列表--印章管理员审批--我参与的
    api:  api/esignDocs/physical/getDocFlowListsOwner.yml
    variables:
      flowName: "$batchTemplateInitiationName"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ne: [ json.data.list.0.flowId, "" ]
      - eq: [ json.data.list.0.flowStatusName,  "印章管理员审批" ]
      - eq: [ json.data.list.0.signStatus,  "0" ]
      - eq: [ json.data.list.0.flowStatus,  "2" ]
      - eq: [ json.data.list.0.initiatorUserName,  $userName0 ]
      - eq: [ json.data.list.0.currentHandler,  $userCode0 ]
      - len_gt: [ json.data.list.0.signerUserNameList, 0 ]
      - eq: [ json.data.list.0.flowTypeName,  "物理用印申请" ]

- test:
    name: "查询流程实例--内部个人--发起人审核环节"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $_processId
    extract:
      - taskIdCommon1: content.data.nodeInstance.taskId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

#- test:
#    name: "查询驳回环节列表"
#    api: api/esignDocs/flow/getTurnDownNodeList.yml
#    variables:
#      processInstanceId: $_processId
#    extract:
#      - nodeCodeCommon1: content.data.0.nodeCode
#      - distTodoTaskIdCommon: content.data.0.id
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
##驳回
#- test:
#    name: "审批驳回"
#    api: api/esignDocs/flow/turnDownNodeInstance.yml
#    variables:
#      businessId: $_flowId
#      nodeConfigCode: $nodeCodeCommon1
#      processInstanceId: $_processId
#      requestUrl: "${ENV(esign.projectHost)}/doc-manage-web/home-workFlow?workflowId=$_processId&bussinessId=$_flowId&noWorkflowCodePageName=$nodeCodeCommon1"
#      sendNotice: 1
#      distTodoTaskId: $distTodoTaskIdCommon
#      todoTaskId: $taskIdCommon1
#      auditOpinion: "自动化测试-审批驳回"
#    validate:
#      - eq: [status_code, 200]
#      - eq: [content.success, true]
#
#- test:
#    name: 物理用印签署列表--驳回到发起
#    api:  api/esignDocs/physical/getDocFlowListsManage.yml
#    variables:
#      flowName: "$batchTemplateInitiationName"
#    extract:
#      - _flowId: content.data.list.0.flowId
#      - _processId: content.data.list.0.processInstanceId
#    validate:
#      - eq: [ content.success, true ]
#      - eq: [ content.status, 200 ]
#      - ne: [ json.data.list.0.flowId, "" ]
#      - ne: [ json.data.list.0.initiatorTime, "" ]
#      - eq: [ json.data.list.0.flowStatusName,  "发起申请-驳回" ]
#      - eq: [ json.data.list.0.flowTypeName,  "物理用印申请" ]
#      - eq: [ json.data.list.0.signStatus,  "0" ]
#      - eq: [ json.data.list.0.flowStatus,  "1" ]
#      - eq: [ json.data.list.0.initiatorUserName,  $userName0 ]
#      - eq: [ json.data.list.0.currentHandler,  $userCode0 ]
#      - len_gt: [ json.data.list.0.signerUserNameList, 0 ]

