- config:
    name: "对申请人信息applyUserCode和applyOrganizationCode的校验"
    base_url: ${ENV(esign.gatewayHost)}

- test:
    name: applyUserCode传内部在职用户编码，applyAccountNo字段不传；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段不传，applyAccountNo传在职的内部用户账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ""
        applyAccountNo: ${ENV(sign01.accountNo)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段传在职的内部用户A编码，applyAccountNo字段传在职的内部用户B账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ${ENV(sign02.accountNo)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段传错误的用户编码，如123，applyAccountNo字段传在职的内部用户账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: "123"
        applyAccountNo: ${ENV(sign01.accountNo)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617007]
        - contains: [ "json.message", 'applyUserCode错误']
        #- len_gt: ["json.data",1]

- test:
    name: applyUserCode传内部离职用户编码，applyAccountNo传空字符串；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(userCode.dimission)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617007]
        - eq: [ "json.message", 'applyUserCode错误：cesfqrlz不存在或状态异常']
      #  - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode传申请人对应的所属组织编码，applyOrgNo不传；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode不传，applyOrgNo传申请人对应的所属组织账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ""
        applyOrgNo: ${ENV(sign01.main.orgNo)}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode不传，applyOrgNo传申请人对应的兼职组织账号
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ""
        applyOrgNo: ${ENV(sign01.JZ.orgNo)}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode传内部在职用户编码，applyAccountNo传“”；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段传在职的内部用户编码，applyAccountNo传该对应的用户账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ${ENV(sign01.accountNo)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段传“”，applyAccountNo字段传“”
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ""
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617005]
        - eq: [ "json.message", '申请人编码与申请人账号必填其一']
       # - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段不传，applyAccountNo字段不传；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ""
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617005]
        - eq: [ "json.message", '申请人编码与申请人账号必填其一']
      #  - len_gt: ["json.data",1]

- test:
    name: applyUserCode字段不传，applyAccountNo字段传活跃的外部用户账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ""
        applyAccountNo: ${ENV(wsignwb01.accountNo)}
        applyOrganizationCode: ${ENV(wsignwb01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617008]
        - eq: [ "json.message", 'applyAccountNo错误：wsignwb01不存在或状态异常']
        - eq: ["json.data",null]

- test:
    name: applyUserCode不传，applyAccountNo传内部离职用户账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ""
        applyAccountNo: ${ENV(userCode.dimission)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617008]
        - eq: [ "json.message", 'applyAccountNo错误：cesfqrlz不存在或状态异常']
        #- len_gt: ["json.data",1]

- test:
    name: applyUserCode传内部删除用户编码，applyAccountNo传空字符串；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(userCode.delete)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(orgCode.delete)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617007]
        - eq: [ "json.message", 'applyUserCode错误：cesqssc不存在或状态异常']
        #- len_gt: ["json.data",1]

#- test:
#    name: applyUserCode不传，applyAccountNo传内部删除用户账号
#    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
#    variables:
#        applyUserCode: ""
#        applyAccountNo: ${ENV(sign01.accountNo_offline)}
#        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
#
#    validate:
#        - eq: [ "json.code", 200]
#        - eq: [ "json.message", '成功']
#        - len_gt: ["json.data",1]

#- test:
#    name: applyUserCode传内部注销用户编码，applyAccountNo传空字符串；
#    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
#    variables:
#        applyUserCode: ${ENV(sign01.userCode_注销)}
#        applyAccountNo: ""
#        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
#
#    validate:
#        - eq: [ "json.code", 200]
#        - eq: [ "json.message", '成功']
#        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode传申请人对应的所属组织编码，applyOrgNo传申请人对应的所属组织账号；
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ${ENV(sign01.main.orgNo)}

    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode传申请人对应的所属组织编码_applyOrgNo传错误的组织账号
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: "723777379237986464"

    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode传非申请人对应的所属或兼职组织编码_applyOrgNo传申请人对应的组织账号
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign03.main.departCode)}
        applyOrgNo: ${ENV(sign03.main.departNo)}

    validate:
        - eq: [ "json.code", 1617011]
        - eq: [ "json.message", '申请人测试签署一(sign01)不在组织esigntest四级部门CI(719feae3403e496c97c7f6ed9a2184b4)内']
       # - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode传申请人对应的兼职组织编码_applyOrgNo不传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.JZ.departCode)}
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode字段不传，applyOrgNo字段不传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ""
        applyOrgNo: ""
    validate:
        - eq: [ "json.code", 1617006]
        - eq: [ "json.message", '申请人组织编码与申请人组织账号必填其一']
        #- len_gt: ["json.data",1]

- test:
    name: applyOrganizationCode字段传，applyOrgNo字段传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyAccountNo: ""
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        applyOrgNo: ${ENV(sign01.main.orgNo)}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]