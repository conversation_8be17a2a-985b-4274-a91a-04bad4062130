- config:
    name: "物理用印申请长businessNo校验"
    base_url: ${ENV(esign.gatewayHost)}

- test:
    name: 50个字符
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: "56527658364457934579345730457345903457034754387551"
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 20个字符
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: "05652765836445793457"
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]


- test:
    name: 长度等于192个字符
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: "565276583644579345793457304573459034570347543875512565276583644579345793457304573459034570347543875512565276583644579345793457304573459034570347543875512565276583644579345793457304573459034570347543875512565276583644579345793457304573459034570347543875512565276583644579345793457304573459034570347543875512"
    validate:
        - eq: [ "json.code", 1600017]
        - eq: [ "json.message", 'businessNo错误：第三方业务流程id长度不可超出191位']
        #- len_gt: ["json.data",1]

- test:
    name: 不传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 传空字符串
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: "  "
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: businessNo与已有流程重复
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        businessNo: "05652765836445793457"
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 根据第三方业务编号查询条件组合查询查询物理用印签署列表
    api:  api/esignDocs/physical/getDocFlowListsManage.yml
    variables:
      businessNo: "05652765836445793457"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ne: [ json.data.list.0.flowId, "" ]
      - eq: [ json.data.list.0.flowTypeName,  "物理用印申请" ]
      - eq: [ json.data.list.0.signStatus,  "1" ]
      - eq: [ json.data.list.0.flowStatus,  "3" ]
      - eq: [ json.data.list.0.flowStatusName,  "用印中" ]