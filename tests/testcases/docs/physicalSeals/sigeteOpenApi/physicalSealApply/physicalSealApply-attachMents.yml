- config:
    name: "[openapi]思格特发起物理用印和查看详情-附件场景"
    variables:
        docxFileKey: ${ENV(docxPageFileKey)}
        docFileKey: ${ENV(docPageFileKey)}
        jpgFileKey: ${ENV(fileKeyJpg)}
        jpegFileKey: ${ENV(jpegFileKey)}
        pdfFileKey: ${ENV(fileKey)}
        pdfFileKey2: ${ENV(1PageFileKey)}
        pdfFileKey3: ${ENV(2PageFileKey)}
        pngFileKey: ${ENV(pngPageFileKey)}
        ofdFileKey1: ${ENV(ofdFileKey)}
        zipFileKey1: ${ENV(zipFileKey)}

- test:
      name: "TC1-【physicalSealApplyapply】附件格式包含pdf/ofd/doc/docx/jpg/png/zip-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         attachments:
           [
             {
               "fileKey": $pdfFileKey
             },
             {
               "fileKey": $ofdFileKey1
             },
             {
               "fileKey": $docFileKey
             },
             {
               "fileKey": $docxFileKey
             },
             {
               "fileKey": $jpgFileKey
             },
             {
               "fileKey": $pngFileKey
             },
             {
               "fileKey": $zipFileKey1
             }
           ]
      extract:
        sealFlowId1: content.data.sealFlowId
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]

- test:
    name: "TC1-【physicalSealFlowDetail】【physicalSealApplyapply】附件格式包含pdf/ofd/doc/docx/jpg/png/zip-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId1
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.attachments.0.fileKey,$pdfFileKey]
      - len_gt: [ content.data.attachments.0.downloadUrl,10 ]
      - eq: [content.data.attachments.1.fileKey,$ofdFileKey1]
      - len_gt: [ content.data.attachments.1.downloadUrl,10 ]
      - eq: [content.data.attachments.2.fileKey,$docFileKey]
      - len_gt: [ content.data.attachments.2.downloadUrl,10 ]
      - eq: [content.data.attachments.3.fileKey,$docxFileKey]
      - len_gt: [ content.data.attachments.3.downloadUrl,10 ]
      - eq: [content.data.attachments.4.fileKey,$jpgFileKey]
      - len_gt: [ content.data.attachments.4.downloadUrl,10 ]
      - eq: [content.data.attachments.5.fileKey,$pngFileKey]
      - len_gt: [ content.data.attachments.5.downloadUrl,10 ]
      - eq: [ content.data.attachments.6.fileKey,$zipFileKey1 ]
      - len_gt: [ content.data.attachments.6.downloadUrl,10 ]

- test:
      name: "TC2-【physicalSealApplyapply】附件是空-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         attachments: []
      extract:
        sealFlowId2: content.data.sealFlowId
      validate:
        - eq: [content.message, 成功]
        - eq: [content.code, 200]
        - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC2-【physicalSealFlowDetail】附件是空-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId2
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.attachments,[] ]

- test:
      name: "TC4-【physicalSealApplyapply】一个pdf附件-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         attachments:
           [
             {
               "fileKey": $pdfFileKey
             }
             ]
      extract:
        sealFlowId4: content.data.sealFlowId
      validate:
            - eq: [ content.message, 成功 ]
            - eq: [ content.code, 200 ]

- test:
    name: "TC4-【physicalSealFlowDetail】一个pdf附件-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId4
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.attachments.0.fileKey,$pdfFileKey]
      - len_gt: [ content.data.attachments.0.downloadUrl,10 ]