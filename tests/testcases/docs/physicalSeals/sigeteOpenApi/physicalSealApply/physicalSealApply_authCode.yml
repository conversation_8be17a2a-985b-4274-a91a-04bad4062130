- config:
    name: "[openapi]思格特发起物理用印和详情-授权码和授权码可见角色"
    variables:
      pdfFileKey: ${ENV(fileKey)}
      pdfFileKey2: ${ENV(1PageFileKey)}

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为null,该流程授权码可见角色为“用印人”-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    extract:
      sealFlowId_authCode1: content.data.sealFlowId
      authCode_1: content.data.sealDetailsInfos.0.authCode
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - len_gt: [ content.data.sealDetailsInfos.0.authCode,1 ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为null,该流程授权码可见角色为“用印人”-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode1
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [content.data.sealDetailsInfos.0.authCode, $authCode_1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"] 

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为空,该流程授权码可见角色为“用印人”-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: ""
    extract:
      sealFlowId_authCode2: content.data.sealFlowId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - len_gt: [ content.data.sealDetailsInfos.0.authCode,1 ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为空,该流程授权码可见角色为“用印人”-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode2
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - len_gt: [content.data.sealDetailsInfos.0.authCode, 1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"]  

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为2,该流程授权码可见角色为“用印人”和“发起人”-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "2"
    extract:
      sealFlowId_authCode3: content.data.sealFlowId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - len_gt: [ content.data.sealDetailsInfos.0.authCode,1 ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为2,该流程授权码可见角色为“用印人”和“发起人”-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode3
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - len_gt: [content.data.sealDetailsInfos.0.authCode, 1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"]  


- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为1,2,该流程授权码可见角色为“用印人”和“发起人”-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "1,2"
    extract:
      sealFlowId_authCode4: content.data.sealFlowId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - len_gt: [ content.data.sealDetailsInfos.0.authCode,1 ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为1,2,该流程授权码可见角色为“用印人”和“发起人”-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode4
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - len_gt: [content.data.sealDetailsInfos.0.authCode, 1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"]  

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为1,2,3,该流程授权码可见角色为'用印人'、'发起人'和'其管理员'-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "1,2,3"
    extract:
      sealFlowId_authCode5: content.data.sealFlowId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - len_gt: [ content.data.sealDetailsInfos.0.authCode,1 ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为1,2,3,该流程授权码可见角色为'用印人'、'发起人'和'其管理员'-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode5
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - len_gt: [content.data.sealDetailsInfos.0.authCode, 1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"]  

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为1,1,2,3,3,该流程授权码可见角色为'用印人'、'发起人'和'其管理员'-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "1,1,2,3,3"
    extract:
      sealFlowId_authCode6: content.data.sealFlowId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - ne: [ content.data.sealDetailsInfos.0.authCode,null ]

- test:
    name: "TC11-【physicalSealFlowDetail】authCodeVisibleRoles为1,1,2,3,3,该流程授权码可见角色为'用印人'、'发起人'和'其管理员'-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId_authCode6
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - len_gt: [content.data.sealDetailsInfos.0.authCode, 1]
      - startswith: [content.data.sealDetailsInfos.0.sealUrl, "http"] 
      - startswith: [content.data.sealDetailsInfos.0.sealUrlShort, "http"]  

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为4-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "4"
    validate:
      - eq: [content.message, "authCodeVisibleRoles错误：参数值仅允许1-用印人、2-发起人、3-管理员"]
      - eq: [content.code, 1617052]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为1，2，3-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "1，2，3" #中文逗号
    validate:
      - eq: [content.message, "authCodeVisibleRoles错误：参数值仅允许1-用印人、2-发起人、3-管理员"]
      - eq: [content.code, 1617052]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】authCodeVisibleRoles为2,3,4-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       authCodeVisibleRoles_physicalSealApply: "2,3,4"
    validate:
      - eq: [content.message, "authCodeVisibleRoles错误：参数值仅允许1-用印人、2-发起人、3-管理员"]
      - eq: [content.code, 1617052]
      - eq: [content.data, null]

