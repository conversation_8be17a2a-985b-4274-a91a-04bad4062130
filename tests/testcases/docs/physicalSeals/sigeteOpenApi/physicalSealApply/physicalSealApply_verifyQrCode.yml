- config:
    name: "[openapi]思格特发起物理用印和详情-用印支持核验二维码，用印文件支持添加二维码"
    variables:
      pdfFileKey: ${ENV(fileKey)}
      pdfFileKey2: ${ENV(1PageFileKey)}
      ofdFileKey: ${ENV(ofdFileKey)}
      docxFileKey: ${ENV(docxPageFileKey)}
      docFileKey: ${ENV(docPageFileKey)}
      jpgFileKey: ${ENV(fileKeyJpg)}
      jpegFileKey: ${ENV(jpegFileKey)}
      pdfFileKey3: ${ENV(2PageFileKey)}
      pngFileKey: ${ENV(pngPageFileKey)}
      newQrWidth: 96
      newWatermarkPage: 3
      newWatermarkPosition: 1
      newQrPosX: 20
      newQrPosY: 20
      newQrFileInfos: {
                     "qrWidth": $newQrWidth, #二维码宽度：只支持正方形二维码，宽度、长度共用一个参数，默认96 ；支持10～149（PX）之间整数
                     "watermarkPage": $newWatermarkPage, #水印页数：1-首页 2-末页 3-所有页 默认为3
                     "watermarkPosition": $newWatermarkPosition, #水印位置：1-左上角  2-右上角 3-左下角 4-右下角 默认为1
                     "qrPosX": $newQrPosX,   #x偏移量：支持0-9999之间整数；默认为20
                     "qrPosY": $newQrPosY #y偏移量：支持0-9999之间整数；默认为20
      }

- test:
    name: "TC-【physicalSealApplyapply】1份pdf用印文件-有二维码水印-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: $newQrFileInfos
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】2份pdf用印文件-有二维码水印-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: $newQrFileInfos
        signFiles:  [
              {
                  "fileKey": $pdfFileKey
              },
              {
                  "fileKey": $pdfFileKey2
              }]
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]


- test:
    name: "TC-【physicalSealApplyapply】1份ofd用印文件-有二维码水印-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: $newQrFileInfos
        signfileKey: $ofdFileKey
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】用印文件格式包含doc_docx_png_jpg_jpeg-有二维码水印-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: $newQrFileInfos
        signFiles:
          [
            {
              "fileKey": $docxFileKey
            },
            {
              "fileKey": $docFileKey
            },
            {
              "fileKey": $jpgFileKey
            },
            {
              "fileKey": $jpegFileKey
            },
            {
              "fileKey": $pngFileKey
            }
          ]
    validate:
      - eq: [content.message, "二维码水印仅支持pdf/ofd文件"]
      - eq: [content.code, 1617050]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=0-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode='dndiwka'-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: "dndiwka"
    validate:
      - eq: [content.message, "verifyQrCode参数错误!"]
      - eq: [content.code, 1600015]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode不填写-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode:
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrFileInfos不填-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos:
    validate:
      - eq: [content.message, "verifyQrCode设置为true，二维码信息应必传"]
      - eq: [content.code, 1617049]
      - eq: [content.data, null]


- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrWidth=149-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": 149,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]


- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrWidth=100-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": 100,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]


#6.0.14.0-beta.1修改二维码大小为10-149px
- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrWidth=9-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": 9,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "qrWidth错误：二维码的宽度不能小于10"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrWidth=150-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": 150,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "qrWidth错误：二维码的宽度不能大于149"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrWidth为小数-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": 100.3,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "qrWidth参数错误!"]
      - eq: [content.code, 1600015]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPage=1(发起二维码水印只在首页)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": 1,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPage=2(发起二维码水印只在末页)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": 2,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPage=3(发起二维码水印在所有页)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": 3,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPage不填(默认=3，发起二维码水印在所有页)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPage填写错误-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage":1000000,
                     "watermarkPosition": $newWatermarkPosition,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "水印页数仅支持：1-首页 2-末页 3-所有页"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPosition=2(发起二维码水印位置右上角)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 2,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]


- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPosition=3(发起二维码水印位置左下角)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 3,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPosition=4(发起二维码水印位置右下角)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 4,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPosition不填写-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，watermarkPosition填写错误的值=100-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 100,
                     "qrPosX": $newQrPosX,
                     "qrPosY": $newQrPosY
      }
    validate:
      - eq: [content.message, "水印位置仅支持：1-左上角  2-右上角 3-左下角 4-右下角 "]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrPosX和qrPosY不填写（默认都是20）-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 1
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrPosX=99999,qrPosY=99999(偏移到右下角)-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 1,
                     "qrPosX": 99999,
                     "qrPosY": 99999
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrPosX=100,qrPosY=100-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 1,
                     "qrPosX": 100,
                     "qrPosY": 100
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrPosX=20,qrPosY=100-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {
                     "qrWidth": $newQrWidth,
                     "watermarkPage": $newWatermarkPage,
                     "watermarkPosition": 1,
                     "qrPosX": 20,
                     "qrPosY": 100
      }
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC-【physicalSealApplyapply】verifyQrCode=1，qrFileInfos传空（有水印，空对象默认水印字段都是默认值）-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        verifyQrCode: 1
        qrFileInfos: {}
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.code, 200]
      - ne: [content.data, null]