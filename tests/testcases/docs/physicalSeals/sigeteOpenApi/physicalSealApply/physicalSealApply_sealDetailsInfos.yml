- config:
    name: "[openapi]思格特发起物理用印和详情-sealDetailInfos信息校验"
    variables:
       sealIdE: ${ENV(sign01_orgSealIdDynamic)}
       sealId111: ${ENV(org01.physical.sealId)}
       sign01SealId: ${ENV(sign01.sealId)}
       orgSealRevoke: ${ENV(orgSealRevoke)}
       orgSealStop: ${ENV(orgSealStop)}
       orgSealDelete: ${ENV(orgSealDelete)}
       orgSealNoPublic: ${ENV(orgSealNoPublic)}
       ceswdzxzdhyhwgd1UserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
       ceswdzxzdhyhwgd1AccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
       ceswdzxzdhyhwgd1OrganizationCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
       thisBusinessNo: "autobusiness-${get_randomNo()}"


- test:
    name: "TC1-【physicalSealApplyapply】sealDetailInfos传空数组-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealDetailsInfos: []
    validate:
      - eq: [content.message, "sealDetailsInfos错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC2-【physicalSealApplyapply】sealDetailInfos不传-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealDetailsInfos:
    validate:
      - eq: [content.message, "sealDetailsInfos错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC3-【physicalSealApplyapply】sealId传企业印章_印章形态无物理印章-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $sealIdE
    validate:
      - contains: [content.message, "sealId错误:${ENV(sign01_orgSealIdDynamic)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC4-【physicalSealApplyapply】sealId传个人印章-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $sign01SealId
    validate:
      - eq: [content.message, "sealId错误:${ENV(sign01.sealId)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC4-【physicalSealApplyapply】sealId不传-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId:
    validate:
      - eq: [content.message, "sealId错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC5-【physicalSealApplyapply】sealId对应的印章已吊销-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $orgSealRevoke
    validate:
      - eq: [content.message, "sealId错误:${ENV(orgSealRevoke)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC6-【physicalSealApplyapply】sealId对应的印章已停用-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $orgSealStop
    validate:
      - eq: [content.message, "sealId错误:${ENV(orgSealStop)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC7-【physicalSealApplyapply】sealId对应的印章已删除-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $orgSealDelete
    validate:
      - eq: [content.message, "sealId错误:${ENV(orgSealDelete)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC8-【physicalSealApplyapply】sealId对应的印章草稿状态-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealId: $orgSealNoPublic
    validate:
      - eq: [content.message, "sealId错误:${ENV(orgSealNoPublic)}企业物理印章不存在或未发布"]
      - eq: [content.code, 1617033]
      - eq: [content.data, null]

- test:
    name: "TC9-【physicalSealApplyapply】sealId对应的印章未授权"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        sealUserCode: $ceswdzxzdhyhwgd1UserCode
        sealOrganizationCode: $ceswdzxzdhyhwgd1OrganizationCode
        sealAccountNo: ""
        sealOrgNo: ""
    validate:
      - contains: [content.message, "用户测试文档中心自动化用户勿改动(${ENV(ceswdzxzdhyhwgd1.userCode)})不是企业章"]
      - eq: [content.code, 1617034]
      - eq: [content.data, null]

- test:
    name: "TC10-【physicalSealApplyapply】在职的内部用户sealUserCode_不传sealAccountNo-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealAccountNo:
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]


- test:
    name: "TC11-【physicalSealApplyapply】在职的内部用户sealUserCode_sealAccountNo传空字符串-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealAccountNo: ""
    extract:
      sealFlowId11: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]


- test:
    name: "TC11-【physicalSealFlowDetail】在职的内部用户sealUserCode_sealAccountNo传空字符串-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId11
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC12-【physicalSealApplyapply】不传sealUserCode_传在职的内部用户sealAccountNo-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode:
    extract:
      sealFlowId12: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC12-【physicalSealFlowDetail】不传sealUserCode_传在职的内部用户sealAccountNo-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId12
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]


- test:
    name: "TC13-【physicalSealApplyapply】传在职的内部用户sealUserCode和另一个在职内部用户的sealAccountNo-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealAccountNo: $ceswdzxzdhyhwgd1AccountNo
    extract:
      sealFlowId13: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC13-【physicalSealFlowDetail】传在职的内部用户sealUserCode和另一个在职内部用户的sealAccountNo-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId13
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC14-【physicalSealApplyapply】传错误的sealUserCode和在职内部用户的sealAccountNo-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: "2882922wa"
    validate:
      - eq: [ content.message, "sealUserCode错误：2882922wa不存在或状态异常" ]
      - eq: [ content.code, 1617024 ]
      - eq: [ content.data, null ]

- test:
    name: "TC15-【physicalSealApplyapply】传错误的sealUserCode和错误的sealAccountNo-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: "2882922wa"
       sealAccountNo: "eeue2ue2e2"
    validate:
      - eq: [ content.message, "sealUserCode错误：2882922wa不存在或状态异常" ]
      - eq: [ content.code, 1617024 ]
      - eq: [ content.data, null ]

- test:
    name: "TC16-【physicalSealApplyapply】applyUserCode和applyAccountNo都为空字符串-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: ""
       sealAccountNo: ""
    validate:
      - eq: [ content.message, "用印人编码与用印人账号必填其一" ]
      - eq: [ content.code, 1617022 ]
      - eq: [ content.data, null ]

- test:
    name: "TC17-【physicalSealApplyapply】applyUserCode和applyAccountNo都不传-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode:
       sealAccountNo:
    validate:
      - eq: [ content.message, "用印人编码与用印人账号必填其一" ]
      - eq: [ content.code, 1617022 ]
      - eq: [ content.data, null ]

- test:
    name: "TC18-【physicalSealApplyapply】传离职的内部用户sealUserCode-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: "${ENV(userCode.dimission)}"
       sealAccountNo:
    validate:
      - eq: [ content.message, "sealUserCode错误：${ENV(userCode.dimission)}不存在或状态异常" ]
      - eq: [ content.code, 1617024 ]
      - eq: [ content.data, null ]


- test:
    name: "TC19-【physicalSealApplyapply】传删除的内部用户sealUserCode-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: "${ENV(userCode.delete)}"
       sealAccountNo:
    validate:
      - eq: [ content.message, "sealUserCode错误：${ENV(userCode.delete)}不存在或状态异常" ]
      - eq: [ content.code, 1617024 ]
      - eq: [ content.data, null ]

- test:
    name: "TC20-【physicalSealApplyapply】传活跃的外部用户sealUserCode-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       sealUserCode: "${ENV(wsignwb01.userCode)}"
       sealAccountNo:
    validate:
      - eq: [ content.message, "用印人${ENV(wsignwb01.userName)}(${ENV(wsignwb01.userCode)})不在组织${ENV(sign01.main.orgName)}(${ENV(sign01.main.orgCode)})内" ]
      - eq: [ content.code, 1617030 ]
      - eq: [ content.data, null ]

- test:
    name: "TC21-【physicalSealApplyapply】sealOrganizationCode传印章已授权用户的企业编号_sealOrgNo不传-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealOrgNo:
    extract:
      sealFlowId21: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC21-【physicalSealFlowDetail】sealOrganizationCode传印章已授权用户的企业编号_sealOrgNo不传-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId21
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC22-【physicalSealApplyapply】sealOrganizationCode不传_sealOrgNo传印章已授权用户的企业账号-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealOrganizationCode:
    extract:
      sealFlowId22: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC22-【physicalSealFlowDetail】sealOrganizationCode不传_sealOrgNo传印章已授权用户的企业账号-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId22
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC23-【physicalSealApplyapply】sealOrganizationCode传印章已授权用户的企业编码_sealOrgNo传错误的组织账号-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealOrgNo: "testlalall0000"
    extract:
      sealFlowId23: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC23-【physicalSealFlowDetail】sealOrganizationCode传印章已授权用户的企业编码_sealOrgNo传错误的组织账号-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId21
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

#signjz01的兼职是sign01的所属企业，使用该帐号作为测试数据
- test:
    name: "TC24-【physicalSealApplyapply】sealOrganizationCode传印章未授权的企业编码_sealOrgNo传印章已授权用户的企业账号-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealUserCode: "${ENV(signjz01.userCode)}"
      sealAccountNo: ""
      sealOrganizationCode: "${ENV(sign01.JZ.orgCode)}"
      sealOrgNo: "${ENV(sign01.main.orgNo)}"
    validate:
      - contains: [content.message,"不是企业章"]
      - contains: [content.message,"的用印人"]
      - eq: [ content.code, 1617034 ]
      - eq: [ content.data, null ]


- test:
    name: "TC25-【physicalSealApplyapply】sealOrganizationCode传印章已授权用户的企业编码_sealOrgNo传印章未授权用户的企业账号-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealOrgNo: "${ENV(sign01.JZ.orgNo)}"
    extract:
      sealFlowId25: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC25-【physicalSealFlowDetail】sealOrganizationCode传印章已授权用户的企业编码_sealOrgNo传印章未授权用户的企业账号-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId25
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC26-【physicalSealApplyapply】sealOrganizationCode传已授权用户的兼职企业编码_sealOrgNo不传-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealUserCode: "${ENV(sign01.userCode)}"
      sealAccountNo:
      sealOrganizationCode: "${ENV(sign01.JZ.orgCode)}"
      sealOrgNo:
    extract:
      sealFlowId26: content.data.sealFlowId
    validate:
      - contains: [ content.message, "成功" ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

#- test:
#    name: "TC26-【physicalSealFlowDetail】sealOrganizationCode传已授权用户的兼职企业编码_sealOrgNo不传-查看详情"
#    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
#    variables:
#       sealFlowId: $sealFlowId26
#    validate:
#      - eq: [ content.message, 成功 ]
#      - eq: [ content.code, 200 ]
#      - ne: [ content.data, null ]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(signjz01.userCode)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(signjz01.accountNo)}"]


- test:
    name: "TC27-【physicalSealApplyapply】sealOrganizationCode不传_sealOrgNo传已授权的兼职企业账号-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealUserCode: "${ENV(sign01.userCode)}"
      sealAccountNo:
      sealOrganizationCode:
      sealOrgNo: "${ENV(sign01.JZ.orgNo)}"
      businessNo: $thisBusinessNo
    extract:
      sealFlowId27: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
#      - eq: [content.data.sealDetailsInfos.0.sealUserCode,"${ENV(signjz01.userCode)}"]
#      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(signjz01.accountNo)}"]

- test:
    name: "TC27-【physicalSealFlowDetail】sealOrganizationCode不传_sealOrgNo传已授权的兼职企业账号-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId27
       businessNo: $thisBusinessNo
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.JZ.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC27-【physicalSealFlowDetail】sealOrganizationCode不传_sealOrgNo传已授权的兼职企业账号-查看详情2"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId27
       businessNo: $thisBusinessNo
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.JZ.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]

- test:
    name: "TC27-【physicalSealFlowDetail】sealFlowId和businessNo都传但对应不同的流程-查看详情3"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId23
       businessNo: $thisBusinessNo
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
#      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}"]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}"]


- test:
    name: "TC27-【physicalSealFlowDetail】sealFlowId和businessNo都传但sealFlowId不存在-查看详情4"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: "2lllll200"
       businessNo: $thisBusinessNo
    validate:
      - eq: [ content.message, "物理用印流程不存在" ]
      - eq: [ content.code, 1617047 ]
      - eq: [content.data,null]

- test:
    name: "TC28-【physicalSealApplyapply】sealOrganizationCode传空字符串_sealOrgNo传空字符串-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealOrganizationCode: ""
      sealOrgNo: ""
    validate:
      - eq: [ content.message, "用印人组织编码与用印人组织账号必填其一" ]
      - eq: [ content.code, 1617023 ]
      - eq: [ content.data, null ]

- test:
    name: "TC29-【physicalSealApplyapply】sealOrganizationCode为部门类型-发起成功（6.0.8）"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealUserCode: "${ENV(sign03.userCode)}"
      sealAccountNo:
      sealOrganizationCode:
      sealOrgNo: "${ENV(sign03.main.departNo)}"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.code, 200 ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [ content.data.sealDetailsInfos.0.authCode,6 ]


- test:
    name: "TC29-【physicalSealApplyapply】用户所属组织为部门，sealOrganizationCode为该部门的最近一级企业"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealUserCode: "${ENV(sign03.userCode)}"
      sealAccountNo:
      sealOrganizationCode:
      sealOrgNo: "${ENV(sign03.main.departNo)}"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.code, 200 ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [ content.data.sealDetailsInfos.0.authCode,6 ]

- test:
    name: "TC30-【physicalSealApplyapply】2个用印申请明细-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealDetailsInfos: [
        {
          "sealId": $sealId111,
          "sealUserCode": $sealUserCode,
          "sealAccountNo": $sealAccountNo,
          "sealOrganizationCode": $sealOrganizationCode,
          "sealOrgNo": $sealOrgNo,
          "applyCount": $applyCount,
          "takeOut": $takeOut,
          "outAddress": $outAddress,
          "longitude": $longitude,
          "latitude": $latitude,
          "verifyFingerprint": $verifyFingerprint
        },
        {
          "sealId": $sealId111,
          "sealUserCode": "${ENV(signjz01.userCode)}",
          "sealAccountNo": "" ,
          "sealOrganizationCode": "",
          "sealOrgNo": "${ENV(sign01.main.orgNo)}",
          "applyCount": 2,
          "takeOut": 1,
          "outAddress": "测试自动化地址 001幢",
          "longitude": "121.340129",
          "latitude": "31.132186",
          "verifyFingerprint": 1
        }
      ]
    extract:
      sealFlowId30: content.data.sealFlowId
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrl,"http" ]
      - startswith: [ content.data.sealDetailsInfos.0.sealUrlShort,"http" ]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [ content.data.sealDetailsInfos.0.organizationCode,"${ENV(sign01.main.orgCode)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}" ]
      - eq: [content.data.sealDetailsInfos.1.organizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.1.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.1.sealUserCode,"${ENV(signjz01.userCode)}"]
#      - eq: [ content.data.sealDetailsInfos.1.sealAccountNo,"${ENV(signjz01.accountNo)}"]

- test:
    name: "TC30-【physicalSealFlowDetail】2个用印申请明细-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId30
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [content.data.flowBeginTime,null]
      - eq: [content.data.flowEndTime,null]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,"${ENV(sign01.main.orgCode)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealOrgNo,"${ENV(sign01.main.orgNo)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealUserCode,"${ENV(sign01.userCode)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealAccountNo,"${ENV(sign01.accountNo)}" ]
      - eq: [ content.data.sealDetailsInfos.0.sealId,$sealId111 ]
      - eq: [content.data.sealDetailsInfos.1.sealOrganizationCode,"${ENV(sign01.main.orgCode)}"]
      - eq: [ content.data.sealDetailsInfos.1.sealOrgNo,"${ENV(sign01.main.orgNo)}"]
      - eq: [content.data.sealDetailsInfos.1.sealUserCode,"${ENV(signjz01.userCode)}"]
#      - eq: [ content.data.sealDetailsInfos.1.sealAccountNo,"${ENV(signjz01.accountNo)}"]
      - eq: [ content.data.sealDetailsInfos.1.sealId,$sealId111 ]


- test:
    name: "TC29-【physicalSealApplyapply】sealDetailsInfos用印方信息重复-发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
      sealDetailsInfos: [
        {
          "sealId": $sealId111,
          "sealUserCode": "${ENV(signjz01.userCode)}",
          "sealAccountNo": "" ,
          "sealOrganizationCode": "",
          "sealOrgNo": "${ENV(sign01.main.orgNo)}",
          "applyCount": 2,
          "takeOut": 0,
          "outAddress": "测试自动化地址 001幢",
          "longitude": "121.340129",
          "latitude": "31.132186",
          "verifyFingerprint": 1
        },
        {
          "sealId": $sealId111,
          "sealUserCode": "${ENV(signjz01.userCode)}",
          "sealAccountNo": "" ,
          "sealOrganizationCode": "",
          "sealOrgNo": "${ENV(sign01.main.orgNo)}",
          "applyCount": 2,
          "takeOut": 1,
          "outAddress": "测试自动化地址 001幢",
          "longitude": "121.340129",
          "latitude": "31.132186",
          "verifyFingerprint": 1
        }
      ]
    validate:
      - eq: [ content.message, "用印人重复" ]
      - eq: [ content.code, 1617037 ]
      - eq: [ content.data, null ]