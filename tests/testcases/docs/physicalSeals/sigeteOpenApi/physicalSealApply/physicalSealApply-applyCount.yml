- config:
    name: "[openapi]思格特发起物理用印和查看详情-applyCount申请用印次数相关场景"

- test:
    name: "TC1-【physicalSealApplyapply】Count不传->发起失败，applyCount必填"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount:
    validate:
      - eq: [content.message, "applyCount错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC2-【physicalSealApplyapply】applyCount传50->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 50
    extract:
      sealFlowId2: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]

- test:
    name: "TC2-【physicalSealFlowDetail】applyCount传50查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId2
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.sealDetailsInfos.0.applyCount,50]

- test:
    name: "TC3-【physicalSealApplyapply】Count传1000->发起失败，applyCount支持1～999之间的整数"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 1000
    validate:
      - eq: [content.message, "applyCount错误：用印人(sign01)申请的用印次数无效，请输入 1～999 之间的次数"]
      - eq: [content.code, 1617046]
      - eq: [content.data, null]

- test:
    name: "TC4-【physicalSealApplyapply】Count传-10->发起失败，applyCount支持1～999之间的整数"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: -10
    validate:
      - eq: [content.message, "applyCount错误：用印人(sign01)申请的用印次数无效，请输入 1～999 之间的次数"]
      - eq: [content.code, 1617046]
      - eq: [content.data, null]

- test:
    name: "TC5-【physicalSealApplyapply】Count传0->发起失败，applyCount支持1～999之间的整数"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 0
    validate:
      - eq: [content.message, "applyCount错误：用印人(sign01)申请的用印次数无效，请输入 1～999 之间的次数"]
      - eq: [content.code, 1617046]
      - eq: [content.data, null]

- test:
    name: "TC6-【physicalSealApplyapply】applyCount传1->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 1
    extract:
      sealFlowId6: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]

- test:
    name: "TC2-【physicalSealFlowDetail】applyCount传1查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId6
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.sealDetailsInfos.0.applyCount,1]

- test:
    name: "TC7-【physicalSealApplyapply】applyCount传999->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 999
    extract:
      sealFlowId7: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]

- test:
    name: "TC7-【physicalSealFlowDetail】applyCount传999查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId7
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.sealDetailsInfos.0.applyCount,999]

- test:
    name: "TC8-【physicalSealApplyapply】applyCount传10.00->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 10.00
    validate:
      - eq: [content.message, "applyCount参数错误!"]
      - eq: [content.code, 1600015]
      - eq: [content.data,null]


- test:
    name: "TC9-【physicalSealApplyapply】applyCount传10.22->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: 10.22
    validate:
      - eq: [content.message, "applyCount参数错误!"]
      - eq: [content.code, 1600015]
      - eq: [content.data,null]

- test:
    name: "TC10-【physicalSealApplyapply】applyCount传英文字符串->发起失败，applyCount支持1～999之间的整数"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: "testakak"
    validate:
      - eq: [content.message, "applyCount参数错误!"]
      - eq: [content.code, 1600015]
      - eq: [content.data,null]


- test:
    name: "TC11-【physicalSealApplyapply】applyCount传空->发起失败，applyCount必填"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyCount: ""
    validate:
      - eq: [content.message, "applyCount错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data,null]