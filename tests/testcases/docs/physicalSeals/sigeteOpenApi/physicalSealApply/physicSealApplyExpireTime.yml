- config:
    name: "对用印过期时间的校验"
    base_url: ${ENV(esign.gatewayHost)}

- test:
    name: expireTime为过去时间
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: "2022-02-02 15:12:12"
    validate:
        - eq: [ "json.code", 1617002]
        - eq: [ "json.message", 'expireTime错误:用印截止时间不可早于当前时间']
        #- len_gt: ["json.data",1]

- test:
    name: expireTime正确的格式且大于当前时间
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: "2030-02-02 15:12:12"
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 为空字符串_不传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 格式正确_时间不存在
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: "2023-99-11 23:01:01"
    validate:
        - eq: [ "json.code", 1617001]
        - eq: [ "json.message", 'expireTime错误:用印截止时间格式异常']
        - eq: ["json.data",null]

- test:
    name: 不包含时分秒
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: "2023-09-11"
    validate:
        - eq: [ "json.code", 1617001]
        - eq: [ "json.message", 'expireTime错误:用印截止时间格式异常']
        #- len_gt: ["json.data",1]

- test:
    name: 时间格式不正确
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        expireTime: "465464646546"
    validate:
        - eq: [ "json.code", 1617001]
        - eq: [ "json.message", 'expireTime错误:用印截止时间格式异常']
        #- len_gt: ["json.data",1]