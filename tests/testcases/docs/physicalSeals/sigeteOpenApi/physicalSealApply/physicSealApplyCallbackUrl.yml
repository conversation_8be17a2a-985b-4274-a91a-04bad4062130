- config:
    name: "物理用印申请回调url长度校验"
    base_url: ${ENV(esign.gatewayHost)}

- test:
    name: 正确的url_长度小于等于1000字符
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        callBackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/test"
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: a正确的url_长度大于1000字符
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        callBackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/test/d0d2d070fd7626d8d84a4475bd974d5fa8a6dd7ce4d04d54cfc61a77d6ca9c23/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJlc2lnbl9sb2dpbiIsIm9yZ2FuaXphdGlvbk5hbWUiOiJlc2lnbnRlc3TmlofmoaPkuK3lv4Poh6rliqjljJbmtYvor5Xlhazlj7jli7_mlLnliqgiLCJ0ZW5hbnRDb2RlIjoiMTAwMCIsInVzZXJOYW1lIjoi5rWL6K-V5paH5qGj5Lit5b-D6Ieq5YqoYyW55So5oi35Yu_5pS55YqoIiwidXNlcklkIjoiMTUyNjAzODkwMDA0MjMxMzcyOSIsInVzZXJDb2RlIjoiY2Vzd2R6eHpkaHlod2dkMSIsInBsYXRmb3JtIjoicGMiLCJleHBpcmVkVGltZSI6MTY4NTk2MTAxNCwib3JnYW5pemF0aW9uSWQiOiIxNTI2MDMyODczOTM0NzYxOTg1Iiwib3JnYW5pemF0aW9uQ29kZSI6IjZhYzYyYzVmYjc2NjRjZGY5NWI4OTQ2OWRhYjhhZjJlIiwidXNlclRlcnJpdG9yeSI6IjEiLCJ1c2VyUm9sZSI6IjAiLCJleHAiOjE2ODU5NjEwMTQsImlhdCI6MTY4NTk1MzgxNH0.iFHm27MUrPEk5k8g6NkENcT9Ig-kk3WZHRTLW2xZ334GRhRZt7/VnwctRieXXcEnyIRc8s0a/O3GkaWmwEl7mt1QorsBbzoGcnvxpCKzsjhCGsaYqklLDyDQZSNP1PZTjk5DKAXagA/bP6W4gywTsWKVidyXoi+uP0BeDPfJ/HndqRBDhKrMFZPNdUxICs0O9VrqiA1gMisAKhabNj+xq2ILpLzs27XGsMKyaBOyR7F1ZSH1vkUYUhqVWfJK9+sUD3tMwnyPLwhhhFL2WlckEpbXGpclHLobPpw8Zw7bWD2rNKaWr/5JeIQRw5GkpuqTwvKCroxY3aoFzXDWuBJn4Hn8t+0Y90IV/6Bjms1XAGDKaFE+WRt0XOKwYCVSQkV3g=="
    validate:
        - eq: [ "json.code", 1600017]
        - eq: [ "json.message", 'callBackUrl错误：回调url路径长度不可超出1000位']
    #    - len_eq: ["json.data",null]

- test:
    name: 非url格式
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        callBackUrl: "x-timevale-project-id"
    validate:
        - eq: [ "json.code", 1617003]
        - eq: [ "json.message", 'callBackUrl错误:回调地址格式异常']
      #  - len_eq: ["json.data",null]

- test:
    name: url不传
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        #callBackUrl: ""
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 传空字符串
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        callBackUrl: "  "
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]