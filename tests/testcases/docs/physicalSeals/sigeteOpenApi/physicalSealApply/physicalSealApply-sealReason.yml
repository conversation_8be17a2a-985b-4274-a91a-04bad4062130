- config:
    name: "[openapi]思格特发起物理用印和查看详情-用印事由"

- test:
      name: "TC1-【physicalSealApplyapply】sealReason不传-发起失败"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason:
      validate:
        - eq: [content.message, "sealReason：必填项未填写"]
        - eq: [content.code, 1600017]
        - eq: [content.data, null]

- test:
      name: "TC2-【physicalSealApplyapply】sealReason传长度为20的字符串-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: "自动化测试测试自动化测试测试自动化测试测"
      extract:
        sealFlowId2: content.data.sealFlowId
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

- test:
    name: "TC2-【physicalSealFlowDetail】sealReason传长度为20的字符串-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId2
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - eq: [content.data.flowStatus,"3"]

- test:
      name: "TC3-【physicalSealApplyapply】sealReason传空字符串-发起失败"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: ""
      validate:
        - eq: [content.message, "sealReason：必填项未填写"]
        - eq: [content.code, 1600017]
        - eq: [content.data, null]

- test:
      name: "TC4-【physicalSealApplyapply】sealReason传长度为1的字符串-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: "自"
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

- test:
      name: "TC5-【physicalSealApplyapply】sealReason传长度为20的字符串_包含特殊字符-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: "自测试测试自动化测\/:*?\"<>|自动"
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

- test:
      name: "TC6-【physicalSealApplyapply】sealReason传长度100个字符串-发起成功"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: "自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测"
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

#todo 可以加一个页面的查看详情case
- test:
      name: "TC6-【physicalSealApplyapply】sealReason传长度201个字符串-发起失败"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
         sealReason: "2自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测"
      validate:
        - eq: [content.message, "sealReason错误：用印事由长度不可超出200位"]
        - eq: [content.code, 1600017]
        - eq: [content.data, null]