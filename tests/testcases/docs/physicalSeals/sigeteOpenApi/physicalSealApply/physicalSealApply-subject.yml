- config:
    name: "[openapi]思格特发起物理用印-subject（流程主题）"

- test:
    name: "TC1-subject输入100个以内的字符_中文_英文_非特殊字符"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       subject: "自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动ooo&》"
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]


- test:
    name: "TC2-subject输入101个字符"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       subject: "1自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测自动化测试测试自动化测试测试自动化测试测"
    validate:
      - eq: [content.message, "subject错误：流程主题长度不可超出100位"]
      - eq: [content.code, 1600017]
      - eq: [content.data,null]

- test:
    name: "TC3-subject输入空字符串"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       subject: ""
    validate:
      - eq: [content.message, "subject错误：必填项未填写"]
      - eq: [content.code, 1600017]
      - eq: [content.data,null]

- test:
    name: "TC4-subject输入包含特殊字符"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       subject: "自动化测试测试自动化测试测试自动化测\/:*?\"<>|"
    validate:
      - eq: [content.message, "subject错误:流程主题不支持特殊字符"]
      - eq: [content.code, 1600017]
      - eq: [content.data,null]
