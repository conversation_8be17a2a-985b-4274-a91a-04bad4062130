- config:
    name: "[openapi]思格特发起物理用印和查看详情-verifyFingerprint用例"
    #todo 这个场景可以加页面的详情

- test:
      name: "TC1-【physicalSealApplyapply】verifyFingerprint传1-发起成功，用印任务的校验指纹功能开启"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
        verifyFingerprint: 1
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

- test:
      name: "TC2-【physicalSealApplyapply】verifyFingerprint传2-发起成功，为true"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
        verifyFingerprint: 2
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]

- test:
      name: "TC3-【physicalSealApplyapply】verifyFingerprint不传-发起成功，默认关闭"
      api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
      variables:
        verifyFingerprint:
      validate:
        - eq: [ content.message, 成功 ]
        - eq: [ content.code, 200 ]
        - ne: [content.data, null]