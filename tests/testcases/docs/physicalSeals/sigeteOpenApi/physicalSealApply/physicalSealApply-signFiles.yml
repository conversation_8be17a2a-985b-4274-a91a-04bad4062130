- config:
    name: "[openapi]思格特发起物理用印和查看详情-用印文件场景"
    variables:
        docxFileKey: ${ENV(docxPageFileKey)}
        docFileKey: ${ENV(docPageFileKey)}
        jpgFileKey: ${ENV(fileKeyJpg)}
        jpegFileKey: ${ENV(jpegFileKey)}
        pdfFileKey: ${ENV(fileKey)}
        pdfFileKey2: ${ENV(1PageFileKey)}
        pdfFileKey3: ${ENV(2PageFileKey)}
        pngFileKey: ${ENV(pngPageFileKey)}
        applyUserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
        applyAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
        applyOrganizationCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
        applyOrgNo: ${ENV(ceswdzxzdhyhwgd1.orgNo)}
        sealUserCode: ${ENV(sign01.userCode)}
        sealAccountNo: ${ENV(sign01.accountNo)}
        sealOrganizationCode: ${ENV(sign01.main.orgCode)}
        sealOrgNo: ${ENV(sign01.main.orgNo)}

- test:
    name: "TC1-【physicalSealApplyapply】多个用印文件，支持的文件格式有pdf/doc/docx/jpg/jpeg/png-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $pdfFileKey
              },
              {
                  "fileKey": $docxFileKey
              },
              {
                  "fileKey": $docFileKey
              },
              {
                  "fileKey": $jpgFileKey
              },
              {
                  "fileKey": $jpegFileKey
              },
              {
                  "fileKey": $pngFileKey
              }
          ]
    extract:
      sealFlowId1: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC1-【physicalSealFlowDetail】多个用印文件，支持的文件格式有pdf/doc/docx/jpg/jpeg/png-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId1
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC2-【todo 这个改成文件格式大写】【physicalSealApplyapply】多个用印文件，支持的文件格式有大写的PDF/DOC/DOCX/JPG/JPEG/PNG-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $pdfFileKey
              },
              {
                  "fileKey": $docxFileKey
              },
              {
                  "fileKey": $docFileKey
              },
              {
                  "fileKey": $jpgFileKey
              },
              {
                  "fileKey": $jpegFileKey
              },
              {
                  "fileKey": $pngFileKey
              }
          ]
    extract:
      sealFlowId1: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]


- test:
    name: "TC4-【physicalSealApplyapply】一个用印文件-pdf格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $pdfFileKey
              }
          ]
    extract:
      sealFlowId4: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC4-【physicalSealFlowDetail】一个用印文件-pdf格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId4
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$pdfFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]

- test:
    name: "TC5-【physicalSealApplyapply】一个用印文件-doc格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $docFileKey
              }
          ]
    extract:
      sealFlowId5: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC5-【physicalSealFlowDetail】一个用印文件-doc格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId5
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$docFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]


- test:
    name: "TC6-【physicalSealApplyapply】一个用印文件-docx格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $docxFileKey
              }
          ]
    extract:
      sealFlowId6: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC6-【physicalSealFlowDetail】一个用印文件-docx格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId6
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$docxFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]

- test:
    name: "TC1-图片预览"
    api: api/esignDocs/fileSystem/physicalSignedFileImagePreview.yml
    variables:
      fileKey: $docxFileKey
      flowId: "$sealFlowId4"
#    extract:
#      signId2: content.data.sign
#      pageNo2: content.data.pageNumber
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, null]
      - contains: [content.message, "http"]


- test:
    name: "TC7-【physicalSealApplyapply】一个用印文件-jpg格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $jpgFileKey
              }
          ]
    extract:
      sealFlowId7: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC7-【physicalSealFlowDetail】一个用印文件-jpg格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId7
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$jpgFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]


- test:
    name: "TC8-【physicalSealApplyapply】一个用印文件-jpeg格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $jpegFileKey
              }
          ]
    extract:
      sealFlowId8: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC8-【physicalSealFlowDetail】一个用印文件-jpeg格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId8
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$jpegFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]

- test:
    name: "TC9-【physicalSealApplyapply】一个用印文件-png格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $pngFileKey
              }
          ]
    extract:
      sealFlowId9: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC9-【physicalSealFlowDetail】一个用印文件-png格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId9
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$pngFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]

- test:
    name: "TC10-【physicalSealApplyapply】3个用印文件-pdf格式-发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        signFiles:
          [
              {
                  "fileKey": $pdfFileKey
              },
            {
              "fileKey": $pdfFileKey2
            },
            {
              "fileKey": $pdfFileKey3
            }
          ]
    extract:
      sealFlowId10: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC10-【physicalSealFlowDetail】3个用印文件-pdf格式-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId10
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
      - eq: [content.data.signFiles.0.fileKey,$pdfFileKey]
      - len_gt: [ content.data.signFiles.0.downloadUrl,10 ]
      - eq: [content.data.signFiles.1.fileKey,$pdfFileKey2]
      - len_gt: [ content.data.signFiles.1.downloadUrl,10 ]
      - eq: [content.data.signFiles.2.fileKey,$pdfFileKey3]
      - len_gt: [ content.data.signFiles.2.downloadUrl,10 ]
      - eq: [content.data.applyUserInfo.applyUserCode,$applyUserCode]
      - eq: [content.data.applyUserInfo.applyAccountNo,$applyAccountNo]
      - eq: [content.data.applyUserInfo.applyOrganizationCode,$applyOrganizationCode]
      - eq: [ content.data.applyUserInfo.applyOrgNo,$applyOrgNo]
      - len_eq: [content.data.sealDetailsInfos.0.taskId,19]
      - eq: [ content.data.sealDetailsInfos.0.sealOrganizationCode,$sealOrganizationCode]