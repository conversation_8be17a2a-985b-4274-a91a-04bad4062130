- config:
    name: "思格特发起物理用印和查看详情-外带的场景"
    variables:
      #上海的经纬度和地址
      commonLongitude: "121.340129"
      commonLatitude: "31.132186"
      commonOutAddress: "上海 九亭大街105弄1-272号"

- test:
    name: "TC1-【physicalSealApplyapply】outAddress不传->发起失败,印章外带时外带地点必填"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress:
       longitude: $commonLongitude
       latitude: $commonLatitude
    validate:
      - eq: [content.message, "outAddress错误：开启外带，外带地点必传"]
      - eq: [content.code, 1617045]
      - eq: [content.data, null]

- test:
    name: "TC2-【physicalSealApplyapply】outAddress传200个字符->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       longitude: $commonLongitude
       latitude: $commonLatitude
       outAddress: "测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址"
    extract:
      sealFlowId2: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC2-【physicalSealFlowDetail】outAddress传200个字符-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId2
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC3-【physicalSealApplyapply】longitude和latitude格式填写正确-国内-北京->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       outAddress: $commonOutAddress
       takeOut: 1
       longitude: "116.340971"
       latitude: "39.944676"
    extract:
      sealFlowId3: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC3-【physicalSealFlowDetail】longitude和latitude格式填写正确-国内-北京-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId3
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC4-【physicalSealApplyapply】longitude和latitude格式填写正确-国内-北京-takeOut传0->发起成功,直接忽略经纬度"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       outAddress: $commonOutAddress
       takeOut: 0
       longitude: "116.340971"
       latitude: "39.944676"
    extract:
      sealFlowId4: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC4-【physicalSealFlowDetail】longitude和latitude格式填写正确-国内-北京-takeOut传0-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId4
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC5-【physicalSealApplyapply】longitude和latitude为空-takeOut传0->发起成功,直接忽略经纬度"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    extract:
      sealFlowId5: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC5-【physicalSealFlowDetail】longitude和latitude为空-takeOut传0-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId5
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC6-【physicalSealApplyapply】longitude或latitude格式不正确->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       outAddress: $commonOutAddress
       takeOut: 1
       longitude: "29292222"
       latitude: "3kw029292"
    validate:
      - eq: [content.message, "latitude错误：外带地点纬度(3kw029292)格式异常"]
      - eq: [content.code, 1617040]
      - eq: [content.data, null]

- test:
    name: "TC7-【physicalSealApplyapply】takeOut不传->发起成功，不传默认为0"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut:
    extract:
      sealFlowId7: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC7-【physicalSealFlowDetail】takeOut不传-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId7
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC9-【physicalSealApplyapply】outAddress传空字符串->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: ""
       longitude: $commonLongitude
       latitude: $commonLatitude
    validate:
      - eq: [content.message, "outAddress错误：开启外带，外带地点必传"]
      - eq: [content.code, 1617045]
      - eq: [content.data, null]

- test:
    name: "TC10-【physicalSealApplyapply】outAddress传长度为1的字符串->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: "n"
       longitude: $commonLongitude
       latitude: $commonLatitude
    extract:
      sealFlowId10: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC10-【physicalSealFlowDetail】outAddress传长度为1的字符串查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId10
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]



- test:
    name: "TC11-【physicalSealApplyapply】outAddress传长度为20的字符串->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       longitude: $commonLongitude
       latitude: $commonLatitude
       outAddress: "杭州杭州杭州杭州杭州杭州杭州杭州杭州杭州"
    extract:
      sealFlowId11: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC11-【physicalSealFlowDetail】outAddress传长度为20的字符串查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId11
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC12-【physicalSealApplyapply】outAddress传长度为20的字符串_包含特殊字符->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: "杭州杭州杭州杭州杭州杭州杭州杭州杭州杭州\/:*?\"<>|"
       longitude: $commonLongitude
       latitude: $commonLatitude
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC13-【physicalSealApplyapply】outAddress传长度201个字符串->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       longitude: $commonLongitude
       latitude: $commonLatitude
       outAddress: "1测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址测试地址"
    validate:
      - eq: [content.message, "outAddress错误：外带地点长度不可超出200位"]
      - eq: [content.code, 1600017]
      - eq: [content.data, null]

- test:
    name: "TC14-【physicalSealApplyapply】longitude和latitude格式填写正确-国内-杭州->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: $commonOutAddress
       longitude: "120.210095"
       latitude: "30.28927"
    extract:
      sealFlowId14: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC14-【physicalSealFlowDetail】longitude和latitude格式填写正确-国内-杭州-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId14
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC15-【physicalSealApplyapply】longitude和latitude格式填写正确-国外（法国）->发起成功"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: $commonOutAddress
       longitude: "48.8566"
       latitude: " 2.3522"
    extract:
      sealFlowId15: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC15-【physicalSealFlowDetail】longitude和latitude格式填写正确-国外（法国）-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId15
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]

- test:
    name: "TC15-【physicalSealApplyapply】latitude不传->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       longitude: $commonLongitude
       Latitude:
       outAddress: $commonOutAddress
    validate:
      - eq: [content.message, "latitude错误：开启外带，经纬度应必传"]
      - eq: [content.code, 1617032]
      - eq: [content.data, null]

- test:
    name: "TC16-【physicalSealApplyapply】longitude和latitude都不传->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       outAddress: $commonOutAddress
    validate:
      - eq: [content.message, "latitude错误：开启外带，经纬度应必传"]
      - eq: [content.code, 1617032]
      - eq: [content.data, null]

- test:
    name: "TC17-【physicalSealApplyapply】longitude和latitude传空字符串->发起失败"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 1
       longitude: ""
       latitude: ""
       outAddress: $commonOutAddress
    validate:
      - eq: [content.message, "latitude错误：开启外带，经纬度应必传"]
      - eq: [content.code, 1617032]
      - eq: [content.data, null]

- test:
    name: "TC18-【physicalSealApplyapply】longitude或latitude格式不正确-takeOut为0->发起成功（数据库不写入经纬度）"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       takeOut: 0
       longitude: "oooo"
       latitude: "oowoow"
       outAddress: $commonOutAddress
    extract:
      sealFlowId18: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]

- test:
    name: "TC18-【physicalSealFlowDetail】longitude或latitude格式不正确-takeOut为0-查看详情"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
       sealFlowId: $sealFlowId18
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.data.flowStatus,"3"]
