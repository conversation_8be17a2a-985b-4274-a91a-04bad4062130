- config:
    name: "[openapi]发起物理用印-添加仅限印控台用印的功能"
    variables:
        signfileKey: ${ENV(fileKey)}
        attachmentFileKey: ${ENV(1PageFileKey)}

- test:
    name: "仅限印控台用印,用印人需在印控台的红外线范围内用印onlyConsole=1"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        onlyConsole: 1
        signfileKey: $signfileKey
        attachmentFileKey: $attachmentFileKey
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.code, 200 ]
       # - eq: [ content.data, null ]

- test:
    name: "仅限印控台用印,用印人需在印控台的红外线范围内用印onlyConsole=0"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        onlyConsole: 0
        signfileKey: $signfileKey
        attachmentFileKey: $attachmentFileKey
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.code, 200 ]
       # - eq: [ content.data, null ]

- test:
    name: "仅限印控台用印,用印人需在印控台的红外线范围内用印onlyConsole不填"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        onlyConsole: ""
        signfileKey: $signfileKey
        attachmentFileKey: $attachmentFileKey
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.code, 200 ]

- test:
    name: "OPENAPI发起-支持仅限印控台用印onlyConsole=1,takeOut=1"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        onlyConsole: 1
        takeOut: 1
        longitude: 32.5
        latitude: 23.5
        outAddress: "黄山"
        signfileKey: $signfileKey
        attachmentFileKey: $attachmentFileKey
    validate:
        - eq: [ content.message, "用印任务开启了仅限印控台用印,不允许开启外带用印" ]
        - eq: [ content.code, 1617051 ]
