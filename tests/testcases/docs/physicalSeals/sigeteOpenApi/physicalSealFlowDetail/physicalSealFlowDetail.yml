- config:
    name: "[openapi]查看物理用印详情"


- test:
    name: "TC1-sealFlowId为空"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: ""
    validate:
      - eq: [ content.message, "sealFlowId与businessNo至少传一个" ]
      - eq: [ content.code, 1617042 ]
      - eq: [ content.data, null ]

- test:
    name: "TC2-sealFlowId不存在"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: "ooo"
    validate:
      - eq: [ content.message, "物理用印流程不存在" ]
      - eq: [ content.code,1617047 ]
      - eq: [content.data,null]

- test:
    name: "TC3-sealFlowId不能超过36位"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: "physicalSealFlowDetailphysicalSealFlowDetail"
    validate:
      - eq: [ content.message, "参数错误：sealFlowId长度不可超出36位" ]
      - eq: [ content.code, 1617044 ]
      - eq: [ content.data, null ]

#考虑其他环境也不会有物理数据，这个删除数据就先固定stable环境
- test:
    name: "TC4-sealFlowId对应流程已删除"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: "76e6b46c3de630d121890b49ac17502c"
    validate:
      - eq: [ content.message, "物理用印流程不存在" ]
      - eq: [ content.code, 1617047 ]
      - eq: [ content.data, null ]

#todo 初始化下面这些状态数据
- test:
    name: "TC5-sealFlowId对应流程已用印"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
        sealFlowId: "b2de6691f651ebd9d111be6e312abfba"
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [content.data.flowStatus,"4"]

- test:
    name: "TC6-sealFlowId对应流程已完成"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
        sealFlowId: "7fd6cbb8df5ef4b4e4e2fed65a763488"
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [content.data.flowStatus,"5"]
      - ne: [content.data.flowBeginTime,null]
      - ne: [content.data.flowEndTime,null]

- test:
    name: "TC6-sealFlowId对应流程已过期"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
        sealFlowId: "e7cf6389212313fde8bb1a3c8e4381fc"
    validate:
      - eq: [ content.message, 成功 ]
      - eq: [ content.code, 200 ]
      - ne: [ content.data, null ]
      - eq: [content.data.flowStatus,"6"]
      - ne: [content.data.flowBeginTime,null]
      - eq: [content.data.flowEndTime,null]

- test:
    name: "TC7-只传businessNo但编号错误"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
        businessNo: "2oppamdmqp-0000000asa"
    validate:
      - eq: [ content.message, "文档流程不存在" ]
      - eq: [ content.code, 1607001 ]
      - eq: [ content.data, null ]
