- config:
    name: "[openapi]根据sealFlowId或businessNo搜索流程"
    variables:
      - base_url: ${ENV(esign.gatewayHost)}
      - domainHost: ${ENV(esign.projectHost)}
      - outerDomainHost: ${ENV(esign.projectOuterHost)}


- test:
    name: 传businessNO生成sealFlowId和businessNO以下用
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        businessNo: ${get_randomNo_16()}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
      #60140-beta1-内外网隔离-接口新增出参
        - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]
    extract:
      sealFlowId1: json.data.sealFlowId
      businessNo1: json.data.businessNo

- test:
    name: 传businessNO生成sealFlowId和businessNO以下用
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        businessNo: ${get_randomNo_16()}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
      #60140-beta1-内外网隔离-接口新增出参
        - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
        - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]
    extract:
      sealFlowId2: json.data.sealFlowId
      businessNo2: json.data.businessNo


- test:
    name: "sealFlowId和businessNo都不传"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      #sealFlowId: ""
      #businessNo: ""
    validate:
      - eq: [ "json.code", 1617042 ]
      - eq: [ "json.message", 'sealFlowId与businessNo至少传一个' ]
     # - len_gt: [ "json.data",1 ]

- test:
    name: "只传sealFlowId且正确"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId1
      businessNo: ""
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.signFiles.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.signFiles.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.attachments.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.attachments.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]



- test:
    name: "只传sealFlowId但流程id错误"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: "22223333"
    validate:
      - eq: [ "json.code", 1617047 ]
      - eq: [ "json.message", '物理用印流程不存在']
      #- len_gt: [ "json.data",1 ]

- test:
    name: "sealFlowId和businessNo都传但sealFlowId错误"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: "111111111"
      businessNo: $businessNo1
    validate:
      - eq: [ "json.code", 1617047 ]
      - eq: [ "json.message", '物理用印流程不存在' ]
      #- len_gt: [ "json.data",1 ]

- test:
    name: "只传businessNo且正确"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: ""
      businessNo: $businessNo1
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.signFiles.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.signFiles.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.attachments.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.attachments.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]


- test:
    name: "只传businessNo但编号错误"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: ""
      businessNo: "1111111111"
    validate:
      - eq: [ "json.code", 1607001 ]
      - eq: [ "json.message", '文档流程不存在' ]
      #- len_gt: [ "json.data",1 ]


- test:
    name: "sealFlowId和businessNo都传且正确"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId1
      businessNo: $businessNo1
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.signFiles.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.signFiles.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.attachments.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.attachments.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]


- test:
    name: "sealFlowId和businessNo都传但对应不同的流程"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId1
      businessNo: $businessNo2
    validate:
      - eq: [ "json.code", 200]
      - eq: [ "json.message", '成功']
      - len_gt: [ "json.data",1 ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.signFiles.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.signFiles.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.attachments.0.downloadUrl, "$domainHost" ]
      - contains: [ content.data.attachments.0.downloadOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrl, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealUrlShort, "$domainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.sealDetailsInfos.0.sealOuterUrlShort, "$outerDomainHost" ]