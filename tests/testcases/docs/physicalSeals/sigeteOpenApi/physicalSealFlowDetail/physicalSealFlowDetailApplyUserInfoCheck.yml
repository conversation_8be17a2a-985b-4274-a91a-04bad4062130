- config:
    name: "[openapi]申请人信息校验"
    base_url: ${ENV(esign.gatewayHost)}

- test:
    name: 申请人账号所属企业是账号所属企业
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.main.orgCode)}
        businessNo: ${get_randomNo_16()}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
    extract:
      sealFlowId1: json.data.sealFlowId

- test:
    name: "入参flowId正确,申请人账号"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId1
      businessNo: ""
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]

- test:
    name: "入参flowId正确,申请人组织为所属企业"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId1
      businessNo: ""
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]

- test:
    name: 申请人组织为所属部门
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign03.userCode)}
        applyOrganizationCode: ${ENV(sign03.main.departCode)}
        #businessNo: ${get_randomNo_16()}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
    extract:
      sealFlowId2: json.data.sealFlowId
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: "入参flowId正确,申请人组织为所属部门"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId2
      businessNo: ""
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]

- test:
    name: 申请人组织为兼职企业
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.JZ.orgCode)}
        #businessNo: ${get_randomNo_16()}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
    extract:
      sealFlowId3: json.data.sealFlowId
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: "入参flowId正确,申请人组织为兼职企业"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId3
      businessNo: ""
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]

- test:
    name: 申请人组织为兼职部门
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
        applyUserCode: ${ENV(sign01.userCode)}
        applyOrganizationCode: ${ENV(sign01.JZ.departCode)}
    validate:
        - eq: [ "json.code", 200]
        - eq: [ "json.message", '成功']
        - len_gt: ["json.data",1]
    extract:
      sealFlowId4: json.data.sealFlowId

- test:
    name: "入参flowId正确,申请人组织为兼职部门"
    api: api/esignDocs/physical/sealcontrols/physicalSealFlowDetail.yml
    variables:
      sealFlowId: $sealFlowId4
    validate:
      - eq: [ "json.code", 200 ]
      - eq: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]