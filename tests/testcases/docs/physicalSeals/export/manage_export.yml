- config:
    name: "物理用印-我管理的-导出"
    variables:
      second: 10
      todayDate: ${getDateTime()}
      todayDateStr: ${substring($todayDate,0,8)}


- test:
    name: "setup-发起物理用印2"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       applyUserCode: ${ENV(sign01.userCode)}
       applyAccountNo: ${ENV(sign01.accountNo)}
       applyOrganizationCode:  ${ENV(sign01.main.orgCode)}
       applyOrgNo: ${ENV(sign01.main.orgNo)}
    extract:
      sealFlowId01: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
    teardown_hooks:
      - ${sleep($second)}

- test:
    name: "setup-发起物理用印2"
    api: api/esignDocs/physical/sealcontrols/physicalSealApply.yml
    variables:
       subject: '物理用印申请'
       applyUserCode: ${ENV(sign01.userCode)}
       applyAccountNo: ${ENV(sign01.accountNo)}
       applyOrganizationCode:  ${ENV(sign01.main.orgCode)}
       applyOrgNo: ${ENV(sign01.main.orgNo)}
    extract:
      sealFlowId02: content.data.sealFlowId
    validate:
      - eq: [content.message, 成功]
      - eq: [content.code, 200]
      - ne: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: "导出物理用印-我管理的"
    api: api/esignDocs/physical/seal/manage_export.yml
    variables:
      includeIdList: ["$sealFlowId01","$sealFlowId02"]
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep($second)}


- test:
    name: "导出物理用印-我管理的-流程id不存在"
    api: api/esignDocs/physical/seal/manage_export.yml
    variables:
      includeIdList: ["sealFlowId01"]
    validate:
      - eq: [content.status, 1612039]
      - eq: [content.success, False]
      - eq: [content.message, "无该数据权限"]
      - eq: [content.data, null]


- test:
    name: TC-sign01的后台任务查看
    api: api/esignSigns/portal/getBackendTaskList.yml
    variables:
      authorizationBackendTaskList: "${getPortalToken()}"
    teardown_hooks:
      - ${sleep($second)}
    validate:
       - eq: [ json.status, 200 ]
       - eq: [ json.success, true ]
       - contains: [ json.data.list.0.name, "批量导出-物理用印" ]
       - contains: [ json.data.list.0.fileName, "批量导出-物理用印-$todayDateStr" ]