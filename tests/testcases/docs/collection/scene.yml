- config:
    name: 查询采集任务列表
    variables:
      - sign01Code: ${ENV(sign01.userCode)}
      - sign01No: ${ENV(sign01.accountNo)}
      - org01No: ${ENV(sign01.main.orgNo)}
      - org01Code: ${ENV(sign01.main.orgCode)}
      - collectionTemplateId111: ${get_collectionTemplateId(全链路)}
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"
      - businessTemplateCode0: ""

- test:
    name: step1-查询采集模板列表
    api: api/esignDocs/collection/templateList.yml
    variables:
      collectionTemplateName: ""
      collectionTemplateId: "$collectionTemplateId111"
      collectionTemplateStartCreateTime: "${getDateTime(-365,1)}"
      collectionTemplateEndCreateTime: "${getDateTime(365,1)}"
      pageNo: 1
      pageSize: 50
    extract:
      - templateId0: content.data.collectionTemplateInfos.0.collectionTemplateId
      - collectionTemplateName0: content.data.collectionTemplateInfos.0.collectionTemplateName
#      - businessTemplateCode0: content.data.collectionTemplateInfos.0.businessTemplateInfos.0.businessTemplateCode
      - collectionTemplateCreatorUserCode0: content.data.collectionTemplateInfos.0.collectionTemplateCreatorUserCode
      - collectionTemplateCreatorCustomAccountNo0: content.data.collectionTemplateInfos.0.collectionTemplateCreatorCustomAccountNo

    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - ne: [ content.data.collectionTemplateInfos, ""]
#      - ne: [ content.data.collectionTemplateInfos.0.businessTemplateInfos, ""]
#      - ne: [ content.data.collectionTemplateInfos.0.businessTemplateInfos.0.businessTemplateCode, ""]
      - ne: [ content.data.collectionTemplateInfos.0.collectionTemplateCreatorUserCode, ""]
      - ne: [ content.data.collectionTemplateInfos.0.collectionTemplateCreatorCustomAccountNo, ""]


- test:
    name: step2-查询采集模板组件
    api: api/esignDocs/collection/templateModuleInfos.yml
    variables:
      collectionTemplateId: $templateId0
    extract:
      - collectionTemplateName1:  content.data.collectionTemplateName
      - moduleId0: content.data.moduleInfos.0.moduleId
      - moduleInfos0:  content.data.moduleInfos

    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - eq: [ content.data.collectionTemplateName, $collectionTemplateName0]
      - eq: [ content.data.collectionTemplateId, $templateId0]
      - ne: [ content.data.moduleInfos, ""]


- test:
    name: step-3-公开采集-需要审核
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "自动化创建采集任务-公开采集-需要审核"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 0
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}]
    extract:
      - collectionTaskName3: content.data.collectionTaskName
      - collectionTaskId3: content.data.collectionTaskId
      - collectionTaskShortUrl0: content.data.collectionTaskShortUrl
      - collectionTaskUrl0: content.data.collectionTaskUrl

    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.collectionTaskName, "自动化创建采集任务-公开采集-需要审核" ]
      - ne: [ content.data.collectionTaskId, "" ]
      - ne: [content.data.collectionTaskShortUrl, ""]
      - ne: [content.data.collectionTaskUrl, ""]


- test:
    name: step-4-指定人员-需要审核
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "自动化创建采集任务-指定人员采集-需要审核"
      collectionTaskType: 1
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 0
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0},{"assignUserName":"测试采集二","contactContent":"<EMAIL>","contactType": 1}]
    extract:
      - collectionTaskName3: content.data.collectionTaskName
      - collectionTaskId3: content.data.collectionTaskId
      - collectionTaskShortUrl0: content.data.collectionTaskShortUrl
      - collectionTaskUrl0: content.data.collectionTaskUrl

    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.collectionTaskName, "自动化创建采集任务-指定人员采集-需要审核" ]
      - ne: [ content.data.collectionTaskId, "" ]
      - ne: [content.data.collectionTaskShortUrl, ""]
      - ne: [content.data.collectionTaskUrl, ""]


- test:
    name: step-5-公开采集-自动发起
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "自动化创建采集任务-公开采集-自动发起"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 0
      automaticInitiation: 1
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0},{"assignUserName":"测试采集二","contactContent":"<EMAIL>","contactType": 1}]
#    extract:
#      - collectionTaskName3: content.data.collectionTaskName
#      - collectionTaskId3: content.data.collectionTaskId
#      - collectionTaskShortUrl0: content.data.collectionTaskShortUrl
#      - collectionTaskUrl0: content.data.collectionTaskUrl

    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.collectionTaskName, "自动化创建采集任务-公开采集-自动发起" ]
#      - ne: [ content.data.collectionTaskId, "" ]
#      - ne: [content.data.collectionTaskShortUrl, ""]
#      - ne: [content.data.collectionTaskUrl, ""]
      - eq: [ content.code, 1612168 ]
      - contains: [ content.message, "创建采集任务失败：自动发起配置项开启时业务模版必填" ]
      - eq: [ content.data, null ]

- test:
    name: step-5-指定人员-自动发起
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "自动化创建采集任务-指定人员-自动发起"
      collectionTaskType: 1
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01Code
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 0
      automaticInitiation: 1
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0},{"assignUserName":"测试采集二","contactContent":"<EMAIL>","contactType": 1}]
#    extract:
#      - collectionTaskName3: content.data.collectionTaskName
#      - collectionTaskId3: content.data.collectionTaskId
#      - collectionTaskShortUrl0: content.data.collectionTaskShortUrl
#      - collectionTaskUrl0: content.data.collectionTaskUrl

    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.collectionTaskName, "自动化创建采集任务-指定人员-自动发起" ]
#      - ne: [ content.data.collectionTaskId, "" ]
#      - ne: [content.data.collectionTaskShortUrl, ""]
#      - ne: [content.data.collectionTaskUrl, ""]
      - eq: [ content.code, 1612168 ]
      - contains: [ content.message, "创建采集任务失败：自动发起配置项开启时业务模版必填" ]
      - eq: [ content.data, null ]
- test:
    name: 查询采集任务列表
    api: api/esignDocs/collection/taskList.yml
    variables:
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorCustomOrgNo : $org01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskEndCreateTime: ""
      collectionTaskEndExpireTime: ""
      collectionTaskEndUpdateTime: ""
      collectionTaskId: ""
      collectionTaskName: ""
      collectionTaskStartCreateTime: ""
      collectionTaskStartExpireTime: ""
      collectionTaskStartUpdateTime: ""
      collectionTaskStatus: ""
      collectionTemplateId: $templateId0
      pageNo: 1
      pageSize: 50
    validate:
      - eq: [ content.code, $code ]
      - contains: [content.message,$message ]
      - ge: [ content.data.total, 0 ]


- test:
    name: step3-查询已采信息列表
    api: api/esignDocs/collection/InformationList.yml
    variables:
      collectedInformationId: ""
      collectedInformationStatus: "1,2,3,4"
      collectionTaskId: ""
      collectionTemplateId: $templateId0
#      moduleInfos: $moduleInfos0
      moduleInfos: []
      pageNo: 1
      pageSize: 50
    extract:
      - collectedInformationId0: content.data.collectedInfos.0.collectedInformationId
      - message0: content.message
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]



- test:
    name: step4-查询已采信息详情
    api: api/esignDocs/collection/InformationDetail.yml
    variables:
      collectedInformationId: $collectedInformationId0
      collectionTemplateId: $templateId0
    extract:
      - code0: content.code
      - message0: content.message
    validate:
      - eq: [ $code, $code0 ]
      - contains: [ $message, $message0 ]


- test:
    name: TC1-校验新建时开启需要审核和自动发起配置项
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "自动化创建采集任务-报错"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 1
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}]

    validate:
      - eq: [ content.code, 1612168 ]
      - eq: [ content.data, null ]
#      - contains: [ content.message, "创建采集任务失败：采集任务不能同时开启需要审核和自动发起配置项" ]
      - contains: [ content.message, "创建采集任务失败：自动发起配置项开启时业务模版必填" ]

- test:
    name: TC2-校TaskName为不支持的特殊字符
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "\/:*?<>|"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 0
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}]

    validate:
      - eq: [ content.code, 1612165 ]
      - eq: [ content.message, "collectionTaskName错误: 【/:*?<>|】不支持的特殊字符" ]
      - eq: [ content.data, null ]

- test:
    name: TC3-校TaskName为不支持<字符
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $templateId0
      collectionTaskName: "采集任务名称<101"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $sign01Code
      collectionTaskCreatorCustomAccountNo: $sign01No
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 0
      businessTemplateCode: $businessTemplateCode0
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}]

    validate:
      - eq: [ content.code, 1612165 ]
      - eq: [ content.message, "collectionTaskName错误: 【采集任务名称<101】不支持的特殊字符" ]
      - eq: [ content.data, null ]