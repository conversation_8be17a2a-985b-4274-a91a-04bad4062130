- config:
    name: 查询采集任务列表
    variables:
      - collectedInformationId: "1735263921751785472"
      - collectedInformationStatus:
      - collectionTaskId:
      - collectionTemplateId: ${get_collectionTemplateId()}
      - moduleId:
      - moduleValue:
      - pageSize: 50
      - pageNo: 1
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"



- test:
    name: $name
    api: api/esignDocs/collection/InformationList.yml
    variables:
      collectedInformationId: $collectedInformationId
      collectedInformationStatus: $collectedInformationStatus
      collectionTaskId: $collectionTaskId
      collectionTemplateId: $collectionTemplateId
      moduleInfos: [{"moduleId": $moduleId,"moduleValue": $moduleValue}]
      pageNo: $pageNo
      pageSize: $pageSize

    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message ]
