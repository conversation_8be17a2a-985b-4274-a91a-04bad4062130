- config:
    name: 查询采集模板列表
    variables:
      - StartCreateTime: "2023-01-01 00:00:00"
      - EndCreateTime: "2024-01-01 00:00:00"
      - collectionTemplateName: ""
      - collectionTemplateId: ""
      - pageSize: 50
      - pageNo: 1
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"

- test:
    name: $name
    api: api/esignDocs/collection/templateList.yml
    variables:
      collectionTemplateName: $collectionTemplateName
      collectionTemplateId: $collectionTemplateId
      collectionTemplateStartCreateTime: $StartCreateTime
      collectionTemplateEndCreateTime: $EndCreateTime
      pageNo: $pageNo
      pageSize: $pageSize

    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message,$message ]

