- config:
    name: 查询采集任务列表
    variables:
      - customAccountNo:
      - StartCreateTime: "2023-01-01 00:00:00"
      - EndCreateTime: "2024-01-01 00:00:00"
      - pageSize: 50
      - pageNo: 1
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"
      - customAccountNo: ""
      - customOrgNo: ""
      - organizationCode: ""
      - userCode: ""
      - collectionTaskEndCreateTime: ""
      - collectionTaskEndExpireTime: ""
      - collectionTaskEndUpdateTime: ""
      - collectionTaskId: ""
      - collectionTaskName: ""
      - collectionTaskStartCreateTime: ""
      - collectionTaskStartExpireTime: ""
      - collectionTaskStartUpdateTime: ""
      - collectionTaskStatus: ""
      - collectionTemplateId: ${get_collectionTemplateId()}


- test:
    name: $name
    api: api/esignDocs/collection/taskList.yml
    variables:
      collectionTaskCreatorCustomAccountNo: $customAccountNo
      collectionTaskCreatorCustomOrgNo : $customOrgNo
      collectionTaskCreatorOrganizationCode: $organizationCode
      collectionTaskCreatorUserCode: $userCode
      collectionTaskEndCreateTime: $collectionTaskEndCreateTime
      collectionTaskEndExpireTime: $collectionTaskEndExpireTime
      collectionTaskEndUpdateTime: $collectionTaskEndUpdateTime
      collectionTaskId: $collectionTaskId
      collectionTaskName: $collectionTaskName
      collectionTaskStartCreateTime: $collectionTaskStartCreateTime
      collectionTaskStartExpireTime: $collectionTaskStartExpireTime
      collectionTaskStartUpdateTime: $collectionTaskStartUpdateTime
      collectionTaskStatus: $collectionTaskStatus
      collectionTemplateId: $collectionTemplateId
      pageNo: $pageNo
      pageSize: $pageSize
    validate:
      - eq: [ content.code, $code ]
      - contains: [content.message,$message ]
