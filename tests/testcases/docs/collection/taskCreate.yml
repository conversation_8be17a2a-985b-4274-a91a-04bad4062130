- config:
    name: 新建采集任务
    variables:
      - collectionTemplateId: ${get_collectionTemplateId()}
      - collectionTaskName: "测试"
      - collectionTaskType: 0
      - assignUsers: []
      - userCode: ${ENV(sign01.userCode)}
      - customAccountNo: ""
      - organizationCode: ""
      - customOrgNo: ${ENV(sign01.main.orgNo)}
      - collectionTaskExpireTime: ${getDateTime(3,1)}
      - collectionTaskApprove: 0
      - automaticInitiation: 0
      - businessTemplateCode: ""
      - code: 200
      - message: "成功"


- test:
    name: $name
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $collectionTemplateId
      collectionTaskName: $collectionTaskName
      collectionTaskType: $collectionTaskType
      collectionTaskCreatorUserCode: $userCode
      collectionTaskCreatorCustomAccountNo: $customAccountNo
      collectionTaskCreatorOrganizationCode: $organizationCode
      collectionTaskCreatorCustomOrgNo: $customOrgNo
      collectionTaskExpireTime: $collectionTaskExpireTime
      collectionTaskApprove: $collectionTaskApprove
      automaticInitiation: $automaticInitiation
      businessTemplateCode: $businessTemplateCode
      assignUsers: $assignUsers

    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message ]
