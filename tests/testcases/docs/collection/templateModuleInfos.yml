- config:
    name: 查询采集模板列表
    variables:
      - collectionTemplateId: ""
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"

- test:
    name: $name
    api: api/esignDocs/collection/templateModuleInfos.yml
    variables:
      collectionTemplateId: $collectionTemplateId
    extract:
      - code0: content.code
      - message0: content.message
    validate:
      - eq: [ $code, $code0 ]
      - contains: [ $message, $message0 ]
