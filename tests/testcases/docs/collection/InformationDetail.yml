- config:
    name: 查询采集任务列表
    variables:
      - collectedInformationId: 1719978802757902336
      - collectionTemplateId: ${get_collectionTemplateId()}
      - code: 200
      - message: "成功"
      - subject: "校验参数"
      - name: "1-校验参数"



- test:
    name: $name
    api: api/esignDocs/collection/InformationDetail.yml
    variables:
      collectedInformationId: $collectedInformationId
      collectionTemplateId: $collectionTemplateId

    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message ]
