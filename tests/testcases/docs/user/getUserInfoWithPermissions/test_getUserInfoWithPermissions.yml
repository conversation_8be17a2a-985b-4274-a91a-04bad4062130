- config:
   name: "获取用户信息-匹配范围为管理平台分配数据权限范围内的用户姓名"
#   variables:
#        headers: ${gen_main_headers()}

- test:
    name: "TC1-userName不填，为空"
    api: api/esignDocs/user/getUserInfoWithPermissions.yml
    variables:
      - userName: ""
    validate:
      - eq: [ "content.success",false ]
      - eq: [ "content.status",913 ]
      - eq: ["content.message","用户名不能为空"]
- test:
    name: "TC1-只填写userName"
    api: api/esignDocs/user/getUserInfoWithPermissions.yml
    variables:
      - userName: "${ENV(ceswdzxzdhyhwgd1.userName)}"
    validate:
      - eq: [ "content.status",200 ]
      - eq: ["content.message","成功"]
      - contains: ["content.data.0.userName",$userName]