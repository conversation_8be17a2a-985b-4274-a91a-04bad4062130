- config:
   variables:
        headers: ${gen_main_headers()}

- test:
    name: "TC1-发起签署-查询个人"
    api: api/esignDocs/user/getUserInfo.yml
    variables:
      - sealType: 1
      - userName: "测试签署"
      - userType: 1
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
      - len_ge: [ "content.data.signerList",1 ]

- test:
    name: "TC2-发起签署-查询机构经办人"
    api: api/esignDocs/user/getUserInfo.yml
    variables:
      - sealType: 2
      - userName: "测试签署"
      - userType: 1
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
      - len_ge: [ "content.data.signerList",1 ]


