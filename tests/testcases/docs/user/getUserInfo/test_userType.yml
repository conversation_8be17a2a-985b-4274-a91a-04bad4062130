- config:
   variables:
        headers: ${gen_main_headers()}

- test:
    name: "TC1-查询内部"
    api: api/esignDocs/user/getUserInfo.yml
    variables:
      - organizeCode:
      - sealType:
      - userName: "测试"
      - userType: 1
    validate:
      - eq: [ "content.status",200 ]



- test:
    name: "TC2-查询外部"
    api: api/esignDocs/user/getUserInfo.yml
    variables:
      - organizeCode:
      - sealType:
      - userName: "测试"
      - userType: 2
    validate:
      - eq: [ "content.status",200 ]