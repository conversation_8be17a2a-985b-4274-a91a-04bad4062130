#通过用户组编码获取组员列表-userGroupCode字段校验
- config:
   variables:
        headers: ${gen_main_headers()}

#- test:
#    name: "TC1-根据管理平台getUserGroupPermissionList获取用户组编码"
#    api: api/esignDocs/managePlatform/user/getUserGroupPermissionList.yml
#    variables:
#      - currPage: 1
#      - pageSize: 10
#    extract:
#      - userGroupCode1: content.data.list.0.userGroupCode
#
#- test:
#    name: "TC2-根据管理平台getUsersByUserGroupCode获取用户组"
#    api: api/esignDocs/managePlatform/user/getUsersByUserGroupCode.yml
#    variables:
#      - userGroupCode: $userGroupCode1
#    extract:
#      - userCode1: content.data.0.userCode
#      - userName1: content.data.0.userName

#- test:
#    name: "TC3-根据userGroupCode获取关联用户列表"
#    api: api/esignDocs/user/getUserListByUserGroupCode.yml
#    variables:
#      - userGroupCode: $userGroupCode1
#    teardown_hooks:
#      - ${getuserCode($response,$userCode1)}
#    validate:
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.userName1",$userName1 ]

- test:
    name: "TC3-userGroupCode非空校验"
    api: api/esignDocs/user/getUserListByUserGroupCode.yml
    variables:
      - userGroupCode:
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]
- test:
    name: "TC4-userGroupCode输入空格"
    api: api/esignDocs/user/getUserListByUserGroupCode.yml
    variables:
      - userGroupCode: " "
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]

- test:
    name: "TC5-userGroupCode输入不存在的用户组编码"
    api: api/esignDocs/user/getUserListByUserGroupCode.yml
    variables:
      - userGroupCode: "test123"
    validate:
      - eq: [ "content.status",200]
      - eq: [ "content.success",true]
      - len_eq: [ "content.data.userList",0 ]