#根据角色编码获取关联的用户(包括所属组织、所在公司)-roleCode字段校验
- config:
   output:
     - usercode1
     - roleCode1

- test:
      name: "TC1-从管理平台getUserPermissionList获取角色编码"
      api: api/esignDocs/managePlatform/user/getUserPermissionList.yml
      variables:
        - currPage: 1
        - pageSize: 10
      extract:
        - usercode1: content.data.list.0.userCode
        - roleCode1: content.data.list.0.roleCode


- test:
    name: "TC2-根据roleCode1获取关联用户列表"
    api: api/esignDocs/user/getUserOrgListByRoleCode.yml
    variables:
      - roleCode: $roleCode1
    teardown_hooks:
      - ${getusercode($response,$usercode1)}
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功"]
      - eq: ["content.code",$usercode1]

- test:
    name: "TC3-RoleCode非空校验"
    api: api/esignDocs/user/getUserOrgListByRoleCode.yml
    variables:
      - roleCode:
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]
- test:
    name: "TC4-RoleCode输入空格"
    api: api/esignDocs/user/getUserOrgListByRoleCode.yml
    variables:
      - roleCode: " "
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]

- test:
    name: "TC4-RoleCode输入不存在的角色编码"
    api: api/esignDocs/user/getUserOrgListByRoleCode.yml
    variables:
      - roleCode: "test123"
    validate:
      - eq: [ content.status,200]
      - eq: [ content.message,"成功"]
      - len_eq: [content.data.userList,0]