#通过组织id获取用户列表-allChildOrganizationFlag字段校验
- config:
   name: "通过组织id获取用户列表-allChildOrganizationFlag字段校验"

- test:
    name: "TC1-获取当前用户所属组织账号"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode:
    extract:
      - organizationId1: content.data.organizationId

- test:
    name: "TC3-allChildOrganizationFlag为true时获取对应内部组织及以下组织用户列表"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag: true
      - organizationId: $organizationId1
      - userName:
    teardown_hooks:
      - ${getorganizationIdlist($response,$organizationId1)}
    validate:
      - eq: [ "content.status",200 ]
      - eq: ["content.orgid", $organizationId1]
      - eq: [ "content.message","成功" ]


# 页面不这次查询外部的，暂时注掉
#- test:
#    name: "TC4-allChildOrganizationFlag为true时获取对应外部组织用户列表"
#    api: api/esignDocs/user/getUserListByOrganizationID.yml
#    variables:
#      - allChildOrganizationFlag: true
#      - organizationId: $outsideOrganizationId1
#      - userName:
#    teardown_hooks:
#      - ${getorganizationIdlist($response,$outsideOrganizationId1)}
#    validate:
#      - eq: [ "content.status",200 ]
#      - eq: ["content.orgid", $outsideOrganizationId1]
#      - eq: [ "content.message","成功" ]

- test:
    name: "TC5-allChildOrganizationFlag为false或空时获取本组织"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag: false
      - organizationId: $organizationId1
      - userName:
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.userList.0.organizationId",$organizationId1 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC6-allChildOrganizationFlag为非法字符时"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag: "test"
      - organizationId: $organizationId1
      - userName:
    validate:
      - eq: [ "content.status",1600015 ]
      - eq: [ "content.message","allChildOrganizationFlag参数错误!" ]
      - eq: [ "content.data",null ]
