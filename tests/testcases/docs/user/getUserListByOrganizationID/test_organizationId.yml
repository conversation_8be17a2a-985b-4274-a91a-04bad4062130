#通过组织id获取用户列表-organizationId字段校验
- config:
   name: "通过组织id获取用户列表-organizationId字段校验"


- test:
    name: "TC1-获取当前内部用户所属组织id"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode:
    extract:
      - organizationId1: content.data.organizationId

- test:
    name: "TC3-根据organizationId获取内部用户列表"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag: true
      - organizationId: $organizationId1
      - userName:
    teardown_hooks:
      - ${getorganizationIdlist($response,$organizationId1)}
    validate:
      - eq: [ "content.status",200 ]
      - eq: ["content.orgid", $organizationId1]
      - eq: [ "content.message","成功" ]

# 页面不这次查询外部的，暂时注掉
#- test:
#    name: "TC4-根据organizationId获取外部用户列表"
#    api: api/esignDocs/user/getUserListByOrganizationID.yml
#    variables:
#      - allChildOrganizationFlag:
#      - organizationId: $outsideOrganizationId1
#      - userName:
#    teardown_hooks:
#      - ${getorganizationIdlist($response,$outsideOrganizationId1)}
#    validate:
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.message","成功" ]
#      - eq: ["content.data.userList.0.organizationId",$outsideOrganizationId1]

- test:
    name: "TC5-organizationId非空校验"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag:
      - organizationId:
      - userName:
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.message","所属组织id不能为空"]
      - eq: [ "content.data",null ]

- test:
    name: "TC6-organizationId输入空格"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag:
      - organizationId: " "
      - userName:
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.message","所属组织id不能为空"]
      - eq: [ "content.data",null ]

- test:
    name: "TC7-organizationId输入任意不存在的组织id"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag:
      - organizationId: "test123"
      - userName:
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功"]
      - len_eq: [ "content.data.userList", 0]