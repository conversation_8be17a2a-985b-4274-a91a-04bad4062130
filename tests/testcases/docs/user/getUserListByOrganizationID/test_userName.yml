#通过组织id获取用户列表-userName字段校验
- config:
   name: "通过组织id获取用户列表-userName字段校验"

- test:
    name: "TC1-获取当前用户所属组织账号和用户姓名"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode:
    extract:
      - userName1: content.data.userName
      - organizationId1: content.data.organizationId


- test:
    name: "TC3-根据内部用户Username获取用户信息"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag:
      - organizationId: $organizationId1
      - userName: $userName1
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.userList.0.userName",$userName1]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC4-输入个不存在的username，则返回空"
    api: api/esignDocs/user/getUserListByOrganizationID.yml
    variables:
      - allChildOrganizationFlag:
      - organizationId: $organizationId1
      - userName: "test1"
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.userList",[]]
      - eq: [ "content.message","成功" ]

#页面不这次查询外部的，暂时注掉
#- test:
#    name: "TC5-输入外部用户的userName获取该用户信息"
#    api: api/esignDocs/user/getUserListByOrganizationID.yml
#    variables:
#      - allChildOrganizationFlag:
#      - organizationId: $outsideOrganizationId1
#      - userName: $outsideuserName1
#    validate:
#      - eq: [ "content.status",200 ]
#      - contains: [ "content.data.userList.0.userName",$outsideuserName1]
#      - eq: [ "content.message","成功" ]