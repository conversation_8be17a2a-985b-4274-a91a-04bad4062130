- config:
   variables:
        headers: ${gen_main_headers()}

- test:
    name: "TC1-查询用戶"
    api: api/esignDocs/user/getUserOrOrg.yml
    variables:
        - name: '测试'
        - signerType: 1
        - userType: 1
    validate:
      - eq: [ "content.status",200 ]
     # - contains: ["content.data",黄虎]
- test:
    name: "TC2-查询组织"
    api: api/esignDocs/user/getUserOrOrg.yml
    variables:
      - name: '测试'
      - signerType: 2
      - userType: 1
    validate:
      - eq: [ "content.status",200 ]
      # - contains: ["content.data",黄虎]