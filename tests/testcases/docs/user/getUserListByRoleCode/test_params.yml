#根据角色编码获取关联的用户-RoleCode字段校验
- config:
   variables:
        headers: ${gen_main_headers()}

- test:
    name: "TC3-RoleCode非空校验"
    api: api/esignDocs/user/getUserListByRoleCode.yml
    variables:
      - RoleCode:
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]
- test:
    name: "TC4-RoleCode输入空格"
    api: api/esignDocs/user/getUserListByRoleCode.yml
    variables:
      - RoleCode: " "
    validate:
      - eq: [ "content.status",913 ]
      - eq: [ "content.success",false]
      - eq: [ "content.data",null ]

- test:
    name: "TC5-RoleCode输入不存在的角色编码"
    api: api/esignDocs/user/getUserListByRoleCode.yml
    variables:
      - RoleCode: "test123"
    validate:
      - eq: [ "content.status",200]
      - eq: [ "content.success",true]
      - len_eq: [ "content.data",0 ]