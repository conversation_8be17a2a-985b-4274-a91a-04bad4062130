- config:
    name: "查询企业数据"
    variables:
      - userCode0: ${ENV(sign01.userCode)}
      - wserCode0: ${ENV(wsignwb01.userCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - userCode2: ${ENV(sign03.userCode)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - worgCode0: ${ENV(worg01.orgCode)}
      - depatCode1: ${ENV(sign03.main.departCode)}
      - sp: " "

- test:
    name: "setup-查询内部用户"
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      customAccountNoMIDetail: $userCode0
    extract:
      - _userCode1: content.data.0.userCode
      - _userName1: content.data.0.name
      - _customAccountNo1: content.data.0.customAccountNo
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - contains: [ content.data.0, "mobile" ]
      - gt: [ content.data.0.mainOrganizationCode, "1" ]

- test:
    name: "step1-查询用户"
    api:  api/esignManage/outerUsers/detail.yml
    variables:
        userCode: "$wserCode0"
        customAccountNo: ""
    extract:
      - _userCode2: content.data.0.userCode
      - _userName2: content.data.0.name
      - _userMobile2: content.data.0.mobile
      - _customAccountNo2: content.data.0.customAccountNo
    validate:
        - eq: [ content.code, 200 ]
        - eq: [ content.message, "成功" ]
        - ne: [ content.data.0.mainCustomOrgNo, "" ]

- test:
    name: "TC1-查询用户数据-正常场景-内部用户"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "测试签署"
          userTerritory: "1"
    extract:
      - firstUserCode: content.data.0.userCode
      - firstUserName: content.data.0.userName
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data, 0]

- test:
    name: "TC2-查询用户数据-正常场景-相对方用户"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "测试签署"
          userTerritory: "2"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data, 0]

- test:
    name: "TC3-查询用户数据-正常场景-内部用户-账号精确匹配"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "$sp$_customAccountNo1$sp"
          userTerritory: "1"
    extract:
      - firstUserCode: content.data.0.userCode
      - firstUserName: content.data.0.userName
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data, 0]

- test:
    name: "TC4-查询用户数据-正常场景-相对方用户-账号精确匹配"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "$_customAccountNo1"
          userTerritory: "2"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC6-查询用户数据-正常场景-部分账号-查询为空"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "sig"
          userTerritory: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC7-keyWord长度校验-查询为空"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "${generate_random_str(201)}"
          organizationTerritory: "1"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC8-查询用户数据-必填缺失-keyWord为空"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: ""
          userTerritory: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC9-查询用户数据-参数格式错误-userTerritory非法字符串"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "测试签署"
          userTerritory: "abc"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC10-查询用户数据-参数格式错误-userTerritory=0"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "测试签署"
          userTerritory: "0"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC11-查询用户数据-参数格式错误-userTerritory=-1"
    api: api/esignDocs/user/search.yml
    variables:
      json:
        params:
          keyWord: "测试签署"
          userTerritory: "-1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC12-查询用户数据-无权限/未登录"
    api: api/esignDocs/user/search.yml
    variables:
      authorization0: ""
      json:
        params:
          keyWord: "测试"
          userTerritory: "1"
    validate:
      - eq: [content.status, 401]
      - eq: [content.success, false]
      - eq: [content.message, "登录失效，请重新登录" ]


