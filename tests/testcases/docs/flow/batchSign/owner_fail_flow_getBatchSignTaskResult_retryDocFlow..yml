- config:
    name: "单方签署，无审批"


- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toSign.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrg<PERSON>odeCommon
      - presetNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "我发起的-失败电子签署失败原因"
    api: api/esignDocs/flow/batchSign/owner_retryDocFlowTask.yml
    variables:
      - "flowId": $flowIdCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success", true ]
#      - eq: [ "content.data.handleStatus", 9 ]


- test:
    name: "我发起的-失败电子签署重试"
    api: api/esignDocs/flow/batchSign/owner_retryDocFlowTask.yml
    variables:
      - "flowId": $flowIdCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", true ]


