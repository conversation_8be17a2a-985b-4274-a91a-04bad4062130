#场景说明：
- config:
    name: "单方签署，有审批"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toAudit.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
      processInstanceIdCommon: content.data.list.0.processInstanceId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC-撤回刚发起的电子签署"
    api: api/esignDocs/flow/business/withdrawNodeInstance.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    teardown_hooks:
      - ${sleep(10)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", true ]


- test:
    name: "check列表状态"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $flowIdCommon
      flowStatus: "11"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.flowStatusName, "撤回到发起"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, "$initiatorUserNameCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]


- test:
    name: "正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "None"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signedFileVOList.0.fileName, "测试.pdf"]


- test:
    name: "获取某个流程实例"
    api: api/esignDocs/flow/owner_getWorkflowInstance.yml
    variables:
      processInstanceId: $processInstanceIdCommon
      workflowConfigCode: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - len_eq: [content.data.nodeInstanceList, 1]
      - eq: [content.data.nodeInstanceList.0.nodeInstanceStatus, "6"]

- test:
    name: "流程终止"
    api: api/esignDocs/docFlow/manage_updateDocProcessStatus.yml
    variables:
      flowId: $flowIdCommon
      auditResult: "a"
      flowOperationType: 2
      whetherTerminationWf: true
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data, true]

- test:
    name: "check列表状态"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $flowIdCommon
      flowStatus: "a"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.flowStatusName, "已终止"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, null]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]