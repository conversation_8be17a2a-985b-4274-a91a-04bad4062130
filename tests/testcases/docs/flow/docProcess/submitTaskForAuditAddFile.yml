#********创建审批环节可上传签署文件和附件业务模板场景
- config:
    name: "********审批环节可上传签署文件和附件业务模板场景"
    variables:
      autoPresetName: "自动化测试业务模板${get_randomNo()}"
      randomSignerId: ${get_randomNo_32()}
      fileFormat: 1   #pdf
      UserNameCommon: ${ENV(sign01.userName)}
      pdfFileName: "testppp.pdf"
      auditUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.account)}
      mainHost: ${ENV(esign.projectHost)}
      firstModelKey: "DOC_BATCH_SIGN"



- test:
    name: "新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]
    extract:
      - testPresetId: content.data.list.0.presetId





#- test:
#    name: "获取模板发起流程引擎模板列表"
#    api: api/esignDocs/businessPreset/getWorkflowList.yml
#    extract:
#      - firstModelKey: content.data.workflowList.0.modelKey
#    validate:
#      - eq: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - length_greater_than: [content.data.workflowList, 0]

- test:
    name: "第一步，填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId,
        "presetName": $autoPresetName,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": $firstModelKey,
        "workFlowModelStatus": 1,
        "signatoryCount": 0,
        "templateList": [],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "业务模板第二步：保存签署方式,修改允许审批人上传附件和正文"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
        businessTypeName: $autoPresetName
        businessTypeId: ""
        presetId: $testPresetId
        allowApproverAddFile: 1
        allowApproverAddAttachment: 1
        limitTemplateSealEnable: 0
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]


- test:
    name: "业务模板第三步：添加签署方（不指定）并启用业务模板"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 1,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "changeReason": null,
        "signerNodeList": []}
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]


- test:
    name: "查看初始状态新建的业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
      - _handColor1: content.data.signBusinessType.handColor
      - _limitTemplateSealEnable1: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor1: content.data.signBusinessType.templateSealColor
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $autoPresetName]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]

- test:
    name: "获取选择业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
    extract:
      - testPresetName: content.data.presetName
      - signerNodeList1: content.data.signerNodeList



- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
      - auditOrgCodeCommon: content.data.list.0.organizationCode
      - auditOrgNameCommon: content.data.list.0.organizationName
      - auditUserNameCommon: content.data.initiatorUserName
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: { }
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "选择业务模板暂存接口"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $testPresetName,
        "businessPresetUuid": $testPresetId,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "type": 3,
        "signersList": [],
        "appendList": []
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取内部用户列表"
    api: api/esignDocs/user/getUserInfo.yml
    variables:
      - userName: $UserNameCommon
      - userType: 1
      - sealType: 1
    extract:
      - signerUserCodeCommon: content.data.signerList.0.id
      - signerUserNameCommon: content.data.signerList.0.name
      - organizationCode: content.data.signerList.0.organizeCode
      - organizationName: content.data.signerList.0.organizeName

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "上传一个pdf签署文件"
    api: api/esignDocs/fileSystem/commonUpload.yml
    variables:
      fileName: $pdfFileName
      file_path: "data/$pdfFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]
    extract:
      - commonFileKey: content.data.fileKey


- test:
    name: "上传一个pdf签署文件"
    api: api/esignDocs/fileSystem/commonUpload.yml
    variables:
      fileName: $pdfFileName
      file_path: "data/$pdfFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]
    extract:
      - commonFileKey1: content.data.fileKey



- test:
    name: "上传一个pdf附件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $pdfFileName
      file_path: "data/$pdfFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]
    extract:
      - attachmentFileKey: content.data.fileKey


- test:
    name: "提交发起任务"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $autoPresetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "type": 3,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerType": 1,
                "signerTerritory": 1,
                "userName": $signerUserNameCommon,
                "userCode": $signerUserCodeCommon
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $commonFileKey,
              "fileName": "测试"
            }
          }
        ],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [ ],
          "nextAssigneeList": [
            {
              "nextAssignee": $signerUserCodeCommon,
              "nextAssigneeOrganizationCode": $auditOrgCodeCommon,
              "nextAssigneeName": $organizationName,
              "nextAssigneeOrganizationName": $organizationCode
            }
          ],
          "nodeConfigCode": "BMLDSP",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid1",
          "sendNotice": "0",
          "variables": { }
        }
      }
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(5)}


- test:
    name: 查询发起的结果-审批(发起人审批人签署人是同一个)
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: $_flowId
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowStatus, 2]
      - eq: [content.data.hasPermission, true]
      - eq: [content.data.hasWorkflow, true]
      - eq: [content.data.message, null]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserNameCommon"]
      - len_eq: [content.data.fillingUserList, 0]
      - len_eq: [content.data.signerList, 1]
      - eq: [content.data.signerList.0.signerName, "$initiatorUserNameCommon"]



- test:
    name: "列表查询-flowid+状态"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $_flowId
      flowStatus: 2
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowId]
      - contains: [content.data.list.0.flowStatusName, "审批"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, "$auditUserNameCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserCodeCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]




- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $autoPresetName
    extract:
      processInstanceIdCommon: content.data.list.0.processInstanceId
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "成功" ]
      - gt: [ content.data.total, 0 ]


- test:
    name: "查询流程实例-审核环节"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    extract:
      - taskIdCommon: content.data.nodeInstance.taskId
      - requestUrl: content.data.nodeInstance.navigationUrl
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



#esign-docs/flow/batchSign/submitTaskForAudit
- test:
    name: "审批提交"
    api: api/esignDocs/flow/submitTaskForAuditAddFile.yml
    variables:
      auditOpinion: "自动化测试审核提交"
      businessId: $batchTemplateInitiationUuid1
      carbonCopyList: []
      processInstanceId: $processInstanceIdCommon
      requestUrl: $requestUrl
      sendNotice: 1
      nextAssigneeList: [
            {
              "nextAssignee": $signerUserCodeCommon,
              "nextAssigneeOrganizationCode": $auditOrgCodeCommon,
              "nextAssigneeName": $organizationName,
              "nextAssigneeOrganizationName": $organizationCode
            }
          ]
      subBusinessId: $batchTemplateInitiationUuid1
      approverFileRequest: {
        "flowId": $flowIdCommon,
        "nodeKey": "",
        "approverFileList": [
          {
            "fileName": "123.pdf",
            "fileKey": $commonFileKey1,
            "approverFileId": "",
            "fileType": 1,
            "delete": false
          },
          {
            "fileName": "租赁合同.pdf",
            "fileKey": $attachmentFileKey,
            "approverFileId": "",
            "fileType": 2,
            "delete": false
          }
        ],
        "saveType": 1
      }
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "审批提交"
    api: api/esignDocs/flow/submitTaskForAuditAddFile.yml
    variables:
      auditOpinion: "自动化测试审核提交"
      businessId: $batchTemplateInitiationUuid1
      carbonCopyList: []
      processInstanceId: $processInstanceIdCommon
      requestUrl: $requestUrl
      sendNotice: 1
      nextAssigneeList: []
      subBusinessId: $batchTemplateInitiationUuid1
      approverFileRequest: {
        "flowId": $flowIdCommon,
        "nodeKey": "",
        "approverFileList": [],
        "saveType": 1
      }
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: 签署个人-检查审批过程上传签署文件和附件
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $flowIdCommon
      - organizeCode: ""
      - approvalProcessId: ""
      - requestSource: 2
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $processId ]
      - eq: [ content.data.aiHandEnable, 1 ]
      - eq: [ content.data.handScope, "1,2" ]
      - eq: [ content.data.aiHandScope, "1,2" ]
      - eq: [ content.data.templateSealScope, "1,2" ]
      - eq: [ content.data.customSealScope, "1,2" ]
      - eq: [ content.data.aiHandEnable, 1 ]
      - eq: [ content.data.docInfo.1.fileName, "123.pdf"]
      - eq: [ content.data.attachmentDocInfo.0.originFileKey, $attachmentFileKey]
      - eq: [ content.data.handColor, $_handColor1 ]
      - eq: [ content.data.templateSealColor, $_templateSealColor1 ]
      - eq: [ content.data.limitTemplateSealEnable, $_limitTemplateSealEnable1 ]