- config:
    name: "作废流程中部分文件"
    variables:
      - fileKey0: ${ENV(fileKey)}
      - fileKey1: ${ENV(1PageFileKey)}
      - fileKey2: ${ENV(2PageFileKey)}
      - fileKey3: ${ENV(3PageFileKey)}
      - fileKey4: ${ENV(fileKeyJpg)}
      - ofdFileKey01: ${ENV(ofdFileKey)}
      - ofdFileKey02: ${ENV(1PageOFDFileKey)}
      - organizationCodeInit: ${ENV(sign01.main.orgCode)}
      - userCodeSigner: ${ENV(sign01.userCode)}
      - customNoSigner: ${ENV(sign01.accountNo)}
      - userMobileSigner: ${ENV(sign01.mobile)}
      - randomCount: ${getDateTime()}
      - subject1: "部分作废测试流程-$randomCount"
      - presetName1: "支持部分作废业务模板-$randomCount"
      - subject2: "不支持部分作废测试流程-$randomCount"
      - presetName2: "不支持部分作废业务模板-$randomCount"
      - subject3: "ofd部分作废测试流程-$randomCount"
      - presetName3: "ofd部分作废业务模板-$randomCount"
      - reason01: "自动化测试-部分作废"
      - reason02: "作废测试"
      - reason03: "ofd自动化测试-部分作废"


# Setup: 创建支持部分作废的业务模板
- test:
    name: "setup-新建一个业务模板，支持部分作废"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName1
    extract:
      - testPresetId1: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName1]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId1: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName1]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName1
      presetId_addDetail: $testPresetId1
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]




- test:
    name: "setup-业务模板配置-第二步：设置业务模板支持作废流程中部分文件开关开启"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId1
      presetId: $testPresetId1
      allowPartRevoke: 1
      forceReadingTime: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}

# Setup: 创建签署中流程
- test:
    name: "setup1-创建签署流程"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId1
      subject: $subject1
      signFiles: [ { "fileKey": $fileKey0 },{ "fileKey": $fileKey1 },{ "fileKey": $fileKey2 } ]
      manualConfig: { "startMode": 0,"finishMode": 1 }
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey2,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]}
        ],  "signNode": 1,"tspId": "LOCAL_DEFAULT_TSP", "userType": 1,"userCode": $userCodeSigner }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId01: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(5)}

#签署中流程作废-直接作废
- test:
    name: "TC1-作废流程中的部分文件-不传文件filekey直接作废"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      - "flowId": $signFlowId01
      - "reason": "$reason01"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]
    teardown_hooks:
      - ${sleep(10)}


#  验证原流程状态为"已作废"
- test:
    name: "TC2-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "5"]
      - eq: [content.data.list.0.flowStatusName, "已作废"]

# Setup: 创建多文件签署流程
- test:
    name: "setup1-创建多文件签署流程"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId1
      subject: $subject1
      signFiles: [ { "fileKey": $fileKey0 },{ "fileKey": $fileKey1 },{ "fileKey": $fileKey2 } ,{ "fileKey": $fileKey3 }]
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey0,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
        { "fileKey": $fileKey1,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
        { "fileKey": $fileKey2,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] },
        { "fileKey": $fileKey3,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]}
        ], "autoSign": 1, "signNode": 1,"tspId": "LOCAL_DEFAULT_TSP", "userType": 1,"userCode": $userCodeSigner }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId01: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(20)}


#获取流程作废详情
- test:
    name: "TC1-我管理的-获取作废详情"
    api: api/esignDocs/flow/manage_revokeDetail.yml
    variables:
      json: {
        "params": {
          "flowId": $signFlowId01
        }
      }
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
    extract:
      signedFileKey0: content.data.signedFileList.0.fileKey
      signedFileKey1: content.data.signedFileList.1.fileKey
      signedFileKey2: content.data.signedFileList.2.fileKey
      signedFileKey3: content.data.signedFileList.3.fileKey

#签署完成流程作废
- test:
    name: "TC2-作废流程中的部分文件-不传文件filekey-报错"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
    validate:
      - eq: [ "content.message","作废流程文件不能为空" ]
      - eq: [ "content.status",1709169 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC-作废流程中部分文件filekey是原文件（不是签署后的文件）"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      revokeSignFiles: [
          {
            "signedFileKey": $fileKey3
          }
        ]
    validate:
      - contains: [ "content.message","不是流程的已签署文件" ]
      - eq: [ "content.status",1709171 ]
      - eq: [ "content.data",null ]

- test:
    name: "TC-作废流程中部分文件filekey-不在流程中"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      revokeSignFiles: [
          {
            "signedFileKey": $fileKey4
          }
        ]
    validate:
      - contains: [ "content.message","不是流程的已签署文件" ]
      - eq: [ "content.status",1709171 ]
      - eq: [ "content.data",null ]

# 作废流程中的第一个文件
- test:
    name: "TC3-作废流程中的部分文件（仅作废第一个文件）"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "$reason01"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          }
        ]
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.success, True]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}

#  验证原流程状态为"作废中"
- test:
    name: "TC4-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]


#  查看签署发起的作废流程
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0作废中状态，另外3个文件是正常状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName01: content.data.flowName
      processId01: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 2]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 1]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 1]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName01"]
      - eq: [content.data.revokeProcessInfo.0.reason, "$reason01"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]


#验证原流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", "$signFlowId01" ]
      - eq: [ "content.data.total", 1 ]

#验证未签署的作废流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.signFileProcessVOList", null ]


# 作废作废中文件
- test:
    name: "TC-作废作废中的文件"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "作废作废中的文件"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          }
        ]
    validate:
      - contains: [content.message, "当前流程状态不支持作废"]
      - eq: [content.success, False]
      - eq: [content.status, 1607005]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}

#  签署发起的作废流程
- test:
    name: setup-查询作废流程1详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $processId01
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $processId01 ]
      - eq: [ content.data.subject, "【作废】$flowName01" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey0 ]

- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $processId01
      organizeCode: ""
    extract:
      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $processId01
      organizeCode: ""
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: setup-进行账密意愿
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]
      - eq: [ content.data,'' ]

- test:
    name: setup-作废流程1签署
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      accountCode: $userCodeSigner
      applyId: $willApplyIdInner0
      tmpSealId: { "personSealId": $innerPsnSealId0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
      processUUId: $processId01
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - ne: [ content.data.taskSignResult, [ ] ]
    teardown_hooks:
      - ${sleep(10)}




- test:
    name: "TC-验证原流程的状态为部分作废"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 14]
      - eq: [content.data.list.0.flowStatusName, "部分作废"]

- test:
    name: 修改作废类型的合同到期时间signFlowExpireTime
    variables:
      signFlowId: $signFlowId01
      businessNo: ""
      signFlowExpireTime: "2053-10-01 14:00:00"
      contractExpireTime: "2051-10-01 13:00:00"
    api: api/esignDocs/signFlow/update.yml
    validate:
      - eq: [content.code, 1630027]
      - eq: [content.message, "当前流程状态不支持修改签署截止日期"]
      - eq: [content.data, null]


#  第一次作废流程签署完成后，查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0已作废状态，另外3个文件是正常状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName01: content.data.flowName
      processId01: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]  #已作废状态
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 1]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 1]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName01"]
      - eq: [content.data.revokeProcessInfo.0.reason, "$reason01"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]
#第一次作废签署后验证原流程和作废流程在文件中展示
#验证原流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId01 ]

#验证未签署的作废流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId01 ]

# 作废已作废的文件
- test:
    name: "TC-作废已作废的文件"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "作废已作废的文件"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          }
        ]
    validate:
      - contains: [content.message, "已作废"]
      - eq: [content.success, False]
      - eq: [content.status, 1709172]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}

# 继续作废第二个文件
- test:
    name: "TC-继续作废第二个文件"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "继续作废第二个文件"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey1
          }
        ]
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.success, True]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}


#  验证原流程状态为"作废中"
- test:
    name: "TC-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]

#  查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0已作废状态，signedFileKey1作废中，另外2个文件是正常状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName02: content.data.flowName
      processId02: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 2]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 1]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName02-2"]
      - eq: [content.data.revokeProcessInfo.0.reason, "继续作废第二个文件"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]

#第二次作废签署前验证原流程和作废流程在文件中展示
#验证原流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId01 ]

#验证已签署的作废流程1在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId01 ]


#验证已签署的作废流程2在已签文件不展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId02
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.signFileProcessVOList", null ]

#  签署发起的作废流程
- test:
    name: TC-查询作废流程2详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $processId02
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $processId02 ]
      - eq: [ content.data.subject, "【作废】$flowName02-2" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey1 ]

- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $processId02
      organizeCode: ""
    extract:
      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $processId02
      organizeCode: ""
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: setup-进行账密意愿
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]
      - eq: [ content.data,'' ]

- test:
    name: setup-作废流程2签署
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      accountCode: $userCodeSigner
      applyId: $willApplyIdInner0
      tmpSealId: { "personSealId": $innerPsnSealId0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
      processUUId: $processId02
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - ne: [ content.data.taskSignResult, [ ] ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: "TC-验证原流程的状态为部分作废"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 14]
      - eq: [content.data.list.0.flowStatusName, "部分作废"]



#  第二次作废流程签署完成后，查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0已作废状态，signedFileKey1已作废状态，另外2个文件是正常状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName01: content.data.flowName
      processId01: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]  #已作废状态
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 3]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 1]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName02-2"]
      - eq: [content.data.revokeProcessInfo.0.reason, "继续作废第二个文件"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]
      - eq: [content.data.revokeProcessInfo.1.flowSubject, "【作废】$flowName01"]
      - eq: [content.data.revokeProcessInfo.1.reason, "$reason01"]
      - ne: [content.data.revokeProcessInfo.1.initiatorTime, ""]

#第二次作废签署后验证原流程和作废流程在文件中展示
#验证原流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId01 ]

#验证已签署的作废流程1在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId01 ]


#验证已签署的作废流程2在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId02 ]

# 作废文件含已作废文件和正常文件
- test:
    name: "TC-作废文件含已作废文件"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "作废文件含已作废文件"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey1
          },          {
            "signedFileKey": $signedFileKey2
          },          {
            "signedFileKey": $signedFileKey3
          }
        ]
    validate:
      - contains: [ "content.message","已作废" ]  #签署文件$signedFileKey1已作废
      - eq: [ "content.status",1709172 ]
      - eq: [ "content.data",null ]

# 修改业务模板允许部分作废开关为关闭后，继续作废剩余文件
- test:
    name: TC-停用业务模板
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId_blockUp: $testPresetId1
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]


- test:
    name: "setup-业务模板配置-第二步：设置业务模板支持作废流程中部分文件开关关闭"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId1
      presetId: $testPresetId1
      allowPartRevoke: 0
      forceReadingTime: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC-继续作废剩余文件，修改业务模板作废部分文件开关不影响，保存的是流程发起时业务模板快照"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "继续作废剩余文件"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey2
          },          {
            "signedFileKey": $signedFileKey3
          }
        ]
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.success, True]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}


#  验证原流程状态为"作废中"
- test:
    name: "TC-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]

#  查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0已作废状态，signedFileKey1已作废中，另外2个文件是作废中状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName03: content.data.flowName
      processId03: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 3]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 2]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 2]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName03-3"]
      - eq: [content.data.revokeProcessInfo.0.reason, "继续作废剩余文件"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]

#第三次作废签署前验证原流程和作废流程在文件中展示
#验证原流程在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId01 ]

#验证已签署的作废流程1在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId01 ]


#验证已签署的作废流程2在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId02
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId02 ]


#验证已签署的作废流程3在已签文件中不展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId03
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.signFileProcessVOList", null ]

#  签署发起的作废流程
- test:
    name: TC-查询作废流程3详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $processId03
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $processId03 ]
      - eq: [ content.data.subject, "【作废】$flowName03-3" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey2 ]
      - eq: [ content.data.attachmentDocInfo.1.fileKey, $signedFileKey3 ]

- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $processId03
      organizeCode: ""
    extract:
      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $processId03
      organizeCode: ""
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: setup-进行账密意愿
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]
      - eq: [ content.data,'' ]

- test:
    name: setup-作废流程3签署
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      accountCode: $userCodeSigner
      applyId: $willApplyIdInner0
      tmpSealId: { "personSealId": $innerPsnSealId0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
      processUUId: $processId03
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - ne: [ content.data.taskSignResult, [ ] ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: "TC-验证原流程的状态为已作废"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 5]
      - eq: [content.data.list.0.flowStatusName, "已作废"]

#第三次作废签署后验证原流程和作废流程在文件中展示
#验证原流程在已签文件中不展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.signFileProcessVOList", null ]

- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $signFlowId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0 ]
      - eq: [ "content.data.signFileProcessVOList", null ]

#验证已签署的作废流程1在已签文件中展示
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       processId: $processId01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId01 ]


#验证已签署的作废流程2在已签文件中展示
- test:
    name: "我的文件-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/owner_list.yml
    variables:
       processId: $processId02
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId02 ]


#验证已签署的作废流程3在已签文件中不展示
- test:
    name: "我的文件-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/owner_list.yml
    variables:
       processId: $processId03
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 1 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $processId03 ]

#  第三次作废流程签署完成后，查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-文件全部已作废状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]  #已作废状态
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 3]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 3]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 3]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName03-3"]
      - eq: [content.data.revokeProcessInfo.0.reason, "继续作废剩余文件"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]
      - eq: [content.data.revokeProcessInfo.1.flowSubject, "【作废】$flowName02-2"]
      - eq: [content.data.revokeProcessInfo.1.reason, "继续作废第二个文件"]
      - ne: [content.data.revokeProcessInfo.1.initiatorTime, ""]
      - eq: [content.data.revokeProcessInfo.2.flowSubject, "【作废】$flowName01"]
      - eq: [content.data.revokeProcessInfo.2.reason, "$reason01"]
      - ne: [content.data.revokeProcessInfo.2.initiatorTime, ""]




# 创建不支持部分作废的业务模板
- test:
    name: "setup-新建一个业务模板，不支持部分作废"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName2
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName2
    extract:
      - testPresetId2: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName2]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - testBusinessTypeId2: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId2]
      - eq: [content.data.presetName, $presetName2]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName2
      presetId_addDetail: $testPresetId2
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]




- test:
    name: "setup-业务模板配置-第二步：设置业务模板支持作废流程中部分文件开关关闭"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName2
      businessTypeId: $testBusinessTypeId2
      presetId: $testPresetId2
      allowPartRevoke: 0  #不支持部分作废
      forceReadingTime: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId2
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}


#使用不支持部分作废模板创建流程
- test:
    name: "setup1-创建多文件签署流程"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId2
      subject: $subject2
      businessNo: "不支持部分作废"
      organizationCodeInitiator: $organizationCodeInit
      userCodeInitiator: $userCodeSigner
      signFiles: [ { "fileKey": $fileKey0 },{ "fileKey": $fileKey1 }]
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey0,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
        { "fileKey": $fileKey1,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
       ], "autoSign": 1, "signNode": 1,"tspId": "LOCAL_DEFAULT_TSP", "userType": 1,"userCode": $userCodeSigner }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId02: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(20)}

#获取流程作废详情
- test:
    name: "TC-我管理的-获取作废详情"
    api: api/esignDocs/flow/manage_revokeDetail.yml
    variables:
      json: {
        "params": {
          "flowId": $signFlowId02
        }
      }
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
    extract:
      signedFileKey0: content.data.signedFileList.0.fileKey
      signedFileKey1: content.data.signedFileList.1.fileKey

#作废签署完成流程作废
- test:
    name: "TC2-作废不支持部分作废流程-不传文件filekey-报错"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
    validate:
      - eq: [ "content.message","作废流程文件不能为空" ]
      - eq: [ "content.status",1709169 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC-作废不支持部分作废流程-作废文件列表为空-报错"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
      revokeSignFiles: []
    validate:
      - eq: [ "content.message","作废流程文件不能为空" ]
      - eq: [ "content.status",1709169 ]
      - eq: [ "content.data",null ]

- test:
    name: "TC-作废不支持部分作废流程-作废文件不在流程中-报错"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
      revokeSignFiles: [          {
            "signedFileKey": $fileKey3
          }]
    validate:
      - contains: [ "content.message","不是流程的已签署文件" ]
      - eq: [ "content.status",1709171 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC-作废不支持部分作废流程-作废文件时签署原文件-报错"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
      revokeSignFiles: [ {
        "signedFileKey": $fileKey0
      } ]
    validate:
      - contains: [ "content.message","不是流程的已签署文件" ]
      - eq: [ "content.status",1709171 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC-作废不支持部分流程-作废文件列表传一个文件-报错"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          }
        ]
    validate:
      - eq: [ "content.message","不支持作废部分签署文件" ]
      - eq: [ "content.status",1709173 ]
      - eq: [ "content.data",null ]

#修改业务模板的允许部分作废开关打开
- test:
    name: TC-停用业务模板
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId_blockUp: $testPresetId2
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]


- test:
    name: "setup-业务模板配置-第二步：设置业务模板支持作废流程中部分文件开关开启"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName2
      businessTypeId: $testBusinessTypeId2
      presetId: $testPresetId2
      allowPartRevoke: 1
      forceReadingTime: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId2
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}
      -
- test:
    name: "TC-作废不支持部分流程-作废文件列表传全部文件-作废成功（修改业务模板允许作废部分文件开关开启，不影响）"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      flowId: $signFlowId02
      reason: $reason02
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          },          {
            "signedFileKey": $signedFileKey1
          }
        ]
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: 查询签署回调-作废回调
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId02
      callbackEventType: "1"
    validate:
        - ne: [ "content.callbackEventOutputs", ""]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackDesc", "SIGN_FLOW_CANCEL" ]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackEnum", 6 ]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.processId", "$signFlowId02" ]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.projectId", "1000000" ]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.businessNo", "不支持部分作废" ]
        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.cancelCause", "$reason02" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "fileKey" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "$fileKey0" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "$fileKey1" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "signedFileKey" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "$signedFileKey0" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "$signedFileKey1" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "downloadUrl" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "downloadOuterUrl" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "signedDownloadUrl" ]
        - contains: [ "content.callbackEventOutputs.3.requestParamJson", "signedDownloadOuterUrl" ]
#本次插件没更新,无本次更新内容
#        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.fileKey", "$fileKey0" ]
#        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.signedFileKey", "$signedFileKey0"]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.downloadOuterUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.downloadUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.signedDownloadOuterUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.0.signedDownloadUrl", "http" ]
#        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.fileKey", "$fileKey1" ]
#        - eq: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.signedFileKey", "$signedFileKey1"]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.downloadOuterUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.downloadUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.signedDownloadOuterUrl", "http" ]
#        - contains: [ "content.callbackEventOutputs.3.signFlowEventCallbackBean.callBackProcessVO.revokeSignFiles.1.signedDownloadUrl", "http" ]

#  验证原流程状态为"作废中"
- test:
    name: "TC-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId02
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]

#  查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-签署文件处于作废中状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId02
    extract:
      _flowName0: content.data.flowName
      _processId0: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId02]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 2]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 2]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$_flowName0"]
      - eq: [content.data.revokeProcessInfo.0.reason, $reason02]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]


#  签署发起的作废流程
- test:
    name: TC-查询作废流程2详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $_processId0
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $_processId0 ]
      - eq: [ content.data.subject, "【作废】$_flowName0" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey0 ]
      - eq: [ content.data.attachmentDocInfo.1.fileKey, $signedFileKey1 ]

- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $_processId0
      organizeCode: ""
    extract:
      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $_processId0
      organizeCode: ""
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: setup-进行账密意愿
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]
      - eq: [ content.data,'' ]

- test:
    name: setup-作废流程2签署
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      accountCode: $userCodeSigner
      applyId: $willApplyIdInner0
      tmpSealId: { "personSealId": $innerPsnSealId0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
      processUUId: $_processId0
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - ne: [ content.data.taskSignResult, [ ] ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: "TC-验证原流程的状态为部分作废"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId02
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 5]
      - eq: [content.data.list.0.flowStatusName, "已作废"]



#  作废流程签署完成后，查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0、signedFileKey1已作废状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId02
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId02]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 3]  #已作废状态
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 3]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$_flowName0"]
      - eq: [content.data.revokeProcessInfo.0.reason, $reason02]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]

# 创建部分作废的ofd业务模板
- test:
    name: "setup-新建一个ofd业务模板，支持部分作废"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName3
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName3
    extract:
      - testPresetId3: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName3]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    extract:
      - testBusinessTypeId3: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId3]
      - eq: [content.data.presetName, $presetName3]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息ofd模板"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName3
      presetId_addDetail: $testPresetId3
      fileFormat: 2
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]




- test:
    name: "setup-业务模板配置-第二步：设置业务模板支持作废流程中部分文件开关开启"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName3
      businessTypeId: $testBusinessTypeId3
      presetId: $testPresetId3
      allowPartRevoke: 1  #支持部分作废
      forceReadingTime: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId3
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}


#使用ofd模板创建流程
- test:
    name: "setup1-创建ofd多文件签署流程-企业签署"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId3
      subject: $subject3
      businessNo: "ofd部分作废"
      organizationCodeInitiator: $organizationCodeInit
      userCodeInitiator: $userCodeSigner
      signFiles: [ { "fileKey": $ofdFileKey01 },{ "fileKey": $ofdFileKey02 }]
      tmp001: { "sealInfos": [
        { "fileKey": $ofdFileKey01,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" }]},
        { "fileKey": $ofdFileKey02,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" }]},
       ], "autoSign": 1, "signNode": 1,"tspId": "LOCAL_DEFAULT_TSP", "userType": 1,"userCode": $userCodeSigner,"organizationCode": $organizationCodeInit }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId03: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(20)}

#获取流程作废详情
- test:
    name: "TC-我管理的-获取作废详情"
    api: api/esignDocs/flow/manage_revokeDetail.yml
    variables:
      json: {
        "params": {
          "flowId": $signFlowId03
        }
      }
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
    extract:
      signedFileKey01: content.data.signedFileList.0.fileKey
      signedFileKey02: content.data.signedFileList.1.fileKey

#作废签署完成流程作废
- test:
    name: "TC2-作废ofd流程中的第一个文件"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId03
      reason1: $reason03
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey01
          }
        ]
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]
    teardown_hooks:
      - ${sleep(10)}



#  验证原流程状态为"作废中"
- test:
    name: "TC-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId03
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]

#  查看原流程详情
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey01作废中，signedFileKey02正常"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId03
    extract:
      flowNameOfd: content.data.flowName
      processIdOfd: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId03]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey01]
      - eq: [content.data.signedFileVOList.0.fileStatus, 2]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey02]
      - eq: [content.data.signedFileVOList.1.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowNameOfd"]
      - eq: [content.data.revokeProcessInfo.0.reason, "$reason03"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]


#  签署发起的作废流程
- test:
    name: TC-查询ofd作废流程1详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $processIdOfd
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $processIdOfd ]
      - eq: [ content.data.subject, "【作废】$flowNameOfd" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey01 ]


- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $processIdOfd
      organizeCode: $organizationCodeInit
    extract:
      - innerOfficSealId0: content.data.officialSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $processIdOfd
      organizeCode: $organizationCodeInit
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

#ofd作废不能指定签署区，签署时报错
#- test:
#    name: setup-进行账密意愿
#    api: api/esignManage/auth/signPasswordAuth.yml
#    variables:
#      manageToken: ${decryptTokenKey($willUrl0)}
#      applyId: $willApplyIdInner0
#    validate:
#      - eq: [ content.status,200 ]
#      - contains: [ content.message,'成功' ]
#      - eq: [ content.data,'' ]

#- test:
#    name: setup-作废ofd流程1签署
#    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
#    variables:
#      accountCode: $userCodeSigner
#      applyId: $willApplyIdInner0
#      tmpSealId: { "orgSealId": innerOfficSealId0 }
#      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
#      processUUId: $processIdOfd
#    validate:
#      - eq: [ content.status,200 ]
#      - eq: [ content.message,'成功' ]
#      - eq: [ content.data.executeStatus, 2 ]
#      - ne: [ content.data.taskSignResult, [ ] ]
#    teardown_hooks:
#      - ${sleep(10)}
#
#
#- test:
#    name: "TC-验证原流程的状态为部分作废"
#    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
#    variables:
#      flowIdOrProcessId: $signFlowId03
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "成功"]
#      - str_eq: [content.data.list.0.flowStatus, 14]
#      - eq: [content.data.list.0.flowStatusName, "部分作废"]
#
#
#
##  作废流程签署完成后，查看原流程详情
#- test:
#    name: "我管理的-正常查看电子签署详情-signedFileKey01已作废、signedFileKey1正常状态"
#    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
#    variables:
#      flowId: $signFlowId03
#    validate:
#      - eq: [status_code, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "成功"]
#      - eq: [content.data.flowId, $signFlowId03]
#      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey01]
#      - eq: [content.data.signedFileVOList.0.fileStatus, 3]  #已作废状态
#      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey02]
#      - eq: [content.data.signedFileVOList.1.fileStatus, 1]
#      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowNameOfd"]
#      - eq: [content.data.revokeProcessInfo.0.reason, "$reason03"]
#      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]