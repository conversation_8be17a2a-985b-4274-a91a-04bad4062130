- config:
    name: "页面-延期-合同到期时间延期"
    variables:
      num: ${get_snowflake()}
      signedFlowId: ${ENV(pdf.signed.processId)}
      now_time_1: ${getDateTime(0,1)}  # 当前时间
      future_time_1: ${getDateTime(1,1)}  # 当前时间+1天
      future_time_2: ${getDateTime(2,1)}  # 当前时间+2天
      _now_time_1: ${getDateTime(0,2)}  # 当前时间
      _future_time_1: ${getDateTime(1, 2)}
      _future_time_2: ${getDateTime(2, 2)}
      _future_time_30: ${addYearsMonths(0,1,2)} #一个月之后的日期
      past_time: ${getDateTime(-1, 1)}      # 当前时间-1天
      fileKey0: ${ENV(fileKey)}
      fileKey1: ${ENV(1PageFileKey)}
      fileKey2: ${ENV(2PageFileKey)}
      fileKey3: ${ENV(3PageFileKey)}
      userCodeSigner: ${ENV(sign01.userCode)}
      customAccountNo: ${ENV(sign01.accountNo)}
      testBusinessTypeId0: ${ENV(businessTypeCode)}
      subject0: "签署中流程延期-$num"
      subject1: "签署完成流程延期-$num"
      userCode0: ${ENV(sign01.userCode)}
      userCode1: ${ENV(userCodeNoSeal)}
      signedProcessId: ${ENV(pdf.signed.processId)}
      expireFlow: ${ENV(pdf.overdue.processId)}   #过期流程
      rejectProcessId: ${ENV(pdf.reject.processId)} #已拒签
      signFlowIdOverdue: ${ENV(pdf.revock.signing.processId)} #已过期
      revockedProcessId: ${ENV(pdf.revock.processId)} #已作废
      revockingProcessId: ${ENV(pdf.revocking.processId)} #作废中
      revockSigningProcessId: ${ENV(pdf.revock.signing.processId)} #作废签署中流程
      reason01: "自动化测试-部分作废"
      presetIdCommon: ${getPreset_flowModel()}
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(wsignwb01.userCode)}
      signerUserNameCommon: ${ENV(wsignwb01.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      mainHost: ${ENV(esign.projectHost)}
      auditUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.account)}
      newSignerSnapshotId: ${get_randomNo_32()}
      presetName0: "自动化-${get_randomNo_16()}"
      orgCode1: ${ENV(sign01.main.orgCode)}
      orgName1: ${ENV(sign01.main.orgName)}


#填写中、填写完成、审批中、签署中、已过期、已完成、部分作废流程有延期按钮，合同到期时间可修改
#作废签署流程、发起失败、已终止、已拒签、已作废、作废中状态无延期按钮

# Setup: 创建签署中流程
- test:
    name: "setup1-创建签署中流程-创建时没有合同到期时间"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      subject: $subject0
      signFiles: [ { "fileKey": $fileKey0 } ]
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey0,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]}
        ],  "signNode": 1,"userType": 1,"userCode": $userCodeSigner, "tspId": "LOCAL_DEFAULT_TSP" }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId01: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(5)}


#延期签署中流程
- test:
    name: TC1-延期合同到期时间为过去时间
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $past_time
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [content.status, 1709175]
      - eq: [content.success, False]
      - eq: [content.message, "合同到期日期必须晚于当前时间"]



- test:
    name: TC2-延期合同到期时间格式不正确，错误格式1
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: "2024/12/31 23:59:59"
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [content.status, 1709179]
      - eq: [content.success, False]
      - eq: [content.message, "合同到期时间格式不正确"]



- test:
    name: TC3-延期合同到期时间格式不正确，错误格式2
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: "2024年12月31"
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [content.status, 1709179]
      - eq: [content.success, False]
      - eq: [ content.message, "合同到期时间格式不正确" ]


- test:
    name: TC4-延期合同到期时间格式不正确，错误格式3
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: "2024-12T23:59:59"
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [content.status, 1709179]
      - eq: [content.success, False]
      - eq: [ content.message, "合同到期时间格式不正确" ]

- test:
    name: TC5-延期合同到期时间格式只有年月日-延期签署中合同
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $_future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证延期后的日期
- test:
    name: TC6-验证延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId01
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime,$_future_time_2 ]

- test:
    name: TC7-延期合同到期时间为当天时间-延期签署中合同
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $now_time_1
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证延期后的日期
- test:
    name: TC8-验证延期后的合同到期日期$now_time_1
    variables:
      flowId: $signFlowId01
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime,$_now_time_1 ]


#延期签署中流程
- test:
    name: TC9-延期签署中流程-将来时间
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $future_time_1
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证延期后的日期
- test:
    name: TC10-验证延期后的合同到期日期$future_time_1
    variables:
      flowId: $signFlowId01
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime,$_future_time_1 ]


# Setup: 创建签署完成流程
- test:
    name: "setup1-创建签署完成流程"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      subject: $subject1
      signFiles: [ { "fileKey": $fileKey0 } ]
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey0,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]}
        ], "autoSign": 1, "signNode": 1,"userType": 1,"userCode": $userCodeSigner , "tspId": "LOCAL_DEFAULT_TSP"}
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId02: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
      -
#查看签署完成流程未延期时的合同到期时间为空
- test:
    name: "TC11-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId02
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.contractExpirationTime, null ]

- test:
    name: "TC12-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId02
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 2 ]
      - eq: [ content.data.contractExpireTime, null ]
#验证延期前的日期
- test:
    name: TC13-验证延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId02
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - eq: [ json.data.contractExpireTime, null ]


#延期签署完成流程
- test:
    name: TC14-延期签署完成流程
    variables:
      flowIdDelay: $signFlowId02
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]


#验证延期后的日期
- test:
    name: TC15-验证延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId02
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]

- test:
    name: "TC16-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId02
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - contains: [ content.data.contractExpirationTime,  $_future_time_2 ]

- test:
    name: "TC17-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId02
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 2 ]
      - contains: [ content.data.contractExpireTime,  $_future_time_2 ]

#延期历数据-签署完成流程

- test:
    name: "TC18-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signedProcessId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
    extract:
        - signedFolwId: content.data.list.0.flowId

- test:
    name: TC19-延期签署完成流程-历史数据
    variables:
      flowIdDelay: $signedFolwId
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]


#验证延期后的日期
- test:
    name: TC20-验证延期后的合同到期日期-历史数据
    variables:
      flowId: $signedFolwId
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]

#创建填写完成流程
- test:
      name: setup-openapi查询电子签署业务模板详情-businessTypeCodeCBBT0-发起时指定合同到期时间
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          _tmp: "${getBusinessTypeId2(10)}"
          businessTypeCode: $_tmp
      extract:
        - businessTypeCodeCBBT0: content.data.businessTypeCode
        - signerId0: content.data.signerInfos.0.signerId
        - templateContentName0: content.data.fillingUserInfos.0.contentsControl.0.contentName
        - templateContentName1: content.data.fillingUserInfos.0.contentsControl.1.contentName
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCodeCBBT0 ]
          - eq: [ content.data.fileFormat, "PDF" ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]
          - eq: [ content.data.signerInfos.0.userType, 1 ]
          - eq: [ content.data.signerInfos.0.signMode, 0 ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ]
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 1]

- test:
    name: 造数据，填写完成流程-业务模板发起
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $businessTypeCodeCBBT0
      - subject: "TC3-填写完成流程延期"
      - autoFillAndSubmit: 1
      - editComponentValue: 1
      - CCInfoCBBT:
          userType: 1
          userCode: $userCode1
          departmentCode:
      - initiatorInfoCBBT:
          userType: 1
          customAccountNo: $customAccountNo
          userCode: ""
      - flowConfigsCBBT:
          flowConfig:
            subject: "TC10-业务模板发起内部用户签署"
            businessNo:
            remark:
            signFlowExpireTime:
            contractExpireTime: $future_time_2
            chargingType: 1
            startMode: 1
      - fillingUserInfosCBBT:
          - fillingUserType: 1
            signerId: $signerId0
            contentsControl:
              - contentName: $templateContentName0
                contentCode:
                contentValue: "siger-001"
              - contentName: $templateContentName1
                contentCode:
                contentValue: "测试文本域"
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
    extract:
      - signFlowId03: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

#验证填写完成流程延期前的日期
- test:
    name: TC21-验证填写完成延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId03
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]

- test:
    name: "TC22-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId03
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - contains: [ content.data.contractExpirationTime,  $_future_time_2 ]

- test:
    name: "TC23-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId03
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 8 ]
      - contains: [ content.data.contractExpireTime,  $_future_time_2 ]

#延期填写完成流程
- test:
    name: TC24-延期填写完成流程
    variables:
      flowIdDelay: $signFlowId03
      contractExpireTimeDelay: $now_time_1
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}


#验证填写完成流程延期后的日期
- test:
    name: TC25-验证填写完成延期后的合同到期日期
    variables:
      flowId: $signFlowId03
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_now_time_1]

- test:
    name: "TC26-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId03
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - contains: [ content.data.contractExpirationTime,  $_now_time_1 ]

- test:
    name: "TC27-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId03
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 8 ]
      - contains: [ content.data.contractExpireTime,  $_now_time_1 ]

- test:
      name: bizTemplatesDetail-openapi查询电子签署业务模板详情
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: "${getBusinessTypeId2(3)}"
      extract:
        - signerId0: content.data.signerInfos.0.signerId
        - businessTypeCodeCBBT0: content.data.businessTypeCode
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCodeCBBT0 ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.signerInfos.0.userType, 1 ]  #签署方类型为相对方
          - eq: [ content.data.signerInfos.0.signMode, 1 ]  #无序
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0] #所有人

- test:
    name: createByBizTemplate-电子签署流程-填写中流程-没有开启合同到期时间
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $businessTypeCodeCBBT0
      - initiatorInfoCBBT:
            userCode: ""
            customAccountNo: "$customAccountNo"
            userType: 1
      - subject: "createByBizTemplate--填写中流程"
      - autoFillAndSubmit: 0
      - editComponentValue: 0
      - fillingUserInfosCBBT: []
      - CCInfosCBBT: []
    extract:
      signFlowId04: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - len_eq: [content.data.fillingUserInfos, 2]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
      - len_gt: [content.data.fillingUserInfos.0.fillingUserId, 1]
    teardown_hooks:
      - ${sleep(5)}


#验证填写中流程延期前的日期
- test:
    name: TC28-验证填写完成延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId04
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - eq: [ json.data.contractExpireTime, null]
- test:
    name: "TC29-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId04
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.contractExpirationTime,  null ]

- test:
    name: "TC30-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId04
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 7 ]
      - eq: [ content.data.contractExpireTime,  null ]

#延期填写中流程
- test:
    name: TC31-延期填写中流程-成功
    variables:
      flowIdDelay: $signFlowId04
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证过期流程延期后的日期
- test:
    name: TC32-验证填写中延期后的合同到期日期$future_time_2
    variables:
      flowId: $signFlowId04
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]


#延期过期流程
- test:
    name: TC33-延期过期流程-成功
    variables:
      flowIdDelay: $expireFlow
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证过期流程延期后的日期
- test:
    name: TC34-验证过期流程延期后的合同到期日期$future_time_2
    variables:
      flowId: $expireFlow
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]


- test:
    name: "创建审批中流程-发起时固定时长"
    testcase: common/submit/getFlowId_collect_audit.yml
    teardown_hooks:
      - ${sleep(10)}
    output:
      - batchTemplateTaskNameCommon
      - batchTemplateInitiationUuid1

- test:
    name: "TC35-获取发起流程的flowId--审批中的列表详情"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      processInstanceIdCommon: content.data.list.0.processInstanceId
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.flowStatusName, "一级领导审批"]

#查看审批中流程详情
- test:
    name: "TC36-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processDefinitionKey, 'DOC_BATCH_SIGN' ]
      - contains: [ content.data.contractExpirationTime, $_future_time_30 ]

- test:
    name: "TC37-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $flowIdCommon
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 0 ]
      - contains: [ content.data.contractExpireTime, $_future_time_30 ]

#验证审批流程延期前的日期
- test:
    name: TC38-验证审批中延期后的合同到期日期$future_time_2
    variables:
      flowId: $flowIdCommon
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - eq: [ json.data.expireType, 'CREATE_AFTER']
      - eq: [ json.data.expireTimeUnitCode, 'Y_M_UNIT']
      - eq: [ json.data.year, 0]
      - eq: [ json.data.month, 1]
      - eq: [ json.data.day, 0]
      - eq: [ json.data.signFlowExpireTime, null]
      - contains: [ json.data.contractExpireTime, $_future_time_30]

#延期审批中流程
- test:
    name: TC39-延期审批中流程合同到期时间-成功
    variables:
      flowIdDelay: $flowIdCommon
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(5)}

#验证审批流程延期后的日期
- test:
    name: TC40-验证审批中延期后的合同到期日期$future_time_2
    variables:
      flowId: $flowIdCommon
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2]


#查看审批中流程详情
- test:
    name: "TC41-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processDefinitionKey, 'DOC_BATCH_SIGN' ]
      - contains: [ content.data.contractExpirationTime, $_future_time_2 ]

- test:
    name: "TC42-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $flowIdCommon
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus, 0 ]
      - contains: [ content.data.contractExpireTime, $_future_time_2 ]


#部分作废场景

# Setup: 创建多文件签署流程
- test:
    name: "setup1-创建多文件签署流程"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      _tmp: "${getBusinessTypeId2(12)}"
      businessTypeCode: $_tmp
      subject: $subject1
      signFiles: [ { "fileKey": $fileKey0 },{ "fileKey": $fileKey1 },{ "fileKey": $fileKey2 } ,{ "fileKey": $fileKey3 }]
      tmp001: { "sealInfos": [
        { "fileKey": $fileKey0,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
        { "fileKey": $fileKey1,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]},
        { "fileKey": $fileKey2,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] },
        { "fileKey": $fileKey3,"signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" }]}
        ], "autoSign": 1, "signNode": 1,"userType": 1,"userCode": $userCodeSigner }
      signerInfos: [ $tmp001 ]
    extract:
      - signFlowId01: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(30)}

#获取流程作废详情
- test:
    name: "TC43-我管理的-获取作废详情"
    api: api/esignDocs/flow/manage_revokeDetail.yml
    variables:
      json: {
        "params": {
          "flowId": $signFlowId01
        }
      }
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
    extract:
      signedFileKey0: content.data.signedFileList.0.fileKey
      signedFileKey1: content.data.signedFileList.1.fileKey
      signedFileKey2: content.data.signedFileList.2.fileKey
      signedFileKey3: content.data.signedFileList.3.fileKey

# 作废流程中的第一个文件
- test:
    name: "TC44-作废流程中的部分文件（仅作废第一个文件）"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      flowId: $signFlowId01
      reason1: "$reason01"
      signConfigs: true
      revokeSignFiles: [
          {
            "signedFileKey": $signedFileKey0
          }
        ]
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.success, True]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep(10)}

#  验证原流程状态为"作废中"
- test:
    name: "TC45-manage_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.flowStatus, "8"]
      - eq: [content.data.list.0.flowStatusName, "作废中"]

- test:
    name: TC46-延期作废中流程-报错
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1630028 ]
      - eq: [ json.message,"当前流程状态不支持修改合同到期时间" ]
      - eq: [ json.success,false ]

#  查看签署发起的作废流程
- test:
    name: "我管理的-正常查看电子签署详情-signedFileKey0作废中状态，另外3个文件是正常状态"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId01
    extract:
      flowName01: content.data.flowName
      processId01: content.data.revokeProcessInfo.0.processId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $signFlowId01]
      - eq: [content.data.signedFileVOList.0.fileKey, $signedFileKey0]
      - eq: [content.data.signedFileVOList.0.fileStatus, 2]
      - eq: [content.data.signedFileVOList.1.fileKey, $signedFileKey1]
      - eq: [content.data.signedFileVOList.1.fileStatus, 1]
      - eq: [content.data.signedFileVOList.2.fileKey, $signedFileKey2]
      - eq: [content.data.signedFileVOList.2.fileStatus, 1]
      - eq: [content.data.signedFileVOList.3.fileKey, $signedFileKey3]
      - eq: [content.data.signedFileVOList.3.fileStatus, 1]
      - eq: [content.data.revokeProcessInfo.0.flowSubject, "【作废】$flowName01"]
      - eq: [content.data.revokeProcessInfo.0.reason, "$reason01"]
      - ne: [content.data.revokeProcessInfo.0.initiatorTime, ""]


#  签署发起的作废流程
- test:
    name: setup-查询作废流程1详情
    api: api/esignSigns/process/detail.yml
    variables:
      processId: $processId01
    extract:
      - detailData001: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - eq: [ content.data.processId, $processId01 ]
      - eq: [ content.data.subject, "【作废】$flowName01" ]
      - contains: [ content.data,'processId' ]
      - eq: [ content.data.signStatus, 1 ]
      - eq: [ content.data.currentSignStatus, 1 ]
      - eq: [ content.data.attachmentDocInfo.0.fileKey, $signedFileKey0 ]

- test:
    name: list-查询流程的印章列表-processId01
    api: api/esignSigns/seals/list.yml
    variables:
      processId: $processId01
      organizeCode: ""
    extract:
      - innerPsnSealId0: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.personalSeal.realNamed,true ]
      - len_gt: [ content.data.personalSeal.seals,0 ]


- test:
    name: setup-获取意愿
    api: api/esignSigns/auth/willing.yml
    variables:
      processId: $processId01
      organizeCode: ""
    extract:
      - willUrl0: content.data.url
      - willApplyIdInner0: content.data.applyId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - contains: [ content.data,'needRealNameFlag' ]

- test:
    name: setup-进行账密意愿
    api: api/esignManage/auth/signPasswordAuth.yml
    variables:
      manageToken: ${decryptTokenKey($willUrl0)}
      applyId: $willApplyIdInner0
    validate:
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]
      - eq: [ content.data,'' ]

- test:
    name: setup-作废流程1签署
    api: api/esignSigns/process/standardFlowSign/standardFlowSign.yml
    variables:
      accountCode: $userCodeSigner
      applyId: $willApplyIdInner0
      tmpSealId: { "personSealId": $innerPsnSealId0 }
      signDocInfoRequests: ${getSignDocInfoRequestsDataByDetail($detailData001, $tmpSealId)}
      processUUId: $processId01
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.executeStatus, 2 ]
      - ne: [ content.data.taskSignResult, [ ] ]
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: "TC47-验证原流程的状态为部分作废"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId01
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 14]
      - eq: [content.data.list.0.flowStatusName, "部分作废"]

#延期部分作废流程
- test:
    name: TC48-延期部分作废流程-成功
    variables:
      flowIdDelay: $signFlowId01
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,True ]
    teardown_hooks:
      - ${sleep(10)}

#验证部分作废流程延期后的日期
- test:
    name: TC49-验证部分作废流程延期后的合同到期日期
    variables:
      flowId: $signFlowId01
    api: api/esignDocs/docFlow/manage_getDocFlowExpireTime.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ json.success,true ]
      - contains: [ json.data.contractExpireTime, $_future_time_2 ]

- test:
    name: TC50-延期作废签署流程-报错
    variables:
      flowIdDelay: $processId01
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1709176 ]
      - eq: [ json.message,"作废流程不支持更新" ]
      - eq: [ json.success,false ]



#签署中的作废流程
- test:
    name: "TC51-manage_getDocFlowLists-by-flowId-查询签署中作废流程Id"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $revockSigningProcessId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
    extract:
        - signFlowIdRevockSigning: content.data.list.0.flowId

- test:
    name: TC52-延期作废中流程-报错
    variables:
      flowIdDelay: $signFlowIdRevockSigning
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1709176 ]
      - eq: [ json.message,"作废流程不支持更新" ]
      - eq: [ json.success,false ]

#已拒签流程Id
- test:
    name: "TC53-manage_getDocFlowLists-by-flowId-查询已拒签流程Id"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $rejectProcessId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
    extract:
        - signFlowIdReject: content.data.list.0.flowId

- test:
    name: TC54-延期已拒签流程-报错
    variables:
      flowIdDelay: $signFlowIdReject
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1630028 ]
      - eq: [ json.message,"当前流程状态不支持修改合同到期时间" ]
      - eq: [ json.success,false ]


#已作废流程Id
- test:
    name: "TC55-manage_getDocFlowLists-by-flowId-查询已作废流程Id"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $revockedProcessId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
    extract:
        - signFlowIdRevocked: content.data.list.0.flowId

- test:
    name: TC56-延期已作废流程-报错
    variables:
      flowIdDelay: $signFlowIdRevocked
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1630028 ]
      - eq: [ json.message,"当前流程状态不支持修改合同到期时间" ]
      - eq: [ json.success,false ]

#作废中流程Id
- test:
    name: "TC57-manage_getDocFlowLists-by-flowId-查询作废中流程Id"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $revockingProcessId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
    extract:
        - signFlowIdRevocking: content.data.list.0.flowId

- test:
    name: TC58-延期作废中流程-报错
    variables:
      flowIdDelay: $signFlowIdRevocking
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1630028 ]
      - eq: [ json.message,"当前流程状态不支持修改合同到期时间" ]
      - eq: [ json.success,false ]

- test:
    name: TC59-延期流程不存在
    variables:
      flowIdDelay: "ff14f8"
      contractExpireTimeDelay: $future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [ json.status,1630028 ]
      - eq: [ json.message,"当前流程状态不支持修改合同到期时间" ]
      - eq: [ json.success,false ]

#审批+指定合同到期时间-延期
- test:
    name: "setup-新建一个pdf业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId0: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]




- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId0
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId0]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "case-第一步，填写基本信息,关联审批流程"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        presetId: $testPresetId0,
        presetName: $presetName0,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": DOC_BATCH_SIGN,
        "workFlowModelStatus": 1,
        "signatoryCount": 0,
        "templateList": [  ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message","成功"]
      - eq: ["content.status",200]




- test:
    name: "TC1-业务模板配置-第二步：设置指定合同到期时间"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      limitTemplateSealEnable: 0
      presetId: $testPresetId0
      processInitiationType: 3
      contractExpireSetting: "{\"enabled\":1,\"settings\":[{\"code\":\"CREATE_AFTER\",\"enabled\":0,\"value\":[]},{\"code\":\"SIGNED\",\"enabled\":0,\"value\":[]},{\"code\":\"CREATE\",\"enabled\":1,\"value\":[]}],\"notify\":{\"remindTime\":\"10:00\",\"remindCount\":3,\"remindBeforeDay\":30,\"remindEveryDay\":15}}"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "setup-第三步：添加业务模板配置签署人-发起时添加"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $testPresetId0,
        "status": 1,
        "needGather": 0,
        "needAudit": 0,
        "sort": 0,
        "signerList":
      }

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]




-   test:
        name: "获取发起人关联的组织信息"
        api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
        variables:
            businessPresetUuid: $testPresetId0
        extract:
            -   departmentCodeCommon: content.data.list.0.departmentCode
            -   departmentNameCommon: content.data.list.0.departmentName
            -   organizationCodeCommon: content.data.list.0.organizationCode
            -   organizationNameCommon: content.data.list.0.organizationName
            -   initiatorUserNameCommon: content.data.initiatorUserName
        validate:
            -   eq: ["content.message","成功"]
            -   ne: [content.data,""]
            -   eq: [content.success,True]

- test:
    name: "case-获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case-提交发起任务"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $testPresetId0,
        "saveSigners": null,
        "type": 3,
        "contractExpirationTime": $_future_time_2,
        "signersList": [
          {
            "signMode" : 0,
            "signNode" : 1,
            "signerList": [
              {
                "signerType": 1,
                "signerTerritory": 1,
                "draggable": true,
                "organizeCode": "",
                "userCode": "$signerUserCodeCommon",
                "id": "add-1",
                "sealTypeCode": "",
                "signatoryList": [ ],
                "sealTypeList": [ ],
                "accountList": [ ],
                "organizeList": [ ],
                "autoSign": 0,
                "onlyUkeySign": "2",
                "assignSigner": 1,
                "signerSnapshotId": "",
                "userName": "$signerUserNameCommon",
                "sealTypeName": null,
                "legalSign": 0
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $toSignFileKeyCommon,
              "fileName": "测试"
            }
          }
        ],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [ ],
          "nextAssigneeList": [
            {
              "nextAssignee": $signerUserCodeCommon,
              "nextAssigneeOrganizationCode": $orgCode1,
              "nextAssigneeName": $signerUserNameCommon,
              "nextAssigneeOrganizationName": $orgName1
            }
          ],
          "nodeConfigCode": "BMLDSP",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid2",
          "sendNotice": "0",
          "variables": { }
        }
      }
    extract:
      - signFlowId001: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC-我发起的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - contains: [ content.data.contractExpirationTime, $_future_time_2 ]

- test:
    name: "TC-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId001
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus,0 ]
      - contains: [ content.data.contractExpireTime, $_future_time_2 ]


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      processInstanceIdCommon: content.data.list.0.processInstanceId
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "查询流程实例--内部个人--发起人审核环节"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    extract:
      - taskIdCommon: content.data.nodeInstance.taskId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "查询驳回环节列表"
    api: api/esignDocs/flow/getTurnDownNodeList.yml
    variables:
      processInstanceId: $processInstanceIdCommon
    extract:
      - nodeCodeCommon: content.data.0.nodeCode
      - distTodoTaskIdCommon: content.data.0.id
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "审批驳回"
    api: api/esignDocs/flow/turnDownNodeInstance.yml
    variables:
      businessId: $signFlowId001
      nodeConfigCode: $nodeCodeCommon
      processInstanceId: $processInstanceIdCommon
      requestUrl: "${ENV(esign.projectHost)}/doc-manage-web/home-workFlow?workflowId=$processInstanceIdCommon&bussinessId=$batchTemplateInitiationUuid2&noWorkflowCodePageName=$nodeCodeCommon"
      sendNotice: 1
      distTodoTaskId: $distTodoTaskIdCommon
      todoTaskId: $taskIdCommon
      auditOpinion: "自动化测试-审批驳回"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]

- test:
    name: "TC-我发起的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.success, true ]
      - contains: [ content.data.contractExpirationTime, $_future_time_2 ]

- test:
    name: "TC-openapi查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      signFlowId: $signFlowId001
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.signFlowStatus,7 ]
      - contains: [ content.data.contractExpireTime, $_future_time_2 ]

#延期待发起流程，页面上不支持，webapi支持，需求这样定，其实是bUG

#延期待发起流程
- test:
    name: TC1-延期合同到期时间为过去时间
    variables:
      flowIdDelay: $signFlowId001
      contractExpireTimeDelay: $_future_time_2
    api: api/esignDocs/flow/docProcess/manage_delay.yml
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, True]
      - eq: [content.message, "成功"]