- config:
    name: "页面作废-发起方、签署方、抄送方有兼职组织，作废时发起方的兼职组织不存在"
    variables:
      num: ${get_snowflake()}
      presetIdCommon: ${getPreset(0,0)}
      batchTemplateTaskNameCommon: "文档自动化测试$num"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerAccountNoCommon: ${ENV(sign01.accountNo)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      telSignerTel: ${ENV(sign01.mobile)}
      telSignerTelCrypt: ${encrypt($telSignerTel)}
      toSignFileKeyCommon: ${ENV(fileKey)}
#      initiatorUserCodeCommon: ${ENV(csqs.userCode)}
#      initiatorUserNoCommon: ${ENV(csqs.accountNo)}
#      initiatorUserNameCommon: "测试签署"
      jzOrgName: "测试兼职人员企业$num"
      jzOrgNo: "CSJZRYZZ$num"
      orgCode: ${ENV(sign01.main.orgCode)}
      orgNo: ${ENV(sign01.main.orgNo)}
      orgName:  ${ENV(sign01.main.orgName)}
      tel1: '***********'
      tel1Crypt: "${encrypt(***********)}"
      userName1: "测试张${get_name(2)}"
      successMessage: "成功"
      successCode: 200

#发起方组织是兼职组织，作废时发起方的兼职组织不存在场景
- test:
    name: TC1-新建内部企业做兼职组织
    variables:
      data:
        domain: ${ENV(manage.domain)}
        params:
          accountNumber: $jzOrgNo
          contactAddress: ""
          dingTalk: ""
          feiShu: ""
          workWechat: ""
          licenseNumber: ""
          licenseType: "12"
          orderNum: 100
          organizationName: "$jzOrgName"
          organizationStatus: 1
          organizationTerritory: 1
          organizationType: 1
          parentOrganizationCode: 0
          parentOrganizationId: 0
    api: api/esignManage/OrgUser/org/saveOrganization.yml
    validate:
      - eq: [ json.status,$successCode ]
      - eq: [ json.message,$successMessage ]

- test:
    name: TC2-获取新建兼职组织id
    variables:
        apiOrganizationName: "$jzOrgName"  #组织名称、编码
        apiOrganizationTerritory: "1"  #组织类型(1内部 2外部)
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    validate:
      - eq: [ json.success, true  ]
      - eq: [ json.status, 200 ]
      - eq: [ json.data.0.deleted,"0" ]
      - eq: [ json.data.0.organizationName,$jzOrgName ]
    extract:
      - jzOrgId: content.data.0.id
      - jzOrgCode: content.data.0.organizationCode

- test:
    name: TC3-新建用户1作为发起人
    api: api/esignManage/InnerUsers/create.yml
    variables:
        data:
          [
            {
              "bankCardNo": "",
              "customAccountNo": "ces${random_str(7)}",
              "email": "",
              "licenseNo": "",
              "licenseType": "",
              "mainCustomOrgNo": "",
              "mainOrganizationCode": "$orgCode",
              "mobile": "$tel1",
              "name": "$userName1",
              "otherOrganization": [
                {
                  "otherCustomOrgName": "",
                  "otherCustomOrgNo": "",
                  "otherOrganizationCode": ""
                }
              ]
            }
          ]
    extract:
      userCode1: content.data.successData.0.userCode
      customAccountNo1: content.data.successData.0.customAccountNo
    validate:
      - eq: [ json.code,200 ]
      - eq: [ json.message,"成功" ]
      - eq: [ content.data.successCount,1 ]



- test:
    name: TC4-获取主组织id
    variables:
      apiOrganizationName: "$orgName"  #组织名称、编码
      apiOrganizationTerritory: "1"  #组织类型(1内部 2外部)
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
      - eq: [ json.data.0.deleted,"0" ]
      - eq: [ json.data.0.organizationName, $orgName ]
    extract:
      - orgId: content.data.0.id

- test:
    name: TC5-获取用户1的id
    variables:
      data: {
        "params": {
          "organizationId": "0",
          "currPage": 1,
          "searchType": "0",
          "pageSize": 10,
          "userName": "$userName1",
          "allChildOrganizationFlag": true,
          "userStatusList": [ "1" ] },
        "domain": "admin_platform" }
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    validate:
      - eq: [ status_code,200 ]
      - eq: [ json.status,200 ]
    extract:
      - userId1: json.data.list.0.id

- test:
    name: TC6-将jzOrgName设置为发起用户initiatorUserCodeCommon的兼职企业
    api: api/esignManage/OrgUser/user/updateUser.yml
    variables:
      data:
        domain: admin_platform
        params:
          userName: $userName1
          accountNumber: $customAccountNo1
          userType: "2"
          userTerritory: "1"
          userStatus: "1"
          userEmail: ""
          userMobile: "$tel1Crypt"
          dimissionTime: ""
          organizationId: $orgId
          ecUserParttimeIDList: [ $jzOrgId ]
          licenseType: "19"
          licenseNumber: ""
          bankCardNo: ""
          useLanguage: "zh-CN"
          userUkey: "-"
          userFeiShu: ""
          userDingTalk: ""
          userWorkWechat: ""
          userWechat: ""
          id: $userId1
          userCode: $userCode1
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: TC7-一步发起流程-签署完成流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      organizationCodeInitiator: $jzOrgCode
      departmentCodeInitiator: $jzOrgCode
      userCodeInitiator: $userCode1
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      businessNo: "businessNo$num"
      subject: "一步发起流程主题$num"
      autoSign: true
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
#      - eq: [content.data.signFlowStatus, 2]
    extract:
      - businessNo001: content.data.businessNo
      - signFlowId001: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC8-我管理的-正常查看流程状态是完成签署
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.list.0.initiatorUserName, $userName1]
      - eq: [content.data.list.0.initiatorOrganizeName, $jzOrgName]
      - eq: [content.data.list.0.initiatorDepartmentName, null]
      - eq: [content.data.list.0.flowId, $signFlowId001]
#      - eq: [content.data.list.0.flowStatus, "2"]

- test:
    name: TC9-将用户initiatorUserCodeCommon的兼职企业去除
    api: api/esignManage/OrgUser/user/updateUser.yml
    variables:
      data:
        domain: admin_platform
        params:
          userName: $userName1
          accountNumber: $customAccountNo1
          userType: "2"
          userTerritory: "1"
          userStatus: "1"
          userEmail: ""
          userMobile: "$tel1Crypt"
          dimissionTime: ""
          organizationId: $orgId
          ecUserParttimeIDList: [""]
          licenseType: "19"
          licenseNumber: ""
          bankCardNo: ""
          useLanguage: "zh-CN"
          userUkey: "-"
          userFeiShu: ""
          userDingTalk: ""
          userWorkWechat: ""
          userWechat: ""
          id: $userId1
          userCode: $userCode1
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC10-接口作废，initiatorInfo为兼职组织(未关联)
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "reason": "自动化测试-作废",
        "signFlowId": "$signFlowId001",
        "initiatorInfo": {
            "customAccountNo": "",
            "customDepartmentNo": "",
            "customOrgNo": "",
            "departmentCode": "$jzOrgCode",
            "organizationCode": "$jzOrgCode",
            "userCode": "$userCode1",
            "userType": 1
          },
      }
    validate:
      - eq: [ "content.code",1702615 ]
      - contains: [ "content.message","发起方$userName1($userCode1)组织关系不匹配"]
      - eq: [ "content.data", null ]


- test:
    name: TC11-将jzOrgName再次设置为发起用户initiatorUserCodeCommon的兼职企业
    api: api/esignManage/OrgUser/user/updateUser.yml
    variables:
      data:
        domain: admin_platform
        params:
          userName: $userName1
          accountNumber: $customAccountNo1
          userType: "2"
          userTerritory: "1"
          userStatus: "1"
          userEmail: ""
          userMobile: "$tel1Crypt"
          dimissionTime: ""
          organizationId: $orgId
          ecUserParttimeIDList: [ $jzOrgId ]
          licenseType: "19"
          licenseNumber: ""
          bankCardNo: ""
          useLanguage: "zh-CN"
          userUkey: "-"
          userFeiShu: ""
          userDingTalk: ""
          userWorkWechat: ""
          userWechat: ""
          id: $userId1
          userCode: $userCode1
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: TC12-接口作废，initiatorInfo为兼职组织(已关联)
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "reason": "自动化测试-作废",
        "signFlowId": "$signFlowId001",
        "initiatorInfo": {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "departmentCode": "$jzOrgCode",
          "organizationCode": "$jzOrgCode",
          "userCode": "$userCode1",
          "userType": 1
        },
      }
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "content.data.revokedSignFlowId", $signFlowId001 ]
      - ne: [ "content.data.signFlowId", "" ]


#清理数据
- test:
    name: "TC-后置操作：删除内部用户1"
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      userCode_innerUsersDelete: $userCode1
      customAccountNo_innerUsersDelete: $customAccountNo1
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [ content.data, "" ]

- test:
    name: "TC-后置操作：删除兼职企业"
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $jzOrgCode
      customOrgNo: $jzOrgNo
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [ content.data, "" ]