#场景说明：单方签署，有审批，单人审批之后是并行会签
- config:
    variables:
      autoPresetName: "文档自动化测试并行会签-${get_randomNo_16()}"
      randomSignerId0: ${get_randomNo_32()}
      randomSignerId1: ${get_randomNo_32()}
      autotestModelName: "文档自动化测试勿动勿复制哈"
      autotestModelKey: ${getWorkFlowModelKey($autotestModelName)}
      autotestWorkFlowModelName: "【电子签署】-$autotestModelName"
      userAccountKey: "ceswdzxzdhyhwgd1.account"
      userNameKey: "ceswdzxzdhyhwgd1.userName"
      userName_outer_Key: "wsignwb01.userName"
      userAccount_outer_Key: "wsignwb01.accountNo"
      userAccount: ${ENV($userAccountKey)}
      assignee: ${ENV(sign01.userCode)}
      assigneeOrganizationCode: ${getUserMainOrg($assignee)}
      userAccount_outer: ${ENV($userAccount_outer_Key)}
      signerUserName_inner: ${ENV($userNameKey)}
      signerUserCode_inner: ${get_inner_UserCode(userAccount=$userAccount)}
      signerUserName_outer: ${ENV($userName_outer_Key)}
      signerUserCode_outer: ${get_outer_UserCode(userAccount=$userAccount_outer)}

- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]


- test:
    name: "setup-创建无内容域无签署区的模板，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate1.yml
    extract:
      - newTemplateUuidCommon_template1
      - newVersionCommon_template1
      - templateNameCommon_template1


- test:
    name: "setup-关联模板、流程引擎到业务模板并保存"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - allowAddFile: 1
      - initiatorAll: 1
      - initiatorList: []
      - presetId: $testPresetId
      - presetName: $autoPresetName
      - templateList: [{
                         "templateId": $newTemplateUuidCommon_template1,
                         "templateName": $templateNameCommon_template1,
                         "version": $newVersionCommon_template1
                       }
      ]
      - workFlowModelKey: $autotestModelKey
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]



- test:
    name: "setup-业务模板第三步：添加签署方（内部个人不指定）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": $autotestModelKey,
        "workFlowModelName": $autotestWorkFlowModelName,
        "workFlowModelStatus": 1,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
        {
          "signNode": 2,
          "signMode": 1,
          "id": "node-0",
          "signerList": [
          {
            "templateInitiationSignersUuid": null,
            "signerId": $randomSignerId0,
            "signerSnapshotId": null,
            "signerTerritory": 1,
            "signerType": 1,
            "assignSigner": 0,
            "userName": null,
            "userCode": null,
            "userAccount": null,
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 0,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": [],
            "id": "0-0",
            "draggable": true
          }
          ]
        }
        ]
      }
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [content.success, true]



#以下为发起电子签署开始
- test:
    name: "setup-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId0: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList0: content.data.signerNodeList.0.signerList.0.signatoryList
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#暂存
- test:
    name: "setup-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $autoPresetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "templateInitiationSignersUuid": null,
            "signerId": $signerId0,
            "signerSnapshotId": null,
            "signerTerritory": 2,
            "signerType": 1,
            "assignSigner": 0,
            "userName": null,
            "userCode": null,
            "userAccount": null,
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 1,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": $signatoryList0
          }
          ],
          "id": 0,
          "draggable": false
        }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
      }
    extract:
      - templateInitiationSignersUuid_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-流程新增实例和获取对应流程的相关信息，submit提交时下一环节审批人使用默认的审批人"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
      modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName: content.data.nextNodeHandlerName
      - testThisFormUrl: content.data.nodeConfig.formUrl

- test:
    name: "setup-提交电子签署（单份发起）"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $autoPresetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": "",
        "wordList": [],
        "saveSigners": null,
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "templateInitiationSignersUuid": $templateInitiationSignersUuid_outer,
            "signerId": $signerId_outer,
            "signerSnapshotId": $signerSnapshotId_outer,
            "signerTerritory": 1,
            "signerType": 1,
            "assignSigner": 0,
            "userName": $signerUserName_inner,
            "userCode": $signerUserCode_inner,
            "userAccount": "",
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 1,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": $signatoryList0
          }]}
        ],
        "type": 3,
        "contentList": [],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [],
        "attachments": [],
        "ccInfos": [],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [],
          "nextAssigneeList": [
          {
            "nextAssignee": $testNextNodeHandlerCode,
            "nextAssigneeOrganizationCode": $testNextHandlerOrganizationCode,
            "nextAssigneeId": "",
            "nextAssigneeName": $testNextNodeHandlerName,
            "nextAssigneeOrganizationName": $testNextHandlerOrganizationName
          }
          ],
          "nodeConfigCode": $testNestNodeConfigCode,
          "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
          "sendNotice": "0",
          "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-获取刚发起的流程的flowId和processInstanceId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      - "flowId": ""
      - "flowName": $autoPresetName
      - "startTime": ""
      - "endTime": ""
      - "flowStatus": ""
      - "initiatorUserName": ""
      - "page": 1
      - "size": 10
    extract:
      - flowId: content.data.list.0.flowId
      - processInstanceId: content.data.list.0.processInstanceId
    teardown_hooks:
      - ${sleep(10)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-根据processInstanceId查询流程处理信息，获取需要的参数"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - "processInstanceId": $processInstanceId
    extract:
      - processDefinitionKey: content.data.processDefinitionKey
      - taskId: content.data.nodeInstance.taskId
      - businessId: content.data.nodeInstance.businessId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


#- test:
#    name: "TC-单人审批，并选择会签审批人"
#    api: api/esignDocs/flow/batchSign/submitTaskForAudit.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#      - "nodeConfigCode": "BHHQ"
#      - "businessId": $businessId
#      - "processDefinitionKey": $processDefinitionKey
#      - "processInstanceId": $processInstanceId
#      - "todoTaskId": $taskId
#      - "nextAssignee": $testNextNodeHandlerCode
#      - "nextAssigneeOrganizationCode": $testNextHandlerOrganizationCode
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC-获取可加签-减签会签环节实例"
#    api: api/esignDocs/flow/manage_getOperateMultiInstance.yml.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.signNodeInstanceList.0.nextAssignee",$testNextNodeHandlerCode ]
#      - eq: [ "content.data.signNodeInstanceList.0.nextAssigneeOrganizationCode",$testNextHandlerOrganizationCode ]
#
#
#- test:
#    name: "TC-会签加签"
#    api: api/esignDocs/flow/manage_addMultiInstance.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#      - "assignee": ${ENV(sign01.userCode)}
#      - "assigneeOrganizationCode": ${ENV(sign01.main.orgNo)}
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-会签减签"
#    api: api/esignDocs/flow/manage_deleteMultiInstance.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#      - "assignee": ${ENV(sign01.userCode)}
#      - "assigneeOrganizationCode": ${ENV(sign01.main.orgNo)}
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC-根据processInstanceId查询流程处理信息，获取需要的参数"
#    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#    extract:
#      - taskId1: content.data.nodeInstance.taskId
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC-并行会签审批通过"
#    api: api/esignDocs/flow/batchSign/submitTaskForAudit.yml
#    variables:
#      - "processInstanceId": $processInstanceId
#      - "nodeConfigCode": "SP"
#      - "businessId": $businessId
#      - "processDefinitionKey": $processDefinitionKey
#      - "processInstanceId": $processInstanceId
#      - "todoTaskId": $taskId1
#      - "nextAssignee": ""
#      - "nextAssigneeOrganizationCode": ""
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

