#场景说明：单方签署，无审批

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toSign.yml
    teardown_hooks:
      - ${sleep(10)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrg<PERSON>odeCommon
      - presetNameCommon


- test:
    name: "owner_getDocFlowLists-by-flowName"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowStatus, "2"]
      - eq: [content.data.list.0.flowStatusName, "签署中"]

#获取流程作废详情
- test:
    name: "我发起的-获取作废详情"
    api: api/esignDocs/flow/owner_revokeDetail.yml
    variables:
      json: {
        "params": {
          "flowId": $flowIdCommon
        }
      }
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
    extract:
      signedFileKey0: content.data.signedFileList.0.fileKey



- test:
    name: "owner-页面作废"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
      - "revokeSignFiles": [
      ]
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "owner_getDocFlowLists-by-flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 5]
      - eq: [content.data.list.0.flowStatusName, "已作废"]

- test:
    name: "owner-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.description, "自动化测试-作废"]


- test:
    name: "owner-作废已作废的文档流程"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
      - "revokeSignFiles": []
    validate:
      - eq: [ "content.message","当前流程状态不支持作废" ]
      - eq: [ "content.status",1607005 ]


- test:
    name: "owner-删除已作废的刚发起的电子签署"
    api: api/esignDocs/flow/docProcess/owner_deleteDocProcess.yml
    variables:
      - flowId: $flowIdCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "owner-作废已删除的文档流程"
    api: api/esignDocs/flow/docProcess/owner_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
      - "revokeSignFiles": []
    validate:
      - eq: [ "content.message","文档流程不存在" ]
      - eq: [ "content.status",1607001 ]


