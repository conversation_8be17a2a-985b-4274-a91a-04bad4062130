#场景说明：单方签署，无审批

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toSign.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "TC-我管理的-作废刚发起的电子签署"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
      - "revokeSignFiles": []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - str_eq: [content.data.list.0.flowStatus, 5]
      - eq: [content.data.list.0.flowStatusName, "已作废"]

- test:
    name: "我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - contains: [content.data.description, "作废"]


- test:
    name: "TC2-我管理的-作废已作废的文档流程"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
    validate:
      - eq: [ "content.message","当前流程状态不支持作废" ]
      - eq: [ "content.status",1607005 ]


- test:
    name: "删除已作废的刚发起的电子签署"
    api: api/esignDocs/flow/docProcess/manage_deleteDocProcess.yml
    variables:
      - flowId: $flowIdCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-我管理的-作废已删除的文档流程"
    api: api/esignDocs/flow/docProcess/manage_revoke.yml
    variables:
      - "flowId": $flowIdCommon
      - "reason": "自动化测试-作废"
    validate:
      - eq: [ "content.message","文档流程不存在" ]
      - eq: [ "content.status",1607001 ]