#文件配置：查看文件类型
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称1$docRandomNo"
      - docNameOfType: "0412延平自动化测试文件夹$docRandomNo"
      - docCodeOfType: "0412YPZDHCSWJLX$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
      - autoTestParentUid1: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup3-在上面的目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType
      - docCode: $docCodeOfType
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup4-列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfType
      - docType: 2
      - parentUid: $autoTestDocUuid1
    extract:
      - autoTestDocTypeUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfType ]

- test:
    name: "TC1-点击文件夹的查看"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.docUuid",$autoTestDocTypeUuid2 ]
      - eq: [ "content.data.docName",$docNameOfType ]

- test:
    name: "setup3-删除上面的文件夹"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

