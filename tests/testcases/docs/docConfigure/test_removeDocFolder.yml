#文件配置：移动文件夹
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹移动$docRandomNo"
      - docNameOfFolder2: "0412延平自动化测试文件夹移动2$docRandomNo"
      - docNameOfFolder3: "延平自动化文件夹移动本身$docRandomNo"
      - docNameOfFolder4: "延平自动化文件夹移动子文件夹$docRandomNo"
      - docNameOfFolder5: "延平自动化文件夹移动子子文件夹$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "TC1-移动文件夹_父文件夹名称必填"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid1
      - newParentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","新的父节点id不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "setup3-在根目录下新增一个文件夹2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup4-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]

- test:
    name: "TC2-目标文件夹选择"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid1
      - docName: $docNameOfFolder2
    validate:
      - eq: [ "content.data.0.docUuid",$autoTestDocUuid2 ]
      - len_eq: [ "content.data",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-移动文件夹_正常移动"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid2
      - newParentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup5-搜索根目录文件夹"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid2
      - docName: "文件类型"
    extract:
      - rootTestDocUuid: content.data.0.docUuid
    validate:
      - len_ge: [ "content.data",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-移动文件夹_目标文件夹可以选择根节点"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid2
      - newParentUid: $rootTestDocUuid
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup6-在根目录下新增一个文件夹3"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder3
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup7-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder3
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder3 ]

- test:
    name: "TC5-移动文件夹_目标文件夹不能是本身"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - docName: $docNameOfFolder3
    validate:
      - len_eq: [ "content.data",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-在文件夹3下新增一个文件夹4"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder4
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid3
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-移动文件夹_目标文件夹不能是子级文件夹"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - docName: $docNameOfFolder4
    validate:
      - len_eq: [ "content.data",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup9-列表搜索上个用例新增的的文件夹信息4"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder4
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder4 ]

- test:
    name: "setup10-在文件夹4下新增一个文件夹5"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder5
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid4
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-移动文件夹_目标文件夹不能是当前文件夹的下几层文件夹"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - docName: $docNameOfFolder5
    validate:
      - len_eq: [ "content.data",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup11-删除文件夹1"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-移动文件夹_移动到已删除的文件夹"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","父文件夹不存在或已被删除" ]
      - eq: [ "content.status",1603005 ]

- test:
    name: "setup12-查询文件夹3的更新人和更新时间"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
    extract:
      - updateUser: content.data.updateUser
      - gmtModified: content.data.gmtModified
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.docUuid",$autoTestDocUuid3 ]

- test:
    name: "TC9-移动不属于更新_查看详情不展示"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-移动不属于更新_查看详情不展示"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.updateUser",$updateUser ]
      - eq: [ "content.data.gmtModified",$gmtModified ]

- test:
    name: "setup13-删除文件夹2"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(1)}

#文件配置删除文件夹，用接口删除，随便传一个不存在的docUuid都会返回删除成功
#- test:
#    name: "setup14-删除文件夹3"
#    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
#    variables:
#      docUuid: d1ba189045050b85ce72aabe88003c64
#    validate:
#      - eq: [ "content.message","文件夹或文件类型不存在" ]
#      - eq: [ "content.status",1603001 ]