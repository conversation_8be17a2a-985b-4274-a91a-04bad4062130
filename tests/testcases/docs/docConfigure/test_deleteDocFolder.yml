#文件配置：删除文件夹
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹$docRandomNo"
      - docNameOfFolder2: "0412延平自动化测试文件夹2$docRandomNo"
      - docNameOfFolder3: "0412延平自动化测试文件夹3$docRandomNo"
      - docNameOfType4: "0412延平自动化测试文件类型4$docRandomNo"
      - docCodeOfType4: "ypzdhyd4$docRandomNo"
      - docNameOfType5: "0412延平自动化测试文件类型5$docRandomNo"
      - docCodeOfType5: "ypzdhyd5$docRandomNo"
      - commonTemplateName: "自动化测试模版-合同测试"
      - description: "自动化测试描述"
      - fileKey: ${ENV(fileKey)}
      - timePosX: 10
      - timePosY: 10

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup3-在文件夹1下新增一个文件夹2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup4-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]

- test:
    name: "TC1-判断文件夹和文件类型是否可以删除_正常删除_查询是否可删除"
    api: api/esignDocs/docConfigure/docCanDeleted.yml
    variables:
      docUuid: $autoTestDocUuid2
      docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.canDeleted",1 ]

- test:
    name: "TC1-删除文件夹_正常删除"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup5-在文件夹1下新增一个文件夹3"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder3
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup6-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder3
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder3 ]

- test:
    name: "setup7-在文件夹3下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType4
      - docCode: $docCodeOfType4
      - docType: 2
      - parentUid: $autoTestDocUuid3
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfType4
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocTypeUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfType4 ]

- test:
    name: "setup9-创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup10_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName$docRandomNo
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocTypeUuid4
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup11_添加签名区1"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方企业"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-删除文件夹_文件夹下的文件类型存在草稿状态模板_不支持删除"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","该文件夹下有模板不能删除，请联系模板管理员删除模板" ]
      - eq: [ "content.status",1603004 ]

- test:
    name: "TC2-判断文件夹和文件类型是否可以删除_有模板不能删除_查询是否可删除"
    api: api/esignDocs/docConfigure/docCanDeleted.yml
    variables:
      docUuid: $autoTestDocUuid3
      docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.canDeleted",0 ]

- test:
    name: "setup11_发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-删除文件夹_文件夹下存在发布状态模板"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","该文件夹下有模板不能删除，请联系模板管理员删除模板" ]
      - eq: [ "content.status",1603004 ]

- test:
    name: "setup12_停用草稿模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-删除文件夹_文件夹下深层次目录的文件类型下存在停用状态模板_不支持删除"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","该文件夹下有模板不能删除，请联系模板管理员删除模板" ]
      - eq: [ "content.status",1603004 ]

- test:
    name: "setup13_删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5-删除文件夹_文件夹下的文件类型下存在删除状态模板_支持删除"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup14-在文件夹1下新增一个文件类型2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType5
      - docCode: $docCodeOfType5
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-删除文件夹_文件夹下的文件类型不存在模板"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-删除文件夹_删除后的文件夹及子级无法搜索到"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","文件夹或文件类型不存在" ]
      - eq: [ "content.status",1603001 ]

- test:
    name: "TC8-删除文件夹_删除后的文件夹下文件类型无法搜索到"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid4
    validate:
      - eq: [ "content.message","文件夹或文件类型不存在" ]
      - eq: [ "content.status",1603001 ]

- test:
    name: "TC3-判断文件夹和文件类型是否可以删除_已删除删除_查询是否可删除"
    api: api/esignDocs/docConfigure/docCanDeleted.yml
    variables:
      docUuid: $autoTestDocUuid1
      docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.canDeleted",1 ]

- test:
    name: "TC9-删除文件夹_删除已删除的文件夹-幂等"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

