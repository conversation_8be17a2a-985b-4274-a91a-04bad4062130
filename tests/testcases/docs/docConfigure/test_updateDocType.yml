#文件配置：编辑文件类型
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称1$docRandomNo"
      - docTypeName1: "0412延平自动化测试文件类型名称1$docRandomNo"
      - docTypeCode1: "0412ypcszdh1$docRandomNo"
      - docTypeNameUpdate1: "0412延平自动化测试文件类型名称1更新$docRandomNo"
      - docTypeName2: "0412延平自动化测试文件类型名称2$docRandomNo"
      - docTypeCode2: "0412ypcszdh2$docRandomNo"
      - docTypeName3: "0412延平自动化测试  文件类型名称3$docRandomNo"
      - docTypeCode3: "0412ypcszdh3$docRandomNo"
      - docNameOfType: "0412延平自动化测试文件夹$docRandomNo"
      - docCodeOfType: "0412YPZDHCSWJLX$docRandomNo"
      - spaceChar: "   "
      - sendLevelFolderName: "0412延平自动化测试文件夹名称二级$docRandomNo"
      - thirdLevelFolderName: "0412延平自动化测试文件夹名称三级$docRandomNo"
      - fourLevelFolderName: "0412延平自动化测试文件夹名称四级$docRandomNo"
      - fiveLevelFolderName: "0412延平自动化测试文件夹名称五级$docRandomNo"
      - sixLevelFolderName: "0412延平自动化测试文件夹名称六级$docRandomNo"
      - sevenLevelFolderName: "0412延平自动化测试文件夹名称七级$docRandomNo"
      - eightLevelFolderName: "0412延平自动化测试文件夹名称八级$docRandomNo"
      - nineLevelFolderName: "0412延平自动化测试文件夹名称九级$docRandomNo"
      - sameName: "0412延平自动化测试相同名称$docRandomNo"
      - sameNameCode: "stmcypcs$docRandomNo"
      - sameName2: "0412延平自动化测试相同名称2$docRandomNo"
      - sameNameCode2: "stmcypcs2$docRandomNo"
      - idempotentName: "0412延平自动化测试幂等$docRandomNo"
      - idempotentCode: "0412ypzdhmd$docRandomNo"

- test:
    name: "setup11-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup12-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
      - autoTestParentUid1: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup1-在根目录下新增一个文件类型1"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docTypeName1
      - docCode: $docTypeCode1
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docTypeName1
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocTypeUuid1: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docTypeName1 ]

- test:
    name: "TC1-编辑文件夹_成功编辑"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docTypeNameUpdate1
      - docCode:
      - docUuid: $autoTestDocTypeUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-判断上个用例更新的文件类型信息是否正确"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.docName",$docTypeNameUpdate1 ]

- test:
    name: "setup3-删除上面的文件类型1"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocTypeUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-编辑文件夹_文件夹被删除"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docTypeName1
      - docCode:
      - docUuid: $autoTestDocTypeUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]
      - eq: [ "content.status",1603001 ]

- test:
    name: "setup4-在根目录下新增一个文件类型2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docTypeName2
      - docCode: $docTypeCode2
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup5-列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docTypeName2
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocTypeUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docTypeName2 ]

- test:
    name: "setup6-查询文件类型2编辑时间"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    extract:
      - gmtModified2: content.data.gmtModified
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-编辑文件类型_未做修改直接提交"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docTypeName2
      - docCode: $docTypeCode2
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-编辑文件类型_未做修改直接提交_判断更新时间"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: ["content.data.gmtModified",$gmtModified2]

- test:
    name: "TC4-编辑文件类型_文件类型名称必填"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName:
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","名称不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC5-编辑文件类型_文件类型名称两端包含空格"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $spaceChar$docTypeName2$spaceChar
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-编辑文件类型_文件类型名称两端包含空格_判断名称"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: ["content.data.docName",$docTypeName2]

- test:
    name: "TC6-编辑文件类型_输入文件类型名称中间包含空格"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docTypeName3
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-编辑文件类型_输入文件类型名称中间包含空格_判断名称"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: ["content.data.docName",$docTypeName3]

- test:
    name: "TC7-编辑文件类型_输入文件类型名称包含不被运行的特殊字符"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: "\\?aa"
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","名称不能包含特殊字符" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "setup7-在根目录下新增一个文件夹1"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $sameName
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-判断名称是否重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $sameName
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
      - docType: 2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",0 ]

- test:
    name: "TC8-编辑文件类型_文件类型名称可以和文件夹名称重复"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $sameName
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-编辑文件类型_文件类型名称可以和文件夹名称重复重复"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocTypeUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: ["content.data.docName",$sameName]

- test:
    name: "setup8-在根目录下新增一个文件类型3"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $sameName2
      - docCode: $sameNameCode2
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-编辑文件类型_文件类型名称不能重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $sameName2
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
      - docType: 2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",1 ]

- test:
    name: "setup9-列表搜索上个用例新增的的文件类型信息3"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sameName2
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocTypeUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$sameName2 ]

- test:
    name: "setup10-删除上面的文件类型3"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocTypeUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC10-编辑文件类型_和已删除的文件类型名称重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $sameName2
      - docCode:
      - docUuid: $autoTestDocTypeUuid2
      - docType: 2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",0 ]

- test:
    name: "setup13-创建二级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup14-列表搜索二级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup15-创建三级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup16-列表搜索三级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup17-创建四级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid3
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup18-列表搜索四级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup19-创建五级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid4
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup20-列表搜索五级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid5: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup21-创建六级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid5
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup22-列表搜索六级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid6: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup23-创建七级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid6
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup24-列表搜索七级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid7: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup25-创建八级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $eightLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid7
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup26-列表搜索八级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $eightLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid8: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup27-创建九级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $nineLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid8
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup28-列表搜索九级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $nineLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid9: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup29-编辑文件类型_文件类型名称和所属文件夹下深处的文件类型名称重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $idempotentName
      - docCode: $idempotentCode
      - docType: 2
      - parentUid: $autoTestDocUuid9
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup30-在根目录下新增一个文件类型4"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType
      - docCode: $docCodeOfType
      - docType: 2
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup31-列表搜索上个用例新增的的文件类型4信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfType
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocTypeUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfType ]

- test:
    name: "TC11-编辑文件类型_文件类型名称和所属文件夹下深处的文件类型名称重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $idempotentName
      - docCode:
      - docUuid: $autoTestDocTypeUuid4
      - docType: 2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",1 ]


- test:
    name: "setup32-删除上面的文件夹"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
