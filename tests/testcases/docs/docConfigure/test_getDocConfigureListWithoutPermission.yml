#文件配置：无权限文件配置列表搜索
- config:
    name: "文件配置的权限测试"
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称$docRandomNo"

- test:
    name: "setup2-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup1-查询新增的一个文件夹"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: ""
      - parentUid: ""
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
#      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-普通用户_配置权限_可操作"
    api: api/esignDocs/docConfigure/canManage.yml
    variables:
#      - account: "ceswdzxzdhyhwgd1.account"
#      - pwd: "ceswdzxzdhyhwgd1.password"
      - account: "${ENV(sign01.accountNo)}"
      - pwd: "${ENV(passwordEncrypt)}"
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.canManage",1 ]

- test:
    name: "TC6-删除上面的文件夹"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    teardown_hooks:
      - ${teardown_docConfigure_delete()}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "查询合同模板-结果排序验证"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 1
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "查询合同模板-返回结果为空的展示验证"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "NOTFOUND${get_randomNo_36()}"
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 1
      - size: 10
    validate:
      - eq: ["content.data.total", 0]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "列表结果验证合同类型和签署域数量"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 1
      - size: 10
    extract:
      - getTemplateUuid: content.data.list.0.templateUuid
      - getVersion: content.data.list.0.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


#- test:
#    name: "列表结果验证合同类型和签署域数量"
#    api: api/esignDocs/template/manage/templateDetail.yml
#    variables:
#      - templateUuid: $getTemplateUuid
#      - version: $getVersion
#
#    validate:
#      - length_greater_than_or_equals: ["content.data.signatoryList",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]


- test:
    name: "带搜索条件搜索后结果翻页_修改搜索条件再次搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合同"
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 2
      - size: 10
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "带搜索条件搜索后结果翻页_修改搜索条件再次搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "模版"
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 1
      - size: 10
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "查询合同模板_分页结果"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 3
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "查询合同模板_每页总条数展示"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 2
      - size: 10

    validate:
      - ge: ["content.data.total",0]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "查询合同模板_前后页按钮"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 2
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "查询合同模板_前往X页"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: 1
      - page: 3
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#- test:
#    name: "setup-新增的的文件夹信息"
#    api: api/esignDocs/docConfigure/addDocConfigure.yml
#    variables:
#      - docName: $docTypeName1
#      - docCode: $docTypeCode1
#      - docType: 1
#      - parentUid: null
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-列表搜索上个用例新增的的文件夹信息"
#    api: api/esignDocs/docConfigure/getDocConfigureList.yml
#    variables:
#      - docName: $docTypeName1
#      - docType: 1
#      - parentUid: null
#    extract:
#      - autoTestDocUuid2: content.data.0.docUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.0.docName",$docTypeName1 ]
#
#- test:
#    name: "setup-新增的的文件夹信息"
#    api: api/esignDocs/docConfigure/addDocConfigure.yml
#    variables:
#      - docName: $docTypeName2
#      - docCode: $docTypeCode2
#      - docType: 2
#      - parentUid: $autoTestDocUuid2
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-列表搜索上个用例新增的的文件夹信息"
#    api: api/esignDocs/docConfigure/getDocConfigureList.yml
#    variables:
#      - docName: $docTypeName2
#      - docType: 2
#      - parentUid: $autoTestDocUuid2
#    extract:
#      - autoTestDocUuid3: content.data.0.docUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.0.docName",$docTypeName2 ]
#
#
#- test:
#    name: "_只进行了文件类型条件搜索-选择具体的某个文件类型"
#    api: api/esignDocs/template/manage/templateList.yml
#    variables:
#      - docUuid: $autoTestDocUuid3
#      - templateName: null
#      - standard: null
#      - status: null
#      - minSignCount: null
#      - page: 1
#      - size: 10
#
#    validate:
#      - len_ge: [ "content.data.list",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "_只进行了文件类型条件搜索-选择具体的某个文件类型"
#    api: api/esignDocs/template/manage/templateList.yml
#    variables:
#      - docUuid: $autoTestDocUuid2
#      - templateName: null
#      - standard: null
#      - status: null
#      - minSignCount: null
#      - page: 1
#      - size: 10
#
#    validate:
#      - len_ge: [ "content.data.list",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "_只进行了模板名称条件搜索-搜索具体的文档名称"
#    api: api/esignDocs/template/manage/templateList.yml
#    variables:
#      - docUuid: null
#      - templateName: $commonTemplateName1
#      - standard: null
#      - status: null
#      - minSignCount: null
#      - page: 1
#      - size: 10
#
#    validate:
#      - contains: ["content.data.list.0.templateName", $commonTemplateName1]
#      - len_ge: [ "content.data.list",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "_先选择文件类型_文件夹_再输入模板名称组合搜索"
#    api: api/esignDocs/template/manage/templateList.yml
#    variables:
#      - docUuid: $autoTestDocUuid3
#      - templateName: $commonTemplateName1
#      - standard: null
#      - status: null
#      - minSignCount: null
#      - page: 1
#      - size: 10
#
#    validate:
#      - contains: ["content.data.list.0.templateName", $commonTemplateName1]
#      - len_ge: [ "content.data.list",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#
#- test:
#    name: "setup_已发布模版搜索"
#    api: api/esignDocs/template/manage/templateList.yml
#    variables:
#      - docUuid: null
#      - templateName: $commonTemplateName2
#      - standard: null
#      - status: null
#      - minSignCount: 1
#      - page: 1
#      - size: 10
#    extract:
#      - newTemplateUuid: content.data.list.0.templateUuid
#      - newVersion: content.data.list.0.version
#    validate:
#      - contains: ["content.data.list.0.templateName", $commonTemplateName2]
#      - len_ge: [ "content.data.list",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]