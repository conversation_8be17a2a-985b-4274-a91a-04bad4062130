#文件配置：新建同级文件夹
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称1$docRandomNo"
      - docNameOfFolder2: "0412延平自动化测试文件夹名称2$docRandomNo"
      - docNameOfFolder3: "0412延平自动化测试文件夹名称3$docRandomNo"
      - docNameOfFolder4: "0412延平自动化测试  文件夹名称3$docRandomNo"
      - docNameOfType: "0412延平自动化测试文件类型$docRandomNo"
      - docCodeOfType: "0412YPZDHCSWJLX$docRandomNo"
      - spaceChar: "   "
      - sendLevelFolderName: "0412延平自动化测试文件夹名称二级$docRandomNo"
      - thirdLevelFolderName: "0412延平自动化测试文件夹名称三级$docRandomNo"
      - fourLevelFolderName: "0412延平自动化测试文件夹名称四级$docRandomNo"
      - fiveLevelFolderName: "0412延平自动化测试文件夹名称五级$docRandomNo"
      - sixLevelFolderName: "0412延平自动化测试文件夹名称六级$docRandomNo"
      - sevenLevelFolderName: "0412延平自动化测试文件夹名称七级$docRandomNo"
      - eightLevelFolderName: "0412延平自动化测试文件夹名称八级$docRandomNo"
      - nineLevelFolderName: "0412延平自动化测试文件夹名称九级$docRandomNo"
      - tenLevelFolderName: "0412延平自动化测试文件夹名称十级$docRandomNo"
      - elevenLevelFolderName: "0412延平自动化测试文件夹名称十一级$docRandomNo"
      - sameName: "0412延平自动化测试相同名称$docRandomNo"
      - sameNameCode: "stmcypcs$docRandomNo"
      - idempotentName: "0412延平自动化测试幂等$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType:
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
      - autoTestParentUid1: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup3-在根目录下新增一个文件夹2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-判断两个文件夹是否同级"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUid2: content.data.0.docUuid
      - autoTestParentUid2: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]
      - eq: [ "content.data.0.parentUid",$autoTestParentUid1 ]

- test:
    name: "TC2-新建同级文件夹_文件夹名称必填"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName:
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","名称不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC3-新建同级文件夹_输入文件夹名称两端包含空格"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $spaceChar$docNameOfFolder3$spaceChar
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-新建同级文件夹_输入文件夹名称中间包含空格"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder4
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-新建同级文件夹_输入文件夹名称包含不被运行的特殊字符"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: "?$docRandomNo"
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","名称不能包含特殊字符" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC5-新建同级文件夹_文件夹名称长度限制50"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: "这是大于五十个字这是大于五十个字这是大于五十个字这是大于五十个字这是大于五十个字这是大于五十个字这是大于$docRandomNo"
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","名称长度不能超过50" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC6-新建同级文件夹_文件夹名称不可重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","文件夹名称不可重复" ]
      - eq: [ "content.status",1603002 ]

- test:
    name: "setup4-创建二级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup5-列表搜索二级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup6-创建三级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup7-列表搜索三级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-创建四级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid3
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup9-列表搜索四级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup10-创建五级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid4
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup11-列表搜索五级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid5: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup12-创建六级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid5
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup13-列表搜索六级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid6: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup14-创建七级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid6
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup15-列表搜索七级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid7: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup16-创建八级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $eightLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid7
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup17-列表搜索八级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $eightLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid8: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup18-创建九级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $nineLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid8
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup19-列表搜索九级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $nineLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid9: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup20-创建十级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $tenLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid9
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup21-列表搜索十级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $tenLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid10: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup22-创建十一级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $elevenLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid10
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup23-列表搜索十一级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $elevenLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid11: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-新建同级文件夹_和目录深处文件夹名称重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $elevenLevelFolderName
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","文件夹名称不可重复" ]
      - eq: [ "content.status",1603002 ]

- test:
    name: "setup24-删除二级文件夹"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-新建同级文件夹_和已删除的文件夹名称重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $sendLevelFolderName
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup25-新建同级文件夹_文件夹名称可以和文件类型名称重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $sameName
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-新建同级文件夹_文件夹名称可以和文件类型名称重复"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $sameName
      - docCode: $sameNameCode
      - docType: 2
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC10-新建同级文件夹_后端幂等性验证1"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $idempotentName
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC11-新建同级文件夹_后端幂等性验证2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $idempotentName
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","文件夹名称不可重复" ]
      - eq: [ "content.status",1603002 ]


