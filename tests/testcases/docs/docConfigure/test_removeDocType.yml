#文件配置：移动文件类型
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件类型移动$docRandomNo"
      - docNameOfFolder2: "0412延平自动化测试文件类型移动2$docRandomNo"
      - docNameOfType3: "延平自动化文件类型移动本身$docRandomNo"
      - docCodeOfType3: "ypzdhyd$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup3-在文件夹1下新增一个文件夹2"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup4-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]

- test:
    name: "setup5-在文件夹2下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType3
      - docCode: $docCodeOfType3
      - docType: 2
      - parentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup6-列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfType3
      - docType: 2
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfType3 ]

- test:
    name: "TC1-移动文件类型_文件夹名称必填"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","新的父节点id不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC2-移动文件类型_目标文件夹选择"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - docName: $docNameOfFolder2
    validate:
      - eq: [ "content.data.0.docUuid",$autoTestDocUuid2 ]
      - len_eq: [ "content.data",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-移动文件类型_正常移动"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-移动文件类型_正常移动_查询移动结果"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
    extract:
      - updateUser: content.data.updateUser
      - gmtModified: content.data.gmtModified
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.parentUid",$autoTestDocUuid1 ]

- test:
    name: "setup7-搜索根目录文件夹"
    api: api/esignDocs/docConfigure/queryMovedDocList.yml
    variables:
      - docUuid: $autoTestDocUuid2
      - docName: "文件类型"
    extract:
      - rootTestDocUuid: content.data.0.docUuid
    validate:
      - len_ge: [ "content.data",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-移动文件类型_目标文件夹可以选择根节点"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $rootTestDocUuid
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-删除文件夹2"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-移动文件类型_移动到已删除的文件夹"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","父文件夹不存在或已被删除" ]
      - eq: [ "content.status",1603005 ]

- test:
    name: "TC6-移动不属于更新_查看详情不展示"
    api: api/esignDocs/docConfigure/removeDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
      - newParentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-移动不属于更新_查看详情不展示"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.updateUser",$updateUser ]
      - eq: [ "content.data.gmtModified",$gmtModified ]

- test:
    name: "setup8-删除文件夹1"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]