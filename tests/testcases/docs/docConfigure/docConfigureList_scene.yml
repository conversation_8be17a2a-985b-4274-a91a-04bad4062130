#文件配置场景：一个正常的操作流程场景
- config:
    variables:
        docRandomNo: ${get_randomNo()}
        docNameOfFolder: "自动化测试文件夹名称$docRandomNo"
        docNameOfType: "自动化测试文件类型$docRandomNo"
        docCodeOfType: "ZDHCSWJLX$docRandomNo"
        updateSuffix: "_更新"
        headers: ${gen_main_headers()}

- test:
    name: "在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]
      - eq: ["content.data.0.docName","自动化测试文件夹名称$docRandomNo"]
      - not_equals: ["content.data.0.updateUser",null]

- test:
    name: "查看所新增的文件夹详情"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.docType",1]
      - eq: ["content.data.docName",$docNameOfFolder]
      - eq: ["content.data.docUuid",$autoTestDocUuid1]

- test:
    name: "修改文件夹名称"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docCode:
      - docName: $docNameOfFolder$updateSuffix
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "在所新增的文件夹下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode: $docCodeOfType
      - docName: $docNameOfType
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "列表搜索上个用例新增的的文件类型信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfType
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]
      - eq: ["content.data.0.docName",$docNameOfType]
      - not_equals: ["content.data.0.updateUser",null]
      - eq: ["content.data.0.parentUid",$autoTestDocUuid1]

- test:
    name: "查看所新增的文件类型详情"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.docType",2]
      - eq: ["content.data.docName",$docNameOfType]
      - eq: ["content.data.docUuid",$autoTestDocUuid2]

- test:
    name: "修改文件类型名称"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docCode:
      - docName: $docNameOfType$updateSuffix
      - docUuid: $autoTestDocUuid2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
