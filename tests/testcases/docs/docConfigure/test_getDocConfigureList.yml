#文件配置：左侧文件类型树区域搜索
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称$docRandomNo"
      - docNameOfFolder2: "0412延平自动化 测试文件夹名称$docRandomNo"
      - docNameOfType: "0412延平自动化测试文件类型$docRandomNo"
      - docCodeOfType: "0412YPZDHCSWJLX$docRandomNo"
      - spaceChar: "   "
      - sendLevelFolderName: "0412延平自动化测试文件夹名称二级$docRandomNo"
      - thirdLevelFolderName: "0412延平自动化测试文件夹名称三级$docRandomNo"
      - fourLevelFolderName: "0412延平自动化测试文件夹名称四级$docRandomNo"
      - fiveLevelFolderName: "0412延平自动化测试文件夹名称五级$docRandomNo"
      - sixLevelFolderName: "0412延平自动化测试文件夹名称六级$docRandomNo"
      - sevenLevelFolderName: "0412延平自动化测试文件夹名称七级$docRandomNo"
      - eightLevelFolderName: "0412延平自动化测试文件夹名称八级$docRandomNo"
      - eightDocTypeName: "0412延平自动化测试文件类型八级$docRandomNo"
      - eightDocCodeOfType: "0412YPZDHCSWJLX8$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType:
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "TC2-搜索文字前后有空格"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $spaceChar$docNameOfFolder$spaceChar
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "setup2-在根目录下新增一个文件夹-名称中间带空格"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-搜索文字中间包含空格"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]

- test:
    name: "TC4-搜索的内容为空"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName:
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - len_ge: [ "content.data",1 ]

- test:
    name: "TC5-搜索的内容只包含文件夹"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: "0412延平自动化测试文件夹名称"
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docType",1 ]

- test:
    name: "setup3-在所新增的文件夹下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode: $docCodeOfType
      - docName: $docNameOfType
      - docType: 2
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-搜索的内容只包含文件类型"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: "0412延平自动化测试文件类型"
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docType",2 ]

- test:
    name: "setup4-创建二级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup5-列表搜索二级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sendLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup6-创建三级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid2
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup7-列表搜索三级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $thirdLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid3: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup8-创建四级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid3
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup9-列表搜索四级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fourLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid4: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup10-创建五级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid4
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup11-列表搜索五级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $fiveLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid5: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup12-创建六级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid5
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup13-列表搜索六级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sixLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid6: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup14-创建七级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid6
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup15-列表搜索七级文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $sevenLevelFolderName
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid7: content.data.0.docUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup16-创建八级文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode:
      - docName: $eightLevelFolderName
      - docType: 1
      - parentUid: $autoTestDocUuid7
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup17-创建八级文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docCode: $eightDocCodeOfType
      - docName: $eightDocTypeName
      - docType: 2
      - parentUid: $autoTestDocUuid7
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-文件类型搜索_搜索的文件夹在目录树比较深处"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $eightLevelFolderName
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docType",1 ]
      - eq: [ "content.data.0.docName",$eightLevelFolderName ]

- test:
    name: "TC8-文件类型搜索_搜索的文件类型在目录树比较深处"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $eightDocTypeName
      - docType:
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docType",2 ]
      - eq: [ "content.data.0.docName",$eightDocTypeName ]
      - eq: [ "content.data.0.docCode",$eightDocCodeOfType ]