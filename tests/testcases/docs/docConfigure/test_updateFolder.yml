#文件配置：编辑文件夹
- config:
    variables:
      - docRandomNo: ${get_randomNo()}
      - docNameOfFolder: "0412延平自动化测试文件夹名称1$docRandomNo"
      - docNameOfFolderUpdate: "0412延平自动化测试文件夹名称1更新$docRandomNo"
      - docNameOfFolder2: "0412延平自动化测试文件夹名称2$docRandomNo"
      - docNameOfFolder3: "0412延平自动化测试文件夹名称3$docRandomNo"
      - docNameOfFolder4: "0412延平自动化测试  文件夹名称3$docRandomNo"
      - docNameOfType: "0412延平自动化测试文件类型$docRandomNo"
      - docCodeOfType: "0412YPZDHCSWJLX$docRandomNo"
      - spaceChar: "   "
      - sendLevelFolderName: "0412延平自动化测试文件夹名称二级$docRandomNo"
      - thirdLevelFolderName: "0412延平自动化测试文件夹名称三级$docRandomNo"
      - fourLevelFolderName: "0412延平自动化测试文件夹名称四级$docRandomNo"
      - fiveLevelFolderName: "0412延平自动化测试文件夹名称五级$docRandomNo"
      - sixLevelFolderName: "0412延平自动化测试文件夹名称六级$docRandomNo"
      - sevenLevelFolderName: "0412延平自动化测试文件夹名称七级$docRandomNo"
      - eightLevelFolderName: "0412延平自动化测试文件夹名称八级$docRandomNo"
      - nineLevelFolderName: "0412延平自动化测试文件夹名称九级$docRandomNo"
      - tenLevelFolderName: "0412延平自动化测试文件夹名称十级$docRandomNo"
      - elevenLevelFolderName: "0412延平自动化测试文件夹名称十一级$docRandomNo"
      - sameName: "0412延平自动化测试相同名称$docRandomNo"
      - sameNameCode: "stmcypcs$docRandomNo"
      - idempotentName: "0412延平自动化测试幂等$docRandomNo"

- test:
    name: "setup1-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup2-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
      - autoTestParentUid1: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder ]

- test:
    name: "TC1-编辑文件夹_成功编辑"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docNameOfFolderUpdate
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-编辑文件夹_成功编辑_查询上一个编辑是否正确"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolderUpdate
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolderUpdate ]
      - eq: [ "content.data.0.docUuid",$autoTestDocUuid1 ]

- test:
    name: "setup3-查询文件编辑时间"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid1
    extract:
      - gmtModified1: content.data.gmtModified
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-编辑文件夹_未做修改直接提交"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docNameOfFolderUpdate
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-编辑文件夹_未做修改直接提交_判断更新时间"
    api: api/esignDocs/docConfigure/queryDocConfigure.yml
    variables:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: ["content.data.gmtModified",$gmtModified1]

- test:
    name: "TC3-编辑文件夹_文件夹名称必填"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName:
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","名称不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "TC4-编辑文件夹_文件夹名称两端包含空格"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $spaceChar$docNameOfFolderUpdate$spaceChar
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-编辑文件夹_文件夹名称两端包含空格_查询编辑是否正确"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolderUpdate
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolderUpdate ]
      - eq: [ "content.data.0.docUuid",$autoTestDocUuid1 ]

- test:
    name: "TC5-编辑文件夹_文件夹名称中间包含空格"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docNameOfFolder4
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-编辑文件夹_文件夹名称中间包含空格_查询编辑是否正确"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder4
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder4 ]
      - eq: [ "content.data.0.docUuid",$autoTestDocUuid1 ]

- test:
    name: "TC6-编辑文件夹_文件夹名称包含不被运行的特殊字符"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: "\\?aa"
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","名称不能包含特殊字符" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "setup3-在根目录下新增一个文件夹"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docType: 1
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-编辑文件夹_文件夹名称不可重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docUuid: $autoTestDocUuid1
      - docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",1 ]

- test:
    name: "setup4-列表搜索上个用例新增的的文件夹信息2"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid:
    extract:
      - autoTestDocUuid2: content.data.0.docUuid
      - autoTestParentUid2: content.data.0.parentUid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.docName",$docNameOfFolder2 ]

- test:
    name: "setup5-删除上面的文件夹2"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-编辑文件夹_和已删除的文件夹名称重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode:
      - docUuid: $autoTestDocUuid1
      - docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",0 ]

- test:
    name: "setup6-在根目录下新增一个文件类型1"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfType
      - docCode: $docCodeOfType
      - docType: 2
      - parentUid:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-编辑文件夹_文件夹名称可以和文件类型名称重复"
    api: api/esignDocs/docConfigure/compareNameOrCodeDuplicate.yml
    variables:
      - docName: $docNameOfType
      - docCode:
      - docUuid: $autoTestDocUuid1
      - docType: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.nameOrCodeDuplicate",0 ]

- test:
    name: "setup7-删除上面的文件夹"
    api: api/esignDocs/docConfigure/deleteDocConfigure.yml
    variables:
      docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC10-编辑文件夹_文件夹被删除"
    api: api/esignDocs/docConfigure/updateDocConfigure.yml
    variables:
      - docName: $docNameOfFolderUpdate
      - docCode:
      - docUuid: $autoTestDocUuid1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]
      - eq: [ "content.status",1603001 ]
