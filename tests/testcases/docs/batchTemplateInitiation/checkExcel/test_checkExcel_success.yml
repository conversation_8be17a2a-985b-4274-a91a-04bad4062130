# 上传.xls/.XLS/.xlsx/.XLSX内容格式匹配
- config:
    name: "上传.xls类型的批量模板"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - lowXlsxfileName: "checkExcel_无签署方无内容域.xlsx"
      - excelFileLowXlsxKey: ${attachment_upload($lowXlsxfileName)}
#      - upXlsFileName: "大写通用填写外部个人信息.XLS"
#      - excelFileUpXlsKey: ${attachment_upload($upXlsFileName)}
#      - lowXlsFieName: "通用填写外部个人信息.xls"
#      - excelFileLowXlsKey: ${attachment_upload($lowXlsFieName)}
#      - upXlsxfileName: "大写通用填写外部个人信息.XLSX"
#      - excelFileUpXlsxKey: ${attachment_upload($upXlsxfileName)}
#      - fileName1: "新增列失败原因.xlsx"
#      - excelFileKey1: ${attachment_upload($fileName1)}
#      - fileName2: "表头和任意行插入5000条无内容.xlsx"
#      - excelFileKey2: ${attachment_upload($fileName2)}
#      - fileName3: "表头和任意行插入10行全空格内容.xlsx"
#      - excelFileKey3: ${attachment_upload($fileName3)}
#      - fileName4: "表头内容项包含首尾空格~.xlsx"
#      - excelFileKey4: ${attachment_upload($fileName4)}
#      - fileName5: "5000条导入.xlsx"
#      - excelFileKey5: ${attachment_upload($fileName5)}
#      - fileName6: "5001条导入.xlsx"
#      - excelFileKey6: ${attachment_upload($fileName6)}
#      - fileName7: "表格和任意行插入500条行一列有值.xlsx"
#      - excelFileKey7: ${attachment_upload($fileName7)}
#      - fileName8: "空白表头添加内容.xlsx"
#      - excelFileKey8: ${attachment_upload($fileName8)}
#      - fileName9: "包含多个sheet.xlsx"
#      - excelFileKey9: ${attachment_upload($fileName9)}
#      - fileName10: "模板配置测试失败记录.xlsx"
#      - excelFileKey10: ${attachment_upload($fileName10)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "上传内容格式正确的小写xlsx表格校验"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
      - excelFileKey: $excelFileLowXlsxKey
    validate:
      - eq: [ "content.data.excelFileKey",$excelFileLowXlsxKey ]
      - eq: [ "content.data.excelNumber",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

#- test:
#    name: "暂存大写xlsx格式的文件任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileUpXlsxKey,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "暂存小写xls格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileLowXlsKey,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "暂存大写xls格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileUpXlsKey,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]

#- test:
#    name: "上传内容格式正确的小写xlsx表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileLowXlsxKey
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileLowXlsxKey ]
#      - eq: [ "content.data.excelNumber",1 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "上传内容格式正确的小写xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileLowXlsKey
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileLowXlsKey ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "上传内容格式正确大写的xlsx表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileUpXlsxKey
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileUpXlsxKey ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "上传内容格式正确的大写xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileLowXlsKey
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileLowXlsKey ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存新增列失败原因的xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey1,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传新增列失败原因的内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey1
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey1 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存表头和任意行插入5000条无内容的xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey2,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传表头和任意行插入5000条无内容且任意行内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey2
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey2 ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存表头和任意行插入10行全空格内容xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey3,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传表头和任意行插入10行全空格的内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey3
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey3 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存表头内容项包含首尾空格xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey4,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传表头内容项包含首尾空格内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey4
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey4 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存5000条导入xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey5,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传5000条内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey5
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey5 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",5000]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存5001条导入xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey6,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传5001条内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey6
#    validate:
#      - eq: [ "content.data",null]
#      - eq: [ "content.message","最多支持5000个签署方批量签署" ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "暂存插入500条行一列有值的xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey7,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传表格插入500条行一列有值内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey7
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey7 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",500]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存空白表头添加内容的xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey8,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传空白表头添加内容的内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey8
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey8 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存包含多个sheet的xlsx格式任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey9,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传包含多个sheet的内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey9
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey9 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "暂存模板配置测试失败记录的表格任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": $excelFileKey10,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": null,
#        "initiatorDepartmentCode": null,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": null,
#        "type": 1,
#        "contentList": [ {
#          "contentName": $contentNameCommon,
#          "contentValue": ""
#        } ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "上传模板配置测试失败记录的内容格式正确的xls表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      - excelFileKey: $excelFileKey10
#    validate:
#      - eq: [ "content.data.excelFileKey",$excelFileKey10 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.excelNumber",1]
#      - eq: [ "content.success",true ]