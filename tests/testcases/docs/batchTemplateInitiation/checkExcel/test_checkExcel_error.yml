# 上传填写内容为空的xlsx文件
# 上传非法格式的pdf文件
# 上传非法格式的txt文件
# 上传非法格式的csv文件
# 上传非法格式的docx文件

- config:
    name: "上传内容为空的excel"
    variables:
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - presetIdCommon: ${getPreset(0,0)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - nullFileContentName: "textnull.xlsx"
      - excelFileNullContentKey: ${attachment_upload($nullFileContentName)}
#      - pdfFileName: "testppp.pdf"
#      - excelFilePdfKey: ${attachment_upload($pdfFileName)}
#      - txtFileName: "what-this.txt"
#      - excelFileTxtKey: ${attachment_upload($txtFileName)}
#      - docxFileName: "test.docx"
#      - excelFileDocxKey: ${attachment_upload($docxFileName)}
#      - csvFileName: "test.csv"
#      - excelFileCsvKey: ${attachment_upload($csvFileName)}
#      - repeatColumnFileName: "重复列导入.xlsx"
#      - excelFileRepeatColumnKey: ${attachment_upload($repeatColumnFileName)}
#      - updateColumnFileName: "修改表头顺序.xlsx"
#      - excelFileUpdateColumnKey: ${attachment_upload($updateColumnFileName)}
#      - noRequiredColumnFileName: "没有必填项列.xlsx"
#      - excelFileNoRequiredColumnKey: ${attachment_upload($noRequiredColumnFileName)}
#      - delNoRequiredColumnFileName: "表头移除非必填项.xlsx"
#      - excelFileDelNoRequiredColumnKey: ${attachment_upload($delNoRequiredColumnFileName)}
#      - blankColumnFileName: "签署头和必填项头添加多个空白列.xlsx"
#      - excelFileBlankColumnKey: ${attachment_upload($blankColumnFileName)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "上传填写内容为空的表格校验"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
      - excelFileKey: $excelFileNullContentKey
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.success",false ]

#- test:
#    name: "上传非法格式pdf文件校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFilePdfKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传非法格式txt文件校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileTxtKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传非法格式docx文件校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileDocxKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传非法格式csv文件校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileCsvKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#
#- test:
#    name: "上传重复列表头的表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileRepeatColumnKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传修改头顺序的xlsx表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileUpdateColumnKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传没有必填项列xlsx表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileNoRequiredColumnKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传表头移除非必填项的xlsx表格校验"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileDelNoRequiredColumnKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传签署头和必填项头添加多个空白列"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey: $excelFileBlankColumnKey
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.success",false ]
#
#- test:
#    name: "上传空的文件"
#    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
#    variables:
#      - batchTemplateInitiationUuid: $uuid
#      - excelFileKey:
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.message","文件Key不能为空"]
#      - eq: [ "content.success",false ]