- config:
    name: 页面发起结果页
    variables:
      name: "页面发起结果页"

- test:
    name: TC1-flowId不存在
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "XXXXXX"
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]

- test:
    name: TC2-flowId是已作废流程
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "${ENV(pdf.revock.processId)}"
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]

- test:
    name: TC3-flowId是拒签流程
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "${ENV(pdf.reject.processId)}"
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]

- test:
    name: TC4-flowId是签署完成流程
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "${ENV(pdf.signed.processId)}"
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]

- test:
    name: TC5-flowId是过期流程
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "${ENV(pdf.overdue.processId)}"
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]

- test:
    name: TC6-flowId是草稿流程
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "${ENV(pdf.draft.processId)}"
#    extract:
#      - code0: content.code
    validate:
      - eq: [content.status, 1602018]
      - eq: [content.message, "数据不存在"]
      - eq: [content.data, null]


