- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-清除历史任务"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid0
    api: api/esignDocs/batchTemplateInitiation/clear.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]