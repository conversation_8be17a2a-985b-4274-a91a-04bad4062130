- config:
    name: "获取单次任务数据-正向case"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - lowXlsxfileName: "checkExcel_无签署方无内容域.xlsx"
      - excelFileLowXlsxKey: ${attachment_upload($lowXlsxfileName)}
      - batchTemplateInitiationName: "自动化测试批量起任务-${get_randomNo()}"

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "上传内容格式正确的小写xlsx表格校验"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
      - excelFileKey: $excelFileLowXlsxKey
    validate:
      - eq: [ "content.data.excelFileKey",$excelFileLowXlsxKey ]
      - eq: [ "content.data.excelNumber",1 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

###############集测容易发生错误：数据不存在。 需要等修复完这个bug之后再打开下面的应用##################
#- test:
#    name: "提交批量任务"
#    api: api/esignDocs/batchTemplateInitiation/submit.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $batchTemplateInitiationName,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": $departmentName,
#        "initiatorDepartmentCode": $departmentCode,
#        "businessPresetUuid": $presetIdCommon,
#        "signersList": [
#        {
#          "signMode": 1,
#          "signerList": [
#          {
#            "signerType": 1,
#            "signerTerritory": 1,
#            "draggable": true,
#            "organizeCode": "",
#            "userCode": $initiatorUserCode,
#            "sealTypeCode": null,
#            "autoSign": 0,
#            "assignSigner": 1,
#            "userName": $initiatorUserName
#          }
#          ]
#        }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#        {
#          "appendType": 1,
#          "attachmentInfo": {
#            "fileKey": $signPdfFileKey,
#            "fileName": $signPdfFileName
#          }
#        }
#        ]
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "获取flowId"
#    variables:
#      - flowId: ""
#      - flowType: ""
#      - flowName: $batchTemplateInitiationName
#      - startTime: ""
#      - endTime: ""
#      - flowStatus: ""
#      - initiatorUserName: ""
#      - page: 1
#      - size: 1
#    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
#    extract:
#      - flowId0: content.data.list.0.flowId
#    setup_hooks:
#      - ${sleep(5)}
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "获取单次任务数据"
#    variables:
#      - relatedUuid: $flowId0
#    api: api/esignDocs/batchTemplateInitiation/getRelatedData.yml
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.flowId",$flowId0 ]
#      - eq: [ "content.data.flowName",$batchTemplateInitiationName ]
#      - eq: [ "content.data.batchTemplateInitiationUuid",$batchTemplateInitiationUuidNew ]
