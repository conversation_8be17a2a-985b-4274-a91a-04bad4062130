- config:
    name: "指定签署区-保存配置-添加有2个签署区的模板文件并绑定到签署方"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(sign01.userCode)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - templateIdNew: ${getTemplateId(1,2)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取模板详情"
    api: api/esignDocs/template/owner/templateInfo.yml
    variables:
      - templateUuid: $templateIdNew
      - version: 1
    extract:
      - templateNameCommon: content.data.templateName
      - templateFileKeyCommon: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "type": 3,
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "signerType": 1,
            "signerTerritory": 1,
            "draggable": true,
            "organizeCode": "",
            "userCode": $initiatorUserCode,
            "sealTypeCode": null,
            "autoSign": 0,
            "assignSigner": 1,
            "userName": $initiatorUserName
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 2,
          "templateInfo": {
            "fileKey": $templateFileKeyCommon,
            "fileName": $templateNameCommon,
            "templateId": $templateIdNew,
            "version": 1,
            "templateType": 1
          }
        }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
    extract:
      - _editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl1)}
      tmp_common_template_002: ${putTempEnv(_tmp_common_template_002, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail--模板1"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_002)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _name1: content.data.name
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_common_template_002)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]


- test:
    name: "epaasTemplate-batch-save-draft--签署区设置页设置备注签"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_002)}
      signfiled1: ${generate_field_object(签名区1, SIGN)}
      signfiled2: ${generate_field_object(签名区2, SIGN)}
      fields_draft1: ["${add_template_role_id($signfiled1,$_signerId1)}","${add_template_role_id($signfiled1,$_signerId1)}"]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": [],
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId,
                        "entityId": $_entityId
                    }
          ]
      }
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "保存盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          ],
          "signerId": "",
          "userType": 1,
          "userCode": "",
          "userName": "",
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]