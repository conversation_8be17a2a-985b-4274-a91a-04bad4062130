- config:
    name: "签署区设置页支持添加个人备注签署区"
    variables:
      - businessTypeCode01: ${ENV(businessTypeCode)}
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      - initiatorUserNameCommon: ${ENV(sign01.userName)}
      - organizationCodeCommon: ${ENV(sign01.main.orgCode)}
      - organizationNameCommon: ${ENV(sign01.main.orgName)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - collectionTaskName0: "${getRemarkCollectionTaskName()}"
      - presetName01: "页面支持备注签-输入方式为键盘输入${getDateTime()}"
      - presetName02: "页面支持备注签-输入方式为采集表单${getDateTime()}"
      - presetName03: "页面支持备注签-输入方式为手绘输入${getDateTime()}"
      - presetName05: "页面支持备注签-经办人备注-输入方式为键盘输入${getDateTime()}"
#      - templateIdNew: ${getTemplateId(0,2)}

#- test:
#    name: "setup1-获取发起人关联的组织信息"
#    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
#    variables:
#      - businessPresetUuid: $presetIdCommon
#    extract:
#      - departmentCode01: content.data.list.0.departmentCode
#      - departmentName01: content.data.list.0.departmentName
#      - organizationCode01: content.data.list.0.organizationCode
#      - organizationName01: content.data.list.0.organizationName
#      - initiatorUserName01: content.data.initiatorUserName
#    validate:
#      - eq: ["content.message","成功"]

- test:
    name: "setup1-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "TC-1-1-获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-1-2-添加一个内部个人签署方和一份pdf签署文件"
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCodeCommon,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserNameCommon
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-1-3-获取盖章配置详情"
    api: api/esignDocs//batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
    extract:
      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-1-4-签署区设置页，只添加个人备注，输入方式为键盘输入，成功"
    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 188,
              "posY": 188,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "",
                "inputType": 2,
                "remarkContent": "我同意签署",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "200",
                "remarkPosY": "200",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "我的备注区",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            }
            ],
             "signatureTypeList": ["PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew01,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-1-5-获取businessPresetSnapshotUuid
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList01: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-1-6-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName01
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid
      fileFormat: 1
      appendList: $appendList01
      signersList: $signerNodeList
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: submitResult-查询发起的结果-V6.0.12.0-去签署(发起人和签署人是同一个)
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "$_flowId"
#    extract:
#      - code0: content.code
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - contains: [content.data.jumpUrl, "/sign-manage-web/sign-page?processId=$_flowId"]
      - contains: [content.data.jumpUrl, "&redirectUrl=http"]
      - eq: [content.data.flowStatus, 3]
      - eq: [content.data.hasPermission, true]
      - eq: [content.data.hasWorkflow, false]
      - eq: [content.data.message, null]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserNameCommon"]
      - len_eq: [content.data.signerList, 1]
      - eq: [content.data.signerList.0.signerName, "$initiatorUserNameCommon"]
      - eq: [content.data.signerList.0.signerOrganizationName, null]
      - eq: [content.data.fillingUserList, []]

- test:
    name: "TC-1-7-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $presetName01
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId01: content.data.list.0.flowId
      - signFlowId01: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-1-8-检查签署区设置页只添加备注签署区会自动添加个人的自由签署区"
    api: api/esignSigns//process/detail.yml
    variables:
      - processId: $signFlowId01
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo01: content.data.signConfigInfo
      - actorSignConfigLists01: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - len_eq: [ "$signConfigInfo01", 1 ] #签署文档

- test:
    name: "TC-2-1-获取batchTemplateInitiationUuid02"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew02: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-2-2-添加一个内部个人签署方和一份pdf签署文件"
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCodeCommon,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserNameCommon
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-2-3-获取盖章配置详情"
    api: api/esignDocs//batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew02
    extract:
      - signConfigsNew02: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew02: content.data.filePreTaskInfos.0.signerId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-2-4-根据采集任务名称获取采集任务id"
    api: api/esignDocs//batchTemplateInitiation/remarkCollectionTaskList.yml
    variables:
      collectionTaskNameRemarkCollectionTaskList: "$collectionTaskName0"
    extract:
      - collectionTaskId01: content.data.0.collectionTaskId
      - collectionTaskName01: content.data.0.collectionTaskName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.collectionTaskName", $collectionTaskName0]

- test:
    name: "TC-2-5-签署区设置页，只添加个人备注，输入方式为采集表单，成功"
    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 188,
              "posY": 188,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "$collectionTaskId01",
                "inputType": 3,
                "remarkContent": "",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "200",
                "remarkPosY": "200",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "我的备注区",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            }
            ],
             "signatureTypeList": ["PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew02,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-2-6-获取batchTemplateInitiationUuid2
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew02"
    extract:
      - businessPresetSnapshotUuid02: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList02: content.data.businessPresetDetail.signerNodeList
      - appendList02: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-2-7-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName02
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew02
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid02
      fileFormat: 1
      appendList: $appendList02
      signersList: $signerNodeList02
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC-2-8-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $presetName02
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId02: content.data.list.0.flowId
      - signFlowId02: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon ]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-2-9-检查签署区设置页只添加备注签署区会自动添加个人的自由签署区"
    api: api/esignSigns//process/detail.yml
    variables:
      - processId: $signFlowId02
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo02: content.data.signConfigInfo
      - actorSignConfigLists: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 3 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.collectionTaskId, $collectionTaskId01 ]
      - len_eq: [ "$signConfigInfo02", 1 ] #签署文档

- test:
    name: "TC-3-1-获取batchTemplateInitiationUuid02"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew03: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-3-2-添加一个内部个人签署方和一份pdf签署文件"
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCodeCommon,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserNameCommon
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-3-3-获取盖章配置详情"
    api: api/esignDocs//batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew03
    extract:
      - signConfigsNew03: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew03: content.data.filePreTaskInfos.0.signerId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-3-4-签署区设置页，只添加个人备注，输入方式为手绘输入，成功"
    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 188,
              "posY": 188,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "",
                "inputType": 1,
                "remarkContent": "我同意签署",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "200",
                "remarkPosY": "200",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "我的备注区",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            }
            ],
             "signatureTypeList": ["PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew03,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-3-5-获取businessPresetSnapshotUuid
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew03"
    extract:
      - businessPresetSnapshotUuid03: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList03: content.data.businessPresetDetail.signerNodeList
      - appendList03: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-3-6-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName03
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew03
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid03
      fileFormat: 1
      appendList: $appendList03
      signersList: $signerNodeList03
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC-3-7-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $presetName03
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId03: content.data.list.0.flowId
      - signFlowId03: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon ]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-3-8-检查签署区设置页只添加备注签署区会自动添加个人的自由签署区"
    api: api/esignSigns//process/detail.yml
    variables:
      - processId: $signFlowId03
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo03: content.data.signConfigInfo
      - actorSignConfigLists03: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 1 ]
      - len_eq: [ "$signConfigInfo03", 1 ] #签署文档











#- test:
#    name: "TC-1-7-获取当前业务模板发起成功的流程的flowId和processId"
#    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
#    variables:
#      flowName: $presetName05
#      flowId: ""
#      startTime: ""
#      endTime: ""
#      flowStatus:
#      initiatorUserName: ""
#      page: 1
#      size: 10
#    extract:
#      - flowId05: content.data.list.0.flowId
#      - signFlowId05: content.data.list.0.processId
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserName01 ]
#      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationName01 ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-1-8-检查签署区设置页只添加备注签署区会自动添加个人的自由签署区"
#    api: api/esignSigns//process/detail.yml
#    variables:
#      - processId: $signFlowId05
#      - organizeCode: $organizationCode01
#      - approvalProcessId: ""
#      - requestSource: 2
#    extract:
#      - signConfigInfo05: content.data.signConfigInfo
#      - actorSignConfigLists: content.data.signConfigInfo.0.actorSignConfigList
#    validate:
#      - contains: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
#      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
#      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
#      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
#      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
#      - len_eq: [ "$signConfigInfo05", 1 ] #签署文档













#
#
#- test:
#    name: "TC-1-2-获取getInfo的接口的batchTemplateInitiationUuid"
#    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
#    variables:
#      "params": { }
#    extract:
#      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-1-3-获取发起人关联的组织信息"
#    api: api/esignDocs//batchTemplateInitiation/getInitiatorUserInfo.yml
#    variables:
#      - businessPresetUuid: $presetIdCommon
#    extract:
#      - departmentCode01: content.data.list.0.departmentCode
#      - departmentName01: content.data.list.0.departmentName
#      - organizationCode01: content.data.list.0.organizationCode
#      - organizationName01: content.data.list.0.organizationName
#      - initiatorUserName01: content.data.initiatorUserName
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-1-4-添加一个内部个人签署方和一份pdf签署文件"
#    api: api/esignDocs//batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon01,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "initiatorOrganizeCode": $organizationCode01,
#        "initiatorOrganizeName": $organizationName01,
#        "initiatorUserName": $initiatorUserName01,
#        "initiatorDepartmentName": $departmentName01,
#        "initiatorDepartmentCode": $departmentCode01,
#        "businessPresetUuid": $presetIdCommon,
#        "signersList": [
#          {
#              "signMode": 1,
#              "signerList": [
#                {
#                  "signerType": 1,
#                  "signerTerritory": 1,
#                  "draggable": true,
#                  "organizeCode": "",
#                  "userCode": $initiatorUserCode,
#                  "sealTypeCode": null,
#                  "autoSign": 0,
#                  "assignSigner": 1,
#                  "userName": $initiatorUserName01
#                }
#              ]
#          }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#          {
#            "appendType": 1,
#            "attachmentInfo": {
#              "fileKey": $signPdfFileKey,
#              "fileName": $signPdfFileName
#            }
#          }
#        ]
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-1-5-获取盖章配置详情"
#    api: api/esignDocs//batchTemplateInitiation/signature/detail.yml
#    variables:
#      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
#    extract:
#      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
#      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-1-6-签署区设置页，只添加个人备注，成功"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#        {
#          "sealInfos": [
#          {
#            "fileKey": $signPdfFileKey,
#            "signConfigs": [
#            {
#              "allowMove": false,
#              "addSignDate": false,
#              "keywordInfo": null,
#              "pageNo": "1",
#              "posX": 188,
#              "posY": 703,
#              "remarkSignConfig": {
#                "aiCheck": 0,
#                "collectionTaskId": "",
#                "inputType": 1,
#                "remarkContent": "我同意签署",
#                "remarkFieldHeight": 200,
#                "remarkFieldWidth": 200,
#                "remarkFontSize": 20,
#                "remarkPageNo": "1",
#                "remarkPosX": "200",
#                "remarkPosY": "200",
#                "remarkPrompt": ""
#              },
#              "edgeScope": null,
#              "sealSignDatePositionInfo": null,
#              "signPosName": "我的备注区",
#              "signFieldType": 1,
#              "signType": "COMMON-SIGN",
#              "signatureType": "PERSON-SEAL"
#            }
#            ]
#          }
#          ],
#          "signerId": $signerIdNew01,
#          "userType": 1,
#          "userCode": $initiatorUserCode,
#          "userName": $initiatorUserName01,
#          "signerType": 1,
#          "legalSignFlag": 0
#        }
#        ]
#      }
#    validate:
#      - contains: [ "content.message", "成功" ]
#      - eq: [ "content.status", 200 ]
#      - eq: [ "content.success", True]
#
#- test:
#    name: "提交批量任务1-签署人重复填写项重复"
#    api: api/esignDocs//batchTemplateInitiation/submit.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon01,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "excelFileKey": "",
#        "initiatorOrganizeCode": $organizationCode01,
#        "initiatorOrganizeName": $organizationName01,
#        "initiatorUserName": $initiatorUserName01,
#        "initiatorDepartmentName": $departmentName01,
#        "initiatorDepartmentCode": $departmentCode01,
#        "businessPresetUuid": $presetIdCommon,
#        "saveSigners": null,
#        "signersList": [
#        {
#          "signMode": 1,
#          "signerList": [
#          {
#            "signerSnapshotId": $signerIdNew01,
#            "signerType": 2,
#            "signerTerritory": 2,
#            "draggable": true,
#            "autoSign": 0,
#            "assignSigner": 0
#          }
#          ]
#        }
#        ],
#        "type": 3,
#        "sort": 1,
#        "chargingType": 1
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
##      - { "expect": "$handleResult1","check": "${assertBatchTemplateInitiationUuid2($sqlLanguage1$batchTemplateInitiationUuidNew$endSql)}" }


#- test:
#    name: "TC-1-6-发起签署"
#    api: api/esignDocs//batchTemplateInitiation/startProcess.yml
#    variables:
#      signFlowIdStartProcess: $batchTemplateInitiationUuidNew01
#    validate:
#      - contains: [ "content.message", "成功" ]
#      - eq: [ "content.status", 200 ]
#      - eq: [ "content.success", True ]

#- test:
#    name: "TC-1-7-签署区设置页，添加个人备注和个人签署区，保存成功"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#          {
#            "sealInfos": [
#              {
#                "fileKey": $signPdfFileKey,
#                "signConfigs": [
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "remarkSignConfig":
#                      {
#                        "aiCheck": 0,
#                        "collectionTaskId": "",
#                        "inputType": 1,
#                        "remarkContent": "我同意签署",
#                        "remarkFieldHeight": 200,
#                        "remarkFieldWidth": 200,
#                        "remarkFontSize": 20,
#                        "remarkPageNo": "1",
#                        "remarkPosX": "200",
#                        "remarkPosY": "200",
#                        "remarkPrompt": ""
#                      },
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "我的备注区",
#                    "signFieldType": 1,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "PERSON-SEAL"
#                  },
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "我的签署区",
#                    "signFieldType": 0,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "PERSON-SEAL"
#                  }
#                ]
#              }
#            ],
#            "signerId": $signerIdNew01,
#            "userType": 1,
#            "userCode": $initiatorUserCode,
#            "userName": $initiatorUserName01,
#            "signerType": 1,
#            "legalSignFlag": 0
#          }
#        ]
#      }
#    validate:
#      - contains: ["content.message", "成功"]
#      - eq: ["content.status", 200]
#      - eq: ["content.success", True]
#
#- test:
#    name: "TC-2-1-添加一个内部企业签署方和一份pdf签署文件"
#    api: api/esignDocs//batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon01,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "initiatorOrganizeCode": $organizationCode01,
#        "initiatorOrganizeName": $organizationName01,
#        "initiatorUserName": $initiatorUserName01,
#        "initiatorDepartmentName": $departmentName01,
#        "initiatorDepartmentCode": $departmentCode01,
#        "businessPresetUuid": $presetIdCommon,
#        "signersList": [
#          {
#              "signMode": 1,
#              "signerList": [
##                {
##                  "signerType": 1,
##                  "signerTerritory": 1,
##                  "draggable": true,
##                  "organizeCode": "",
##                  "userCode": $initiatorUserCode,
##                  "sealTypeCode": null,
##                  "autoSign": 0,
##                  "assignSigner": 1,
##                  "userName": $initiatorUserName01
##                },
#                {
#                  "signerType": 2,
#                  "signerTerritory": 1,
#                  "organizeCode": $organizationCode01,
#                  "userCode": $initiatorUserCode,
#                  "sealTypeCode": null,
#                  "autoSign": 0,
#                  "assignSigner": 1,
#                  "userName": $initiatorUserName01
#                }
#              ]
#          }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#          {
#            "appendType": 1,
#            "attachmentInfo": {
#              "fileKey": $signPdfFileKey,
#              "fileName": $signPdfFileName
#            }
#          }
#        ]
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]

#- test:
#    name: "TC-2-2-获取盖章配置详情"
#    api: api/esignDocs//batchTemplateInitiation/signature/detail.yml
#    variables:
#      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
#    extract:
#      - signConfigsNew02: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
#      - signerIdNew02: content.data.filePreTaskInfos.0.signerId
#    validate:
#      - eq: [ "content.message", "成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status", 200 ]

#- test:
#    name: "TC-2-3-签署区设置页，只添加经办人备注，报错"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#        {
#          "sealInfos": [
#          {
#            "fileKey": $signPdfFileKey,
#            "signConfigs": [
#            {
#              "allowMove": false,
#              "addSignDate": false,
#              "keywordInfo": null,
#              "pageNo": "1",
#              "posX": 188,
#              "posY": 703,
#              "remarkSignConfig": {
#                "aiCheck": 0,
#                "collectionTaskId": "",
#                "inputType": 1,
#                "remarkContent": "我同意签署",
#                "remarkFieldHeight": 200,
#                "remarkFieldWidth": 200,
#                "remarkFontSize": 20,
#                "remarkPageNo": "1",
#                "remarkPosX": "200",
#                "remarkPosY": "200",
#                "remarkPrompt": ""
#              },
#              "edgeScope": null,
#              "sealSignDatePositionInfo": null,
#              "signPosName": "我的备注区",
#              "signFieldType": 1,
#              "signType": "COMMON-SIGN",
#              "signatureType": "PERSON-SEAL"
#            }
#            ]
#          }
#          ],
#          "signerId": $signerIdNew02,
#          "userType": 1,
#          "userCode": $initiatorUserCode,
#          "userName": $initiatorUserName01,
#          "signerType": 2,
#          "legalSignFlag": 0
#        }
#        ]
#      }
#    validate:
#      - contains: ["content.message", "在签署文件中至少设置一个企业签署区或是法人签署区"]
#      - eq: ["content.status", 1606048]
#      - eq: ["content.success", False]
#
#- test:
#    name: "TC-2-4-签署区设置页，添加经办人备注和企业签署区，报错"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#          {
#            "sealInfos": [
#              {
#                "fileKey": $signPdfFileKey,
#                "signConfigs": [
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "remarkSignConfig":
#                      {
#                        "aiCheck": 0,
#                        "collectionTaskId": "",
#                        "inputType": 1,
#                        "remarkContent": "我同意签署",
#                        "remarkFieldHeight": 200,
#                        "remarkFieldWidth": 200,
#                        "remarkFontSize": 20,
#                        "remarkPageNo": "1",
#                        "remarkPosX": "200",
#                        "remarkPosY": "200",
#                        "remarkPrompt": ""
#                      },
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "我的备注区",
#                    "signFieldType": 1,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "PERSON-SEAL"
#                  },
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "企业签署区",
#                    "signFieldType": 0,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "COMMON-SEAL"
#                  }
#                ]
#              }
#            ],
#            "signerId": $signerIdNew02,
#            "userType": 1,
#            "userCode": $initiatorUserCode,
#            "userName": $initiatorUserName01,
#            "signerType": 2,
#            "legalSignFlag": 0
#          }
#        ]
#      }
#    validate:
#      - contains: ["content.message", "经办人签署区不存在，请先添加经办人签署区后再添加经办人备注"]
#      - eq: ["content.status", 1612244]
#      - eq: ["content.success", False]
#
#- test:
#    name: "TC-2-5-签署区设置页，添加经办人备注、企业签署区、经办人签署区，成功"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#          {
#            "sealInfos": [
#              {
#                "fileKey": $signPdfFileKey,
#                "signConfigs": [
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "remarkSignConfig":
#                      {
#                        "aiCheck": 0,
#                        "collectionTaskId": "",
#                        "inputType": 1,
#                        "remarkContent": "我同意签署",
#                        "remarkFieldHeight": 200,
#                        "remarkFieldWidth": 200,
#                        "remarkFontSize": 20,
#                        "remarkPageNo": "1",
#                        "remarkPosX": "200",
#                        "remarkPosY": "200",
#                        "remarkPrompt": ""
#                      },
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "我的备注区",
#                    "signFieldType": 1,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "PERSON-SEAL"
#                  },
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 188,
#                    "posY": 703,
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "企业签署区",
#                    "signFieldType": 0,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "COMMON-SEAL"
#                  },
#                  {
#                    "allowMove": false,
#                    "addSignDate": false,
#                    "keywordInfo": null,
#                    "pageNo": "1",
#                    "posX": 288,
#                    "posY": 603,
#                    "edgeScope": null,
#                    "sealSignDatePositionInfo": null,
#                    "signPosName": "经办人签署区",
#                    "signFieldType": 0,
#                    "signType": "COMMON-SIGN",
#                    "signatureType": "PERSON-SEAL"
#                  }
#                ]
#              }
#            ],
#            "signerId": $signerIdNew02,
#            "userType": 1,
#            "userCode": $initiatorUserCode,
#            "userName": $initiatorUserName01,
#            "signerType": 2,
#            "legalSignFlag": 0
#          }
#        ]
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success", True ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC-2-6-签署区设置页，骑缝章签署区无法添加备注区"
#    api: api/esignDocs//batchTemplateInitiation/signature/save.yml
#    variables:
#      params: {
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
#        "filePreTaskInfos": [
#        {
#          "sealInfos": [
#          {
#            "fileKey": $signPdfFileKey,
#            "signConfigs": [
#            {
#              "allowMove": false,
#              "addSignDate": false,
#              "keywordInfo": null,
#              "pageNo": "1",
#              "posX": 188,
#              "posY": 703,
#              "remarkSignConfig": {
#                "aiCheck": 0,
#                "collectionTaskId": "",
#                "inputType": 1,
#                "remarkContent": "我同意签署",
#                "remarkFieldHeight": 200,
#                "remarkFieldWidth": 200,
#                "remarkFontSize": 20,
#                "remarkPageNo": "1",
#                "remarkPosX": "200",
#                "remarkPosY": "200",
#                "remarkPrompt": ""
#              },
#              "edgeScope": 0,
#              "sealSignDatePositionInfo": null,
#              "signPosName": "我的备注区",
#              "signFieldType": 1,
#              "signType": "EDGE-SIGN",
#              "signatureType": "PERSON-SEAL"
#            },
#              {
#                "allowMove": false,
#                "addSignDate": false,
#                "keywordInfo": null,
#                "pageNo": "1",
#                "posX": 188,
#                "posY": 703,
#                "edgeScope": null,
#                "sealSignDatePositionInfo": null,
#                "signPosName": "企业签署区",
#                "signFieldType": 0,
#                "signType": "COMMON-SIGN",
#                "signatureType": "COMMON-SEAL"
#              },
#              {
#                "allowMove": false,
#                "addSignDate": false,
#                "keywordInfo": null,
#                "pageNo": "1",
#                "posX": 288,
#                "posY": 603,
#                "edgeScope": null,
#                "sealSignDatePositionInfo": null,
#                "signPosName": "经办人签署区",
#                "signFieldType": 0,
#                "signType": "COMMON-SIGN",
#                "signatureType": "PERSON-SEAL"
#              }
#            ]
#          }
#          ],
#          "signerId": $signerIdNew02,
#          "userType": 1,
#          "userCode": $initiatorUserCode,
#          "userName": $initiatorUserName01,
#          "signerType": 2,
#          "legalSignFlag": 0
#        }
#        ]
#      }
#    validate:
#      - contains: ["content.message", "在签署文件中至少设置一个企业签署区或是法人签署区"]
#      - eq: ["content.status", 1606048]
#      - eq: ["content.success", False]


