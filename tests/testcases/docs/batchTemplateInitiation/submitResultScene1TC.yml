#场景说明：内部静默签署-有审批
- config:
    name: 页面发起结果页-********-stable-带有审批流程的发起
    variables:
      name: "页面发起结果页"
      mainHost: ${ENV(esign.projectHost)}
#      presetName0: "自动化勿动6-带审批内部企业和个人指定或签不填写"
      presetName0: "自动化有流程发起结果页-${get_randomNo_16()}"
#      newTemplateUuid1: ${getTemplateId(0,2,pdf,0)}
#      newVersion1: 1
#      autotestModelName: "文档自动化测试勿动勿复制"
#      autotestModelKey: ${getWorkFlowModelKey($autotestModelName)}
#      autotestWorkFlowModelName: "【电子签署】-$autotestModelName"
      userCode0: ${ENV(sign01.userCode)}
      userName0: ${ENV(sign01.userName)}
      orgCode0: ${ENV(sign01.main.orgCode)}
      orgName0: ${ENV(sign01.main.orgName)}
      testPresetId: "${getPresetTypeId(6)}"

#- test:
#    name: "setup-获取文档模板详情"
#    api: api/esignDocs/template/manage/templateInfo.yml
#    variables:
#      - templateUuid: $newTemplateUuid1
#      - version: $newVersion1
#    extract:
#      - commonTemplateName: content.data.templateName
#      - fileKey0: content.data.fileKey
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - eq: ["content.data.fileType","pdf"]
#      - eq: ["content.data.status", "PUBLISH"]
#      - eq: ["content.data.status", "PUBLISH"]
#
#- test:
#    name: "setup-新建一个业务模板"
#    api: api/esignDocs/businessPreset/create.yml
#    variables:
#      - presetName: $presetName0
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#
#- test:
#    name: "setup-查询新增的业务模板,并获取presetId"
#    api: api/esignDocs/businessPreset/list.yml
#    variables:
#      - presetName: $presetName0
#    extract:
#      - testPresetId: content.data.list.0.presetId
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.data.total, 1]
#      - eq: [content.data.list.0.presetName, $presetName0]
#
#- test:
#    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId
#    extract:
#      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "成功"]
#      - eq: [content.data.fileFormat, 1]
#      - eq: [content.data.allowAddFile, 1]
#      - eq: [content.data.allowAddSigner, 1]
#      - eq: [content.data.initiatorAll, 1]
#      - eq: [content.data.presetId, $testPresetId]
#      - eq: [content.data.presetName, $presetName0]
#      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
#      - len_gt: [content.data.signBusinessType.messageConfigList, 1]
#
#- test:
#    name: "setup-业务模板配置-第一步：填写基本信息（关联pdf模板）"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      presetName: $presetName0
#      presetId_addDetail: $testPresetId
#      allowAddFile: 0
#      workFlowModelKey: $autotestModelKey
#      templateList:
#        - fileKey: $fileKey0
#          templateId: $newTemplateUuid1
#          templateName: $commonTemplateName
#          version: $newVersion1
#          templateType: 1
#          contentDomainCount: 0
#          includeSpecialContent: 0
#    validate:
#      - eq: [content.message,"成功"]
#      - eq: [content.status,200]
#      - eq: [ content.success, true ]
#
#- test:
#    name: "setup-通过业务模板配置id获取签署区列表"
#    api: api/esignDocs/businessPreset/getSignatoryList.yml
#    variables:
#      - presetId: $testPresetId
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data", null]
#
#- test:
#    name: "setup-业务模板配置-第三步：签署方设置（内部个人指定（sign01））"
#    api: api/esignDocs/businessPreset/addSigners.yml
#    variables:
#      presetId: $testPresetId
#      status: 1
#      needGather: 0
#      needAudit: 0
#      sort: 0
#      allowAddSigner: 0
#      allowAddSealer: 1
#      signerList:
#        - autoSign: 1
#          assignSigner: 1
#          sealTypeCode:
#          signerTerritory: 1
#          signerType: 1
#          userCode: "$userCode0"
#          sealTypeName: ""
#          departmentCode: ""
#          signatoryList:
#            - signatoryId: $signatoryIdA
#              templateId: $newTemplateUuid1
#              signatoryName: $signatoryNameA
#              templateName: $commonTemplateName
#              sealType: 2
#            - signatoryId: $signatoryIdB
#              templateId: $newTemplateUuid1
#              signatoryName: $signatoryNameB
#              templateName: $commonTemplateName
#              sealType: 1
#    validate:
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#
#- test:
#      name: setup-openapi查询电子签署业务模板详情
#      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
#      variables:
#          businessTypeCode: $testBusinessTypeId
#      extract:
#        - signerId0: content.data.signerInfos.0.signerId
#      validate:
#          - eq: [ content.code, 200 ]
#          - eq: [ content.message, "成功" ]
#          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
#          - eq: [ content.data.bizTemplateName, $presetName0 ]
#          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
#          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
#          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许添加文件
#          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]
#          - eq: [ content.data.signerInfos.0.signerAccountInfo.userCode, $userCode0]
#          - eq: [ content.data.signerInfos.0.autoSign, true]
#          - eq: [ content.data.signerInfos.0.UkeyOnly, 2]

##发起电子签署系列接口
- test:
    name: "setup3-选择业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerNodeList001: content.data.signerNodeList
      - contentList001: content.data.contentList
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "getInitiatorUserInfo-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "getInfo-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "staging"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName0,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "type": 3,
        "signersList": $signerNodeList001,
        "appendList": []
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]

- test:
    name: "getInfo-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
    extract:
      - signerNodeList001: content.data.businessPresetDetail.signerNodeList
      - templateList001: content.data.businessPresetDetail.templateList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "submit-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName0
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      initiatorOrganizeCode: $organizationCode
      initiatorOrganizeName: $organizationName
      initiatorUserName: $initiatorUserName
      initiatorDepartmentName: $departmentName
      initiatorDepartmentCode: $departmentCode
      businessPresetUuid: $testPresetId
      fileFormat: 1
      signersList: $signerNodeList001
      appendList: []
      auditInfo:
#        nodeConfigCode: "BMLDSP"
        nodeConfigCode: "Activity_01nvj49"
        sendNotice: "0"
        requestUrl: "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid1"
        nextAssigneeList:
          - nextAssignee: $userCode0
            nextAssigneeOrganizationCode: "$orgCode0"
            nextAssigneeName: "$userName0"
            nextAssigneeOrganizationName: ""
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: submitResult-查询发起的结果-V********-去审批(发起人和审批人是同一个)
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "$_flowId"
#    extract:
#      - code0: content.code
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - contains: [content.data.jumpUrl, "bussinessId=$_flowId"]
      - contains: [content.data.jumpUrl, "redirectUrl"]
      - eq: [content.data.flowStatus, 2]
      - eq: [content.data.hasPermission, true]
      - eq: [content.data.hasWorkflow, true]
      - eq: [content.data.message, null]
      - eq: [content.data.initiator.initiatorDepartmentName, "$organizationName"]
      - eq: [content.data.initiator.initiatorOrganizeName, "$organizationName"]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserName"]
      - len_eq: [content.data.signerList, 2]
      - eq: [content.data.signerList.0.signerName, "$initiatorUserName"]
      - eq: [content.data.signerList.0.signerOrganizationName, null]
      - eq: [content.data.fillingUserList, []]

- test:
    name: "setup4-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $presetName0
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId: content.data.list.0.flowId
      - signFlowId1: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserName ]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationName ]
      - eq: [ "content.data.list.0.flowId","$_flowId" ]
      - eq: [ "content.data.list.0.processId","" ]
      - eq: [ "content.status",200 ]


        #todo: 发起直接静默签署完成的流程，检查结果页，无【去签署】
        #todo: 发起第二个人手动签署与发起人是同一人，检查结果页，【去签署】