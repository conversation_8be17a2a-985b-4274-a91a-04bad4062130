- config:
    name: "发起页设置模板的数据来源，验证下保存，无校验"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - batchTemplateTaskName: "自动化测试批量发起${get_randomNo()}"
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - templateIdNew: ${getTemplateId(1,2)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取模板详情"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $templateIdNew
      - version: 1
    extract:
      - templateNameCommon: content.data.templateName
      - templateFileKeyCommon: content.data.fileKey
      - contentId0: content.data.contentList.0.templateContentUuid
      - contentName0: content.data.contentList.0.contentName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "wordList": [],
        "saveSigners": null,
        "signersList": [],
        "contentList": [],
        "type": 3,
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "appendList": [
        {
          "appendType": 2,
          "templateInfo": {
            "fileKey": $templateFileKeyCommon,
            "fileName": $templateNameCommon,
            "templateId": $templateIdNew,
            "version": 1,
            "templateType": 1
          }
        }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "获取getInfo的接口的templateSnapshotId"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
      }
    extract:
      - templateSnapshotIdNew: content.data.appendList.0.templateInfo.templateSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "添加批量任务配置绑定模板内容域数据"
    variables:
      "params": {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "templateList": [
        {
          "templateName": $templateNameCommon,
          "templateId": $templateSnapshotIdNew,
          "version": 1
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/editTemplateContentDomain.yml
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]