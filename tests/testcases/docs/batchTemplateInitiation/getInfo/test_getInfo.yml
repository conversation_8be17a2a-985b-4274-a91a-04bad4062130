- test:
    name: "batchTemplateInitiationUuid为空"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "batchTemplateInitiationUuid已存在"
    variables:
      params: {
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid0
      }
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.batchTemplateInitiationUuid",$batchTemplateInitiationUuid0 ]
