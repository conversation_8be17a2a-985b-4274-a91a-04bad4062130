- config:
    name: "暂存批量任务-停用模板清除任务"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(sign01.userCode)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 3,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
    teardown_hooks:
      - ${sleep(1)}

###############集测容易发生错误：数据不存在。 需要等修复完这个bug之后再打开下面的应用##################
#- test:
#    name: "校验暂存数据"
#    variables:
#      params: {
#         "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
#      }
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.isNeedClear",1 ]

- test:
    name: "停用业务模板"
    variables:
      presetId: $presetIdCommon
    api: api/esignDocs/businessPreset/blockUp.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

#- test:
#    name: "再次校验暂存数据"
#    variables:
#      params: {
#         "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
#      }
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
##      - eq: [ "content.data.isNeedClear",0 ]  待确认校验项

- test:
    name: "启用业务模板"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $presetIdCommon,
        "presetSnapshotId": null,
        "presetName": $presetNameCommon,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 1,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "o",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

