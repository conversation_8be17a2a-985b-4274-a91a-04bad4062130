- config:
    name: "暂存批量任务-暂存合同到期时间草稿"
    variables:
      - name: "发起电子签署"
      - attachments0: []
      - CCInfosCBBT: []
      - orgCode1: ${ENV(csqs.orgCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - userCode0: ${ENV(sign01.userCode)}
      - userCode2: ${ENV(userCodeNoSeal)}
      - userNo0: ${ENV(sign01.accountNo)}
      - csqsNo: ${ENV(csqs.accountNo)}
      - userName0: ${ENV(sign01.userName)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - presetName0: "指定合同到期时间-$randomCount"
      - presetName1: "发起签署后固定时长-$randomCount"
      - presetName2: "签署完成后固定时长-$randomCount"
      - commonFileKey1: ${ENV(fileKey)}
      - YesDatetime1: ${getDateTime(-1,1)}
      - YesDatetime2: ${getDateTime(-1,2)}
      - todayTime1: ${getDateTime(0,2)}
      - tomorrowTime1: ${getDateTime(1,2)}
      - yearMonthDate: ${addYearsMonths(years=1, months=1,timeFormat=2)}  #一年一月后的日期
      - futureTime: ${getDateTime(60,2)}  #60天后的日期
      - tmp1: "encryptedValues="
      - tmp2: "&auth"
      - tmp3: "auth="
      - tmp4: None



#创建一个指定合同到期时间业务模板
- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId1: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId1
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第二步：签署方式，指定合同到期日期,提醒规则：提前30天每15天 10:00提醒，提醒次数3次"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId: $testPresetId1
      forceReadingTime: 0
      contractExpireSetting: "{\"enabled\":1,\"settings\":[{\"code\":\"CREATE_AFTER\",\"enabled\":0,\"value\":[]},{\"code\":\"SIGNED\",\"enabled\":0,\"value\":[]},{\"code\":\"CREATE\",\"enabled\":1,\"value\":[]}],\"notify\":{\"remindTime\":\"10:00\",\"remindCount\":3,\"remindBeforeDay\":30,\"remindEveryDay\":15}}"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "detail-查询业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - _contractExpireSetting1: content.data.signBusinessType.contractExpireSetting
      - _limitTemplateSealEnable1: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor1: content.data.signBusinessType.templateSealColor
      - _PresetName1: content.data.presetName
      - _PresetId1: content.data.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]



- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId1
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - initiatorUserNameCommon: content.data.initiatorUserName

    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - _batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "提交发起任务（内部个人签署）-指定合同到期时间，暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "presetVersion": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName0,
        "batchTemplateInitiationUuid": $_batchTemplateInitiationUuid0,
        "businessPresetUuid": $testPresetId1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "contractExpirationTime": $futureTime,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerType": 1,
                "signerTerritory": 1,
                "userName": $userName0,
                "userCode": $userCode0
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $commonFileKey1,
              "fileName": "测试"
            }
          }
        ],
        "fillList": [ ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "sealReason": null,
        "physicsSealUserList": [ ],
        "physicalSeal": 0,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]
    teardown_hooks:
      - ${sleep(10)}



#发起电子签署使用暂存的草稿，验证合同到期时间
- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: $_batchTemplateInitiationUuid0
      createNewDraft: 0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.batchTemplateInitiationUuid",$_batchTemplateInitiationUuid0 ]
      - contains: [ "content.data.contractExpirationTime",$futureTime ]
      - eq: [ "content.data.signFlowExpireTime",null]



#创建一个发起签署后固定时长的业务模板
- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName1
    extract:
      - testPresetId2: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName1]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - testBusinessTypeId2: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId2]
      - eq: [content.data.presetName, $presetName1]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName1
      presetId_addDetail: $testPresetId2
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第二步：签署方式，发起签署后固定时长1年1月,提醒规则：提前30天每15天 10:00提醒，提醒次数3次"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId2
      presetId: $testPresetId2
      forceReadingTime: 0
      contractExpireSetting: "{\"enabled\":1,\"settings\":[{\"code\":\"CREATE_AFTER\",\"enabled\":1,\"value\":[{\"code\":\"Y_M_UNIT\",\"enabled\":1,\"year\":1,\"month\":1,\"day\":0},{\"code\":\"D_UNIT\",\"enabled\":0,\"year\":0,\"month\":1,\"day\":1}]},{\"code\":\"SIGNED\",\"enabled\":0,\"value\":[{\"code\":\"Y_M_UNIT\",\"enabled\":1,\"year\":0,\"month\":1,\"day\":0},{\"code\":\"D_UNIT\",\"enabled\":0,\"year\":0,\"month\":1,\"day\":1}]},{\"code\":\"CREATE\",\"enabled\":0,\"value\":[{\"code\":\"Y_M_UNIT\",\"enabled\":1,\"year\":0,\"month\":1,\"day\":0},{\"code\":\"D_UNIT\",\"enabled\":0,\"year\":0,\"month\":1,\"day\":1}]}],\"notify\":{\"remindTime\":\"10:00\",\"remindCount\":3,\"remindBeforeDay\":30,\"remindEveryDay\":15}}"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId2
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "detail-查询业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - _contractExpireSetting2: content.data.signBusinessType.contractExpireSetting
      - _limitTemplateSealEnable2: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor2: content.data.signBusinessType.templateSealColor
      - _PresetName2: content.data.presetName
      - _PresetId2: content.data.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]



- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId2
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - initiatorUserNameCommon: content.data.initiatorUserName

    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - _batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "提交发起任务（内部个人签署）,页面不填写合同到期时间"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "presetVersion": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName1,
        "batchTemplateInitiationUuid": $_batchTemplateInitiationUuid2,
        "businessPresetUuid": $testPresetId2,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerType": 1,
                "signerTerritory": 1,
                "userName": $userName0,
                "userCode": $userCode0
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $commonFileKey1,
              "fileName": "测试"
            }
          }
        ],
        "fillList": [ ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "sealReason": null,
        "physicsSealUserList": [ ],
        "physicalSeal": 0,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    extract:
      - signFlowId003: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(10)}


#发起电子签署使用暂存的草稿，验证合同到期时间
- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: $_batchTemplateInitiationUuid2
      createNewDraft: 0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.batchTemplateInitiationUuid",$_batchTemplateInitiationUuid2 ]
      - eq: [ "content.data.businessPresetDetail.signBusinessType.contractExpireSetting",$_contractExpireSetting2 ]
      - eq: [ "content.data.signFlowExpireTime",null]
