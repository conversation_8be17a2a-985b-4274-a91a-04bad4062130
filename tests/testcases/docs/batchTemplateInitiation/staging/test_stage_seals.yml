- config:
    name: "签署区设置  落章筛选印章可用项目和自动落章"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - userCode0: ${ENV(csqs.userCode)}
#      - userName0: ${ENV(ceswdzxzdhyhwgd1.userName)}
      - orgCode0: ${ENV(csqs.orgCode)}
#      - orgName0: ${ENV(ceswdzxzdhyhwgd1.orgName)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - newVersion1: 1
      - sealGroupName: "印章测试${generate_random_str(6)}-可删"
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
      - presetName1: "自动化${getDateTime()}"



- test:
    name: "TC1-企业创建2枚模板印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        userCode: $userCode0
        organizationCode: $orgCode0
        sealGroupName: $sealGroupName
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealName: "SCENE-TC1-模板印章"
            sealSource: 2
          - sealPattern: 1
            sealName: "SCENE-TC2-模板印章"
            sealSource: 2
    extract:
      sealId001: content.data.sealInfos.0.sealId
      sealId002: content.data.sealInfos.1.sealId
      sealGroupId_001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/enterprise/electronic/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      remoteSealId: ""
    extract:
      sealTC1_001: content.data.list.0.sealId
      sealTC1_002: content.data.list.1.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

#授权用印人
#- test:
#    name: TC1-授权用印人所有人
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
#    variables:
#      sealsigners_json:
#        authorizationScope: 2
#        sealId: $sealId001
#        sealsignerType:  1
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - ne: [ content.data.sealId, "" ]
#
#
##授权用印人
#- test:
#    name: TC1-授权用印人所有人
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
#    variables:
#      sealsigners_json:
#        authorizationScope: 2
#        sealId: $sealId002
#        sealsignerType:  1
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - ne: [ content.data.sealId, "" ]

#授权可用项目为全部
- test:
    name: TC-企业印章授权可用项目为全部项目
    api: api/esignSeals/enterprise/authSealProject.yml
    variables:
      authorization0: ${getPortalToken($userCode0,${ENV(passwordEncrypt)})}
      sealId: $sealTC1_001
      sealProjectRange: 1
      projectIds: [ ]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, '服务器成功返回' ]
      - eq: [ content.success, True ]


#授权可用项目为全部
#- test:
#    name: TC-企业印章授权可用项目为全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/authSealProject.yml
#    variables:
#      sealId: $sealTC1_002
#      sealProjectRange: 1
#      projectIds: [ ]
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, '服务器成功返回' ]
#      - eq: [ content.success, True ]


#授权1000000项目自动签署
- test:
    name: TC-企业印章1授权1000000项目自动签署
    api: api/esignSigns/seals/authSealProject.yml
    variables:
      sealId: $sealTC1_001
      projectIds: ['1000000']
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, '服务器成功返回' ]
      - eq: [ content.success, True ]


#授权1000000项目自动签署
#- test:
#    name: TC-企业印章2授权无项目自动签署
#    api: api/signs/seals/authSealProject.yml
#    variables:
#      sealId: $sealTC1_002
#      projectIds: ['']
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, '服务器成功返回' ]
#      - eq: [ content.success, True ]

###################业务模板A-指定内部企业自动签署####
#- test:
#    name: "setup-获取文档模板详情"
#    api: api/esignDocs/template/manage/templateInfo.yml
#    variables:
#      - templateUuid: $newTemplateUuid1
#      - version: $newVersion1
#    extract:
#      - commonTemplateName: content.data.templateName
#      - fileKey0: content.data.fileKey
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - eq: ["content.data.fileType","pdf"]
#      - eq: ["content.data.status", "PUBLISH"]
#      - eq: ["content.data.status", "PUBLISH"]
#
#- test:
#    name: "TC2-新建一个业务模板"
#    api: api/esignDocs/businessPreset/create.yml
#    variables:
#      - presetName: $presetName1
#    extract:
#      - testPresetId1: content.data
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - ne: [content.data, null]
#
#- test:
#    name: "TC2-查看初始状态新建的业务模板详情，并获取businessTypeId"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId1
#    extract:
#      - testBusinessTypeId1: content.data.signBusinessType.businessTypeId
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.data.presetId, $testPresetId1]
#      - eq: [content.data.presetName, $presetName1]
#      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
#      - len_gt: [content.data.signBusinessType.messageConfigList, 1]
#
#- test:
#    name: "TC2-业务模板配置-第一步：填写基本信息（关联pdf模板）"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      presetId_addDetail: $testPresetId1
#      allowAddFile: 0
#      presetName: $presetName1
#      templateList:
#        - fileKey: $fileKey0
#          templateId: $newTemplateUuid1
#          templateName: $commonTemplateName
#          version: $newVersion1
#          templateType: 1
#          contentDomainCount: 0
#          includeSpecialContent: 0
#    validate:
#      - eq: [content.message,"成功"]
#      - eq: [content.status,200]
#
#- test:
#    name: "TC2-业务模板配置-第二步：签署方式（默认配置）"
#    api: api/esignDocs/businessPreset/addBusinessType.yml
#    variables:
#      businessTypeName: $presetName1
#      businessTypeId: $testBusinessTypeId1
#      presetId_addBusinessType: $testPresetId1
#    validate:
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#
#- test:
#    name: "TC2-通过业务模板配置id获取签署区列表"
#    api: api/esignDocs/businessPreset/getSignatoryList.yml
#    variables:
#      - presetId: $testPresetId1
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data", null]
#
#- test:
#    name: "TC2-业务模板配置-第三步：签署方设置（设置签署方）"
#    api: api/esignDocs/businessPreset/addSigners.yml
#    variables:
#      presetId: $testPresetId1
#      status: 1
#      needGather: 0
#      needAudit: 0
#      sort: 0
#      allowAddSigner: 0
#      allowAddSealer: 1
#      signerList:
#        - autoSign: 0
#          assignSigner: 1
#          organizeCode: "$orgCode0"
#          userName: "$userName0"
#          organizeName: "$orgName0"
#          departmentName: "$orgName0"
#          sealTypeCode: ""
#          signerTerritory: 1
#          signerType: 2
#          userCode: "$userCode0"
#          sealTypeName: ""
#          departmentCode: "$orgCode0"
#          signatoryList:
#            - signatoryId: $signatoryIdA
#              templateId: $newTemplateUuid1
#              signatoryName: $signatoryNameA
#              templateName: $commonTemplateName
#              sealType: 2
#            - signatoryId: $signatoryIdB
#              templateId: $newTemplateUuid1
#              signatoryName: $signatoryNameB
#              templateName: $commonTemplateName
#              sealType: 2
#    validate:
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#
#- test:
#    name: "获取业务模板详情"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      - getPresetId: $testPresetId1
#    extract:
#      - presetNameCommon: content.data.presetName
#    validate:
#      - eq: ["content.message","成功"]
#
#- test:
#    name: "获取getInfo的接口的batchTemplateInitiationUuid"
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    variables:
#      "params": { }
#    extract:
#      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "获取发起人关联的组织信息"
#    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
#    variables:
#      - businessPresetUuid: $testPresetId1
#    extract:
#      - departmentCode1: content.data.list.0.departmentCode
#      - departmentName1: content.data.list.0.departmentName
#      - organizationCode1: content.data.list.0.organizationCode
#      - organizationName1: content.data.list.0.organizationName
#      - initiatorUserName1: content.data.initiatorUserName
#    validate:
#      - eq: ["content.message","成功"]
#
#- test:
#    name: "暂存任务"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $presetNameCommon,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "initiatorOrganizeCode": $organizationCode1,
#        "initiatorOrganizeName": $organizationName1,
#        "initiatorUserName": $initiatorUserName1,
#        "initiatorDepartmentName": $departmentName1,
#        "initiatorDepartmentCode": $departmentCode1,
#        "businessPresetUuid": $testPresetId1,
#        "signersList": [
#          {
#              "signMode": 1,
#              "signerList": [
#                {
#                  "signerType": 2,
#                  "signerTerritory": 1,
#                  "draggable": true,
#                  "organizeCode": $orgCode0,
#                  "userCode": $userCode0,
#                  "sealTypeCode": null,
#                  "autoSign": 1,
#                  "assignSigner": 1,
#                  "userName": ""
#                }
#              ]
#          }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#          {
#            "appendType": 1,
#            "attachmentInfo": {
#              "fileKey": $signPdfFileKey,
#              "fileName": $signPdfFileName
#            }
#          }
#        ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#
#- test:
#    name: "签署区设置  落章"
#    api: api/esignDocs/signTools/seals.yml
#    variables:
#      organizationCode: $orgCode0
#      userCode: $userCode0
#      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
#      autoSign: 1
#    validate:
#      - eq: ["content.message","成功"]