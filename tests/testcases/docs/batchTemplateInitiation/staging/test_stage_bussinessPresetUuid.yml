- config:
    name: "暂存批量任务-业务模板校验"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}
      - excelFileName1: "双方签署_内部个人指定签署方+外部个人签署.xlsx"
      - excelFileKey1: ${attachment_upload($excelFileName1)}
      - lowXlsxfileName: "checkExcel_无签署方无内容域.xlsx"
      - excelFileLowXlsxKey: ${attachment_upload($lowXlsxfileName)}

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "停用业务模板"
    variables:
      presetId: $presetIdCommon
    api: api/esignDocs/businessPreset/blockUp.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存任务"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","您选择的业务模板已停用，请重新发起"]
      - eq: ["content.status",1602030]
      - eq: ["content.data",null]

- test:
    name: "启用业务模板"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $presetIdCommon,
        "presetSnapshotId": null,
        "presetName": $presetNameCommon,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 1,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "o",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#- test:
#    name: "获取内部用户信息"
#    variables:
#      - userCode: $innerUser
#    api: api/esignDocs/user/getUserOrg.yml
#    extract:
#      - innerUserName: content.data.userName
#      - innerUserId: content.data.userId
#      - innerUserCode: content.data.userCode
#      - innerOrganizationId: content.data.organizationId
#      - innerOrganizationName: content.data.organizationName
#      - innerOrganizationCode: content.data.organizationCode
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "修改权限范围"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      - presetId: $presetIdCommon
#      - presetName: $presetNameCommon
#      - signBusinessTypeId: $businessTypeIdCommon
#      - initiatorAll: 0
#      - multiSigner: 1
#      - templateId: $newTemplateUuidCommon
#      - templateName: $templateNameCommon
#      - version: $newVersionCommon
#      - checkRepetition: 1
#      - initiatorList: [{
#        "userId": $innerUserId,
#        "userCode": $innerUserCode,
#        "userName": $innerUserName,
#        "organizationId": $innerOrganizationId,
#        "organizationCode": $innerOrganizationCode,
#        "organizationName": $innerOrganizationName
#      }]
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "通过业务模板配置id获取签署区列表"
#    api: api/esignDocs/businessPreset/getSignatoryList.yml
#    variables:
#      - presetId: $presetIdCommon
#    extract:
#      - name: content.data.templateList.0.name
#      - signatoryNameB: content.data.templateList.0.simpleVOList.0.name
#      - signTypeB: content.data.templateList.0.simpleVOList.0.signType
#      - signatoryIdB: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameA: content.data.templateList.0.simpleVOList.1.name
#      - signTypeA: content.data.templateList.0.simpleVOList.1.signType
#      - signatoryIdA: content.data.templateList.0.simpleVOList.1.id
#
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "开启流程"
#    api: api/esignDocs/businessPreset/addSigners.yml
#    variables:
#      "params": {
#        "presetId": $presetIdCommon,
#        "status": 1,
#        "needGather": 1,
#        "needAudit": 1,
#        "sort": 0,
#        "signerList": [
#          {
#            "autoSign": 0,
#            "organizeCode": "",
#            "sealTypeCode": "",
#            "signatoryList": [
#              {
#                "signatoryId": $signatoryIdA,
#                "templateId": $newTemplateUuidCommon,
#                "signatoryName": $signatoryNameA,
#                "templateName": $templateNameCommon,
#                "sealType": 1
#              }
#            ],
#            "signerTerritory": 1,
#            "signerType": $signTypeA,
#            "userCode": $userCodeCommon,
#            "sealTypeName": "",
#            "departmentCode": ""
#          },
#          {
#            "autoSign": 0,
#            "organizeCode": "",
#            "sealTypeCode": "",
#            "signatoryList": [
#              {
#                "signatoryId": $signatoryIdB,
#                "templateId": $newTemplateUuidCommon,
#                "signatoryName": $signatoryNameB,
#                "templateName": $templateNameCommon,
#                "sealType": 1
#              }
#            ],
#            "signerTerritory": 2,
#            "signerType": $signTypeB,
#            "userCode": "",
#            "sealTypeName": "",
#            "departmentCode": ""
#          }
#        ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC2-获取batchTemplateInitiationUuid-停用模板清除暂存任务"
#    variables:
#      params: {}
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    extract:
#      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC3-暂存批量任务-无业务模板权限"
#    variables:
#      params: {
#        "batchTemplateInitiationName": $batchTemplateTaskName,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid0,
#        "businessPresetUuid": $presetIdCommon,
#        "initiatorOrganizeCode": $organizationCodeCommon,
#        "initiatorOrganizeName": $getOrganizationNameCommon,
#        "initiatorUserName": $userNameCommon,
#        "type": 1
#      }
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    validate:
#      - eq: [ "content.message","无此业务模板的权限" ]
#      - eq: [ "content.status",1602031 ]