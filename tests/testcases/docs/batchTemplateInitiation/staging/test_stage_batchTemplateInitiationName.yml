- config:
    name: "暂存批量任务-batchTemplateInitiationName校验"
    variables:
      - batchTemplateTaskName1: "自动化测试批量发起${random_str(91)}"
      - batchTemplateTaskName2: "自动化测试批量发起${random_str(92)}"
      - batchTemplateTaskName3: "自动化测试批量发起\/:*?<>|"
      - batchTemplateTaskName4: ""
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务-长度位100字符"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskName1,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "暂存任务-长度位101字符"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskName2,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $initiatorUserCode,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $initiatorUserName
                }
              ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $signPdfFileKey,
              "fileName": $signPdfFileName
            }
          }
        ]
      }
    validate:
      - eq: ["content.message","流程主题名称最大长度100位"]
      - eq: ["content.status",1600017]

#- test:
#    name: "暂存任务-包含特殊字符"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $batchTemplateTaskName3,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": $departmentName,
#        "initiatorDepartmentCode": $departmentCode,
#        "businessPresetUuid": $presetIdCommon,
#        "signersList": [
#          {
#              "signMode": 1,
#              "signerList": [
#                {
#                  "signerType": 1,
#                  "signerTerritory": 1,
#                  "draggable": true,
#                  "organizeCode": "",
#                  "userCode": $initiatorUserCode,
#                  "sealTypeCode": null,
#                  "autoSign": 0,
#                  "assignSigner": 1,
#                  "userName": $initiatorUserName
#                }
#              ]
#          }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#          {
#            "appendType": 1,
#            "attachmentInfo": {
#              "fileKey": $signPdfFileKey,
#              "fileName": $signPdfFileName
#            }
#          }
#        ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "暂存任务-为空"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $batchTemplateTaskName4,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": $departmentName,
#        "initiatorDepartmentCode": $departmentCode,
#        "businessPresetUuid": $presetIdCommon,
#        "signersList": [
#          {
#              "signMode": 1,
#              "signerList": [
#                {
#                  "signerType": 1,
#                  "signerTerritory": 1,
#                  "draggable": true,
#                  "organizeCode": "",
#                  "userCode": $initiatorUserCode,
#                  "sealTypeCode": null,
#                  "autoSign": 0,
#                  "assignSigner": 1,
#                  "userName": $initiatorUserName
#                }
#              ]
#          }
#        ],
#        "type": 1,
#        "sort": 0,
#        "chargingType": 1,
#        "appendList": [
#          {
#            "appendType": 1,
#            "attachmentInfo": {
#              "fileKey": $signPdfFileKey,
#              "fileName": $signPdfFileName
#            }
#          }
#        ]
#      }
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
