- config:
    name: "获取模板快照详情信息-正向case"
    variables:
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}
      - templateIdCommon: ${getTemplateId(0,0)}
      - batchTemplateInitiationName: "自动化测试批量起任务-${get_randomNo()}"

- test:
    name: "获取业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid0: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取模板详情"
    api: api/esignDocs/template/owner/templateInfo.yml
    variables:
      - templateUuid: $templateIdCommon
      - version: 1
    extract:
      - templateNameCommon: content.data.templateName
      - templatefileKeyCommon: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存任务"
    variables:
      {
        "params": {
          "batchTemplateInitiationName": $batchTemplateInitiationName,
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuid0,
          "businessPresetUuid": $presetIdCommon,
          "initiatorOrganizeCode": $organizationCode,
          "initiatorOrganizeName": $organizationName,
          "initiatorUserName": $initiatorUserName,
          "initiatorDepartmentName": $departmentCode,
          "initiatorDepartmentCode": $departmentName,
          "signersList": [],
          "type": 3,
          "sort": 0,
          "advertisement": false,
          "chargingType": 1,
          "readComplete": false,
          "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "fileKey": $templatefileKeyCommon,
              "fileName": $templateNameCommon,
              "templateId": $templateIdCommon,
              "version": 1,
              "templateType": 1
            }
          }
          ]
        }
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取templateSnapshotId"
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid0
      }
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - templateSnapshotId: content.data.appendList.0.templateInfo.templateSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


#- test:
#    name: "获取模板快照详情信息"
#    api: api/esignDocs/batchTemplateInitiation/getTemplateSnapshootDetail.yml
#    variables:
#        templateUuid: $templateSnapshotId
#        version: -1
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - eq: ["content.data.templateName",$templateNameCommon]
