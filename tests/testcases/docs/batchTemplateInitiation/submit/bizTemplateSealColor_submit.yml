- config:
    name: V6.0.13.0-beta.2-支持模板印章颜色设置,不限制模板印章颜色和限制模板印章颜色场景
    variables:
      - name: "使用发起电子签署-限制模板颜色的业务模板发起流程"
      - attachments0: []
      - CCInfosCBBT: []
      - orgCode1: ${ENV(csqs.orgCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - userCode0: ${ENV(sign01.userCode)}
      - userCode2: ${ENV(userCodeNoSeal)}
      - userNo0: ${ENV(sign01.accountNo)}
      - csqsNo: ${ENV(csqs.accountNo)}
      - userName0: ${ENV(sign01.userName)}
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
#      - businessTypeCode1: "${ENV(businessTypeCode)}" #不限制模板印章颜色
      - businessTypeCode0: "${getBusinessTypeId2(12)}" #自动化勿动12-校验个人页面签署方式只能普通手绘和蓝色印章
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - presetName0: "自动化-其他填写方$randomCount"
      - mainHost: ${ENV(esign.projectHost)}
      - pdfFileName: "testppp.pdf"


######################################
#限制模板印章颜色,使用业务模板businessTypeCode0发起电子签署
- test:
      name: 查询电子签署业务模板详情
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode0
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCode0 ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许发起时添加签署方
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许追加签署文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]
      extract:
          - docTemplateId0: content.data.docTemplateInfos.0.docTemplateId
          - docTemplateName0: content.data.docTemplateInfos.0.docTemplateName
          - signerId0: content.data.signerInfos.0.signerId
          - signerId1: content.data.signerInfos.1.signerId
          - signUserCode0: content.data.signerInfos.0.signerAccountInfo.userCode
          - signCustomAccountNo0: content.data.signerInfos.0.signerAccountInfo.customAccountNo
          - signUserName0: content.data.signerInfos.0.signerAccountInfo.userName
          - signOrganizationCode0: content.data.signerInfos.0.signerAccountInfo.organizationCode
          - signDepartmentCode0: content.data.signerInfos.0.signerAccountInfo.departmentCode
          - signUserCode1: content.data.signerInfos.1.signerAccountInfo.userCode
          - signCustomAccountNo1: content.data.signerInfos.1.signerAccountInfo.customAccountNo
          - signUserName1: content.data.signerInfos.1.signerAccountInfo.userName

- test:
    name: "list-业务模板列表"
    api: api/esignDocs/businessPreset/list.yml
    variables:
        businessTypeId: $businessTypeCode0
    extract:
        - _presetId0: content.data.list.0.presetId
        - -presetName0: content.data.list.0.presetName
        - _businessTypeId0: content.data.list.0.businessTypeId
    validate:
        - eq: [content.message, "成功"]
        - eq: [content.status, 200]
        - ge: [content.data.total, 1]
        - eq: [content.data.list.0.status, 1]

- test:
    name: "查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $docTemplateId0}
    extract:
      - sealControlId0: content.data.sealControl.0.sealControlId
      - sealControlName0: content.data.sealControl.0.sealControlName
      - sealControlId1: content.data.sealControl.1.sealControlId
      - sealControlName1: content.data.sealControl.1.sealControlName
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$docTemplateId0" ]
      - eq: [ content.data.contentsControl, [] ]

- test:
    name: "detail-查询业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $_presetId0
    extract:
      - _handColor0: content.data.signBusinessType.handColor
      - _limitTemplateSealEnable0: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor0: content.data.signBusinessType.templateSealColor
      - _PresetName: content.data.presetName
      - _PresetId: content.data.presetId
      - _templateId: content.data.templateList.0.templateId
      - _templateFileKey: content.data.templateList.0.fileKey
      - _templateName: content.data.templateList.0.templateName
      - _signUserCode0: content.data.signerNodeList.0.signerList.0.userCode
      - _signUserName0: content.data.signerNodeList.0.signerList.0.userName
      - _signOrganizationCode0: content.data.signerNodeList.0.signerList.0.organizationCode
      - _signOrganizationName0: content.data.signerNodeList.0.signerList.0.organizationName
      - _signUserCode1: content.data.signerNodeList.0.signerList.1.userCode
      - _signUserName1: content.data.signerNodeList.0.signerList.1.userName

    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $_presetId0
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - initiatorUserNameCommon: content.data.initiatorUserName

    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - _batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "提交发起任务"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "presetVersion": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $_PresetName,
        "batchTemplateInitiationUuid": $_batchTemplateInitiationUuid1,
        "businessPresetUuid": $_PresetId,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "signersList": [
          {
            "signMode": 2,
            "signNode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerId": $signerId0,
                "signerType": 2,
                "signerTerritory": 1,
                "organizationCode": $_signOrganizationCode0,
                "organizationName": $_signOrganizationName0,
                "userName": $_signUserName0,
                "userCode": $_signUserCode0,
                'signatoryList': [ {
                  'templateId': $docTemplateId0,
                  'templateName': $docTemplateName0,
                  'signatoryId': $sealControlId0,
                  'signatoryName': $sealControlName0,
                  'sealType': 2,
                  'templateNameWithSignatoryName': null
                },
                "userList": [
                            {
                                "id": "",
                                "name": $_signUserName0,
                                "organizeCode": $_signOrganizationCode0,
                                "organizeName": $_signOrganizationName0
                            }
                        ],
                "organizeList": [
                            {
                                "id": "",
                                "name": $_signOrganizationName0
                            }
                        ],
                ]
              },
              {
                "assignSigner": 1,
                "signerId": $signerId1,
                "signerType": 1,
                "signerTerritory": 1,
                "userName": $_signUserName1,
                "userCode": $_signUserCode1,
                'signatoryList': [ {
                  'templateId': $docTemplateId0,
                  'templateName': $docTemplateName0,
                  'signatoryId': $sealControlId1,
                  'signatoryName': $sealControlName1,
                  'sealType': 2,
                  'templateNameWithSignatoryName': null,
                  "userList": [
                    {
                      "id": "",
                      "name": $_signUserName1
                    }
                  ],
                } ]
              }
            ]
          }

        ],
        "fillList": [ ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "sealReason": null,
        "physicsSealUserList": [ ],
        "physicalSeal": 0,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    extract:
      - signFlowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: detail-signFlowId-签署-检查可使用印章颜色
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    extract:
      - signConfigInfo001: content.data.signConfigInfo
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $signFlowId ]
      - eq: [ content.data.handScope, "1" ]
      - eq: [ content.data.aiHandScope, "" ]
      - eq: [ content.data.templateSealScope, "1,2" ]
      - eq: [ content.data.customSealScope, "2" ]
      - eq: [ content.data.aiHandEnable, 0 ]
      - eq: [ content.data.handColor, $_handColor0 ]
      - eq: [ content.data.templateSealColor, $_templateSealColor0 ]
      - eq: [ content.data.limitTemplateSealEnable, $_limitTemplateSealEnable0 ]
      - eq: [ content.data.currentGovSignFlag, 0 ]

############################################################################
#不限制模板印章颜色,使用页面发起电子签署

- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]


- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId1: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId1
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置,不限制模板印章颜色）"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId: $testPresetId1
      limitTemplateSealEnable: 0
      allowApproverAddFile: 0
      allowApproverAddAttachment: 0
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "detail-查询业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - _handColor1: content.data.signBusinessType.handColor
      - _limitTemplateSealEnable1: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor1: content.data.signBusinessType.templateSealColor
      - _PresetName1: content.data.presetName
      - _PresetId1: content.data.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]



- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId1
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - initiatorUserNameCommon: content.data.initiatorUserName

    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - _batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "上传一个pdf签署文件"
    api: api/esignDocs/fileSystem/commonUpload.yml
    variables:
      fileName: $pdfFileName
      file_path: "data/$pdfFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]
    extract:
      - commonFileKey1: content.data.fileKey


- test:
    name: "提交发起任务（内部个人签署）"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "presetVersion": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName0,
        "batchTemplateInitiationUuid": $_batchTemplateInitiationUuid2,
        "businessPresetUuid": $testPresetId1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerType": 1,
                "signerTerritory": 1,
                "userName": $userName0,
                "userCode": $userCode0
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $commonFileKey1,
              "fileName": "测试"
            }
          }
        ],
        "fillList": [ ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "sealReason": null,
        "physicsSealUserList": [ ],
        "physicalSeal": 0,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    extract:
      - signFlowId001: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: detail-signFlowId-内部个人签署-检查可使用印章颜色
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId001
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    extract:
      - signConfigInfo001: content.data.signConfigInfo
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $processId ]
      - eq: [ content.data.handScope, "1,2" ]
      - eq: [ content.data.aiHandScope, "1,2" ]
      - eq: [ content.data.templateSealScope, "1,2" ]
      - eq: [ content.data.customSealScope, "1,2" ]
      - eq: [ content.data.aiHandEnable, 1 ]
      - eq: [ content.data.handColor, $_handColor1 ]
      - eq: [ content.data.templateSealColor, $_templateSealColor1 ]
      - eq: [ content.data.limitTemplateSealEnable, $_limitTemplateSealEnable1 ]
      - eq: [ content.data.currentGovSignFlag, 0 ]


- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params: {}
    extract:
      - _batchTemplateInitiationUuid3: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "提交发起任务（内部机构签署）"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "presetVersion": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName0,
        "batchTemplateInitiationUuid": $_batchTemplateInitiationUuid3,
        "businessPresetUuid": $testPresetId1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "initiatorUserName": $initiatorUserNameCommon,
        "saveSigners": null,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "assignSigner": 1,
                "signerType": 2,
                "userName": $userName0,
                "userCode": $userCode0,
                "signerTerritory": 1,
                "organizationCode": $orgCode0,
                "organizationName": $orgName0
              }
            ]
          }
        ],
        "appendList": [
          {
            "appendType": 1,
            "attachmentInfo": {
              "fileKey": $commonFileKey1,
              "fileName": "测试"
            }
          }
        ],
        "fillList": [ ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "sealReason": null,
        "physicsSealUserList": [ ],
        "physicalSeal": 0,
        "chargingType": 1,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    extract:
      - signFlowId002: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(10)}


- test:
    name: detail-signFlowId-内部机构签署-检查可使用印章颜色
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId002
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: $orgCode0
    extract:
      - signConfigInfo001: content.data.signConfigInfo
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $processId ]
      - eq: [ content.data.handScope, "1,2" ]
      - eq: [ content.data.aiHandScope, "1,2" ]
      - eq: [ content.data.templateSealScope, "1,2" ]
      - eq: [ content.data.customSealScope, "1,2" ]
      - eq: [ content.data.aiHandEnable, 1 ]
      - eq: [ content.data.handColor, $_handColor1 ]
      - eq: [ content.data.templateSealColor, $_templateSealColor1 ]
      - eq: [ content.data.limitTemplateSealEnable, $_limitTemplateSealEnable1 ]
      - eq: [ content.data.currentGovSignFlag, 0 ]



