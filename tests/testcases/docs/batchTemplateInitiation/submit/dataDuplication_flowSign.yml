- config:
    name: "发起批量任务-带审批流程"
    variables:
      batchTemplateInitiationName01: "自动化测试批量起任务-${get_randomNo()}"
      batchTemplateInitiationName02: "自动化测试批量起任务-${get_randomNo()}"
      batchTemplateInitiationName03: "自动化测试批量起任务-${get_randomNo()}"
      batchTemplateInitiationName04: "自动化测试批量起任务-${get_randomNo()}"
      batchTemplateInitiationName05: "自动化测试批量起任务-${get_randomNo()}"
      batchTemplateInitiationName06: "自动化测试批量起任务-${get_randomNo()}"
      autoPresetName: "自动化测试业务模板-${get_randomNo_16()}"
      randomSignerId0: ${get_randomNo_32()}
      userAccountKey: "ceswdzxzdhyhwgd1.account"
      userpasswordKey: "ceswdzxzdhyhwgd1.password"
      userNameKey: "ceswdzxzdhyhwgd1.userName"
      userAccount: ${ENV($userAccountKey)}
      autotestModelName: "发起电子签署流程"
      autotestModelKey: ${getWorkFlowModelKey($autotestModelName)}
      autotestWorkFlowModelName: "【电子签署】-$autotestModelName"
      signerUserName_inner: ${ENV($userNameKey)}
      signerUserCode_inner: ${get_inner_UserCode(userAccount=$userAccount)}
      fileName1: "审批处理人_正常数据.xlsx"
      fileKey1: ${attachment_upload($fileName1)}
      fileName2: "审批处理人_下一环节处理人为空.xlsx"
      fileKey2: ${attachment_upload($fileName2)}
      fileName3: "审批处理人_非竞争审批_下一环节处理人为多用户.xlsx"
      fileKey3: ${attachment_upload($fileName3)}
      fileName4: "审批处理人_下一环节处理人格式错误.xlsx"
      fileKey4: ${attachment_upload($fileName4)}
      fileName5: "审批处理人_下一环节处理人组织不存在.xlsx"
      fileKey5: ${attachment_upload($fileName5)}
      fileName6: "审批处理人_下一环节处理人用户不存在.xlsx"
      fileKey6: ${attachment_upload($fileName6)}




- test:
    name: "case1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "case2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]


- test:
    name: "case3-创建文档，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate2.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "case4-关联模板、流程引擎到业务模板并保存"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
       - allowAddFile: 1
       - initiatorAll: 1
       - initiatorList: []
       - presetId: $testPresetId
       - presetName: $autoPresetName
       - templateList: [{
         "templateId": $newTemplateUuidCommon,
         "templateName": $templateNameCommon,
         "version": $newVersionCommon
       }
       ]
       - workFlowModelKey: $autotestModelKey
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "case5-获取业务模板的签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - testTemplateName: content.data.templateList.0.name
#      - signatoryName1: content.data.templateList.0.simpleVOList.0.name
#      - signType1: content.data.templateList.0.simpleVOList.0.signType
#      - signatoryId1: content.data.templateList.0.simpleVOList.0.id
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "case6-业务模板第三步：添加签署方（内部个人指定（测试文档中心自动化用户勿改动））"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": $autotestModelKey,
        "workFlowModelName": $autotestWorkFlowModelName,
        "workFlowModelStatus": 1,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
          {
            "signMode": 1,
            "id": "node-1",
            "signerList": [
              {
                "signerType": 1,
                "signerTerritory": 1,
                "draggable": true,
                "organizeCode": "",
                "userCode": $signerUserCode_inner,
                "id": "add-1",
                "sealTypeCode": "",
                "sealTypeList": [ ],
                "accountList": [ ],
                "organizeList": [ ],
                "autoSign": 0,
                "assignSigner": 1,
                "signerId": $randomSignerId0,
                "organizeName": "",
                "sealTypeName": "",
                "userName": $signerUserName_inner,
                "departmentCode": "",
                "departmentName": "",
                "signatoryList": [
                  {
                    "signatoryId": $signatoryId1,
                    "templateId": $newTemplateUuidCommon,
                    "signatoryName": $signatoryName1,
                    "templateName": $templateNameCommon,
                    "sealType": 1
                  }
                ]
              }
            ]
          }
        ]
    }
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "case7-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case8-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case9-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId0: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList0: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount1: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode1: content.data.signerNodeList.0.signerList.0.userCode
      - userName1: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case10-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName01,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId0,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName1,
                        "userCode": $userCode1,
                        "userAccount": $userAccount1,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList0,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case11-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
      }
    extract:
      - templateInitiationSignersUuid_inner: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case12-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
      - excelFileKey: $fileKey1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case13-流程新增实例和获取对应流程的相关信息，submit提交时下一环节审批人使用默认的审批人"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName: content.data.nextNodeHandlerName
      - testThisFormUrl: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case14-提交批量任务-处理人为正常数据"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName01,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": $fileKey1,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner,
              "signerId": $signerId_inner,
              "signerSnapshotId": $signerSnapshotId_inner,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount1,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList0
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case15-查询电子签署列表-通过flowName存在查询此流程为审批中状态
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName01
    extract:
      signFlowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 1 ]
      - eq: [ json.data.list.0.flowStatus, "2" ]





- test:
    name: "case15-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew02: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case16-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId1: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList1: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount2: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode2: content.data.signerNodeList.0.signerList.0.userCode
      - userName2: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case17-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName02,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId1,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName2,
                        "userCode": $userCode2,
                        "userAccount": $userAccount2,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList1,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case18-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02
      }
    extract:
      - templateInitiationSignersUuid_inner02: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner02: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner02: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case19-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew02
      - excelFileKey: $fileKey2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case20-流程新增实例和获取对应流程的相关信息"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode02: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode02: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName02: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode02: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName02: content.data.nextNodeHandlerName
      - testThisFormUrl02: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case21-提交批量任务-下一环节处理人为空"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName02,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "excelFileKey": $fileKey2,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner02,
              "signerId": $signerId_inner02,
              "signerSnapshotId": $signerSnapshotId_inner02,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount2,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList1
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode02,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case22-查询电子签署列表-流程发起失败，无此流程
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName02
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list, [] ]



- test:
    name: "case23-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew03: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case24-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId3: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList3: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount3: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode3: content.data.signerNodeList.0.signerList.0.userCode
      - userName3: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case25-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName03,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId3,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName3,
                        "userCode": $userCode3,
                        "userAccount": $userAccount3,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList3,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case26-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03
      }
    extract:
      - templateInitiationSignersUuid_inner03: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner03: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner03: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case27-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew03
      - excelFileKey: $fileKey3
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case28-流程新增实例和获取对应流程的相关信息"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode03: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode03: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName03: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode03: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName03: content.data.nextNodeHandlerName
      - testThisFormUrl03: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case29-提交批量任务-非竞争审批-下一环节处理人为多个用户"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName03,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03,
        "excelFileKey": $fileKey3,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner03,
              "signerId": $signerId_inner03,
              "signerSnapshotId": $signerSnapshotId_inner03,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount3,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList3
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode03,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case30-查询电子签署列表-查询电子签署列表-流程发起失败，无此流程
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName03
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list, [] ]



- test:
    name: "case31-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew04: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case32-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId4: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList4: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount4: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode4: content.data.signerNodeList.0.signerList.0.userCode
      - userName4: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case33-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName04,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew04,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId3,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName4,
                        "userCode": $userCode4,
                        "userAccount": $userAccount4,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList4,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case34-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew04
      }
    extract:
      - templateInitiationSignersUuid_inner04: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner04: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner04: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case35-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew04
      - excelFileKey: $fileKey4
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case36-流程新增实例和获取对应流程的相关信息"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode04: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode04: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName04: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode04: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName04: content.data.nextNodeHandlerName
      - testThisFormUrl04: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case37-提交批量任务-下一环节处理人格式错误"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName04,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew04,
        "excelFileKey": $fileKey4,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner04,
              "signerId": $signerId_inner04,
              "signerSnapshotId": $signerSnapshotId_inner04,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount4,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList4
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode04,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case38-查询电子签署列表-查询电子签署列表-流程发起失败，无此流程
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName04
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list, [] ]



- test:
    name: "case39-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew05: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case40-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId4: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList4: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount4: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode4: content.data.signerNodeList.0.signerList.0.userCode
      - userName4: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case41-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName05,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew05,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId3,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName4,
                        "userCode": $userCode4,
                        "userAccount": $userAccount4,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList4,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case42-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew05
      }
    extract:
      - templateInitiationSignersUuid_inner05: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner05: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner05: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case43-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew05
      - excelFileKey: $fileKey5
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case44-流程新增实例和获取对应流程的相关信息"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode05: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode05: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName05: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode05: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName05: content.data.nextNodeHandlerName
      - testThisFormUrl05: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case45-提交批量任务-下一环节处理人格式错误"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName05,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew05,
        "excelFileKey": $fileKey5,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner05,
              "signerId": $signerId_inner05,
              "signerSnapshotId": $signerSnapshotId_inner05,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount4,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList4
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode05,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case46-查询电子签署列表-查询电子签署列表-流程发起失败，无此流程
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName05
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list, [] ]


- test:
    name: "case47-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew06: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "case48-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId4: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList4: content.data.signerNodeList.0.signerList.0.signatoryList
      - userAccount4: content.data.signerNodeList.0.signerList.0.userAccount
      - userCode4: content.data.signerNodeList.0.signerList.0.userCode
      - userName4: content.data.signerNodeList.0.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case49-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName06,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew06,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
            {
                "signNode": 3,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $signerId3,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 1,
                        "userName": $userName4,
                        "userCode": $userCode4,
                        "userAccount": $userAccount4,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 3,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": $signatoryList4,
                        "userList":                             {
                                "id": $signerUserCode_inner,
                                "name": $signerUserName_inner,
                                "mobile": "",
                                "organizeCode": null,
                                "organizeName": null
                            },
                        "option": [
                            {
                                "value": "1-1",
                                "label": "内部个人",
                                "children": [
                                    {
                                        "value": 1,
                                        "label": "指定签署方"
                                    },
                                    {
                                        "value": 0,
                                        "label": "不指定签署方"
                                    }
                                ]
                            }
                        ],
                        "default": [
                            "1-1",
                            1
                        ]
                    }
                ],
                "id": 1,
                "draggable": false
            }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case50-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
          "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew06
      }
    extract:
      - templateInitiationSignersUuid_inner06: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_inner06: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_inner06: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case51-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew06
      - excelFileKey: $fileKey6
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case52-流程新增实例和获取对应流程的相关信息"
    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
    variables:
       modelKey: $autotestModelKey
    extract:
      - testNestNodeConfigCode06: content.data.nextNodeConfigList.0.nodeConfigCode
      - testNextHandlerOrganizationCode06: content.data.nextHandlerOrganizationCode
      - testNextHandlerOrganizationName06: content.data.nextHandlerOrganizationName
      - testNextNodeHandlerCode06: content.data.nextNodeHandlerCode
      - testNextNodeHandlerName06: content.data.nextNodeHandlerName
      - testThisFormUrl06: content.data.nodeConfig.formUrl
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.success,true ]

- test:
    name: "case53-提交批量任务-下一环节处理人格式错误"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName06,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew06,
        "excelFileKey": $fileKey6,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
            {
              "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner06,
              "signerId": $signerId_inner06,
              "signerSnapshotId": $signerSnapshotId_inner06,
              "signerTerritory": 1,
              "signerType": 1,
              "assignSigner": 1,
              "userName": $signerUserName_inner,
              "userCode": $signerUserCode_inner,
              "userAccount": $userAccount4,
              "legalSign": 0,
              "organizeName": null,
              "organizeCode": null,
              "departmentName": null,
              "departmentCode": null,
              "sealTypeCode": null,
              "sealTypeName": null,
              "autoSign": 0,
              "needGather": 0,
              "signNode": 3,
              "signOrder": 1,
              "signMode": 1,
              "signerStatus": null,
              "signerStatusStr": null,
              "signatoryList": $signatoryList4
            }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "auditInfo": {
            "auditResult": "",
            "carbonCopyList": [],
            "nextAssigneeList": [
            ],
            "nodeConfigCode": $testNestNodeConfigCode06,
            "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
            "sendNotice": "0",
            "variables": {}
        }
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: case54-查询电子签署列表-查询电子签署列表-流程发起失败，无此流程
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateInitiationName06
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list, [] ]





