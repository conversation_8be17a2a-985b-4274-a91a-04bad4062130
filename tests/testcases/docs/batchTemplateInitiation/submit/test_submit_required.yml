- config:
    name: "批量发起，发起人填写为必填时，发起校验"
    variables:
      - batchTemplateTaskName1: "自动化测试批量发起${random_str(91)}"
      - newTemplateUuid1: ${getTemplateId(1,0,pdf,0)}
      - newVersion1: 1

#创建文档模版，1个内容域，为必填，没有默认值
#创建业务模板，关联文档模板，内容域为发起方填写
#批量发起，选择此业务模板，发起方填写必填项是否填写校验
- test:
    name: "case3-获取文档模板详情"
    api: api/docs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName1: content.data.templateName
      - fileKey1: content.data.fileKey
      - presetName1: "自动化-批量发起业务模板-发起方填写项必填项"
      - presetName2: "自动化-审批待办${getDateTime()}"
      - orgCode1: ${ENV(sign01.main.orgCode)}
      - orgNo1: ${ENV(sign01.main.orgNo)}
      - orgName1: ${ENV(sign01.main.orgName)}
      - userCode1: ${ENV(sign01.userCode)}
      - userNo1: ${ENV(sign01.accountNo)}
      - userName1: ${ENV(sign01.userName)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "case4-新建一个业务模板"
    api: api/apiFlink/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "case5-查询新增的业务模板,并获取presetId"
    api: api/apiFlink/esignDocs/businessPreset/list.yml
    variables:
      - presetName: $presetName1
    extract:
      - presetId1: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName1]

















