- config:
    name: "批量发起预览模板校验"
    variables:
      - batchTemplateTaskName1: "自动化测试批量发起-预览模板${random_str(4)}"
      - batchTemplateTaskName2: "自动化测试批量发起-预览模板${random_str(4)}"
      - batchTemplateTaskName3: "自动化测试批量发起-预览模板${random_str(4)}"
      - excelFileName1: "批量发起_2个指定签署方_1个发起方填写_1个签署方填写.xlsx"
      - excelFileKey1: ${attachment_upload($excelFileName1)}
      - businessTypeCodeCBBT1: "${getBusinessTypeId2(3)}" #自动化勿动3-内部企业和个人无序指定填写
      - userCode1: ${ENV(sign01.userCode)}
      - userName1: ${ENV(sign01.userName)}
      - userNo1: ${ENV(sign01.accountNo)}

      - orgName1: ${ENV(sign01.main.orgName)}
      - orgCode1: ${ENV(sign01.main.orgCode)}
      - orgNo1: ${ENV(sign01.main.orgNo)}

      - excelFileName2: "批量发起3个流程_2个外部签署方_1个发起方填写_1个签署方填写.xlsx"
      - excelFileKey2: ${attachment_upload($excelFileName2)}
      - businessTypeCodeCBBT2: "${getBusinessTypeId2(5)}" #自动化勿动5-外部企业和个人顺序不指定填写
      - w_userNo1: ${ENV(wsign01_userNo)}
      - w_userName1: ${ENV(sign01.userName)}
      - w_orgNo1: ${ENV(wci_orgNo)}
      - w_orgName1: ${ENV(csqs.orgName)}

      - w_userNo2: ${ENV(wsignwb01.accountNo)}
      - w_userName2: ${ENV(wsignwb01.userName)}
      - w_orgName2: ${ENV(wsignwb01.main.orgName)}
      - w_orgNo2: ${ENV(wsignwb01.main.orgNo)}

      - w_userNo3: ${ENV(externalCustomAccountN)}
      - w_userName3: ${ENV(externalUserName)}
      - w_orgName3: ${ENV(externalOrganizationName)}
      - w_orgNo3: ${ENV(externalOrganizationOrgNo)}

      - excelFileName3: "批量发起30个流程_带审批_2个指定内部签署方_无填写.xlsx"
      - excelFileKey3: ${attachment_upload($excelFileName3)}
      - businessTypeCodeCBBT3: "${getBusinessTypeId2(6)}" #自动化勿动6-带审批内部企业和个人指定或签不填写
      - mainHost: ${ENV(esign.projectHost)}

#批量发起一条流程，文档中有2个内容域+2个签署区，1个发起方填写，预览模板，文件仅展示1个内容域，1条流程，1份文件，2个签署方
- test:
    name: "list-业务模板列表-获取presetId、businessTypeId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
        businessTypeId: $businessTypeCodeCBBT1
    extract:
        - presetId1: content.data.list.0.presetId
        - businessTypeId1: content.data.list.0.businessTypeId
    validate:
        - eq: [content.message, "成功"]
        - eq: [content.status, 200]
        - ge: [content.data.total, 1]
        - eq: [content.data.list.0.status, 1]

- test:
    name: "获取业务模板详情"
    api: api/esignDocs//businessPreset/detail.yml
    variables:
      - getPresetId: $presetId1
    extract:
      - signersList1: content.data.signerNodeList
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs//batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId1
    extract:
      - departmentCodeCommon1: content.data.list.0.departmentCode
      - departmentNameCommon1: content.data.list.0.departmentName
      - organizationCodeCommon1: content.data.list.0.organizationCode
      - organizationNameCommon1: content.data.list.0.organizationName
      - initiatorUserNameCommon1: content.data.initiatorUserName
    validate:
        -   eq: ["content.message","成功"]
        -   ne: [content.data,""]
        -   eq: [content.success,True]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName1,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon1,
        "initiatorOrganizeName": $organizationNameCommon1,
        "initiatorUserName": $initiatorUserNameCommon1,
        "initiatorDepartmentName": $departmentNameCommon1,
        "initiatorDepartmentCode": $departmentCodeCommon1,
        "businessPresetUuid": $presetId1,
        "saveSigners": { },
        "signersList": $signersList1,
        "type": 1
      }
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs//batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "预览发起文件-查询流程列表-校验文件名称"
    variables:
      batchTemplateInitiationUuid_previewLaunchFileFlowList: $batchTemplateInitiationUuid1
      excelFileKey_previewLaunchFileFlowList: $excelFileKey1
      page_previewLaunchFileFlowList: 1
      size_previewLaunchFileFlowList: 15
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFileFlowList.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #流程校验
      - eq: [ "content.data.total", 1]  #流程数量
      - contains: [ "content.data.pageList.0.flowName", $batchTemplateTaskName1] #流程名
      - contains: [ "content.data.pageList.0.flowName", "1"]

- test:
    name: "根据流程信息预览批量发起模板文件"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid1
      excelFileKey_previewLaunchFile: $excelFileKey1
      lineNumber_previewLaunchFile: 1
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $userName1]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $orgName1]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $userName1 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #内容域校验
      - eq: [ "content.data.templateList.0.contentDomainCount", 2 ] #全部填写的内容域
      - len_eq: [ "content.data.templateList.0.contentList", 1 ] #pdf展示发起方填写的内容域
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "TC4-提交批量发起任务"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName1,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey":  $excelFileKey1,
        "initiatorOrganizeCode": $organizationCodeCommon1,
        "initiatorOrganizeName": $organizationNameCommon1,
        "initiatorUserName": $initiatorUserNameCommon1,
        "initiatorDepartmentName": $departmentNameCommon1,
        "initiatorDepartmentCode": $departmentCodeCommon1,
        "businessPresetUuid": $presetId1,
        "saveSigners": { },
        "signersList": $signersList1,
        "type": 1
      }
    api: api/esignDocs//batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

#- test:
#    name: TC-后台任务信息
#    api: api/esignSigns//portal/getBackendTaskList.yml
#    variables:
#      - authorizationBackendTaskList: ${getPortalToken()}
#      - currPageBackendTaskList: 1
#      - pageSizeBackendTaskList: 1
#      - userCodeBackendTaskList: ￥userCode1 #当前登录人
#    validate:
#      - eq: [ content.success, true ]
#      - eq: [ content.status, 200 ]
#      - contains: [ content.data.list., "成功" ]
#      - contains: [ json.data.list.0.name, "批量转交" ]

#批量发起3条流程，文档中有2个内容域+2个签署区，预览模板，3条流程，每份流程1份文件，每份文件仅展示1个内容域，2个签署方

- test:
    name: "list-业务模板列表-获取presetId、businessTypeId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
        businessTypeId: $businessTypeCodeCBBT2
    extract:
        - presetId2: content.data.list.0.presetId
        - businessTypeId2: content.data.list.0.businessTypeId
    validate:
        - eq: [content.message, "成功"]
        - eq: [content.status, 200]
        - ge: [content.data.total, 1]
        - eq: [content.data.list.0.status, 1]

- test:
    name: "获取业务模板详情"
    api: api/esignDocs//businessPreset/detail.yml
    variables:
      - getPresetId: $presetId2
    extract:
      - signersList2: content.data.signerNodeList
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs//batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId2
    extract:
      - departmentCodeCommon2: content.data.list.0.departmentCode
      - departmentNameCommon2: content.data.list.0.departmentName
      - organizationCodeCommon2: content.data.list.0.organizationCode
      - organizationNameCommon2: content.data.list.0.organizationName
      - initiatorUserNameCommon2: content.data.initiatorUserName
    validate:
        - eq: ["content.message","成功"]
        - ne: [content.data,""]
        - eq: [content.success,True]


- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName2,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "initiatorOrganizeCode": $organizationCodeCommon2,
        "initiatorOrganizeName": $organizationNameCommon2,
        "initiatorUserName": $initiatorUserNameCommon2,
        "initiatorDepartmentName": $departmentNameCommon2,
        "initiatorDepartmentCode": $departmentCodeCommon2,
        "businessPresetUuid": $presetId2,
        "saveSigners": { },
        "signersList": $signersList2,
        "type": 1
      }
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid2
      excelFileKey:  $excelFileKey2
    api: api/esignDocs//batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "预览发起文件-查询流程列表-校验文件名称"
    variables:
      batchTemplateInitiationUuid_previewLaunchFileFlowList: $batchTemplateInitiationUuid2
      excelFileKey_previewLaunchFileFlowList: $excelFileKey2
      page_previewLaunchFileFlowList: 1
      size_previewLaunchFileFlowList: 15
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFileFlowList.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #流程校验
      - eq: [ "content.data.total", 3]  #流程数量
      - contains: [ "content.data.pageList.0.flowName", "$batchTemplateTaskName2"] #流程名
      - contains: [ "content.data.pageList.0.flowName", "1"]
      - contains: [ "content.data.pageList.1.flowName", "$batchTemplateTaskName2"]
      - contains: [ "content.data.pageList.1.flowName", "2"]
      - contains: [ "content.data.pageList.2.flowName", "$batchTemplateTaskName2"]
      - contains: [ "content.data.pageList.2.flowName", "3"]

- test:
    name: "根据流程信息预览批量发起模板文件-流程1"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid2
      excelFileKey_previewLaunchFile: $excelFileKey2
      lineNumber_previewLaunchFile: 1
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $w_userName1]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $w_orgName1]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $w_userName2 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #内容域校验
      - eq: [ "content.data.templateList.0.contentDomainCount", 2 ] #全部填写的内容域
      - len_eq: [ "content.data.templateList.0.contentList", 1 ] #pdf展示发起方填写的内容域
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "根据流程信息预览批量发起模板文件-流程2"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid2
      excelFileKey_previewLaunchFile: $excelFileKey2
      lineNumber_previewLaunchFile: 2
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $w_userName2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $w_orgName2]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $w_userName3 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #内容域校验
      - eq: [ "content.data.templateList.0.contentDomainCount", 2 ] #仅展示全部填写的内容域
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "根据流程信息预览批量发起模板文件-流程3"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid2
      excelFileKey_previewLaunchFile: $excelFileKey2
      lineNumber_previewLaunchFile: 3
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $w_userName3]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $w_orgName3]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $w_userName1 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #内容域校验
      - eq: [ "content.data.templateList.0.contentDomainCount", 2 ] #仅展示全部填写的内容域
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "TC4-提交批量发起任务"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName2,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "excelFileKey":  $excelFileKey2,
        "initiatorOrganizeCode": $organizationCodeCommon2,
        "initiatorOrganizeName": $organizationNameCommon2,
        "initiatorUserName": $initiatorUserNameCommon2,
        "initiatorDepartmentName": $departmentNameCommon2,
        "initiatorDepartmentCode": $departmentCodeCommon2,
        "businessPresetUuid": $presetId2,
        "saveSigners": { },
        "signersList": $signersList2,
        "type": 1
      }
    api: api/esignDocs//batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


#批量发起30条流程，文档中有无内容域，2个签署区，预览模板，30条流程，每份流程1份文件，每份文件仅展示签署区，2个签署方
- test:
    name: "list-业务模板列表-获取presetId、businessTypeId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
        businessTypeId: $businessTypeCodeCBBT3
    extract:
        - presetId3: content.data.list.0.presetId
        - businessTypeId3: content.data.list.0.businessTypeId
    validate:
        - eq: [content.message, "成功"]
        - eq: [content.status, 200]
        - ge: [content.data.total, 1]
        - eq: [content.data.list.0.status, 1]

- test:
    name: "获取业务模板详情"
    api: api/esignDocs//businessPreset/detail.yml
    variables:
      - getPresetId: $presetId3
    extract:
      - signersList3: content.data.signerNodeList
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs//batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId3
    extract:
      - departmentCodeCommon3: content.data.list.0.departmentCode
      - departmentNameCommon3: content.data.list.0.departmentName
      - organizationCodeCommon3: content.data.list.0.organizationCode
      - organizationNameCommon3: content.data.list.0.organizationName
      - initiatorUserNameCommon3: content.data.initiatorUserName
    validate:
        - eq: ["content.message","成功"]
        - ne: [content.data,""]
        - eq: [content.success,True]


- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid3: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName3,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid3,
        "initiatorOrganizeCode": $organizationCodeCommon3,
        "initiatorOrganizeName": $organizationNameCommon3,
        "initiatorUserName": $initiatorUserNameCommon3,
        "initiatorDepartmentName": $departmentNameCommon3,
        "initiatorDepartmentCode": $departmentCodeCommon3,
        "businessPresetUuid": $presetId3,
        "saveSigners": { },
        "signersList": $signersList3,
        "type": 1
      }
    api: api/esignDocs//batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid3
      excelFileKey:  $excelFileKey3
    api: api/esignDocs//batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "预览发起文件-查询流程列表-校验文件名称"
    variables:
      batchTemplateInitiationUuid_previewLaunchFileFlowList: $batchTemplateInitiationUuid3
      excelFileKey_previewLaunchFileFlowList: $excelFileKey3
      page_previewLaunchFileFlowList: 1
      size_previewLaunchFileFlowList: 50
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFileFlowList.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #流程校验
      - eq: [ "content.data.total", 30]  #流程数量
      - contains: [ "content.data.pageList.0.flowName", "$batchTemplateTaskName3"] #流程名
      - contains: [ "content.data.pageList.0.flowName", "1"]
      - contains: [ "content.data.pageList.9.flowName", "$batchTemplateTaskName3"]
      - contains: [ "content.data.pageList.9.flowName", "10"]
      - contains: [ "content.data.pageList.29.flowName", "$batchTemplateTaskName3"]
      - contains: [ "content.data.pageList.29.flowName", "30"]

- test:
    name: "根据流程信息预览批量发起模板文件-流程1"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid3
      excelFileKey_previewLaunchFile: $excelFileKey3
      lineNumber_previewLaunchFile: 1
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $userName1]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $orgName1]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $userName1 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "根据流程信息预览批量发起模板文件-流程10"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid3
      excelFileKey_previewLaunchFile: $excelFileKey3
      lineNumber_previewLaunchFile: 10
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $userName1]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $orgName1]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $userName1 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "根据流程信息预览批量发起模板文件-流程30"
    variables:
      batchTemplateInitiationUuid_previewLaunchFile: $batchTemplateInitiationUuid3
      excelFileKey_previewLaunchFile: $excelFileKey3
      lineNumber_previewLaunchFile: 30
    api: api/esignDocs/batchTemplateInitiation/previewLaunchFile.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      #签署方列表校验
      - len_eq: [ "content.data.signerNodeList.0.signerList", 2]
      - eq: [ "content.data.signerNodeList.0.signerList.0.signerType", 2] #企业
      - eq: [ "content.data.signerNodeList.0.signerList.0.userName", $userName1]
      - eq: [ "content.data.signerNodeList.0.signerList.0.organizeName", $orgName1]
      - eq: [ "content.data.signerNodeList.0.signerList.1.signerType", 1 ] #个人
      - eq: [ "content.data.signerNodeList.0.signerList.1.userName", $userName1 ]
      - eq: [ "content.data.signerNodeList.0.signerList.1.organizeName", null ]
      #文件校验
      - len_eq: [ "content.data.templateList", 1 ] #文件数量校验
      #签署区校验
      - len_eq: [ "content.data.templateList.0.signatoryList", 2 ] #2个签署区

- test:
    name: "TC4-提交批量发起任务"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName3,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid3,
        "excelFileKey":  $excelFileKey3,
        "initiatorOrganizeCode": $organizationCodeCommon3,
        "initiatorOrganizeName": $organizationNameCommon3,
        "initiatorUserName": $initiatorUserNameCommon3,
        "initiatorDepartmentName": $departmentNameCommon3,
        "initiatorDepartmentCode": $departmentCodeCommon3,
        "businessPresetUuid": $presetId3,
        "saveSigners": { },
        "signersList": $signersList3,
        "type": 1,
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [ ],
          "nextAssigneeList": [ ],
          "nodeConfigCode": "Activity_01nvj49",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid3",
          "sendNotice": "0",
          "variables": { }
        }

      }
    api: api/esignDocs//batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

