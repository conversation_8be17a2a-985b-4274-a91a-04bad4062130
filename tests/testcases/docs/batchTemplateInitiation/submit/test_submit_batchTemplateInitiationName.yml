- config:
    name: "batchTemplateInitiationName校验"
    variables:
      - batchTemplateTaskName1: "自动化测试批量发起${random_str(91)}"
      - batchTemplateTaskName2: "自动化测试批量发起${random_str(92)}"
      - batchTemplateTaskName3: "自动化测试批量发起\/:*?<>|"
      - batchTemplateTaskName4: ""
      - excelFileName1: "单方签署_发起成功.xlsx"
      - excelFileKey1: ${attachment_upload($excelFileName1)}
      - presetIdCommon:  ${getPreset(1,1)}
      - presetNameCommon: "自动化测试发起"
      - signerId: ${get_randomNo_32()}

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - organizationCodeCommon: content.data.list.0.organizationCode
      - organizationNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
        -   eq: ["content.message","成功"]
        -   ne: [content.data,""]
        -   eq: [content.success,True]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-提交批量任务-任务名称超过长度限制"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName1,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey":  $excelFileKey1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-提交批量任务-提交批量任务-长度为支持最长字符串:任务名称输入100长度字符串"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName2,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey":  $excelFileKey1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.status",1600017 ]
      - eq: [ "content.message","流程主题名称最大长度100位" ]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-提交批量任务-提交批量任务-提交批量任务-任务名称包含特殊字符"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName3,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-提交批量任务-提交批量任务-提交批量任务-任务名称为纯空（自动生成的任务名称为：使用模板发起流程）"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName4,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey":  $excelFileKey1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]