- config:
    name: 业务模版中添加其他填写方校验
    variables:
      - name: "V6.0.12.0-beta.5-支持业务添加其他填写方"
      - attachments0: []
      - CCInfosCBBT: []
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - userCode0: ${ENV(sign01.userCode)}
      - userNo0: ${ENV(sign01.accountNo)}
      - userName0: ${ENV(sign01.userName)}

      - csqsNo: ${ENV(csqs.accountNo)}
      - csqsCode: ${ENV(csqs.userCode)}
      - csqsOrgCode: ${ENV(csqs.orgCode)}
      - csqsOrgName: ${ENV(csqs.orgName}

      - outUserCode0: ${ENV(wsignwb01.userCode)}
      - outUserNo0: ${ENV(wsignwb01.accountNo)}
      - outUserName0: ${ENV(wsignwb01.userName)}
      - outOrgCode0: ${ENV(worg01.orgCode)}
      - outOrgNo0: ${ENV(wsignwb01.main.orgNo)}
      - outOrgName0: ${ENV(wsignwb01.main.orgName)}
      - newTemplateUuid1: ${getTemplateId(5,2,pdf,0)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - presetName0: "自动化-其他填写方$randomCount"

- test:
    name: "case1-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "case2-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "case3-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "case4-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "case5-业务模板配置-第一步：填写基本信息（关联pdf模板）"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "case6-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "case7-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "case8-业务模板配置-第三步：签署方设置（设置签署方）,并启动-指定内部个人+外部个人签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: ""
          userName: "$userName0"
          organizeName: ""
          departmentName: "$orgName0"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 1
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
          signatoryList:
            - signatoryId: $signatoryIdA
              templateId: $newTemplateUuid1
              signatoryName: $signatoryNameA
              templateName: $commonTemplateName
              sealType: 1
        - autoSign: 0
          assignSigner: 1
          organizeCode: ""
          userName: "$outUserName0"
          organizeName: ""
          departmentName: "$outOrgName0"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 2
          signerType: 1
          userCode: "$outUserCode0"
          sealTypeName: ""
          departmentCode: "$outOrgCode0"
          signatoryList:
            - signatoryId: $signatoryIdB
              templateId: $newTemplateUuid1
              signatoryName: $signatoryNameB
              templateName: $commonTemplateName
              sealType: 1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "case9-业务模版-新增其他填写方-管理其他填写方-name名称相同"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "填写方1"
            userTerritory: 1
            key: ${get_snowflake()}
          - name: "填写方1"
            userTerritory: 1
            key: ${get_snowflake()}
          - name: "填写方1"
            userTerritory: 2
            key: ${get_snowflake()}
    validate:
      - contains: [content.message, "其他填写方格式校验不通过"]
      - eq: [content.status, 1640003]
      - eq: [content.success, false]
      - eq: [content.data, null]


- test:
    name: "case10-管理其他填写方-name名称长度>20"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "${generate_random_str(21)}"
            userTerritory: 1
            key: ${get_snowflake()}
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case11-管理其他填写方-name名称为空，"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: ""
            userTerritory: 1
            key: ${get_snowflake()}
          - name: ""
            userTerritory: 1
            key: ${get_snowflake()}
          - name: ""
            userTerritory: 2
            key: ${get_snowflake()}
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case12-管理其他填写方-name名称不传，"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: null
            userTerritory: 1
            key: ${get_snowflake()}
          - name: null
            userTerritory: 1
            key: ${get_snowflake()}
          - name: null
            userTerritory: 2
            key: ${get_snowflake()}
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case13-管理其他填写方-name名称传空格，"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "   "
            userTerritory: 1
            key: ${get_snowflake()}
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case14-管理其他填写方-name名称前后有空格空格，自动去前后空格"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "  内部填写方  "
            userTerritory: 1
            key: ${get_snowflake()}
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.fillingList.0.name, "内部填写方" ]

- test:
    name: "case15-管理其他填写方-userTerritory不传1和2"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "内部填写方"
            userTerritory: 3
            key: ${get_snowflake()}
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case16-管理其他填写方-key为空"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "内部填写方"
            userTerritory: 3
            key: ""
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case17-管理其他填写方-key不传"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "内部填写方"
            userTerritory: 3
            key: null
    validate:
      - contains: [ content.message, "其他填写方格式校验不通过" ]
      - eq: [ content.status, 1640003 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "case18-管理其他填写方-正常用例-添加两个内部填写方+一个外部填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "内部填写方1"
            userTerritory: 1
            key: ${get_snowflake()}
          - name: "外部填写方2"
            userTerritory: 2
            key: ${get_snowflake()}
          - name: "内部填写方3"
            userTerritory: 1
            key: ${get_snowflake()}
    extract:
      signerId0: content.data.fillingList.0.signerId
      signerName0: content.data.fillingList.0.name
      signerId1: content.data.fillingList.1.signerId
      signerName1: content.data.fillingList.1.name
      signerId2: content.data.fillingList.2.signerId
      signerName2: content.data.fillingList.2.name
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.presetId, $testPresetId]

- test:
    name: "case19-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
    api: api/esignDocs/businessPreset/templateDetail.yml
    variables:
      - presetId: $testPresetId
      - templateId: $newTemplateUuid1
      - version: $newVersion1
    extract:
      templateContentId0: content.data.contentList.0.templateContentId
      templateContentName0: content.data.contentList.0.contentName
      templateContentId1: content.data.contentList.1.templateContentId
      templateContentName1: content.data.contentList.1.contentName
      templateContentId2: content.data.contentList.2.templateContentId
      templateContentName2: content.data.contentList.2.contentName
      templateContentId3: content.data.contentList.3.templateContentId
      templateContentName3: content.data.contentList.3.contentName
      templateContentId4: content.data.contentList.4.templateContentId
      templateContentName4: content.data.contentList.4.contentName
      signerIdA: content.data.contentList.0.contentSourceTypeList.1.signerList.0.signerId #内部签署方
      signerNameA: content.data.contentList.0.contentSourceTypeList.1.signerList.0.name #内部签署方
      signerIdB: content.data.contentList.0.contentSourceTypeList.1.signerList.1.signerId #外部签署方
      signerNameB: content.data.contentList.0.contentSourceTypeList.1.signerList.1.name #外部签署方
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.templateId, $newTemplateUuid1]

- test:
    name: "case20-业务模板配置的第4步：设置发起方、签署方、其他填写方填写信息"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
      - templateList:
          - templateId: $newTemplateUuid1
            version: $newVersion1
            templateName: $commonTemplateName
            contentList:
              - contentId: $templateContentId0
                contentName: $templateContentName0
                contentSource:  3 #其他填写方填写
                signerId: $signerId0
                signerName: $signerName0
              - contentId: $templateContentId1
                contentName: $templateContentName1
                contentSource: 3 #其他填写方填写
                signerId: $signerId1
                signerName: $signerName1
#              - contentId: $templateContentId2
#                contentName: $templateContentName2
#                contentSource: 1 #发起方填写
#                signerId:
#                signerName:
              - contentId: $templateContentId3
                contentName: $templateContentName3
                contentSource: 2 #签署方填写
                signerId: $signerIdA
                signerName: $signerNameA
              - contentId: $templateContentId4
                contentName: $templateContentName4
                contentSource: 2 #签署方填写
                signerId: $signerIdB
                signerName: $signerNameB
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]

- test:
      name: case21-openapi查询电子签署业务模板详情-V6.0.12.0-beta.5新增出参其他类型的填写
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
#      extract: #返回是无序的，所以不建议用这里的数据，还是用业务模板第四步的接口获取signerId信息
#        - signerId0: content.data.signerInfos.0.signerId
#        - signerId1: content.data.signerInfos.1.signerId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]
          - len_eq: [ content.data.fillingUserInfos, 5] #填写内容域数量
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "0"]
          - eq: [ content.data.fillingUserInfos.1.fillingUserType, "2"]
          - eq: [ content.data.fillingUserInfos.2.fillingUserType, "2"]
          - eq: [ content.data.fillingUserInfos.3.fillingUserType, "1"]
          - eq: [ content.data.fillingUserInfos.4.fillingUserType, "1"]


- test:
    name: "case22-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

#- test:
#    name: "setup-获取批量发起选择页业务模板配置详情"
#    api: api/esignDocs/businessPreset/choseDetail.yml
#    variables:
#      - presetId: $testPresetId
#    extract:
##      - signerId0: content.data.signerNodeList.0.signerList.0.signerId
##      - signatoryList0: content.data.signerNodeList.0.signerList.0.signatoryList
#    #      - signerId1: content.data.signerNodeList.1.signerList.0.signerId
#    #      - signatoryList1: content.data.signerNodeList.1.signerList.0.signatoryList
#    #      - userAccount1: content.data.signerNodeList.1.signerList.0.userAccount
#    #      - userCode1: content.data.signerNodeList.1.signerList.0.userCode
#    #      - userName1: content.data.signerNodeList.1.signerList.0.userName
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "case23-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "case24-weiapi-发起电子签署，其他填写方没有指定用户"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "",
            "userTerritory": 1
          },
          {
            "departmentCode": "",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: case25-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid01: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList01: content.data.businessPresetDetail.signerNodeList
      - fillList01: content.data.businessPresetDetail.fillingList
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "case26-提交发起任务"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      batchTemplateInitiationName: $batchTemplateTaskNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $orgCode0
      initiatorOrganizeName: $orgName0
      initiatorUserName: $userName0
      initiatorDepartmentName: $orgName0
      initiatorDepartmentCode: $orgCode0
      businessPresetUuid: $testPresetId
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid01
      fileFormat: 1
      appendList: []
      signersList: $signerNodeList01
      fillList: $fillList01
    validate:
      - contains: [ "content.message","参数不能为空!" ]
      - eq: [ "content.status", 1600013 ]
      - eq: [ "content.success", False ]

- test:
    name: "case27-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew02: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "case28-weiapi-发起电子签署，内部其他填写方指定外部用户"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "$outOrgCode0",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 1
          },
          {
            "departmentCode": "$outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - contains: [ "content.message","填写项用户不匹配" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1605116 ]

- test:
    name: "case29-weiapi-发起电子签署，外部其他填写方指定内部用户"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "$orgCode0",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$userCode0",
            "userTerritory": 1
          },
          {
            "departmentCode": "$orgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$userCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - contains: [ "content.message","填写项用户不匹配" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1605116 ]

- test:
    name: "case30-weiapi-发起电子签署，内部其他填写方指定不存在的用户"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "outOrgCode0123",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "outUserCode01111",
            "userTerritory": 1
          },
          {
            "departmentCode": "$outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","用户已不存在" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1111003 ]

- test:
    name: "case31-weiapi-发起电子签署，内部其他填写方指定不存在的组织"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "orgCode0123",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$userCode0",
            "userTerritory": 1
          },
          {
            "departmentCode": "$outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","选择的用户已被删除，请重新选择" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1602036 ]

- test:
    name: "case31-weiapi-发起电子签署，外部其他填写方指定不存在的用户"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "$orgCode0",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$userCode0",
            "userTerritory": 1
          },
          {
            "departmentCode": "111outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "1123outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","用户已不存在" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1111003 ]

- test:
    name: "case33-weiapi-发起电子签署，外部其他填写方指定不存在的组织"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew02,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "$orgCode0",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$userCode0",
            "userTerritory": 1
          },
          {
            "departmentCode": "111outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","选择的用户已被删除，请重新选择" ]
      - eq: [ "content.success", False ]
      - eq: [ "content.status",1602036 ]

- test:
    name: "case34-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew03: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "case35-正常用例"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew03,
        "initiatorOrganizeCode": $orgCode0,
        "initiatorOrganizeName": $orgName0,
        "initiatorUserName": $userName0,
        "initiatorDepartmentName": $orgName0,
        "initiatorDepartmentCode": $orgCode0,
        "businessPresetUuid": $testPresetId,
        "signersList": [
          {
              "signMode": 1,
              "signerList": [
                {
                  "signerType": 1,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $userCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $userName0
                },
                {
                  "signerType": 1,
                  "signerTerritory": 2,
                  "draggable": true,
                  "organizeCode": "",
                  "userCode": $outUserCode0,
                  "sealTypeCode": null,
                  "autoSign": 0,
                  "assignSigner": 1,
                  "userName": $outUserName0
                }
              ]
          }
        ],
        "fillList": [
          {
            "departmentCode": "$csqsOrgCode",
            "signerId": "$signerId0",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$csqsCode",
            "userTerritory": 1
          },
          {
            "departmentCode": "$outOrgCode0",
            "signerId": "$signerId1",
            "signerSnapshotId": "",
            "templateInitiationSignersUuid": "",
            "userCode": "$outUserCode0",
            "userTerritory": 2
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": []
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: case36-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew03"
    extract:
      - businessPresetSnapshotUuid02: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList02: content.data.businessPresetDetail.signerNodeList
      - fillList02: content.data.businessPresetDetail.fillingList
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "case37-提交发起任务"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      batchTemplateInitiationName: $batchTemplateTaskNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew03
      initiatorOrganizeCode: $orgCode0
      initiatorOrganizeName: $orgName0
      initiatorUserName: $userName0
      initiatorDepartmentName: $orgName0
      initiatorDepartmentCode: $orgCode0
      businessPresetUuid: $testPresetId
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid02
      fileFormat: 1
      appendList: []
      signersList: $signerNodeList02
      fillList: $fillList02
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data", ""]










