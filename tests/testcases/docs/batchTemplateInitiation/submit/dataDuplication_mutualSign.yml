- config:
    name: "发起批量任务-excel数据重复性校验"
    variables:
      signerUUid: ${random_str()}
      batchTemplateInitiationName: "自动化测试批量起任务-${get_randomNo()}"
      fileName1: "签署人重复_填写项重复.xlsx"
      fileKey1: ${attachment_upload($fileName1)}
      sqlLanguage1: "select a.handle_status, a.handle_result from doc_template_initiation_data_related a left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id where a.handle_status = 3 and b.uuid = '"
      sqlLanguage2: "select a.handle_status, a.handle_result from doc_template_initiation_data_related a left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id where b.uuid = '"
      endSql: "' order by a.gmt_create desc limit 1"
      handleResult1: "数据已存在"
      handleStatus1: 2
      presetIdCommon: ${getPreset(1,0)}
      initiatorUserCode: ${ENV(ceswdzxzdhyhwgd1.account)}

- test:
    name: "获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "暂存批量任务1"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "signerType": 2,
            "signerTerritory": 2,
            "draggable": true,
            "autoSign": 0,
            "assignSigner": 0,
            "signerSnapshotId": $signerUUid
          }]
        }],
        "type": 1,
        "sort": 0,
        "chargingType": 1
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
    extract:
      - templateFileKey: content.data.docList.0.fileKey
      - signPosName1: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs.0.signPosName
      - signPosName2: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs.1.signPosName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "保存盖章配置详情-绑定签署区-签署区不可移动"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $templateFileKey,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": "1",
              "posX": 188,
              "posY": 703,
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": $signPosName1,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            },
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": "1",
              "posX": 188,
              "posY": 703,
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": $signPosName2,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ],
          "signerId": $signerUUid,
          "userType": 2,
          "signerType": 2,
          "legalSignFlag": 1
        }
        ]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew
      - excelFileKey: $fileKey1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "提交批量任务1-签署人重复填写项重复"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": $fileKey1,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "signerSnapshotId": $signerUUid,
            "signerType": 2,
            "signerTerritory": 2,
            "draggable": true,
            "autoSign": 0,
            "assignSigner": 0
          }
          ]
        }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - { "expect": "$handleResult1","check": "${assertBatchTemplateInitiationUuid2($sqlLanguage1$batchTemplateInitiationUuidNew$endSql)}" }
