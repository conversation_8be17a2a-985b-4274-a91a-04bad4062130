- config:
    name: "批量发起-提交-内部个人"
    variables:
      batchTemplateInitiationName: "自动化测试批量起任务-${get_randomNo()}"
      fileName1: "单方签署_签署人重复_填写项重复.xlsx"
      fileKey1: ${attachment_upload($fileName1)}
      fileName2: "单方签署_签署人重复_填写项重复_包含非所属列项.xlsx"
      fileKey2: ${attachment_upload($fileName2)}
      fileName3: "单方签署_签署人重复_填写项不重复.xlsx"
      fileKey3: ${attachment_upload($fileName3)}
      fileName5: "单方签署_签署人不重复_填写项重复.xlsx"
      fileKey5: ${attachment_upload($fileName5)}
      sqlLanguage1: "select a.handle_status, a.handle_result from doc_template_initiation_data_related a left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id where a.handle_status = 3 and b.uuid = '"
      sqlLanguage2: "select a.handle_status, a.handle_result from doc_template_initiation_data_related a left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id where b.uuid = '"
      endSql: "' order by a.batch_data_id desc limit 1"
      handleResult1: "数据已存在"
      handleStatus1: 3
      handleStatus2: 2
      presetIdCommon: ${getPreset(1,1)}
      signerId: ${get_randomNo_32()}

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - departmentCodeCommon: content.data.list.0.departmentCode
      - departmentNameCommon: content.data.list.0.departmentName
      - organizationCodeCommon: content.data.list.0.organizationCode
      - organizationNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]


- test:
    name: "TC1-setup-获取批量发起任务uuid1"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {}
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-setup-获取批量发起任务uuid1"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {}
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-暂存批量任务1"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      - excelFileKey: $fileKey1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-单方签署_签署人重复_填写项重复"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey": $fileKey1,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5-setup-获取批量发起任务uuid2"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {}
    extract:
      - batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC6-暂存批量任务2"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-setup-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid2
      - excelFileKey: $fileKey2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC8-单方签署_签署人重复_填写项重复_包含非所属列项"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "excelFileKey": $fileKey2,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC9-setup-获取批量发起任务uuid3"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {}
    extract:
      - batchTemplateInitiationUuid3: content.data.batchTemplateInitiationUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC10-暂存批量任务3"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid3,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC11-setup-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid3
      - excelFileKey: $fileKey3
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC12-单方签署_签署人重复_填写项不重复"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid3,
        "excelFileKey": $fileKey3,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC17-setup-获取批量发起任务uuid5"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {}
    extract:
      - batchTemplateInitiationUuid5: content.data.batchTemplateInitiationUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC18-暂存批量任务5"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid5,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC19-setup-校验上传的excel1"
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid5
      - excelFileKey: $fileKey5
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC20-单方签署_签署人不重复_填写项重复"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateInitiationName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid5,
        "excelFileKey": $fileKey5,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $departmentNameCommon,
        "initiatorDepartmentCode": $departmentCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - { "expect": "$handleResult1","check": "${assertBatchTemplateInitiationUuid2($sqlLanguage1$batchTemplateInitiationUuid1$endSql)}" }
      - { "expect": "$handleResult1","check": "${assertBatchTemplateInitiationUuid2($sqlLanguage1$batchTemplateInitiationUuid2$endSql)}" }
      - { "expect": "$handleStatus1","check": "${assertBatchTemplateInitiationUuid1($sqlLanguage2$batchTemplateInitiationUuid3$endSql)}" }
      - { "expect": "$handleStatus2","check": "${assertBatchTemplateInitiationUuid1($sqlLanguage2$batchTemplateInitiationUuid5$endSql)}" }