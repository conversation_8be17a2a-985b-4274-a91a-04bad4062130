- config:
    name: 已签文件导入(open-api)
    variables:
      - signType: 1
      - subject: "主题校验"
      - signFlowCreateTime: "2024-07-12 12:00:00"
      - signFlowEndTime: "2024-07-12 13:00:00"
      - departmentName: ${ENV(sign01.main.orgNo)}
      - customAccountNo: ${ENV(sign01.userCode)}
      - departmentName1: ${ENV(sign01.main.orgNo)}
      - customAccountNo1: ${ENV(sign01.userCode)}
      - userType: 1
      - cryptoFileKey: ${ENV(cryptoFileKey)}
      - fileKey: ${ENV(fileKey)}
      - ofdFileKey: ${ENV(ofdFileKey)}
      - fileKeyJpg:  ${ENV(fileKeyJpg)}
      - pngPageFileKey: ${ENV(pngPageFileKey)}
      - jpegFileKey: ${ENV(jpegFileKey)}
      - docPageFileKey: ${ENV(docPageFileKey)}

- test:
    name: P1TL-签署完成文档signFiles传{}或者null
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: []
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: P2TL-签署完成文档signedFileKey签署文档不存在
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: $cryptoFileKey
        }
      ]
    validate:
      - eq: [ content.code, 1610004 ]


- test:
    name: P3TL-签署完成文档signedFileKey签署文档不存在
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: null
        }
      ]
    validate:
      - eq: [ content.code, 1624079 ]
      - eq: [ content.message, "签署文档fileKey不可为空" ]


- test:
    name: P4TL-签署完成文档signedFileKey为空字符串或不传
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: ""
        }
      ]
    validate:
      - eq: [ content.code, 1624079 ]
      - eq: [ content.message, "签署文档fileKey不可为空" ]
      - eq: [ content.data, null ]



- test:
    name: P5TL-签署完成文档signedFileKey重复
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: $fileKey
        },{
          signedFileKey: $fileKey
        }
      ]
    validate:
      - eq: [ content.code, 1702117 ]
      - eq: [ content.message, "流程签署文件不可重复！" ]
      - eq: [ content.data, null ]



- test:
    name: P6TL-签署文档为ofd签署文档+pdf签署文档
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: $ofdFileKey
        },{
          signedFileKey: $fileKey
        }
      ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: P7TL-签署完成文档为其他jpg/png/jpeg格式文件签署文件
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: $fileKeyJpg
        },{
          signedFileKey: $pngPageFileKey
        },{
          signedFileKey: $jpegFileKey
        }
      ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: P8TL-签署完成文档为其他不支持txt等格式签署文件
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: "aaa.txt"
        }
      ]
    validate:
      - eq: [ content.code, 1610004 ]
      - eq: [ content.message, "文件获取失败:aaa.txt" ]
      - eq: [ content.data, null ]

- test:
    name: P8TL-签署完成文档无后缀
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [
        {
          signedFileKey: "aaa"
        }
      ]
    validate:
      - eq: [ content.code, 1610004 ]
      - eq: [ content.message, "文件获取失败:aaa" ]
      - eq: [ content.data, null ]