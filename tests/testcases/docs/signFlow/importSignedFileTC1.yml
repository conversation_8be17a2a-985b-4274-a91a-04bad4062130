- config:
    name: 已签文件导入(open-api)场景用例
    variables:
      signedFileKey: ${ENV(fileKey)}
      ofdFileKey: ${ENV(ofdFileKey)}
      fileKeyJpg: ${ENV(fileKeyJpg)}
      pngPageFileKey: ${ENV(pngPageFileKey)}
      jpegFileKey: ${ENV(jpegFileKey)}
      customAccountNo: ${ENV(sign01.accountNo)}
      departmentName: ${ENV(sign01.main.orgName)}
      customDepartmentNo: ${ENV(sign01.main.orgNo)}
      wsignwb01userName: ${ENV(wsignwb01.userName)}
      wsignwb01userCode: ${ENV(wsignwb01.userCode)}
      wsignwb01orgNo: ${ENV(wsignwb01.main.orgNo)}
      ceswdzxzdhyhwgd1orgName: ${ENV(ceswdzxzdhyhwgd1.orgName)}
      ceswdzxzdhyhwgd1orgNo: ${ENV(ceswdzxzdhyhwgd1.orgNo)}
      customAccountNo30: ${generate_random_str(30)}
      customDepartmentNo54: ${generate_random_str(54)}

- test:
    name: "P1TL-subject不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ]
      }
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "流程主题支持的字符长度为1-200" ]
      - eq: [ content.data, null ]

- test:
    name: "P2TL-签署创建时间signFlowCreateTime不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-签署创建时间signFlowCreateTime不传"
      }
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "签署流程创建时间不能为空" ]
      - eq: [ content.data, null ]

- test:
    name: "P3TL-签署结束时间signFlowEndTime不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-签署结束时间signFlowEndTime不传"
      }
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "签署流程创建时间不能为空" ]
      - eq: [ content.data, null ]

- test:
    name: "P4TL-signType不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-签署结束时间signFlowEndTime不传"
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P5TL-发起人userName、customAccountNo 不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-发起人userName、customAccountNo 不传"
      }
    validate:
      - eq: [ content.code, 1624099 ]
      - eq: [ content.message, "流程发起人名称和编码不能都为空" ]
      - eq: [ content.data, null ]

- test:
    name: "P6TL-发起人departmentName、customDepartmentNo不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-发起人departmentName、customDepartmentNo不传"
      }
    validate:
      - eq: [ content.code, 1624070 ]
      - eq: [ content.message, "流程发起人组织名称和编码不能都为空" ]
      - eq: [ content.data, null ]

- test:
    name: "P7TL-签署人userName、customAccountNo不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-签署人userName、customAccountNo不传"
      }
    validate:
      - eq: [ content.code, 1624072 ]
      - eq: [ content.message, "流程签署人名称和编码不能都为空" ]
      - eq: [ content.data, null ]

- test:
    name: "P8TL-签署人departmentName、customDepartmentNo 不传"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-签署人departmentName、customDepartmentNo 不传"
      }
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P9TL-发起人customAccountNo字符长度30边界值校验"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "",
          "customAccountNo": $customAccountNo30,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-发起人customAccountNo字符长度30边界值校验"
      }
    validate:
      - eq: [ content.code, 1623008 ]
      - startswith: [ "content.message","customAccountNo:" ]
      - eq: [ content.data, null ]

- test:
    name: "P10TL-发起人customDepartmentNo 字符长度54边界值校验"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": "",
          "customDepartmentNo": $customDepartmentNo54
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-发起人customAccountNo字符长度30边界值校验"
      }
    validate:
      - eq: [ content.code, 1623026 ]
      - startswith: [ "content.message","customDepartmentNo:" ]
      - eq: [ content.data, null ]


- test:
    name: "P1TL-创建电子签署-有2份签署文件-2个签署方未创建（内部企业+外部个人签署方）"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          },
          {
            "signedFileKey": $fileKeyJpg
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "AA残觞哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "AA残觞哈哈哈企业",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "AA残觞外部哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TL-创建电子签署-有2份签署文件-2个签署方未创建（内部企业+外部个人签署方）"
      }
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P2TL-创建电子签署-有2份签署文件-2个签署方已创建（内部个人+外部企业签署方）"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $ofdFileKey
          },
          {
            "signedFileKey": $pngPageFileKey
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $wsignwb01orgNo
          }
        ],
        "subject": "P2TL-创建电子签署-有2份签署文件-2个签署方已创建（内部个人+外部企业签署方）"
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: "P3TL-创建电子签署-无签署文件-2个签署方未创建（内部个人+外部个人签署方）"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "内部哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "外部哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P3TL-创建电子签署-无签署文件-2个签署方未创建（内部个人+外部个人签署方）"
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: "P4TL-创建物理用印流程-有2份签署文件-2个签署方已创建（外部个人+外部企业）"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $ofdFileKey
          },
          {
            "signedFileKey": $signedFileKey
          }
        ],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 2,
        "signerInfos": [
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $wsignwb01orgNo
          }
        ],
        "subject": "P4TL-创建物理用印流程-有2份签署文件-2个签署方已创建（外部个人+外部企业）"
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P5TL-创建物理用印流程-无签署文件-2个签署方已创建（内部企业+外部企业）"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [],
        "signFlowCreateTime": " 2024-07-22  09:00:00 ",
        "signFlowEndTime": " 2024-07-22  09:03:00 ",
        "signType": 2,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01userCode,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $wsignwb01orgNo
          }
        ],
        "subject": "P5TL-创建物理用印流程-无签署文件-2个签署方已创建（内部企业+外部企业）"
      }
    extract:
      - signFlowId05: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P5TL-我管理的-根据signFlowId查询已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "processId": $signFlowId05
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": ""
      - "gmtSignFinishStart": ""
      - "gmtSignFinishEnd": ""
      - "page": 1
      - "size": 10
    extract:
      - signedFileProcessUuid02: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId05 ]
      - eq: [ "content.data.signFileProcessVOList.0.signerUserNameList.0", "测试签署一（sign01）" ]
      - eq: [ "content.data.signFileProcessVOList.0.signerUserNameList.1", "测试签署外部一（wsignwb01）" ]


- test:
    name: "P5TL-根据signedFileProcessUuid比对流程列表"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid02
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ "content.data.historyFileDetailVO.processId", $signFlowId05 ]
      - eq: [ "content.data.historyFileDetailVO.signMode", 2 ]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userCode", $customAccountNo]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.1.userName", $wsignwb01userName ]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeCode", "58bec88e506142b0bf05342c68518827" ]

- test:
    name: "P1TL-我管理的-根据signFlowId查询已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "processId": $signFlowId
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": ""
      - "gmtSignFinishStart": ""
      - "gmtSignFinishEnd": ""
      - "page": 1
      - "size": 10
    extract:
      - signedFileProcessUuid: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId ]
      - eq: [ "content.data.signFileProcessVOList.0.signerUserNameList.0", "AA残觞哈哈哈" ]
      - eq: [ "content.data.signFileProcessVOList.0.signerUserNameList.1", "AA残觞外部哈哈哈" ]
      - eq: [ "content.data.signFileProcessVOList.0.signOrgNameList.0", "AA残觞哈哈哈企业" ]


- test:
    name: "P1TL-我管理的-查看根据signedFileProcessUuid比对流程列表"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid
    extract:
      - flowName1: content.data.historyFileDetailVO.flowName
      - gmtSignFinish: content.data.historyFileDetailVO.gmtSignFinish
      - gmtSignInitiate: content.data.historyFileDetailVO.gmtSignInitiate
      - signedFileDetailUuid: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - initiatorOrganizeName1: content.data.historyFileDetailVO.initiatorOrganizeName
      - initiatorUserCode: content.data.historyFileDetailVO.initiatorUserCode
      - initiatorUserName1: content.data.historyFileDetailVO.initiatorUserName
      - id0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.id
      - uuid0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - signedFileProcessId0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signedFileProcessId
      - userCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userCode
      - userName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userName
      - departmentName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentName
      - departmentCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentCode
      - organizeCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeCode
      - organizeName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeName
      - userType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userType
      - signerType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signerType
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ "content.data.historyFileDetailVO.processId", $signFlowId ]
      - eq: [ "content.data.historyFileDetailVO.signMode", 1 ]
      - eq: [ "content.data.historyFileDetailVO.historyFileVOList.0.fileName", "key30page.pdf" ]
      - eq: [ "content.data.historyFileDetailVO.historyFileVOList.1.fileName", "jpg格式图.jpg" ]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userName", "AA残觞哈哈哈" ]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.1.userName", "AA残觞外部哈哈哈" ]
      - eq: [ "content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeName", "AA残觞哈哈哈企业" ]

- test:
    name: "P1TL-删除导入的签署文件"
    api: api/esignDocs/signedFileProcess/deleteHistoryImportFile.yml
    variables:
      - signedFileDetailUuid: $signedFileDetailUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "P1TL-编辑已签署文件"
    api: api/esignDocs/signedFileProcess/manage_updateHistoryImportFile.yml
    variables:
      - flowName: $flowName1
      - gmtSignFinish: $gmtSignFinish
      - gmtSignInitiate: $gmtSignInitiate
      - initiatorOrganizeName: $initiatorOrganizeName1
      - initiatorUserCode: $initiatorUserCode
      - initiatorUserName: $initiatorUserName1
      - id0: $id0
      - uuid0: $uuid0
      - signedFileProcessId0: $signedFileProcessId0
      - userCode0: $userCode0
      - userName0: $userName0
      - departmentName0: $departmentName0
      - departmentCode0: $departmentCode0
      - organizeCode0: $organizeCode0
      - organizeName0: $organizeName0
      - userType0: $userType0
      - signerType0: $signerType0
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "P1TL-我管理的-删除已签署文件"
    api: api/esignDocs/signedFileProcess/manage_deleteSignedFileProcess.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]



- test:
    name: "导入历史已签署文件"
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          }
        ],
        "signFlowCreateTime": "${getDateTime(-2,1)}",
        "signFlowEndTime": "${getDateTime(-1,1)}",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $customAccountNo,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $customDepartmentNo
          }
        ],
        "subject": "导入历史已签署文件"
      }
    extract:
      - signFlowId_001: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]


- test:
    name: TC-已签署文件列表-我的文件-通过signFlowId查询
    api:  api/esignDocs/signedFileProcess/owner_list.yml
    variables:
      processId: $signFlowId_001
      viewType: 1
    extract:
      - signedFileProcessUuid_001: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 1 ]
      - eq: [ json.data.signFileProcessVOList.0.processId, $processId ]

 ##BUG应该可以查出数据，现在查不出
#- test:
#    name: TC-已签署文件列表-我的文件-通过signFlowId查询
#    api:  api/esignDocs/signedFileProcess/owner_list.yml
#    variables:
#      processId: $signFlowId_001
#      viewType: null
#    validate:
#      - eq: [ content.success, true ]
#      - eq: [ content.status, 200 ]
#      - eq: [ json.data.total, 0 ]
#      - eq: [ json.data.signFileProcessVOList.0.processId, $processId ]

 ##BUG应该可以查出数据，现在查不出
#- test:
#    name: TC-已签署文件列表-我的文件-通过signFlowId查询
#    api:  api/esignDocs/signedFileProcess/owner_list.yml
#    variables:
#      processId: $signFlowId_001
#      viewType: 2
#    validate:
#      - eq: [ content.success, true ]
#      - eq: [ content.status, 200 ]
#      - eq: [ json.data.total, 0 ]
#      - eq: [ json.data.signFileProcessVOList.0.processId, $processId ]

- test:
    name: "删除导入的签署文件"
    api: api/esignDocs/signedFileProcess/deleteHistoryImportFile.yml
    variables:
      - signedFileDetailUuid: $signedFileProcessUuid_001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]