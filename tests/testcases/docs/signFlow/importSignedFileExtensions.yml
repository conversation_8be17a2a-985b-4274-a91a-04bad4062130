- config:
    name: 已签文件导入(open-api)-增加扩展字段场景
    variables:
      signedFileKey: ${ENV(fileKey)}
      ofdFileKey: ${ENV(ofdFileKey)}
      fileKeyJpg: ${ENV(fileKeyJpg)}
      pngPageFileKey: ${ENV(pngPageFileKey)}
      jpegFileKey: ${ENV(jpegFileKey)}
      initiatorUserCode: ${ENV(sign01.userCode)}
      initiatorUserName: ${ENV(sign01.userName)}
      customAccountNo: ${ENV(sign01.accountNo)}
      departmentName: ${ENV(sign01.main.orgName)}
      customDepartmentNo: ${ENV(sign01.main.orgNo)}
      orgCode: ${ENV(sign01.main.orgCode)}
      fieldKey001: "base_extend_field_2"  #参数key，如果有人手工新建扩展字段可能被占用，需要在环境删除这两个参数key 对应查询baseExtendField2
      fieldKey002: "base_extend_field_3" #对应查询baseExtendField3
      todayTime: ${getDateTime(0,1)}
      tomorrowTime: ${getDateTime(1,1)}
      yesterdayTime: ${getDateTime(-1,1)}



- test:
    name: setup-新增扩展字段2
    variables:
      categoryKey: "SIGN_FLOW"
      fieldName: "扩展字段B"
      fieldKey: "$fieldKey002"
      fieldParam : "fieldParamB"
    api: api/esignManage/integrate/manage/extensionsFieldSave.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data, True ]
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: setup-新增扩展字段1
    variables:
      categoryKey: "SIGN_FLOW"
      fieldName: "扩展字段A"
      fieldKey: "$fieldKey001"
      fieldParam : "fieldParamA"
    api: api/esignManage/integrate/manage/extensionsFieldSave.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data, True ]
#    condition: ${should_run}  < 10


- test:
    name: 查询扩展字段
    variables:
      pageIndex: 1
      pageSize: 10
    api: api/esignManage/integrate/manage/extensionsFieldPage.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "操作成功" ]
      - ne: [ content.data.total, "" ]
      - eq: [ content.data.data.0.categoryKey, "SIGN_FLOW" ]
      - ne: [ content.data.data.0.modifyTime, null ]
      - ne: [ content.data.data.0.modifier, null ]
      - eq: [ content.data.data.1.categoryKey, "SIGN_FLOW" ]
      - ne: [ content.data.data.1.modifyTime, null ]
      - ne: [ content.data.data.1.modifier, null ]
    extract:
      - dataId001: content.data.data.0.dataId
      - dataId002: content.data.data.1.dataId


- test:
    name: TC-导入电子签署-扩展字段字符都不填写成功
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          }
        ],
        "signFlowCreateTime": $yesterdayTime,
        "signFlowEndTime":$todayTime,
        "signType": 1,
        "flowExtensions": {
            "fieldParamA": "",
            "fieldParamB": ""
            },
        "signerInfos": [
          {
            "userType": 1,
            "userName": "AA哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "AA哈企业",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "AA哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TC-历史电子签署文件1"
      }
    extract:
      - signFlowId01: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.signFlowId, $signFlowId01 ]

- test:
    name: "TC-我管理的-根据扩展字段查询已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": "1"
      - "processId": $signFlowId01
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": ""
      - "gmtSignFinishStart": ""
      - "gmtSignFinishEnd": ""
      - "flowExtensions": {
          "fieldParamA": "参数A-传参数B"}
      - "page": 1
      - "size": 10
    extract:
      - signedFileProcessUuid01: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId01 ]
      - eq: [ "content.data.signFileProcessVOList.0.signerUserNameList.0", "AA哈哈哈" ]


- test:
    name: "TC-根据signedFileProcessUuid比对流程列表"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid01
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ "content.data.historyFileDetailVO.processId", $signFlowId01 ]
      - eq: [ "content.data.historyFileDetailVO.signMode", 1 ]
      - eq: [ "content.data.extensionList.0.extensionFieldName", "扩展字段A"]  #无排序，导致每次验证顺序会变
      - eq: [ "content.data.extensionList.0.extensionFieldParam", "fieldParamA"]
      - eq: [ "content.data.extensionList.0.extensionFieldKey", "baseExtendField2"]
      - eq: [ "content.data.extensionList.0.extensionFieldValue", ""]
      - eq: [ "content.data.extensionList.1.extensionFieldName", "扩展字段B"]
      - eq: [ "content.data.extensionList.1.extensionFieldParam", "fieldParamB"]
      - eq: [ "content.data.extensionList.1.extensionFieldKey", "baseExtendField3"]
      - eq: [ "content.data.extensionList.1.extensionFieldValue", ""]


- test:
    name: TC-导入电子签署-有扩展字段字符串大于191,报错
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          }
        ],
        "signFlowCreateTime": $yesterdayTime,
        "signFlowEndTime": $todayTime,
        "signType": 1,
        "flowExtensions": {
          "fieldParamA": "大于191扩展字段扩在字段扩展字段扩在字段扩展字段扩在字段nziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1kuozhanziduan1",
          "fieldParamB": ""
      },
        "signerInfos": [
          {
            "userType": 1,
            "userName": "AA哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "AA哈企业",
            "customOrgNo": ""
          },
          {
            "userType": 2,
            "userName": "AA哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TC-历史电子签署文件"
      }
    validate:
      - eq: [ content.code, 1650029 ]
      - eq: [ content.message, "扩展字段【fieldParamA】长度上限不能超过191" ]
      - eq: [ content.data, null ]

- test:
    name: TC-导入电子签署-有扩展字段字符传值
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "initiatorInfo": {
          "userName": "测试签署一",
          "customAccountNo": $customAccountNo,
          "departmentName": $departmentName,
          "customDepartmentNo": $customDepartmentNo
        },
        "signFiles": [
          {
            "signedFileKey": $signedFileKey
          }
        ],
        "signFlowCreateTime": $yesterdayTime,
        "signFlowEndTime":$todayTime,
        "signType": 1,
        "flowExtensions": {
            "fieldParamA": "参数A-传参数B",
            "fieldParamB": ""
            },
        "signerInfos": [
          {
            "userType": 1,
            "userName": "AA哈哈哈",
            "customAccountNo": "",
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "AA哈企业",
            "customOrgNo": ""
          }
        ],
        "subject": "P1TC-历史电子签署文件2"
      }
    extract:
      - signFlowId02: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.signFlowId, $signFlowId02 ]
        
- test:
    name: "TC-已签署文件列表-我管理的-根据flowExtensions查询已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "processId": $signFlowId02
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": ""
      - "gmtSignFinishStart": ""
      - "gmtSignFinishEnd": ""
      - "flowExtensions": {
          "baseExtendField2": "参数A-传参数B"}
      - "page": 1
      - "size": 10
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.processId", $signFlowId02 ]
    extract:
      - signedFileProcessUuid02: content.data.signFileProcessVOList.0.signedFileProcessUuid

- test:
    name: "TC-根据signedFileProcessUuid比对流程列表"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid02
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ "content.data.historyFileDetailVO.processId", $signFlowId02 ]
      - eq: [ "content.data.extensionList.0.extensionFieldName", "扩展字段B"]  #无排序，导致每次验证顺序会变
      - eq: [ "content.data.extensionList.0.extensionFieldParam", "fieldParamB"]
      - eq: [ "content.data.extensionList.0.extensionFieldKey", "baseExtendField3"]
      - eq: [ "content.data.extensionList.0.extensionFieldValue", ""]
      - eq: [ "content.data.extensionList.1.extensionFieldName", "扩展字段A"]
      - eq: [ "content.data.extensionList.1.extensionFieldParam", "fieldParamA"]
      - eq: [ "content.data.extensionList.1.extensionFieldKey", "baseExtendField2"]
      - eq: [ "content.data.extensionList.1.extensionFieldValue", "参数A-传参数B"]
    extract:
      - flowName1: content.data.historyFileDetailVO.flowName
      - gmtSignFinish: content.data.historyFileDetailVO.gmtSignFinish
      - gmtSignInitiate: content.data.historyFileDetailVO.gmtSignInitiate
      - signedFileDetailUuid: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - initiatorOrganizeName1: content.data.historyFileDetailVO.initiatorOrganizeName
      - initiatorUserCode: content.data.historyFileDetailVO.initiatorUserCode
      - initiatorUserName1: content.data.historyFileDetailVO.initiatorUserName
      - id0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.id
      - uuid0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - signedFileProcessId0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signedFileProcessId
      - userCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userCode
      - userName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userName
      - departmentName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentName
      - departmentCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentCode
      - organizeCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeCode
      - organizeName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeName
      - userType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userType
      - signerType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signerType




- test:
    name: "P1TL-编辑导入的已签文件"
    api: api/esignDocs/signedFileProcess/manage_updateHistoryImportFile.yml
    variables:
        flowName: $flowName1
        flowExtensions: {"baseExtendField3":"BBB"}
        initiatorOrganizeName: $departmentName
        initiatorUserCode: $initiatorUserCode
        initiatorUserName: $initiatorUserName
        signedFileProcessUuid: $signedFileProcessUuid02
        signInfoRequestList: [
          {
            id0: $id0,
            uuid0: $uuid0,
            signedFileProcessId0: $signedFileProcessId0,
            userCode0: $initiatorUserCode,
            userName0: $initiatorUserName,
            departmentName0: $departmentName,
            organizeCode0: $orgCode,
            organizeName0: $departmentName,
            userType0: $userType0,
            signerType0: $signerType0
         }
        ]
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]




- test:
    name: TC-已签署文件列表-我的文件-通过flowExtensions查询
    api:  api/esignDocs/signedFileProcess/owner_list.yml
    variables:
      processId: $signFlowId02
      flowExtensions: {"baseExtendField3":"BBB"}
      viewType: 1
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 1 ]
      - eq: [ json.data.signFileProcessVOList.0.processId, $signFlowId02 ]


- test:
    name: "删除导入的签署文件"
    api: api/esignDocs/signedFileProcess/deleteHistoryImportFile.yml
    variables:
      - signedFileDetailUuid: $signedFileProcessUuid01
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "删除导入的签署文件"
    api: api/esignDocs/signedFileProcess/deleteHistoryImportFile.yml
    variables:
      - signedFileDetailUuid: $signedFileProcessUuid02
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: 删除扩展字段1
    variables:
      dataId: $dataId001
    api: api/esignManage/integrate/manage/extensionsFieldDelete.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data, true ]

- test:
    name: 删除扩展字段2
    variables:
      dataId: $dataId002
    api: api/esignManage/integrate/manage/extensionsFieldDelete.yml
    validate:
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data, true ]