- config:
    name: "更新签署流程接口测试"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - future_time_1: ${get_future_time(1)}  # 当前时间+1天
      - future_time_2: ${get_future_time(2)}  # 当前时间+2天
      - past_time: ${get_past_time(1)}        # 当前时间-1天
      - current_time_plus_1s: ${get_current_time_plus_seconds(1)}  # 当前时间+1秒
      - current_time_minus_1s: ${get_current_time_minus_seconds(1)} # 当前时间-1秒
      - invalid_signflow_id: "invalid_flow_id_123"
      - invalid_business_no: "invalid_business_no_123"
      - test_reason: "自动化测试更新签署流程"
      - second: 3
      - fileKey0: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - customAccountNoSigner0: ${ENV(sign01.accountNo)}
      - customOrgNoSigner0: ${ENV(sign01.main.orgNo)}

# Setup: 创建测试用的签署流程
- test:
    name: setup-创建测试签署流程1
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化测试更新签署流程1"
      remark: "自动化测试更新签署流程1"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
#      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      test_signflow_id_1: content.data.signFlowId
      test_business_no_1: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: setup-创建测试签署流程2
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: null
      subject: "自动化测试更新签署流程2"
      remark: "自动化测试更新签署流程2"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
#      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      test_signflow_id_2: content.data.signFlowId
      test_business_no_2: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例1: 正常更新contractExpireTime
- test:
    name: case1-正常更新contractExpireTime
    variables:
      signFlowId: "$test_signflow_id_1"
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data,null]
- test:
    name: TC10-6-验证特殊字符详情接口返回contractExpireTime为空
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $test_signflow_id_1
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.signFlowId, 1]
      # 验证contractExpireTime为空
      - eq: [content.data.contractExpireTime, $future_time_1]

# 测试用例2: 使用businessNo更新
- test:
    name: case2-使用businessNo更新contractExpireTime
    variables:
      signFlowId: ""
      businessNo: $test_business_no_2
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data,null]
- test:
    name: TC10-6-验证特殊字符详情接口返回contractExpireTime为空
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: "$test_business_no_2"
      signFlowId: ""
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.signFlowId, 1]
      # 验证contractExpireTime为空
      - eq: [content.data.contractExpireTime, $future_time_1]


# 测试用例5: contractExpireTime格式验证 - 正确格式
- test:
    name: case5-contractExpireTime传null不处理
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
- test:
    name: TC10-6-验证特殊字符详情接口传空不处理
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: "$test_signflow_id_1"
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.signFlowId, 1]
      # 验证contractExpireTime为空
      - eq: [content.data.contractExpireTime, $future_time_1]

- test:
    name: case5-contractExpireTime传空报错
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: ""
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]
      - eq: [content.data,null]
# 测试用例3: 同时更新signFlowExpireTime和contractExpireTime
- test:
    name: case3-同时更新signFlowExpireTime和contractExpireTime
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: $future_time_2
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data,null]
# 测试用例4: 参数验证 - signFlowId和businessNo都为空
- test:
    name: case4-参数验证signFlowId和businessNo都为空
    variables:
      signFlowId: ""
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1607007]
      - contains: [content.message, "流程id和业务id不能同时为空"]
      - eq: [content.data,null]
# 测试用例5: contractExpireTime格式验证 - 正确格式
- test:
    name: case5-contractExpireTime正确格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "2024-12-31 23:59:59"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1709175]
      - eq: [content.message, "合同到期日期必须晚于当前时间"]
      - eq: [content.data,null]


# 测试用例6: contractExpireTime格式验证 - 错误格式1
- test:
    name: case6-contractExpireTime错误格式验证1
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "2024/12/31 23:59:59"  # 错误格式
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]
      - eq: [content.data,null]
# 测试用例7: contractExpireTime格式验证 - 错误格式2
- test:
    name: case7-contractExpireTime错误格式验证2
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "2024-12-31"  # 只有日期
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]
      - eq: [content.data,null]

# 测试用例8: contractExpireTime格式验证 - 错误格式3
- test:
    name: case8-contractExpireTime错误格式验证3
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "2024-12-31T23:59:59"  # ISO格式
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]
      - eq: [content.data,null]

# 测试用例9: contractExpireTime格式验证 - 错误格式4
- test:
    name: case9-contractExpireTime错误格式验证4
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "invalid_date_format"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]
      - eq: [content.data,null]
# 测试用例10: signFlowExpireTime格式验证 - 正确格式
- test:
    name: case10-signFlowExpireTime正确格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: $future_time_1
      contractExpireTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data,null]
# 测试用例11: signFlowExpireTime格式验证 - 错误格式
- test:
    name: case11-signFlowExpireTime错误格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: "2024/12/31 23:59:59"  # 错误格式
      contractExpireTime: ""
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "签署截止日期格式不正确"]
      - eq: [content.data,null]

- test:
    name: case11-signFlowExpireTime错误格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: "2023-12-31 23:59:59"  # 过去的时间
      contractExpireTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1702072]
      - eq: [content.message, "签署截止日期不可早于当前日期！"]
      - eq: [content.data,null]
# 测试用例12: 边界值验证 - 超长signFlowId
- test:
    name: case12-超长signFlowId验证
    variables:
      signFlowId: "${random_str(100)}"  # 超长ID
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1607008]
      - contains: [content.message, "流程id长度不能大于36"]
# 测试用例13: 边界值验证 - 超长businessNo
- test:
    name: case13-超长businessNo验证
    variables:
      signFlowId: ""
      businessNo: "${random_str(300)}"  # 超长业务号
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1607009]
      - contains: [content.message, "业务id长度不能大于191"]

# 测试用例14: 空值验证 - contractExpireTime为空
- test:
    name: case14-contractExpireTime为空验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例15: 日期边界值验证 - 过去时间
- test:
    name: case15-contractExpireTime过去时间验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $past_time
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1709175]
      - contains: [content.message, "合同到期日期必须晚于当前时间"]

# 测试用例16: 日期边界值验证 - 当前时间+1秒
- test:
    name: case16-contractExpireTime当前时间加1秒
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $current_time_plus_1s
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1709175]
      - contains: [content.message, "合同到期日期必须晚于当前时间"]

# 测试用例17: 日期边界值验证 - 当前时间-1秒
- test:
    name: case17-contractExpireTime当前时间减1秒
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $current_time_minus_1s
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1709175]
      - contains: [content.message, "合同到期日期必须晚于当前时间"]

# 测试用例18: 数据格式异常处理 - null值
- test:
    name: case18-contractExpireTime为null
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例19: 数据格式异常处理 - 超长字符串
- test:
    name: case19-contractExpireTime超长字符串
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: "${random_str(1000)}"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1600017]
      - eq: [content.message, "合同到期时间格式不正确"]


# 测试用例21: 异常场景 - 不存在的signFlowId
- test:
    name: case21-不存在的signFlowId
    variables:
      signFlowId: $invalid_signflow_id
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1607001]
      - contains: [content.message, "不存在"]

# 测试用例22: 异常场景 - 超长signFlowId
- test:
    name: case22-超长signFlowId异常处理
    variables:
      signFlowId: "${random_str(500)}"
      businessNo: ""
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1607008]
      - eq: [content.message, "流程id长度不能大于36"]

# 测试用例23: 异常场景 - 不存在的businessNo
- test:
    name: case23-不存在的businessNo
    variables:
      signFlowId: ""
      businessNo: $invalid_business_no
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      - eq: [content.code, 1607001]
      - eq: [content.message, "文档流程不存在"]

# 测试用例24: 异常场景 - 超长businessNo
- test:
    name: case24-超长businessNo异常处理
    variables:
      signFlowId: ""
      businessNo: "${random_str(500)}"
      signFlowExpireTime: null
      contractExpireTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 1607009]
      - contains: [content.message, "业务id长度不能大于191"]

