- config:
    name: 作废接口指定签署区
    variables:
      businessTypeCode0: ${ENV(businessTypeCode)}
      presetName0: "通用发起签署业务"
      randomCount: ${getDateTime()}
      presetName2: "自动化-$randomCount"
      initiatorUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
      initiatorUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.userCode)}
      initiatorOrgCodeCommon: ${getUserMainOrg($initiatorUserCodeCommon)}
      initiatorOrgNameCommon: ${get_orgInfo(None,$initiatorOrgCodeCommon)}
      businessNoCommon: "文档自动化测试-编码-${get_randomNo_16()}"
      subjectCommon: "文档自动化测试-主题-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      signerOrgCodeCommon: ${ENV(sign01.main.orgCode)}
      signerOrgNoCommon: ${ENV(sign01.main.orgNo)}
      signerOrgNameCommon: ${ENV(sign01.main.orgName)}
      toSignFileKeyCommon: ${ENV(ofdFileKey)}
      orgSealId: ${ENV(org01.sealId)}
      orgSealIdDynamic: ${ENV(sign01_orgSealIdDynamic)}
      perSealId: ${ENV(sign01.sealId)}


- test:
    name: "setup-查询业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId0: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.list.0.presetName, $presetName0]


- test:
    name: "setup-查看初始状态新建的业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId0
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
      - assignedRevokeSignatureArea: content.data.signBusinessType.assignedRevokeSignatureArea
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId0]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - eq: [content.data.signBusinessType.assignedRevokeSignatureArea, 0]

- test:
    name: setup-一步发起流程-签署完成流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $businessTypeCode0
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - businessNo0: content.data.businessNo
      - signFlowId0: content.data.signFlowId


- test:
    name: TC1-作废接口signConfigs不传，读取业务模板的assignedRevokeSignatureArea
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "reason": "作废",
        "signFlowId": $signFlowId0
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke0: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC2-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke0
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke0 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: setup-一步发起流程-签署完成流程1
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $businessTypeCode0
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - businessNo1: content.data.businessNo
      - signFlowId1: content.data.signFlowId


- test:
    name: TC3-作废接口signConfigs传空，读取业务模板的assignedRevokeSignatureArea
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": "",
        "reason": "作废",
        "signFlowId": $signFlowId1
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke1: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC4-detail-signFlowId-检查作废签署不指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke1
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke1 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]



- test:
    name: setup-一步发起流程-签署完成流程2
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $businessTypeCode0
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(10)}
    extract:
      - businessNo2: content.data.businessNo
      - signFlowId2: content.data.signFlowId


- test:
    name: TC5-作废接口signConfigs传null，读取业务模板的assignedRevokeSignatureArea
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": null,
        "reason": "作废",
        "signFlowId": $signFlowId2
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke2: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC6-detail-signFlowId-检查作废签署不指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke2
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke2 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]

- test:
    name: setup-一步发起流程-签署完成流程3
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $businessTypeCode0
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(10)}
    extract:
      - businessNo3: content.data.businessNo
      - signFlowId3: content.data.signFlowId


- test:
    name: TC5-作废接口signConfigs传false，作废流程不指定签署区
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": false,
        "reason": "作废",
        "signFlowId": $signFlowId3
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke3: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: TC6-detail-signFlowId-检查作废签署不指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke3
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke3 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: setup-一步发起流程-签署完成流程4
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $businessTypeCode0
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - businessNo4: content.data.businessNo
      - signFlowId4: content.data.signFlowId


- test:
    name: TC7-作废接口signConfigs传true，作废流程指定签署区
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": true,
        "reason": "作废",
        "signFlowId": $signFlowId4
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke4: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: TC8-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke4
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke4 ]
      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName2
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName2
    extract:
      - testPresetId2: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName2]


- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - testBusinessTypeId2: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId2]
      - eq: [content.data.presetName, $presetName2]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "setup-业务模板配置-第一步：填写基本信息"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName2
      presetId_addDetail: $testPresetId2
      allowAddFile: 1
      templateList: []
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]




- test:
    name: "setup-业务模板配置-第二步：设置业务模板作废签署区位置开关开启"
    api: api/esignDocs/businessPreset/addBusinessTypeAllowApproverAddFile.yml
    variables:
      businessTypeName: $presetName2
      businessTypeId: $testBusinessTypeId2
      limitTemplateSealEnable: 0
      presetId: $testPresetId2
      assignedRevokeSignatureArea: 1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: "setup-业务模板配置-第三步：签署方设置,发起时设置签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId2
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddFile: 1
      allowAddSigner: 1
      allowAddSealer: 1
      sealerList: ""
      fileFormat: 1
      "signerNodeList": [ {
        "signMode": 1,
        "signerList": [ {
          "assignSigner": "",
          "autoSign": 0,
          "departmentCode": "",
          "organizeCode": "",
          "sealTypeCode": "",
          "signNode": 1,
          "signOrder": "",
          "onlyUkeySign": 0,
          "signerId": "",
          "signerTerritory": "",
          "signerType": "1",
          "userCode": "",
          "signatoryList": [ ]
        } ]
      } ]
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
    teardown_hooks:
      - ${sleep(5)}





- test:
    name: "TC-通过查询业务模板详情，验证作废协议指定签署区开启"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - _handColor1: content.data.signBusinessType.handColor
      - _limitTemplateSealEnable1: content.data.signBusinessType.limitTemplateSealEnable
      - _templateSealColor1: content.data.signBusinessType.templateSealColor
      - _PresetId1: content.data.presetId
      - _processInitiationType1: content.data.signBusinessType.processInitiationType
      - _oneClickStampSwitch1: content.data.signBusinessType.oneClickStampSwitch
      - _oneClickStampRange1: content.data.signBusinessType.oneClickStampRange
      - _businessTypeId: content.data.signBusinessType.businessTypeId
      - _assignedRevokeSignatureArea: content.data.signBusinessType.assignedRevokeSignatureArea
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - eq: [ content.data.signBusinessType.processInitiationType, $_processInitiationType1 ]
      - eq: [ content.data.signBusinessType.oneClickStampSwitch, $_oneClickStampSwitch1 ]
      - eq: [ content.data.signBusinessType.oneClickStampRange, $_oneClickStampRange1 ]
      - eq: [ content.data.signBusinessType.assignedRevokeSignatureArea, $_assignedRevokeSignatureArea ]



- test:
    name: setup-一步发起流程-签署完成流程5
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId2
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - signFlowId5: content.data.signFlowId




- test:
    name: TC9-作废接口signConfigs传空，作废流程指定签署区跟业务模板一致，开启
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": "",
        "reason": "作废",
        "signFlowId": $signFlowId5
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke5: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

#- test:
#    name: TC10-detail-signFlowId-检查作废签署指定签署方位置
#    api: api/esignSigns/process/detail.yml
#    variables:
#      - processId: $businessNoRevoke5
#      - approvalProcessId: ""
#      - requestSource: 2
#      - organizeCode: ""
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, "成功"]
#      - eq: [ content.data.processId, $businessNoRevoke5 ]
#      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
#      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: setup-一步发起流程-签署完成流程6
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId2
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - signFlowId6: content.data.signFlowId




- test:
    name: TC11-作废接口signConfigs传null，作废流程指定签署区跟业务模板一致，开启
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": null,
        "reason": "作废",
        "signFlowId": $signFlowId6
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke6: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

#- test:
#    name: TC12-detail-signFlowId-检查作废签署指定签署方位置
#    api: api/esignSigns/process/detail.yml
#    variables:
#      - processId: $businessNoRevoke6
#      - approvalProcessId: ""
#      - requestSource: 2
#      - organizeCode: ""
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, "成功"]
#      - eq: [ content.data.processId, $businessNoRevoke6]
#      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
#      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: setup-一步发起流程-签署完成流程7
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId2
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - signFlowId7: content.data.signFlowId




- test:
    name: TC13-作废接口signConfigs传true，作废流程指定签署区开启
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": true,
        "reason": "作废",
        "signFlowId": $signFlowId7
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke7: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: TC14-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke7
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke7 ]
      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - ne: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]



- test:
    name: setup-一步发起流程-签署完成流程7
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessTypeCode: $testBusinessTypeId2
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      autoSign: true
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${sleep(15)}
    extract:
      - signFlowId8: content.data.signFlowId




- test:
    name: TC15-作废接口signConfigs传false，作废流程指定签署区关闭
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": false,
        "reason": "作废",
        "signFlowId": $signFlowId8
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke8: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: TC16-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke8
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke8 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: "setup-查询ofd业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: "ofd"
      - fileFormat: 2
    extract:
      - testPresetId3: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.success, true]
      - eq: [content.status, 200]
      - ne: [content.data.total, 0]


- test:
    name: "setup-查看ofd业务模板详情，assignedRevokeSignatureArea为null"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    extract:
      - testBusinessTypeId3: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 2]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId3]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - eq: [content.data.signBusinessType.assignedRevokeSignatureArea, 0]


- test:
    name: "setup-内部企业ofd"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $testBusinessTypeId3,
        "subject": "内部企业ofd签署",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgNoCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommonOfd1: content.data.signFlowId
    teardown_hooks:
      - ${sleep(20)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: TC17-作废接口signConfigs传false，作废ofd流程指定签署区关闭
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": false,
        "reason": "作废",
        "signFlowId": $signFlowIdCommonOfd1
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke9: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: TC18-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke9
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke9 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]


- test:
    name: "setup-内部企业ofd签署2"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $testBusinessTypeId3,
        "subject": "内部企业ofd签署2",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgNoCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommonOfd2: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: TC19-作废接口signConfigs传true，作废ofd流程指定签署区仍是关闭
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "signConfigs": true,
        "reason": "作废",
        "signFlowId": $signFlowIdCommonOfd2
      }
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功"]
    extract:
      - businessNoRevoke10: content.data.signFlowId
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: TC20-detail-signFlowId-检查作废签署指定签署方位置
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $businessNoRevoke10
      - approvalProcessId: ""
      - requestSource: 2
      - organizeCode: ""
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.processId, $businessNoRevoke10 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posX, null ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.posY, null ]