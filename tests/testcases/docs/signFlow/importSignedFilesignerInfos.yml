- config:
    name: 已签文件导入(open-api)
    variables:
      - signType: 1
      - subject: "主题校验"
      - signFlowCreateTime: "2024-07-12 12:00:00"
      - signFlowEndTime: "2024-07-12 13:00:00"
      - customAccountNo1: ${ENV(sign01.userCode)}
      - customDepartmentNo1: ${ENV(sign01.main.orgNo)}
      - signedFileKey: ${ENV(fileKey)}
      - userType: 1
      - code: ${code}
      - message: ${message}
      - name: ${name}

- test:
    name: ${name}
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      initiatorInfo: {"customAccountNo": customAccountNo1,"customDepartmentNo": customDepartmentNo1}
    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message ]
