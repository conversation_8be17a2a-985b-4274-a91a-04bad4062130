- name: "指定印章类型签署完成后进行作废"
- config:
    variables:
      initiatorUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
      initiatorUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.userCode)}
      initiatorOrgCodeCommon: ${getUserMainOrg($initiatorUserCodeCommon)}
      initiatorOrgNameCommon: ${get_orgInfo(None,$initiatorOrgCodeCommon)}
      businessNoCommon: "文档自动化测试-编码-${get_randomNo_16()}"
      subjectCommon: "文档自动化测试-主题-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserCodeCommon2: ${ENV(userCodeNoSeal)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      signerOrgCodeCommon: ${ENV(sign01.main.orgNo)}
      signerOrgNameCommon: ${ENV(sign01.main.orgName)}
      signerUserCodeCommon4: ${ENV(sign04.userCode)}
      signerOrgCodeCommon4: ${ENV(sign04.main.orgNo)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      businessTypeCodeCommon: ${ENV(businessTypeCode)}
      orgSealId: ${ENV(org01.sealId)}
      orgSealIdDynamic: ${ENV(sign01_orgSealIdDynamic)}
      perSealId: ${ENV(sign01.sealId)}
    output:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon
      - signerOrgNameCommon
      - orgSealId

- test:
    name: "cans-P1TL-内部企业指定公章类型-不更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业指定公章类型-不更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon: content.data.signFlowId
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: "cans1-获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowIdCommon
    extract:
      flowIdCommon2: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "cans1-作废流程信息"
    api: api/esignDocs/flow/manage_revokeDetail.yml
    variables:
      json:
        {
          "params": {
            "flowId": $flowIdCommon2
          }
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "成功" ]

- test:
    name: "cans-P1TL-内部企业不指定公章章签署完成后作废-更换经办人,不是企业章用印人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废企业章用印人",
          "signFlowId": $signFlowIdCommon,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon4,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon4,
             "customOrgNo": $signerOrgCodeCommon4
          }
          ]
        }
    validate:
      - eq: [ "content.code", 1702609 ]
      - eq: [ "content.data", null ]
      - contains: [ "content.message", "经办人测试签署四(ORG-SIGN-03)不是企业章用印人" ]

- test:
    name: "cans-P2TL-内部企业指定公章签署完成后作废-不更换经办人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废不更换经办人",
          "signFlowId": $signFlowIdCommon,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ $businessNo1, $businessNoCommon ]


- test:
    name: "cans-P3TL-内部企业指定公章类型-更换经办人且有企业印章授权-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业指定公章类型-更换经办人且有企业印章授权-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon2: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: "cans-P4TL-内部企业指定公章+法人章类型-不更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业指定公章+法人章类型-不更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL,LEGAL-PERSON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon3: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: "cans-P6TL-内部企业指定公章+法人章类型-更换经办人且有印章授权-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业指定公章+法人章类型-更换经办人且有印章授权-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL,LEGAL-PERSON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon4: content.data.signFlowId
#    teardown_hooks:
#      - ${sleep(5)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: "cans-P7TL-内部企业不指定印章类型-不更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业不指定印章类型-不更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon5: content.data.signFlowId
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: "cans-P7TL-内部企业不指定印章签署完成后作废-更换经办人,不是企业章用印人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废",
          "signFlowId": $signFlowIdCommon5,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon4,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon4,
             "customOrgNo": $signerOrgCodeCommon4
          }
          ]
        }
    validate:
      - eq: [ "content.code", 1702609 ]
      - eq: [ "content.data", null ]
      - contains: [ "content.message", "经办人测试签署四(ORG-SIGN-03)不是企业章用印人" ]

- test:
    name: "cans-P9TL-内部企业不指定印章类型-更换经办人且有印章授权-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部企业不指定印章类型-更换经办人且有印章授权-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon6: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: "cans-P10TL-内部个人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P0TL-内部个人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "sealId": $perSealId,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon7: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: "cans-P11TL-内部企业两个经办人A指定公章类型，B指定法人章类型-不更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P11TL-内部企业两个经办人A指定公章类型，B指定法人章类型-不更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon2,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        },
          {
            "userCode": $signerUserCodeCommon,
            "customOrgNo": $signerOrgCodeCommon,
            "userType": "1",
            "signNode": 2,
            "autoSign": 1,
            "signMode": 0,
            "sealTypeCode": "LEGAL-PERSON-SEAL",
            "sealInfos": [
              {
                "fileKey": $toSignFileKeyCommon,
                "signConfigs": [
                  {
                    "pageNo": 1,
                    "posX": 200,
                    "posY": 100,
                    "signType": "COMMON-SIGN",
                    "signatureType": "LEGAL-PERSON-SEAL"
                  }
                ]
              }
            ]
          }
        ]
      }
    extract:
      signFlowIdCommon8: content.data.signFlowId
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.code",200 ]


- test:
    name: "cans11-获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      processId: $signFlowIdCommon8
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "cans11-作废流程信息"
    api: api/esignDocs/flow/owner_revokeDetail.yml
    variables:
      json:
        {
          "params": {
            "flowId": $flowIdCommon
          }
        }
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "成功" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "cans-P12TL-内部企业两个经办人A指定公章类型，B指定法人章类型-更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P12TL-内部企业两个经办人A指定公章类型，B指定法人章类型-更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon2,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealTypeCode": "COMMON-SEAL",
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        },
          {
            "userCode": $signerUserCodeCommon,
            "customOrgNo": $signerOrgCodeCommon,
            "userType": "1",
            "signNode": 2,
            "autoSign": 1,
            "signMode": 0,
            "sealTypeCode": "LEGAL-PERSON-SEAL",
            "sealInfos": [
              {
                "fileKey": $toSignFileKeyCommon,
                "signConfigs": [
                  {
                    "pageNo": 1,
                    "posX": 200,
                    "posY": 100,
                    "signType": "COMMON-SIGN",
                    "signatureType": "LEGAL-PERSON-SEAL"
                  }
                ]
              }
            ]
          }
        ]
      }
    extract:
      signFlowIdCommon9: content.data.signFlowId
#    teardown_hooks:
#      - ${sleep(10)}
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: "cans-P13TL-内部个人+内部企业指定公章+法人章类型-更换经办人-静默签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": "cans-P13TL-内部个人+内部企业指定公章+法人章类型-更换经办人-静默签",
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
          {
            "userCode": $signerUserCodeCommon,
            "userType": "1",
            "tspId": " LOCAL_DEFAULT_TSP",
            "signNode": 1,
            "autoSign": 1,
            "signMode": 0,
            "sealInfos": [
              {
                "fileKey": $toSignFileKeyCommon,
                "signConfigs": [
                  {
                    "pageNo": 1,
                    "posX": 200,
                    "posY": 100,
                    "sealId": $perSealId,
                    "signType": "COMMON-SIGN",
                    "signatureType": "PERSON-SEAL"
                  }
                ]
              }
            ]
          },
          {
            "userCode": $signerUserCodeCommon,
            "customOrgNo": $signerOrgCodeCommon,
            "userType": "1",
            "tspId": " LOCAL_DEFAULT_TSP",
            "signNode": 2,
            "autoSign": 1,
            "signMode": 0,
            "sealTypeCode": "COMMON-SEAL,LEGAL-PERSON-SEAL",
            "sealInfos": [
              {
                "fileKey": $toSignFileKeyCommon,
                "signConfigs": [
                  {
                    "pageNo": 1,
                    "posX": 200,
                    "posY": 100,
                    "signType": "COMMON-SIGN",
                    "signatureType": "LEGAL-PERSON-SEAL"
                  }
                ]
              }
            ]
          }
        ]
      }
    extract:
      signFlowIdCommon10: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: "cans-P3TL-内部企业指定公章签署完成后作废-更换经办人且有企业印章授权"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废更换经办人且有企业印章授权",
          "signFlowId": $signFlowIdCommon2,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon2,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P4TL-内部企业不指定公章+法人章签署完成后作废-更换经办人,不是企业章用印人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废更换经办人,不是企业章用印人",
          "signFlowId": $signFlowIdCommon3,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon4,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon4,
             "customOrgNo": $signerOrgCodeCommon4
          }
          ]
        }
    validate:
      - eq: [ "content.code", 1702609 ]
      - eq: [ "content.data", null ]
      - contains: [ "content.message", "经办人测试签署四(ORG-SIGN-03)不是企业章用印人" ]

- test:
    name: "cans-P5TL-内部企业指定公章+法人章签署完成后作废-不更换经办人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废不更换经办人不更换经办人",
          "signFlowId": $signFlowIdCommon3,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon2,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ $businessNo1, $businessNoCommon ]

- test:
    name: "cans-P6TL-内部企业指定公章+法人章签署完成后作废-更换经办人且有印章授权"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废更换经办人且有印章授权",
          "signFlowId": $signFlowIdCommon4,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon2,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P8TL-内部企业不指定印章签署完成后作废-不更换经办人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废",
          "signFlowId": $signFlowIdCommon5,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ $businessNo1, $businessNoCommon ]

- test:
    name: "cans-P9TL-内部企业不指定印章签署完成后作废-更换经办人且有印章授权"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废",
          "signFlowId": $signFlowIdCommon6,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon2,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P10TL-内部个人签署完成后作废"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废",
          "signFlowId": $signFlowIdCommon7,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon,
             "userType": "1"
          }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P11TL-内部企业两个经办人A指定公章类型，B指定法人章类型签署完成后作废-不更换经办人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废不更换经办人",
          "signFlowId": $signFlowIdCommon8,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon2,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          },
            {
              "customAccountNo": $signerUserCodeCommon,
              "userType": "1",
              "customDepartmentNo": $signerOrgCodeCommon,
              "customOrgNo": $signerOrgCodeCommon
            }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P12TL-内部企业两个经办人A指定公章类型，B指定法人章类型签署完成后作废-更换经办人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废更换经办人",
          "signFlowId": $signFlowIdCommon9,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon,
             "userType": "1",
             "customDepartmentNo": $signerOrgCodeCommon,
             "customOrgNo": $signerOrgCodeCommon
          },
            {
              "customAccountNo": $signerUserCodeCommon2,
              "userType": "1",
              "customDepartmentNo": $signerOrgCodeCommon,
              "customOrgNo": $signerOrgCodeCommon
            }
          ]
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]

- test:
    name: "cans-P13TL-内部个人+内部企业指定公章+法人章类型签署完成后作废-更换经办人并通知抄送人"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "businessNo": $businessNoCommon,
          "reason": "作废更换经办人",
          "signFlowId": $signFlowIdCommon10,
          "signerInfos":[
          {
             "customAccountNo": $signerUserCodeCommon,
             "userType": "1"
          },
            {
              "customAccountNo": $signerUserCodeCommon2,
              "userType": "1",
              "customDepartmentNo": $signerOrgCodeCommon,
              "customOrgNo": $signerOrgCodeCommon
            }
          ],"CCInfos": [
          {
            "customAccountNo": $signerUserCodeCommon,
            "userCode": $signerUserCodeCommon,
            "userType": 1
          }
        ],
        }
    extract:
      - revokedSignFlowId1: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo

    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.businessNo", $businessNoCommon ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC13-查询流程详情"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $revokedSignFlowId1
    validate:
      - eq: [ "status_code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.signFlowId", $revokedSignFlowId1]
#    teardown_hooks:
#      - ${check_callBack(1, $revokedSignFlowId1)}