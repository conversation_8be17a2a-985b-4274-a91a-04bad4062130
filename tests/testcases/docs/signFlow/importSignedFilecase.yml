- config:
    name: 已签文件导入(open-api)
    variables:
      - departmentName: ${ENV(sign01.main.orgNo)}
      - customAccountNo: ${ENV(sign01.userCode)}
      - departmentName1: ${ENV(sign01.main.orgNo)}
      - customAccountNo1: ${ENV(sign01.userCode)}
      - signedFileKey: ${ENV(fileKey)}
      - userType: 1
      - code: ${code}
      - message: ${message}
      - name: ${name}

- test:
    name: ${name}
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
    validate:
      - eq: [ content.code, $code ]
      - eq: [ content.message, $message ]
