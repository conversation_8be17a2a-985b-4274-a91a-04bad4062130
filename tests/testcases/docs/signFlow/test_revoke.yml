- test:
    name: "TC1-reason必填，不传"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "",
        "reason": "",
        "signFlowId": ""
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","作废原因不能为空"]
- test:
    name: "TC2-只填reason，businessNo和signFlowId同时不填"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "",
        "reason": "钻风作废测试",
        "signFlowId": ""
      }
    validate:
      #- eq: [ "content.code",1600017 ]
      - eq: [ "content.message","流程id和业务id不能同时为空"]
- test:
    name: "TC3-businessNo只填1位"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "1",
        "reason": "钻风作废测试",
        "signFlowId": ""
      }
    validate:
      - eq: [ "content.code",1617043 ]
      - eq: [ "content.data",null ]
      - eq: [ "content.message","businessNo查到多条记录"]
- test:
    name: "TC4-只传reason和businessNo，且businessNo不存在"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "121234561234561234563456",
        "reason": "钻风作废测试",
        "signFlowId": ""
      }
    validate:
      - eq: [ "content.code",1607001 ]
      - eq: [ "content.message","文档流程不存在"]
- test:
    name: "TC5-只传reason和signFlowId，且signFlowId不存在"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "",
        "reason": "钻风作废测试",
        "signFlowId": "123456"
      }
    validate:
      - eq: [ "content.code",1607001 ]
      - eq: [ "content.message","文档流程不存在"]
#- test:
#    name: "TC6-同时传reason、businessNo、signFlowId，取交集数据不存在"
#    api: api/esignDocs/signFlow/revoke.yml
#    variables:
#      json: {
#        "businessNo": "123456",
#        "reason": "钻风作废测试",
#        "signFlowId": "123456"
#      }
#    validate:
#      - eq: [ "content.code",1702258 ]
#      - eq: [ "content.message","流程不存在或已删除"]
- test:
    name: "TC7-只传reason和businessNo，且businessNo长度为191"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "123456789012345678901234567890123456789012345678901123456789012345678901234567890123456789012345678901123456789012345678901234567890123456789012345678901123456789012345678901234567890123456789012345678901",
        "reason": "钻风作废测试",
        "signFlowId": ""
      }
    validate:
      - eq: [ "content.code",1607009 ]
      - eq: [ "content.message","业务id长度不能大于191"]
- test:
    name: "TC8-只传reason和signFlowId，且signFlowId长度为37"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "",
        "reason": "钻风作废测试",
        "signFlowId": "1234567890123456789012345678901234567"
      }
    validate:
      - eq: [ "content.code",1607008 ]
      - eq: [ "content.message","流程id长度不能大于36"]
- test:
    name: "TC9-reason长度为101"
    api: api/esignDocs/signFlow/revoke.yml
    variables:
      json: {
        "businessNo": "",
        "reason": "12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901",
        "signFlowId": "123456"
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","作废原因不能超过100字"]
 # -test:
  #  name: "TC10-正常查询，同时传reason、businessNo、signFlowId，取交集数据存在"
