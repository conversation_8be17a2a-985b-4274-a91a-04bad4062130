#场景说明：双方签署-追加采集信息-填写-提交场景-有审批
#审批流程：开始-》发起流程-〉审批-》签署-〉结束
#1、配置业务模板并启用（内部个人指定+外部个人+追加采集信息+流程引擎）；2、发起单份电子签署；3、填写
- config:
    variables:
      autoPresetName: "自动化测试业务模板-${get_randomNo_16()}"
      randomSignerId0: ${get_randomNo_32()}
      randomSignerId1: ${get_randomNo_32()}
      autotestModelName: "文档自动化测试勿动勿复制"
      autotestModelKey: ${getWorkFlowModelKey($autotestModelName)}
      autotestWorkFlowModelName: "【电子签署】-$autotestModelName"
      userAccountKey: "ceswdzxzdhyhwgd1.account"
      userpasswordKey: "ceswdzxzdhyhwgd1.password"
      userNameKey: "ceswdzxzdhyhwgd1.userName"
      userName_outer_Key: "wsignwb01.userName"
      userAccount_outer_Key: "wsignwb01.accountNo"
      userPassword_outer_key: "wsignwb01.password"
      outer_user_mobile_key: "wsignwb01.account.encrypt"
      userAccount: ${ENV($userAccountKey)}
      userAccount_outer: ${ENV($userAccount_outer_Key)}
      userPassword_outer: ${ENV($userPassword_outer_key)}
      signerUserName_inner: ${ENV($userNameKey)}
      signerUserCode_inner: ${get_inner_UserCode(userAccount=$userAccount)}
      signerUserName_outer: ${ENV($userName_outer_Key)}
      signerUserCode_outer: ${get_outer_UserCode(userAccount=$userAccount_outer)}
      pdfFileKey: ${ENV(fileKey)}
      picFileName: "jpg格式图.jpg"
      picFileKey: ${doc_upload(fileName=$picFileName)}
      testFormJson: "{\"list\":[{\"type\":\"input\",\"icon\":\"icon-input\",\"options\":{\"width\":\"\",\"defaultValue\":\"\",\"required\":false,\"requiredMessage\":\"\",\"dataType\":\"\",\"dataTypeCheck\":false,\"dataTypeMessage\":\"\",\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"validatorCheck\":false,\"validator\":\"\",\"placeholder\":\"\",\"customClass\":\"\",\"disabled\":false,\"labelWidth\":100,\"isLabelWidth\":false,\"hidden\":false,\"dataBind\":true,\"showPassword\":false,\"remoteFunc\":\"func_h3pf2rvo\",\"remoteOption\":\"option_h3pf2rvo\",\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"单行文本\",\"key\":\"h3pf2rvo\",\"model\":\"input_h3pf2rvo\",\"rules\":[]},{\"type\":\"fileupload\",\"icon\":\"icon-wenjianshangchuan\",\"options\":{\"defaultValue\":[],\"width\":\"\",\"tokenFunc\":\"funcGetToken\",\"token\":\"\",\"tokenType\":\"datasource\",\"domain\":\"http://tcdn.form.making.link/\",\"disabled\":false,\"tip\":\"\",\"fileFormat\":[\"pdf\"],\"action\":\"\",\"customClass\":\"\",\"limit\":9,\"multiple\":true,\"isQiniu\":false,\"labelWidth\":100,\"isLabelWidth\":false,\"hidden\":false,\"dataBind\":true,\"headers\":[],\"required\":false,\"validatorCheck\":false,\"validator\":\"\",\"withCredentials\":false,\"remoteFunc\":\"func_2jsft4gt\",\"remoteOption\":\"option_2jsft4gt\",\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onUploadSuccess\":\"\",\"onUploadError\":\"\",\"onRemove\":\"\"},\"name\":\"文件\",\"key\":\"2jsft4gt\",\"model\":\"fileupload_2jsft4gt\",\"rules\":[]},{\"type\":\"imgupload\",\"icon\":\"icon-tupian\",\"options\":{\"defaultValue\":[],\"size\":{\"width\":100,\"height\":100},\"width\":\"\",\"tokenFunc\":\"funcGetToken\",\"token\":\"\",\"tokenType\":\"datasource\",\"domain\":\"\",\"disabled\":false,\"readonly\":false,\"limit\":8,\"multiple\":true,\"isQiniu\":false,\"isDelete\":true,\"min\":0,\"isEdit\":false,\"fileFormat\":[\"png\",\"jpg\",\"jpeg\"],\"action\":\"\",\"customClass\":\"\",\"labelWidth\":100,\"isLabelWidth\":false,\"hidden\":false,\"dataBind\":true,\"headers\":[],\"required\":false,\"validatorCheck\":false,\"validator\":\"\",\"withCredentials\":false,\"remoteFunc\":\"func_8l3684vp\",\"remoteOption\":\"option_8l3684vp\",\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onUploadSuccess\":\"\",\"onUploadError\":\"\",\"onRemove\":\"\"},\"name\":\"图片\",\"key\":\"8l3684vp\",\"model\":\"imgupload_8l3684vp\",\"rules\":[]}],\"config\":{\"labelWidth\":100,\"formName\":\"\",\"labelPosition\":\"right\",\"size\":\"small\",\"customClass\":\"\",\"ui\":\"element\",\"layout\":\"horizontal\",\"labelCol\":3,\"width\":\"100%\",\"hideLabel\":false,\"hideErrorMessage\":false,\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]}}"


- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]


- test:
    name: "setup-创建1个2内容域2签署区的模板，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate3.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "setup-关联模板、流程引擎到业务模板并保存"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
       - allowAddFile: 1
       - initiatorAll: 1
       - initiatorList: []
       - presetId: $testPresetId
       - presetName: $autoPresetName
       - templateList: [{
         "templateId": $newTemplateUuidCommon,
         "templateName": $templateNameCommon,
         "version": $newVersionCommon
       }
       ]
       - workFlowModelKey: $autotestModelKey
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup-获取业务模板的签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - testTemplateName: content.data.templateList.0.name
#      - signatoryName1: content.data.templateList.0.simpleVOList.0.name
#      - signType1: content.data.templateList.0.simpleVOList.0.signType
#      - signatoryId1: content.data.templateList.0.simpleVOList.0.id
#      - signatoryName2: content.data.templateList.0.simpleVOList.1.name
#      - signType2: content.data.templateList.0.simpleVOList.1.signType
#      - signatoryId2: content.data.templateList.0.simpleVOList.1.id
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "setup-业务模板第三步：添加签署方（内部个人指定（测试文档中心自动化用户勿改动）+相对方个人）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 0,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": $autotestModelKey,
        "workFlowModelName": $autotestWorkFlowModelName,
        "workFlowModelStatus": 1,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
            {
                "signNode": 2,
                "signMode": 1,
                "signerList": [
                    {
                        "templateInitiationSignersUuid": null,
                        "signerId": $randomSignerId0,
                        "signerSnapshotId": null,
                        "signerTerritory": 2,
                        "signerType": 1,
                        "assignSigner": 0,
                        "userName": null,
                        "userCode": null,
                        "userAccount": null,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 1,
                        "signNode": 2,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": [],
                        "id": "0-0",
                        "draggable": true
                    }
                ],
                "id": "node-0"
            },
            {
                "signMode": 1,
                "id": "node-1",
                "signerList": [
                    {
                        "signerType": 1,
                        "signerTerritory": 1,
                        "draggable": true,
                        "organizeCode": "",
                        "userCode": $signerUserCode_inner,
                        "id": "add-1",
                        "sealTypeCode": "",
                        "sealTypeList": [],
                        "accountList": [],
                        "organizeList": [],
                        "autoSign": 0,
                        "assignSigner": 1,
                        "signerId": $randomSignerId1,
                        "organizeName": "",
                        "sealTypeName": "",
                        "userName": $signerUserName_inner,
                        "departmentCode": "",
                        "departmentName": "",
                        "signatoryList": []
                    }
                ]
            }
        ]
    }
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]
#
#- test:
#    name: "TC-追加采集表单设置到外部个人用户：单行文本、图片、文件"
#    api: api/esignDocs/docGatherForm/buildForm.yml
#    variables:
#      - formJson: $testFormJson
#      - formName: ""
#      - presetUuid: $testPresetId
#      - signerId: $randomSignerId0
#      - signerSnapshotId: $randomSignerId0
#    validate:
#        - eq: [ content.message, "成功" ]
#        - eq: [ content.status, 200 ]
#        - eq: [content.success, true]
#
#- test:
#    name: "setup-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
#    api: api/esignDocs/businessPreset/templateDetail.yml
#    variables:
#      - presetId: $testPresetId
#      - templateId: $newTemplateUuidCommon
#      - version: $newVersionCommon
#    extract:
#      templateContentId0: content.data.contentList.0.templateContentId
#      templateContentName0: content.data.contentList.0.contentName
#      templateContentId1: content.data.contentList.1.templateContentId
#      templateContentName1: content.data.contentList.1.contentName
#      signerId0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.signerId
#      signerName0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.name
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.data.templateId, $newTemplateUuidCommon]
#
#- test:
#    name: "setup-业务模板设置第四步：填写方设置（2个内容域都设置为相对方个人填写）+启用业务模板"
#    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
#    variables:
#      - presetId: $testPresetId
#      - status: 1
#      - templateList: [ {
#        "templateId": $newTemplateUuidCommon,
#        "templateName": $templateNameCommon,
#        "version": $newVersionCommon,
#        "contentList": [ {
#          "contentId": $templateContentId0,
#          "contentName": $templateContentName0,
#          "contentSource": 2,
#          "signerId": $signerId0,
#          "signerName": $signerName0
#        },
#          {
#            "contentId": $templateContentId1,
#            "contentName": $templateContentName1,
#            "contentSource": 2,
#            "signerId": $signerId0,
#            "signerName": $signerName0
#          } ]
#      } ]
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
##发起电子签署系列接口
#- test:
#    name: "setup-获取发起人关联的组织信息"
#    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
#    variables:
#      - businessPresetUuid: $testPresetId
#    extract:
#      - departmentCode: content.data.list.0.departmentCode
#      - departmentName: content.data.list.0.departmentName
#      - organizationCode: content.data.list.0.organizationCode
#      - organizationName: content.data.list.0.organizationName
#      - initiatorUserName: content.data.initiatorUserName
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "获取getInfo的接口的batchTemplateInitiationUuid"
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    variables:
#      "params": { }
#    extract:
#      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]
#
#- test:
#    name: "setup-获取批量发起选择页业务模板配置详情"
#    api: api/esignDocs/businessPreset/choseDetail.yml
#    variables:
#      - presetId: $testPresetId
#    extract:
#      - signerId0: content.data.signerNodeList.0.signerList.0.signerId
#      - signatoryList0: content.data.signerNodeList.0.signerList.0.signatoryList
#      - signerId1: content.data.signerNodeList.1.signerList.0.signerId
#      - signatoryList1: content.data.signerNodeList.1.signerList.0.signatoryList
#      - userAccount1: content.data.signerNodeList.1.signerList.0.userAccount
#      - userCode1: content.data.signerNodeList.1.signerList.0.userCode
#      - userName1: content.data.signerNodeList.1.signerList.0.userName
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
##暂存
#- test:
#    name: "setup-暂存"
#    api: api/esignDocs/batchTemplateInitiation/staging.yml
#    variables:
#      "params": {
#        "batchTemplateInitiationName": $autoPresetName,
#        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#        "excelFileKey": null,
#        "initiatorOrganizeCode": $organizationCode,
#        "initiatorOrganizeName": $organizationName,
#        "initiatorUserName": $initiatorUserName,
#        "initiatorDepartmentName": $departmentName,
#        "initiatorDepartmentCode": $departmentCode,
#        "businessPresetUuid": $testPresetId,
#        "businessPresetSnapshotUuid": null,
#        "wordList": [ ],
#        "saveSigners": null,
#        "signersList": [
#            {
#                "signNode": 2,
#                "signMode": 1,
#                "signerList": [
#                    {
#                        "templateInitiationSignersUuid": null,
#                        "signerId": $signerId0,
#                        "signerSnapshotId": null,
#                        "signerTerritory": 2,
#                        "signerType": 1,
#                        "assignSigner": 0,
#                        "userName": null,
#                        "userCode": null,
#                        "userAccount": null,
#                        "legalSign": 0,
#                        "organizeName": null,
#                        "organizeCode": null,
#                        "departmentName": null,
#                        "departmentCode": null,
#                        "sealTypeCode": null,
#                        "sealTypeName": null,
#                        "autoSign": 0,
#                        "needGather": 1,
#                        "signNode": 2,
#                        "signOrder": 1,
#                        "signMode": 1,
#                        "signerStatus": null,
#                        "signerStatusStr": null,
#                        "signatoryList": $signatoryList0
#                    }
#                ],
#                "id": 0,
#                "draggable": false
#            },
#            {
#                "signNode": 3,
#                "signMode": 1,
#                "signerList": [
#                    {
#                        "templateInitiationSignersUuid": null,
#                        "signerId": $signerId1,
#                        "signerSnapshotId": null,
#                        "signerTerritory": 1,
#                        "signerType": 1,
#                        "assignSigner": 1,
#                        "userName": $userName1,
#                        "userCode": $userCode1,
#                        "userAccount": $userAccount1,
#                        "legalSign": 0,
#                        "organizeName": null,
#                        "organizeCode": null,
#                        "departmentName": null,
#                        "departmentCode": null,
#                        "sealTypeCode": null,
#                        "sealTypeName": null,
#                        "autoSign": 0,
#                        "needGather": 0,
#                        "signNode": 3,
#                        "signOrder": 1,
#                        "signMode": 1,
#                        "signerStatus": null,
#                        "signerStatusStr": null,
#                        "signatoryList": $signatoryList1,
#                        "userList":                             {
#                                "id": $signerUserCode_inner,
#                                "name": $signerUserName_inner,
#                                "mobile": "",
#                                "organizeCode": null,
#                                "organizeName": null
#                            },
#                        "option": [
#                            {
#                                "value": "1-1",
#                                "label": "内部个人",
#                                "children": [
#                                    {
#                                        "value": 1,
#                                        "label": "指定签署方"
#                                    },
#                                    {
#                                        "value": 0,
#                                        "label": "不指定签署方"
#                                    }
#                                ]
#                            }
#                        ],
#                        "default": [
#                            "1-1",
#                            1
#                        ]
#                    }
#                ],
#                "id": 1,
#                "draggable": false
#            }
#        ],
#        "type": 3,
#        "contentList": [ ],
#        "sort": 0,
#        "advertisement": false,
#        "chargingType": 1,
#        "readComplete": false,
#        "remark": null,
#        "signFlowExpireTime": null,
#        "businessNo": null,
#        "appendList": [ ],
#        "attachments": [ ],
#        "ccInfos": [ ],
#        "auditInfo": null
#    }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-获取getInfo的接口的签署方信息"
#    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
#    variables:
#      "params": {
#                  "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
#      }
#    extract:
#      - templateInitiationSignersUuid_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
#      - signerId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
#      - signerSnapshotId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
#      - templateInitiationSignersUuid_inner: content.data.businessPresetDetail.signerNodeList.1.signerList.0.templateInitiationSignersUuid
#      - signerId_inner: content.data.businessPresetDetail.signerNodeList.1.signerList.0.signerId
#      - signerSnapshotId_inner: content.data.businessPresetDetail.signerNodeList.1.signerList.0.signerSnapshotId
#    validate:
#      - eq: [ content.message,"成功" ]
#      - eq: [ content.success,true ]
#
#- test:
#    name: "setup-流程新增实例和获取对应流程的相关信息，submit提交时下一环节审批人使用默认的审批人"
#    api: api/esignDocs/flow/manage_addWorkflowInstance.yml
#    variables:
#       modelKey: $autotestModelKey
#    extract:
#      - testNestNodeConfigCode: content.data.nextNodeConfigList.0.nodeConfigCode
#      - testNextHandlerOrganizationCode: content.data.nextHandlerOrganizationCode
#      - testNextHandlerOrganizationName: content.data.nextHandlerOrganizationName
#      - testNextNodeHandlerCode: content.data.nextNodeHandlerCode
#      - testNextNodeHandlerName: content.data.nextNodeHandlerName
#      - testThisFormUrl: content.data.nodeConfig.formUrl
#    validate:
#      - eq: [ content.message,"成功" ]
#      - eq: [ content.success,true ]
#
#- test:
#    name: "setup-提交电子签署（单份发起）"
#    api: api/esignDocs/batchTemplateInitiation/submit.yml
#    variables:
#      "params": {
#    "batchTemplateInitiationName": $autoPresetName,
#    "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
#    "excelFileKey": null,
#    "initiatorOrganizeCode": $organizationCode,
#    "initiatorOrganizeName": $organizationName,
#    "initiatorUserName": $initiatorUserName,
#    "initiatorDepartmentName": null,
#    "initiatorDepartmentCode": null,
#    "businessPresetUuid": $testPresetId,
#    "businessPresetSnapshotUuid": "",
#    "wordList": [],
#    "saveSigners": null,
#    "signersList": [
#        {
#            "signNode": 2,
#            "signMode": 1,
#            "signerList": [
#                {
#                    "templateInitiationSignersUuid": $templateInitiationSignersUuid_outer,
#                    "signerId": $signerId_outer,
#                    "signerSnapshotId": $signerSnapshotId_outer,
#                    "signerTerritory": 2,
#                    "signerType": 1,
#                    "assignSigner": 0,
#                    "userName": $signerUserName_outer,
#                    "userCode": $signerUserCode_outer,
#                    "userAccount": "",
#                    "legalSign": 0,
#                    "organizeName": null,
#                    "organizeCode": null,
#                    "departmentName": null,
#                    "departmentCode": null,
#                    "sealTypeCode": null,
#                    "sealTypeName": null,
#                    "autoSign": 0,
#                    "needGather": 1,
#                    "signNode": 2,
#                    "signOrder": 1,
#                    "signMode": 1,
#                    "signerStatus": null,
#                    "signerStatusStr": null,
#                    "signatoryList": $signatoryList0
#        }]},
#        {
#            "signNode": 3,
#            "signMode": 1,
#            "signerList": [
#                {
#                    "templateInitiationSignersUuid": $templateInitiationSignersUuid_inner,
#                    "signerId": $signerId_inner,
#                    "signerSnapshotId": $signerSnapshotId_inner,
#                    "signerTerritory": 1,
#                    "signerType": 1,
#                    "assignSigner": 1,
#                    "userName": $signerUserName_inner,
#                    "userCode": $signerUserCode_inner,
#                    "userAccount": $userAccount1,
#                    "legalSign": 0,
#                    "organizeName": null,
#                    "organizeCode": null,
#                    "departmentName": null,
#                    "departmentCode": null,
#                    "sealTypeCode": null,
#                    "sealTypeName": null,
#                    "autoSign": 0,
#                    "needGather": 0,
#                    "signNode": 3,
#                    "signOrder": 1,
#                    "signMode": 1,
#                    "signerStatus": null,
#                    "signerStatusStr": null,
#                    "signatoryList": $signatoryList1
#                }
#            ]
#        }
#    ],
#    "type": 3,
#    "contentList": [],
#    "sort": 0,
#    "advertisement": false,
#    "chargingType": 1,
#    "readComplete": false,
#    "remark": null,
#    "signFlowExpireTime": null,
#    "businessNo": null,
#    "appendList": [],
#    "attachments": [],
#    "ccInfos": [],
#    "auditInfo": {
#        "auditResult": "",
#        "carbonCopyList": [],
#        "nextAssigneeList": [
#            {
#                "nextAssignee": $testNextNodeHandlerCode,
#                "nextAssigneeOrganizationCode": $testNextHandlerOrganizationCode,
#                "nextAssigneeId": "",
#                "nextAssigneeName": $testNextNodeHandlerName,
#                "nextAssigneeOrganizationName": $testNextHandlerOrganizationName
#            }
#        ],
#        "nodeConfigCode": $testNestNodeConfigCode,
#        "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=$testThisFormUrl&batchTemplateInitiationUuid=$batchTemplateInitiationUuidNew",
#        "sendNotice": "0",
#        "variables": {}
#    }
#}
#    extract:
#      - _flowId: content.data
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - ne: [ "content.data","" ]
#    teardown_hooks:
#      - ${sleep(2)}
#
#- test:
#    name: submitResult-查询发起的结果-V6.0.12.0-带有审批流程的填写中且填写人和发起人不一致不能跳转
#    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
#    variables:
#      flowId_submitResult: "$_flowId"
##    extract:
##      - code0: content.code
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.message, "成功"]
#      - eq: [content.data.jumpUrl, null]
#      - eq: [content.data.flowStatus, 1] #1-去填写，2-去审批，3-去签署
#      - eq: [content.data.initiator.initiatorDepartmentName, "$organizationName"]
#      - eq: [content.data.initiator.initiatorOrganizeName, "$organizationName"]
#      - eq: [content.data.initiator.initiatorName, "$initiatorUserName"]
#      - len_eq: [content.data.signerList, 2]
##      - eq: [content.data.signerList.0.signerName, "$signerUserName_inner"]
#      - eq: [content.data.signerList.0.signerOrganizationName, null]
#      - len_eq: [content.data.fillingUserList, 1]
#      - eq: [content.data.fillingUserList.0.fillingUserName, "$signerUserName_outer"]
##
##- test:
#    name: "TC-查看采集表单详情"
#    api: api/esignDocs/docGatherForm/queryGatherForm.yml
#    variables:
#      - account: $userAccountKey
#      - password: $userpasswordKey
#      - signerId: $randomSignerId0
#      - signerSnapshotId: $signerSnapshotId_outer
#    validate:
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.status, 200]
#      - eq: [content.data.formJson, $testFormJson]
#
#- test:
#    name: "TC-外部个人签署人填写页查询签署文件列表"
#    api: api/esignDocs/docGatherForm/querySignerFillFiles.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#    extract:
#      - templateIdS: content.data.templateFileList.0.templateId
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200]
#      - length_equals: [content.data.templateFileList, 1]
#      - eq: [content.data.templateFileList.0.templateFileName, $templateNameCommon]
#
#- test:
#    name: "TC-外部个人签署人填写页查询签署文件明细(内容域、签署区)"
#    api: api/esignDocs/docGatherForm/signerFileDetail.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - templateId: $templateIdS
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200]
#      - length_equals: [content.data.contentList, 2]
#      - length_equals: [content.data.signatoryList, 2]

#- test:
#    name: "TC-【保存】外部签署人正文信息和追加采集信息填写-正文：文本，追加：单行文本、图片、文件"
#    api: api/esignDocs/docGatherForm/signerFill.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - hasSubmit: 0
#      - signerContents: [
#            {
#                "contentName": $templateContentName0,
#                "contentValue": "正文填写文本1",
#                "formatType": 0,
#                "formatRule": "28",
#                "formatSelect": null,
#                "length": 28,
#                "required": 0,
#                "templateIdWithContentIdsMap": {
#                    $templateIdS: [
#                        $templateContentId0
#                    ]
#                },
#                "fileName": null,
#                "id": "0file",
#                "type": "input"
#            },
#            {
#                "contentName": $templateContentName1,
#                "contentValue": "正文填写文本2",
#                "formatType": 0,
#                "formatRule": "28",
#                "formatSelect": null,
#                "length": 28,
#                "required": 1,
#                "templateIdWithContentIdsMap": {
#                    $templateIdS: [
#                         $templateContentId1
#                    ]
#                },
#                "fileName": null,
#                "id": "1file",
#                "type": "input"
#            }
#        ]
#      - formValues: [
#            {
#                "elementValue": "追加填写单行文本",
#                "elementKey": "h3pf2rvo",
#                "elementType": "input",
#                "elementName": "单行文本",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "model": "input_h3pf2rvo"
#            },
#            {
#                "elementValue": "$pdfFileKey",
#                "elementKey": "2jsft4gt",
#                "elementType": "fileupload",
#                "elementName": "文件",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "backfillExtOrg": "",
#                "backfillExtUser": "",
#                "model": "fileupload_2jsft4gt"
#            },
#            {
#                "elementValue": $picFileKey,
#                "elementKey": "8l3684vp",
#                "elementType": "imgupload",
#                "elementName": "图片",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "backfillExtOrg": "",
#                "backfillExtUser": "",
#                "model": "imgupload_8l3684vp"
#            }
#        ]
#      - wordList: []
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200 ]
#      - eq: [content.success, true]
#
#- test:
#    name: "TC-【提交】外部签署人正文信息和追加采集信息填写-正文：文本，追加：单行文本、图片、文件"
#    api: api/esignDocs/docGatherForm/signerFill.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - hasSubmit: 1
#      - signerContents: [
#            {
#                "contentName": $templateContentName0,
#                "contentValue": "正文填写文本1",
#                "formatType": 0,
#                "formatRule": "28",
#                "formatSelect": null,
#                "length": 28,
#                "required": 0,
#                "templateIdWithContentIdsMap": {
#                    $templateIdS: [
#                        $templateContentId0
#                    ]
#                },
#                "fileName": null,
#                "id": "0file",
#                "type": "input"
#            },
#            {
#                "contentName": $templateContentName1,
#                "contentValue": "正文填写文本2",
#                "formatType": 0,
#                "formatRule": "28",
#                "formatSelect": null,
#                "length": 28,
#                "required": 1,
#                "templateIdWithContentIdsMap": {
#                    $templateIdS: [
#                         $templateContentId1
#                    ]
#                },
#                "fileName": null,
#                "id": "1file",
#                "type": "input"
#            }
#        ]
#      - formValues: [
#            {
#                "elementValue": "追加填写单行文本",
#                "elementKey": "h3pf2rvo",
#                "elementType": "input",
#                "elementName": "单行文本",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "model": "input_h3pf2rvo"
#            },
#            {
#                "elementValue": "$pdfFileKey",
#                "elementKey": "2jsft4gt",
#                "elementType": "fileupload",
#                "elementName": "文件",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "backfillExtOrg": "",
#                "backfillExtUser": "",
#                "model": "fileupload_2jsft4gt"
#            },
#            {
#                "elementValue": $picFileKey,
#                "elementKey": "8l3684vp",
#                "elementType": "imgupload",
#                "elementName": "图片",
#                "formatType": "",
#                "formatRule": "",
#                "asSignFile": "0",
#                "backfillExtOrg": "",
#                "backfillExtUser": "",
#                "model": "imgupload_8l3684vp"
#            }
#        ]
#      - wordList: []
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200 ]
#      - eq: [content.success, true]
#
#- test:
#    name: "TC-签署人填写页面正文信息与追加采集信息查询"
#    api: api/esignDocs/docGatherForm/querySignerFillValue.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200 ]
#      - eq: [content.success, true ]
#      - eq: [content.data.currentUserIsFiller, 1]
#      - eq: [content.data.hasSubmit, 1]
#      - length_equals: [content.data.formValues, 3]
#      - length_equals: [content.data.signerContents, 2]
#
#- test:
#    name: "TC-外部用户查看追加的采集填写信息"
#    api: api/esignDocs/docGatherForm/queryGatherFormValue.yml
#    variables:
#      - phoneOrMail: $outer_user_mobile_key
#      - templateInitiationSignersUuid: $templateInitiationSignersUuid_outer
#      - templateInitiationUuid: $batchTemplateInitiationUuidNew
#    validate:
#      - eq: [content.message,"成功" ]
#      - eq: [content.status, 200 ]
#      - eq: [content.success, true ]
#      - length_equals: [content.data.formValues, 3]