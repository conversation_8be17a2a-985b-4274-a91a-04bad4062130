- config:
    name: "openapi填写模板并转为pdf-填写错误，超出控件长度，message透出内容域名称-60140.beta3"
    variables:
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版${random_str(4)}"
      description: "自动化测试描述"
      contentName1: "自动化测试内容域"
      contentCode1: ZDHCS
      autoTestDocUuid1: ${get_docConfig_type()}
      contentFieldId001: "${get_randomStr_32()}"

- test:
    name: "新建文档模板"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",True ]
      - ne: [ "content.data",null ]


- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试文本-0525",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "文本测试",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "1",
            "minFontSize": "10",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "文本测试",
          "defaultValue": "文本测试",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC1-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId1: content.data.contentsControl.0.contentId
      - contentCode1: content.data.contentsControl.0.contentCode
      - contentName1: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC1-填写超出预设的长度-文本"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode1
      - contentValue: "测试自动化文本"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId1($contentCode1), 控件名称: $contentName1, 填写内容: 测试自动化文本, 失败原因: 控件\"$contentName1\"填写内容长度超限,最大长度为:5个汉字(或10个数字或英文)" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC1-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC2-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC2-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC2-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC2-epaasTemplate-batch-save-draft-手机号内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试手机号-0525",
          "custom": false,
          "type": "PHONE_NUM",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 315,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "11",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-telephone",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC2-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC2-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId2: content.data.contentsControl.0.contentId
      - contentCode2: content.data.contentsControl.0.contentCode
      - contentName2: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC2-填写格式不正确-超出手机号长度-手机号"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode2
      - contentValue: "1234567898654"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId2($contentCode2), 控件名称: $contentName2, 填写内容: 1234567898654, 失败原因: 手机号控件'$contentName2'填写格式不正确" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC3-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC3-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC3-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC3-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC3-epaasTemplate-batch-save-draft-身份证号内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试身份证号-0525",
          "custom": false,
          "type": "ID_CARD",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 436,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-id-card",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC3-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC3-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId3: content.data.contentsControl.0.contentId
      - contentCode3: content.data.contentsControl.0.contentCode
      - contentName3: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC3-填写格式不正确-身份证号"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode3
      - contentValue: "1112222222222222222"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId3($contentCode3), 控件名称: $contentName3, 填写内容: 1112222222222222222, 失败原因: 控件'$contentName3'身份证号码填写有误,请检查!" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC4-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC4-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC4-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC4-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC4-epaasTemplate-batch-save-draft-邮箱内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试邮箱-0525",
          "custom": false,
          "type": "EMAIL",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-mail",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC4-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC4-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId4: content.data.contentsControl.0.contentId
      - contentCode4: content.data.contentsControl.0.contentCode
      - contentName4: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC4-填写格式不正确-邮箱"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode4
      - contentValue: "<EMAIL>"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId4($contentCode4), 控件名称: $contentName4, 填写内容: <EMAIL>, 失败原因: 控件\"$contentName4\"填写内容长度超限,最大长度为:5个汉字(或10个数字或英文)" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC5-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-社会信用代码内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试社会信用代码-0525",
          "custom": false,
          "type": "UNIFY_THE_SOCIAL_CREDIT_CODE",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 436,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-uscc",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC5-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId5: content.data.contentsControl.0.contentId
      - contentCode5: content.data.contentsControl.0.contentCode
      - contentName5: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC5-填写格式不正确-社会信用代码"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode5
      - contentValue: "9100000087423245J21"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId5($contentCode5), 控件名称: $contentName5, 填写内容: 9100000087423245J21, 失败原因: 统一社会信用代码'$contentName5'填写格式不正确" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC6-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC6-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC6-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC6-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC6-epaasTemplate-batch-save-draft-日期内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试身份证号-0525",
          "custom": false,
          "type": "DATE",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 436,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": "yyyy-MM-dd",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-date",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC6-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC6-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId6: content.data.contentsControl.0.contentId
      - contentCode6: content.data.contentsControl.0.contentCode
      - contentName6: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC6-填写格式不正确-日期"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode6
      - contentValue: "2024-10-241"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId6($contentCode6), 控件名称: $contentName6, 填写内容: 2024-10-241, 失败原因: 日期控件'$contentName6'格式与预设不符: yyyy-MM-dd" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC7-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC7-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-batch-save-draft-数字内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试数字-0525",
          "custom": false,
          "type": "NUM",
          "subType": null,
          "sort": 1,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 108.43,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "groupKey": "",
              "tickOptions": null,
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": "0",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": "0",
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-number",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00,
            "y": 674.90,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC7-启用模板"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC7-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId7: content.data.contentsControl.0.contentId
      - contentCode7: content.data.contentsControl.0.contentCode
      - contentName7: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC7-填写格式不正确-数字"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化"
      - contentId: ""
      - contentCode: $contentCode7
      - contentValue: "11122231111"
    validate:
      - eq: [ "content.data", null ]
      - contains: [ "content.message","参数异常 填写内容校验失败, 控件id(key): $contentId7($contentCode7), 控件名称: $contentName7, 填写内容: 11122231111, 失败原因: 控件\"$contentName7\"填写内容长度超限,最大长度为:5个汉字(或10个数字或英文)" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "TC7-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "删除模板"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]