#openapi填写模板并转为pdf-模版id
- config:
    variables:
        docRandomNo: ${get_randomNo()}
        autoTestDocUuid1: ${get_docConfig_type()}
        FileName: "testppp.pdf"
        fileKey: ${ENV(fileKey)}
        commonTemplateName: "自动化测试模版-合同测试"
        description: "自动化测试描述"
        page: 1
        size: 5
        contentName1: "自动化测试内容域$docRandomNo"
        contentCode1: "zdhcsypcode$docRandomNo"
        contentValue1: "111111"
        contentFieldId001: "${get_randomStr_32()}"
        fillContent001: {
          "fieldId": "",
          "label": "自动化测试-数字0525",
          "custom": false,
          "type": "NUM",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 12,
            "textColor": "#2969B0",
            "width": 90.21,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "111112",
            "required": false,
            "dateFormat": "0",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": 0,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "15",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-number",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "111112",
          "defaultValue": "111112",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template3: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [ $fillContent001 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    extract:
      - contentId1: content.data.0.contentId
      - contentVersionId1: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId1: content.data.contentsControl.0.contentId
      - contentCode1: content.data.contentsControl.0.contentCode
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC1-正确格式的数字"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: "89"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId1,
                  contentCode: $contentCode1,
                  contentValue: "89"
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC2-错误格式的数字"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: "89.1"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId1,
                  contentCode: $contentCode1,
                  contentValue: "89.1"
                } ] }
    validate:
#      - contains: [ "content.message","格式填写错误，【数字】校验不通过，正确格式为小数位数【0】,总长度不超过15位" ]
      - contains: [ "content.message","数字格式不匹配" ]
      - eq: [ "content.code",1601009 ]
      - eq: [ "content.data",null ]

- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]