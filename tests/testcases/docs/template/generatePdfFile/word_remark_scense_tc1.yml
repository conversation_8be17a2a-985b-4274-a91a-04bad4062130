- config:
    name: "word模板：一个正文签署区，一个骑缝签署区，一个备注签署区"
    variables:
      autoTestDocUuid1: ${get_docConfig_type()}
      createUserOrg: ${ENV(sign01.main.orgCode)}
      authorization0: ${getPortalToken()}
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      templateNameCommon: "自动化测试通用模版-word模板-带备注-${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      signNameA: "甲方企业"
      signNameB: "乙方企业"

- test:
    name: "上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - fileKey

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateName: $templateNameCommon
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 1,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          },{
            "allowMove": false,
            "addSignTime": 1,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 3
          },{
            "allowMove": true,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signFieldType": 1,
            "remarkSignatory": {"inputType":2,"aiCheck":0,"remarkContent":"","remarkFontSize":10,"collectionTaskId":"","remarkPrompt":""},
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
        "fileKey": $htmlFileKey
      } }
    extract:
      - templateUuid0: content.data.templateUuid
      - templateType0: content.data.templateType
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
      - ne: [ "content.data", null ]

- test:
    name: "TC4-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateUuid0}
    extract:
      - sealControl0: content.data.sealControl
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$templateUuid0" ]
      - eq: [ content.data.contentsControl, [] ]
      - len_eq: [ content.data.sealControl, 3 ]
      - eq: [ content.data.sealControl.0.sealControlName,  "甲方企业" ]

- test:
    name: "TC5-查询模板控件内容-详细比对"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateUuid0}
      remarkSignConfig0: "${dataArraySort($sealControl0, signFieldType,2, remarkSignConfig)}"
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "${dataArraySort($sealControl0, sealControlType,0, sealControlType)}", "1" ]
      - eq: [ "${dataArraySort($sealControl0, signFieldType,2, signFieldType)}", 1 ]
      - eq: [ "${dataArraySort($sealControl0, sealControlType,2, sealControlType)}", "2" ]
      - eq: [ "${get_json_field($remarkSignConfig0, inputType)}", 2 ]
      - eq: [ "${get_json_field($remarkSignConfig0, aiCheck)}", 0 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFontSize)}", 10 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkPageNo)}", null ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFieldWidth)}", 165 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFieldHeight)}", 165 ]

- test:
    name: "TC6-停用草稿模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $templateUuid0
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]

- test:
    name: "TC7-查询模板控件-确认模板停用"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateUuid0}
    validate:
      - eq: [ "content.code",1605011 ]
      - eq: ["content.data",null]
      - eq: [ "content.message","该模板并未发布" ]

- test:
    name: "TC8-模版编辑变成草稿状态"
    api: api/esignDocs/template/owner/templateUpdate.yml
    variables:
      - templateUuid: $templateUuid0
      - version: $newVersionCommon
      - fileKey: $htmlFileKey
      - templateType: 2
      - zipFileKey: null
      - templateName: $templateNameCommon
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data", "" ]

- test:
    name: "TC9-发布模板-同名不合并"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 1,
            "name": "A001",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          },{
            "allowMove": false,
            "addSignTime": 1,
            "name": "A001",
            "edgeScope": null,
            "thirdKey": "ele-1655434999482",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          },{
            "allowMove": true,
            "addSignTime": 0,
            "name": "B001",
            "edgeScope": null,
            "thirdKey": "ele-1655434999483",
            "keywordOrder": "",
            "keywordType": 0,
            "signFieldType": 1,
            "remarkSignatory": {"inputType":2,"aiCheck":0,"remarkContent":"","remarkFontSize":10,"collectionTaskId":"","remarkPrompt":""},
            "signType": 1
          },{
            "allowMove": true,
            "addSignTime": 0,
            "name": "B001",
            "edgeScope": null,
            "thirdKey": "ele-1655434999484",
            "keywordOrder": "",
            "keywordType": 0,
            "signFieldType": 1,
            "remarkSignatory": {
              "inputType":3,
              "aiCheck":0,
              "remarkContent":"",
              "remarkFontSize": 8,
              "collectionTaskId":"${getRemarkCollectionTaskId()}",
              "remarkPrompt":""
            },
            "signType": 1
          }
        ],
        "templateUuid": $templateUuid0,
        "version": $newVersionCommon,
        "fileKey": $htmlFileKey
      } }
    extract:
      - templateUuid0: content.data.templateUuid
      - templateType0: content.data.templateType
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
      - ne: [ "content.data", null ]

- test:
    name: "TC10-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateUuid0}
    extract:
      - sealControl0: content.data.sealControl
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$templateUuid0" ]
      - eq: [ content.data.contentsControl, [] ]
      - len_eq: [ content.data.sealControl, 4 ]
      - contains: [ content.data.sealControl.0.sealControlName,  "001" ]

- test:
    name: "TC11-查询模板控件内容-详细比对"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateUuid0}
      remarkSignConfig0: "${dataArraySort($sealControl0, signFieldType,2, remarkSignConfig)}"
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "${dataArraySort($sealControl0, sealControlType,0, sealControlType)}", "1" ]
      - eq: [ "${dataArraySort($sealControl0, signFieldType,2, signFieldType)}", 1 ]
      - eq: [ "${dataArraySort($sealControl0, sealControlType,2, sealControlType)}", "1" ]
      - eq: [ "${get_json_field($remarkSignConfig0, inputType)}", 2 ]
      - eq: [ "${get_json_field($remarkSignConfig0, aiCheck)}", 0 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFontSize)}", 10 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkPageNo)}", null ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFieldWidth)}", 165 ]
      - eq: [ "${get_json_field($remarkSignConfig0, remarkFieldHeight)}", 165 ]