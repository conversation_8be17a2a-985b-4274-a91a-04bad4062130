- config:
    name: "线上问题-https://forward.esign.cn/mainBugManagement/edit?id=21185&type=check"
    variables:
      presetName0: "自动化-有很多内容域${getDateTime()}"
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用模版-内容域大全-${get_randomNo_16()}"
      description: "自动化测试描述"
      initiatorAll: 1
      signNameA: "甲方个人"
      signNameB: "乙方企业"
      timePosX: 10
      timePosY: 10
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      width: 216
      height: 36
      contentUuid: ""
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
      required: 1
      newVersion1: 1
      CCInfosCBBT: []
      userCode0: ${ENV(sign01.userCode)}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - autoTestDocUuid1Common
      - signatoryDomainUUid_common1
      - signNameA

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "TC2-setup_添加内容域 自动化测试-数字"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-数字"
      - formatType: 6
      - formatRule: "2"
      - edgeScope: 0
      - length: 15
      - required: 1
      - contentCode: "code1"
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup_添加内容域相同code的数字，长度不一致"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-数字"
      - formatType: 6
      - formatRule: "2"
      - edgeScope: 0
      - length: 20
      - required: 1
      - contentCode: "code1"
      - pageNo: "2"
      - posX: 200
      - posY: 100
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC2-setup_添加内容域 自动化测试-文本"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-文本"
      - formatType: 0
      - formatRule: ""
      - edgeScope: 0
      - length: 11
      - contentCode: "code2"
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC2-setup_添加内容域 自动化测试相同code的文本，长度不一致"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-文本"
      - formatType: 0
      - formatRule: ""
      - edgeScope: 0
      - length: 15
      - contentCode: "code2"
      - pageNo: 2
      - posX: 34
      - posY: 3
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC2-setup_添加内容域 自动化测试-日期1-红色"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-日期1"
      - formatType: 7
      - formatRule: "yyyy年MM月dd日"
      - edgeScope: 0
      - length: 11
      - contentCode: "code3"
      - fontColor: "RED"
      - fontSize: 12
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC2-setup_添加内容域 自动化测试-日期1-黑色"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-日期1"
      - formatType: 7
      - formatRule: "yyyy年MM月dd日"
      - edgeScope: 0
      - length: 11
      - contentCode: "code3"
      - fontColor: "RED"
      - fontSize: 16
      - pageNo: 3
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]



- test:
    name: "TC2-setup_添加内容域 自动化测试-统一社会信用代码"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-统一社会信用代码"
      - formatType: 4
      - formatRule: ""
      - edgeScope: 0
      - length: 18
      - contentCode: "code4"
      - fontStyle: "Normal"
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup_添加内容域 自动化测试-统一社会信用代码2"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-统一社会信用代码"
      - formatType: 4
      - formatRule: ""
      - edgeScope: 0
      - length: 18
      - contentCode: "code4"
      - fontStyle: "Normal"
      - fontSize: 12
      - pageNo: 2
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC2-setup_添加内容域 自动化测试-身份证1"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-身份证"
      - formatType: 2
      - formatRule: ""
      - edgeScope: 0
      - length: 18
      - contentCode: "code5"
      - fontColor: "RED"
      - pageNo: 3
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup_添加内容域 自动化测试-身份证2"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-身份证"
      - formatType: 2
      - formatRule: ""
      - edgeScope: 0
      - length: 18
      - contentCode: "code5"
      - fontColor: "RED"
      - fontSize: 9
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]



- test:
    name: "TC2-setup_添加内容域 自动化测试-邮箱1"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-邮箱"
      - formatType: 3
      - formatRule: ""
      - edgeScope: 0
      - length: 18
      - contentCode: "code6"
      - pageNo: 1
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup_添加内容域 自动化测试-邮箱2"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "自动化测试-邮箱"
      - formatType: 3
      - formatRule: ""
      - edgeScope: 0
      - length: 15
      - contentCode: "code6"
      - pageNo: 2
      - posX: 34
      - posY: 603
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

#用该模板文件新建业务模板
- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "setup-业务模板配置-第一步：填写基本信息（关联pdf模板）"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId
      allowAddFile: 1
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuidCommon
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "setup-业务模板配置-第三步：签署方设置（设置签署方）,并启动-指定内部个人签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 1
      allowAddSealer: 1
      signerList: null

#- test:
#    name: "setup-管理其他签署方"
#    api: api/esignDocs/businessPreset/updateFillingUsers.yml
#    variables:
#      - presetId_updateFillingUsers: $testPresetId
#      - fillingList_updateFillingUsers:
#          - name: "3TC-内部填写方1"
#            userTerritory: 1
#            key: ${get_snowflake()}
#          - name: "3TC-内部填写方2"
#            userTerritory: 1
#            key: ${get_snowflake()}
#    extract:
#      signerId0: content.data.fillingList.0.signerId
#      signerName0: content.data.fillingList.0.name
#      signerId1: content.data.fillingList.1.signerId
#      signerName1: content.data.fillingList.1.name
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.data.presetId, $testPresetId]

- test:
    name: "setup-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
    api: api/esignDocs/businessPreset/templateDetail.yml
    variables:
      - presetId: $testPresetId
      - templateId: $newTemplateUuidCommon
      - version: $newVersion1
    extract:
      templateContentId0: content.data.contentList.0.templateContentId
      templateContentName0: content.data.contentList.0.contentName
      templateContentId1: content.data.contentList.1.templateContentId
      templateContentName1: content.data.contentList.1.contentName
#      signerId0: content.data.contentList.0.contentSourceTypeList.2.signerList.0.signerId #其他-填写方
#      signerId1: content.data.contentList.0.contentSourceTypeList.3.signerList.0.signerId #其他-填写方
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.templateId, $newTemplateUuidCommon]

- test:
    name: "setup-业务模板配置的第4步：设置签署方填写信息"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
      - templateList:
          - templateId: $newTemplateUuidCommon
            version: $newVersion1
            templateName: $commonTemplateName
            contentList: []
#              - contentId: $templateContentId0
#                contentName: $templateContentName0
#                contentSource:  0 #发起方填写方填写
##                signerId: $signerId0
##                signerName: $signerName0
#                contentSourceType: 0
#              - contentId: $templateContentId1
#                contentName: $templateContentName1
#                contentSource: 0 #其他填写方填写
##                signerId: $signerId1
##                signerName: $signerName1
#                contentSourceType: 0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data",null ]

#通过业务模板发起
- test:
    name: TC1-业务模板发起-发起方填写-startMode=1
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - initiatorInfoCBBT:
            userCode: "$userCode0"
            userType: 1
      - subject: "TC1-业务模板发起-发起方填写"
      - autoFillAndSubmit: 0
      - editComponentValue: 1
      - signerInfosCBBT: []
      - startMode: 1
      - fillingUserInfosCBBT: []

    extract:
      signFlowId001: content.data.signFlowId
      fillingUserId: content.data.fillingUserInfos.0.signerId

    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "发起人填写页查询详情"
    api: api/esignDocs/docGatherForm/querySignerFillValue.yml
    variables:
      - templateInitiationSignersUuid: $signFlowId001
      - templateInitiationUuid:  $fillingUserId
    extract:
      - signerContents1: content.data.signerContents
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200 ]
      - eq: [content.success, true ]
      - eq: [content.data.currentUserIsFiller, 1]
      - eq: [content.data.hasSubmit, 0]
      - eq: ["${dataArraySort($signerContents1, formatType,0, length)}", 11]
      - eq: ["${dataArraySort($signerContents1, formatType,1, length)}", 18]
      - eq: ["${dataArraySort($signerContents1, formatType,2, length)}", 15]
      - length_equals: [content.data.signerContents, 6]

#提交填写
- test:
    name: "发起方填写，必填项未填"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: $signFlowId001
      hasSubmit: 1
      signerContents: $signerContents1
      templateInitiationUuid:  $fillingUserId
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [content.status, 1604023]
      - eq: [content.success, false]
      - contains: [content.message, '必填，请填写']
      - eq: [content.data, null]

#提交填写
- test:
    name: "发起方填写，必填项未填"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: $signFlowId001
      hasSubmit: 1
      signerContents: $signerContents1
      templateInitiationUuid:  $fillingUserId
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [content.status, 1604023]
      - eq: [content.success, false]
      - contains: [content.message, '必填，请填写']
      - eq: [content.data, null]

- test:
    name: TC1-业务模板发起-发起方填写-有预填项
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - initiatorInfoCBBT:
            userCode: "$userCode0"
            userType: 1
      - subject: "TC1-业务模板发起-发起方填写"
      - autoFillAndSubmit: 0
      - editComponentValue: 1
      - signerInfosCBBT: []
      - startMode: 1
      - fillingUserInfosCBBT: [
        {
           "fillingUserType": 0,
           "signerId": "",
           "contentsControl": [
             {
               contentCode: "code1",
               contentValue: "27822" },
             {
               contentCode: "code3",
               contentValue: "2025年06月06日"
             },{
               contentCode: "code2",
               contentValue: "278222"
             },{
               contentCode: "code4",
               contentValue: "921118767898765678"
             },{
               contentCode: "code5",
               contentValue: "120223197001012930"
             },{
               contentCode: "code6",
               contentValue: "<EMAIL>"
             }
           ]
        }
      ]
    extract:
      signFlowId002: content.data.signFlowId
      fillingUserId2: content.data.fillingUserInfos.0.signerId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "发起人填写页查询详情"
    api: api/esignDocs/docGatherForm/querySignerFillValue.yml
    variables:
      - templateInitiationSignersUuid: $signFlowId002
      - templateInitiationUuid:  $fillingUserId2
    extract:
      - signerContents2: content.data.signerContents
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200 ]
      - eq: [content.success, true ]
      - eq: [content.data.currentUserIsFiller, 1]
      - eq: [content.data.hasSubmit, 0]
      - eq: ["${dataArraySort($signerContents2, formatType,0, length)}", 11]
      - eq: ["${dataArraySort($signerContents2, formatType,1, length)}", 18]
      - eq: ["${dataArraySort($signerContents2, formatType,2, length)}", 15]
      - eq: ["${dataArraySort($signerContents2, formatType,0, contentValue)}", "278222"]
      - eq: ["${dataArraySort($signerContents2, formatType,1, contentValue)}", "120223197001012930"]
      - eq: ["${dataArraySort($signerContents2, formatType,2, contentValue)}", "<EMAIL>"]
      - eq: ["${dataArraySort($signerContents2, formatType,3, contentValue)}", "921118767898765678"]
      - eq: ["${dataArraySort($signerContents2, formatType,4, contentValue)}", "27822"]
      - eq: ["${dataArraySort($signerContents2, formatType,5, contentValue)}", "2025年06月06日"]
      - length_equals: [content.data.signerContents, 6]
- test:
    name: "发起方填写，必填都填了"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: $signFlowId002
      hasSubmit: 1
      signerContents: $signerContents2
      templateInitiationUuid:  $fillingUserId2
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, '成功']
      - ne: [content.data, null]
