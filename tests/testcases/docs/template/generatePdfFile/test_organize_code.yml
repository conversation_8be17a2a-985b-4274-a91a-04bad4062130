#openapi填写模板并转为pdf-模版id
- config:
    variables:
        docRandomNo: ${get_randomNo()}
        autoTestDocUuid1: ${get_docConfig_type()}
        FileName: "testppp.pdf"
        fileKey: ${ENV(fileKey)}
        commonTemplateName: "自动化测试模版-合同测试"
        description: "自动化测试描述"
        page: 1
        size: 5
        contentNameyp1: "自动化测试内容域$docRandomNo"
        contentCodeyp1: "zdhcsypcode$docRandomNo"
        contentNameyp2: "自动化测试内容域2$docRandomNo"
        contentCodeyp2: "zdhcsypcode2$docRandomNo"
        contentNameyp3: "自动化测试内容域3$docRandomNo"
        contentCodeyp3: "zdhcsypcode3$docRandomNo"
        contentValue1: "88888888-8"
        dataSource: null
        sourceField: null
        font: "SimSun"
        fontSize: 14
        fontColor: "BLACK"
        fontStyle: "Normal"
        textAlign: "Left"
        formatType: 5
        formatRule: 100
        length: 100
        required: 0
        pageNo: 1
        posX: 34
        posY: 603
        width: 216
        height: 36
        spaceChar: "   "
        timePosX: 10
        timePosY: 10
        signNameA: "甲方企业"

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-添加内容域"
    api: api/esignDocs/content_domain/add.yml
    variables:
      - description: null
      - contentName: $contentNameyp1
      - contentCode: $contentCodeyp1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-分页查询"
    api: api/esignDocs/content_domain/list.yml
    variables:
      - contentName: $contentNameyp1
      - page: $page
      - size: $size
    extract:
      - contentUuid1: content.data.list.0.domainId
    validate:
      - len_ge: ["content.data.list", 1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-编辑模板内容/添加内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - contentUuid: $contentUuid1
      - contentCode: $contentCodeyp1
      - contentName: $contentNameyp1
      - edgeScope: 0
    extract:
      - templateContentUuid1: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup_添加签名区1"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: $signNameA
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-正确格式的组织机构代码-纯数字"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: $contentValue1
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: $contentValue1
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC2-正确格式的组织机构代码-包含英文字母"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: "8888888y-8"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: "8888888y-8"
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC3-错误格式的组织机构代码"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: "888888888"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: "888888888"
                } ] }
    validate:
      - contains: [ "content.message","格式填写错误，【组织机构代码】校验不通过" ]
      - eq: [ "content.code",1601009 ]
      - eq: [ "content.data",null ]

- test:
    name: "setup-删除内容域1"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $contentUuid1 }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]