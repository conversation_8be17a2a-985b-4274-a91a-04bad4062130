- config:
    name: "校验文本内容域长度不同，其他属性相同，调用openapi合成pdf时合并填写"
    variables:
      templateFileKey: ${ENV(fileKey)}
      autoTestDocUuid1: ${get_docConfig_type()}
      docRandomNo: ${get_randomNo()}
      contentNameyp1: "自动化测试内容域$docRandomNo"
      contentCodeyp1: "zdhcsypcode$docRandomNo"
      contentFieldId1: "${get_randomStr_32()}"
      description: "自动化测试添加内容域"
      contentUuid:
      contentValue1: "自动化测试合并填写文本"

- test:
    name: "setup-新建pdf文档模板"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      fileKey: $templateFileKey
      docUuid: $autoTestDocUuid1
    extract:
      newTemplateUuid1: content.data.templateUuid
      newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
                {
          "fieldId": "",
          "label": "自动化测试文本-0525",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "文本测试",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "1",
            "minFontSize": "10",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId1,
          "bizId": $contentFieldId1,
          "fieldKey": $contentCodeyp1,
          "fillGroupKey": "",
          "fieldValue": "文本测试",
          "defaultValue": "文本测试",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
        {
          "fieldId": "",
          "label": "自动化测试文本-0525",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "文本测试",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "10",
            "overflowType": "1",
            "minFontSize": "10",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId1,
          "bizId": $contentFieldId1,
          "fieldKey": $contentCodeyp1,
          "fillGroupKey": "",
          "fieldValue": "文本测试",
          "defaultValue": "文本测试",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

#- test:
#    name: "setup-编辑模板内容/添加内容域-文本类型15个字"
#    api: api/esignDocs/template/owner/contentAdd.yml
#    variables:
#      templateUuid: $newTemplateUuid1
#      version: $newVersion1
#      contentCode: $contentCodeyp1
#      contentName: $contentNameyp1
#      contentUuid:
#      length: 15
#      height: 28.8
#      pageNo: 1
#      posX: 326
#      posY: 563
#      width: 244.8
#    extract:
#      templateContentUuid1: content.data.list.0
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-编辑模板内容/添加内容域-文本类型18个字"
#    api: api/esignDocs/template/owner/contentAdd.yml
#    variables:
#      templateUuid: $newTemplateUuid1
#      version: $newVersion1
#      contentCode: $contentCodeyp1
#      contentName: $contentNameyp1
#      contentUuid:
#      length: 18
#      height: 28.8
#      pageNo: 1
#      posX: 282
#      posY: 504
#      width: 288.8
#    extract:
#      - templateContentUuid2: content.data.list.0
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "setup-发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-文本类型内容域长度不同其他属性相同-合并填写-generatePdfFile"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      templateId: $newTemplateUuid1
      fileName: "自动化测试合并填写"
      contentId:
      contentCode: $contentCodeyp1
      contentValue: $contentValue1
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试合并填写.pdf" ]