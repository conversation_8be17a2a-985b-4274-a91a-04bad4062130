- config:
    variables:
      autoTestDocUuid1: ${get_docConfig_type()}
      docFileName: "word-测试模版获取控件.docx"
      docFileKey: ${attachment_upload($docFileName)}
      htmlFileName: "word-测试模板获取控件.html"
      htmlFileKey: ${attachment_upload($htmlFileName)}
      templateNameCommon: "自动化测试word模板${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      space: " "
      longContentValue: "自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项自动化测试单选选项啦"


#----------新建模板1--------------

- test:
    name: "TC1-模版新建1"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $docFileKey
      - zipFileKey: null
      - templateName: "1$templateNameCommon"
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon1: content.data.templateUuid
      - newVersionCommon1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-发布模板1"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "单选控件编码",
            "contentName": "单选控件名称",
            "dataSource": "",
            "sourceField": "",
            "description": "单选控件说明",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "1",
              "2",
              "3"
            ],
            "required": 0,
            "thirdKey": "ele-1655890790080"
          }
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655891903315",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon1,
        "version": $newVersionCommon1,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC3-查看单选控件的接口返回值"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuidCommon1
    extract:
      - contentCode1: content.data.contentsControl.0.contentCode
      - contentId1: content.data.contentsControl.0.contentId
    validate:
      - eq: [ "content.data.contentsControl.0.contentName", "单选控件名称" ]
      - eq: [ "content.data.contentsControl.0.contentFormat", "SINGLE" ]
      - eq: [ "content.data.contentsControl.0.required", "0" ]
      - eq: [ "content.data.contentsControl.0.contentCode", "单选控件编码" ]
      - eq: [ "content.code",200 ]


#------content为单选下拉框_并设置格式----------1-----
- test:
    name: "TC4-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon1
      - fileName: "自动化测试文件"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: "3"

    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuidCommon1]


#------content为单选下拉框_contentValue非选项----------2-----
- test:
    name: "TC5-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon1
      - fileName: "自动化测试文件"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: "4"

    validate:
      - contains: [ "content.message","格式填写错误，【单选】校验不通过格式填写错误，【单选】校验不通过选项值错误，选项值为【1｜2｜3】中的任意一个" ]
      - eq: [ "content.code",1601009 ]


#---content为单选下拉框_contentValue为选项且包含首尾空格------3-------
- test:
    name: "TC6-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon1
      - fileName: "自动化测试文件"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: " 3 "

    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuidCommon1]


#-----content为单选下拉框且必填_contentValue为空--------4-----
- test:
    name: "TC7-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon1
      - fileName: "自动化测试文件"
      - contentId: $contentId1
      - contentCode: $contentCode1
      - contentValue: null

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]



#----------新建模板2--------------
- test:
    name: "TC8-模版新建2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $docFileKey
      - zipFileKey: null
      - templateName: "2$templateNameCommon"
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon2: content.data.templateUuid
      - newVersionCommon2: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-发布模板2"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "单选控件编码2",
            "contentName": "单选控件名称",
            "dataSource": "",
            "sourceField": "",
            "description": "单选控件说明",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "1",
              "2",
              "3"
            ],
            "required": 0,
            "thirdKey": "ele-1655890790080"
          }
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655891903315",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon2,
        "version": $newVersionCommon2,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC10-查看单选控件的接口返回值"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuidCommon2
    extract:
      - contentCode2: content.data.contentsControl.0.contentCode
      - contentId2: content.data.contentsControl.0.contentId
    validate:
      - eq: [ "content.data.contentsControl.0.contentName", "单选控件名称" ]
      - eq: [ "content.data.contentsControl.0.contentFormat", "SINGLE" ]
      - eq: [ "content.data.contentsControl.0.required", "0" ]
      - eq: [ "content.data.contentsControl.0.contentCode", "单选控件编码2" ]
      - eq: [ "content.code",200 ]


#-----content为单选下拉框且非必填_contentValue为空------5------todo:代码bug待修-----
#- test:
#    name: "TC11-填写模板并转为pdf"
#    api: api/esignDocs/documents/template/generatePdfFile.yml
#    variables:
#      - templateId: $newTemplateUuidCommon2
#      - fileName: "自动化测试文件"
#      - contentId: $contentId2
#      - contentCode: $contentCode2
#      - contentValue: null
#    validate:
#      - len_gt: [ "content.data.fileKey",0 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - eq: [ "content.data.templateId",$newTemplateUuidCommon2]


#----------新建模板2--------------
- test:
    name: "TC12-模版新建3"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $docFileKey
      - zipFileKey: null
      - templateName: "3$templateNameCommon"
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon3: content.data.templateUuid
      - newVersionCommon3: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC13-发布模板3"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "单选控件编码3",
            "contentName": "单选控件名称",
            "dataSource": "",
            "sourceField": "",
            "description": "单选控件说明",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              $longContentValue
            ],
            "required": 0,
            "thirdKey": "ele-1655890790080"
          }
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655891903315",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon3,
        "version": $newVersionCommon3,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC14-查看单选控件的接口返回值"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuidCommon3
    extract:
      - contentCode3: content.data.contentsControl.0.contentCode
      - contentId3: content.data.contentsControl.0.contentId
    validate:
      - eq: [ "content.data.contentsControl.0.contentName", "单选控件名称" ]
      - eq: [ "content.data.contentsControl.0.contentFormat", "SINGLE" ]
      - eq: [ "content.data.contentsControl.0.contentCode", "单选控件编码3" ]
      - eq: [ "content.code",200 ]


#-----content为单选下拉框且非必填_contentValue最100字的超长选项---------
- test:
    name: "TC15-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon3
      - fileName: "自动化测试文件"
      - contentId: $contentId3
      - contentCode: $contentCode3
      - contentValue: $longContentValue

    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuidCommon3]


#-------模版新建4------------------
- test:
    name: "TC16-模版新建4"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $docFileKey
      - zipFileKey: null
      - templateName: "4$templateNameCommon"
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon4: content.data.templateUuid
      - newVersionCommon4: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC17-发布模板4"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "单选控件编码",
            "contentName": "单选控件名称",
            "dataSource": "",
            "sourceField": "",
            "description": "单选控件说明",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "1",
              "2",
              "3"
            ],
            "required": 0,
            "thirdKey": "ele-1655890790080"
          },
          {
            "contentCode": "单选控件编码2",
            "contentName": "单选控件名称2",
            "dataSource": "",
            "sourceField": "",
            "description": "单选控件说明",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "1",
              "2",
              "3"
            ],
            "required": 0,
            "thirdKey": "ele-1655890790080"
          }
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655891903315",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon4,
        "version": $newVersionCommon4,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC18-查看单选控件的接口返回值"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuidCommon4
    extract:
      - contentCode4: content.data.contentsControl.0.contentCode
      - contentId4: content.data.contentsControl.0.contentId
    validate:
      - eq: [ "content.data.contentsControl.0.contentName", "单选控件名称" ]
      - eq: [ "content.data.contentsControl.0.contentFormat", "SINGLE" ]
      - eq: [ "content.data.contentsControl.0.required", "0" ]
      - eq: [ "content.data.contentsControl.0.contentCode", "单选控件编码" ]
      - eq: [ "content.code",200 ]



- test:
    name: "TC19-填写模板并转为pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon4
      - fileName: "自动化测试文件"
      - contentId: $contentId4
      - contentCode: $contentCode4
      - contentValue: "1"

    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuidCommon4]



