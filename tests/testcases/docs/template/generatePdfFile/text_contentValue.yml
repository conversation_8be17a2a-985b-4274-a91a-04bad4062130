#openapi填写模板并转为pdf-模版id
- config:
    variables:
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      contentNameyp1: "自动化测试内容域$docRandomNo"
      contentCodeyp1: "zdhcsypcode$docRandomNo"
      contentNameyp2: "自动化测试内容域2$docRandomNo"
      contentCodeyp2: "zdhcsypcode2$docRandomNo"
      contentValue1: "填充测试"
      contentFieldId001: "${get_randomStr_32()}"
      contentFieldId002: "${get_randomStr_32()}"
      spaceChar: "   "

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-模版中内容域个数为0个"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: $contentCodeyp1
      - contentValue: $contentValue1
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: null,
                  contentCode: $contentCodeyp1,
                  contentValue: $contentValue1
                } ] }
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

- test:
    name: "setup_模版新建2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试文本-0525",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "文本测试",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "1",
            "minFontSize": "10",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCodeyp1,
          "fillGroupKey": "",
          "fieldValue": "文本测试",
          "defaultValue": "文本测试",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },{
          "fieldId": "",
          "label": "自动化测试文本-0525",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 58.22,
            "height": 37.67,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "文本测试",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "1",
            "minFontSize": "10",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "COMMON",
          "contentFieldId": $contentFieldId002,
          "bizId": $contentFieldId002,
          "fieldKey": $contentCodeyp1,
          "fillGroupKey": "",
          "fieldValue": "文本测试",
          "defaultValue": "文本测试",
          "position": {
            "x": 200,
            "y": 200,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid2
    extract:
      - templateContentUuid1: content.data.contentsControl.0.contentId
      - contentCodeyp1: content.data.contentsControl.0.contentCode
      - contentName1: content.data.contentsControl.0.contentName
      - templateContentUuid2: content.data.contentsControl.1.contentId
      - contentCodeyp2: content.data.contentsControl.1.contentCode
      - contentName2: content.data.contentsControl.1.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid2]

- test:
    name: "TC2-模版中存在重复的内容域"
    api: api/esignDocs/documents/template/generatePdfFile2.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId_generatePdfFile2: $templateContentUuid1
      - contentCode_generatePdfFile2: $contentCodeyp1
      - contentValue_generatePdfFile2: $contentValue1
      - contentId2_generatePdfFile2: $templateContentUuid2
      - contentCode2_generatePdfFile2: $contentCodeyp2
      - contentValue2_generatePdfFile2: $contentValue1
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: $contentValue1
                },{
                  contentId: $templateContentUuid2,
                  contentCode: $contentCodeyp2,
                  contentValue: $contentValue1
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid2 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC3-通用规则_字段值首尾空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: $spaceChar$contentValue1$spaceChar
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: $spaceChar$contentValue1$spaceChar
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid2 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC4-模版中对应内容域实例的内容域已被删除"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: $contentValue1
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: $contentValue1
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid2 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-停用2"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板2"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
