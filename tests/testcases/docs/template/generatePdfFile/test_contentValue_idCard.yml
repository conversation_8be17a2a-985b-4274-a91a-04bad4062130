#openapi填写模板并转为pdf-模版id
- config:
    variables:
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      contentNameyp1: "自动化测试内容域$docRandomNo"
      contentCodeyp1: "zdhcsypcode$docRandomNo"
      contentFieldId001: "${get_randomStr_32()}"
      contentValue1: "320722198804250011"

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-身份证内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试身份证号-0525",
          "custom": false,
          "type": "ID_CARD",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 16,
            "textColor": "#2969B0",
            "width": 436,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": ""
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-id-card",
              "fastCheck": null,
              "addSealRule": "",
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCodeyp1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00780234070223,
            "y": "674.90",
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - templateContentUuid1: content.data.contentsControl.0.contentId
      - contentCodeyp1: content.data.contentsControl.0.contentCode
      - contentName1: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC1-正确格式的身份证号"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: $contentValue1
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: $contentValue1
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC2-正确格式的身份证号-包含尾字母"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: "43242718940609551x"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: "43242718940609551x"
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC3-错误格式的身份证号"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $templateContentUuid1
      - contentCode: $contentCodeyp1
      - contentValue: "29938833"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $templateContentUuid1,
                  contentCode: $contentCodeyp1,
                  contentValue: "29938833"
                } ] }
    validate:
      - contains: [ "content.message","失败原因: 控件'$contentName1'身份证号码填写有误,请检查!" ]
      - eq: [ "content.code",1601009 ]
      - eq: [ "content.data",null ]

- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]