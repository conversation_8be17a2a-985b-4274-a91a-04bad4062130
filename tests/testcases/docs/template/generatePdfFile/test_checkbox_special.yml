- config:
    name: "openapi填写模板并转换为pdf，多选必填测试"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"

- test:
    name: "创建企业模板，特殊多选"
    testcase: common/template/wordTemplate_spi_checkbox.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "content为多选下拉框且非必填_contentValue为100个选项分割_测试用例写错了应该是100"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "100个选项"
      - contentValue: "[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\"21\",\"22\",\"23\",\"24\",\"25\",\"26\",\"27\",\"28\",\"29\",\"30\",\"31\",\"32\",\"33\",\"34\",\"35\",\"36\",\"37\",\"38\",\"39\",\"40\",\"41\",\"42\",\"43\",\"44\",\"45\",\"46\",\"47\",\"48\",\"49\",\"50\",\"51\",\"52\",\"53\",\"54\",\"55\",\"56\",\"57\",\"58\",\"59\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\"]"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "content为多选下拉框且非必填_contentValue为100个选项分割_测试用例写错了应该是100"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "超长选项"
      - contentValue: "[\"1红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红\",\"2红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳\"]"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
