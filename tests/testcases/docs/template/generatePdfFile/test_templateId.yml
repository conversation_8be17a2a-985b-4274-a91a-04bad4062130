#openapi填写模板并转为pdf-模版id
- config:
    variables:
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      FileName: "testppp.pdf"
      largerFileName: "testfile.pdf"
      fileKey: ${ENV(fileKey)}
      largerFileKey: ${common_upload($largerFileName)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      contentName: "自动化测试内容域2$docRandomNo"
      contentCode: "zdhcsypcode2$docRandomNo"
      contentValue: "填充测试"
      spaceChar: "   "
      contentFieldId001: ${get_randomNo_32()}
      fillContent001: {
        "fieldId": "",
        "label": "自动化测试-文本0507",
        "custom": false,
        "type": "MULTILINE_TEXT",
        "subType": null,
        "sort": 1,
        "formula": null,
        "style": {
          "font": 1,
          "fontSize": 16,
          "textColor": "#2969B0",
          "width": 272.01,
          "height": 37.67,
          "bold": false,   #true
          "italic": false,
          "underLine": false,
          "lineThrough": false,
          "leadingRate": 1,
          "verticalAlignment": "TOP",
          "horizontalAlignment": "LEFT",
          "styleExt": {
            "signDatePos": null,
            "units": "px",
            "imgType": null,
            "usePageTypeGroupId": "",
            "hideTHeader": null,
            "selectLayout": null,
            "borderWidth": 1,
            "borderColor": "#000",
            "keyword": "",
            "groupKey": "",
            "tickOptions": null,
            "elementId": "",
            "posKey": ""
          }
        },
        "settings": {
          "defaultValue": "文本-COMMON-001",
          "required": true,
          "dateFormat": null,
          "validation": {
            "type": "REGEXP",
            "pattern": ""
          },
          "selectableDataSource": [ ],
          "numberFormat": {
            "integerDigits": null,
            "fractionDigits": null,
            "thousandsSeparator": ""
          },
          "editable": true,
          "encryptAlgorithm": "",
          "fillLengthLimit": 54,
          "overflowType": 1,
          "minFontSize": 10,
          "remarkInputType": null,
          "content": null,
          "remarkAICheck": null,
          "dateRule": null,
          "tickOptions": null,
          "configExt": {
            "cooperationerSubjectType": "",
            "icon": "epaas-icon-multiline",
            "fastCheck": null,
            "addSealRule": "",
            "ext": "{}",
            "version": null,
            "mergeId": null
          },
          "sealTypes": [ ],
          "positionMovable": null
        },
        "options": null,
        "instructions": "COMMON",
        "contentFieldId": $contentFieldId001,
        "bizId": $contentFieldId001,
        "fieldKey": $contentCode,
        "fillGroupKey": "",
        "fieldValue": "文本-COMMON-001",
        "defaultValue": "文本-COMMON-001",
        "position": {
          "x": 186.00780234070223,
          "y": "674.90",
          "page": 1,
          "scope": "default",
          "intervalType": null
        },
        "formField": false
      }

- test:
    name: "TC1-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-模版id不填_直接提交"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: null
      - fileName: "自动化测试延平"
      - contentId: 1
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": null,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: 1,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","模板id不能为空" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC1-删除模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-模版id对应的模版状态为已删除"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平"
      - contentId: 1
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: 1,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","企业模板不存在。" ]
      - eq: [ "content.code",1605001 ]

- test:
    name: "TC3-模版新建2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-模版id对应的模版状态为草稿"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId: 1
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: 1,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","该模板并未发布" ]
      - eq: [ "content.code",1605011 ]

- test:
    name: "TC3-发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-停用"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-模版id对应的模版状态为停用"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId: 1
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: 1,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","该模板并未发布" ]
      - eq: [ "content.code",1605011 ]

- test:
    name: "TC5-模版新建3"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid3: content.data.templateUuid
      - newVersion3: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
    extract:
      - editUrl3: content.data.editUrl
      - previewUrl3: content.data.previewUrl
      - templateName3: content.data.templateName
      - autoTestDocUuid3: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl3)}
      tmp_common_template3: ${putTempEnv(_tmp_common_template3, $tplToken_config)}
    extract:
      - contentId3: content.data.contents.0.id
      - entityId3: content.data.contents.0.entityId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template3)}
      contentId_detail: $contentId3
      entityId_detail: $entityId3
    extract:
      - baseFile3: content.data.baseFile
      - originFile3: content.data.originFile
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template3)}
      baseFile_draft: $baseFile3
      originFile_draft: $originFile3
      fields_draft: [ $fillContent001 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName3
      contentId_draft: $contentId3
      entityId_draft: $entityId3
    extract:
      - contentId3: content.data.0.contentId
      - contentVersionId3: content.data.0.contentVersionId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC5-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid3
    extract:
      - contentId3: content.data.contentsControl.0.contentId
      - contentCode3: content.data.contentsControl.0.contentCode
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid3]

- test:
    name: "TC5-模版id对应的模版状态为发布_其他字段信息填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid3
      - fileName: "自动化测试延平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid3,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid3 ]

- test:
    name: "TC6-模版id对应的模版状态为发布_模版id前后有空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $spaceChar$newTemplateUuid3$spaceChar
      - fileName: "自动化测试延平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $spaceChar$newTemplateUuid3$spaceChar,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid3 ]

- test:
    name: "TC4-删除模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-停用"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-删除模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-setup_模版新建4-大文件"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $largerFileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid4: content.data.templateUuid
      - newVersion4: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC7-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - version: $newVersion4
    extract:
      - editUrl4: content.data.editUrl
      - previewUrl4: content.data.previewUrl
      - templateName4: content.data.templateName
      - autoTestDocUuid4: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl4)}
      tmp_common_template4: ${putTempEnv(_tmp_common_template4, $tplToken_config)}
    extract:
      - contentId4: content.data.contents.0.id
      - entityId4: content.data.contents.0.entityId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template4)}
      contentId_detail: $contentId4
      entityId_detail: $entityId4
    extract:
      - baseFile4: content.data.baseFile
      - originFile4: content.data.originFile
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC7-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template4)}
      baseFile_draft: $baseFile4
      originFile_draft: $originFile4
      fields_draft: [ $fillContent001 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName4
      contentId_draft: $contentId4
      entityId_draft: $entityId4
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC7-发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - version: $newVersion4
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid4
    extract:
      - contentId4: content.data.contentsControl.0.contentId
      - contentCode4: content.data.contentsControl.0.contentCode
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid4]

- test:
    name: "TC7-模版id对应的模版状态为发布_其他字段信息填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid4
      - fileName: "自动化测试延平"
      - contentId: $contentId4
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid4,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId4,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid4 ]

- test:
    name: "TC7-停用"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - version: $newVersion4
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-删除模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid4
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
