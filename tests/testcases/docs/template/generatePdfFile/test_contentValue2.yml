- config:
    name: "线上问题都是必填-https://forward.esign.cn/mainBugManagement/edit?id=21185&type=check"
    variables:
      fileKey: ${ENV(fileKey)}
      docRandomNo: ${get_randomNo()}
      templateNameCommon: "自动化测试通用模版-内容域大全-${get_randomNo_16()}"
      description: "自动化测试描述"
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
      contentCode001: "zdhcsypcode$docRandomNo"
      contentFieldId001: "${get_randomStr_32()}"
      contentFieldId002: "${get_randomStr_32()}"

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-数字内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
        {
          "fieldId": "",
          "label": "自动化测试-数字",
          "custom": false,
          "type": "NUM",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": "0",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": 0,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "7",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-number",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 163.5195024490848,
            "y": 676.5772557360144,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-数字",
            "custom": false,
            "type": "NUM",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "10",
              "fontSize": 18,
              "textColor": "#FBA026",
              "width": 67.31,
              "height": 22,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "CENTER",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": "0",
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": 0,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "7",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-number",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 248.61716937354987,
              "y": 562.2661633587154,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#查询模板控件
- test:
    name: "TC4-查询模板控件内容-数字"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId1: content.data.contentsControl.0.contentId
      - contentName1: content.data.contentsControl.0.contentName
      - contentCode1: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "数字控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "2333",
          "contentCode": "$contentCode1"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-文本内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-文本",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 162.67543181232276,
            "y": 710.2086977741096,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-文本",
            "custom": false,
            "type": "TEXT",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "4",
              "fontSize": 15,
              "textColor": "#61BD6D",
              "width": 160,
              "height": 18,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "CENTER",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "13",
              "overflowType": "1",
              "minFontSize": "12",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-text",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 196.05458881154934,
              "y": 581.4274748646558,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#查询模板控件
- test:
    name: "TC4-查询模板控件内容-文本"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId2: content.data.contentsControl.0.contentId
      - contentName2: content.data.contentsControl.0.contentName
      - contentCode2: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "文本控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "83999",
          "contentCode": "$contentCode2"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-日期内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-日期",
          "custom": false,
          "type": "DATE",
          "subType": null,
          "sort": 3,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 294,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "units": "px",
              "imgType": null,
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "groupKey": "",
              "tickOptions": null,
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": "yyyy-MM-dd",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": null,
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-date",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "signDatePosition": null
          },
          "options": null,
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 181.9356,
            "y": 673.50793,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-日期",
            "custom": false,
            "type": "DATE",
            "subType": null,
            "sort": 4,
            "style": {
              "font": "10",
              "fontSize": 12,
              "textColor": "#EB6B56",
              "width": 84,
              "height": 15,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "groupKey": "",
                "tickOptions": null,
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": "yyyy-MM-dd",
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-date",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "signDatePosition": null
            },
            "options": null,
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 323.50925,
              "y": 573.5556,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-日期"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId3: content.data.contentsControl.0.contentId
      - contentName3: content.data.contentsControl.0.contentName
      - contentCode3: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "日期控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "2025-03-25",
          "contentCode": "$contentCode3"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]


#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-文本内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-统一社会信用代码",
          "custom": false,
          "type": "UNIFY_THE_SOCIAL_CREDIT_CODE",
          "subType": null,
          "sort": 4,
          "formula": null,
          "style": {
            "font": "5",
            "fontSize": 10.5,
            "textColor": "#B8312F",
            "width": 160,
            "height": 14,
            "bold": true,
            "italic": true,
            "underLine": true,
            "lineThrough": true,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "RIGHT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-uscc",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 330.8756896107244,
            "y": 594.8108226991492,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-统一社会信用代码",
            "custom": false,
            "type": "UNIFY_THE_SOCIAL_CREDIT_CODE",
            "subType": null,
            "sort": 5,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 42,
              "textColor": "#B8312F",
              "width": 559,
              "height": 49,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "18",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-uscc",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 36.299999999999955,
              "y": 679.7729247228666,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-统一社会信用代码"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId4: content.data.contentsControl.0.contentId
      - contentName4: content.data.contentsControl.0.contentName
      - contentCode4: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "统一社会信用代码控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "920030003030303030",
          "contentCode": "$contentCode4"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]


#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-身份证内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-身份证",
          "custom": false,
          "type": "ID_CARD",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "5",
            "fontSize": 24,
            "textColor": "#54ACD2",
            "width": 249,
            "height": 28,
            "bold": true,
            "italic": true,
            "underLine": true,
            "lineThrough": true,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "RIGHT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "18",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-id-card",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 140.19245939675173,
            "y": 508.37699793761277,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-身份证",
            "custom": false,
            "type": "ID_CARD",
            "subType": null,
            "sort": 3,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 42,
              "textColor": "#000",
              "width": 436,
              "height": 49,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "18",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-id-card",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 119.**************,
              "y": 660.742968548595,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-身份证"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId5: content.data.contentsControl.1.contentId
      - contentName5: content.data.contentsControl.1.contentName
      - contentCode5: content.data.contentsControl.1.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "身份证控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "130101195301012114",
          "contentCode": "$contentCode5"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-邮箱内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-邮箱",
          "custom": false,
          "type": "EMAIL",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 368.71,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "17",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-mail",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 123.38778035576178,
            "y": 681.9707076566125,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-邮箱",
            "custom": false,
            "type": "EMAIL",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "6",
              "fontSize": 18,
              "textColor": "#F37934",
              "width": 160,
              "height": 22,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "CENTER",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "17",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-mail",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 274.62989172467127,
              "y": 497.9412155194638,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-邮箱"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId6: content.data.contentsControl.0.contentId
      - contentName6: content.data.contentsControl.0.contentName
      - contentCode6: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "邮箱控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "<EMAIL>",
          "contentCode": "$contentCode6"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]


#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-手机号内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-手机号",
          "custom": false,
          "type": "PHONE_NUM",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 315,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "11",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-telephone",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 83.10259087393658,
            "y": 717.7065158546017,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-手机号",
            "custom": false,
            "type": "PHONE_NUM",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "2",
              "fontSize": 12,
              "textColor": "#00A885",
              "width": 90,
              "height": 15,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "RIGHT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "11",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-telephone",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 284.835473060067,
              "y": 603.0213279840165,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-手机号箱"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId7: content.data.contentsControl.0.contentId
      - contentName7: content.data.contentsControl.0.contentName
      - contentCode7: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "邮箱控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "19881403226",
          "contentCode": "$contentCode7"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-多行文本内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-多行文本",
          "custom": false,
          "type": "MULTILINE_TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 397.85,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "9",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-multiline",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 70.59499871100799,
            "y": 693.0203596287703,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-多行文本",
            "custom": false,
            "type": "MULTILINE_TEXT",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 18,
              "textColor": "#F37934",
              "width": 148,
              "height": 68.24,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "leadingRate": 3,
              "verticalAlignment": "MIDDLE",
              "horizontalAlignment": "RIGHT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "9",
              "overflowType": "1",
              "minFontSize": "15",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-multiline",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 282.9938643980407,
              "y": "538.11",
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-多行文本"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId8: content.data.contentsControl.0.contentId
      - contentName8: content.data.contentsControl.0.contentName
      - contentCode8: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "多行文本控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "测试多行文本",
          "contentCode": "$contentCode8"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-单选内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-单选",
          "custom": false,
          "type": "RADIO_BOX",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 235.31924465068317,
            "height": 187,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": "vertical",
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": null,
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-radio",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": [
            {
              "index": 0,
              "label": "选项一",
              "selected": false,
              "position": {
                "x": 15.319244650683174,
                "y": 128,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            },
            {
              "index": 1,
              "label": "选项二",
              "selected": false,
              "position": {
                "x": 10,
                "y": 69.8305529341435,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            },
            {
              "index": 2,
              "label": "选项三",
              "selected": false,
              "position": {
                "x": 15.319244650683174,
                "y": 10,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            }
          ],
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 104.97169373549883,
            "y": 609.6587925367362,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-单选",
            "custom": false,
            "type": "RADIO_BOX",
            "subType": null,
            "sort": 4,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#553982",
              "width": 431.1686465583913,
              "height": 42.344533062644985,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": "hortizontal",
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "",
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-radio",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": [
              {
                "index": 0,
                "label": "选项一",
                "selected": false,
                "position": {
                  "x": 10,
                  "y": 12.892541569992261,
                  "page": "1"
                },
                "style": {
                  "width": 128.97,
                  "height": 19.451991492652745
                }
              },
              {
                "index": 1,
                "label": "选项二",
                "selected": false,
                "position": {
                  "x": 307.54864655839134,
                  "y": 11.227739108017488,
                  "page": "1"
                },
                "style": {
                  "width": 113.62,
                  "height": 19.451991492652745
                }
              },
              {
                "index": 2,
                "label": "选项三",
                "selected": false,
                "position": {
                  "x": 152.8535189481825,
                  "y": 10,
                  "page": "1"
                },
                "style": {
                  "width": 129.58,
                  "height": 19.451991492652745
                }
              }
            ],
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 142.4177365300335,
              "y": 481.1394463779324,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-单选"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId9: content.data.contentsControl.0.contentId
      - contentName9: content.data.contentsControl.0.contentName
      - contentCode9: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "单选控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "选项二",
          "contentCode": "$contentCode9"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-多选内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-多选",
          "custom": false,
          "type": "CHECK_BOX",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 230,
            "height": 187,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": "vertical",
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": null,
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-multiple-choice",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true,
            "signDatePosition": null
          },
          "options": [
            {
              "index": 0,
              "label": "选项一",
              "selected": false,
              "position": {
                "x": 10,
                "y": 128,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            },
            {
              "index": 1,
              "label": "选项二",
              "selected": false,
              "position": {
                "x": 10,
                "y": 69,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            },
            {
              "index": 2,
              "label": "选项三",
              "selected": false,
              "position": {
                "x": 10,
                "y": 10,
                "page": "1"
              },
              "style": {
                "width": 210,
                "height": 49
              }
            }
          ],
          "instructions": "",
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 72.0529389017788,
            "y": 604.1502400747615,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-多选",
            "custom": false,
            "type": "CHECK_BOX",
            "subType": null,
            "sort": 4,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 15,
              "textColor": "#475577",
              "width": 393.14000000000004,
              "height": 40.45912122969838,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": "hortizontal",
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "",
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-multiple-choice",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null
            },
            "options": [
              {
                "index": 0,
                "label": "选项一",
                "selected": false,
                "position": {
                  "x": 137.92000000000002,
                  "y": 10,
                  "page": "1"
                },
                "style": {
                  "width": 118.53,
                  "height": 20.459121229698376
                }
              },
              {
                "index": 1,
                "label": "选项二",
                "selected": false,
                "position": {
                  "x": 10,
                  "y": 10,
                  "page": "1"
                },
                "style": {
                  "width": 117.92,
                  "height": 20.459121229698376
                }
              },
              {
                "index": 2,
                "label": "选项三",
                "selected": false,
                "position": {
                  "x": 266.45000000000005,
                  "y": 10,
                  "page": "1"
                },
                "style": {
                  "width": 116.69,
                  "height": 20.459121229698376
                }
              }
            ],
            "instructions": "",
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 66.29791183294662,
              "y": 485.4749001031193,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-多选"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId10: content.data.contentsControl.0.contentId
      - contentName10: content.data.contentsControl.0.contentName
      - contentCode10: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "多选控件转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "[\"选项二\",\"选项三\"]",
          "contentCode": "$contentCode10"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]

- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "TC1-epaasTemplate-batch-save-draft-下拉内容域-编码相同、长度相同、其他属性不同（字体、颜色、样式）"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft:
        [ {
          "fieldId": "",
          "label": "自动化测试-下拉选择",
          "custom": false,
          "type": "PULL_DOWN",
          "subType": null,
          "sort": 2,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "groupKey": "",
              "tickOptions": null,
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": true,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "3",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-select",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "signDatePosition": null
          },
          "options": [
            {
              "index": 0,
              "label": "选项一",
              "selected": false,
              "position": {
                "x": 0,
                "y": 50,
                "page": "1"
              },
              "style": {
                "width": 124,
                "height": 14
              }
            },
            {
              "index": 1,
              "label": "选项二",
              "selected": false,
              "position": {
                "x": 0,
                "y": 25,
                "page": "1"
              },
              "style": {
                "width": 124,
                "height": 14
              }
            },
            {
              "index": 2,
              "label": "选项三",
              "selected": false,
              "position": {
                "x": 0,
                "y": 0,
                "page": "1"
              },
              "style": {
                "width": 124,
                "height": 14
              }
            }
          ],
          "contentFieldId": "$contentFieldId001",
          "bizId": "$contentFieldId001",
          "fieldKey": "$contentCode001",
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 130.49652,
            "y": 686.52545,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
          {
            "fieldId": "",
            "label": "自动化测试-下拉选择",
            "custom": false,
            "type": "PULL_DOWN",
            "subType": null,
            "sort": 3,
            "style": {
              "font": "10",
              "fontSize": 22,
              "textColor": "#00A885",
              "width": 76.52,
              "height": 26,
              "bold": true,
              "italic": true,
              "underLine": true,
              "lineThrough": true,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "groupKey": "",
                "tickOptions": null,
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": null,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [ ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "3",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-select",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [ ],
              "columnMapping": null,
              "positionMovable": null,
              "signDatePosition": null
            },
            "options": [
              {
                "index": 0,
                "label": "选项一",
                "selected": false,
                "position": {
                  "x": 0,
                  "y": 50,
                  "page": "1"
                },
                "style": {
                  "width": 124,
                  "height": 14
                }
              },
              {
                "index": 1,
                "label": "选项二",
                "selected": false,
                "position": {
                  "x": 0,
                  "y": 25,
                  "page": "1"
                },
                "style": {
                  "width": 124,
                  "height": 14
                }
              },
              {
                "index": 2,
                "label": "选项三",
                "selected": false,
                "position": {
                  "x": 0,
                  "y": 0,
                  "page": "1"
                },
                "style": {
                  "width": 124,
                  "height": 14
                }
              }
            ],
            "contentFieldId": "$contentFieldId002",
            "bizId": "$contentFieldId002",
            "fieldKey": "$contentCode001",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 299.56833,
              "y": 617.8022,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          } ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-查询模板控件内容-下拉选项"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentId11: content.data.contentsControl.0.contentId
      - contentName11: content.data.contentsControl.0.contentName
      - contentCode11: content.data.contentsControl.0.contentCode
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "下拉选项控件转pdf-有bug"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: [
        {
          "contentValue": "选项二",
          "contentCode": "$contentCode11"
        }
      ]
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.data.pdfPreviewUrl","http" ]


#查询模板控件
- test:
    name: "TC4-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    extract:
      - contentsControl1: content.data.contentsControl
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
      - ne: [ content.data.contentsControl, null ]

#填写模板转为pdf
- test:
    name: "转pdf"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "测试"
      - contentsControl: $contentsControl1
    validate:
      - contains: [ "content.message","没有填写" ]
      - eq: [ "content.code",1605046 ]
      - eq: [ "content.data",null ]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板2"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]