- config:
    name: "openapi填写模板并转换为pdf，多选必填测试"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"

- test:
    name: "创建企业模板，带多选和文本，多选必填"
    testcase: common/template/wordTemplate_checkbox_req.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "content为多选下拉框且必填_contentValue为空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "text"
      - contentValue: "文本填充"
    validate:
      - eq: [ "content.message","【多选】没有填写" ]
      - eq: [ "content.code",1605046 ]
