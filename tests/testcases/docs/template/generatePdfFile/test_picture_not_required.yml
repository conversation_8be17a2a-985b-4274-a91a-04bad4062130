- config:
    name: "openapi填写模板并转换为pdf，单图片控件，"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"

- test:
    name: "创建企业模板，单图片企业模板，非必填"
    testcase: common/template/wordTemplate_one_pic.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "content为图片且非必填_contentValue为空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "danxuan"
      - contentValue: "A"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
