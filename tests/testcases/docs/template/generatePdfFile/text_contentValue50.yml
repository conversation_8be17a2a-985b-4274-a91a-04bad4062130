#openapi填写模板并转为pdf-模版id
- config:
    variables:
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      contentCodeyp1: "zdhcsypcode$docRandomNo"
      contentCodeyp2: "zdhcsypcode2$docRandomNo"
      contentCodeyp3: "zdhcsypcode3$docRandomNo"
      contentCodeyp4: "zdhcsypcode4$docRandomNo"
      contentCodeyp5: "zdhcsypcode5$docRandomNo"
      contentCodeyp6: "zdhcsypcode6$docRandomNo"
      contentCodeyp7: "zdhcsypcode7$docRandomNo"
      contentCodeyp8: "zdhcsypcode8$docRandomNo"
      contentCodeyp9: "zdhcsypcode9$docRandomNo"
      contentCodeyp10: "zdhcsypcode10$docRandomNo"
      contentCodeyp11: "zdhcsypcode11$docRandomNo"
      contentCodeyp12: "zdhcsypcode12$docRandomNo"
      contentCodeyp13: "zdhcsypcode13$docRandomNo"
      contentCodeyp14: "zdhcsypcode14$docRandomNo"
      contentCodeyp15: "zdhcsypcode15$docRandomNo"
      contentCodeyp16: "zdhcsypcode16$docRandomNo"
      contentCodeyp17: "zdhcsypcode17$docRandomNo"
      contentCodeyp18: "zdhcsypcode18$docRandomNo"
      contentCodeyp19: "zdhcsypcode19$docRandomNo"
      contentCodeyp20: "zdhcsypcode20$docRandomNo"
      contentCodeyp21: "zdhcsypcode21$docRandomNo"
      contentCodeyp22: "zdhcsypcode22$docRandomNo"
      contentCodeyp23: "zdhcsypcode23$docRandomNo"
      contentCodeyp24: "zdhcsypcode24$docRandomNo"
      contentCodeyp25: "zdhcsypcode25$docRandomNo"
      contentCodeyp26: "zdhcsypcode26$docRandomNo"
      contentCodeyp27: "zdhcsypcode27$docRandomNo"
      contentCodeyp28: "zdhcsypcode28$docRandomNo"
      contentCodeyp29: "zdhcsypcode29$docRandomNo"
      contentCodeyp30: "zdhcsypcode30$docRandomNo"
      contentCodeyp31: "zdhcsypcode31$docRandomNo"
      contentCodeyp32: "zdhcsypcode32$docRandomNo"
      contentCodeyp33: "zdhcsypcode33$docRandomNo"
      contentCodeyp34: "zdhcsypcode34$docRandomNo"
      contentCodeyp35: "zdhcsypcode35$docRandomNo"
      contentCodeyp36: "zdhcsypcode36$docRandomNo"
      contentCodeyp37: "zdhcsypcode37$docRandomNo"
      contentCodeyp38: "zdhcsypcode38$docRandomNo"
      contentCodeyp39: "zdhcsypcode39$docRandomNo"
      contentCodeyp40: "zdhcsypcode40$docRandomNo"
      contentCodeyp41: "zdhcsypcode41$docRandomNo"
      contentCodeyp42: "zdhcsypcode42$docRandomNo"
      contentCodeyp43: "zdhcsypcode43$docRandomNo"
      contentCodeyp44: "zdhcsypcode44$docRandomNo"
      contentCodeyp45: "zdhcsypcode45$docRandomNo"
      contentCodeyp46: "zdhcsypcode46$docRandomNo"
      contentCodeyp47: "zdhcsypcode47$docRandomNo"
      contentCodeyp48: "zdhcsypcode48$docRandomNo"
      contentCodeyp49: "zdhcsypcode49$docRandomNo"
      contentCodeyp50: "zdhcsypcode50$docRandomNo"
      contentFieldId001: "${get_randomStr_32()}"
      contentFieldId002: "${get_randomStr_32()}"
      contentFieldId003: "${get_randomStr_32()}"
      contentFieldId004: "${get_randomStr_32()}"
      contentFieldId005: "${get_randomStr_32()}"
      contentFieldId006: "${get_randomStr_32()}"
      contentFieldId007: "${get_randomStr_32()}"
      contentFieldId008: "${get_randomStr_32()}"
      contentFieldId009: "${get_randomStr_32()}"
      contentFieldId010: "${get_randomStr_32()}"
      contentFieldId011: "${get_randomStr_32()}"
      contentFieldId012: "${get_randomStr_32()}"
      contentFieldId013: "${get_randomStr_32()}"
      contentFieldId014: "${get_randomStr_32()}"
      contentFieldId015: "${get_randomStr_32()}"
      contentFieldId016: "${get_randomStr_32()}"
      contentFieldId017: "${get_randomStr_32()}"
      contentFieldId018: "${get_randomStr_32()}"
      contentFieldId019: "${get_randomStr_32()}"
      contentFieldId020: "${get_randomStr_32()}"
      contentFieldId021: "${get_randomStr_32()}"
      contentFieldId022: "${get_randomStr_32()}"
      contentFieldId023: "${get_randomStr_32()}"
      contentFieldId024: "${get_randomStr_32()}"
      contentFieldId025: "${get_randomStr_32()}"
      contentFieldId026: "${get_randomStr_32()}"
      contentFieldId027: "${get_randomStr_32()}"
      contentFieldId028: "${get_randomStr_32()}"
      contentFieldId029: "${get_randomStr_32()}"
      contentFieldId030: "${get_randomStr_32()}"
      contentFieldId031: "${get_randomStr_32()}"
      contentFieldId032: "${get_randomStr_32()}"
      contentFieldId033: "${get_randomStr_32()}"
      contentFieldId034: "${get_randomStr_32()}"
      contentFieldId035: "${get_randomStr_32()}"
      contentFieldId036: "${get_randomStr_32()}"
      contentFieldId037: "${get_randomStr_32()}"
      contentFieldId038: "${get_randomStr_32()}"
      contentFieldId039: "${get_randomStr_32()}"
      contentFieldId040: "${get_randomStr_32()}"
      contentFieldId041: "${get_randomStr_32()}"
      contentFieldId042: "${get_randomStr_32()}"
      contentFieldId043: "${get_randomStr_32()}"
      contentFieldId044: "${get_randomStr_32()}"
      contentFieldId045: "${get_randomStr_32()}"
      contentFieldId046: "${get_randomStr_32()}"
      contentFieldId047: "${get_randomStr_32()}"
      contentFieldId048: "${get_randomStr_32()}"
      contentFieldId049: "${get_randomStr_32()}"
      contentFieldId050: "${get_randomStr_32()}"

      contentValue1: "填充测试"

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: "自动化脚本50个内容域${get_randomNo()}"
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

#- test:
#    name: "setup-添加内容域"
#    api: api/esignDocs/content_domain/add.yml
#    times: 50
#    variables:
#      - description: null
#      - contentName: "0428自动化脚本50个内容域${get_randomNo()}"
#      - contentCode: "zdh50${get_randomNo()}"
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-分页查询"
#    api: api/esignDocs/content_domain/list.yml
#    variables:
#      - contentName: "0428自动化脚本50个内容域"
#      - page: 1
#      - size: 50
#    extract:
#      - contentUuid1: content.data.list.0.domainId
#      - contentUuid2: content.data.list.1.domainId
#      - contentUuid3: content.data.list.2.domainId
#      - contentUuid4: content.data.list.3.domainId
#      - contentUuid5: content.data.list.4.domainId
#      - contentUuid6: content.data.list.5.domainId
#      - contentUuid7: content.data.list.6.domainId
#      - contentUuid8: content.data.list.7.domainId
#      - contentUuid9: content.data.list.8.domainId
#      - contentUuid10: content.data.list.9.domainId
#      - contentUuid11: content.data.list.10.domainId
#      - contentUuid12: content.data.list.11.domainId
#      - contentUuid13: content.data.list.12.domainId
#      - contentUuid14: content.data.list.13.domainId
#      - contentUuid15: content.data.list.14.domainId
#      - contentUuid16: content.data.list.15.domainId
#      - contentUuid17: content.data.list.16.domainId
#      - contentUuid18: content.data.list.17.domainId
#      - contentUuid19: content.data.list.18.domainId
#      - contentUuid20: content.data.list.19.domainId
#      - contentUuid21: content.data.list.20.domainId
#      - contentUuid22: content.data.list.21.domainId
#      - contentUuid23: content.data.list.22.domainId
#      - contentUuid24: content.data.list.23.domainId
#      - contentUuid25: content.data.list.24.domainId
#      - contentUuid26: content.data.list.25.domainId
#      - contentUuid27: content.data.list.26.domainId
#      - contentUuid28: content.data.list.27.domainId
#      - contentUuid29: content.data.list.28.domainId
#      - contentUuid30: content.data.list.29.domainId
#      - contentUuid31: content.data.list.30.domainId
#      - contentUuid32: content.data.list.31.domainId
#      - contentUuid33: content.data.list.32.domainId
#      - contentUuid34: content.data.list.33.domainId
#      - contentUuid35: content.data.list.34.domainId
#      - contentUuid36: content.data.list.35.domainId
#      - contentUuid37: content.data.list.36.domainId
#      - contentUuid38: content.data.list.37.domainId
#      - contentUuid39: content.data.list.38.domainId
#      - contentUuid40: content.data.list.39.domainId
#      - contentUuid41: content.data.list.40.domainId
#      - contentUuid42: content.data.list.41.domainId
#      - contentUuid43: content.data.list.42.domainId
#      - contentUuid44: content.data.list.43.domainId
#      - contentUuid45: content.data.list.44.domainId
#      - contentUuid46: content.data.list.45.domainId
#      - contentUuid47: content.data.list.46.domainId
#      - contentUuid48: content.data.list.47.domainId
#      - contentUuid49: content.data.list.48.domainId
#      - contentUuid50: content.data.list.49.domainId
#      - contentCodeyp1: content.data.list.0.contentCode
#      - contentCodeyp2: content.data.list.1.contentCode
#      - contentCodeyp3: content.data.list.2.contentCode
#      - contentCodeyp4: content.data.list.3.contentCode
#      - contentCodeyp5: content.data.list.4.contentCode
#      - contentCodeyp6: content.data.list.5.contentCode
#      - contentCodeyp7: content.data.list.6.contentCode
#      - contentCodeyp8: content.data.list.7.contentCode
#      - contentCodeyp9: content.data.list.8.contentCode
#      - contentCodeyp10: content.data.list.9.contentCode
#      - contentCodeyp11: content.data.list.10.contentCode
#      - contentCodeyp12: content.data.list.11.contentCode
#      - contentCodeyp13: content.data.list.12.contentCode
#      - contentCodeyp14: content.data.list.13.contentCode
#      - contentCodeyp15: content.data.list.14.contentCode
#      - contentCodeyp16: content.data.list.15.contentCode
#      - contentCodeyp17: content.data.list.16.contentCode
#      - contentCodeyp18: content.data.list.17.contentCode
#      - contentCodeyp19: content.data.list.18.contentCode
#      - contentCodeyp20: content.data.list.19.contentCode
#      - contentCodeyp21: content.data.list.20.contentCode
#      - contentCodeyp22: content.data.list.21.contentCode
#      - contentCodeyp23: content.data.list.22.contentCode
#      - contentCodeyp24: content.data.list.23.contentCode
#      - contentCodeyp25: content.data.list.24.contentCode
#      - contentCodeyp26: content.data.list.25.contentCode
#      - contentCodeyp27: content.data.list.26.contentCode
#      - contentCodeyp28: content.data.list.27.contentCode
#      - contentCodeyp29: content.data.list.28.contentCode
#      - contentCodeyp30: content.data.list.29.contentCode
#      - contentCodeyp31: content.data.list.30.contentCode
#      - contentCodeyp32: content.data.list.31.contentCode
#      - contentCodeyp33: content.data.list.32.contentCode
#      - contentCodeyp34: content.data.list.33.contentCode
#      - contentCodeyp35: content.data.list.34.contentCode
#      - contentCodeyp36: content.data.list.35.contentCode
#      - contentCodeyp37: content.data.list.36.contentCode
#      - contentCodeyp38: content.data.list.37.contentCode
#      - contentCodeyp39: content.data.list.38.contentCode
#      - contentCodeyp40: content.data.list.39.contentCode
#      - contentCodeyp41: content.data.list.40.contentCode
#      - contentCodeyp42: content.data.list.41.contentCode
#      - contentCodeyp43: content.data.list.42.contentCode
#      - contentCodeyp44: content.data.list.43.contentCode
#      - contentCodeyp45: content.data.list.44.contentCode
#      - contentCodeyp46: content.data.list.45.contentCode
#      - contentCodeyp47: content.data.list.46.contentCode
#      - contentCodeyp48: content.data.list.47.contentCode
#      - contentCodeyp49: content.data.list.48.contentCode
#      - contentCodeyp50: content.data.list.49.contentCode
#      - contentNameyp1: content.data.list.0.contentName
#      - contentNameyp2: content.data.list.1.contentName
#      - contentNameyp3: content.data.list.2.contentName
#      - contentNameyp4: content.data.list.3.contentName
#      - contentNameyp5: content.data.list.4.contentName
#      - contentNameyp6: content.data.list.5.contentName
#      - contentNameyp7: content.data.list.6.contentName
#      - contentNameyp8: content.data.list.7.contentName
#      - contentNameyp9: content.data.list.8.contentName
#      - contentNameyp10: content.data.list.9.contentName
#      - contentNameyp11: content.data.list.10.contentName
#      - contentNameyp12: content.data.list.11.contentName
#      - contentNameyp13: content.data.list.12.contentName
#      - contentNameyp14: content.data.list.13.contentName
#      - contentNameyp15: content.data.list.14.contentName
#      - contentNameyp16: content.data.list.15.contentName
#      - contentNameyp17: content.data.list.16.contentName
#      - contentNameyp18: content.data.list.17.contentName
#      - contentNameyp19: content.data.list.18.contentName
#      - contentNameyp20: content.data.list.19.contentName
#      - contentNameyp21: content.data.list.20.contentName
#      - contentNameyp22: content.data.list.21.contentName
#      - contentNameyp23: content.data.list.22.contentName
#      - contentNameyp24: content.data.list.23.contentName
#      - contentNameyp25: content.data.list.24.contentName
#      - contentNameyp26: content.data.list.25.contentName
#      - contentNameyp27: content.data.list.26.contentName
#      - contentNameyp28: content.data.list.27.contentName
#      - contentNameyp29: content.data.list.28.contentName
#      - contentNameyp30: content.data.list.29.contentName
#      - contentNameyp31: content.data.list.30.contentName
#      - contentNameyp32: content.data.list.31.contentName
#      - contentNameyp33: content.data.list.32.contentName
#      - contentNameyp34: content.data.list.33.contentName
#      - contentNameyp35: content.data.list.34.contentName
#      - contentNameyp36: content.data.list.35.contentName
#      - contentNameyp37: content.data.list.36.contentName
#      - contentNameyp38: content.data.list.37.contentName
#      - contentNameyp39: content.data.list.38.contentName
#      - contentNameyp40: content.data.list.39.contentName
#      - contentNameyp41: content.data.list.40.contentName
#      - contentNameyp42: content.data.list.41.contentName
#      - contentNameyp43: content.data.list.42.contentName
#      - contentNameyp44: content.data.list.43.contentName
#      - contentNameyp45: content.data.list.44.contentName
#      - contentNameyp46: content.data.list.45.contentName
#      - contentNameyp47: content.data.list.46.contentName
#      - contentNameyp48: content.data.list.47.contentName
#      - contentNameyp49: content.data.list.48.contentName
#      - contentNameyp50: content.data.list.49.contentName
#    validate:
#      - len_ge: [ "content.data.list", 1 ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "setup-编辑模板内容/添加内容域"
#    api: api/esignDocs/template/contentAdd50.yml
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuid1
#      - version: $newVersion1
#      - contentUuid1: $contentUuid1
#      - contentCode1: $contentCodeyp1
#      - contentName1: $contentNameyp1
#      - contentUuid2: $contentUuid2
#      - contentCode2: $contentCodeyp2
#      - contentName2: $contentNameyp2
#      - contentUuid3: $contentUuid3
#      - contentCode3: $contentCodeyp3
#      - contentName3: $contentNameyp3
#      - contentUuid4: $contentUuid4
#      - contentCode4: $contentCodeyp4
#      - contentName4: $contentNameyp4
#      - contentUuid5: $contentUuid5
#      - contentCode5: $contentCodeyp5
#      - contentName5: $contentNameyp5
#      - contentUuid6: $contentUuid6
#      - contentCode6: $contentCodeyp6
#      - contentName6: $contentNameyp6
#      - contentUuid7: $contentUuid7
#      - contentCode7: $contentCodeyp7
#      - contentName7: $contentNameyp7
#      - contentUuid8: $contentUuid8
#      - contentCode8: $contentCodeyp8
#      - contentName8: $contentNameyp8
#      - contentUuid9: $contentUuid9
#      - contentCode9: $contentCodeyp9
#      - contentName9: $contentNameyp9
#      - contentUuid10: $contentUuid10
#      - contentCode10: $contentCodeyp10
#      - contentName10: $contentNameyp10
#      - contentUuid11: $contentUuid11
#      - contentCode11: $contentCodeyp11
#      - contentName11: $contentNameyp11
#      - contentUuid12: $contentUuid12
#      - contentCode12: $contentCodeyp12
#      - contentName12: $contentNameyp12
#      - contentUuid13: $contentUuid13
#      - contentCode13: $contentCodeyp13
#      - contentName13: $contentNameyp13
#      - contentUuid14: $contentUuid14
#      - contentCode14: $contentCodeyp14
#      - contentName14: $contentNameyp14
#      - contentUuid15: $contentUuid15
#      - contentCode15: $contentCodeyp15
#      - contentName15: $contentNameyp15
#      - contentUuid16: $contentUuid16
#      - contentCode16: $contentCodeyp16
#      - contentName16: $contentNameyp16
#      - contentUuid17: $contentUuid17
#      - contentCode17: $contentCodeyp17
#      - contentName17: $contentNameyp17
#      - contentUuid18: $contentUuid18
#      - contentCode18: $contentCodeyp18
#      - contentName18: $contentNameyp18
#      - contentUuid19: $contentUuid19
#      - contentCode19: $contentCodeyp19
#      - contentName19: $contentNameyp19
#      - contentUuid20: $contentUuid20
#      - contentCode20: $contentCodeyp20
#      - contentName20: $contentNameyp20
#      - contentUuid21: $contentUuid21
#      - contentCode21: $contentCodeyp21
#      - contentName21: $contentNameyp21
#      - contentUuid22: $contentUuid22
#      - contentCode22: $contentCodeyp22
#      - contentName22: $contentNameyp22
#      - contentUuid23: $contentUuid23
#      - contentCode23: $contentCodeyp23
#      - contentName23: $contentNameyp23
#      - contentUuid24: $contentUuid24
#      - contentCode24: $contentCodeyp24
#      - contentName24: $contentNameyp24
#      - contentUuid25: $contentUuid25
#      - contentCode25: $contentCodeyp25
#      - contentName25: $contentNameyp25
#      - contentUuid26: $contentUuid26
#      - contentCode26: $contentCodeyp26
#      - contentName26: $contentNameyp26
#      - contentUuid27: $contentUuid27
#      - contentCode27: $contentCodeyp27
#      - contentName27: $contentNameyp27
#      - contentUuid28: $contentUuid28
#      - contentCode28: $contentCodeyp28
#      - contentName28: $contentNameyp28
#      - contentUuid29: $contentUuid29
#      - contentCode29: $contentCodeyp29
#      - contentName29: $contentNameyp29
#      - contentUuid30: $contentUuid30
#      - contentCode30: $contentCodeyp30
#      - contentName30: $contentNameyp30
#      - contentUuid31: $contentUuid31
#      - contentCode31: $contentCodeyp31
#      - contentName31: $contentNameyp31
#      - contentUuid32: $contentUuid32
#      - contentCode32: $contentCodeyp32
#      - contentName32: $contentNameyp32
#      - contentUuid33: $contentUuid33
#      - contentCode33: $contentCodeyp33
#      - contentName33: $contentNameyp33
#      - contentUuid34: $contentUuid34
#      - contentCode34: $contentCodeyp34
#      - contentName34: $contentNameyp34
#      - contentUuid35: $contentUuid35
#      - contentCode35: $contentCodeyp35
#      - contentName35: $contentNameyp35
#      - contentUuid36: $contentUuid36
#      - contentCode36: $contentCodeyp36
#      - contentName36: $contentNameyp36
#      - contentUuid37: $contentUuid37
#      - contentCode37: $contentCodeyp37
#      - contentName37: $contentNameyp37
#      - contentUuid38: $contentUuid38
#      - contentCode38: $contentCodeyp38
#      - contentName38: $contentNameyp38
#      - contentUuid39: $contentUuid39
#      - contentCode39: $contentCodeyp39
#      - contentName39: $contentNameyp39
#      - contentUuid40: $contentUuid40
#      - contentCode40: $contentCodeyp40
#      - contentName40: $contentNameyp40
#      - contentUuid41: $contentUuid41
#      - contentCode41: $contentCodeyp41
#      - contentName41: $contentNameyp41
#      - contentUuid42: $contentUuid42
#      - contentCode42: $contentCodeyp42
#      - contentName42: $contentNameyp42
#      - contentUuid43: $contentUuid43
#      - contentCode43: $contentCodeyp43
#      - contentName43: $contentNameyp43
#      - contentUuid44: $contentUuid44
#      - contentCode44: $contentCodeyp44
#      - contentName44: $contentNameyp44
#      - contentUuid45: $contentUuid45
#      - contentCode45: $contentCodeyp45
#      - contentName45: $contentNameyp45
#      - contentUuid46: $contentUuid46
#      - contentCode46: $contentCodeyp46
#      - contentName46: $contentNameyp46
#      - contentUuid47: $contentUuid47
#      - contentCode47: $contentCodeyp47
#      - contentName47: $contentNameyp47
#      - contentUuid48: $contentUuid48
#      - contentCode48: $contentCodeyp48
#      - contentName48: $contentNameyp48
#      - contentUuid49: $contentUuid49
#      - contentCode49: $contentCodeyp49
#      - contentName49: $contentNameyp49
#      - contentUuid50: $contentUuid50
#      - contentCode50: $contentCodeyp50
#      - contentName50: $contentNameyp50
#      - edgeScope: 0
#    extract:
#      - templateContentUuid1: content.data.list.0
#      - templateContentUuid2: content.data.list.1
#      - templateContentUuid3: content.data.list.2
#      - templateContentUuid4: content.data.list.3
#      - templateContentUuid5: content.data.list.4
#      - templateContentUuid6: content.data.list.5
#      - templateContentUuid7: content.data.list.6
#      - templateContentUuid8: content.data.list.7
#      - templateContentUuid9: content.data.list.8
#      - templateContentUuid10: content.data.list.9
#      - templateContentUuid11: content.data.list.10
#      - templateContentUuid12: content.data.list.11
#      - templateContentUuid13: content.data.list.12
#      - templateContentUuid14: content.data.list.13
#      - templateContentUuid15: content.data.list.14
#      - templateContentUuid16: content.data.list.15
#      - templateContentUuid17: content.data.list.16
#      - templateContentUuid18: content.data.list.17
#      - templateContentUuid19: content.data.list.18
#      - templateContentUuid20: content.data.list.19
#      - templateContentUuid21: content.data.list.20
#      - templateContentUuid22: content.data.list.21
#      - templateContentUuid23: content.data.list.22
#      - templateContentUuid24: content.data.list.23
#      - templateContentUuid25: content.data.list.24
#      - templateContentUuid26: content.data.list.25
#      - templateContentUuid27: content.data.list.26
#      - templateContentUuid28: content.data.list.27
#      - templateContentUuid29: content.data.list.28
#      - templateContentUuid30: content.data.list.29
#      - templateContentUuid31: content.data.list.30
#      - templateContentUuid32: content.data.list.31
#      - templateContentUuid33: content.data.list.32
#      - templateContentUuid34: content.data.list.33
#      - templateContentUuid35: content.data.list.34
#      - templateContentUuid36: content.data.list.35
#      - templateContentUuid37: content.data.list.36
#      - templateContentUuid38: content.data.list.37
#      - templateContentUuid39: content.data.list.38
#      - templateContentUuid40: content.data.list.39
#      - templateContentUuid41: content.data.list.40
#      - templateContentUuid42: content.data.list.41
#      - templateContentUuid43: content.data.list.42
#      - templateContentUuid44: content.data.list.43
#      - templateContentUuid45: content.data.list.44
#      - templateContentUuid46: content.data.list.45
#      - templateContentUuid47: content.data.list.46
#      - templateContentUuid48: content.data.list.47
#      - templateContentUuid49: content.data.list.48
#      - templateContentUuid50: content.data.list.49
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC2-setup_添加签名区1"
#    api: api/esignDocs/template/owner/signatoryAdd.yml
#    variables:
#      - templateUuid: $newTemplateUuid1
#      - version: $newVersion1
#      - addSignTime: 0
#      - signType: 1
#      - dateFormat: "yyyy-MM-dd"
#      - edgeScope: null
#      - pageNo: "1"
#      - posX: 10
#      - posY: 10
#      - name: $signNameA
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-batch-save-draft-必填、非必填文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [
                {
                    "fieldId": "",
                    "label": "单行文本1",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 1,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": $contentFieldId001,
                    "bizId": $contentFieldId001,
                    "fieldKey": $contentCodeyp1,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 57.32006960556844,
                        "y": 741.6474284609435,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本2",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 2,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId002",
                    "bizId": "$contentFieldId002",
                    "fieldKey": $contentCodeyp2,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 264.807978860531,
                        "y": 753.31094998711,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本3",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 3,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId003",
                    "bizId": "$contentFieldId003",
                    "fieldKey": $contentCodeyp3,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 376.5322376901263,
                        "y": 674.7356470739882,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本4",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 4,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId004",
                    "bizId": "$contentFieldId004",
                    "fieldKey": $contentCodeyp4,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 193.5991105955143,
                        "y": 551.9617362722352,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本5",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 5,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId005",
                    "bizId": "$contentFieldId005",
                    "fieldKey": $contentCodeyp5,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 368.93560195926784,
                        "y": 569.0186909851876,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本6",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 6,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId006",
                    "bizId": "$contentFieldId006",
                    "fieldKey": $contentCodeyp6,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 129.7566769786027,
                        "y": 437.168129672596,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本7",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 7,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId007",
                    "bizId": "$contentFieldId007",
                    "fieldKey": $contentCodeyp7,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 313.91754318123225,
                        "y": 421.7609274439112,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本8",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 8,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId008",
                    "bizId": "$contentFieldId008",
                    "fieldKey": $contentCodeyp8,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 84.94419953596287,
                        "y": 316.11490077593794,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本9",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 9,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId009",
                    "bizId": "$contentFieldId009",
                    "fieldKey": $contentCodeyp9,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 275.8576308326888,
                        "y": 290.2719254960102,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本10",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 10,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId010",
                    "bizId": "$contentFieldId010",
                    "fieldKey": $contentCodeyp10,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 331.7197602474864,
                        "y": 189.53686462701307,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本11",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 11,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId011",
                    "bizId": "$contentFieldId011",
                    "fieldKey": $contentCodeyp11,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 5.141157514823408,
                        "y": 133.00041164664708,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本12",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 12,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId012",
                    "bizId": "$contentFieldId012",
                    "fieldKey": $contentCodeyp12,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 341.54167311162666,
                        "y": 106.54354807889843,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本13",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 13,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId013",
                    "bizId": "$contentFieldId013",
                    "fieldKey": $contentCodeyp13,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 119.93476411446248,
                        "y": 36.501946176527554,
                        "page": "1",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本14",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 14,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId014",
                    "bizId": "$contentFieldId014",
                    "fieldKey": $contentCodeyp14,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 31.537548337200306,
                        "y": 730.2675368495625,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本15",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 15,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId015",
                    "bizId": "$contentFieldId015",
                    "fieldKey": $contentCodeyp15,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 216.3122840938386,
                        "y": 654.7011089611127,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本16",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 16,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId016",
                    "bizId": "$contentFieldId016",
                    "fieldKey": $contentFieldId016,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 419.50310647073985,
                        "y": 668.7596359579426,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本17",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 17,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId017",
                    "bizId": "$contentFieldId017",
                    "fieldKey": $contentCodeyp17,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 45.042678525393136,
                        "y": 568.0245563551334,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本18",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 18,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId018",
                    "bizId": "$contentFieldId018",
                    "fieldKey": $contentCodeyp18,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 260.5108919824697,
                        "y": 575.9443878118757,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本19",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 19,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId019",
                    "bizId": "$contentFieldId019",
                    "fieldKey": $contentCodeyp19,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 379.6015854601701,
                        "y": 476.43704731708397,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本20",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 20,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId020",
                    "bizId": "$contentFieldId020",
                    "fieldKey": $contentCodeyp20,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 62.231026037638564,
                        "y": 396.5735325505728,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本21",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 21,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId021",
                    "bizId": "$contentFieldId021",
                    "fieldKey": $contentCodeyp21,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 196.05458881154934,
                        "y": 301.9771484878513,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本22",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 22,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId022",
                    "bizId": "$contentFieldId022",
                    "fieldKey": $contentCodeyp22,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 387.5818896622841,
                        "y": 351.02624006318075,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本23",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 23,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId023",
                    "bizId": "$contentFieldId023",
                    "fieldKey": $contentCodeyp23,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 33.3791569992266,
                        "y": 232.48894339411743,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本24",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 24,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId024",
                    "bizId": "$contentFieldId024",
                    "fieldKey": $contentCodeyp24,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 215.69841453982983,
                        "y": 210.94303625843895,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本25",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 25,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId025",
                    "bizId": "$contentFieldId025",
                    "fieldKey": $contentCodeyp25,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 425.6418020108275,
                        "y": 196.1496942168568,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本26",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 26,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId026",
                    "bizId": "$contentFieldId026",
                    "fieldKey": $contentCodeyp26,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 44.42880897138437,
                        "y": 79.4540062098198,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本27",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 27,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId027",
                    "bizId": "$contentFieldId027",
                    "fieldKey": $contentCodeyp27,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 269.1050657385924,
                        "y": 74.48257703237789,
                        "page": "2",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本28",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 28,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId028",
                    "bizId": "$contentFieldId028",
                    "fieldKey": $contentCodeyp28,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 379.6015854601701,
                        "y": 792.9,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本29",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 29,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId029",
                    "bizId": "$contentFieldId029",
                    "fieldKey": $contentCodeyp29,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 154.9253286929621,
                        "y": 692.1649863447409,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本30",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 30,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId030",
                    "bizId": "$contentFieldId030",
                    "fieldKey": $contentCodeyp30,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 348.2942382057231,
                        "y": 659.569502172153,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本31",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 31,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId031",
                    "bizId": "$contentFieldId031",
                    "fieldKey": $contentCodeyp31,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 103.36028615622583,
                        "y": 565.5870625986885,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本32",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 32,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId032",
                    "bizId": "$contentFieldId032",
                    "fieldKey": $contentCodeyp32,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 337.8584557875741,
                        "y": 561.2295779105036,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本33",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 33,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId033",
                    "bizId": "$contentFieldId033",
                    "fieldKey": $contentCodeyp33,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 110.11285125032224,
                        "y": 491.18805094338103,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本34",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 34,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId034",
                    "bizId": "$contentFieldId034",
                    "fieldKey": $contentCodeyp34,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 299.79854343903065,
                        "y": 483.14734893114365,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本35",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 35,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId035",
                    "bizId": "$contentFieldId035",
                    "fieldKey": $contentCodeyp35,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 84.94419953596287,
                        "y": 413.7196915180298,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本36",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 36,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId036",
                    "bizId": "$contentFieldId036",
                    "fieldKey": $contentCodeyp36,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 263.58023975251353,
                        "y": 389.71838110156455,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本37",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 37,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId037",
                    "bizId": "$contentFieldId037",
                    "fieldKey": $contentCodeyp37,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 77.57776488785768,
                        "y": 316.6075063643981,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本38",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 38,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId038",
                    "bizId": "$contentFieldId038",
                    "fieldKey": $contentCodeyp38,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 305.32336942510955,
                        "y": 309.79454346017826,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本39",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 39,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId039",
                    "bizId": "$contentFieldId039",
                    "fieldKey": $contentCodeyp39,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 97.83546017014693,
                        "y": 215.81210388671377,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本40",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 40,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId040",
                    "bizId": "$contentFieldId040",
                    "fieldKey": $contentCodeyp40,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 313.3036736272235,
                        "y": 196.7217499023186,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本41",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 41,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId041",
                    "bizId": "$contentFieldId041",
                    "fieldKey": $contentCodeyp41,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 70.82519979376127,
                        "y": 129.74957070523976,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本42",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 42,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId042",
                    "bizId": "$contentFieldId042",
                    "fieldKey": $contentCodeyp42,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 242.09480536220673,
                        "y": 74.44091303432754,
                        "page": "3",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本43",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 43,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId043",
                    "bizId": "$contentFieldId043",
                    "fieldKey": $contentCodeyp43,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 88.62741686001546,
                        "y": 760.2262744404969,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本44",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 44,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId044",
                    "bizId": "$contentFieldId044",
                    "fieldKey": $contentCodeyp44,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 287.5211523588554,
                        "y": 704.9176167695846,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本45",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 45,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId045",
                    "bizId": "$contentFieldId045",
                    "fieldKey": $contentCodeyp45,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 343.99715132766175,
                        "y": 582.0833081577082,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本46",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 46,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId046",
                    "bizId": "$contentFieldId046",
                    "fieldKey": $contentCodeyp46,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 137.73698118071667,
                        "y": 476.4371971875806,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本47",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 47,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId047",
                    "bizId": "$contentFieldId047",
                    "fieldKey": $contentCodeyp47,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 337.2445862335653,
                        "y": 421.1283896461717,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本48",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 48,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId048",
                    "bizId": "$contentFieldId048",
                    "fieldKey": $contentCodeyp48,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 186.23267594740912,
                        "y": 338.1954521743684,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本49",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 49,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId049",
                    "bizId": "$contentFieldId049",
                    "fieldKey": $contentCodeyp49,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 329.87815158546016,
                        "y": 325.2436438595644,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                },
                {
                    "fieldId": "",
                    "label": "单行文本50",
                    "custom": false,
                    "type": "TEXT",
                    "subType": null,
                    "sort": 50,
                    "formula": null,
                    "style": {
                        "font": "1",
                        "fontSize": 42,
                        "textColor": "#000",
                        "width": 160,
                        "height": 49,
                        "bold": false,
                        "italic": false,
                        "underLine": false,
                        "lineThrough": false,
                        "verticalAlignment": "TOP",
                        "horizontalAlignment": "LEFT",
                        "styleExt": {
                            "signDatePos": null,
                            "units": "px",
                            "imgType": null,
                            "usePageTypeGroupId": "",
                            "hideTHeader": null,
                            "selectLayout": null,
                            "borderWidth": "1",
                            "borderColor": "#000",
                            "keyword": "",
                            "groupKey": "",
                            "tickOptions": null,
                            "elementId": "",
                            "posKey": "",
                            "imgCrop": null,
                            "paddingLeftAndRight": "0"
                        }
                    },
                    "settings": {
                        "defaultValue": null,
                        "required": false,
                        "dateFormat": null,
                        "validation": {
                            "type": "REGEXP",
                            "pattern": ""
                        },
                        "selectableDataSource": [],
                        "numberFormat": {
                            "integerDigits": null,
                            "fractionDigits": null,
                            "thousandsSeparator": ""
                        },
                        "editable": true,
                        "encryptAlgorithm": "",
                        "fillLengthLimit": "20",
                        "overflowType": "1",
                        "minFontSize": "8",
                        "remarkInputType": null,
                        "content": null,
                        "remarkAICheck": null,
                        "dateRule": null,
                        "tickOptions": null,
                        "configExt": {
                            "cooperationerSubjectType": "",
                            "icon": "epaas-icon-text",
                            "fastCheck": null,
                            "addSealRule": "",
                            "keyPosX": null,
                            "keyPosY": null,
                            "ext": "{}",
                            "version": null,
                            "mergeId": null
                        },
                        "sealTypes": [],
                        "columnMapping": null,
                        "positionMovable": null,
                        "cellEditableOnFilling": true,
                        "signDatePosition": null
                    },
                    "options": null,
                    "instructions": "",
                    "contentFieldId": "$contentFieldId050",
                    "bizId": "$contentFieldId050",
                    "fieldKey": $contentCodeyp50,
                    "fillGroupKey": "",
                    "fieldValue": null,
                    "defaultValue": null,
                    "position": {
                        "x": 170.88593709718998,
                        "y": 241.6968368337523,
                        "page": "4",
                        "scope": "default",
                        "intervalType": null
                    },
                    "formField": false
                }
            ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC1-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - templateContentUuid1: content.data.contentsControl.0.contentId
      - contentCodeyp1: content.data.contentsControl.0.contentCode
      - contentName1: content.data.contentsControl.0.contentName
      - templateContentUuid2: content.data.contentsControl.1.contentId
      - contentCodeyp2: content.data.contentsControl.1.contentCode
      - contentName2: content.data.contentsControl.1.contentName
      - templateContentUuid3: content.data.contentsControl.2.contentId
      - contentCodeyp3: content.data.contentsControl.2.contentCode
      - contentName3: content.data.contentsControl.2.contentName
      - templateContentUuid4: content.data.contentsControl.3.contentId
      - contentCodeyp4: content.data.contentsControl.3.contentCode
      - contentName4: content.data.contentsControl.3.contentName
      - templateContentUuid5: content.data.contentsControl.4.contentId
      - contentCodeyp5: content.data.contentsControl.4.contentCode
      - contentName5: content.data.contentsControl.4.contentName
      - templateContentUuid6: content.data.contentsControl.5.contentId
      - contentCodeyp6: content.data.contentsControl.5.contentCode
      - contentName6: content.data.contentsControl.5.contentName
      - templateContentUuid7: content.data.contentsControl.6.contentId
      - contentCodeyp7: content.data.contentsControl.6.contentCode
      - contentName7: content.data.contentsControl.6.contentName
      - templateContentUuid8: content.data.contentsControl.7.contentId
      - contentCodeyp8: content.data.contentsControl.7.contentCode
      - contentName8: content.data.contentsControl.7.contentName
      - templateContentUuid9: content.data.contentsControl.8.contentId
      - contentCodeyp9: content.data.contentsControl.8.contentCode
      - contentName9: content.data.contentsControl.8.contentName
      - templateContentUuid10: content.data.contentsControl.9.contentId
      - contentCodeyp10: content.data.contentsControl.9.contentCode
      - contentName10: content.data.contentsControl.9.contentName
      - templateContentUuid11: content.data.contentsControl.10.contentId
      - contentCodeyp11: content.data.contentsControl.10.contentCode
      - contentName11: content.data.contentsControl.10.contentName
      - templateContentUuid12: content.data.contentsControl.11.contentId
      - contentCodeyp12: content.data.contentsControl.11.contentCode
      - contentName12: content.data.contentsControl.11.contentName
      - templateContentUuid13: content.data.contentsControl.12.contentId
      - contentCodeyp13: content.data.contentsControl.12.contentCode
      - contentName13: content.data.contentsControl.12.contentName
      - templateContentUuid14: content.data.contentsControl.13.contentId
      - contentCodeyp14: content.data.contentsControl.13.contentCode
      - contentName14: content.data.contentsControl.13.contentName
      - templateContentUuid15: content.data.contentsControl.14.contentId
      - contentCodeyp15: content.data.contentsControl.14.contentCode
      - contentName15: content.data.contentsControl.14.contentName
      - templateContentUuid16: content.data.contentsControl.15.contentId
      - contentCodeyp16: content.data.contentsControl.15.contentCode
      - contentName16: content.data.contentsControl.15.contentName
      - templateContentUuid17: content.data.contentsControl.16.contentId
      - contentCodeyp17: content.data.contentsControl.16.contentCode
      - contentName17: content.data.contentsControl.16.contentName
      - templateContentUuid18: content.data.contentsControl.17.contentId
      - contentCodeyp18: content.data.contentsControl.17.contentCode
      - contentName18: content.data.contentsControl.17.contentName
      - templateContentUuid19: content.data.contentsControl.18.contentId
      - contentCodeyp19: content.data.contentsControl.18.contentCode
      - contentName19: content.data.contentsControl.18.contentName
      - templateContentUuid20: content.data.contentsControl.19.contentId
      - contentCodeyp20: content.data.contentsControl.19.contentCode
      - contentName20: content.data.contentsControl.19.contentName
      - templateContentUuid21: content.data.contentsControl.20.contentId
      - contentCodeyp21: content.data.contentsControl.20.contentCode
      - contentName21: content.data.contentsControl.20.contentName
      - templateContentUuid22: content.data.contentsControl.21.contentId
      - contentCodeyp22: content.data.contentsControl.21.contentCode
      - contentName22: content.data.contentsControl.21.contentName
      - templateContentUuid23: content.data.contentsControl.22.contentId
      - contentCodeyp23: content.data.contentsControl.22.contentCode
      - contentName23: content.data.contentsControl.22.contentName
      - templateContentUuid24: content.data.contentsControl.23.contentId
      - contentCodeyp24: content.data.contentsControl.23.contentCode
      - contentName24: content.data.contentsControl.23.contentName
      - templateContentUuid25: content.data.contentsControl.24.contentId
      - contentCodeyp25: content.data.contentsControl.24.contentCode
      - contentName25: content.data.contentsControl.24.contentName
      - templateContentUuid26: content.data.contentsControl.25.contentId
      - contentCodeyp26: content.data.contentsControl.25.contentCode
      - contentName26: content.data.contentsControl.25.contentName
      - templateContentUuid27: content.data.contentsControl.26.contentId
      - contentCodeyp27: content.data.contentsControl.26.contentCode
      - contentName27: content.data.contentsControl.26.contentName
      - templateContentUuid28: content.data.contentsControl.27.contentId
      - contentCodeyp28: content.data.contentsControl.27.contentCode
      - contentName28: content.data.contentsControl.27.contentName
      - templateContentUuid29: content.data.contentsControl.28.contentId
      - contentCodeyp29: content.data.contentsControl.28.contentCode
      - contentName29: content.data.contentsControl.28.contentName
      - templateContentUuid30: content.data.contentsControl.29.contentId
      - contentCodeyp30: content.data.contentsControl.29.contentCode
      - contentName30: content.data.contentsControl.29.contentName
      - templateContentUuid31: content.data.contentsControl.30.contentId
      - contentCodeyp31: content.data.contentsControl.30.contentCode
      - contentName31: content.data.contentsControl.30.contentName
      - templateContentUuid32: content.data.contentsControl.31.contentId
      - contentCodeyp32: content.data.contentsControl.31.contentCode
      - contentName32: content.data.contentsControl.31.contentName
      - templateContentUuid33: content.data.contentsControl.32.contentId
      - contentCodeyp33: content.data.contentsControl.32.contentCode
      - contentName33: content.data.contentsControl.32.contentName
      - templateContentUuid34: content.data.contentsControl.33.contentId
      - contentCodeyp34: content.data.contentsControl.33.contentCode
      - contentName34: content.data.contentsControl.33.contentName
      - templateContentUuid35: content.data.contentsControl.34.contentId
      - contentCodeyp35: content.data.contentsControl.34.contentCode
      - contentName35: content.data.contentsControl.34.contentName
      - templateContentUuid36: content.data.contentsControl.35.contentId
      - contentCodeyp36: content.data.contentsControl.35.contentCode
      - contentName36: content.data.contentsControl.35.contentName
      - templateContentUuid37: content.data.contentsControl.36.contentId
      - contentCodeyp37: content.data.contentsControl.36.contentCode
      - contentName37: content.data.contentsControl.36.contentName
      - templateContentUuid38: content.data.contentsControl.37.contentId
      - contentCodeyp38: content.data.contentsControl.37.contentCode
      - contentName38: content.data.contentsControl.37.contentName
      - templateContentUuid39: content.data.contentsControl.38.contentId
      - contentCodeyp39: content.data.contentsControl.38.contentCode
      - contentName39: content.data.contentsControl.38.contentName
      - templateContentUuid40: content.data.contentsControl.39.contentId
      - contentCodeyp40: content.data.contentsControl.39.contentCode
      - contentName40: content.data.contentsControl.39.contentName
      - templateContentUuid41: content.data.contentsControl.40.contentId
      - contentCodeyp41: content.data.contentsControl.40.contentCode
      - contentName41: content.data.contentsControl.40.contentName
      - templateContentUuid42: content.data.contentsControl.41.contentId
      - contentCodeyp42: content.data.contentsControl.41.contentCode
      - contentName42: content.data.contentsControl.41.contentName
      - templateContentUuid43: content.data.contentsControl.42.contentId
      - contentCodeyp43: content.data.contentsControl.42.contentCode
      - contentName43: content.data.contentsControl.42.contentName
      - templateContentUuid44: content.data.contentsControl.43.contentId
      - contentCodeyp44: content.data.contentsControl.43.contentCode
      - contentName44: content.data.contentsControl.43.contentName
      - templateContentUuid45: content.data.contentsControl.44.contentId
      - contentCodeyp45: content.data.contentsControl.44.contentCode
      - contentName45: content.data.contentsControl.44.contentName
      - templateContentUuid46: content.data.contentsControl.45.contentId
      - contentCodeyp46: content.data.contentsControl.45.contentCode
      - contentName46: content.data.contentsControl.45.contentName
      - templateContentUuid47: content.data.contentsControl.46.contentId
      - contentCodeyp47: content.data.contentsControl.46.contentCode
      - contentName47: content.data.contentsControl.46.contentName
      - templateContentUuid48: content.data.contentsControl.47.contentId
      - contentCodeyp48: content.data.contentsControl.47.contentCode
      - contentName48: content.data.contentsControl.47.contentName
      - templateContentUuid49: content.data.contentsControl.48.contentId
      - contentCodeyp49: content.data.contentsControl.48.contentCode
      - contentName49: content.data.contentsControl.48.contentName
      - templateContentUuid50: content.data.contentsControl.49.contentId
      - contentCodeyp50: content.data.contentsControl.49.contentCode
      - contentName50: content.data.contentsControl.49.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC1-模版中内容域个数较多"
    api: api/esignDocs/documents/template/generatePdfFile50.yml
    variables:
       json: {
          templateId: $newTemplateUuid1,
          fileName: "自动化测试延平",
          contentsControl: [ {
            contentId: $templateContentUuid1,
            contentCode: $contentCodeyp1,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid2,
            contentCode: $contentCodeyp2,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid3,
            contentCode: $contentCodeyp3,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid4,
            contentCode: $contentCodeyp4,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid5,
            contentCode: $contentCodeyp5,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid6,
            contentCode: $contentCodeyp6,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid7,
            contentCode: $contentCodeyp7,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid8,
            contentCode: $contentCodeyp8,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid9,
            contentCode: $contentCodeyp9,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid10,
            contentCode: $contentCodeyp10,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid11,
            contentCode: $contentCodeyp11,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid12,
            contentCode: $contentCodeyp12,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid13,
            contentCode: $contentCodeyp13,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid14,
            contentCode: $contentCodeyp14,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid15,
            contentCode: $contentCodeyp15,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid16,
            contentCode: $contentCodeyp16,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid17,
            contentCode: $contentCodeyp17,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid18,
            contentCode: $contentCodeyp18,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid19,
            contentCode: $contentCodeyp19,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid20,
            contentCode: $contentCodeyp20,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid21,
            contentCode: $contentCodeyp21,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid22,
            contentCode: $contentCodeyp22,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid23,
            contentCode: $contentCodeyp23,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid24,
            contentCode: $contentCodeyp24,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid25,
            contentCode: $contentCodeyp25,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid26,
            contentCode: $contentCodeyp26,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid27,
            contentCode: $contentCodeyp27,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid28,
            contentCode: $contentCodeyp28,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid29,
            contentCode: $contentCodeyp29,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid30,
            contentCode: $contentCodeyp30,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid31,
            contentCode: $contentCodeyp31,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid32,
            contentCode: $contentCodeyp32,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid33,
            contentCode: $contentCodeyp33,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid34,
            contentCode: $contentCodeyp34,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid35,
            contentCode: $contentCodeyp35,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid36,
            contentCode: $contentCodeyp36,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid37,
            contentCode: $contentCodeyp37,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid38,
            contentCode: $contentCodeyp38,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid39,
            contentCode: $contentCodeyp39,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid40,
            contentCode: $contentCodeyp40,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid41,
            contentCode: $contentCodeyp41,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid42,
            contentCode: $contentCodeyp42,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid43,
            contentCode: $contentCodeyp43,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid44,
            contentCode: $contentCodeyp44,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid45,
            contentCode: $contentCodeyp45,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid46,
            contentCode: $contentCodeyp46,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid47,
            contentCode: $contentCodeyp47,
            contentValue: $contentValue1
          } ,{
            contentId: $templateContentUuid48,
            contentCode: $contentCodeyp48,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid49,
            contentCode: $contentCodeyp49,
            contentValue: $contentValue1
          },{
            contentId: $templateContentUuid50,
            contentCode: $contentCodeyp50,
            contentValue: $contentValue1
          }]
      }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]


- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]