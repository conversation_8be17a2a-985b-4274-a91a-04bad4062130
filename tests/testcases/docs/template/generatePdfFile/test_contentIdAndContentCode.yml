#openapi填写模板并转为pdf-模版id
- config:
    variables:
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      docNameOfFolder: "0412延平自动化测试文件夹名称1$docRandomNo"
      docCodeOfFolder: "AUTOTESTYP$docRandomNo"
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      contentFieldName1: "自动化测试内容域$docRandomNo"
      contentFieldCode1: "zdhcsypcode$docRandomNo"
      contentFieldName2: "自动化测试内容域2$docRandomNo"
      contentFieldCode2: "zdhcsypcode2$docRandomNo"
      contentFieldName3: "自动化测试内容域3$docRandomNo"
      contentFieldCode3: "zdhcsypcode3$docRandomNo"
      contentValue: "填充测试"
      contentFieldId001: "${get_randomStr_32()}"
      contentFieldId002: "${get_randomStr_32()}"
      contentFieldId003: "${get_randomStr_32()}"
      fillContent001: {
          "fieldId": "",
          "label": "自动化测试-文本0507",
          "custom": false,
          "type": "MULTILINE_TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 148,
            "height": 40,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "54",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-multiline",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentFieldCode1,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 270.1026037638566,
            "y": 757.8795146945088,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
      }
      fillContent002: {
          "fieldId": "",
          "label": "自动化测试-文本0507",
          "custom": false,
          "type": "MULTILINE_TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 148,
            "height": 40,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "54",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-multiline",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId003,
          "bizId": $contentFieldId003,
          "fieldKey": $contentFieldCode3,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 270.1026037638566,
            "y": 757.8795146945088,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
      }
      fillContent003: {
          "fieldId": "",
          "label": "自动化测试-文本0507",
          "custom": false,
          "type": "MULTILINE_TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 148,
            "height": 40,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "leadingRate": 1,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "54",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-multiline",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId003,
          "bizId": $contentFieldId003,
          "fieldKey": $contentFieldCode3,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 270.1026037638566,
            "y": 757.8795146945088,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
      }

- test:
    name: "模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - len_eq: ["content.data.contents",1]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile_resourceId1: content.data.baseFile.resourceId
      - fileType1: content.data.baseFile.fileType
      - originFile_resourceId1: content.data.originFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - eq: ["content.data.contentId",$contentId1]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: {
        "resourceId": $baseFile_resourceId1,
        "fileType": $fileType1
      }
      originFile_draft: {
        "resourceId": $originFile_resourceId1
      }
      fields_draft: [ $fillContent001 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - eq: ["content.data.0.contentId",$contentId1]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

- test:
    name: "TC6-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid1
    extract:
      - contentId01: content.data.contentsControl.0.contentId
      - contentCode01: content.data.contentsControl.0.contentCode
      - contentName01: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid1]

- test:
    name: "TC1-contentId和contentCode都不填_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId:
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: null,
                  contentCode: null,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文本域实例id与文本域域参数编码必填其一" ]
      - eq: [ "content.code",1605055 ]

- test:
    name: "TC1-contentId和contentCode都为空_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId:
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: "",
                  contentCode: "",
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文本域实例id与文本域域参数编码必填其一" ]
      - eq: [ "content.code",1605055 ]

- test:
    name: "TC2-填写的contentId不存在_且contentCode不填_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: "abcdef"
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: "abcdef",
                  contentCode: null,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605088 ]

- test:
    name: "TC2-填写的contentId不填_且contentCode不存在_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: "abcdef"
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: "",
                  contentCode: "abcdef",
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605089 ]

- test:
    name: "模版新建-setup_模版新建2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    extract:
      - editUrl2: content.data.editUrl
      - previewUrl2: content.data.previewUrl
      - templateName2: content.data.templateName
      - autoTestDocUuid2: content.data.docUid
    validate:
      - eq: ["content.success",true]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl2)}
      tmp_common_template2: ${putTempEnv(_tmp_common_template2, $tplToken_config)}
    extract:
      - contentId2: content.data.contents.0.id
      - entityId2: content.data.contents.0.entityId
    validate:
      - len_eq: ["content.data.contents",1]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template2)}
      contentId_detail: $contentId2
      entityId_detail: $entityId2
    extract:
      - baseFile2: content.data.baseFile
      - originFile2: content.data.originFile
    validate:
      - eq: ["content.data.contentId",$contentId2]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template2)}
      baseFile_draft: $baseFile2
      originFile_draft: $originFile2
      fields_draft: [ $fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName2
      contentId_draft: $contentId2
      entityId_draft: $entityId2
    validate:
      - eq: ["content.data.0.contentId",$contentId2]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

- test:
    name: "TC6-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid2
    extract:
      - contentId02: content.data.contentsControl.0.contentId
      - contentCode02: content.data.contentsControl.0.contentCode
      - contentName02: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid2]

- test:
    name: "TC6-停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data",null]
      - eq: [ "content.success",True ]

- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    extract:
      - editUrl2: content.data.editUrl
      - previewUrl2: content.data.previewUrl
      - templateName2: content.data.templateName
      - autoTestDocUuid2: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl2)}
      tmp_common_template2: ${putTempEnv(_tmp_common_template2, $tplToken_config)}
    extract:
      - contentId2: content.data.contents.0.id
      - entityId2: content.data.contents.0.entityId
    validate:
      - len_eq: ["content.data.contents",1]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template2)}
      contentId_detail: $contentId2
      entityId_detail: $entityId2
    extract:
      - baseFile2: content.data.baseFile
      - originFile2: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - eq: ["content.data.contentId",$contentId2]

- test:
    name: "TC5-删除模板与内容域关联关系"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template2)}
      baseFile_draft: $baseFile2
      originFile_draft: $originFile2
      fields_draft: [ ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName2
      contentId_draft: $contentId2
      entityId_draft: $entityId2
    validate:
      - eq: ["content.data.0.contentId",$contentId2]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

- test:
    name: "TC6-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid2
    validate:
      - len_eq: ["content.data.contentsControl",0]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid2]

- test:
    name: "TC3-填写的contentId为已删除状态_且contentCode不填_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid2
      - fileName: "自动化测试延平"
      - contentId: $contentId02
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid2,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId02,
                  contentCode: null,
                  contentValue: $contentValue
                } ] }
    validate:
      - ne: [ "content.data",null ]
      - contains: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "模版新建-setup_模版新建3"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid3: content.data.templateUuid
      - newVersion3: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
    extract:
      - editUrl3: content.data.editUrl
      - previewUrl3: content.data.previewUrl
      - templateName3: content.data.templateName
      - autoTestDocUuid3: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl3)}
      tmp_common_template3: ${putTempEnv(_tmp_common_template3, $tplToken_config)}
    extract:
      - contentId3: content.data.contents.0.id
      - entityId3: content.data.contents.0.entityId
    validate:
      - len_eq: ["content.data.contents",1]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template3)}
      contentId_detail: $contentId3
      entityId_detail: $entityId3
    extract:
      - baseFile3: content.data.baseFile
      - originFile3: content.data.originFile
    validate:
      - eq: ["content.data.contentId",$contentId3]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template3)}
      baseFile_draft: $baseFile3
      originFile_draft: $originFile3
      fields_draft: [ $fillContent003 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName3
      contentId_draft: $contentId3
      entityId_draft: $entityId3
    validate:
      - eq: ["content.data.0.contentId",$contentId3]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true]

- test:
    name: "TC6-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid3
    extract:
      - contentId03: content.data.contentsControl.0.contentId
      - contentCode03: content.data.contentsControl.0.contentCode
      - contentName03: content.data.contentsControl.0.contentName
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid3]

- test:
    name: "TC4-填写的contentId正确但不属于当前的模版_contentCode不填_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $contentId03
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId03,
                  contentCode: null,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605088 ]

- test:
    name: "TC5-填写的contentId正确且属于当前的模版_contentCode不填_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid3
      - fileName: "自动化测试延平"
      - contentId: $contentId03
      - contentCode:
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid3,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId03,
                  contentCode: null,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid3 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC6-文件名称填写正确_其他字段填写正确_提交"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $contentId01
      - contentCode: $contentCode01
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId01,
                  contentCode: $contentCode01,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC7-contentId填写错误_contentCode填写正确且属于当前模版_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: "abcdef"
      - contentCode: $contentCode01
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: "abcdef",
                  contentCode: $contentCode01,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605088 ]

- test:
    name: "TC8-contentId填写正确且属于当前模版_contentCode填写错误_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId: $contentId01
      - contentCode: "testCodeyp"
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId01,
                  contentCode: "testCodeyp",
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC9-contentId不填_contentCode填写正确且属于当前模版_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId:
      - contentCode: $contentCode01
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: null,
                  contentCode: $contentCode01,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC10-contentId不填_contentCode填写正确但不属于当前模版_其他字段填写正确"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId:
      - contentCode: $contentCode03
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: null,
                  contentCode: $contentCode03,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605089 ]

- test:
    name: "TC11-contentsControl列表中其中2条数据重复_contentId与contentCode"
    api: api/esignDocs/documents/template/generatePdfFile2.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId_generatePdfFile2: $contentId01
      - contentCode_generatePdfFile2: $contentCode01
      - contentValue_generatePdfFile2: $contentValue
      - contentId2_generatePdfFile2: $contentId01
      - contentCode2_generatePdfFile2: $contentCode01
      - contentValue2_generatePdfFile2: $contentValue
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId01,
                  contentCode: $contentCode01,
                  contentValue: $contentValue
                },{
                  contentId: $contentId01,
                  contentCode: $contentCode01,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","存在重复文本域" ]
      - eq: [ "content.code",1605045 ]

- test:
    name: "TC12-contentsControl列表中其中2条数据重复_contentId与contentId"
    api: api/esignDocs/documents/template/generatePdfFile2.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId_generatePdfFile2: $contentId01
      - contentCode_generatePdfFile2:
      - contentValue_generatePdfFile2: "填充测试"
      - contentId2_generatePdfFile2: $contentId01
      - contentCode2_generatePdfFile2:
      - contentValue2_generatePdfFile2: "填充测试2"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId01,
                  contentCode: null,
                  contentValue: "填充测试"
                },{
                  contentId: $contentId01,
                  contentCode: null,
                  contentValue: "填充测试2"
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","存在重复文本域" ]
      - eq: [ "content.code",1605045 ]

- test:
    name: "TC13-contentsControl列表中其中2条数据重复_contentCode与contentCode"
    api: api/esignDocs/documents/template/generatePdfFile2.yml
    variables:
      - templateId: $newTemplateUuid1
      - fileName: "自动化测试延平"
      - contentId_generatePdfFile2:
      - contentCode_generatePdfFile2: $contentCode01
      - contentValue_generatePdfFile2: "填充测试"
      - contentId2_generatePdfFile2:
      - contentCode2_generatePdfFile2: $contentCode01
      - contentValue2_generatePdfFile2: "填充测试2"
      - body: { "templateId": $newTemplateUuid1,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: null,
                  contentCode: $contentCode01,
                  contentValue: "填充测试"
                },{
                  contentId: null,
                  contentCode: $contentCode01,
                  contentValue: "填充测试2"
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","存在重复文本域" ]
      - eq: [ "content.code",1605045 ]

- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-停用2"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板2"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-停用3"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除模板3"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

