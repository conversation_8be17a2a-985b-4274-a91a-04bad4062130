- config:
    name: "openapi填写模板并转换为pdf，多选测试"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"

- test:
    name: "创建企业模板，带多选且内容固定"
    testcase: common/template/word-buildTemplate.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "content为多选下拉框_contentValue非选项"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: "[\"A\"]"
    validate:
      - contains: [ "content.message","格式填写错误，【多选】校验不通过，选项值为【Q|W|E】中的任意一个或多个" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "content为多选下拉框_contentValue为单个选项且包含首尾空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: " [\"Q\"] "
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "content为多选下拉框_contentValue为多个选项_非标准分隔符分割"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: "[\"Q\" ^ \"W\"]"
    validate:
      - contains: [ "content.message","格式填写错误，【多选】校验不通过，多选框传参结构不符合要求" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "content为多选下拉框_contentValue为多个选项_为标准分隔符分割"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: "[\"Q\" ,\"W\"]"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "content为多选下拉框_contentValue为多个选项为f标准分隔符分割_且包含非选项内容"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: "[\"Q\" ,\"W\",\"A\"]"
    validate:
      - contains: [ "content.message","格式填写错误，【多选】校验不通过，选项值为【Q|W|E】中的任意一个或多个" ]
      - eq: [ "content.code",1601009 ]

- test:
    name: "content为多选下拉框且非必填_contentValue为空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "danxuan"
      - contentValue: "A"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "contentCode对应多个多选内容域_和_content为多选下拉框_并设置格式"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "duoxuan"
      - contentValue: "[\"Q\" ,\"W\"]"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
