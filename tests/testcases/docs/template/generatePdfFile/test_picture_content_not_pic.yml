- config:
    name: "contentCode对应多个图片内容域"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"

- test:
    name: "创建企业模板，带文本域word模板"
    testcase: common/template/word-buildTemplate.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
- test:
    name: "上传 PNG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$PNG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - PNGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_为PNG文件"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: null
      - contentCode: "text"
      - contentValue: $PNGFileKey
    extract:
      - filekeypng: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
