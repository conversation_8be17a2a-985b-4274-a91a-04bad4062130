- config:
    name: "openapi填写模板并转换为pdf，单图片控件，"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      10MPNG: "4.png"
      10MJPG: "66.jpg"
      GIF: "temp1.gif"
      JPEG: "temp3.jpeg"
      BMP: "temp4.bmp"
      PNG: "小于1M.png"
      JPG: "jpg格式图.jpg"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"


- test:
    name: "TC1-创建企业模板，单图片企业模板，必填"
    testcase: common/template/wordTemplate_one_pic_req.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "TC2-内容域列表"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - contentCode: content.data.contentList.0.contentCode
      - contentId: content.data.contentList.0.templateContentUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "content为图片且必填_contentValue为空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: null
    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
      - eq: [ "content.message","【图片】没有填写" ]
      - eq: [ "content.code",1605046 ]
      - eq: [ "content.data",null ]

- test:
    name: "上传 10MPNG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$10MPNG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - 10MPNGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_实际图片大于10M_png"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: $10MPNGFileKey
    validate:
      - eq: [ "content.message","图片大小不能超过10M" ]
      - eq: [ "content.code",1605082 ]
      - eq: [ "content.data",null ]

- test:
    name: "上传 10MJPG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$10MJPG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - 10MJPGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_实际图片大于10M_jpg"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: $10MJPGFileKey
    validate:
      - eq: [ "content.message","图片大小不能超过10M" ]
      - eq: [ "content.code",1605082 ]
      - eq: [ "content.data",null ]


- test:
    name: "上传 GIF"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$GIF"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
#    extract:
#      - GIFFileKey: content.data.fileKey
    validate:
      - eq: [ content.status, 1610033 ]
      - eq: [ content.success, false ]
      - eq: [ content.message,"文件格式不支持" ]

#- test:
#    name: "content为图片_contentValue_为gif文件"
#    api: api/esignDocs/documents/template/generatePdfFile.yml
#    variables:
#      - templateId: $newTemplateUuidCommon
#      - fileName: "自动化测试延平"
#      - contentId: $contentId
#      - contentCode: $contentCode
#      - contentValue: $GIFFileKey
#    validate:
#      - eq: [ "content.message","图片仅支持.png,.jpeg,.jpg格式" ]
#      - eq: [ "content.code",1605081 ]
#      - eq: [ "content.data",null ]


- test:
    name: "上传 JPEG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$JPEG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - JPEGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_为jpeg文件"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: $JPEGFileKey
    extract:
      - JPEGFileKeyjpeg: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "上传 PNG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$PNG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - PNGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_为PNG文件"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: $PNGFileKey
    extract:
      - filekeypng: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "上传 JPG"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$JPG"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - JPGFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "content为图片_contentValue_为JPG文件"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: $JPGFileKey
    extract:
      - fileKey1: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "上传 BMP"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$BMP"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
#    extract:
#      - BMPFileKey: content.data.fileKey
    validate:
      - eq: [ content.status, 1610033 ]
      - eq: [ content.success, false ]
      - eq: [ content.message,"文件格式不支持" ]

#- test:
#    name: "content为图片_contentValue_为BMP文件"
#    api: api/esignDocs/documents/template/generatePdfFile.yml
#    variables:
#      - templateId: $newTemplateUuidCommon
#      - fileName: "自动化测试延平"
#      - contentId: $contentId
#      - contentCode: $contentCode
#      - contentValue: $BMPFileKey
#    validate:
#      - eq: [ "content.message","图片仅支持.png,.jpeg,.jpg格式" ]
#      - eq: [ "content.code",1605081 ]
#      - eq: [ "content.data",null ]


- test:
    name: "content为图片_contentValue_对应filekey不存在"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: "随便写个字符串.png"
    validate:
      - eq: [ "content.message","文件获取失败:随便写个字符串.png" ]
      - eq: [ "content.code",1610004 ]
      - eq: [ "content.data",null ]

- test:
    name: "content为图片_contentValue_对应filekey包含首尾空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试延平"
      - contentId: $contentId
      - contentCode: $contentCode
      - contentValue: " $JPGFileKey "
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
