#openapi填写模板并转为pdf-模版id
- config:
    variables:
      outerDomainHost: ${ENV(esign.projectOuterHost)}
      domainHost: ${ENV(esign.projectHost)}
      docRandomNo: ${get_randomNo()}
      autoTestDocUuid1: ${get_docConfig_type()}
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      contentName1: "自动化测试内容域$docRandomNo"
      contentCode1: "zdhcsypcode$docRandomNo"
      contentValue: "11"
      contentFieldId001: "${get_randomStr_32()}"
      fillContent001: {
          "fieldId": "",
          "label": "自动化测试数字-0525",
          "custom": false,
          "type": "NUM",
          "subType": null,
          "sort": 1,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 108.43,
            "height": 49,
            "bold": false,   #true
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "groupKey": "",
              "tickOptions": null,
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": "",
            "required": true,
            "dateFormat": "0",
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [ ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": "0",
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "5",
            "overflowType": "2",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-number",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [ ],
            "columnMapping": null,
            "positionMovable": null
          },
          "options": null,
          "instructions": "",
          "contentFieldId": $contentFieldId001,
          "bizId": $contentFieldId001,
          "fieldKey": $contentCode1,
          "fillGroupKey": "",
          "fieldValue": "",
          "defaultValue": "",
          "position": {
            "x": 186.00,
            "y": 674.90,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "TC5-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template3: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-epaasTemplate-batch-save-draft-文本内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template1)}
      baseFile_draft: $baseFile1
      originFile_draft: $originFile1
      fields_draft: [ $fillContent001 ]
      pageFormatInfoParam_draft: null
      name_draft: $templateName1
      contentId_draft: $contentId1
      entityId_draft: $entityId1
    extract:
      - contentId1: content.data.0.contentId
      - contentVersionId1: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5-content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    extract:
      - contentId3: content.data.contentsControl.0.contentId
      - contentCode: content.data.contentsControl.0.contentCode
    validate:
      - len_ge: ["content.data.contentsControl",1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]
      - eq: ["content.data.templateId",$newTemplateUuid]

- test:
    name: "TC1-文件名称不填_其他字段正确填写_提交"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName:
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": null,
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能为空" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC2-文件名称填写正确_其他字段填写正确_提交"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.pdfPreviewOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.pdfPreviewLongOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.pdfPreviewLongUrl, "$domainHost" ]
      - contains: [ content.data.pdfPreviewUrl, "$domainHost" ]

- test:
    name: "TC3-文件名称填写正确_包含_pdf_其他字段填写正确_提交"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平.pdf"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平.pdf",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf.pdf" ]

- test:
    name: "TC4-文件名称存在重复"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid ]
      - eq: [ "content.data.fileName","自动化测试延平.pdf" ]

- test:
    name: "TC5-文件名称长度_100"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "abcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdf延平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "abcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdfabcdf延平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名长度不能超过100" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自 动 化 测 试 延 平"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自 动 化 测 试 延 平",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid ]
      - eq: [ "content.data.fileName","自 动 化 测 试 延 平.pdf" ]

- test:
    name: "TC6-文件名称全是空格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "    "
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "    ",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能为空" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC6-文件名称包含特殊字符-支持的字符"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平@#$"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平@#$",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid ]
      - eq: [ "content.data.fileName","自动化测试延平@#$.pdf" ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符/"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平/"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平/",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符\\"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平\\"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平\\",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平:"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平:",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符*"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平*"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平*",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符\""
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平\""
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平\"",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符<"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平<"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平<",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符>"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平>"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平>",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符|"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平|"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平|",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-文件名称包含特殊字符-不支持的字符?"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuid
      - fileName: "自动化测试延平?"
      - contentId: $contentId3
      - contentCode: $contentCode
      - contentValue: $contentValue
      - body: { "templateId": $newTemplateUuid,
                "fileName": "自动化测试延平?",
                contentsControl: [ {
                  contentId: $contentId3,
                  contentCode: $contentCode,
                  contentValue: $contentValue
                } ] }
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","文件名称不能包含特殊字符" ]
      - eq: [ "content.code",1600017 ]

#- test:
#    name: "TC7-文件名称包含特殊字符-不支持的字符-emoji表情"
#    api: api/esignDocs/documents/template/generatePdfFile.yml
#    variables:
#      - templateId: $newTemplateUuid
#      - fileName: "自动化测试延平💰"
#      - contentId: $contentId3
#      - contentCode: $contentCode
#      - contentValue: $contentValue
#      - body: { "templateId": $newTemplateUuid,
#                "fileName": "自动化测试延平💰",
#                contentsControl: [ {
#                  contentId: $contentId3,
#                  contentCode: $contentCode,
#                  contentValue: $contentValue
#                } ] }
#    validate:
#      - eq: [ "content.data",null ]
#      - eq: [ "content.message","文件名称不能包含特殊字符" ]
#      - eq: [ "content.code",1600017 ]

- test:
    name: "TC7-停用"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-删除模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
