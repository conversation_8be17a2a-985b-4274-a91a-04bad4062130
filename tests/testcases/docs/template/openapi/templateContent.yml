- config:
    variables:
      docNameOfFolder: "自动化测试文件夹名称${getTimeStamp()}"
      docNameOfFolder1: "自动化测试文件夹名称1${getTimeStamp()}"
      docNameOfFolder2: "自动化测试文件夹名称2${getTimeStamp()}"
      docNameOfFolder3: "自动化测试文件夹名称3${getTimeStamp()}"
      docCodeOfFolder: "AUTOTEST${getTimeStamp()}"
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      addContentName: "自动化测试内容域"
      addContentCode: "testcode"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      posX: 34
      posY: 603
      width: 216
      height: 36
      signName: "自动化测试签署区${getTimeStamp()}"
      reName: "自动化测试签署区${getTimeStamp()}"
      timePosX: 10
      timePosY: 10
      dateFormat1: "YYYY-MM-DD"
      dateFormat2: "YYYY年MM月DD日"
      dateFormat3: "YYYY/MM/DD"
      

- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder
      - docCode: $docCodeOfFolder
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]


- test:
    name: "创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
- test:
    name: "TC7-setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateId正常启用状态"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: "templateId长度超过限制"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid+${get_randomNo_32()}
    validate:
      - eq: ["content.message","模板id最大长度36位"]
      - eq: ["content.code",1600017]

- test:
    name: "templateId为空或空格"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: ""
    validate:
      - eq: ["content.message","templateId不能为空"]
      - eq: ["content.code",1600017]

- test:
    name: "templateId为空或空格"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: " "
    validate:
      - eq: ["content.message","templateId不能为空"]
      - eq: ["content.code",1600017]

- test:
    name: "templateId不传"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: null
    validate:
      - eq: ["content.message","templateId不能为空"]
      - eq: ["content.code",1600017]


- test:
    name: "templateId包含首尾空格"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: ${addSpace($newTemplateUuid)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"

- test:
    name: "setup-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateId已删除"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.message","企业模板不存在。"]
      - eq: ["content.code",1605001]

- test:
    name: "templateId不存在"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: ${get_randomNo_36()}
    validate:
      - eq: ["content.message","企业模板不存在。"]
      - eq: ["content.code",1605001]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateId停用"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.message","该模板并未发布"]
      - eq: ["content.code",1605011]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateId草稿"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.message","该模板并未发布"]
      - eq: ["content.code",1605011]
    teardown_hooks:
      - ${sleep(0.5)}
- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    times: 5
    variables:
      - description: null
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - contentUuid: "AUTOUUIDADD${getTimeStamp()}"
      - contentCode: "AUTOCODEADD${getTimeStamp()}"
      - contentName: $addContentName+${getTimeStamp()}
      - formatType: 0
      - formatRule: "28"
      - edgeScope: 0
      - pageNo: 1

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-添加签名域"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    times: 5
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1-6"
      - posX: 10
      - posY: 10
      - name: $signName+${getTimeStamp()}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模板相关信息"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.templateId",$newTemplateUuid]
      - length_greater_than: ["content.data.createTime", 0]
      - length_greater_than: ["content.data.updateTime", 0]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: "文件类型相关信息"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.docTypeId",$autoTestDocUuid1]
      - eq: ["content.data.docTypeName",$docNameOfFolder]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "内容域集合实例，签署区集合实例"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - len_eq: ["content.data.contentsControl",0]
      - len_eq: ["content.data.sealControl",0]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft--5个内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      fields_draft: ${getEpaasTemplateContent(5,0,1)}
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]



- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模板中只包含了内容域但是没有签名域"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - len_eq: ["content.data.contentsControl",5]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]




- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "epaasTemplate-batch-save-draft--5个签署区"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      fields_draft: ${getEpaasTemplateContent(0,2,1)}
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC8-模板中只包含了签名域没有内容域"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - len_eq: ["content.data.contentsControl",0]
      - len_ge: ["content.data.sealControl",2]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]




- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模板中即没有签名域也没有内容域"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - len_eq: ["content.data.contentsControl",0]
      - len_eq: ["content.data.sealControl",0]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]



- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder1
      - docCode: $docCodeOfFolder+${getTimeStamp()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder1
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC7-setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板的文件类型是挂在第一层文件类型主目录下"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.docTypeId",$autoTestDocUuid1]
      - eq: ["content.data.docTypeName",$docNameOfFolder1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder1
      - docCode: $docCodeOfFolder+${getTimeStamp()}
      - docType: 1
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder1
      - docType: 1
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "setup-在parentUid目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder2
      - docCode: $docCodeOfFolder+${getTimeStamp()}
      - docType: 1
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder2
      - docType: 1
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "setup-在parentUid目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder3
      - docCode: $docCodeOfFolder+${getTimeStamp()}
      - docType: 2
      - parentUid: $autoTestDocUuid1
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder3
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC7-setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板的文件类型是挂在第三层级的文件类型目录下"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.docTypeId",$autoTestDocUuid1]
      - eq: ["content.data.docTypeName",$docNameOfFolder3]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]




- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft--200个内容域,200个签署区"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      fields_draft: ${getEpaasTemplateContent(200,200,1)}
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


#- test:
#    name: "setup_添加内容域"
#    api: api/esignDocs/template/owner/contentAdd.yml
#    times: 200
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuid
#      - version: $newVersion
#      - contentUuid: "AUTOUUIDADD${getTimeStamp()}"
#      - contentCode: "AUTOCODEADD${getTimeStamp()}"
#      - contentName: $addContentName+${getTimeStamp()}
#      - formatType: 0
#      - formatRule: "28"
#      - edgeScope: 0
#      - pageNo: 1
#
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC200-setup-添加签名域"
#    api: api/esignDocs/template/owner/signatoryAdd.yml
#    times: 200
#    variables:
#      - templateUuid: $newTemplateUuid
#      - version: $newVersion
#      - addSignTime: 0
#      - signType: 2
#      - dateFormat: "yyyy-MM-dd"
#      - edgeScope: null
#      - pageNo: "1-6"
#      - posX: 10
#      - posY: 10
#      - name: $signName+${getTimeStamp()}
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data", null]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模板中包含了200个内容域和200个签名域的场景"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - len_eq: ["content.data.contentsControl",200]
      - len_eq: ["content.data.sealControl",200]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]




- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-骑缝章指定2-5"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "骑缝章"
      type: "QF_SIGN"
      width: 120
      height: 120
      posx: 63
      posY: 250
      page: "2-5"
      scope: "appoint"
      entityId_signatory: $_entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]



- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "骑缝章的指定页2-5的出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "2-5"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-骑缝章奇数"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "骑缝章"
      type: "QF_SIGN"
      width: 120
      height: 120
      posx: 63
      posY: 250
      page: null
      scope: "interval"
      entityId_signatory: $_entityId
      intervalType: "odd"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "骑缝章的奇数页的出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", null]
      - eq: ["content.data.sealControl.0.edgeScope", 1]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-骑缝章指定"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "骑缝章"
      type: "QF_SIGN"
      page: null
      width: 120
      height: 120
      posx: 63
      posY: 250
      scope: "interval"
      entityId_signatory: $_entityId
      intervalType: "even"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "骑缝章的偶数页的出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", null]
      - eq: ["content.data.sealControl.0.edgeScope", 2]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-骑缝章指定2-5"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "普通章单页"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "普通章的单页出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "1"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-骑缝章指定2-5"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "普通章多页"
      page: "2-5"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "普通章的多页出参校验_1-3_5-7_9_"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "2-5"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-普通章指定1,4-5"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "普通章指定页"
      page: "1,4-5"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "普通章的多页出参校验_1-3_5-7_9_"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "1,4-5"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${getTimeStamp()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域-签署日期"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "普通章指定页"
      page: "1,4-5"
      scope: "appoint"
      dateFormat: "yyyy年MM月dd日"
      dateRule: "1"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]



- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "签署日期格式_三种都有的展示_"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: [content.data.sealControl.0.signDateInfo.dateFormat, "yyyy年MM月dd日"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]


















