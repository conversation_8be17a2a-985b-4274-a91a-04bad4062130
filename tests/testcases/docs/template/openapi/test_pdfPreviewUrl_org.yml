#针对查询模板预览页面open api 出参校验
- config:
    name: "针对查询模板预览页面open api 出参校验"
    variables:
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      docNameOfFolder: "openAPI自动化测试文件夹名称"
      docCodeOfFolder: "openApi-AUTOTEST"
      commonTemplateName: "openAPi自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      addContentName: "openApi自动化测试内容域"
      addContentCode: "openApi-testcode"
      dataSource: null
      sourceField: null
      formatType: 0
      formatRule: "28"
      required: 0
      length: 28
      user_account1: ${ENV(ceswdzxzdhyhwgd1.account)}
      varAccount: "ceswdzxzdhyhwgd1.account"
      varPassword: "ceswdzxzdhyhwgd1.password"



#-----------所属机构为指定机构----1--------
- test:
    name: "TC1-获取用户列表"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      userCode: $user_account1
    extract:
      - organizationId: content.data.organizationId
      - organizationCode: content.data.organizationCode
      - organizationName: content.data.organizationName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC2-setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo_16()}
      - docCode: $docCodeOfFolder+${get_randomNo_16()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "TC4-创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 2
      - organizeRange: [{organizationCode: $organizationCode}]
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"
      - timePosX: 10
      - timePosY: 10

- test:
    name: "TC6-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

- test:
    name: "TC8-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid
      - account: "ceswdzxzdhyhwgd1.account"
      - password: "ceswdzxzdhyhwgd1.password"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


##----父级机构用户登录---2----
- test:
    name: "TC9-根据上级组织id查询组织列表"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - parentOrganizationId: $organizationId
      - organizationTerritory: null
    extract:
      - sonOrganizationId: content.data.organizeList.0.id
      - sonOrganizationCode: content.data.organizeList.0.organizationCode
      - sonOrganizationName: content.data.organizeList.0.organizationName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC10-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - account: $varAccount
      - password: $varPassword
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 2
      - organizeRange: [{organizationCode: $sonOrganizationCode}]
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"
      - timePosX: 10
      - timePosY: 10

- test:
    name: "TC11-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - account: $varAccount
      - password: $varPassword
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC12-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]


#----模板管理员登录---3----
- test:
    name: "TC14-根据上级组织id查询组织列表"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - parentOrganizationId: 0
      - organizationTerritory: "1"
    extract:
      - firstOrganizationId: content.data.organizeList.0.id
      - firstOrganizationCode: content.data.organizeList.0.organizationCode
      - firstOrganizationName: content.data.organizeList.0.organizationName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC15-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - account: $varAccount
      - password: $varPassword
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 2
      - organizeRange: [{organizationCode: $firstOrganizationCode}]
    extract:
      - newTemplateUuid3: content.data.templateUuid
      - newVersion3: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区--甲方个人"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: "甲方个人"
      - timePosX: 10
      - timePosY: 10

- test:
    name: "TC16-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - account: $varAccount
      - password: $varPassword
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC17-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

#5月31日修改：有查看权限
- test:
    name: "TC18-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - account: $varAccount
      - password: $varPassword
      - templateUuid: $newTemplateUuid3
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success",true]



##-----修改为无权限后重新登录----4-----
##with_son_user_accountt_val需要配置兼职机构
#- test:
#    name: "TC19-模版新建-setup_模版新建"
#    api: api/esignDocs/template/owner/templateAdd.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - fileKey: $fileKey
#      - zipFileKey: null
#      - templateName: $commonTemplateName+${get_randomNo()}
#      - createUserOrg: $createUserOrgCode
#      - description: $description
#      - docUuid: $autoTestDocUuid1
#      - allRange: 2
#      - organizeRange: [ { organizationCode: $firstOrganizationCode },{ organizationCode: $userPartTimeOrgCode } ]
#    extract:
#      - newTemplateUuid4: content.data.templateUuid
#      - newVersion4: content.data.version
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC20-setup_发布"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid4
#      - version: $newVersion4
#      - isPublish: true
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "TC21-查询模板预览页面"
#    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
#    variables:
#      - templateId: $newTemplateUuid4
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC22-模板预览"
#    api: api/esignDocs/template/templateView.yml
#    variables:
#      - account: $with_son_user_accountt
#      - password: $with_son_user_password
#      - templateUuid: $newTemplateUuid4
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC23-setup-停用模版"
#    api: api/esignDocs/template/owner/templateSuspend.yml
#    variables:
#      - templateUuid: $newTemplateUuid4
#      - version: $newVersion4
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC24-编辑模版"
#    api: api/esignDocs/template/templateUpdate.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid4
#      - version: $newVersion4
#      - fileKey: $fileKey
#      - zipFileKey: null
#      - templateName: $commonTemplateName+${get_randomNo()}
#      - createUserOrg: $createUserOrgCode
#      - description: $description
#      - docUuid: $autoTestDocUuid1
#      - allRange: 2
#      - organizeRange: [ { organizationCode: $firstOrganizationCode } ]
#
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC20-setup_发布"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid4
#      - version: $newVersion4
#      - isPublish: true
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "TC25-查询模板预览页面"
#    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
#    variables:
#      - templateId: $newTemplateUuid4
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC26-模板预览"
#    api: api/esignDocs/template/templateView.yml
#    variables:
#      - account: $with_son_user_accountt
#      - password: $with_son_user_password
#      - templateUuid: $newTemplateUuid4
#    validate:
#      - eq: ["content.message","用户无查看模板权限"]
#      - eq: ["content.status",1605038]
#      - eq: ["content.success",false]
#
##----子级机构用户登录----5-----
#- test:
#    name: "TC3-setup-列表搜索上个用例新增的的文件夹信息"
#    api: api/esignDocs/docConfigure/getDocConfigureList.yml
#    variables:
#      - docName: $docNameOfFolder
#      - docType: 2
#      - parentUid: null
#    extract:
#      - autoTestDocUuid1: content.data.0.docUuid
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - length_greater_than_or_equals: ["content.data.0.docUuid",32]
#
#- test:
#    name: "TC4-创建人名称-组织自动带出"
#    api: api/esignDocs/user/getUserOrg.yml
#    variables:
#      - userCode: null
#    extract:
#      - createUserOrgCode: content.data.organizationCode
#      - createUserCode: content.data.userCode
#    validate:
#      - length_greater_than_or_equals: ["content.data.organizationCode",1]
#      - length_greater_than_or_equals: ["content.data.userCode",1]
#      - length_greater_than_or_equals: ["content.data.orgList",1]
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC27-根据上级组织id查询组织列表"
#    api: api/esignDocs/organize/getOrganizationListByPId.yml
#    variables:
#      - parentOrganizationId: 0
#      - organizationTerritory: "1"
#    extract:
#      - firstOrganizationId: content.data.organizeList.0.id
#      - firstOrganizationCode: content.data.organizeList.0.organizationCode
#      - firstOrganizationName: content.data.organizeList.0.organizationName
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC28-获取用户列表"
#    api: api/esignDocs/user/getUserList.yml
#    variables:
#      userCode: null
#      accountNumber: $second_user_accountt_val
#      userTerritory: 1
#    extract:
#      - second_no_template_organizationId: content.data.userList.0.organizationId
#      - second_no_template_organizationCode: content.data.userList.0.organizationCode
#      - second_no_template_organizationName: content.data.userList.0.organizationName
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC29-查上级组织ID--根据组织ID获取组织机构信息"
#    api: api/esignDocs/managePlatform/org/getOrganizationById.yml
#    variables:
#      organizationId: $second_no_template_organizationId
#    extract:
#      - second_no_template_parent_organizationId: content.data.parentOrganizationId
#      - second_no_template_parent_organizationName: content.data.parentOrganizationName
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data",null]
#
#
#- test:
#    name: "TC30-根据组织ID获取组织机构信息"
#    api: api/esignDocs/managePlatform/org/getOrganizationById.yml
#    variables:
#      organizationId: $second_no_template_parent_organizationId
#    extract:
#      - second_no_template_parent_organizationCode: content.data.organizationCode
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data",null]
#
#- test:
#    name: "TC31-模版新建-setup_模版新建"
#    api: api/esignDocs/template/owner/templateAdd.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - fileKey: $fileKey
#      - zipFileKey: null
#      - templateName: $commonTemplateName+${get_randomNo()}
#      - createUserOrg: $createUserOrgCode
#      - description: $description
#      - docUuid: $autoTestDocUuid1
#      - allRange: 2
#      - organizeRange: [ { organizationCode: $second_no_template_parent_organizationCode } ]
#    extract:
#      - newTemplateUuid5: content.data.templateUuid
#      - newVersion5: content.data.version
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "32-setup_发布"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid5
#      - version: $newVersion5
#      - isPublish: true
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "TC33-查询模板预览页面"
#    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
#    variables:
#      - templateId: $newTemplateUuid5
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC34-模板预览"
#    api: api/esignDocs/template/templateView.yml
#    variables:
#      - account: $second_user_accountt
#      - password: $second_user_password
#      - templateUuid: $newTemplateUuid5
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - ne: [ "content.data",null ]
#
#
##---------有权限内部用户登录-----6-----ok--
#- test:
#    name: "TC35-获取用户列表"
#    api: api/esignDocs/user/getUserList.yml
#    variables:
#      userCode: null
#      accountNumber: $no_template_account_value
#      userTerritory: 1
#    extract:
#      - organizationId6: content.data.userList.0.organizationId
#      - organizationCode6: content.data.userList.0.organizationCode
#      - organizationName6: content.data.userList.0.organizationName
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC36-模版新建-setup_模版新建"
#    api: api/esignDocs/template/owner/templateAdd.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - fileKey: $fileKey
#      - zipFileKey: null
#      - templateName: $commonTemplateName+${get_randomNo()}
#      - createUserOrg: $createUserOrgCode
#      - description: $description
#      - docUuid: $autoTestDocUuid1
#      - allRange: 2
#      - organizeRange: [ { organizationCode: $organizationCode6 } ]
#    extract:
#      - newTemplateUuid6: content.data.templateUuid
#      - newVersion6: content.data.version
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC37-setup_发布"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid6
#      - version: $newVersion6
#      - isPublish: true
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "TC38-查询模板预览页面"
#    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
#    variables:
#      - templateId: $newTemplateUuid6
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC39-模板预览"
#    api: api/esignDocs/template/templateView.yml
#    variables:
#      - account: $with_son_user_accountt
#      - password: $with_son_user_password
#      - templateUuid: $newTemplateUuid6
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - ne: [ "content.data",null ]
#
##-----兼职机构为指定机构------7-----ok--
#- test:
#    name: "TC40-获取用户列表"
#    api: api/esignDocs/user/getUserList.yml
#    variables:
#      userCode: null
#      accountNumber: $user_accountt_two_val
#      userTerritory: 1
#    extract:
#      - organizationId7: content.data.userList.0.organizationId
#      - organizationCode7: content.data.userList.0.organizationCode
#      - organizationName7: content.data.userList.0.organizationName
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC41-模版新建-setup_模版新建"
#    api: api/esignDocs/template/owner/templateAdd.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - fileKey: $fileKey
#      - zipFileKey: null
#      - templateName: $commonTemplateName+${get_randomNo()}
#      - createUserOrg: $createUserOrgCode
#      - description: $description
#      - docUuid: $autoTestDocUuid1
#      - allRange: 2
#      - organizeRange: [ { organizationCode: $organizationCode7 } ]
#    extract:
#      - newTemplateUuid7: content.data.templateUuid
#      - newVersion7: content.data.version
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC42-setup_发布"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - account: $varAccount
#      - password: $varPassword
#      - templateUuid: $newTemplateUuid7
#      - version: $newVersion7
#      - isPublish: true
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#- test:
#    name: "TC43-查询模板预览页面"
#    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
#    variables:
#      - templateId: $newTemplateUuid7
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.code",200 ]
#      - ne: [ "content.data",null ]
#
#- test:
#    name: "TC44-模板预览"
#    api: api/esignDocs/template/templateView.yml
#    variables:
#      - account: $with_son_user_accountt
#      - password: $with_son_user_password
#      - templateUuid: $newTemplateUuid7
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - ne: [ "content.data",null ]













