#针对查询模板预览页面open api 出参校验
- config:
    name: "针对查询模板预览页面open api 出参校验"
    variables:
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      docNameOfFolder: "openAPI自动化测试文件夹名称"
      docCodeOfFolder: "openApi-AUTOTEST"
      commonTemplateName: "openAPi自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      addContentName: "openApi自动化测试内容域"
      addContentCode: "openApi-testcode"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      formatType: 0
      formatRule: "28"
      required: 0
      length: 28
      pageNo: 1
      posX: 34
      posY: 603
      width: 216
      height: 36
      signName: "openAPi自动化测试签署区${get_randomNo()}"
      reName: "openAPi自动化测试签署区${get_randomNo()}"
      timePosX: 10
      timePosY: 10
#      outer_user_account: "wsignwb01.accountNo"
#      outer_user_password: "wsignwb01.password"
#      phoneOrMail: "wsignwb01.account.encrypt" ###想要外部用户能够查看只能系统参数设置公开访问
#      account1: "ceswdzxzdhyhwgd1.account"
#      password1: "ceswdzxzdhyhwgd1.password"
      account1: "${ENV(sign01.accountNo)}"
      password1: "ceswdzxzdhyhwgd1.password"
      outerDomainHost: ${ENV(esign.projectOuterHost)}
      domainHost: ${ENV(esign.projectHost)}

#----内容域----1----
- test:
    name: "TC1-setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo_16()}
      - docCode: $docCodeOfFolder+${get_randomNo_16()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "TC3-创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC10-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC11-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

- test:
    name: "TC12-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - account: $account1
      - password: $password1
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


#----签名域----2----
- test:
    name: "TC13-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]




- test:
    name: "TC17-盖章页-所有页"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC18-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC19-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templatePreviewUrl, "$domainHost" ]
      - contains: [ content.data.templatePreviewOuterUrl, "$outerDomainHost" ]


- test:
    name: "TC20-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - account: $account1
      - password: $password1
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#-----模板文件---3-----

- test:
    name: "TC21-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid3: content.data.templateUuid
      - newVersion3: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "TC24-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - version: $newVersion3
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC25-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid3
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templatePreviewUrl, "$domainHost" ]
      - contains: [ content.data.templatePreviewOuterUrl, "$outerDomainHost" ]


- test:
    name: "TC26-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid3
      - account: $account1
      - password: $password1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


#-----模板取消发布后重新登录----4---
- test:
      name: "TC27-模版新建-setup_模版新建"
      api: api/esignDocs/template/owner/templateAdd.yml
      variables:
        - fileKey: $fileKey
        - zipFileKey: null
        - templateType: 1
        - templateName: $commonTemplateName+${get_randomNo()}
        - createUserOrg: $createUserOrgCode
        - description: $description
        - docUuid: $autoTestDocUuid1
        - allRange: 1
        - organizeRange: null
      extract:
        - newTemplateUuid4: content.data.templateUuid
        - newVersion4: content.data.version
      validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]



- test:
    name: "TC28-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - version: $newVersion4
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC29-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid4
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templatePreviewUrl, "$domainHost" ]
      - contains: [ content.data.templatePreviewOuterUrl, "$outerDomainHost" ]


- test:
    name: "TC30-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - account: $account1
      - password: $password1
    validate:
      - eq: ["content.message","成功"]
      - ne: [ "content.data",null ]
      - eq: ["content.status",200]


- test:
    name: "31-setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - version: $newVersion4
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC32-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid4
    validate:
      - eq: [ "content.message","该模板并未发布" ]
      - eq: [ "content.code",1605011 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC33-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid4
      - account: $account1
      - password: $password1
    validate:
      - eq: [ "content.message","该模板并未发布" ]
      - eq: [ "content.status",1605011 ]
      - eq: [ "content.data",null ]

#----模板删除后重新登录------5------
- test:
    name: "TC34-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid5: content.data.templateUuid
      - newVersion5: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]




- test:
    name: "TC35-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid5
      - version: $newVersion5
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC36-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid5
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]


- test:
    name: "TC37-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid5
      - account: $account1
      - password: $password1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC39-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid5
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]


- test:
    name: "TC40-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid5
      - account: $account1
      - password: $password1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "38-setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid5
      - version: $newVersion5
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "41-setup-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid5
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC42-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid5
    validate:
      - eq: [ "content.message","企业模板不存在。" ]
      - eq: [ "content.code",1605001 ]
      - eq: [ "content.data",null ]


- test:
    name: "TC43-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid5
      - account: $account1
      - password: $password1
    validate:
      - eq: [ "content.message","企业模板不存在。" ]
      - eq: [ "content.status",1605001 ]
      - eq: [ "content.data",null ]

#-----外部用户登录---6-----
- test:
    name: "TC44-模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid6: content.data.templateUuid
      - newVersion6: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC45-setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid6
      - version: $newVersion6
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC46-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuid6
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templatePreviewUrl, "$domainHost" ]
      - contains: [ content.data.templatePreviewOuterUrl, "$outerDomainHost" ]

- test:
    name: "TC47-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - account: $account1
      - password: $password1
      - templateUuid: $newTemplateUuid6
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC48-模板预览-外部用户无权查看"
    api: api/esignDocs/template/templateView.yml
    variables:
        phoneOrMail: "wsignwb01.account.encrypt"
        templateUuid: $newTemplateUuid6
    validate:
      - eq: [ "content.message","用户无查看模板权限" ]
      - eq: [ "content.status",1605038 ]
      - eq: [ "content.success",False ]





