#open api word模板查看预览页面---
- config:
    name: "open api word模板查看预览页面"


#---------右侧内容域列表-------------
- test:
    name: "TC1-创建企业模板"
    testcase: common/template/word-buildTemplate.yml
    extract:
      - newTemplateUuidCommon


- test:
    name: "TC2-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuidCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

- test:
    name: "TC3-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - account: "ceswdzxzdhyhwgd1.account"
      - password: "ceswdzxzdhyhwgd1.password"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]





















