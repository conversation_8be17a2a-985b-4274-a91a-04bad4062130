#open api word模板查看预览页面---
- config:
    name: "open api word模板查看预览页面"



- test:
    name: "TC1-创建企业模板"
    testcase: common/template/word-buildTemplate-withTable.yml
    extract:
      - newTemplateUuidCommon


- test:
    name: "TC2-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuidCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]

- test:
    name: "TC3-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - account: "ceswdzxzdhyhwgd1.account"
      - password: "ceswdzxzdhyhwgd1.password"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "content是表格_columnContentId_columnContentCode都为空_columnValue非空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: null
      - contentCode: null
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","文本域实例id与文本域域参数编码必填其一" ]
      - eq: [ "content.code",1605055 ]

- test:
    name: "content是表格_columnContentId为空_columnValue非空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: null
      - contentCode: "tablecode01"
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "setup-模版详情"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: 1
    extract:
      - newContentId: content.data.contentList.0.templateContentUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "content是表格_columnContentId为空_columnValue非空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: $newContentId
      - contentCode: null
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]


- test:
    name: "content是表格_columnContentId_columnContentCode都不为空_columnValue非空"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: $newContentId
      - contentCode: "tablecode01"
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]


- test:
    name: "content是表格_columnContentId超过长度限制"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: "testid00000000000000000000000000000000000000000000000000000000000000001"
      - contentCode: null
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","模板id长度不能超过36" ]
      - eq: [ "content.code",1600017 ]



- test:
    name: "content是表格_columnContentCode超过长度限制"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: null
      - contentCode: "testcode00000000000000000000000000000000000000000000000000000000000000001"
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","内容控件id长度不能超过50" ]
      - eq: [ "content.code",1600017 ]


- test:
    name: "content是表格_columnContentId为不存_columnContentCode为存在"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: "testid001"
      - contentCode: "tablecode01"
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - contains: [ "content.message","文本域未找到" ]
      - eq: [ "content.code",1605088 ]


- test:
    name: "content是表格_columnContentId为存在_columnContentCode为不存在"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: $newContentId
      - contentCode: "notexisttestcode01"
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"}]},{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容11\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容12\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容13\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容14\"}]}]}"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]

- test:
    name: "content是表格_columnContent存在列未填充"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: $newContentId
      - contentCode: null
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"}]}]}"
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]


- test:
    name: "content是表格_columnContent存在多余列"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - templateId: $newTemplateUuidCommon
      - fileName: "自动化测试test"
      - contentId: $newContentId
      - contentCode: null
      - contentValue: "{\"form\":[{\"row\":[{\"columnContentCode\":\"column1\",\"columnContentValue\":\"内1容01\"},{\"columnContentCode\":\"column2\",\"columnContentValue\":\"内1容02\"},{\"columnContentCode\":\"column3\",\"columnContentValue\":\"内1容03\"},{\"columnContentCode\":\"column4\",\"columnContentValue\":\"内1容04\"},{\"columnContentCode\":\"column5\",\"columnContentValue\":\"内1容05\"},{\"columnContentCode\":\"column6\",\"columnContentValue\":\"内1容06\"}]}]}"
    validate:
      - contains: [ "content.message","不存在" ]
      - eq: [ "content.code",1605073 ]



