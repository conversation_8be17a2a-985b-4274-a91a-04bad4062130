#open api word模板查看预览页面---
- config:
    name: "open api word模板查看预览页面"
    variables:
      - domainHost: ${ENV(esign.projectHost)}
      - outerDomainHost: ${ENV(esign.projectOuterHost)}
#---------控件加载----------------
- test:
    name: "TC1-创建企业模板"
    testcase: common/template/word-buildTemplate-withAllCtl.yml
    extract:
      - newTemplateUuidCommon


- test:
    name: "TC2-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuidCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templatePreviewUrl, "$domainHost" ]
      - contains: [ content.data.templatePreviewOuterUrl, "$outerDomainHost" ]

- test:
    name: "TC3-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - account: "ceswdzxzdhyhwgd1.account"
      - password: "ceswdzxzdhyhwgd1.password"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - len_eq: [ "content.data.contentList",11 ]
      - len_eq: [ "content.data.signatoryList",1 ]




















