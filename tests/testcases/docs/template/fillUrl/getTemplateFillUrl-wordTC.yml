- config:
    name: openapi获取文件模板填写页-word文档模板
    variables:
      businessNo: "${get_randomNo_16()}"
      fillNotifyUrl: ${ENV(notifyUrl)}
      PicFileKey:  ${ENV(pngPageFileKey)}
      expirationDefaultDate: ${getDateTime(30, 2)}
      htmlFileName: "word-测试模版有默认值.html"




- test:
    name: "创建文档模板-内容域有默认值"
    testcase: common/template/word-bulidTemplate-defaultContentValue.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon



- test:
    name: "TC-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentIdPic1: content.data.contentsControl.0.contentId
      - contentCodePic1: content.data.contentsControl.0.contentCode
      - defaultContentValuePic1: content.data.contentsControl.0.defaultContentValue
      - contentIdPic2: content.data.contentsControl.1.contentId
      - contentCodePic2: content.data.contentsControl.1.contentCode
      - defaultContentValuePic2: content.data.contentsControl.1.defaultContentValue
      - contentIdSelDan: content.data.contentsControl.2.contentId
      - contentCodeSelDan: content.data.contentsControl.2.contentCode
      - defaultContentValueSelDan: content.data.contentsControl.2.defaultContentValue
      - contentIdSelDuo: content.data.contentsControl.3.contentId
      - contentCodeSelDuo: content.data.contentsControl.3.contentCode
      - defaultContentValueSelDuo: content.data.contentsControl.3.defaultContentValue
      - contentIdForm: content.data.contentsControl.4.contentId
      - contentCodeForm: content.data.contentsControl.4.contentCode
      - defaultContentValueForm: content.data.contentsControl.4.defaultContentValue
      - contentIdText: content.data.contentsControl.5.contentId
      - contentCodeText: content.data.contentsControl.5.contentCode
      - defaultContentValueText: content.data.contentsControl.5.defaultContentValue
      - contentIdNum: content.data.contentsControl.6.contentId
      - contentCodeNum: content.data.contentsControl.6.contentCode
      - defaultContentValueNum: content.data.contentsControl.6.defaultContentValue
      - contentIdTel: content.data.contentsControl.7.contentId
      - contentCodeTel: content.data.contentsControl.7.contentCode
      - defaultContentValueTel: content.data.contentsControl.7.defaultContentValue
      - contentIdDate: content.data.contentsControl.8.contentId
      - contentCodeDate: content.data.contentsControl.8.contentCode
      - defaultContentValueDate: content.data.contentsControl.8.defaultContentValue
      - contentIdMail: content.data.contentsControl.9.contentId
      - contentCodeMail: content.data.contentsControl.9.contentCode
      - defaultContentValueMail: content.data.contentsControl.9.defaultContentValue
      - contentIdIDCard: content.data.contentsControl.10.contentId
      - contentCodeIDCard: content.data.contentsControl.10.contentCode
      - defaultContentValueIDCard: content.data.contentsControl.10.defaultContentValue
      - contentIdCode: content.data.contentsControl.11.contentId
      - contentCodeCode: content.data.contentsControl.11.contentCode
      - defaultContentValueCode: content.data.contentsControl.11.defaultContentValue



- test:
    name: "TC1-contentValue为空，内容域赋值空"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: "" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC2-验证contentValue为空，内容域赋值空-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - eq: [ content.data.contentList.0.contentValue, ""]


- test:
    name: "TC2-contentValue为空,editFillingValue为1"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: "" ,
            editFillingValue: 1
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC3-验证contentValue为空,editFillingValue为1-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - eq: [ content.data.contentList.0.contentValue, ""]
      - eq: [ content.data.contentList.0.editFillingValue, 1]

- test:
    name: "TC4-contentValue为空,editFillingValue为null"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: "" ,
            editFillingValue: null
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC5-contentValue为空,editFillingValue为null-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - eq: [ content.data.contentList.0.contentValue, ""]
      - eq: [ content.data.contentList.0.editFillingValue, 1]

- test:
    name: "TC6-contentValue为空,editFillingValue不传"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: ""
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC7-验证contentValue为空,editFillingValue不传-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - eq: [ content.data.contentList.0.contentValue, ""]
      - eq: [ content.data.contentList.0.editFillingValue, 1]

- test:
    name: "TC8-contentValue传值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: $PicFileKey,
            editFillingValue: 1
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC9-验证contentValue传值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - contains: [ content.data.contentList.0.contentValue, "$PicFileKey"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]


- test:
    name: "TC10-contentValue传null，为默认值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdPic1",
            contentValue: null,
            editFillingValue: 1
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC11-验证contentValue传null，为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - contains: [ content.data.contentList.0.contentValue, "$defaultContentValuePic1"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]




- test:
    name: "TC12-全部内容域都不传"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC13-验证全部内容域都不传，内容域赋值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - contains: [ content.data.contentList.0.contentValue, "$defaultContentValuePic1"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdPic2" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodePic2" ]
      - eq: [ content.data.contentList.1.contentValue, $defaultContentValuePic2 ]
      - eq: [ content.data.contentList.1.editFillingValue, 1]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdSelDan" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeSelDan" ]
      - eq: [ content.data.contentList.2.contentValue, "$defaultContentValueSelDan" ]
      - eq: [ content.data.contentList.2.editFillingValue, 1]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdSelDuo" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeSelDuo" ]
      - eq: [ content.data.contentList.3.contentValue, "$defaultContentValueSelDuo" ]
      - eq: [ content.data.contentList.3.editFillingValue, 1]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdForm" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeForm" ]
      - eq: [ content.data.contentList.4.contentValue, "$defaultContentValueForm" ]
      - eq: [ content.data.contentList.4.editFillingValue, 1]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdText" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeText" ]
      - eq: [ content.data.contentList.5.contentValue, "$defaultContentValueText" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.6.contentValue, "$defaultContentValueNum" ]
      - eq: [ content.data.contentList.6.editFillingValue, 1]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdTel" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeTel" ]
      - eq: [ content.data.contentList.7.contentValue, "$defaultContentValueTel" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1 ]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdDate" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeDate" ]
      - eq: [ content.data.contentList.8.contentValue, "$defaultContentValueDate" ]
      - eq: [ content.data.contentList.8.editFillingValue, 1]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdMail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeMail" ]
      - eq: [ content.data.contentList.9.contentValue, "$defaultContentValueMail" ]
      - eq: [ content.data.contentList.9.editFillingValue, 1]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.10.contentValue, "$defaultContentValueIDCard" ]
      - eq: [ content.data.contentList.10.editFillingValue, 1]
      - eq: [ content.data.contentList.11.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.11.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.11.contentValue, "$defaultContentValueCode" ]
      - eq: [ content.data.contentList.11.editFillingValue, 1]


- test:
    name: "TC14-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "TC15-全部内容域都不传，内容域赋值为默认值-提交任务"
    api: api/esignDocs/template/fillTask/submit.yml
    variables:
        fillTaskId: $fillTaskId,
        contentsControl: [
          {
            "contentId": "$contentIdPic1",
            "contentValue": "$defaultContentValuePic1"
          },
          {
            "contentId": "$contentIdPic2",
            "contentValue": "$defaultContentValuePic2"
          },
          {
            "contentId": "$contentIdSelDan",
            "contentValue": "$defaultContentValueSelDan"
          },
          {
            "contentId": "$contentIdSelDuo",
            "contentValue": "$defaultContentValueSelDuo"
          },
          {
            "contentId": "$contentIdForm",
            "contentValue": "$defaultContentValueForm"
          },
          {
            "contentId": "$contentIdText",
            "contentValue": "$defaultContentValueText"
          },
          {
            "contentId": "$contentIdNum",
            "contentValue": "$defaultContentValueNum"
          },
          {
            "contentId": "$contentIdTel",
            "contentValue": "$defaultContentValueTel"
          },
          {
            "contentId": "$contentIdDate",
            "contentValue": "$defaultContentValueDate"
          },
          {
            "contentId": "$contentIdMail",
            "contentValue": "$defaultContentValueMail"
          },
          {
            "contentId": "$contentIdIDCard",
            "contentValue": "$defaultContentValueIDCard"
          },
          {
            "contentId": "$contentIdCode",
            "contentValue": "$defaultContentValueCode"
          }
        ]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, true]
      - eq: [ content.data.taskId, $fillTaskId]
      - ne: [ content.data.fileKey, ""]
      - contains: [ content.data.downloadUrl, "http"]
      - contains: [ content.data.downloadOuterUrl, "http"]



- test:
    name: "TC16-全部内容域传值，传空，传null，不传，editFillingValue不传，默认为1"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [
            {
            contentId: "$contentIdPic1" ,
            contentValue: "$PicFileKey" } ,
            {
            contentId: "$contentIdPic2" ,
            contentValue: "" }  ,
            {
            contentId: "$contentIdSelDan" ,
            contentValue: "单选1" }  ,
            {
            contentId: "$contentIdSelDuo" ,
            contentValue: "[\"多选1\",\"多选2\"]" }  ,
            {
            contentId: "$contentIdForm" ,
            contentValue: null }  ,
            {
            contentId: "$contentIdText" ,
            contentValue: "2025TXT" }  ,
            {
            contentId: "$contentIdNum" ,
            contentValue: 12345 }  ,
            {
            contentId: "$contentIdTel" ,
            contentValue: "***********" }  ,
            {
            contentId: "$contentIdDate" ,
            contentValue: "2025/12/30" }  ,
            {
            contentId: "$contentIdMail" ,
            contentValue: "" } ,
            {
            contentId: "$contentIdIDCard" ,
            contentValue: null},
            {
            contentId: "$contentIdCode" ,
            contentValue: ""}
          ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId

- test:
    name: "TC17-全部内容域传值，传空，传null，不传，editFillingValue不传-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - contains: [ content.data.contentList.0.contentValue, "$PicFileKey"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdPic2" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodePic2" ]
      - eq: [ content.data.contentList.1.contentValue, "" ]
      - eq: [ content.data.contentList.1.editFillingValue, 1]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdSelDan" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeSelDan" ]
      - eq: [ content.data.contentList.2.contentValue, "单选1" ]
      - eq: [ content.data.contentList.2.editFillingValue, 1]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdSelDuo" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeSelDuo" ]
      - eq: [ content.data.contentList.3.contentValue, "[\"多选1\",\"多选2\"]" ]
      - eq: [ content.data.contentList.3.editFillingValue, 1]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdForm" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeForm" ]
      - eq: [ content.data.contentList.4.contentValue, null ]
      - eq: [ content.data.contentList.4.editFillingValue, 1]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdText" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeText" ]
      - eq: [ content.data.contentList.5.contentValue, "2025TXT" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.6.contentValue, "12345" ]
      - eq: [ content.data.contentList.6.editFillingValue, 1]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdTel" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeTel" ]
      - eq: [ content.data.contentList.7.contentValue, "***********" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdDate" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeDate" ]
      - eq: [ content.data.contentList.8.contentValue, "2025/12/30" ]
      - eq: [ content.data.contentList.8.editFillingValue, 1]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdMail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeMail" ]
      - eq: [ content.data.contentList.9.contentValue, "" ]
      - eq: [ content.data.contentList.9.editFillingValue, 1]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.10.contentValue, "$defaultContentValueIDCard" ]
      - eq: [ content.data.contentList.10.editFillingValue, 1]
      - eq: [ content.data.contentList.11.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.11.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.11.contentValue, "" ]
      - eq: [ content.data.contentList.11.editFillingValue, 1]



- test:
    name: "TC18-全部内容域传值，传空，传null，不传，editFillingValue传"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [
            {
            contentId: "$contentIdPic1" ,
            contentValue: "$PicFileKey",
            "editFillingValue": 1
            } ,
            {
            contentId: "$contentIdPic2" ,
            contentValue: "" ,
            "editFillingValue": 0
            }  ,
            {
            contentId: "$contentIdSelDan" ,
            contentValue: "单选1" ,
            "editFillingValue": 0
            }  ,
            {
            contentId: "$contentIdSelDuo" ,
            contentValue: "[\"多选1\",\"多选2\"]" ,
            "editFillingValue": 1
            }  ,
            {
            contentId: "$contentIdForm" ,
            contentValue: null,
            "editFillingValue": 0}  ,
            {
            contentId: "$contentIdText" ,
            contentValue: "2025TXT" }  ,
            {
            contentId: "$contentIdNum" ,
            contentValue: 12345,
            "editFillingValue": 0}  ,
            {
            contentId: "$contentIdTel" ,
            contentValue: "***********" ,
            "editFillingValue": 1}  ,
            {
            contentId: "$contentIdDate" ,
            contentValue: "2025/12/30",
            "editFillingValue": 1
            }  ,
            {
            contentId: "$contentIdMail" ,
            contentValue: "" ,
            "editFillingValue": 1
            } ,
            {
            contentId: "$contentIdIDCard" ,
            contentValue: null,
            "editFillingValue": 0},
            {
            contentId: "$contentIdCode" ,
            contentValue: "",
            "editFillingValue": 0}
          ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId

- test:
    name: "TC19-全部内容域传值，传空，传null，不传，editFillingValue传-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdPic1"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodePic1"]
      - contains: [ content.data.contentList.0.contentValue, "$PicFileKey"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdPic2" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodePic2" ]
      - eq: [ content.data.contentList.1.contentValue, "" ]
      - eq: [ content.data.contentList.1.editFillingValue, 0]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdSelDan" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeSelDan" ]
      - eq: [ content.data.contentList.2.contentValue, "单选1" ]
      - eq: [ content.data.contentList.2.editFillingValue, 0]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdSelDuo" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeSelDuo" ]
      - eq: [ content.data.contentList.3.contentValue, "[\"多选1\",\"多选2\"]" ]
      - eq: [ content.data.contentList.3.editFillingValue, 1]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdForm" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeForm" ]
      - eq: [ content.data.contentList.4.contentValue, null ]
      - eq: [ content.data.contentList.4.editFillingValue, 0]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdText" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeText" ]
      - eq: [ content.data.contentList.5.contentValue, "2025TXT" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.6.contentValue, "12345" ]
      - eq: [ content.data.contentList.6.editFillingValue, 0]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdTel" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeTel" ]
      - eq: [ content.data.contentList.7.contentValue, "***********" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdDate" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeDate" ]
      - eq: [ content.data.contentList.8.contentValue, "2025/12/30" ]
      - eq: [ content.data.contentList.8.editFillingValue, 1]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdMail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeMail" ]
      - eq: [ content.data.contentList.9.contentValue, "" ]
      - eq: [ content.data.contentList.9.editFillingValue, 1]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.10.contentValue, "$defaultContentValueIDCard" ]
      - eq: [ content.data.contentList.10.editFillingValue, 0]
      - eq: [ content.data.contentList.11.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.11.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.11.contentValue, "" ]
      - eq: [ content.data.contentList.11.editFillingValue, 0]



- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]