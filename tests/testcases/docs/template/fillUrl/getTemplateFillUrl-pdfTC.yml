- config:
    name: openapi获取文件模板填写页-pdf文档模板
    variables:
      businessNo: "${get_randomNo_16()}"
      fillNotifyUrl: ${ENV(notifyUrl)}
      redirectUrl: "http://www.baidu.com"
      expirationDate: "1"
      tomorrowDate: ${getDateTime(1, 2)}
      expirationDefaultDate: ${getDateTime(30, 2)}





- test:
    name: "创建文档模板-各种有默认值内容域"
    testcase: common/template/pdf-bulidTemplate-defaultContentValue.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "创建文档模板-无内容域"
    testcase: common/template/buildTemplate1.yml
    extract:
      - newTemplateUuidCommon_template1
      - newVersionCommon_template1
      - templateNameCommon_template1


- test:
    name: "TC-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentIdText: content.data.contentsControl.0.contentId
      - contentCodeText: content.data.contentsControl.0.contentCode
      - defaultContentValueText: content.data.contentsControl.0.defaultContentValue
      - contentIdTextSjy: content.data.contentsControl.1.contentId
      - contentCodeTextSjy: content.data.contentsControl.1.contentCode
      - defaultContentValueTextSjy: content.data.contentsControl.1.defaultContentValue
      - contentIdTel1: content.data.contentsControl.2.contentId
      - contentCodeTel1: content.data.contentsControl.2.contentCode
      - defaultContentValueTel1: content.data.contentsControl.2.defaultContentValue
      - contentIdNum: content.data.contentsControl.3.contentId
      - contentCodeNum: content.data.contentsControl.3.contentCode
      - defaultContentValueNum: content.data.contentsControl.3.defaultContentValue
      - contentIdDate1: content.data.contentsControl.4.contentId
      - contentCodeDate1: content.data.contentsControl.4.contentCode
      - defaultContentValueDate1: content.data.contentsControl.4.defaultContentValue
      - contentIdDate2: content.data.contentsControl.5.contentId
      - contentCodeDate2: content.data.contentsControl.5.contentCode
      - defaultContentValueDate2: content.data.contentsControl.5.defaultContentValue
      - contentIdDate3: content.data.contentsControl.6.contentId
      - contentCodeDate3: content.data.contentsControl.6.contentCode
      - defaultContentValueDate3: content.data.contentsControl.6.defaultContentValue
      - contentIdCode: content.data.contentsControl.7.contentId
      - contentCodeCode: content.data.contentsControl.7.contentCode
      - defaultContentValueCode: content.data.contentsControl.7.defaultContentValue
      - contentIdIDCard: content.data.contentsControl.8.contentId
      - contentCodeIDCard: content.data.contentsControl.8.contentCode
      - defaultContentValueIDCard: content.data.contentsControl.8.defaultContentValue
      - contentIdEmail: content.data.contentsControl.9.contentId
      - contentCodeEmail: content.data.contentsControl.9.contentCode
      - defaultContentValueEmail: content.data.contentsControl.9.defaultContentValue
      - contentIdTel2: content.data.contentsControl.10.contentId
      - contentCodeTel2: content.data.contentsControl.10.contentCode
      - defaultContentValueTel2: content.data.contentsControl.10.defaultContentValue


- test:
    name: "TC-文档模板无内容域，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon_template1
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [
          ]
    validate:
      - eq: [ content.code, 1605135 ]
      - contains: [ content.message, "模板无填写控件" ]
      - eq: [ content.data, null ]


- test:
    name: "TC1-contentsControl集合中内容域重复，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdText",
            contentCode: "$contentIdText",
            contentValue: "" },
            {
              contentId: "$contentIdText",
              contentCode: "$contentIdText",
              contentValue: "" } ]
    validate:
      - eq: [ content.code, 1605045 ]
      - contains: [ content.message, "存在重复文本域"]      #内容域名称相同，不支持同时指定"]
      - eq: [ content.data, null]


- test:
    name: "TC2-contentsControl集合中两个内容域名称相同1，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTel1",
            contentCode: "$contentCodeTel1",
            contentValue: "" },
            {
              contentId: "$contentIdTel2",
              contentCode: "$contentCodeTel2",
              contentValue: "" } ]
    validate:
      - eq: [ content.code, 1605127 ]
      - contains: [ content.message, "内容域名称相同，不支持同时指定"]
      - eq: [ content.data, null]

- test:
    name: "TC3-contentsControl集合中两个内容域名称相同2，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTel1",
            contentCode: "",
            contentValue: "" },
            {
              contentId: "$contentIdTel2",
              contentCode: "",
              contentValue: "" } ]
    validate:
      - eq: [ content.code, 1605127 ]
      - contains: [ content.message, "内容域名称相同，不支持同时指定"]
      - eq: [ content.data, null]


- test:
    name: "TC4-contentsControl集合中两个内容域名称相同3，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "",
            contentCode: "$contentCodeTel1",
            contentValue: "" },
            {
              contentId: "",
              contentCode: "$contentCodeTel2",
              contentValue: "" } ]
    validate:
      - eq: [ content.code, 1605127 ]
      - contains: [ content.message, "内容域名称相同，不支持同时指定" ]
      - eq: [ content.data, null ]


- test:
    name: "TC5-contentsControl日期内容域格式不正确"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdDate1",
            contentCode: "$contentCodeDate1",
            contentValue: "2024-12-30" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【日期】校验不通过，正确格式为"]
      - eq: [ content.data, null]


- test:
    name: "TC6-contentsControl日期内容域格式不正确"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdDate2",
            contentCode: "$contentCodeDate2",
            contentValue: "2024/12/30" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【日期】校验不通过，正确格式为"]
      - eq: [ content.data, null]

- test:
    name: "TC7-contentsControl日期内容域格式不正确"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdDate3",
            contentCode: "$contentCodeDate3",
            contentValue: "2024年12月30" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【日期】校验不通过，正确格式为"]
      - eq: [ content.data, null]

- test:
    name: "TC8-contentsControl日期内容域长度校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdDate3",
            contentCode: "$contentCodeDate3",
            contentValue: "2024年12月30日" } ]
    validate:
      - eq: [ content.code, 1601008 ]
      - contains: [ content.message, "【日期】格式长度校验失败"]
      - eq: [ content.data, null]

- test:
    name: "TC9-contentsControl手机号格式校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTel1",
            contentCode: "",
            contentValue: "***********" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【手机号】校验不通过，正确格式为11位1开头的数字"]
      - eq: [ content.data, null]

- test:
    name: "TC10-contentsControl手机号长度校验-多于11位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTel1",
            contentCode: "",
            contentValue: "198480207201" } ]
    validate:
      - eq: [ content.code, 1601008 ]
      - contains: [ content.message, "【手机号】格式长度校验失败，最大长度【11】" ]
      - eq: [ content.data, null ]


- test:
    name: "TC11-contentsControl手机号长度校验-少于11位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTel1",
            contentCode: "",
            contentValue: "19848020" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【手机号】校验不通过，正确格式为11位1开头的数字" ]
      - eq: [ content.data, null ]


- test:
    name: "TC12-contentsControl信用代码长度校验-多于18位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdCode",
            contentCode: "",
            contentValue: "9100000005992698F523M" } ]
    validate:
      - eq: [ content.code, 1601008 ]
      - contains: [ content.message, "【统一社会信用代码】格式长度校验失败，最大长度【18】" ]
      - eq: [ content.data, null ]


- test:
    name: "TC13-contentsControl信用代码长度校验-少于18位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdCode",
            contentCode: "",
            contentValue: "91000000059926" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【统一社会信用代码】校验不通过" ]
      - eq: [ content.data, null ]


- test:
    name: "TC14-contentsControl信用代码格式校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdCode",
            contentCode: "",
            contentValue: "91MM00000648601265" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【统一社会信用代码】校验不通过" ]
      - eq: [ content.data, null ]


- test:
    name: "TC15-contentsControl证件号长度校验-多于18位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdIDCard",
            contentCode: "",
            contentValue: "432427189306139239111" } ]
    validate:
      - eq: [ content.code, 1601008 ]
      - contains: [ content.message, "【身份证号】格式长度校验失败，最大长度【18】" ]
      - eq: [ content.data, null ]



- test:
    name: "TC16-contentsControl证件号长度校验-少于18位"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdIDCard",
            contentCode: "",
            contentValue: "**************" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【身份证号】校验不通过" ]
      - eq: [ content.data, null ]



- test:
    name: "TC17-contentsControl证件号码格式校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdIDCard",
            contentCode: "",
            contentValue: "******************" } ]
    validate:
      - eq: [ content.code, 1601009]
      - contains: [ content.message, "格式填写错误，【身份证号】校验不通过" ]
      - eq: [ content.data, null ]



- test:
    name: "TC18-contentsControl邮箱长度校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdEmail",
            contentCode: "",
            contentValue: "<EMAIL>" } ]
    validate:
      - eq: [ content.code, 1601008 ]
      - contains: [ content.message, "【邮箱】格式长度校验失败，最大长度【18】" ]
      - eq: [ content.data, null ]


- test:
    name: "TC19-contentsControl邮箱格式校验"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdEmail",
            contentCode: "",
            contentValue: "4324qq.com" } ]
    validate:
      - eq: [ content.code, 1601009 ]
      - contains: [ content.message, "格式填写错误，【邮箱】校验不通过" ]
      - eq: [ content.data, null ]

- test:
    name: "TC20-内容域必填，接口没有填写不报错,填写页面校验是否必填"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: $businessNo
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdDate1",
            contentCode: "",
            contentValue: "" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data.fillTaskId, null ]
      - ne: [ content.data.businessNo, null ]
      - contains: [ content.data.fillUrl, "http" ]
      - contains: [ content.data.fillUrlShort, "http" ]

- test:
    name: "TC21-contentsControl集合中contentId不存在、contentCode存在，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "xx$contentIdTel1",
            contentCode: "$contentCodeTel1",
            contentValue: "" } ]
    validate:
      - eq: [ content.code, 1605088 ]
      - contains: [ content.message, "文本域未找到"]
      - eq: [ content.data, null]

- test:
    name: "TC22-contentsControl集合中contentId存在、contentCode不存在,以contentId为准"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdText",
            contentCode: "xx$contentCodeText",
            contentValue: null,
            editFillingValue: 0
          } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId

- test:
    name: "TC23-验证以contentId为准，-查看填写页详情的contentId"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "$defaultContentValueText"]
      - eq: [ content.data.contentList.0.editFillingValue, 0]

- test:
    name: "TC24-contentValue为空，内容域赋值空"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdText",
            contentValue: "" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC25-验证contentValue为空，内容域赋值空-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, ""]



- test:
    name: "TC26-contentValue为null，内容域赋值为默认值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdText",
            contentValue: null } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC27-验证contentValue为null，内容域值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "$defaultContentValueText"]

- test:
    name: "TC28-contentValue不传，内容域赋值为默认值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdText" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC29-验证contentValue不传，内容域赋值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "$defaultContentValueText"]

- test:
    name: "TC30-关联数据源内容域，内容域赋值空"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTextSjy",
            contentValue: "" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC31-验证contentValue为空，内容域赋值空-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy"]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy"]
      - eq: [ content.data.contentList.1.contentValue, ""]



- test:
    name: "TC32-关联数据源内容域，内容域赋值为null默认值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTextSjy",
            contentValue: null } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC33-关联数据源内容域，Value为null，默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy"]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy"]
      - eq: [ content.data.contentList.1.contentValue, "$defaultContentValueTextSjy"]

- test:
    name: "TC34-关联数据源内容域，Value不传，内容域赋值为默认值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [ {
            contentId: "$contentIdTextSjy" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC35-验证关联数据源内容域，Value不传，内容域赋值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy"]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy"]
      - eq: [ content.data.contentList.1.contentValue, "$defaultContentValueTextSjy"]


- test:
    name: "TC36-关联数据源内容域，Value传值，内容域赋值为传入的值"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          redirectUrl: $redirectUrl
          expirationDate: $expirationDate
          fileName: "填写页页面"
          contentsControl: [ {
            contentId: "$contentIdTextSjy" ,
            contentValue: "文本域传值" } ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC37-验证关联数据源内容域，Value传值，内容域赋值为传入的值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$tomorrowDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy"]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy"]
      - eq: [ content.data.contentList.1.contentValue, "文本域传值"]

- test:
    name: "TC38-全部内容域都不传"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          redirectUrl: $redirectUrl
          expirationDate: $expirationDate
          fileName: "填写页页面"
          contentsControl: []
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC39-验证全部内容域都不传，内容域赋值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$tomorrowDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "$defaultContentValueText"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy" ]
      - eq: [ content.data.contentList.1.contentValue, "$defaultContentValueTextSjy" ]
      - eq: [ content.data.contentList.1.editFillingValue, 1]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdTel1" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeTel1" ]
      - eq: [ content.data.contentList.2.contentValue, "$defaultContentValueTel1" ]
      - eq: [ content.data.contentList.2.editFillingValue, 1]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.3.contentValue, "$defaultContentValueNum" ]
      - eq: [ content.data.contentList.3.editFillingValue, 1]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdDate1" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeDate1" ]
      - eq: [ content.data.contentList.4.contentValue, "$defaultContentValueDate1" ]
      - eq: [ content.data.contentList.4.editFillingValue, 1]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdDate2" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeDate2" ]
      - eq: [ content.data.contentList.5.contentValue, "$defaultContentValueDate2" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdDate3" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeDate3" ]
      - eq: [ content.data.contentList.6.contentValue, "$defaultContentValueDate3" ]
      - eq: [ content.data.contentList.6.editFillingValue, 1]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.7.contentValue, "$defaultContentValueCode" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.8.contentValue, "$defaultContentValueIDCard" ]
      - eq: [ content.data.contentList.8.editFillingValue, 1]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdEmail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeEmail" ]
      - eq: [ content.data.contentList.9.contentValue, "$defaultContentValueEmail" ]
      - eq: [ content.data.contentList.9.editFillingValue, 1]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdTel2" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeTel2" ]
      - eq: [ content.data.contentList.10.contentValue, "$defaultContentValueTel2" ]
      - eq: [ content.data.contentList.10.editFillingValue, 1]


- test:
    name: "TC40-全部内容域都不传，内容域赋值为默认值-提交任务"
    api: api/esignDocs/template/fillTask/submit.yml
    variables:
        fillTaskId: $fillTaskId,
        contentsControl: [
          {
            "contentId": "$contentIdText",
            "contentValue": "$defaultContentValueText"
          },
          {
            "contentId": "$contentIdTextSjy",
            "contentValue": "$defaultContentValueTextSjy"
          },
          {
            "contentId": "$contentIdTel1",
            "contentValue": "$defaultContentValueTel1"
          },
          {
            "contentId": "$contentIdNum",
            "contentValue": "$defaultContentValueNum"
          },
          {
            "contentId": "$contentIdDate1",
            "contentValue": "$defaultContentValueDate1"
          },
          {
            "contentId": "$contentIdDate2",
            "contentValue": "$defaultContentValueDate2"
          },
          {
            "contentId": "$contentIdDate3",
            "contentValue": "$defaultContentValueDate3"
          },
          {
            "contentId": "$contentIdCode",
            "contentValue": "$defaultContentValueCode"
          },
          {
            "contentId": "$contentIdCode",
            "contentValue": "$defaultContentValueCode"
          },
          {
            "contentId": "$contentIdIDCard",
            "contentValue": "$defaultContentValueIDCard"
          },
          {
            "contentId": "$contentIdEmail",
            "contentValue": "$defaultContentValueEmail"
          },
          {
            "contentId": "$contentIdTel2",
            "contentValue": "$defaultContentValueTel2"
          }

        ]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, true]
      - eq: [ content.data.taskId, $fillTaskId]
      - ne: [ content.data.fileKey, ""]
      - contains: [ content.data.downloadUrl, "http"]
      - contains: [ content.data.downloadOuterUrl, "http"]



- test:
    name: "TC41-全部内容域传值，传空，传null，不传,相同名称内容域传一个值同时赋值，editFillingValue不传，默认为1"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          contentsControl: [
            {
            contentId: "$contentIdText" ,
            contentValue: "文本域传值" } ,
            {
            contentId: "$contentIdTextSjy" ,
            contentValue: "" }  ,
            {
            contentId: "$contentIdTel1" ,
            contentValue: "***********" }  ,
            {
            contentId: "$contentIdNum" ,
            contentValue: "99998888" }  ,
            {
            contentId: "$contentIdDate1" ,
            contentValue: "2025年12月30日" }  ,
            {
            contentId: "$contentIdDate2" ,
            contentValue: "2025-12-30" }  ,
            {
            contentId: "$contentIdDate3" ,
            contentValue: null }  ,
            {
            contentId: "$contentIdIDCard" ,
            contentValue: "******************" }  ,
            {
            contentId: "$contentIdEmail" ,
            contentValue: "" }
          ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC42-验证全部内容域都不传，内容域赋值为默认值-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "文本域传值"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy" ]
      - eq: [ content.data.contentList.1.contentValue, "" ]
      - eq: [ content.data.contentList.1.editFillingValue, 1]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdTel1" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeTel1" ]
      - eq: [ content.data.contentList.2.contentValue, "***********" ]
      - eq: [ content.data.contentList.2.editFillingValue, 1]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.3.contentValue, "99998888" ]
      - eq: [ content.data.contentList.3.editFillingValue, 1]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdDate1" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeDate1" ]
      - eq: [ content.data.contentList.4.contentValue, "2025年12月30日" ]
      - eq: [ content.data.contentList.4.editFillingValue, 1]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdDate2" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeDate2" ]
      - eq: [ content.data.contentList.5.contentValue, "2025-12-30" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdDate3" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeDate3" ]
      - eq: [ content.data.contentList.6.contentValue, "$defaultContentValueDate3" ]
      - eq: [ content.data.contentList.6.editFillingValue, 1]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.7.contentValue, "$defaultContentValueCode" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.8.contentValue, "******************" ]
      - eq: [ content.data.contentList.8.editFillingValue, 1]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdEmail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeEmail" ]
      - eq: [ content.data.contentList.9.contentValue, "" ]
      - eq: [ content.data.contentList.9.editFillingValue, 1]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdTel2" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeTel2" ]
      - eq: [ content.data.contentList.10.editFillingValue, 1]
#      - eq: [ content.data.contentList.10.contentValue, "***********" ]  #相同名称内容域的Value后端没有做修改，前端做了适配，这应该是个BUG

- test:
    name: "TC43-全部内容域都不传，内容域赋值为默认值-提交任务"
    api: api/esignDocs/template/fillTask/submit.yml
    variables:
        fillTaskId: $fillTaskId,
        contentsControl: [
          {
            "contentId": "$contentIdText",
            "contentValue": "文本域传值"
          },
          {
            "contentId": "$contentIdTextSjy",
            "contentValue": ""
          },
          {
            "contentId": "$contentIdTel1",
            "contentValue": "***********"
          },
          {
            "contentId": "$contentIdNum",
            "contentValue": "99998888"
          },
          {
            "contentId": "$contentIdDate1",
            "contentValue": "2025年12月30日"
          },
          {
            "contentId": "$contentIdDate2",
            "contentValue": "2025-12-30"
          },
          {
            "contentId": "$contentIdDate3",
            "contentValue": "$defaultContentValueDate3"
          },
          {
            "contentId": "$contentIdCode",
            "contentValue": "$defaultContentValueCode"
          },
          {
            "contentId": "$contentIdIDCard",
            "contentValue": "******************"
          },
          {
            "contentId": "$contentIdEmail",
            "contentValue": ""
          },
          {
            "contentId": "$contentIdTel2",
            "contentValue": "***********"
          }

        ]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, true]
      - eq: [ content.data.taskId, $fillTaskId]
      - ne: [ content.data.fileKey, ""]
      - contains: [ content.data.downloadUrl, "http"]
      - contains: [ content.data.downloadOuterUrl, "http"]

- test:
    name: "TC44-全部内容域传值，传空，传null，不传,相同名称内容域传一个值同时赋值,editFillingValue赋值、不传"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "$businessNo"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: $fillNotifyUrl
          fileName: "填写页页面"
          contentsControl: [
            {
            contentId: "$contentIdText" ,
            contentValue: "文本域传值",
            editFillingValue: 1
            } ,
            {
            contentId: "$contentIdTextSjy" ,
            contentValue: "" ,
            editFillingValue: 1
            }  ,
            {
            contentId: "$contentIdTel1" ,
            contentValue: "***********",
            editFillingValue: 0 }  ,
            {
            contentId: "$contentIdNum" ,
            contentValue: "99998888" ,
            editFillingValue: 0
            }  ,
            {
            contentId: "$contentIdDate1" ,
            contentValue: "2025年12月30日",
            editFillingValue: 1
            }  ,
            {
            contentId: "$contentIdDate2" ,
            contentValue: "2025-12-30" ,
            editFillingValue: 1
            }  ,
            {
            contentId: "$contentIdDate3" ,
            contentValue: null ,
            editFillingValue: 1
            }  ,
            {
            contentId: "$contentIdIDCard" ,
            contentValue: "******************" ,
            editFillingValue: 0
            }  ,
            {
            contentId: "$contentIdEmail" ,
            contentValue: "" ,
            editFillingValue: 0}
          ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.fillTaskId, null]
      - ne: [ content.data.businessNo, null]
      - contains: [ content.data.fillUrl, "http"]
      - contains: [ content.data.fillUrlShort, "http"]
    extract:
      - fillTaskId: content.data.fillTaskId



- test:
    name: "TC45-验证全部内容域传值，传空，传null，不传,相同名称内容域传一个值同时赋值,editFillingValue赋值、不传-查看填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $fillTaskId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.expirationDate, "$expirationDefaultDate"]
      - eq: [ content.data.templateUuid, "$newTemplateUuidCommon"]
      - eq: [ content.data.contentList.0.templateContentUuid, "$contentIdText"]
      - eq: [ content.data.contentList.0.contentCode, "$contentCodeText"]
      - eq: [ content.data.contentList.0.contentValue, "文本域传值"]
      - eq: [ content.data.contentList.0.editFillingValue, 1]
      - eq: [ content.data.contentList.1.templateContentUuid, "$contentIdTextSjy" ]
      - eq: [ content.data.contentList.1.contentCode, "$contentCodeTextSjy" ]
      - eq: [ content.data.contentList.1.contentValue, "" ]
      - eq: [ content.data.contentList.1.editFillingValue, 1]
      - eq: [ content.data.contentList.2.templateContentUuid, "$contentIdTel1" ]
      - eq: [ content.data.contentList.2.contentCode, "$contentCodeTel1" ]
      - eq: [ content.data.contentList.2.contentValue, "***********" ]
      - eq: [ content.data.contentList.2.editFillingValue, 0]
      - eq: [ content.data.contentList.3.templateContentUuid, "$contentIdNum" ]
      - eq: [ content.data.contentList.3.contentCode, "$contentCodeNum" ]
      - eq: [ content.data.contentList.3.contentValue, "99998888" ]
      - eq: [ content.data.contentList.3.editFillingValue, 0]
      - eq: [ content.data.contentList.4.templateContentUuid, "$contentIdDate1" ]
      - eq: [ content.data.contentList.4.contentCode, "$contentCodeDate1" ]
      - eq: [ content.data.contentList.4.contentValue, "2025年12月30日" ]
      - eq: [ content.data.contentList.4.editFillingValue, 1]
      - eq: [ content.data.contentList.5.templateContentUuid, "$contentIdDate2" ]
      - eq: [ content.data.contentList.5.contentCode, "$contentCodeDate2" ]
      - eq: [ content.data.contentList.5.contentValue, "2025-12-30" ]
      - eq: [ content.data.contentList.5.editFillingValue, 1]
      - eq: [ content.data.contentList.6.templateContentUuid, "$contentIdDate3" ]
      - eq: [ content.data.contentList.6.contentCode, "$contentCodeDate3" ]
      - eq: [ content.data.contentList.6.contentValue, "$defaultContentValueDate3" ]
      - eq: [ content.data.contentList.6.editFillingValue, 1]
      - eq: [ content.data.contentList.7.templateContentUuid, "$contentIdCode" ]
      - eq: [ content.data.contentList.7.contentCode, "$contentCodeCode" ]
      - eq: [ content.data.contentList.7.contentValue, "$defaultContentValueCode" ]
      - eq: [ content.data.contentList.7.editFillingValue, 1]
      - eq: [ content.data.contentList.8.templateContentUuid, "$contentIdIDCard" ]
      - eq: [ content.data.contentList.8.contentCode, "$contentCodeIDCard" ]
      - eq: [ content.data.contentList.8.contentValue, "******************" ]
      - eq: [ content.data.contentList.8.editFillingValue, 0]
      - eq: [ content.data.contentList.9.templateContentUuid, "$contentIdEmail" ]
      - eq: [ content.data.contentList.9.contentCode, "$contentCodeEmail" ]
      - eq: [ content.data.contentList.9.contentValue, "" ]
      - eq: [ content.data.contentList.9.editFillingValue, 0]
      - eq: [ content.data.contentList.10.templateContentUuid, "$contentIdTel2" ]
      - eq: [ content.data.contentList.10.contentCode, "$contentCodeTel2" ]
      - eq: [ content.data.contentList.10.editFillingValue, 1]

- test:
    name: "TC43-全部内容域传值，传空，传null，不传,相同名称内容域传一个值同时赋值,editFillingValue赋值、不传-提交任务"
    api: api/esignDocs/template/fillTask/submit.yml
    variables:
        fillTaskId: $fillTaskId,
        contentsControl: [
          {
            "contentId": "$contentIdText",
            "contentValue": "文本域传值"
          },
          {
            "contentId": "$contentIdTextSjy",
            "contentValue": ""
          },
          {
            "contentId": "$contentIdTel1",
            "contentValue": "***********"
          },
          {
            "contentId": "$contentIdNum",
            "contentValue": "99998888"
          },
          {
            "contentId": "$contentIdDate1",
            "contentValue": "2025年12月30日"
          },
          {
            "contentId": "$contentIdDate2",
            "contentValue": "2025-12-30"
          },
          {
            "contentId": "$contentIdDate3",
            "contentValue": "$defaultContentValueDate3"
          },
          {
            "contentId": "$contentIdCode",
            "contentValue": "$defaultContentValueCode"
          },
          {
            "contentId": "$contentIdIDCard",
            "contentValue": "******************"
          },
          {
            "contentId": "$contentIdEmail",
            "contentValue": ""
          },
          {
            "contentId": "$contentIdTel2",
            "contentValue": "***********"
          }
        ]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, true]
      - eq: [ content.data.taskId, $fillTaskId]
      - ne: [ content.data.fileKey, ""]
      - contains: [ content.data.downloadUrl, "http"]
      - contains: [ content.data.downloadOuterUrl, "http"]


- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup-停用2"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon_template1
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板2"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon_template1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]