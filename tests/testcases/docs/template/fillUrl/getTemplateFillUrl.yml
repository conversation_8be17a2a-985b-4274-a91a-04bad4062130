- config:
    name: openapi获取文件模板填写页异常校验
    variables:
      pdfFileKey0: ${ENV(fileKey)}
      wordFileKey0: ${ENV(docPageFileKey)}
#      templateNameCommon0: "自动化测试模板-pdf-${get_randomNo_16()}"
#      templateNameCommon1: "自动化测试模版-pdf-${get_randomNo_16()}"
#      htmlFileName: "word-测试模板.html"
#      description: ""
#      timePosX: 10
#      timePosY: 10
#      formatType: 0
#      formatRule: "28"
#      dataSource: null
#      sourceField: null
#      font: "SimSun"
#      fontSize: 14
#      fontColor: "BLACK"
#      fontStyle: "Normal"
#      textAlign: "Left"
#      length: 28
#      pageNo: 1
#      posX: 188
#      posY: 703
#      width: 216
#      height: 36
#      initiatorAll: 1
#      checkRepetition: 0
#      contentCodeCommon: ${get_randomNo_16()}
#      contentNameCommon: "自动化测试-文本0507"
#      contentNameCommon2: "自动化测试-文本0508"
#      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
#      newTemplateUuid1: ${getTemplateId(5,2,pdf,0)}
      newVersion1: 1


- test:
    name: "创建文档模板-1内容域+1签名域"
    testcase: common/template/buildTemplate7.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common



- test:
    name: "TC-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentIdText: content.data.contentsControl.0.contentId
      - contentCodeText: content.data.contentsControl.0.contentCode
      - defaultContentValueText: content.data.contentsControl.0.defaultContentValue

#- test:
#    name: "case1-引用公共用例获取文件类型"
#    testcase: common/docType/buildDocType.yml
#    extract:
#      - autoTestDocUuid1Common
#
#- test:
#    name: "case2-引用公共用例获取当前登录用户详情信息"
#    testcase: common/user/getCurrentUserInfo.yml
#    extract:
#      - createUserOrgCodeCommon
#      - createUserCodeCommon
#      - userNameCommon
#      - userIdCommon
#      - userCodeCommon
#      - organizationIdCommon
#      - organizationCodeCommon
#      - getOrganizationNameCommon
#
#- test:
#    name: "case3-setup_模版新建-pdf文档"
#    api: api/esignDocs/template/owner/templateAdd.yml
#    variables:
#      - fileKey: $pdfFileKey0
#      - templateType: 1
#      - zipFileKey: null
#      - templateName: $templateNameCommon0
#      - createUserOrg: $createUserOrgCodeCommon
#      - description: $description
#      - docUuid: $autoTestDocUuid1Common
#      - allRange: 1
#      - organizeRange: null
#    extract:
#      - newTemplateUuidCommon01: content.data.templateUuid
#      - newVersionCommon01: content.data.version
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#########################后面需要加默认值、属性是否可修改入参和默认值、属性是否可修改提取，
#- test:
#    name: "case4-添加内容域，设置默认值为空"
#    api: api/esignDocs/template/owner/contentAdd.yml
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuidCommon01
#      - version: $newVersionCommon01
#      - contentName: $contentNameCommon
#      - edgeScope: 0
#      - contentUuid: ""
#      - contentCode: ""
#      - required: 1
#      - encrypted: 0
#    extract:
#      templateContentUuid01: content.data.list.0
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "case5-保存模板"
#    api: api/esignDocs/template/owner/templatePublish.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon01
#      - version: $newVersionCommon01
#      - isPublish: false
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]


- test:
    name: "setup-停用文档模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#######################后面需要加默认值、属性是否可修改验证
#- test:
#    name: "case6-查看文档模版预览页-验证内容域默认值是否正确"
#    api: api/esignDocs/template/withoutPermissionDetail.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#    validate:
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.status, 200 ]
#      - eq: [ content.data.contentList.0.contentName, $contentNameCommon]
#      - eq: [ content.data.contentList.0.encrypted, 0]

- test:
    name: "TC1-templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersion1
    extract:
      - editUrl1: content.data.editUrl
      - previewUrl1: content.data.previewUrl
      - templateName1: content.data.templateName
      - autoTestDocUuid1: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "TC1-epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
      tmp_common_template1: ${putTempEnv(_tmp_common_template1, $tplToken_config)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "case6-查看文档模版预览页-验证内容域默认值是否正确-epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template1)}
      contentId_detail: $contentId1
      entityId_detail: $entityId1
    extract:
      - baseFile1: content.data.baseFile
      - originFile1: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - ne: ["content.data.fields.0.label","单行文本1"]
      - ne: ["content.data.fields.0.type","TEXT"]



- test:
    name: "templateId未发布，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      -  data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1605011 ]
      - eq: [ content.message, "该模板并未发布"]
      - eq: [ content.data, null]


- test:
    name: "case7-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "businessNo长度192个字符，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: "businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符businessNo长度192个字符字符"
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "第三方业务id不可超过191个字符"]
      - eq: [ content.data, null]

- test:
    name: "templateId为空，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: ""
         fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "文件模板id不可为空"]
      - eq: [ content.data, null]

- test:
    name: "templateId为null，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: null
         fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "文件模板id不可为空"]
      - eq: [ content.data, null]



- test:
    name: "templateId不传，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "文件模板id不可为空"]
      - eq: [ content.data, null]


- test:
    name: "templateId不存在，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
           templateId: "123%xx"
           fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1605128]
      - eq: [ content.message, "未查询到已发布的指定文件模板"]
      - eq: [ content.data, null]


- test:
    name: "setup-停用"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateId停用，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
           templateId: $newTemplateUuidCommon
           fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1605011 ]
      - eq: [ content.message, "该模板并未发布"]
      - eq: [ content.data, null]


- test:
    name: "case7-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "fileName字符超过100，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fileName: "fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过fileName字符超过"
          fillNotifyUrl: ${ENV(notifyUrl)}
    validate:
      - eq: [ content.code, 1600017]
      - contains: [ content.message, "文件名长度不能超过"]
      - eq: [ content.data, null]


- test:
    name: "fillNotifyUrl为空，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ""
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "填写成功回调地址不可为空"]
      - eq: [ content.data, null]

- test:
    name: "fillNotifyUrl为null，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fileName: ""
          redirectUrl: ""
          fillNotifyUrl: null
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "填写成功回调地址不可为空"]
      - eq: [ content.data, null]

- test:
    name: "fillNotifyUrl不传，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          redirectUrl: ""
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "填写成功回调地址不可为空"]
      - eq: [ content.data, null]



- test:
    name: "expirationDate为0，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)}
         expirationDate: 0
    validate:
      - eq: [ content.code, 1600017]
      - eq: [ content.message, "填写任务有效期仅支持选择1-365内整数"]
      - eq: [ content.data, null]


- test:
    name: "expirationDate为366，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)}
         expirationDate: 366
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "填写任务有效期仅支持选择1-365内整数"]
      - eq: [ content.data, null]


- test:
    name: "expirationDate为-1，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)}
         expirationDate: "-1.2"
    validate:
      - eq: [ content.code, 1600015]
      - eq: [ content.message, "expirationDate参数错误!"]
      - eq: [ content.data, null]


- test:
    name: "expirationDate为特殊字符，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)}
         expirationDate: "%@"
    validate:
      - eq: [ content.code, 1600015]
      - eq: [ content.message, "expirationDate参数错误!"]
      - eq: [ content.data, null]




- test:
    name: "expirationDate为字母，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)}
         expirationDate: "a"
    validate:
      - eq: [ content.code, 1600015 ]
      - eq: [ content.message, "expirationDate参数错误!"]
      - eq: [ content.data, null]





- test:
    name: "editFillingValue为-1，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
         templateId: $newTemplateUuidCommon
         fillNotifyUrl: ${ENV(notifyUrl)
         contentsControl: [ {
           contentId: "",
           editFillingValue: -1 } ]
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "editFillingValue仅支持填写0、1"]
      - eq: [ content.data, null]


- test:
    name: "editFillingValue为2，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
          contentsControl: [ {
            contentId: "",
            editFillingValue: 2 } ]
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "editFillingValue仅支持填写0、1"]
      - eq: [ content.data, null]

- test:
    name: "editFillingValue为10，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
          contentsControl: [ {
            contentId: "",
            editFillingValue: 10 } ]
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.message, "editFillingValue仅支持填写0、1"]
      - eq: [ content.data, null]



- test:
    name: "contentId不存在、contentCode为空或null，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
          contentsControl: [ {
            contentId: "123xxx" } ]
    validate:
      - eq: [ content.code, 1605088 ]
      - contains: [ content.message, "文本域未找到"]
      - eq: [ content.data, null]


- test:
    name: "contentId为空或null、contentCode不存在，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
          contentsControl: [ {
            contentId: "",
            contentCode: "123Xx"
      } ]
    validate:
      - eq: [ content.code, 1605089 ]
      - contains: [ content.message, "文本域未找到"]
      - eq: [ content.data, null]


- test:
    name: "contentId、contentCode都为空、contentValue传值，报错"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: ${ENV(notifyUrl)}
          contentsControl: [ {
            contentId: "",
            contentCode: "",
            contentValue: "内容域" } ]
    validate:
      - eq: [ content.code, 1605055 ]
      - contains: [ content.message, "文本域实例id与文本域域参数编码必填其一"]
      - eq: [ content.data, null]



- test:
    name: "setup-停用1"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersion1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模板1"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]




