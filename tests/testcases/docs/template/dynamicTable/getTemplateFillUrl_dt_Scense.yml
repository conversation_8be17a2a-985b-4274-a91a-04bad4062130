- config:
    name: 对接epaas模板word文档模板-动态表格-填写验证：页面填写
    variables:
      fileKey1: ${ENV(fileKey)}

- test:
    name: "引用公共用例-创建带有动态表格的word文档模板"
    testcase: common/template/word-buildTemplate-dynamic_table.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "contents-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentId_tb1: content.data.contentsControl.0.contentId
      - contentCode_tb1: content.data.contentsControl.0.contentCode
      - contentName_tb1: content.data.contentsControl.0.contentName
      - contentId_tb2: content.data.contentsControl.1.contentId
      - contentCode_tb2: content.data.contentsControl.1.contentCode
      - contentName_tb2: content.data.contentsControl.1.contentName
      - contentId_tb3: content.data.contentsControl.2.contentId
      - contentCode_tb3: content.data.contentsControl.2.contentCode
      - contentName_tb3: content.data.contentsControl.2.contentName
      - contentId_num1: content.data.contentsControl.3.contentId
      - contentCode_num1: content.data.contentsControl.3.contentCode
      - contentName_num1: content.data.contentsControl.3.contentName
      - contentId_num2: content.data.contentsControl.4.contentId
      - contentCode_num2: content.data.contentsControl.4.contentCode
      - contentName_num2: content.data.contentsControl.4.contentName
      - contentId_tb4: content.data.contentsControl.5.contentId
      - contentCode_tb4: content.data.contentsControl.5.contentCode
      - contentName_tb4: content.data.contentsControl.5.contentName

- test:
    name: "TC1-getTemplateFillUrl-预填动态表格-表格编码不匹配"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: ""
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: "http://www.tsign-test.com"
          contentsControl: [
        {
            "contentId": "$contentId_tb1",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1-XXXX','columnContentValue':'111'},{'columnContentCode':'NO2-NO-EXIST','columnContentValue':'第一行'},{'columnContentCode':'NO3','columnContentValue':'预设行0'},{'columnContentCode':'4','columnContentValue':'实际传入2行'},{'columnContentCode':'5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'第一张表格bbb'},{'columnContentCode':'NO3','columnContentValue':'3-2'},{'columnContentCode':'4','columnContentValue':'4'},{'columnContentCode':'5','columnContentValue':'END-第二行'}]}]}",
            "editFillingValue": 1
        }
    ]
    validate:
      - eq: [ content.code, 1605132 ]
      - contains: [ content.message, "模板动态表格id:【"]
      - contains: [ content.message, "】对应编码【NO1-XXXX】不匹配"]
      - eq: [ content.data, null]

- test:
    name: "TC2-getTemplateFillUrl-预填动态表格"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          businessNo: ""
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: "http://www.tsign-test.com"
          contentsControl: [
        {
            "contentId": "$contentId_num1",
            "contentValue": "12356",
            "editFillingValue": 0
        },
        {
            "contentId": "$contentId_num2",
            "contentValue": "56789",
            "editFillingValue": 1
        },{
            "contentId": "$contentId_tb1",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'只有表头的填充'},{'columnContentCode':'NO2','columnContentValue':'第一行'},{'columnContentCode':'NO3','columnContentValue':'预设行0'},{'columnContentCode':'4','columnContentValue':'实际传入2行'},{'columnContentCode':'5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'第一张表格bbb'},{'columnContentCode':'NO3','columnContentValue':'3-2'},{'columnContentCode':'4','columnContentValue':'4'},{'columnContentCode':'5','columnContentValue':'END-第二行'}]}]}",
            "editFillingValue": 1
        },
        {
            "contentId": "$contentId_tb4",
            "contentValue": "{'form':[{'row':[{'columnContentId':'column1','columnContentValue':'第3张表格-允许修改-有2行空白'},{'columnContentId':'column2','columnContentValue':'测试'},{'columnContentId':'column3','columnContentValue':'预设行5'},{'columnContentId':'column4','columnContentValue':'实际传入7行'},{'columnContentId':'column5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentId':'column1','columnContentValue':'two'},{'columnContentId':'column5','columnContentValue':'2'}]},{'row':[{'columnContentId':'column1','columnContentValue':'ccc'},{'columnContentId':'column2','columnContentValue':'2-3'},{'columnContentId':'column3','columnContentValue':'3-3'},{'columnContentCode':'4','columnContentValue':'第一张表格4'},{'columnContentCode':'5','columnContentValue':'3'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ddd'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END-5原来的结尾'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'fff'}]},{'row':[{'columnContentCode':'4','columnContentValue':'第七行第4列'},{'columnContentCode':'5','columnContentValue':'END3-7-5'}]},{'row':[{'columnContentCode':'5','columnContentValue':''}]},{'row':[{'columnContentCode':'NO1','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':''},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'4','columnContentValue':''},{'columnContentCode':'5','columnContentValue':''}]}]}",
            "editFillingValue": 1
        },
        {
            "contentId": "$contentId_tb2",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'第2张表格-禁止修改'},{'columnContentCode':'5','columnContentValue':'正好匹配'},{'columnContentCode':'4','columnContentValue':'实际传入5行'},{'columnContentCode':'NO3','columnContentValue':'预设行5'},{'columnContentCode':'NO2','columnContentValue':'222'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'居中bbb'},{'columnContentCode':'5','columnContentValue':'2'},{'columnContentCode':'4','columnContentValue':'4-2'},{'columnContentCode':'NO3','columnContentValue':'13'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ccc'},{'columnContentCode':'5','columnContentValue':'3'},{'columnContentCode':'4','columnContentValue':'合并4'},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':'3'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ddd'},{'columnContentCode':'5','columnContentValue':'4'},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':''}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'eee'},{'columnContentCode':'5','columnContentValue':'END-555'}]}]}",
            "editFillingValue": 0
        },
        {
            "contentId": "$contentId_tb3",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'第4张表格-没有表头'},{'columnContentCode':'NO3','columnContentValue':'预设行5'}]},{'row':[{'columnContentCode':'5','columnContentValue':'填写的2-5数据'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END4-3-5'},{'columnContentCode':'4','columnContentValue':'合并右下角'},{'columnContentCode':'NO3','columnContentValue':''}]}]}"
        }
    ]
    extract:
      - _fillTaskId_dt_002: content.data.fillTaskId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - ne: [ content.data, null]


- test:
    name: "TC3-getTemplateFillUrl-不预填动态表格"
    api: api/esignDocs/documents/template/getTemplateFillUrl.yml
    variables:
      - data:
          templateId: $newTemplateUuidCommon
          fillNotifyUrl: "http://www.tsign-test.com"
          contentsControl: []
    extract:
      - _fillTaskId_dt_003: content.data.fillTaskId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - ne: [ content.data, null]

- test:
    name: "templateFillTaskDetail-填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $_fillTaskId_dt_002
    extract:
      - _fillUrl_dt_002: content.data.fillUrl
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.fillUrl, "http"]
############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_fillUrl_dt_002)}
      tmp_dt_template_001: ${putTempEnv(_tmp_dt_002, $tplToken_config)}
    validate:
      - eq: [content.code, 0]
      - eq: [content.data.businessContext.open_api, "true"]
      - eq: [content.data.businessContext.fill_from_page_key, "GET_FILL_TASK_OPEN_API_URL"]

- test:
    name: "get_fill_task_info-获取填写页信息"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: ${ENV(_tmp_dt_002)}
      - timestamp_info: ${getTimeStamp()}
    extract:
#      - _fieldId01: content.data.waitFillFields.0
      - _contentId: content.data.contents.0.contentId
      - _entityId: content.data.contents.0.documentId
      - _contents: content.data.contents
      - _preFillValues: content.data.preFillValues
#      - _fieldValue_tb_1: content.data.preFillValues.0.fieldValue
#      - _fieldValue_tb_2: content.data.preFillValues.2.fieldValue
#      - _fieldValue_tb_3: content.data.preFillValues.4.fieldValue
#      - _fieldValue_tb_4: content.data.preFillValues.5.fieldValue
    validate:
      - eq: [content.code, 0]
      - eq: [content.data.allowedModifications, true]
      - ne: [content.data.fillTaskId, "$_fillTaskId_dt_002"]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb1, allowedModifications)}", true]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb2, allowedModifications)}", false]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb4, allowedModifications)}", true]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb3, allowedModifications)}", true]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_num1, allowedModifications)}", false]
      - eq: ["${get_value_by_fieldId($_preFillValues, fieldId, $contentId_num2, allowedModifications)}", true]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_dt_002)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
#      - _fieldValue_tb_1: content.data.fields.2.fieldValue
      - _defaultValue_tb_1: content.data.fields.2.settings.defaultValue
      - _defaultValue_tb_2: content.data.fields.3.settings.defaultValue
      - _defaultValue_tb_3: content.data.fields.4.settings.defaultValue
      - _defaultValue_tb_4: content.data.fields.7.settings.defaultValue
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
      - contains: ["content.data.name","自动化"]
      - eq: ["content.data.contentType","DYNAMIC"]

- test:
    name: "epaasTemplate-detail-检查表格"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_dt_002)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
      _fieldValue_tb_1: "${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb1, fieldValue)}"
      _fieldValue_tb_2: "${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb2, fieldValue)}"
      _fieldValue_tb_3: "${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb4, fieldValue)}"
      _fieldValue_tb_4: "${get_value_by_fieldId($_preFillValues, fieldId, $contentId_tb3, fieldValue)}"
    extract:
      - _baseFile: content.data.baseFile
    validate:
      - eq: ["${extract_table_cell($_fieldValue_tb_1, 1, 1)}", "只有表头"]
      - eq: ["${extract_table_cell($_fieldValue_tb_1, 3, 5)}", "END-第二行"]
      - eq: ["${extract_table_cell($_fieldValue_tb_2, 2, 1)}", "第2张表格-禁止修改"]
      - eq: ["${extract_table_cell($_fieldValue_tb_2, 3, 5)}", "2"]
      - eq: ["${extract_table_cell($_fieldValue_tb_2, 6, 5)}", "END-555"]
      - eq: ["${extract_table_cell($_fieldValue_tb_3, 2, 1)}", "第3张表格-允许修改-有2行空白"]
      - eq: ["${extract_table_cell($_fieldValue_tb_3, 8, 5)}", "END3-7-5"]
      - eq: ["${extract_table_cell($_fieldValue_tb_3, 10, 5)}", ""]
      - eq: ["${extract_table_cell($_fieldValue_tb_4, 1, 1)}", "第4张表格-没有表头"]
      - eq: ["${extract_table_cell($_fieldValue_tb_4, 5, 5)}", "END"]

- test:
    name: "save-fill-data-填写页提交"
    api: api/esignDocs/template/save-fill-data.yml
    variables:
      tplToken_savefilldata: ${ENV(_tmp_dt_002)}
      contents_savefilldata: ${remove_key_from_dict_list($_contents, class)}
      fillData_savefilldata: ${remove_key_from_dict_list($_preFillValues, class)}
#      tmp_dt_template_002: ${putTempEnv(_contents_1, $contents_savefilldata)}
#      tmp_dt_template_003: ${putTempEnv(_fillData_1, $fillData_savefilldata)}
    validate:
      - eq: ["content.code",0]
      - eq: ["content.code",0]
      - eq: ["content.code",0]

- test:
    name: "submit-fill-task-填写页提交"
    api: api/esignDocs/template/submit-fill-task.yml
    variables:
      tplToken_submitfilltask: ${ENV(_tmp_dt_002)}
      contents_submitfilltask: ${remove_key_from_dict_list($_contents, class)}
      fillData_submitfilltask: ${remove_key_from_dict_list($_preFillValues, class)}
#    extract:
#      - _baseFile: content.data.baseFile
    validate:
      - eq: ["content.code",0]
      - eq: ["content.code",0]
      - eq: ["content.code",0]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "submit-提交任务"
    api: api/esignDocs/template/fillTask/submit.yml
    variables:
        params_submit:
          fillTaskId: $_fillTaskId_dt_002
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, true]
      - eq: [ content.data.taskId, $_fillTaskId_dt_002]
      - ne: [ content.data.fileKey, ""]
      - contains: [ content.data.downloadUrl, "http"]
      - contains: [ content.data.downloadOuterUrl, "http"]
    teardown_hooks:
      - ${sleep(3)}


- test:
    name: "templateFillTaskDetail-填写页详情"
    api: api/esignDocs/template/templateFillTaskDetail.yml
    variables:
      fillTaskId: $_fillTaskId_dt_002
    extract:
      - _fillUrl_dt_002_1: content.data.fillUrl
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.success, True]
      - contains: [ content.data.fillUrl, "http"]

- test:
    name: "get_fill_task_info-获取填写页信息（填写很慢，若是需要校验填写的数据，需要等待很久1min以上）"
    setup_hooks:
      - ${sleep(10)}
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: ${getTplToken($_fillUrl_dt_002_1)}
      - timestamp_info: ${getTimeStamp()}
    extract:
#      - _fieldId01: content.data.waitFillFields.0
      - _contentId: content.data.contents.0.contentId
      - _entityId: content.data.contents.0.documentId
      - _contents: content.data.contents
      - _fillValues: content.data.fillValues
    validate:
      - eq: [content.code, 0]
      - eq: [content.data.fillTaskStatus, "1"]
      - ne: [content.data.fillTaskId, "$_fillTaskId_dt_002"]
#      - contains: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_tb1, fieldValue)}", "只有表头"]
#      - contains: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_tb2, fieldValue)}", "第2张表格-禁止修改"]
#      - contains: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_tb4, fieldValue)}", "第3张表格-允许修改-有2行空白"]
#      - contains: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_tb3, fieldValue)}", "第4张表格-没有表头"]
#      - eq: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_num1, fieldValue)}", "12356"]
#      - eq: ["${get_value_by_fieldId($_fillValues, fieldId, $contentId_num2, fieldValue)}", "56789"]

- test:
    name: 查询填写回调-填写回调
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $_fillTaskId_dt_002
      callbackEventType: "4"
    validate:
        - len_eq: ["content.callbackEventOutputs", 1]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$_fillTaskId_dt_002" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "4" ]
        - contains: [ "content.callbackEventOutputs.0.callBackUrl", "http" ]
        - eq: [ "content.callbackEventOutputs.0.eviReportEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.failReason", "执行post请求失败" ]
        - eq: [ "content.callbackEventOutputs.0.physicalSealEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.preSignFileEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.transferFillingEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.status", "0" ]
        - ne: [ "content.callbackEventOutputs.0.firstRequestTime", "" ]
        - ne: [ "content.callbackEventOutputs.0.projectId", "" ]
        - ne: [ "content.callbackEventOutputs.0.docTemplateFillFinishEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.docTemplateFillFinishEventCallbackBean.callBackDesc", "FILL_TEMPLATE_FINISH" ]
        - eq: [ "content.callbackEventOutputs.0.docTemplateFillFinishEventCallbackBean.callBackEnum", 25 ]
        - contains: [ "content.callbackEventOutputs.0.requestParamJson", "表格2-没有表头" ]
