- config:
    name: 对接epaas模板word文档模板-动态表格-填写验证：createByBizTemplate接口发起填写流程填写
    variables:
      fileKey1: ${ENV(fileKey)}
      preset_dt_name: "自动化测试-动态表格${get_randomNo()}"
      orgCode1: ${ENV(sign01.main.orgCode)}
      orgName1: ${ENV(sign01.main.orgName)}
      userCode1: ${ENV(sign01.userCode)}
      userName1: ${ENV(sign01.userName)}

- test:
    name: "引用公共用例-创建带有动态表格的word文档模板"
    testcase: common/template/word-buildTemplate-dynamic_table.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "contents-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentId_tb1: content.data.contentsControl.0.contentId
      - contentCode_tb1: content.data.contentsControl.0.contentCode
      - contentName_tb1: content.data.contentsControl.0.contentName
      - contentId_tb2: content.data.contentsControl.1.contentId
      - contentCode_tb2: content.data.contentsControl.1.contentCode
      - contentName_tb2: content.data.contentsControl.1.contentName
      - contentId_tb3: content.data.contentsControl.2.contentId
      - contentCode_tb3: content.data.contentsControl.2.contentCode
      - contentName_tb3: content.data.contentsControl.2.contentName
      - contentId_num1: content.data.contentsControl.3.contentId
      - contentCode_num1: content.data.contentsControl.3.contentCode
      - contentName_num1: content.data.contentsControl.3.contentName
      - contentId_num2: content.data.contentsControl.4.contentId
      - contentCode_num2: content.data.contentsControl.4.contentCode
      - contentName_num2: content.data.contentsControl.4.contentName
      - contentId_tb4: content.data.contentsControl.5.contentId
      - contentCode_tb4: content.data.contentsControl.5.contentCode
      - contentName_tb4: content.data.contentsControl.5.contentName

- test:
    name: "TC3-添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $preset_dt_name
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-setup-获取业务模版PresetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $preset_dt_name
      - page: 1
      - size: 10
    extract:
      - _presetId_dt_1: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $_presetId_dt_1
    extract:
      - _businessTypeId_dt_1: content.data.signBusinessType.businessTypeId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message","成功"]
      - ne: ["content.data.presetId",""]
      - ne: ["content.data.signBusinessType.businessTypeId",""] 
        
- test:
    name: "addDetail-业务模板添加详情"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - presetId: $_presetId_dt_1
      - presetName: $preset_dt_name
      - signBusinessTypeId: $_businessTypeId_dt_1
      - initiatorAll: 1
      - multiSigner: 1
      - templateId: $newTemplateUuidCommon
      - templateName: $templateNameCommon
      - version: $newVersionCommon
      - checkRepetition: 1
      - initiatorList: []
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC8-添加业务模板配置签署人--非自动签署--单方个人"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $_presetId_dt_1
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 1
          signMode: 0
          signerList:
            - autoSign: 0
              assignSigner: 1
              organizeCode: ""
              userName: "$userName1"
              organizeName: ""
              departmentName: "$orgName1"
              legalSign: 0
              sealTypeCode: ""
              signerTerritory: 1
              signerType: 1
              userCode: "$userCode1"
              sealTypeName: ""
              departmentCode: "$orgCode1"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "businessPreset_detail-业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $_presetId_dt_1
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_003, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_003)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
      - _label_0: content.data.fields.0.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_003)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_003)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      _tmp_label_6: "${dataArraySort($_fields, type, 6, label)}"
      _tmp_label_7: "${dataArraySort($_fields, type, 7, label)}"
      _tmp_label_8: "${dataArraySort($_fields, type, 8, label)}"
      _tmp_sign: [ {"label": "$_tmp_label_6","templateRoleId": "$_signerId1" },
                   {"label": "$_tmp_label_7","templateRoleId": "$_signerId1" },
                   {"label": "$_tmp_label_8","templateRoleId": "$_signerId1" }
      ]
      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,,$_tmp_sign)}"
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - ne: ["content.data.0.contentVersionId",""]
      - ne: ["content.data.0.contentId",""]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: editTemplateContentDomain-业务模板配置的第4步：只有发起方填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $_presetId_dt_1
      - status: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]

- test:
      name: bizTemplatesDetail-openapi查询电子签署业务模板详情
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $_businessTypeId_dt_1
      extract:
        - docTemplateId001: content.data.docTemplateInfos.0.docTemplateId
        - _contentName_0: content.data.fillingUserInfos.0.contentsControl.0.contentName
        - _contentName_1: content.data.fillingUserInfos.0.contentsControl.1.contentName
        - _contentName_2: content.data.fillingUserInfos.0.contentsControl.2.contentName
        - _contentName_3: content.data.fillingUserInfos.0.contentsControl.3.contentName
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $_businessTypeId_dt_1 ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 1 ] #不允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]

- test:
    name: "文档模板-控件查询"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $docTemplateId001
#    extract:
#      - templateContentName1: content.data.contentsControl.0.contentName
#      - templateContentName0: content.data.contentsControl.1.contentName
    validate:
      - len_eq: ["content.data.contentsControl",6]
      - len_eq: ["content.data.sealControl",3]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: TC1-createByBizTemplate-发起预填了信息的填写完成流程
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $_businessTypeId_dt_1
      - flowConfigsCBBT:
          flowConfig:
            subject: "createByBizTemplate-动态表格-填写完成"
            startMode: 1
          fillConfig:
            autoFillAndSubmit: 1
            editComponentValue: 1
      - fillingUserInfosCBBT:
          - fillingUserType: 0
            signerId:
            contentsControl:
              - contentName: $_contentName_0
                contentCode:
                contentValue: "{'form':[{'row':[{'columnContentId':'column1','columnContentValue':'第3张表格-允许修改-有2行空白'},{'columnContentId':'column2','columnContentValue':'测试'},{'columnContentId':'column3','columnContentValue':'预设行5'},{'columnContentId':'column4','columnContentValue':'实际传入7行'},{'columnContentId':'column5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentId':'column1','columnContentValue':'two'},{'columnContentId':'column5','columnContentValue':'2'}]},{'row':[{'columnContentId':'column1','columnContentValue':'ccc'},{'columnContentId':'column2','columnContentValue':'2-3'},{'columnContentId':'column3','columnContentValue':'3-3'},{'columnContentCode':'4','columnContentValue':'第一张表格4'},{'columnContentCode':'5','columnContentValue':'3'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ddd'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END-5原来的结尾'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'fff'}]},{'row':[{'columnContentCode':'4','columnContentValue':'第七行第4列'},{'columnContentCode':'5','columnContentValue':'END3-7-5'}]},{'row':[{'columnContentCode':'5','columnContentValue':''}]},{'row':[{'columnContentCode':'NO1','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':''},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'4','columnContentValue':''},{'columnContentCode':'5','columnContentValue':''}]}]}"
              - contentName: $_contentName_1
                contentCode:
                contentValue: "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'只有表头的填充'},{'columnContentCode':'NO2','columnContentValue':'第一行'},{'columnContentCode':'NO3','columnContentValue':'预设行0'},{'columnContentCode':'4','columnContentValue':'实际传入2行'},{'columnContentCode':'5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'第一张表格bbb'},{'columnContentCode':'NO3','columnContentValue':'3-2'},{'columnContentCode':'4','columnContentValue':'4'},{'columnContentCode':'5','columnContentValue':'END-第二行'}]}]}"
              - contentName: $_contentName_2
                contentCode:
                contentValue: "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'第4张表格-没有表头'},{'columnContentCode':'NO3','columnContentValue':'预设行5'}]},{'row':[{'columnContentCode':'5','columnContentValue':'填写的2-5数据'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END4-3-5'},{'columnContentCode':'4','columnContentValue':'合并右下角'},{'columnContentCode':'NO3','columnContentValue':''}]}]}"
              - contentName: $_contentName_3
                contentCode:
                contentValue: "789456"
      - signerInfosCBBT: []
      - CCInfosCBBT: []
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, ""]