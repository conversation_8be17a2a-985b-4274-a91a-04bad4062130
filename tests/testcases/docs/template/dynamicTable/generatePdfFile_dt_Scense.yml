- config:
    name: 对接epaas模板word文档模板-动态表格-填写验证：generatePdfFile
    variables:
      fileKey1: ${ENV(fileKey)}

- test:
    name: "引用公共用例-创建带有动态表格的word文档模板"
    testcase: common/template/word-buildTemplate-dynamic_table.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "contents-查询模板控件内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ content.data.templateId, "$newTemplateUuidCommon" ]
    extract:
      - contentId_tb1: content.data.contentsControl.0.contentId
      - contentCode_tb1: content.data.contentsControl.0.contentCode
      - contentName_tb1: content.data.contentsControl.0.contentName
      - contentId_tb2: content.data.contentsControl.1.contentId
      - contentCode_tb2: content.data.contentsControl.1.contentCode
      - contentName_tb2: content.data.contentsControl.1.contentName
      - contentId_tb3: content.data.contentsControl.2.contentId
      - contentCode_tb3: content.data.contentsControl.2.contentCode
      - contentName_tb3: content.data.contentsControl.2.contentName
      - contentId_num1: content.data.contentsControl.3.contentId
      - contentCode_num1: content.data.contentsControl.3.contentCode
      - contentName_num1: content.data.contentsControl.3.contentName
      - contentId_num2: content.data.contentsControl.4.contentId
      - contentCode_num2: content.data.contentsControl.4.contentCode
      - contentName_num2: content.data.contentsControl.4.contentName
      - contentId_tb4: content.data.contentsControl.5.contentId
      - contentCode_tb4: content.data.contentsControl.5.contentCode
      - contentName_tb4: content.data.contentsControl.5.contentName

- test:
    name: "TC1-generatePdfFile-填了部分动态表格-表格编码不匹配"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - body:
          templateId: $newTemplateUuidCommon
          fileName: "1-$newTemplateUuidCommon"
          contentsControl: [
        {
            "contentId": "$contentId_tb1",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1-XXXX','columnContentValue':'只有表头的填充'},{'columnContentCode':'NO2-NO-EXIST','columnContentValue':'第一行'},{'columnContentCode':'NO3','columnContentValue':'预设行0'},{'columnContentCode':'4','columnContentValue':'实际传入2行'},{'columnContentCode':'5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'第一张表格bbb'},{'columnContentCode':'NO3','columnContentValue':'3-2'},{'columnContentCode':'4','columnContentValue':'4'},{'columnContentCode':'5','columnContentValue':'END-第二行'}]}]}",
            "editFillingValue": 1
        }
    ]
    validate:
      - eq: [ content.code, 1605132 ]
      - contains: [ content.message, "模板动态表格id:【"]
      - contains: [ content.message, "】对应编码【NO1-XXXX】不匹配"]
      - eq: [ content.data, null]

- test:
    name: "TC2-generatePdfFile-填写完成动态表格"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - body:
          templateId: $newTemplateUuidCommon
          fileName: "2-$newTemplateUuidCommon"
          contentsControl: [
        {
            "contentId": "$contentId_num1",
            "contentValue": "12356",
            "editFillingValue": 0
        },
        {
            "contentId": "$contentId_num2",
            "contentValue": "56789",
            "editFillingValue": 1
        },{
            "contentId": "$contentId_tb1",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'只有表头的填充'},{'columnContentCode':'NO2','columnContentValue':'第一行'},{'columnContentCode':'NO3','columnContentValue':'预设行0'},{'columnContentCode':'4','columnContentValue':'实际传入2行'},{'columnContentCode':'5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'第一张表格bbb'},{'columnContentCode':'NO3','columnContentValue':'3-2'},{'columnContentCode':'4','columnContentValue':'4'},{'columnContentCode':'5','columnContentValue':'END-第二行'}]}]}",
            "editFillingValue": 1
        },
        {
            "contentId": "$contentId_tb2",
            "contentValue": "{'form':[{'row':[{'columnContentId':'column1','columnContentValue':'第3张表格-允许修改-有2行空白'},{'columnContentId':'column2','columnContentValue':'测试'},{'columnContentId':'column3','columnContentValue':'预设行5'},{'columnContentId':'column4','columnContentValue':'实际传入7行'},{'columnContentId':'column5','columnContentValue':'往下新增行'}]},{'row':[{'columnContentId':'column1','columnContentValue':'two'},{'columnContentId':'column5','columnContentValue':'2'}]},{'row':[{'columnContentId':'column1','columnContentValue':'ccc'},{'columnContentId':'column2','columnContentValue':'2-3'},{'columnContentId':'column3','columnContentValue':'3-3'},{'columnContentCode':'4','columnContentValue':'第一张表格4'},{'columnContentCode':'5','columnContentValue':'3'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ddd'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END-5原来的结尾'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'fff'}]},{'row':[{'columnContentCode':'4','columnContentValue':'第七行第4列'},{'columnContentCode':'5','columnContentValue':'END3-7-5'}]},{'row':[{'columnContentCode':'5','columnContentValue':''}]},{'row':[{'columnContentCode':'NO1','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':''},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'4','columnContentValue':''},{'columnContentCode':'5','columnContentValue':''}]}]}",
            "editFillingValue": 1
        },
        {
            "contentId": "$contentId_tb4",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'第2张表格-禁止修改'},{'columnContentCode':'5','columnContentValue':'正好匹配'},{'columnContentCode':'4','columnContentValue':'实际传入5行'},{'columnContentCode':'NO3','columnContentValue':'预设行5'},{'columnContentCode':'NO2','columnContentValue':'222'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'居中bbb'},{'columnContentCode':'5','columnContentValue':'2'},{'columnContentCode':'4','columnContentValue':'4-2'},{'columnContentCode':'NO3','columnContentValue':'13'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ccc'},{'columnContentCode':'5','columnContentValue':'3'},{'columnContentCode':'4','columnContentValue':'合并4'},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':'3'}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'ddd'},{'columnContentCode':'5','columnContentValue':'4'},{'columnContentCode':'NO3','columnContentValue':''},{'columnContentCode':'NO2','columnContentValue':''}]},{'row':[{'columnContentCode':'NO1','columnContentValue':'eee'},{'columnContentCode':'5','columnContentValue':'END-555'}]}]}",
            "editFillingValue": 0
        },
        {
            "contentId": "$contentId_tb3",
            "contentValue": "{'form':[{'row':[{'columnContentCode':'NO1','columnContentValue':'第4张表格-没有表头'},{'columnContentCode':'NO3','columnContentValue':'预设行5'}]},{'row':[{'columnContentCode':'5','columnContentValue':'填写的2-5数据'}]},{'row':[{'columnContentCode':'5','columnContentValue':'END4-3-5'},{'columnContentCode':'4','columnContentValue':'合并右下角'},{'columnContentCode':'NO3','columnContentValue':''}]}]}"
        }
    ]
    extract:
      - _fileKey_0: content.data.fileKey
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - ne: [ content.data, null]


- test:
    name: "TC3-generatePdfFile-没填控件"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      - body:
          templateId: $newTemplateUuidCommon
          fileName: "3-$newTemplateUuidCommon"
          contentsControl: []
    validate:
      - eq: [ content.code, 1600017 ]
      - contains: [ content.message, "内容域不能为空"]
      - eq: [ content.data, null]

- test:
    name: getDownloadUrl-pdf正常文件下载
    api: api/esignFileSystem/getDownloadUrl.yml
    variables:
        fileKey: $_fileKey_0
    extract:
      - _download_url: content.data.url
    validate:
        - eq: ["content.code",200]
        - eq: ["content.message","成功"]
        - contains: ["content.data.url","http"]

- test:
    name: "teardown-解析PDF-确认文件里面合成的内容"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
      expected_text: ['第4张表格','第2张表格','4-2','END4-3-5','填写的2-5数据','END-555']
    teardown_hooks:
      - ${delete_temp_file($_fileKey_0)}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
      - eq: [ "${verify_pdf_contains_text($_fileKey_0, 实际传入2行, $_download_url)}", true ]
      - eq: [ "${verify_pdf_contains_text($_fileKey_0, $expected_text, $_download_url)}", true ]

- test:
    name: "teardown-删除下载得pdf"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $newTemplateUuidCommon}
      tmp_delete: "${delete_temp_file($_fileKey_0)}"
    teardown_hooks:
      - ${delete_temp_file($_fileKey_0)}
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]