- config:
    name: 文件模板(open-api)
    variables:
      - userCode1: ${ENV(sign01.userCode)}
      - pixff: ${generate_random_str(7)}
      - templateName: 测试模版PDF$pixff
      - templateNamedoc: 测试模版doc$pixff
      - fileKey: ${ENV(fileKey)}
      - docPageFileKey: ${ENV(docPageFileKey)}
      - docTypeId1: ${get_a_docConfig_type()}
      - templateId:
      - redirectUrl:
      - updateStatus:
      - createUserOrgCode: ${ENV(sign01.main.orgCode)}
      - domainHost: ${ENV(esign.projectHost)}
      - outerDomainHost: ${ENV(esign.projectOuterHost)}

- test:
    name: "P1TL-创建人userCode、customAccountNo不传"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
      json: {
        "organizationCode": "",
        "customOrgNo": "",
        "templateName": $templateName,
        "docTypeId": $docTypeId1,
        "docTypeName": "",
        "description": "",
        "fileKey": $fileKey
      }
    validate:
      - eq: [ content.code, 1605104 ]
      - eq: [ content.message, "userCode和customAccountNo二选一必填" ]
      - eq: [ content.data, null]

- test:
    name: "P2TL-docTypeId、docTypeName 不传"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
      json: {
        "userCode": $userCode1,
        "customAccountNo": "",
        "organizationCode": "",
        "customOrgNo": "",
        "templateName": $templateName,
        "description": "",
        "fileKey": $fileKey
      }
    validate:
      - eq: [ content.code, 1605104 ]
      - eq: [ content.message, "docTypeId和docTypeName二选一必填" ]
      - eq: [ content.data, null]

- test:
    name: "P3TL-编辑模版templateId、templateName不传"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "updateStatus": 0
      }
    validate:
      - eq: [ content.code, 1605104 ]
      - eq: [ content.message, "templateId和templateName二选一必填" ]
      - eq: [ content.data, null ]

- test:
    name: "P4TL-openapi编辑模版templateId、templateName不传"
    api: api/esignDocs/documents/template/getTemplateEditUrl.yml
    variables:
      json: {
        "redirectUrl": "https://www.baidu.com"
      }
    validate:
      - eq: [ content.code, 1605104 ]
      - eq: [ content.message, "templateId和templateName二选一必填" ]
      - eq: [ content.data, null ]

- test:
    name: "P1TL-openapi创建人userCode、customAccountNo 为均填写且organizationCode、customOrgNo为空"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
      json: {
        "userCode": $userCode1,
        "customAccountNo": "",
        "organizationCode": "",
        "customOrgNo": "",
        "templateName": $templateName,
        "docTypeId": $docTypeId1,
        "docTypeName": "",
        "description": "",
        "fileKey": $fileKey
      }
    extract:
      - templateId1: content.data.templateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateId, $templateId1]

- test:
    name: "setup_openapi创建模版页面发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $templateId1
      - version: 1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-openapi创建模版页面停用"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $templateId1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "P2TL-openapi创建fileKey文件doc、docx格式"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
      json: {
        "userCode": $userCode1,
        "customAccountNo": "",
        "organizationCode": "",
        "customOrgNo": "",
        "templateName": $templateNamedoc,
        "docTypeId": $docTypeId1,
        "docTypeName": "",
        "description": "",
        "fileKey": $docPageFileKey
      }
    extract:
      - templateId2: content.data.templateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateId, $templateId2]

- test:
    name: "P3TL-openapi编辑模版templateId、templateName均填写，有重定向地址,状态为草稿"
    api: api/esignDocs/documents/template/getTemplateEditUrl.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": $templateName,
        "redirectUrl": "https://www.baidu.com"
      }
    extract:
      - templateEditUrl1: content.data.templateEditUrl
      - templateEditUrlShort: content.data.templateEditUrlShort
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templateEditUrl, "$domainHost" ]
      - contains: [ content.data.templateEditUrlShort, "$domainHost" ]
      - contains: [ content.data.templateEditOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.templateEditOuterUrlShort, "$outerDomainHost" ]

- test:
    name: "P3TL-redirectUrl为templateEditUrl1长链截取重定向地址"
    api: api/esignDocs/documents/template/getTemplateEditUrl.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": $templateName,
        "redirectUrl": "${substring($templateEditUrl1,171,194)}"
      }
    extract:
      - templateEditUrl: content.data.templateEditUrl
      - templateEditUrlShort: content.data.templateEditUrlShort
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "P4TL-openapi文件模板状态为草稿变更为已发布"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": $templateNamedoc,
        "updateStatus": 0
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "4" ]

- test:
    name: "P5TL-openapi文件模板状态为已发布变更为已停用"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": "",
        "templateName": $templateNamedoc,
        "updateStatus": 1
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "5" ]
      - eq: [ content.data.templateId, $templateId2 ]

- test:
    name: "P6TL-openapi获取编辑模版文件模板状态为已停用,无重定向地址"
    api: api/esignDocs/documents/template/getTemplateEditUrl.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": $templateNamedoc,
        "redirectUrl": ""
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.templateEditUrl, "$domainHost" ]
      - contains: [ content.data.templateEditUrlShort, "$domainHost" ]
      - contains: [ content.data.templateEditOuterUrl, "$outerDomainHost" ]
      - contains: [ content.data.templateEditOuterUrlShort, "$outerDomainHost" ]

- test:
    name: "setup-删除模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $templateId1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]

- test:
    name: "setup_页面模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $templateName
      - createUserOrg: $createUserOrgCode
      - description: "aaaaa"
      - docUuid: $docTypeId1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-页面创建openapi接口变更状态为已发布"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": $newTemplateUuid,
        "templateName": "",
        "updateStatus": 0
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "4" ]

- test:
    name: "setup-页面创建openapi接口复制模版"
    api: api/esignDocs/documents/template/copy.yml
    variables:
      json: {
        "templateId": $newTemplateUuid,
        "templateName": "",
        "renameTemplate": ""
      }
    extract:
      - newTemplateName: content.data.newTemplateName
      - newTemplateId: content.data.newTemplateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "setup-页面创建openapi接口变更状态为已停用"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": $newTemplateUuid,
        "templateName": "",
        "updateStatus": 1
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "5" ]

- test:
    name: "setup-页面创建openapi接口删除模版"
    api: api/esignDocs/documents/template/delete.yml
    variables:
      json: {
        "templateId": $newTemplateUuid,
        "templateName": "",
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data, null ]