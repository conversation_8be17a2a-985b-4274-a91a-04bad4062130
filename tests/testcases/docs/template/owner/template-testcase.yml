- config:
    variables:
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      docNameOfFolder: "自动化测试文件夹名称"
      docCodeOfFolder: "AUTOTEST"
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      addContentName: "自动化测试内容域"
      addContentCode: "testcode"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      formatType: 0
      formatRule: "28"
      required: 0
      length: 28
      pageNo: 1
      posX: 34
      posY: 603
      width: 216
      height: 36



- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo()}
      - docCode: $docCodeOfFolder+${get_randomNo()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-添加内容域"
    api: api/esignDocs/content_domain/add.yml
    times: 5
    variables:
      - description: null
      - contentName: $addContentName+${get_randomNo()}
      - contentCode: $addContentCode+${get_randomNo_16()}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "编辑模板内容/左侧区域/添加内容域/分页功能验证"
    api: api/esignDocs/content_domain/list.yml
    variables:
      - contentName: null
      - page: $page
      - size: $size
    validate:
      - len_eq: ["content.data.list", 5]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "编辑模板内容/左侧区域/添加内容域/模糊搜索功能"
    api: api/esignDocs/content_domain/list.yml
    variables:
      - contentName: $addContentName
      - page: 1
      - size: $size
    validate:
      - len_eq: ["content.data.list", 5]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "编辑模板内容/添加内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - contentUuid: "AUTOUUID${get_randomNo()}"
      - contentCode: "AUTOCODE${get_randomNo()}"
      - contentName: $addContentName+${get_randomNo()}
      - edgeScope: 0

    extract:
      - templateContentUuid: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "编辑模板内容/编辑操作"
    api: api/esignDocs/template/owner/contentUpdate.yml
    variables:
      - description: null
      - templateContentUuid: $templateContentUuid
      - contentUuid: "AUTOUUID${get_randomNo()}UPDATE"
      - contentCode: "AUTOCODE${get_randomNo()}UPDATE"
      - contentName: $addContentName+${get_randomNo()}
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - edgeScope: 0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "编辑模板内容/删除操作"
    api: api/esignDocs/template/owner/contentDel.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - templateContentUuid: $templateContentUuid
      - edgeScope: 0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "编辑模板内容/添加内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    times: 5
    variables:
      - description: null
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - contentUuid: "AUTOUUIDADD${get_randomNo()}"
      - contentCode: "AUTOCODEADD${get_randomNo()}"
      - contentName: $addContentName+${get_randomNo()}
      - edgeScope: 0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "对应的内容域分页显示"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion

    validate:
      - length_greater_than_or_equals: ["content.data.contentList",5]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



