#我管理的-历史版本列表
- config:
    name: "模板相关"
    variables:
      docNameOfFolder: "自动化测试文件夹名称"
      docCodeOfFolder: "AUTOTEST"
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      signName: "自动化测试签署区${get_randomNo()}"
      timePosX: 10
      timePosY: 10

- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo()}
      - docCode: $docCodeOfFolder+${get_randomNo()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "setup-创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      signContent001: ${generate_field_object(签名区1, SIGN)}
      signContent002: ${generate_field_object(签名区2, SIGN)}
      fillContent001: ${generate_field_object(单行文本, TEXT)}
      fillContent002: ${generate_field_object(单行文本, TEXT)}
      fields_draft: [ $signContent001,$signContent002,$fillContent001,$fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_新增"
    api: api/esignDocs/template/owner/templateNew.yml
    variables:
      - fileKey:  ${ENV(fileKey)}
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      signContent001: ${generate_field_object(签名区1, SIGN)}
      signContent002: ${generate_field_object(签名区2, SIGN)}
      fillContent001: ${generate_field_object(单行文本, TEXT)}
      fillContent002: ${generate_field_object(单行文本, TEXT)}
      fields_draft: [ $signContent001,$signContent002,$fillContent001,$fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_新增"
    api: api/esignDocs/template/owner/templateNew.yml
    variables:
      - fileKey:  ${ENV(fileKey)}
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      signContent001: ${generate_field_object(签名区1, SIGN)}
      signContent002: ${generate_field_object(签名区2, SIGN)}
      fillContent001: ${generate_field_object(单行文本, TEXT)}
      fillContent002: ${generate_field_object(单行文本, TEXT)}
      fields_draft: [ $signContent001,$signContent002,$fillContent001,$fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_新增"
    api: api/esignDocs/template/owner/templateNew.yml
    variables:
      - fileKey:  ${ENV(fileKey)}
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      signContent001: ${generate_field_object(签名区1, SIGN)}
      signContent002: ${generate_field_object(签名区2, SIGN)}
      fillContent001: ${generate_field_object(单行文本, TEXT)}
      fillContent002: ${generate_field_object(单行文本, TEXT)}
      fields_draft: [ $signContent001,$signContent002,$fillContent001,$fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板历史记录"
    api: api/esignDocs/template/owner/templateVersionList.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板预览-openapi"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuid
      - account: "ceswdzxzdhyhwgd1.account"
      - password: "ceswdzxzdhyhwgd1.password"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板信息"
    api: api/esignDocs/template/owner/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

##todo 返回的文件大小的校验
#- test:
#    name: "导出文件与控件"
#    api: api/esignDocs/template/owner/templateArchive.yml
#    variables:
#      - templateUuid: $newTemplateUuid
#      - version: $newVersion