- config:
    variables:
      docNameOfFolder: "自动化测试文件夹名称"
      docCodeOfFolder: "AUTOTEST"
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      page: 1
      size: 5
      signName: "自动化测试签署区${get_randomNo()}"
      reName: "自动化测试签署区${get_randomNo()}"
      timePosX: 10
      timePosY: 10
      lengName: "超长签名主体测试-超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试超长签名主体测试"
      fileKey: ${ENV(fileKey)}

- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo()}
      - docCode: $docCodeOfFolder+${get_randomNo()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模版新建-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "指定了个人签署方_指定多页签署区_设置指定签署页页码带有中短线和逗号"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,4,6"
      - posX: 10
      - posY: 10
      - name: $signName
    extract:
      - signatoryUuid: content.data.list.0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "指定多页签署区_设置指定签署页页码带有全角逗号_设置失败"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - dateFormat: "yyyy-MM-dd"
      - signType: 2
      - edgeScope: null
      - pageNo: "1，2"
      - posX: 10
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","签署页码参数错误"]
      - eq: ["content.status",1600017]

- test:
    name: "指定多页签署区_设置指定签署页页码带有半角句号_设置失败"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - dateFormat: "yyyy-MM-dd"
      - addSignTime: 0
      - signType: 2
      - edgeScope: null
      - pageNo: "1.2"
      - posX: 10
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","签署页码参数错误"]
      - eq: ["content.status",1600017]


- test:
    name: "指定了机构的用章要求_指定了机构印章_指定骑缝章签署"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - dateFormat: "yyyy-MM-dd"
      - signType: 3
      - edgeScope: 0
      - pageNo: null
      - posX: null
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "指定骑缝章签署_设置指定正确的签署页码_骑缝章签署范围设置成功"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - dateFormat: "yyyy-MM-dd"
      - signType: 3
      - edgeScope: null
      - pageNo: "2-5"
      - posX: null
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "指定骑缝章签署_设置仅奇数页盖章_骑缝章签署范围设置成功"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - dateFormat: "yyyy-MM-dd"
      - signType: 3
      - edgeScope: 1
      - pageNo: null
      - posX: null
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "指定骑缝章签署_设置仅奇数页盖章_骑缝章签署范围设置成功"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 3
      - edgeScope: 2
      - pageNo: null
      - posX: null
      - posY: 10
      - dateFormat: "yyyy-MM-dd"
      - name: $signName


    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "指定了个人签署方_指定单页签署区_勾选显示签署日期_默认选中第一个样式"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 1
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: 1
      - posX: 10
      - posY: 10
      - name: $signName

    extract:
      - signatoryUuid: content.data.list.0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "更新签署日期的样式_文件中的预览样式正确"
    api: api/esignDocs/template/owner/signatoryUpdate.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - signatoryUuid: $signatoryUuid
      - addSignTime: 1
      - signType: 1
      - edgeScope: null
      - dateFormat: "yyyy年MM月dd日"
      - timePosX: 10
      - timePosY: 10
      - pageNo: 1
      - posX: 10
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "设置多页签_选择签署日期_每一个盖章处都会有签署日期"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 2
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: "1-4"
      - posX: 10
      - posY: 10
      - name: $signName


    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "签署区名称输入-非空验证"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 2
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: "1-4"
      - posX: 10
      - posY: 10
      - name: null

    validate:
      - eq: ["content.message","签署主体不能为空"]
      - eq: ["content.status",1600017]

- test:
    name: "签署区名称最大长度"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 2
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: "1-4"
      - posX: 10
      - posY: 10
      - name: $lengName

    validate:
      - eq: ["content.status",1600017]

- test:
    name: "setup-签署区名称重复"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 2
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: "1-4"
      - posX: 10
      - posY: 10
      - name: $reName

    validate:
      - eq: ["content.status",200]
      - eq: ["content.message","成功"]

- test:
    name: "签署区名称重复"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 2
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: "1-4"
      - posX: 10
      - posY: 10
      - name: $reName

    validate:
      - eq: ["content.status",200]
      - eq: ["content.message","成功"]


- test:
    name: "setup-签署日期的删除"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 1
      - signType: 1
      - edgeScope: null
      - dateFormat: "yyyy-MM-dd"
      - timePosX: 10
      - timePosY: 10
      - pageNo: 1
      - posX: 10
      - posY: 10
      - name: $signName

    extract:
      - signatoryUuid: content.data.list.0

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "签署日期的删除"
    api: api/esignDocs/template/owner/signatoryUpdate.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - signatoryUuid: $signatoryUuid
      - addSignTime: 0
      - signType: 1
      - edgeScope: null
      - dateFormat: null
      - timePosX: null
      - timePosY: null
      - pageNo: 1
      - posX: 10
      - posY: 10
      - name: $signName

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "签署区的删除"
    api: api/esignDocs/template/owner/signatoryDel.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - signatoryUuid: $signatoryUuid

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "对应的签署区"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-对应的内容域分页显示"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    times: 5
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1-6"
      - posX: 10
      - posY: 10
      - name: $signName+${get_randomNo()}


- test:
    name: "对应的内容域分页显示"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - signatoryUuid: $signatoryUuid

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "签署区名称输入"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - dateFormat: "yyyy-MM-dd"
      - signType: 2
      - edgeScope: null
      - pageNo: "1,2"
      - posX: 10
      - posY: 10
      - name: null

    validate:
      - eq: ["content.message","签署主体不能为空"]
      - eq: ["content.status",1600017]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1-6"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "盖章页-所有页"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "盖章页-当前页"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "盖章页-指定页"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签署区"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    extract:
      - signatoryUuid1: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "批量修改签署区"
    api: api/esignDocs/template/owner/signatoryDomain_batchUpdate.yml
    variables:
      - batchUpdates: [
            {
                "addSignTime": 0,
                "name": "甲方企业",
                "pageNo": "2",
                "posX": 415,
                "posY": 713,
                "edgeScope": null,
                "signType": 1,
                "signatoryUuid": $signatoryUuid1,
                "templateUuid": $newTemplateUuid1,
                "version": 1
            }
        ]
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "关键字坐标查询：查询'劳动合同'在newTemplateUuid1文档中的位置信息"
    api: api/esignDocs/template/owner/keywordPos.yml
    variables:
      - params: {
        "keywords": [
            "劳动合同"
        ],
        "pageNo": "1-9",
#        "offsetX": 5,
#        "offsetY": -6,
        "templateUuid": $newTemplateUuid1,
        "rightOrLeft": 1,
        "version": "1"
    }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: ["content.data.keyPositions.0.exist", true]
      - length_greater_than: ["content.data.keyPositions.0.positions", 0]