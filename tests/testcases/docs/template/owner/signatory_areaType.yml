- config:
    name: "pdf模板签署区支持自定义签署区尺寸(6.0.14-beta.1)"
    variables:
      templateFileKey: ${ENV(fileKey)}
      signatoryName: "自动化测试签署区名称"
      pageNo: "1"
      addSignTime: 0
      signType: 1

- test:
    name: "setup-新建pdf文档模板"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      fileKey: $templateFileKey
    extract:
      newTemplateUuid: content.data.templateUuid
      newVersion: content.data.version
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]




- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "setup-添加签名域自定义大小"
    api: api/esignDocs/epaasTemplate/signatoryAddByEpaas.yml
    times: 1
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      contentId_signatory: $_contentId
      resourceId_signatory: $_resourceId
      label: "普通章单页"
      width: 300
      height: 200
      addSealRule: "custom"
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "普通章的单页出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "1"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: "TC-查询pdf模板详情"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      templateUuid: $newTemplateUuid
      version: $newVersion
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]



- test:
    name: "普通章的多页出参校验"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuid
    validate:
      - eq: ["content.data.sealControl.0.page", "1"]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
      - eq: ["content.data.fields.0.style.width",300]
      - eq: ["content.data.fields.0.style.height",200]