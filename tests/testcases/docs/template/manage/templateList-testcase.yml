- config:
    variables:
      docNameOfFolder: "自动化测试文件夹名称"
      docCodeOfFolder: "AUTOTEST"
      queryTemplateName: "自动化测试模版-合同测试${get_randomNo()}"
      queryTemplateNameWithBlank: "自动化测试模版-合 同测试${get_randomNo()}"
      templateRandomNo: $docNameOfFolder+${get_randomNo()}
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}

- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo()}
      - docCode: $docCodeOfFolder+${get_randomNo()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $queryTemplateName
      - createUserOrg: $createUserOrgCode
      - description: null
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-模版新建:模板名称中间包含空格"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $queryTemplateNameWithBlank
      - createUserOrg: $createUserOrgCode
      - description: null
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模板名称正常搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合同"
      - standard: null
      - status: null
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - contains: ["content.data.list.0.templateName", "合同"]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "输入模板名称前后有空格"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: " 合同 "
      - standard: null
      - status: null
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - contains: ["content.data.list.0.templateName", "合同"]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "输入模板名称中间包含空格"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合 同"
      - standard: null
      - status: null
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - contains: ["content.data.list.0.templateName", "合 同"]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板名称为空"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: ""
      - standard: null
      - status: null
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "根据模板状态草稿搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: null
      - standard: null
      - status: 'DRAFT'
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - length_greater_than: ["content.data.list", 1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "根据模板状态停用搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: null
      - standard: null
      - status: 'SUSPEND'
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "根据模板状态发布搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: null
      - standard: null
      - status: 'PUBLISH'
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "组合搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合同"
      - standard: null
      - status: 'PUBLISH'
      - minSignCount: null
      - page: 1
      - size: 10

    validate:
      - contains: ["content.data.list.0.templateName", "合同"]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "翻页之后搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合同"
      - standard: null
      - status: 'PUBLISH'
      - minSignCount: null
      - page: 3
      - size: 10

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "选择每页条数之后搜索"
    api: api/esignDocs/template/manage/templateList.yml
    variables:
      - docUuid: null
      - templateName: "合同"
      - standard: null
      - status: 'PUBLISH'
      - minSignCount: null
      - page: 1
      - size: 20

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

