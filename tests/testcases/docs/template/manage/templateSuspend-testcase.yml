- config:
    variables:
      docNameOfFolder: "自动化测试文件夹名称"
      docCodeOfFolder: "AUTOTEST"
      commonTemplateName: "自动化测试模版-合同测试"
      description: "自动化测试描述"
      FileName: "testppp.pdf"
      fileKey: ${ENV(fileKey)}
      signName: "自动化测试签署区${get_randomNo()}"
      timePosX: 10
      timePosY: 10

- test:
    name: "setup-在根目录下新增一个文件类型"
    api: api/esignDocs/docConfigure/addDocConfigure.yml
    variables:
      - docName: $docNameOfFolder+${get_randomNo()}
      - docCode: $docCodeOfFolder+${get_randomNo()}
      - docType: 2
      - parentUid:
    validate:
      - eq: ["content.data",null]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-列表搜索上个用例新增的的文件夹信息"
    api: api/esignDocs/docConfigure/getDocConfigureList.yml
    variables:
      - docName: $docNameOfFolder
      - docType: 2
      - parentUid: null
    extract:
      - autoTestDocUuid1: content.data.0.docUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - length_greater_than_or_equals: ["content.data.0.docUuid",32]

- test:
    name: "创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCode: content.data.organizationCode
      - createUserCode: content.data.userCode
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "停用草稿模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-停用"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "停用停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","该模板状态已变更无法进行此操作，请刷新页面查看模板最新状态"]
      - eq: ["content.status",1605003]
      - eq: ["content.data",null]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_新增"
    api: api/esignDocs/template/manage/templateNew.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: 1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "停用已发布模板_历史版本_"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_新增版本"
    api: api/esignDocs/template/manage/templateNew.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "停用已发布模板_最新版本_"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-停用"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "停用已删除模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","企业模板不存在。"]
      - eq: ["content.status",1605001]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup_新增"
    api: api/esignDocs/template/manage/templateNew.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "停用新增版本"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: $signName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      
- test:
    name: "setup_发布"
    api: api/esignDocs/template/manage/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: $newVersion
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_新增"
    api: api/esignDocs/template/manage/templateNew.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateType: 1
      - templateName: $commonTemplateName+${get_randomNo()}
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
      - sourceTemplateUuid: $newTemplateUuid
      - sourceVersion: $newVersion
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "停用模板_包含新增草稿状态版本_"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

