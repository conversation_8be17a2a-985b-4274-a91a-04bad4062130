- config:
    name: 文件模板列表，返回baseFileKey
    variables:
      - userCode: ${ENV(sign01.userCode)}
      - pixff1: ${generate_random_str(7)}
      - pixff2: ${generate_random_str(5)}
      - templateNamePdf: "测试pdf模版$pixff1"
      - templateNameDoc: "测试doc模版$pixff2"
      - fileKeyPdf: ${ENV(fileKey)}
      - fileKey1: ${ENV(fileKey)}
      - docPageFileKey: ${ENV(docPageFileKey)}
      - createUserOrgCode: ${ENV(sign01.main.orgCode)}
      - domainHost: ${ENV(esign.projectHost)}
      - outerDomainHost: ${ENV(esign.projectOuterHost)}
      - newFileKey: ${ENV(3PageFileKey)}
      - templateNameCommon: "自动化测试通用模版-${get_randomNo_16()}"
      - description: "自动化测试描述"
      - signNameA: "甲方企业"
      - signNameB: "乙方企业"
      - contentCodeCommon: ${get_randomNo_16()}
      - contentNameCommon: "自动化测试-文本0507"
      - contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      - dataSource: null
      - sourceField: null
      - font: "SimSun"
      - fontSize: 14
      - fontColor: "BLACK"
      - fontStyle: "Normal"
      - textAlign: "Left"
      - required: 0
      - length: 28
      - pageNo: 1
      - posX: 188
      - posY: 703
      - width: 216
      - height: 36
      - initiatorAll: 1
      - checkRepetition: 0
      - timePosX: 10
      - timePosY: 10
      - formatType: 0
      - formatRule: "28"




- test:
    name: "TC-openapi创建pdf文档模板"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
        userCode: $userCode
        templateName: $templateNamePdf
        docTypeId: ${get_a_docConfig_type()}
        fileKey: $fileKeyPdf
    extract:
      - templateId1: content.data.templateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateId, $templateId1]



- test:
    name: "TC-查询pdf业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
       templateName: $templateNamePdf
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $fileKeyPdf]




- test:
    name: "TC-变更pdf文档模板为已发布"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": $templateId1,
        "templateName": "",
        "updateStatus": 0
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "4" ]



- test:
    name: "TC-查询pdf业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
       templateName: $templateNamePdf
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $fileKeyPdf]
- test:
    name: "TC-openapi创建doc文档模板"
    api: api/esignDocs/documents/template/createTemplate.yml
    variables:
        userCode: $userCode
        templateName: $templateNameDoc
        docTypeId: ${get_a_docConfig_type()}
        fileKey: $docPageFileKey
    extract:
      - templateId2: content.data.templateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateId, $templateId2]

- test:
    name: "TC-查询word业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
       templateName: $templateNameDoc
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $docPageFileKey]
- test:
    name: "TC-变更word文档模板为已发布"
    api: api/esignDocs/documents/template/updateStatus.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": "",
        "updateStatus": 0
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateStatus, "4" ]
- test:
    name: "TC-查询word业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
       templateName: $templateNameDoc
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $docPageFileKey]

- test:
    name: "TC-替换pdf文档模版底稿文件"
    api: api/esignDocs/documents/template/replace.yml
    variables:
      templateId: $templateId1
      fileKey: $newFileKey
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.success, true ]
      - ne: [ content.data.pageNo, "" ]

- test:
    name: "TC-查询替换底稿文件的pdf业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
       templateName: $templateNamePdf
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $newFileKey]


- test:
    name: "setup-复制pdf文档模版"
    api: api/esignDocs/documents/template/copy.yml
    variables:
      json: {
        "templateId": $templateId1,
        "templateName": "",
        "renameTemplate": ""
      }
    extract:
      - newTemplateName1: content.data.newTemplateName
      - newTemplateId1: content.data.newTemplateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "setup-复制word文档模版"
    api: api/esignDocs/documents/template/copy.yml
    variables:
      json: {
        "templateId": $templateId2,
        "templateName": "",
        "renameTemplate": ""
      }
    extract:
      - newTemplateName2: content.data.newTemplateName
      - newTemplateId2: content.data.newTemplateId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: "TC-查询pdf业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      templateName: $templateNamePdf
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $newFileKey ]

- test:
    name: "TC-查询word业务模板"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      templateName: $newTemplateName2
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $docPageFileKey ]

- test:
    name: "停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $templateId1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC-删除pdf模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $templateId1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]

- test:
    name: "停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $templateId2
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]
- test:
    name: "TC-删除word模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $templateId2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]


- test:
    name: "TC-删除pdf模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $newTemplateId1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]
- test:
    name: "TC-删除word模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $newTemplateId2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]


#页面创建模板
- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey1
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: ""
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon1: content.data.templateUuid
      - newVersionCommon1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC-查询未发布业务模板底稿文件"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      templateName: $templateNameCommon
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $fileKey1 ]

- test:
    name: "TC2-setup_添加签名区1"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: $signNameA
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup_添加签名区2"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - addSignTime: 0
      - signType: 1
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1"
      - posX: 10
      - posY: 10
      - name: $signNameB
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-setup-模板关联内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - contentUuid: $contentUuidCommon
      - contentCode: $contentCodeCommon
      - contentName: $contentNameCommon
      - edgeScope: 0
    extract:
      - templateContentUuidCommon: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC-查询有内容域已发布业务模板底稿文件"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      templateName: $templateNameCommon
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.templateInfos.0.baseFilekey, $fileKey1 ]

- test:
    name: "停用模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success",True ]
      - eq: [ "content.data",null ]

- test:
    name: "TC-删除模版"
    api: api/esignDocs/template/manage/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: [ "content.success", true ]