- config:
    name: pageNo校验

- test:
    name: "TC1-正常查询"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC2-pageIndex必填不传"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo:
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","页码不能为空" ]

- test:
    name: "TC3-pageIndex为null"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: null
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","页码不能为空" ]

- test:
    name: "TC4-pageIndex为空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: "   "
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","页码不能为空" ]

- test:
    name: "TC5-pageIndex为非int类型_为string"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: "asd"
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageNo参数错误!" ]

- test:
    name: "TC6-pageIndex为非正整数_为-1"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: -1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","当前页不小于1" ]

- test:
    name: "TC7-pageIndex为2147483647"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 2147483647
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]

- test:
    name: "TC8-pageIndex为2147483648"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 2147483648
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageNo参数错误!" ]

- test:
    name: "TC9-pageIndex为非int类型_为1_5"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1.5
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageNo参数错误!" ]

- test:
    name: "TC10-pageIndex为非正整数_为0"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 0
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","当前页不小于1" ]

- test:
    name: "TC11-pageIndex为_1"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]
