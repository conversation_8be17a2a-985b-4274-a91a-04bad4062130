- config:
    variables:
      - spaceChar: "   "
      - autoTestDocUuid1Common: ${get_a_docConfig_type()}

- test:
    name: "TC1-docTypeId非必填"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC2-docTypeId精确查询"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: $autoTestDocUuid1Common
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC3-docTypeId两端带有空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: $spaceChar$autoTestDocUuid1Common$spaceChar
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC4-docTypeId为null"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: null
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC5-docTypeId为空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: $spaceChar
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC6-docTypeId中间有空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "123  321"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC7-docTypeId非string类型_为数字"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: $autoTestDocUuid1Common
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC8-docTypeId为非string类型_为boolean"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: true
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC9-docTypeId长度为1"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "a"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC10-docTypeId长度为36"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "abcdefghijklmnopqrstuvwxyz0123456789"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC11-docTypeId长度为20"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "abcdefghijklmnopqrst"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC12-docTypeId长度为37"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "1abcdefghijklmnopqrstuvwxyz0123456789"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","文件类型id最大长度36" ]

- test:
    name: "TC13-docTypeId带有特殊字符"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId: "\ / : * ? \" < > |"
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1600017 ]
      - startswith: [ "content.message","文件类型id包含特殊字符" ]