
- test:
    name: "TC2-pageSize必填_不传"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize:
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不能为空" ]

- test:
    name: "TC3-pageSize为null"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: null
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不能为空" ]

- test:
    name: "TC4-pageSize为空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: "   "
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不能为空" ]

- test:
    name: "TC5-pageSize为非int类型_为string"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: "adf"
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC6-pageSize为非int类型_为2147483648"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 2147483648
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC7-pageSize为非int类型_为0"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 0
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不小于1条" ]

- test:
    name: "TC8-pageSize为非int类型_为-1"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: -1
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不小于1条" ]

- test:
    name: "TC9-pageSize为非int类型_为2_46"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 2.46
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC10-pageSize为2147483647"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 2147483647
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不超过50条" ]

- test:
    name: "TC11-pageSize为3"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 3
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC12-pageSize为30"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 30
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC13-pageSize为31"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName: "测试"
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 31
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]