- config:
    teardown_hooks:
      - ${teardown_delete()}
    variables:
      - docUuid: ${get_a_docConfig_type()}

- test:
    name: "TC1-创建时间正常查询"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022-04-11 00:00:01"
      - createEndTime: "2032-04-11 00:00:01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC2-创建时间非必填"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC3-创建时间为null或空格"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: null
      - createEndTime: "  "
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC4-只传createStartTime"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022-04-11 00:00:01"
      - createEndTime:
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605041 ]
      - eq: [ "content.message","开始时间和结束时间必须都传" ]
      - eq: [ "content.data",null ]

- test:
    name: "TC5-只传createEndTime"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime:
      - createEndTime: "2032-04-11 00:00:01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605041 ]
      - eq: [ "content.message","开始时间和结束时间必须都传" ]
      - eq: [ "content.data",null ]

- test:
    name: "TC6-不是yyyy-MM-dd_HH_mm_ss格式"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022/04/11 00/00/01"
      - createEndTime: "2032/04/11 00/00/01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605044 ]
      - eq: [ "content.message","错误的开始时间" ]
      - eq: [ "content.data",null ]

- test:
    name: "TC7-开始时间晚于结束时间"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2032-04-11 00:00:01"
      - createEndTime: "2022-04-11 00:00:01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605042 ]
      - eq: [ "content.message","开始时间大于结束时间" ]
      - eq: [ "content.data",null ]

- test:
    name: "TC8-开始时间等于结束时间"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022-04-11 00:00:01"
      - createEndTime: "2022-04-11 00:00:01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.total",0 ]

- test:
    name: "TC9-开始时间不存在"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022-02-30 12:23:54"
      - createEndTime: "2032-04-11 00:00:01"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605044 ]
      - eq: [ "content.message","错误的开始时间" ]
      - eq: [ "content.data",null ]

- test:
    name: "TC10-结束时间不存在"
    api: api/esignDocs/documents/template/list-api.yml
    variables:
      - templateName:
      - docTypeId:
      - organizationCode:
      - organizationName:
      - createStartTime: "2022-04-11 12:23:54"
      - createEndTime: "2032-04-11 12:78:54"
      - pageNo: 1
      - pageSize: 10
    validate:
      - eq: [ "content.code",1605051 ]
      - eq: [ "content.message","错误的结束时间" ]
      - eq: [ "content.data",null ]
