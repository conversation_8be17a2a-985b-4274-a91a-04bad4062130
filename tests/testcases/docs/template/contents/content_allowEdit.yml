- config:
    name: "文档模版内容域新增可修改属性、默认值********-beta.5---对接epaas后这个功能没了"
    variables:
      pdfFileKey0: ${ENV(fileKey)}
      wordFileKey0: ${ENV(docPageFileKey)}
      templateNameCommon0: "自动化测试模版${get_randomNo_16()}"
      templateNameCommon1: "自动化测试模版${get_randomNo_16()}"
      htmlFileName: "word-测试模板.html"
      description: "自动化测试描述"
      timePosX: 10
      timePosY: 10
      formatRule: ""
      formatType: 0
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentNameCommon2: "自动化测试-文本0508"
#      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      name: "测试内容域"
      contentCode: "ceszdh"


- test:
    name: "case1-引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "case2-引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "case3-setup_模版新建-pdf文档"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $pdfFileKey0
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon0
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon01: content.data.templateUuid
      - newVersionCommon01: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "case4-添加可修、有默认值内容域-文本"
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - contentName: $contentNameCommon
      - formatType: 0
      - formatRule: $formatRule
      - edgeScope: 0
      - contentUuid:
      - contentCode:
      - allowEdit: 1
      - defaultContentValue: "123456"
      - required: 0
    extract:
      templateContentUuid01: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case5-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case6-查看文档模版预览页-验证内容域可修改是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case7-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case8-查看文档模版预览页-验证内容域可修改是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case9-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "case10-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case11-setup_模版新建-pdf文档2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $pdfFileKey0
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon1
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon02: content.data.templateUuid
      - newVersionCommon02: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case12-添加不可修改、有默认值内容域-文本"
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - contentName: $contentNameCommon
      - formatType: 0
      - formatRule: $formatRule
      - edgeScope: 0
      - contentUuid:
      - contentCode:
      - allowEdit: 0
      - defaultContentValue: "1234567890"
      - required: 0
    extract:
      templateContentUuid02: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case13-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case14-查看文档模版预览页-验证内容域不可修改"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case15-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case16-查看文档模版预览页-验证内容域不可修改"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case17-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "case18-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "case19-上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - wordFileKey01

- test:
    name: "case20-模版新建-word文档"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $wordFileKey01
      - zipFileKey: null
      - templateName: $templateNameCommon1
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon03: content.data.templateUuid
      - newVersionCommon03: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case21--上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "case22-保存模板-添加都为可修改内容域"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "allowEdit": 1,
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "required": 0,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "allowEdit": 1,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": false,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon03,
        "version": $newVersionCommon03,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case23-查看文档模版预览页-验证内容域可修改"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: case24-保存模板-添加都为不可修内容域
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "allowEdit": 0,
            "required": 0,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": true,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon03,
        "version": $newVersionCommon03,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case25-查看文档模版预览页-验证内容域不可修改"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case26-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case27-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

