- config:
    name: "文档模版内容域新增可加密属性"
    variables:
      pdfFileKey0: ${ENV(fileKey)}
      wordFileKey0: ${ENV(docPageFileKey)}
      templateNameCommon0: "自动化测试模版内容域不加密变加密-pdf-${get_randomNo_16()}"
      templateNameCommon1: "自动化测试模版内容域加密变不加密-pdf-${get_randomNo_16()}"
      templateNameCommon2: "自动化测试模版不加密-word-${get_randomNo_16()}"
      templateNameCommon3: "自动化测试模版加密-word-${get_randomNo_16()}"
      htmlFileName: "word-测试模板.html"
      description: "自动化测试描述"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentNameCommon2: "自动化测试-文本0508"


- test:
    name: "case1-引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "case2-引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "case3-setup_模版新建-pdf文档"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $pdfFileKey0
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon0
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon01: content.data.templateUuid
      - newVersionCommon01: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case4-添加不加密内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - contentName: $contentNameCommon
      - edgeScope: 0
      - contentUuid: ""
      - contentCode: ""
      - required: 1
      - encrypted: 0
    extract:
      templateContentUuid01: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case5-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case6-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case7-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case8-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case9-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "cse10-编辑模版中内容域-将内容域变更为加密"
    api: api/esignDocs/template/owner/contentUpdate.yml
    variables:
      - description: null
      - templateContentUuid: $templateContentUuid01
      - contentUuid: ""
      - contentCode: ""
      - contentName: $contentNameCommon
      - templateUuid: $newTemplateUuidCommon01
      - encrypted: 1
      - version: $newVersionCommon01
      - edgeScope: 0
      - required: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case11-setup-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case12-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case13-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case14-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case15-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
      - version: $newVersionCommon01
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case16-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon01
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case17-setup_模版新建-pdf文档2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $pdfFileKey0
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon1
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon02: content.data.templateUuid
      - newVersionCommon02: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case18-添加加密内容域"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - contentName: $contentNameCommon
      - edgeScope: 0
      - contentUuid: ""
      - contentCode: ""
      - required: 1
      - encrypted: 1
    extract:
      templateContentUuid02: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case19-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case20-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case21-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case22-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case23-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case24-编辑模版中内容域-将内容域变更为不加密"
    api: api/esignDocs/template/owner/contentUpdate.yml
    variables:
      - description: null
      - templateContentUuid: $templateContentUuid02
      - contentUuid: ""
      - contentCode: ""
      - contentName: $contentNameCommon
      - templateUuid: $newTemplateUuidCommon02
      - encrypted: 0
      - version: $newVersionCommon02
      - edgeScope: 0
      - required: 1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case25-setup-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case26-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case27发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case28-查看文档模版预览页-验证内容域加密是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case29-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
      - version: $newVersionCommon02
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case30-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon02
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "case31-上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - wordFileKey01

- test:
    name: "case32-模版新建-word文档"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $wordFileKey01
      - zipFileKey: null
      - templateName: $templateNameCommon2
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon03: content.data.templateUuid
      - newVersionCommon03: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case33--上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "case34-保存模板-添加都为不加密内容域"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "required": 0,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": false,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon03,
        "version": $newVersionCommon03,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case35-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case36-保存模板-添加都为不加密内容域"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "0",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "required": 0,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": true,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon03,
        "version": $newVersionCommon03,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case37-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case38-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
      - version: $newVersionCommon03
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case39-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon03
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "case40-模版新建-word文档2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $wordFileKey01
      - zipFileKey: null
      - templateName: $templateNameCommon3
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon04: content.data.templateUuid
      - newVersionCommon04: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "case41-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey1: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "case42-保存模板-添加都为加密内容域"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "required": 0,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": false,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon04,
        "version": $newVersionCommon04,
        "fileKey": $htmlFileKey1
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case43-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon04
      - version: $newVersionCommon04
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case44-发布模板-添加都为加密内容域"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "encrypted": "1",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "required": 0,
            "thirdKey": "ele-1655963783528"
          }
        ],
        "isPublish": true,
        "signatories": [],
        "templateUuid": $newTemplateUuidCommon04,
        "version": $newVersionCommon04,
        "fileKey": $htmlFileKey1
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "case45-查看文档模版预览页-验证内容域加密字段是否正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon04
      - version: $newVersionCommon04
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - contains: [ content.data.editUrl, "epaas-file-template"]
      - contains: [ content.data.previewUrl, "epaas-file-template"]

- test:
    name: "case46-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon04
      - version: $newVersionCommon04
#    setup_hooks:
#      - ${sleep(2)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "case47-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon04
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]





