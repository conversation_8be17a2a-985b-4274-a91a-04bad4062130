- config:
    name: "文本类型内容域格式新增行间距测试用例-6.0.14-beta.1"
    variables:
      templateFileKey: ${ENV(fileKey)}
      autoTestDocUuid1: ${get_docConfig_type()}
      docRandomNo: ${get_randomNo()}
      templateName: "自动化测试模板${get_randomNo()}"
      contentName_multipliedLeading: "自动化测试内容域"
      contentCode_multipliedLeading: "zdhcsypcode"
      required: 1 #必填
      font: SimSun
      fontColor: RED
      fontSize: 12
      fontStyle: Normal
      formatRule: ""
      formatType: 0
      textAlign: Left
      leaveGroup: true
      offsetX: 0
      offsetY: 0
      encrypted: 0
      edgeScope: 0
      dataSource:
      sourceField:
      description: "自动化测试添加内容域"
      contentUuid:
      presetName_multipliedLeading: "自动化测试电子签署业务模板-${getDateTime()}"
      sign01UserCode: ${ENV(sign01.userCode)}
      sign01AccountNo: ${ENV(sign01.accountNo)}

- test:
    name: "setup-新建pdf文档模板"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      fileKey: $templateFileKey
      docUuid: $autoTestDocUuid1
    extract:
      newTemplateUuid1: content.data.templateUuid
      newVersion1: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-添加内容域-文本类型-1倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 1-$contentCode_multipliedLeading
      contentName: 1-$contentName_multipliedLeading
      contentUuid:
      length: 64
      multipliedLeading: 1
      height: 79.2
      pageNo: 1
      posX: 87
      posY: 699
      width: 262.4
    extract:
      templateContentUuid1: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-添加内容域-文本类型-1.15倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 2-$contentCode_multipliedLeading
      contentName: 2-$contentName_multipliedLeading
      contentUuid:
      length: 64
      multipliedLeading: 1.15
      height: 79.2
      pageNo: 1
      posX: 87
      posY: 699
      width: 262.4
    extract:
      templateContentUuid2: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-添加内容域-文本类型-1.5倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 3-$contentCode_multipliedLeading
      contentName: 3-$contentName_multipliedLeading
      contentUuid:
      length: 48
      multipliedLeading: 1.5
      height: 79.2
      pageNo: 1
      posX: 115
      posY: 602
      width: 262.4
    extract:
      templateContentUuid3: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-添加内容域-文本类型-2.0倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 4-$contentCode_multipliedLeading
      contentName: 4-$contentName_multipliedLeading
      contentUuid:
      length: 32
      multipliedLeading: 2
      height: 79.2
      pageNo: 1
      posX: 115
      posY: 493
      width: 262.4
    extract:
      templateContentUuid4: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-添加内容域-文本类型-2.5倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 5-$contentCode_multipliedLeading
      contentName: 5-$contentName_multipliedLeading
      contentUuid:
      length: 32
      multipliedLeading: 2.5
      height: 79.2
      pageNo: 1
      posX: 115
      posY: 384
      width: 262.4
    extract:
      templateContentUuid5: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-添加内容域-文本类型-3.0倍行间距"
    api: api/esignDocs/template/owner/contentAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      contentCode: 6-$contentCode_multipliedLeading
      contentName: 6-$contentName_multipliedLeading
      contentUuid:
      length: 16
      multipliedLeading: 3
      height: 79.2
      pageNo: 1
      posX: 115
      posY: 275
      width: 262.4
    extract:
      templateContentUuid6: content.data.list.0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-模板详情-内容域行间距返回正确"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
    validate:
      - eq: [ content.data.editUrl, "epaas-file-template" ]

- test:
    name: "setup-发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-使用openapi填写生成pdf-generatePdfFile"
    api: api/esignDocs/documents/template/generatePdfFile.yml
    variables:
      templateId: $newTemplateUuid1
      fileName: "test"
      contentsControl: [
        {
           contentId: ,
           contentCode: 1-$contentCode_multipliedLeading,
           contentValue: "${generate_random_string(64)}"
        },
        {
          contentId: ,
          contentCode: 2-$contentCode_multipliedLeading,
          contentValue: "${generate_random_string(64)}"
        },
        {
          contentId: ,
          contentCode: 3-$contentCode_multipliedLeading,
          contentValue: "${generate_random_string(48)}"
        },
        {
          contentId: ,
          contentCode: 4-$contentCode_multipliedLeading,
          contentValue: "${generate_random_string(32)}"
        },
        {
          contentId: ,
          contentCode: 5-$contentCode_multipliedLeading,
          contentValue: "${generate_random_string(32)}"
        },
        {
          contentId: ,
          contentCode: 6-$contentCode_multipliedLeading,
          contentValue: "${generate_random_string(16)}"
        }
      ]
    validate:
      - len_gt: [ "content.data.fileKey",0 ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.templateId",$newTemplateUuid1 ]
      - eq: [ "content.data.fileName","test.pdf" ]


