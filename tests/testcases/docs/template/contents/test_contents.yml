- config:
    variables:
      templateId: ${getTemplateId(0,0,pdf,0)}

- test:
    name: "TC1-templateId为空"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": ""}
    validate:
      - eq: [ "content.code",1600017 ]
      - ge: [ "content.message","templateId不能为空" ]
- test:
    name: "TC2-templateId不存在"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "11111"}
    validate:
      - eq: [ "content.code",1605001 ]
      - ge: [ "content.message","企业模板不存在。" ]
- test:
    name: "TC3-templateId为特殊符号"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "\/:*?<>|"}
    validate:
      - eq: [ "content.code",1605001 ]
      - ge: [ "content.message","企业模板不存在。" ]
- test:
    name: "TC4-templateId真实存在"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": $templateId}
    validate:
      - eq: [ "content.code",200 ]
      - contains: [ "content.message","成功" ]
- test:
    name: "TC7-templateId为null"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "null"}
    validate:
      - eq: [ "content.code",1605001 ]
      - ge: [ "content.message","企业模板不存在。" ]
- test:
    name: "TC8-templateId为空格"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "  "}
    validate:
      - eq: [ "content.code",1600017 ]
      - ge: [ "content.message","templateId不能为空" ]
- test:
    name: "TC9-templateId为非String类型，为boolean"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "true"}
    validate:
      - eq: [ "content.code",1605001 ]
      - ge: [ "content.message","企业模板不存在。" ]


