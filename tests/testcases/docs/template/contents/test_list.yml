- config:
    name: "模板相关"


- test:
    name: "TC1-pageNo必填，不传"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
              "createEndTime": "",
              "createStartTime": "",
              "docTypeId": "",
              "pageNo": "",
              "pageSize": "1",
              "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","页码不能为空"]
- test:
    name: "TC2-pageNo为非int类型，为String"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "string",
        "pageSize": "",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageNo参数错误!" ]
- test:
    name: "TC3-pageNo为int类型，为0"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "0",
        "pageSize": "1",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","当前页不小于1" ]

- test:
    name: "TC4-pageNo为非int类型，为1111111111111111111111"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "11111111111111111111111",
        "pageSize": "",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageNo参数错误!" ]
- test:
    name: "TC5-pageSize必填，不传"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","单页数量不能为空" ]
- test:
    name: "TC6-pageSize为非int类型，为String"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "string",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageSize参数错误!" ]
- test:
    name: "TC7-pageSize为非int类型，为1111111111111111"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "11111111111111",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1600015 ]
      - eq: [ "content.message","pageSize参数错误!" ]
- test:
    name: "TC8-开始时间格式不对"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "111",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1605044 ]
      - eq: [ "content.message","错误的开始时间" ]
- test:
    name: "TC9-结束时间格式不对"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "111",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1605051 ]
      - eq: [ "content.message","错误的结束时间" ]
- test:
    name: "TC10-docTypeId输入数据不存在"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "11",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",1603001 ]
      - eq: [ "content.message","文件夹或文件类型不存在" ]

- test:
    name: "TC11-正常查询"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": ""
      }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC12-templateStatus为空字符串"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": ""
      }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC13-templateStatus非2、4、5"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": 7
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","文件模板状态仅支持2、4或者5" ]
      - eq: [ "content.data", null ]

- test:
    name: "TC14-templateStatus非int类型"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": "hah"
      }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","文件模板状态仅支持2、4或者5" ]
      - eq: [ "content.data", null ]

- test:
    name: "TC15-templateStatus为2"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": 2
      }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.templateInfos.0.templateStatus","2"]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC16-templateStatus为4"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": 4
      }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.templateInfos.0.templateStatus","4" ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC17-templateStatus为5"
    api: api/esignDocs/documents/template/list.yml
    variables:
      json: {
        "createEndTime": "",
        "createStartTime": "",
        "docTypeId": "",
        "pageNo": "1",
        "pageSize": "10",
        "templateName": "",
        "templateStatus": 5
      }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.templateInfos.0.templateStatus","5" ]
      - eq: [ "content.message","成功" ]