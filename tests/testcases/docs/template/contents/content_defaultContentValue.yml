- config:
    name: "文档模版内容域新增默认值-********-beta.5-默认值格式检验"
    variables:
      pdfFileKey0: ${ENV(fileKey)}
      wordFileKey0: ${ENV(docPageFileKey)}
      templateNameCommon0: "自动化测试模版-${get_randomNo_16()}"
      templateNameCommon1: "自动化测试模版-${get_randomNo_16()}"
      htmlFileName: "word-测试模板.html"
      description: "自动化测试描述"
      timePosX: 10
      timePosY: 10
      formatRule: ""
      formatType: 0
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentNameCommon2: "自动化测试-文本0508"
      name: "测试内容域"
      contentCode: ""
      contentUuid: ""


- test:
    name: "tc-引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "tc-引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "tc_模版新建-pdf文档"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $pdfFileKey0
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon0
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC-文本域默认值格式校验"
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "文本"
      - formatType: 0
      - formatRule: ""
      - edgeScope: 0
      - contentUuid:
      - contentCode:
      - length: 28
      - allowEdit: 1
      - defaultContentValue: "text文本1234567890text文本1234567890text文本1234567890text文本1234567890text文本1234567890"
      - dataSource: $dataSource
      - required: 0
    validate:
      - eq: ["content.message","【文本】格式长度校验失败，最大长度【28】"]
      - eq: ["content.status",1601007]

- test:
    name: TC-内容域手机号-默认值格式校验(后端没校验)
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "手机号"
      - formatType: 1
      - allowEdit: 1
      - formatRule: ""
      - edgeScope: 0
      - length: 11
      - allowEdit: 1
      - defaultContentValue: "12343456123456"
      - contentCode: ""
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]



- test:
    name: TC-数字默认值格式检验（无校验）
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "数字"
      - formatType: 6
      - allowEdit: 1
      - formatRule: "2"
      - edgeScope: 0
      - length: 15
      - required: 1
      - allowEdit: 1
      - defaultContentValue: "qqq"
      - contentCode: ""
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "TC-添加内容域日期格式校验-后端没校验 "
    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - contentName: "日期1"
      - formatType: 7
      - allowEdit: 1
      - formatRule: "yyyy年MM月dd日"
      - edgeScope: 0
      - length: 11
      - allowEdit: 1
      - defaultContentValue: "2024-12-30"
      - contentCode: ""
    validate:
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]


- test:
    name: "tc-保存模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: false
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "tc-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]




