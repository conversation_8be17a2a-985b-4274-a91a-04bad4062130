- config:
    name: 对接epaas模板pdf文档模板创建
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用模版-${get_randomNo_16()}"
      description: "自动化测试描述"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      groupName: "自动化创建的分组${get_randomNo_str()}"
      groupName1: "自动化创建的分组${get_randomNo_str()}"
      contentNameCommon: "自动化测试-文本0507"
      signNameA: "甲方企业"
      signNameB: "乙方企业"
      labeltext: "单行文本${get_randomNo_str()}"
      labelmultitext: "多行文本${get_randomNo_str()}"
      labelNum: "数字控件${get_randomNo_str()}"
      labelDate: "日期控件${get_randomNo_str()}"
      labelPhone: "手机号控件${get_randomNo_str()}"
      labelIdCard: "身份证控件${get_randomNo_str()}"
      labelRadio: "单选控件${get_randomNo_str()}"
      labelCheckbox: "多选控件${get_randomNo_str()}"
      labelCheck: "勾选控件${get_randomNo_str()}"
      labelDropdown: "下拉选择控件${get_randomNo_str()}"
      labelImage: "图片控件${get_randomNo_str()}"
      labelCreditCode: "统一社会信用代码控件${get_randomNo_str()}"
      labelEmail: "邮箱控件${get_randomNo_str()}"
      labelRmb: "人民币控件${get_randomNo_str()}"
      labelSignArea: "签署区控件${get_randomNo_str()}"
      labelCrossPageSign: "骑缝签署区控件${get_randomNo_str()}"
      labelRemarkSign: "备注签署区控件${get_randomNo_str()}"
      contentFieldId001: "${get_randomNo_32()}"
      contentFieldId002: "${get_randomNo_32()}"
      contentFieldId003: "${get_randomNo_32()}"
      contentFieldId004: "${get_randomNo_32()}"
      contentFieldId005: "${get_randomNo_32()}"
      contentFieldId006: "${get_randomNo_32()}"
      contentFieldId007: "${get_randomNo_32()}"
      contentFieldId008: "${get_randomNo_32()}"
      contentFieldId009: "${get_randomNo_32()}"
      contentFieldId010: "${get_randomNo_32()}"
      contentFieldId011: "${get_randomNo_32()}"
      contentFieldId012: "${get_randomNo_32()}"
      contentFieldId013: "${get_randomNo_32()}"
      contentFieldId014: "${get_randomNo_32()}"
      # 通用样式配置
      textStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      textSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"20","overflowType":"1","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-text","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      textPosition: {"x":219.3816273642189,"y":580.1997655342852,"page":"1","scope":"default","intervalType":null}
      # 下拉框样式配置
      dropdownStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      dropdownSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"3","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-select","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      dropdownPosition: {"x":398.01766391682526,"y":768.04384520816,"page":"1","scope":"default","intervalType":null}
      dropdownOptions: [{"index":0,"label":"选项一","selected":false,"position":{"x":0,"y":50,"page":"1"},"style":{"width":124,"height":14}},{"index":1,"label":"选项二","selected":false,"position":{"x":0,"y":25,"page":"1"},"style":{"width":124,"height":14}},{"index":2,"label":"选项三","selected":false,"position":{"x":0,"y":0,"page":"1"},"style":{"width":124,"height":14}}]
      # 图片控件样式配置
      imageStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      imageSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-image","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      imagePosition: {"x":166,"y":650,"page":"1","scope":"default","intervalType":null}
      # 统一社会信用代码控件样式配置
      creditCodeStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      creditCodeSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"18","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-credit-code","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      creditCodePosition: {"x":166,"y":650,"page":"1","scope":"default","intervalType":null}
      # 邮箱控件样式配置
      emailStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      emailSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"50","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-email","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      emailPosition: {"x":166,"y":650,"page":"1","scope":"default","intervalType":null}
      # 人民币控件样式配置
      rmbStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      rmbSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"20","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-rmb","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      rmbPosition: {"x":166,"y":650,"page":"1","scope":"default","intervalType":null}
      # 手机号控件样式配置
      phoneStyle: {"font":"4","fontSize":42,"textColor":"#000","width":315,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      phoneSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"11","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-telephone","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      phonePosition: {"x":171.**************,"y":29.69797910328441,"page":"1","scope":"default","intervalType":null}
      # 身份证控件样式配置
      idCardStyle: {"font":"5","fontSize":5.5,"textColor":"#000","width":125,"height":8,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      idCardSettings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"18","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-id-card","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      idCardPosition: {"x":55.***************,"y":145.50549470465012,"page":"2","scope":"default","intervalType":null}
      # 多行文本控件样式配置
      multiTextStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      multiTextSettings: {"defaultValue":"自动化测试","required":true,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"210","overflowType":"1","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-text","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      multiTextPosition: {"x":166,"y":650,"page":"1","scope":"default","intervalType":null}
      # 单选控件样式配置
      radioStyle: {"font":"1","fontSize":42,"textColor":"#000","width":230,"height":187,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":"vertical","borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","imgCrop":null,"paddingLeftAndRight":"0"}}
      radioSettings: {"defaultValue":"","required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-radio","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      radioPosition: {"x":244.55027856235245,"y":334.9869095201241,"page":"3","scope":"default","intervalType":null}
      radioOptions: [{"index":0,"label":"选项一","selected":false,"position":{"x":10,"y":128,"page":"3"},"style":{"width":210,"height":49}},{"index":1,"label":"选项二","selected":false,"position":{"x":10,"y":69,"page":"3"},"style":{"width":210,"height":49}},{"index":2,"label":"选项三","selected":false,"position":{"x":10,"y":10,"page":"3"},"style":{"width":210,"height":49}}]
      # 多选控件样式配置
      checkboxStyle: {"font":"1","fontSize":42,"textColor":"#000","width":230.00000000000003,"height":187,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":"vertical","borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","imgCrop":null,"paddingLeftAndRight":"0"}}
      checkboxSettings: {"defaultValue":"","required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-multiple-choice","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      checkboxPosition: {"x":147.55889101832562,"y":580.5347260872807,"page":"3","scope":"default","intervalType":null}
      checkboxOptions: [{"index":0,"label":"选项一","selected":false,"position":{"x":10,"y":128,"page":"3"},"style":{"width":210,"height":49}},{"index":1,"label":"选项二","selected":false,"position":{"x":10,"y":69,"page":"3"},"style":{"width":210,"height":49}},{"index":2,"label":"选项三","selected":false,"position":{"x":10,"y":10,"page":"3"},"style":{"width":210,"height":49}}]
            #数字控件
      numStyle: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      numSettings: {"defaultValue":null,"required":false,"dateFormat":"0","validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":0,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"7","overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-number","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      numPosition: {"x":237.79771848414538,"y":572.2194315545244,"page":"1","scope":"default","intervalType":null}
      # 日期控件样式配置
      dateStyle: {"font":"1","fontSize":42,"textColor":"#000","width":294,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","imgCrop":null,"paddingLeftAndRight":"0"}}
      dateSettings: {"defaultValue":"25.07.2025","required":false,"dateFormat":"dd.MM.yyyy","validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-date","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      datePosition: {"x":173.72508378448052,"y":680.742977915501,"page":"1","scope":"default","intervalType":null}      
      # 勾选框控件样式配置
      tickBoxStyle: {"font":"1","fontSize":14,"textColor":"#000","width":15,"height":17,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","hideBorder":false,"styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":"[1]","elementId":"","posKey":"","imgCrop":null,"paddingLeftAndRight":"0"}}
      tickBoxSettings: {"defaultValue":"0","required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":[1],"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-check-square","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      tickBoxPosition: {"x":245.16414810377034,"y":614.6531935463644,"page":"1","scope":"default","intervalType":null}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon




- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
      - _editUrl: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


#模板详情
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "获取文档模板权限内容"
    api: api/esignDocs/custom-group/doc-template-permission-content.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      timestamp: ${get_timestamp()}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]


- test:
    name: "获取文档模板权限内容"
    api: api/esignDocs/custom-group/content.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
    extract:
      - _customGroupContent: content.data
      - _customGroupCode: content.code
      - _customGroupMessage: content.message
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.0.customPermGroup.groupCode", "BasicTemplateNoParticipantEdit"]

- test:
    name: "创建自定义字段组创建-可重复创建"
    api: api/esignDocs/custom-group/create-custom-field-group.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupName: "$groupName"
    extract:
      - _groupId: content.data.groupId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - ne: ["$_groupId", null]

- test:
    name: "获取自定义字段组列表"
    api: api/esignDocs/custom-group/get-custom-field-groups.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.0.groupName", "$groupName"]
      - eq: ["content.data.0.groupId", "$_groupId"]
      - eq: ["content.data.0.class", "com.timevale.sterna.doctemplate.adapter.rest.vo.CustomFieldGroupVO"]


#控件另存为-单行文本
- test:
    name: "创建自定义单行文本控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labeltext"
      custom: "true"
      type: "TEXT"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 1
      options: null
      style: $textStyle
      settings: $textSettings
      position: $textPosition
    extract:
      - TextfiledId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]


- test:
    name: "创建自定义勾选控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelCheck"
      custom: "true"
      type: "TICK_BOX"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 1
      options: null
      style: $tickBoxStyle
      settings: $tickBoxSettings
      position: $tickBoxPosition
    extract:
      - TickfiledId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]


- test:
    name: "创建自定义日期控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelDate"
      custom: "true"
      type: "DATE"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 1
      options: null
      style: $dateStyle
      settings: $dateSettings
      position: $datePosition
    extract:
      - DateFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义数字控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelNum"
      custom: "true"
      type: "NUM"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 1
      options: null
      style: $numStyle
      settings: $numSettings
      position: $numPosition
    extract:
      - NumfiledId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]


- test:
    name: "创建自定义下拉框控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelDropdown"
      custom: "true"
      type: "PULL_DOWN"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 14
      options: $dropdownOptions
      style: $dropdownStyle
      settings: $dropdownSettings
      position: $dropdownPosition
    extract:
      - DropdownFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义图片控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelImage"
      custom: "true"
      type: "IMAGE"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 11
      style: $imageStyle
      settings: $imageSettings
      position: $imagePosition
    extract:
      - ImageFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义统一社会信用代码控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelCreditCode"
      custom: "true"
      type: "UNIFY_THE_SOCIAL_CREDIT_CODE"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 21
      style: $creditCodeStyle
      settings: $creditCodeSettings
      position: $creditCodePosition
    extract:
      - CreditCodeFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义邮箱控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:   
      tplToken_content: "${getTplToken($_editUrl)}"
      groupId: "$_groupId"
      label: "$labelEmail"
      custom: "true"
      type: "EMAIL"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 22
      style: $emailStyle
      settings: $emailSettings
      position: $emailPosition
    extract:
      - EmailFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义人民币控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelRmb"
      custom: "true"
      type: "TEXT"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 24
      style: $rmbStyle
      settings: $rmbSettings
      position: $rmbPosition
    extract:
      - RmbFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义签名区域控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelSignArea"
      custom: "true"
      type: "SIGN"
      sort: 0
      bizId: ""
      contentFieldId: "4969e6270d5a418aa726046434fa87a"
      id: ""
      structType: 6
      style: {"font":"1","fontSize":12,"textColor":"#000","width":119,"height":119,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      settings: {"defaultValue":null,"required":true,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":"2","tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-stamp","fastCheck":"true","addSealRule":"followSeal","keyPosX":"0","keyPosY":"0","ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":false,"cellEditableOnFilling":true,"signDatePosition":null}
      position: {"x":189.25667431720677,"y":720.7440333458628,"page":"2","scope":"default","intervalType":null}
    extract:
      - SignAreaFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义骑缝章签名区域控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelCrossPageSign"
      custom: "true"
      type: "QF_SIGN"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 23
      style: {"font":"1","fontSize":12,"textColor":"#000","width":120,"height":120,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      settings: {"defaultValue":null,"required":true,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-paging-seal","fastCheck":"true","addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":false,"cellEditableOnFilling":true,"signDatePosition":null}
      position: {"x":64.97570038453955,"y":706.9687189685201,"page":"1-50","scope":"all","intervalType":null}
    extract:
      - ContinuousSignAreaFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义备注签名区域控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelRemarkSign"
      custom: "true"
      type: "REMARK_SIGN"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 17
      style: {"font":"1","fontSize":42,"textColor":"#000","width":"323.54","height":"133.13","bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      settings: {"defaultValue":"","required":true,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":null,"overflowType":"2","minFontSize":"8","remarkInputType":2,"content":"beizhhahhah","remarkAICheck":0,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-remarks-signature","fastCheck":null,"addSealRule":"","keyPosX":"0","keyPosY":"0","ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"autoFillRemark":true,"positionMovable":false,"cellEditableOnFilling":true,"signDatePosition":null}
      position: {"x":263.6867772776077,"y":135.76999999999998,"page":"1","scope":"default","intervalType":null}
    extract:
      - RemarkSignAreaFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义单行文本控件-控件已存在"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labeltext"
      custom: "true"
      type: "TEXT"
      sort: 0
      bizId: "1"
      contentFieldId: "1"
      id: ""
      structType: 1
      style: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
      settings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"20","overflowType":"1","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-text","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
      position: {"x":219.3816273642189,"y":580.1997655342852,"page":"1","scope":"default","intervalType":null}
    validate:
      - eq: ["content.code", 1100001204]
      - eq: ["content.message", "控件已存在"]
      - eq: ["content.error", "com.timevale.sterna.doctemplate.core.client.errors.TemplateCoreErrorEnum#CUSTOM_FIELD_EXISTED"]

#创建多行文本控件
- test:
    name: "创建自定义多行文本控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelmultitext"
      custom: "true"
      type: "MULTITEXT"
      sort: 0
      bizId: "1"
      contentFieldId: "1"
      id: ""
      structType: 1
      style: $multiTextStyle
      settings: $multiTextSettings
      position: $multiTextPosition
    extract:
      - MULTITextfiledId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义手机号控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelPhone"
      custom: "true"
      type: "PHONE_NUM"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 19
      style: $phoneStyle
      settings: $phoneSettings
      position: $phonePosition
    extract:
      - PhoneFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义身份证控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:  
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelIdCard"
      custom: "true"
      type: "ID_CARD"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 16
      options: null
      style: $idCardStyle
      settings: $idCardSettings
      position: $idCardPosition
    extract:
      - IdCardFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义单选控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:   
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelRadio"
      custom: "true"
      type: "RADIO_BOX"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 10
      options: $radioOptions
      style: $radioStyle
      settings: $radioSettings
      position: $radioPosition
    extract:
      - RadioFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

- test:
    name: "创建自定义多选控件"
    api: api/esignDocs/custom-group/create-custom-field.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupId: "$_groupId"
      label: "$labelCheckbox"
      custom: "true"
      type: "CHECK_BOX"
      sort: 0
      bizId: ""
      contentFieldId: ""
      id: ""
      structType: 9
      options: $checkboxOptions
      style: $checkboxStyle
      settings: $checkboxSettings
      position: $checkboxPosition
    extract:
      - CheckboxFieldId: content.data
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "success"]
      - ne: ["content.data", ""]

#查询该分组下的控件
- test:
    name: "查询分组下的控件"
    api: api/esignDocs/custom-group/queryBygroups.yml
    variables:  
      tplToken_content: ${getTplToken($_editUrl)}
      timestamp: ${get_timestamp()}
      groupId: "$_groupId"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.class", "com.timevale.sterna.base.result.QueryPageResultModel"]

- test:
    name: "epaasTemplate-detail-空内容域"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${getTplToken($_editUrl)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
#模板保存
- test:
    name: "epaasTemplate-batch-save-draft--0个内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-batch-save-draft--多个内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      signfiled: ${generate_field_object(签名区1, SIGN, $SignAreaFieldId)}
      qfsignField: ${generate_field_object(骑缝签名区, QF_SIGN, $ContinuousSignAreaFieldId)}
      remarkSignField: ${generate_field_object(备注签名区, REMARK_SIGN, $RemarkSignAreaFieldId)}
      textField: ${generate_field_object(单行文本, TEXT, $TextfiledId)}
      multilineTextField: ${generate_field_object(多行文本, MULTILINE_TEXT, $MULTITextfiledId)}
      numField: ${generate_field_object(数字, NUM, $NumfiledId)}
      dateField: ${generate_field_object(日期, DATE, $DateFieldId)}
      phoneField: ${generate_field_object(手机号, PHONE_NUM, $PhoneFieldId)}
      idCardField: ${generate_field_object(身份证号, ID_CARD, $IdCardFieldId)}
      tickBoxField: ${generate_field_object(勾选框, TICK_BOX, $TickfiledId)}
      pullDownField: ${generate_field_object(下拉框, PULL_DOWN, $DropdownFieldId)}
      radioField: ${generate_field_object(单选框, RADIO, $RadioFieldId)}
      checkboxField: ${generate_field_object(多选框, CHECKBOX, $CheckboxFieldId)}
      imageField: ${generate_field_object(图片, IMAGE, $ImageFieldId)}
      rmbField: ${generate_field_object(金额, RMB, $RmbFieldId)}
      emailField: ${generate_field_object(邮箱, EMAIL, $EmailFieldId)}
      creditCodeField: ${generate_field_object(统一社会信用代码, UNIFY_THE_SOCIAL_CREDIT_CODE, $CreditCodeFieldId)}
      fields_draft: [$signfiled,$qfsignField,$remarkSignField,$textField,$multilineTextField,$numField,$dateField,$phoneField,$idCardField,$tickBoxField,$pullDownField,$radioField,$checkboxField,$imageField,$rmbField,$emailField,$creditCodeField]
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${getTplToken($_editUrl)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
      - len_eq: ["content.data.fields",17]

- test:
    name: "发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "isPublish": true,
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

#查询模板控件-openapi
- test:
    name: "查询模板控件"
    api: api/esignDocs/documents/template/contents.yml
    variables:
      json: { "templateId": "$newTemplateUuidCommon"}
    validate:
      - eq: [ "content.code",200 ]
      - len_eq: ["content.data.contentsControl",14]
      - len_eq: ["content.data.sealControl",3]

#模板停用
- test:
    name: "setup-停用模版"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#停用再发布
- test:
    name: "停用后再发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "isPublish": true,
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "查询模板"
    api: api/esignDocs/template/manage/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _docUid: content.data.docUid
      - _editUrl1: content.data.editUrl
      - _fileKey: content.data.fileKey
      - _fileType: content.data.fileType
      - _originFileKey:  content.data.originFileKey
      - _previewUrl1: content.data.previewUrl
      - _templateType: content.data.templateType
      - _templateName: content.data.templateName
      - _createOrganizeCode: content.data.createOrganizeCode
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC11-查询模板预览页面"
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuidCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
      - ne: [ "content.data",null ]
      - ne: [ "content.data.templatePreviewUrl",null ]
      - ne: [ "content.data.templatePreviewOuterUrl",null ]


- test:
    name: "TC12-模板预览"
    api: api/esignDocs/template/templateView.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
    extract:
        - _previewUrl002: content.data.previewUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-从业务模板配置页获取模板详情"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _previewUrl002: content.data.previewUrl
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]



#- test:
#    name: "epaasTemplate-service-config"
#    api: api/esignDocs/epaasTemplate/service-config.yml
#    variables:
#      tplToken_config: ${getTplToken($_previewUrl002)}
#      tmp_common_template_002: ${putTempEnv(tmp_common_template_002, $tplToken_config)}
#    extract:
#      - _contentId2: content.data.contents.0.id
#      - _entityId2: content.data.contents.0.entityId
#    validate:
#      - eq: ["content.data.entityType","DOCUMENT"]
#      - eq: ["content.code",0]
#      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/list-preview-content-details.yml
    variables:
      tplToken_config: ${getTplToken($_previewUrl002)}
      tmp_common_template_002: ${putTempEnv(tmp_common_template_002, $tplToken_config)}
#      tplToken_config: ${ENV(tmp_common_template_002)}
    extract:
      - _contentId2: content.data.renderData.0.contentId
#      - _entityId2: content.data.contents.0.entityId
    validate:
#      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail-大小位置"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(tmp_common_template_002)}
      contentId_detail: $_contentId2
#      entityId_detail: $_entityId2
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
      - len_eq: ["content.data.fields",17]

#新建版本
- test:
    name: "新增版本"
    api: api/esignDocs/template/manage/templateNew.yml
    variables:
      - fileKey: $_fileKey
      - zipFileKey: null
      - templateType: $_templateType
      - templateName: "自动化新增版本的模板+${get_randomNo()}"
      - createUserOrg: $_createOrganizeCode
      - description: "新增版本"
      - docUuid: "$_docUid"
      - docUid: $_docUid
      - allRange: 1
      - organizeRange: []
      - sourceTemplateUuid: $newTemplateUuidCommon
      - sourceVersion: $newVersionCommon
      - editUrl: $_editUrl1
      - previewUrl: $_previewUrl1
    extract:
      - newTemplateUuid: content.data.templateUuid
      - newVersion: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模板历史记录"
    api: api/esignDocs/template/owner/templateVersionList.yml
    variables:
      - templateUuid: $newTemplateUuid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - len_eq: ["content.data.list",2]


#发布新版本的模板
- test:
    name: "发布新版本模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "isPublish": true,
        "templateUuid": $newTemplateUuidCommon,
        "version": 2,
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]


#创建分组
- test:
    name: "创建自定义字段组创建-可重复创建"
    api: api/esignDocs/custom-group/create-custom-field-group.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      groupName: "$groupName1"
    extract:
      - _groupId1: content.data.groupId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - ne: ["$_groupId", null]

##移动控件
- test:
    name: "移动其中一个控件到新的分组"
    api: api/esignDocs/epaasTemplate/batch-move-field.yml
    variables:
      tplToken_move: ${getTplToken($_editUrl)}
      fieldId_move: "$PhoneFieldId"
      srcGroupId_move: "$_groupId"
      targetGroupId_move: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
#查询新分组下的控件
- test:
    name: "查询分组下的控件"
    api: api/esignDocs/custom-group/queryBygroups.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      timestamp: ${get_timestamp()}
      groupId: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.class", "com.timevale.sterna.base.result.QueryPageResultModel"]
##删除控件
- test:
    name: "删除控件"
    api: api/esignDocs/epaasTemplate/delete-custom-field.yml
    variables:
      tplToken_delete: ${getTplToken($_editUrl)}
      fieldId_delete: "$PhoneFieldId"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
- test:
    name: "查询分组下的控件-0控件"
    api: api/esignDocs/custom-group/queryBygroups.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      timestamp: ${get_timestamp()}
      groupId: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.class", "com.timevale.sterna.base.result.QueryPageResultModel"]

#重命名分组名称
- test:
    name: "重命名分组名称"
    api: api/esignDocs/epaasTemplate/update-field-group.yml
    variables:
      tplToken_update: ${getTplToken($_editUrl)}
      groupId_update: "$_groupId1"
      groupName_update: "自动化重命名创建的分组${get_randomNo_str()}"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]

#移动至其他分组
- test:
    name: "移动到其他分组"
    api: api/esignDocs/epaasTemplate/moveothergroup.yml
    variables:
      tplToken_move: ${getTplToken($_editUrl)}
      srcGroupId_move: "$_groupId"
      targetGroupId_move: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]

#目标分组下的内容
- test:
    name: "查询分组下的控件-0控件"
    api: api/esignDocs/custom-group/queryBygroups.yml
    variables:
      tplToken_content: ${getTplToken($_editUrl)}
      timestamp: ${get_timestamp()}
      groupId: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.class", "com.timevale.sterna.base.result.QueryPageResultModel"]
##删除分组
- test:
    name: "删除分组"
    api: api/esignDocs/epaasTemplate/deleteGroup.yml
    variables:
      tplToken_delete: ${getTplToken($_editUrl)}
      groupId_delete: "$_groupId1"
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
##