- config:
    name: "我管理的-已签署文件"
    variables:
      FileName: "历史文件导入.xlsx"
      fileKey: ${attachment_upload($FileName)}
      submit_file_name: "startImg.png"
      submit_file_key: ${attachment_upload($submit_file_name)}

- test:
    name: "下载文件列表明细Excel模板"
    api: api/esignDocs/signedFile/getExcelTemplate.yml
    validate:
        -   ne: ["content", '']

- test:
    name: "我管理的-历史文件导入"
    api: api/esignDocs/signedFile/manage_historyImport.yml
    variables:
      - fileKey: $fileKey
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
       fileName:
       initiatorUserName:
       initiatorOrganizeName:
       signerUserName:
       signOrgName:
       signMode:
       includeSignedFileProcessUuidList: [ ]
       excludeSignedFileProcessUuidList: [ ]
       flowName: 'ppp历史导入电子签署流程10002'
       gmtSignFinishStart: '2022-08-01 00:00:00'
       gmtSignFinishEnd: '2022-09-13 23:59:59'
       page: 1
       size: 10
    extract:
      - ex_signedFileProcessUuid: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.flowName", "ppp历史导入电子签署流程10002" ]

- test:
    name: "我管理的-已签署文件导出明细"
    api: api/esignDocs/signedFile/manage_export.yml
    variables:
       signedFileProcessUuid:
       fileName:
       initiatorUserName:
       initiatorOrganizeName:
       signerUserName:
       signOrgName:
       signMode:
       includeSignedFileProcessUuidList: [ ]
       excludeSignedFileProcessUuidList: [ ]
       flowName: 'ppp历史导入电子签署流程10002'
       gmtSignFinishStart: '2022-08-13 00:00:00'
       gmtSignFinishEnd: '2022-09-13 23:59:59'
       page: 1
       size: 10
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "历史文件提交"
    api: api/esignDocs/signedFile/historyDocSubmit.yml
    variables:
      fileKeyList:
        - $submit_file_key
      signedFileProcessUuid: $ex_signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "我管理的-添加已签署文件下载任务"
    api: api/esignDocs/signedFile/manage_addTask.yml
    variables:
        flowName: ""
        gmtSignFinishStart: ""
        gmtSignFinishEnd: ""
        signedFileProcessUuid: $ex_signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取已签署文件下载任务结果"
    api: api/esignDocs/signedFile/taskResult.yml
    variables:
      taskId: $ex_signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]