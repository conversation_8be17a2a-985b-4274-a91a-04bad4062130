#已签署流程列表
- config:
    variables:
      page: 1
      size: 10
      gmtSignFinishStart: "2022-06-01 00:00:00"
      gmtSignFinishEnd: "2022-08-01 00:00:00"
      userName_random: ${generate_random_string(5)}
      userNo_random: ${generate_random_string(5)}
      orgName_random: ${generate_random_string(5)}
      orgNo_random: ${generate_random_string(5)}


- test:
    name: "TC1-查询已签署流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-查询已签署流程列表-签署人姓名不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: $userName_random
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-签署人账号不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName:
      - userAccountNumber5: $userNo_random
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-签署人企业不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: $orgName_random
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-签署人企业账号不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName:
      - orgAccountNumber5: $orgNo_random
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-发起人姓名不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName:
      - initiatorOrganizeName: $userName_random
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-发起人账号不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorAccountNumber5: $userNo_random
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]

- test:
    name: "TC1-查询已签署流程列表-发起人组织不存在"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorAccountNumber5: ""
      - initiatorOrganizeName: $orgName_random
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart
      - gmtSignFinishEnd: $gmtSignFinishEnd
      - signMode: ""
      - page: $page
      - size: $size
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total", 0]