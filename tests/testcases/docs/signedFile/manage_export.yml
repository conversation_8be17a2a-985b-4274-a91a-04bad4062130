- config:
    name: "已签文件-我管理的文件-导出"
    variables:
      second: 30
      todayDate: ${getDateTime()}
      todayDateStr: ${substring($todayDate,0,8)}
      randomCount: ${getDateTime()}
      signType: 1
      signFlowCreateTime:  ${getDateTime(-1,1)}
      signFlowEndTime:  ${getDateTime(0,1)}
      departmentName: ${ENV(sign01.main.orgNo)}
      customAccountNo: ${ENV(sign01.userCode)}
      departmentName1: ${ENV(sign01.main.orgNo)}
      customAccountNo1: ${ENV(sign01.userCode)}
      subject: "流程主题"
      userType: 1
      fileKey: ${ENV(fileKey)}
      ofdFileKey: ${ENV(ofdFileKey)}

- test:
    name: setup-查询已签署流程列表
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
        fileName: ""
        flowName: ""
        initiatorUserName: ""
        initiatorAccountNumber: ""
        initiatorOrganizeCode: ""
        signerUserName: ""
        userAccountNumber: ""
        signOrgName: ""
        orgAccountNumber: ""
        signMode: ""
        processId:
        businessNo: ""
        dynamicCode: ""
        projectId: ""
        viewType: 1
        gmtSignFinishStart: $signFlowCreateTime
        gmtSignFinishEnd: $signFlowEndTime
        flowExtensions: {}
        page: 1
        size: 10
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
    teardown_hooks:
      - ${sleep($second)}

- test:
    name: setup-导入已签文件1
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [{
          signedFileKey: $fileKey
        }
      ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]

- test:
    name: setup-导入已签文件2
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      - signFiles: [{
          signedFileKey: $ofdFileKey
        }
      ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
#    extract:
#      - flowId002: content.data.signFlowId
    teardown_hooks:
      - ${sleep($second)}
      -
- test:
    name: setup-查询已签署流程列表
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
        fileName: ""
        flowName: ""
        initiatorUserName: ""
        initiatorAccountNumber: ""
        initiatorOrganizeCode: ""
        signerUserName: ""
        userAccountNumber: ""
        signOrgName: ""
        orgAccountNumber: ""
        signMode: ""
        processId:
        businessNo: ""
        dynamicCode: ""
        projectId: ""
        viewType: 1
        gmtSignFinishStart: $signFlowCreateTime
        gmtSignFinishEnd: $signFlowEndTime
        flowExtensions: {}
        page: 1
        size: 10
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
    extract:
      - signedFileProcessUuid001: content.data.signFileProcessVOList.0.signedFileProcessUuid
      - signedFileProcessUuid002: content.data.signFileProcessVOList.1.signedFileProcessUuid


- test:
    name: "已签文件-我管理的文件-导出"
    api: api/esignDocs/signedFile/manage_export.yml
    variables:
      includeSignedFileProcessUuidList: ["$signedFileProcessUuid001","$signedFileProcessUuid002"]
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data, null]
    teardown_hooks:
      - ${sleep($second)}




- test:
    name: TC-后台任务查看
    api: api/esignSigns/portal/getBackendTaskList.yml
    variables:
      authorizationBackendTaskList: "${getPortalToken()}"
    teardown_hooks:
      - ${sleep($second)}
    validate:
       - eq: [ json.status, 200 ]
       - eq: [ json.success, true ]
       - contains: [ json.data.list.0.name, "批量导出-已签文件" ]
       - contains: [ json.data.list.0.fileName, "批量导出-已签文件-$todayDateStr" ]