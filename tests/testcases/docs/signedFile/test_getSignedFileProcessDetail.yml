#已签署流程列表
- config:
    variables:
      page: 1
      size: 10
      FileName: "历史文件导入.xlsx"
      historyImportFileKey: ${attachment_upload($FileName)}
      gmtSignFinishStart1: "${getDateTime(-90, 1)}"
      gmtSignFinishEnd1: "${getDateTime(1, 1)}"

- test:
    name: createAndStart-内部个人静默签署完成-自动化测试数据
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: createAndStart-内部个人静默签署完成-自动化测试数据
      signFiles:
        - fileKey: ${ENV(fileKey)}
      signerInfos:
        - signNode: 1
          userCode: ${ENV(sign01.userCode)}
          organizationCode: ""
          userType: 1
          signMode: 0
          autoSign: 1
          sealInfos:
            - fileKey: "${ENV(fileKey)}"
              signConfigs:
                - freeMode: 0
                  posY: 200
                  posX: 200
                  pageNo: "1-3"
                  signType: "EDGE-SIGN"
                  signatureType: "PERSON-SEAL"
    extract:
      - signFlowId001: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "TC1-查询已签署流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - initiatorOrganizeName: ""
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart1
      - gmtSignFinishEnd: $gmtSignFinishEnd1
      - signMode: ""
      - page: $page
      - size: $size
    extract:
       - signedFileProcessUuid1: content.data.signFileProcessVOList.0.signedFileProcessUuid
       - initiatorAccountNumber1: content.data.signFileProcessVOList.0.initiatorAccountNumber
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-查询已签署流程明细"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - detailSource: "0"
      - oldProcessId: ""
      - signedFileProcessUuid: $signedFileProcessUuid1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signedCenterProcessDetailVO.initiatorAccountNumber",$initiatorAccountNumber1 ]
      #- ne: [ "content.data",null ]


#---查看签署详情----
- test:
    name: "TC3-查询已签署流程列表-电子签署"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: "自动化测试"
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart1
      - gmtSignFinishEnd: $gmtSignFinishEnd1
      - signMode: "1"
      - page: $page
      - size: $size
    extract:
       - signedFileProcessUuid2: content.data.signFileProcessVOList.0.signedFileProcessUuid
       - initiatorAccountNumber2: content.data.signFileProcessVOList.0.initiatorAccountNumber
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-查询已签署流程明细"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - detailSource: "0"
      - oldProcessId: ""
      - signedFileProcessUuid: $signedFileProcessUuid2
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signedCenterProcessDetailVO.initiatorAccountNumber",$initiatorAccountNumber2 ]
#      - ne: [ "content.data.signedCenterProcessDetailVO",null]

#-----查看物理签署详情-----
#todo 造物理签署
- test:
    name: "TC5-查询已签署流程列表-物理签署"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: ""
      - initiatorUserName: ""
      - initiatorOrganizeName: ""
      - signerUserName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - signOrgName: ""
      - gmtSignFinishStart: $gmtSignFinishStart1
      - gmtSignFinishEnd: $gmtSignFinishEnd1
      - signMode: "2"
      - page: $page
      - size: $size
    extract:
       - signedFileProcessUuid3: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "历史导入文件"
    api: api/esignDocs/signedFile/manage_historyImport.yml
    variables:
      - fileKey: $historyImportFileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC7-查询已签署流程列表-历史导入文件"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - fileName: ""
      - flowName: "历史导入"
      - initiatorUserName: ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - initiatorOrganizeName: ""
      - signerUserName: ""
      - signOrgName: ""
      - gmtSignFinishStart: "2022-08-13 00:00:00"
      - gmtSignFinishEnd: "2022-08-13 23:59:59"
      - signMode: "1"
      - page: $page
      - size: $size
    extract:
       - signedFileProcessUuid4: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-查询已签署流程明细"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      - detailSource: "0"
      - oldProcessId: ""
      - signedFileProcessUuid: $signedFileProcessUuid4
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data.historyFileDetailVO",null]
      - eq: [ "content.data.templateInitiationDetailVO",null ]

##------模板发起(在其他业务中校验)-----------
#- test:
#    name: "TC9-查询已签署流程列表"
#    api: api/esignDocs/signedFileProcess/manage_list.yml
#    variables:
#      - fileName: ""
#      - flowName: "模板"
#      - initiatorUserName: ""
#      - initiatorOrganizeName: ""
#      - "includeSignedFileProcessUuidList": [ ]
#      - "excludeSignedFileProcessUuidList": [ ]
#      - signerUserName: ""
#      - signOrgName: ""
#      - "includeSignedFileProcessUuidList": [ ]
#      - "excludeSignedFileProcessUuidList": [ ]
#      - gmtSignFinishStart: $gmtSignFinishStart1
#      - gmtSignFinishEnd: $gmtSignFinishEnd1
#      - signMode: "1"
#      - page: $page
#      - size: $size
#    extract:
#       - signedFileProcessUuid5: content.data.signFileProcessVOList.0.signedFileProcessUuid
#       - initiatorAccountNumber5: content.data.signFileProcessVOList.0.initiatorAccountNumber
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC10-查询已签署流程明细"
#    api: api/esignDocs/signedFileProcess/manage_detail.yml
#    variables:
#      - detailSource: "0"
#      - oldProcessId: ""
#      - signedFileProcessUuid: $signedFileProcessUuid5
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.templateInitiationDetailVO.initiatorAccountNumber",$initiatorAccountNumber5 ]
#      #- ne: [ "content.data",null]
