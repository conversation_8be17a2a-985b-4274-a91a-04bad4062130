- config:
    name: "我发起的-已签署文档"
    variables:
      FileName: "历史文件导入.xlsx"
      fileKey: ${attachment_upload($FileName)}
      submit_file_name: "startImg.png"
      submit_file_key: ${attachment_upload($submit_file_name)}

- test:
    name: "我发起的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/owner_list.yml
    variables:
      - account0: "${ENV(ceswdzxzdhyhwgd1.account)}"
      - password0: "${ENV(ceswdzxzdhyhwgd1.password)}"
      - authorization0: "${getPortalToken($account0,$password0)}"
      - tmp_owner_ceswdzxzdhyhwgd1: "${putTempEnv(token_tmp_ceswdzxzdhyhwgd1,$authorization0)}"
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": "ppp历史导入电子签署流程10002"
      - "gmtSignFinishStart": "2022-08-01 00:00:00"
      - "gmtSignFinishEnd": "2022-09-13 23:59:59"
      - "page": 1
      - "size": 10
    extract:
      - signedFileProcessUuid: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.flowName", "ppp历史导入电子签署流程10002" ]

- test:
    name: "我发起的-已签署文件导出明细"
    api: api/esignDocs/signedFile/owner_export.yml
    variables:
      - authorization0: "${ENV(token_tmp_ceswdzxzdhyhwgd1)}"
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": "ppp历史导入电子签署流程10002"
      - "gmtSignFinishStart": "2022-08-13 00:00:00"
      - "gmtSignFinishEnd": "2022-09-13 23:59:59"
      - "page": 1
      - "size": 10
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "历史文件提交"
    api: api/esignDocs/signedFile/historyDocSubmit.yml
    variables:
      - fileKeyList: [ $submit_file_key ]
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "我发起的-添加已签署文件下载任务"
    api: api/esignDocs/signedFile/owner_addTask.yml
    variables:
      - authorization0: "${ENV(token_tmp_ceswdzxzdhyhwgd1)}"
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": "ppp历史导入电子签署流程10002"
      - "gmtSignFinishStart": "2022-08-01 00:00:00"
      - "gmtSignFinishEnd": "2022-09-13 23:59:59"
      - "page": 1
      - "size": 10
      - "signedFileProcessUuid": $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "我发起的-查看已签署文件详情"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
      #      - signedFileProcessUuid: [ $submit_file_key ]
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.historyFileDetailVO.flowName", "ppp历史导入电子签署流程10002" ]
      - eq: [ "content.data.historyFileDetailVO.initiatorAccountNumber",null ]
