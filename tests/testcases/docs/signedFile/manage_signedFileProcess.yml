- config:
    variables:
      FileName: "历史文件导入.xlsx"
      fileKey: ${attachment_upload($FileName)}
      submit_file_name: "startImg.png"
      submit_file_key: ${attachment_upload($submit_file_name)}

#- test:
#    name: "我管理的-历史文件导入"
#    api: api/esignDocs/signedFile/manage_historyImport.yml
#    variables:
#      - fileKey: $fileKey
#    teardown_hooks:
#      - ${sleep(5)}
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": "ppp历史导入电子签署流程10002"
      - "gmtSignFinishStart": "2022-08-01 00:00:00"
      - "gmtSignFinishEnd": "2022-09-13 23:59:59"
      - "page": 1
      - "size": 10
      - viewType: 2
    extract:
      - signedFileProcessUuid: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.signFileProcessVOList.0.flowName", "ppp历史导入电子签署流程10002" ]

- test:
    name: "我管理的-历史文件提交"
    api: api/esignDocs/signedFile/historyDocSubmit.yml
    variables:
      - fileKeyList: [ $submit_file_key ]
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "我管理的-查看已签署文件详情"
    api: api/esignDocs/signedFileProcess/manage_detail.yml
    variables:
#      - signedFileProcessUuid: [ $submit_file_key ]
      - signedFileProcessUuid: $signedFileProcessUuid
    extract:
      - flowName: content.data.historyFileDetailVO.flowName
      - gmtSignFinish: content.data.historyFileDetailVO.gmtSignFinish
      - gmtSignInitiate: content.data.historyFileDetailVO.gmtSignInitiate
      - signedFileDetailUuid: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - initiatorOrganizeName: content.data.historyFileDetailVO.initiatorOrganizeName
      - initiatorUserCode: content.data.historyFileDetailVO.initiatorUserCode
      - initiatorUserName: content.data.historyFileDetailVO.initiatorUserName
      - id0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.id
      - uuid0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.uuid
      - signedFileProcessId0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signedFileProcessId
      - userCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userCode
      - userName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userName
      - departmentName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentName
      - departmentCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.departmentCode
      - organizeCode0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeCode
      - organizeName0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.organizeName
      - userType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.userType
      - signerType0: content.data.historyFileDetailVO.docSignedProcessActorVOList.0.signerType
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.historyFileDetailVO.flowName", "ppp历史导入电子签署流程10002" ]
      - eq : ["content.data.historyFileDetailVO.initiatorAccountNumber",null ]

- test:
    name: "删除导入的签署文件"
    api: api/esignDocs/signedFileProcess/deleteHistoryImportFile.yml
    variables:
      #      - signedFileProcessUuid: [ $submit_file_key ]
      - signedFileDetailUuid: $signedFileDetailUuid
#    extract:
#      - signedFileDetailUuid: content.data.historyFileVOList.0.signedFileDetailUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "编辑已签署文件"
    api: api/esignDocs/signedFileProcess/manage_updateHistoryImportFile.yml
    variables:
      - flowName: $flowName
      - gmtSignFinish: $gmtSignFinish
      - gmtSignInitiate: $gmtSignInitiate
      - initiatorOrganizeName: $initiatorOrganizeName
      - initiatorUserCode: $initiatorUserCode
      - initiatorUserName: $initiatorUserName
      - id0: $id0
      - uuid0: $uuid0
      - signedFileProcessId0: $signedFileProcessId0
      - userCode0: $userCode0
      - userName0: $userName0
      - departmentName0: $departmentName0
      - departmentCode0: $departmentCode0
      - organizeCode0: $organizeCode0
      - organizeName0: $organizeName0
      - userType0: $userType0
      - signerType0: $signerType0
      - signedFileProcessUuid: $signedFileProcessUuid
    #    extract:
    #      - signedFileDetailUuid: content.data.historyFileVOList.0.signedFileDetailUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "我管理的-删除已签署文件"
    api: api/esignDocs/signedFileProcess/manage_deleteSignedFileProcess.yml
    variables:
      - signedFileProcessUuid: $signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
