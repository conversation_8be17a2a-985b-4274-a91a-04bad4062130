- config:
    name: pdf文档模版支持拖拽备注签署区-发起内部个人签署-只有一个备注区V6.0.12.0-beta.2
    variables:
      - collectionTaskId0: "${getRemarkCollectionTaskId()}"
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      - initiatorUserNameCommon: ${ENV(sign01.userName)}
      - organizationCodeCommon: ${ENV(sign01.main.orgCode)}
      - organizationNameCommon: ${ENV(sign01.main.orgName)}


- test:
    name: "TC-1-setup-创建1个备注区的模板"
    testcase: common/template/pdf-bulidTemplate-remark1.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - _editUrl
      - _contentId
      - _entityId

- test:
    name: "epaasTemplate-detail-大小位置"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${getTplToken($_editUrl)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
      - eq: ["content.data.fields.0.style.width",277.1]
      - eq: ["content.data.fields.0.style.height",118.18]
- test:
    name: "TC-2-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "TC-3-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-4-添加一个内部个人签署方和一份pdf签署文件"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "signerType": 1,
                "signerTerritory": 1,
                "draggable": true,
                "organizeCode": "",
                "userCode": $initiatorUserCodeCommon,
                "sealTypeCode": null,
                "autoSign": 0,
                "assignSigner": 1,
                "userName": $initiatorUserNameCommon
              }
            ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "templateId": $newTemplateUuidCommon
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-5-获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
    extract:
      - _editUrl1: content.data.editUrl
#      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
#      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
#      - signPdfFileKey01: content.data.filePreTaskInfos.0.sealInfos.0.fileKey

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl1)}
      tmp_common_template_002: ${putTempEnv(_tmp_common_template_002, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail--模板1"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_002)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields.0
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_common_template_002)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]


- test:
    name: "epaasTemplate-batch-save-draft--签署区设置页设置备注签"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_002)}
      fields_draft1: ["${add_template_role_id($_fields1,$_signerId1)}"]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft1,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId,
                        "entityId": $_entityId
                    }
          ]
      }
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: TC-7-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList01: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-8-提交发起任务"
    variables:
      batchTemplateInitiationName: $templateNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid
      fileFormat: 1
      appendList: $appendList01
      signersList: $signerNodeList
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC-9-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $templateNameCommon
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId01: content.data.list.0.flowId
      - signFlowId01: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-10-检查签署区设置页的备注区大小正确"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $flowId01
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo01: content.data.signConfigInfo
      - actorSignConfigLists01: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 288]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 123 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 42 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosX, "385.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosY, "595.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkContent, null ]
      - len_eq: [ "$signConfigInfo01", 1 ] #签署文档
