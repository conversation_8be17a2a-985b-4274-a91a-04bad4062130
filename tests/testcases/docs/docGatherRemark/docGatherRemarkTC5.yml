- config:
    name: pdf文档模版支持拖拽备注签署区-发起内部企业签署-含企业签署区+备注区V6.0.12.0-beta.2
    variables:
      - collectionTaskId0: "${getRemarkCollectionTaskId()}"
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      - initiatorUserNameCommon: ${ENV(sign01.userName)}
      - organizationCodeCommon: ${ENV(sign01.main.orgCode)}
      - organizationNameCommon: ${ENV(sign01.main.orgName)}


- test:
    name: "TC-1-setup-创建1个签名区+1个备注签署区-键盘输入 有预抄录"
    testcase: common/template/pdf-bulidTemplate-remark2.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "TC-1-查看文档模版预览页-验证备注区位置大小正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.signatoryList.1.signFieldType, 1]
      - eq: [ content.data.signatoryList.1.name, "备注"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkPosX, "500.0"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkPosY, "703.0"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkFieldHeight, 36]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkFieldWidth, 216]
      - eq: [ content.data.signatoryList.1.remarkSignatory.inputType, 2]
      - ne: [ content.data.signatoryList.1.remarkSignatory.remarkContent, ""]

- test:
    name: "TC-2-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "TC-3-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-4-添加一个内部企业签署方和一份pdf签署文件"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "signerType": 2,
                "signerTerritory": 1,
                "draggable": true,
                "organizationCode": $organizationCodeCommon,
                "userCode": $initiatorUserCodeCommon,
                "sealTypeCode": null,
                "autoSign": 0,
                "assignSigner": 1,
                "userName": $initiatorUserNameCommon
              }
            ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "templateId": $newTemplateUuidCommon
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-5-获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
    extract:
      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
      - signPdfFileKey01: content.data.filePreTaskInfos.0.sealInfos.0.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-6-签署区设置页，添加个人签署+备注，输入方式为键盘输入，成功"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey01,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 500,
              "posY": 703,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "",
                "inputType": 2,
                "remarkContent": "",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "588.0",
                "remarkPosY": "703.0",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "备注",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 188,
                "posY": 703,
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "甲方",
                "signFieldType": 0,
                "signType": "COMMON-SIGN",
                "signatureType": "COMMON-SEAL"
              }
            ],
             "signatureTypeList": ["COMMON-SEAL", "PERSON-SEAL", "LEGAL-PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew01,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 2,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-7-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList01: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-8-提交发起任务"
    variables:
      batchTemplateInitiationName: $templateNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid
      fileFormat: 1
      appendList: $appendList01
      signersList: $signerNodeList
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "TC-9-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $templateNameCommon
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId01: content.data.list.0.flowId
      - signFlowId01: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-10-检查签署区设置页的备注区大小正确"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $flowId01
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo01: content.data.signConfigInfo
      - actorSignConfigLists01: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosX, "500.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosY, "703.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - ne: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkContent, "" ]
      - len_eq: [ "$signConfigInfo01", 1 ] #签署文档
