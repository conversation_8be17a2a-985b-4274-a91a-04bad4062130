- config:
    name: pdf文档模版支持拖拽备注签署区-发起内部企业签署-含4个备注区V6.0.12.0-beta.2
    variables:
      - collectionTaskId0: "${getRemarkCollectionTaskId()}"
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      - initiatorUserNameCommon: ${ENV(sign01.userName)}
      - organizationCodeCommon: ${ENV(sign01.main.orgCode)}
      - organizationNameCommon: ${ENV(sign01.main.orgName)}


- test:
    name: "TC-1-setup-创建2个签名区+4个备注签署区-键盘、手绘、表单"
    testcase: common/template/pdf-bulidTemplate-remark4.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "TC-1-查看文档模版预览页-验证备注区位置大小正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.signatoryList.2.signFieldType, 1]
      - eq: [ content.data.signatoryList.2.name, "备注1"]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkPosX, "100.0"]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkPosY, "503.0"]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkFieldHeight, 36]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkFieldWidth, 216]
      - eq: [ content.data.signatoryList.2.remarkSignatory.inputType, 2]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkContent, ""]
      - eq: [ content.data.signatoryList.3.signFieldType, 1 ]
      - eq: [ content.data.signatoryList.3.name, "备注2" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkPosX, "100.0" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkPosY, "303.0" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkFieldHeight, 36 ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkFieldWidth, 216 ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.inputType, 2 ]
      - ne: [ content.data.signatoryList.3.remarkSignatory.remarkContent, "" ]
      - eq: [ content.data.signatoryList.4.signFieldType, 1 ]
      - eq: [ content.data.signatoryList.4.name, "备注3" ]
      - eq: [ content.data.signatoryList.4.remarkSignatory.remarkPosX, "300.0" ]
      - eq: [ content.data.signatoryList.4.remarkSignatory.remarkPosY, "503.0" ]
      - eq: [ content.data.signatoryList.4.remarkSignatory.remarkFieldHeight, 165 ]
      - eq: [ content.data.signatoryList.4.remarkSignatory.remarkFieldWidth, 165 ]
      - eq: [ content.data.signatoryList.4.remarkSignatory.inputType, 1 ]
      - eq: [ content.data.signatoryList.5.signFieldType, 1 ]
      - eq: [ content.data.signatoryList.5.name, "备注4" ]
      - eq: [ content.data.signatoryList.5.remarkSignatory.remarkPosX, "500.0" ]
      - eq: [ content.data.signatoryList.5.remarkSignatory.remarkPosY, "503.0" ]
      - eq: [ content.data.signatoryList.5.remarkSignatory.remarkFieldHeight, 165 ]
      - eq: [ content.data.signatoryList.5.remarkSignatory.remarkFieldWidth, 165 ]
      - eq: [ content.data.signatoryList.5.remarkSignatory.inputType, 3 ]

- test:
    name: "TC-2-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "TC-3-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-4-添加一个内部企业签署方和一份pdf签署文件"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "signerType": 2,
                "signerTerritory": 1,
                "draggable": true,
                "organizationCode": $organizationCodeCommon,
                "userCode": $initiatorUserCodeCommon,
                "sealTypeCode": null,
                "autoSign": 0,
                "assignSigner": 1,
                "userName": $initiatorUserNameCommon
              }
            ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "templateId": $newTemplateUuidCommon
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-5-获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
    extract:
      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
      - signPdfFileKey01: content.data.filePreTaskInfos.0.sealInfos.0.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-6-签署区设置页，添加个人签署+备注，输入方式为键盘输入，成功"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey01,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 100,
              "posY": 503,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "",
                "inputType": 2,
                "remarkContent": "",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "100.0",
                "remarkPosY": "503.0",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "备注1",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 100,
                "posY": 303,
                "remarkSignConfig": {
                  "aiCheck": 0,
                  "collectionTaskId": "",
                  "inputType": 2,
                  "remarkContent": "",
                  "remarkFieldHeight": "",
                  "remarkFieldWidth": "",
                  "remarkFontSize": "14",
                  "remarkPageNo": "1",
                  "remarkPosX": "100.0",
                  "remarkPosY": "303.0",
                  "remarkPrompt": ""
                },
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "备注2",
                "signFieldType": 1,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 300,
                "posY": 503,
                "remarkSignConfig": {
                  "aiCheck": 0,
                  "collectionTaskId": "",
                  "inputType": 1,
                  "remarkContent": "",
                  "remarkFieldHeight": "",
                  "remarkFieldWidth": "",
                  "remarkFontSize": "14",
                  "remarkPageNo": "1",
                  "remarkPosX": "300.0",
                  "remarkPosY": "503.0",
                  "remarkPrompt": ""
                },
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "备注3",
                "signFieldType": 1,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 500,
                "posY": 503,
                "remarkSignConfig": {
                  "aiCheck": 0,
                  "collectionTaskId": "",
                  "inputType": 3,
                  "remarkContent": "",
                  "remarkFieldHeight": "",
                  "remarkFieldWidth": "",
                  "remarkFontSize": "14",
                  "remarkPageNo": "1",
                  "remarkPosX": "500.0",
                  "remarkPosY": "503.0",
                  "remarkPrompt": ""
                },
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "备注4",
                "signFieldType": 1,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 100,
                "posY": 703,
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "甲方",
                "signFieldType": 0,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 288,
                "posY": 703,
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "乙方",
                "signFieldType": 0,
                "signType": "COMMON-SIGN",
                "signatureType": "COMMON-SEAL"
              }
            ],
             "signatureTypeList": ["COMMON-SEAL", "PERSON-SEAL", "LEGAL-PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew01,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 2,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-7-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList01: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-8-提交发起任务"
    variables:
      batchTemplateInitiationName: $templateNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid
      fileFormat: 1
      appendList: $appendList01
      signersList: $signerNodeList
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "TC-9-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $templateNameCommon
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId01: content.data.list.0.flowId
      - signFlowId01: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-10-检查签署区设置页的备注区大小正确"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $flowId01
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo01: content.data.signConfigInfo
      - actorSignConfigLists01: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosX, "100.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosY, "503.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkContent, "" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkFieldWidth, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkPosX, "100.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkPosY, "303.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.1.inputType, 2 ]
      - ne: [ content.data.signConfigInfo.0.remarkSignConfigList.1.remarkContent, "" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkFieldWidth, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkPosX, "300.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkPosY, "503.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.2.inputType, 1 ]
      - ne: [ content.data.signConfigInfo.0.remarkSignConfigList.2.remarkContent, "" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.remarkFieldWidth, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.remarkFieldHeight, 165 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.remarkFontSize, 14 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.remarkPosX, "500.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.remarkPosY, "503.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.3.inputType, 3 ]
      - len_eq: [ "$signConfigInfo01", 1 ] #签署文档
