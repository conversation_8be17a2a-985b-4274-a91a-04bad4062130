- config:
    name: pdf文档模版支持拖拽备注签署区-发起内部个人签署-含个人签署区+3个备注区V6.0.12.0-beta.2
    variables:
      - collectionTaskId0: "${getRemarkCollectionTaskId()}"
      - presetIdCommon: ${getPreset(0,0)}
      - initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      - initiatorUserNameCommon: ${ENV(sign01.userName)}
      - organizationCodeCommon: ${ENV(sign01.main.orgCode)}
      - organizationNameCommon: ${ENV(sign01.main.orgName)}


- test:
    name: "TC-1-setup-创建1个签名区+3个备注签署区-键盘（无抄录）、手绘、表单"
    testcase: common/template/pdf-bulidTemplate-remark3.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "TC-1-查看文档模版预览页-验证备注区位置大小正确"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.signatoryList.1.signFieldType, 1]
      - eq: [ content.data.signatoryList.1.name, "备注1"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkPosX, "300.0"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkPosY, "703.0"]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkFieldHeight, 36]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkFieldWidth, 216]
      - eq: [ content.data.signatoryList.1.remarkSignatory.inputType, 2]
      - eq: [ content.data.signatoryList.1.remarkSignatory.remarkContent, ""]
      - eq: [ content.data.signatoryList.2.signFieldType, 1 ]
      - eq: [ content.data.signatoryList.2.name, "备注2" ]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkPosX, "400.0" ]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkPosY, "703.0" ]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkFieldHeight, 149 ]
      - eq: [ content.data.signatoryList.2.remarkSignatory.remarkFieldWidth, 149 ]
      - eq: [ content.data.signatoryList.2.remarkSignatory.inputType, 1 ]
      - ne: [ content.data.signatoryList.2.remarkSignatory.remarkContent, "" ]
      - eq: [ content.data.signatoryList.3.signFieldType, 1 ]
      - eq: [ content.data.signatoryList.3.name, "备注3" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkPosX, "500.0" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkPosY, "703.0" ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkFieldHeight, 149 ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.remarkFieldWidth, 149 ]
      - eq: [ content.data.signatoryList.3.remarkSignatory.inputType, 3 ]

- test:
    name: "TC-2-查询业务模板详情-获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      - businessTypeIdCommon: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: "TC-3-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuidNew01: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-4-添加一个内部个人签署方和一份pdf签署文件"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "initiatorOrganizeCode": $organizationCodeCommon,
        "initiatorOrganizeName": $organizationNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "initiatorDepartmentName": $organizationNameCommon,
        "initiatorDepartmentCode": $organizationCodeCommon,
        "businessPresetUuid": $presetIdCommon,
        "signersList": [
          {
            "signMode": 1,
            "signerList": [
              {
                "signerType": 1,
                "signerTerritory": 1,
                "draggable": true,
                "organizeCode": "",
                "userCode": $initiatorUserCodeCommon,
                "sealTypeCode": null,
                "autoSign": 0,
                "assignSigner": 1,
                "userName": $initiatorUserNameCommon
              }
            ]
          }
        ],
        "type": 1,
        "sort": 0,
        "chargingType": 1,
        "appendList": [
          {
            "appendType": 2,
            "templateInfo": {
              "templateId": $newTemplateUuidCommon
            }
          }
        ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-5-获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
    extract:
      - signConfigsNew01: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew01: content.data.filePreTaskInfos.0.signerId
      - signPdfFileKey01: content.data.filePreTaskInfos.0.sealInfos.0.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-6-签署区设置页，添加个人签署+备注，输入方式为键盘输入，成功"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew01,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $signPdfFileKey01,
            "signConfigs": [
            {
              "allowMove": false,
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": 1,
              "posX": 300,
              "posY": 703,
              "remarkSignConfig": {
                "aiCheck": 0,
                "collectionTaskId": "",
                "inputType": 2,
                "remarkContent": "",
                "remarkFieldHeight": "",
                "remarkFieldWidth": "",
                "remarkFontSize": "14",
                "remarkPageNo": "1",
                "remarkPosX": "300.0",
                "remarkPosY": "703.0",
                "remarkPrompt": ""
              },
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "备注1",
              "signFieldType": 1,
              "signType": "COMMON-SIGN",
              "signatureType": "PERSON-SEAL"
            },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 400,
                "posY": 703,
                "remarkSignConfig": {
                  "aiCheck": 0,
                  "collectionTaskId": "",
                  "inputType": 1,
                  "remarkContent": "",
                  "remarkFieldHeight": "",
                  "remarkFieldWidth": "",
                  "remarkFontSize": "14",
                  "remarkPageNo": "1",
                  "remarkPosX": "400.0",
                  "remarkPosY": "703.0",
                  "remarkPrompt": ""
                },
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "备注2",
                "signFieldType": 1,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 500,
                "posY": 703,
                "remarkSignConfig": {
                  "aiCheck": 0,
                  "collectionTaskId": "",
                  "inputType": 3,
                  "remarkContent": "",
                  "remarkFieldHeight": "",
                  "remarkFieldWidth": "",
                  "remarkFontSize": "14",
                  "remarkPageNo": "1",
                  "remarkPosX": "500.0",
                  "remarkPosY": "703.0",
                  "remarkPrompt": ""
                },
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "备注3",
                "signFieldType": 1,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              },
              {
                "allowMove": false,
                "addSignDate": false,
                "keywordInfo": null,
                "pageNo": 1,
                "posX": 188,
                "posY": 703,
                "edgeScope": null,
                "sealSignDatePositionInfo": null,
                "signPosName": "甲方",
                "signFieldType": 0,
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL"
              }
            ],
             "signatureTypeList": ["PERSON-SEAL"]
          }
          ],
          "signerId": $signerIdNew01,
          "userType": 1,
          "userCode": $initiatorUserCodeCommon,
          "userName": $initiatorUserNameCommon,
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", True]

- test:
    name: TC-7-获取businessPresetSnapshotUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuidNew01"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList01: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC-8-提交发起任务"
    variables:
      batchTemplateInitiationName: $templateNameCommon
      batchTemplateInitiationUuid: $batchTemplateInitiationUuidNew01
      initiatorOrganizeCode: $organizationCodeCommon
      initiatorOrganizeName: $organizationNameCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorDepartmentName: $organizationNameCommon
      initiatorDepartmentCode: $organizationCodeCommon
      businessPresetUuid: $presetIdCommon
      businessPresetSnapshotUuid: $businessPresetSnapshotUuid
      fileFormat: 1
      appendList: $appendList01
      signersList: $signerNodeList
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC-9-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $templateNameCommon
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId01: content.data.list.0.flowId
      - signFlowId01: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserNameCommon]
      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationNameCommon ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-10-检查签署区设置页的备注区大小正确"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $flowId01
      - organizeCode: $organizationCodeCommon
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo01: content.data.signConfigInfo
      - actorSignConfigLists01: content.data.signConfigInfo.0.actorSignConfigList
      - remarkSignConfigList01: content.data.signConfigInfo.0.remarkSignConfigList
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkPosX)}", "400.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkPosY)}", "703.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, inputType)}", 1 ]
      - ne: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkContent)}", "" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,0, remarkFontSize)}", 14 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkPosY)}", "703.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, inputType)}", 2 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkFontSize)}", 14 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,1, remarkContent)}", "" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, remarkPosX)}", "500.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, remarkPosY)}", "703.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, inputType)}", 3 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList01, inputType,2, remarkFontSize)}", 14 ]
      - len_eq: [ "$signConfigInfo01", 1 ] #签署文档
