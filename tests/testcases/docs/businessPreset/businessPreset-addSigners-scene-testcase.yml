#创建物理业务模板链路场景
- config:
    name: "6.0.5创建物理用印业务模板链路场景"
    variables:
      autoPresetName: "物理用印自动化测试业务模板${getDateTime()}"
      randomSignerId: ${get_randomNo_32()}

- test:
     name: "TC1-新建一个业务模板-物理用印"
     api: api/esignDocs/businessPreset/create_wl.yml
     variables:
         - presetName: $autoPresetName
     validate:
         - eq: [ content.message, "成功" ]
         - eq: [ content.status, 200 ]
         - eq: [ content.success, true ]

- test:
    name: "TC2-查询新增的业务模板,并获取presetId-物理用印"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]

- test:
    name: "TC-查看初始状态新建的业务模板详情-物理用印"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.sealConfig.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $autoPresetName]
      - length_equals: [content.data.sealConfig.businessTypeId, 32]
      - len_gt: [content.data.sealConfig.messageConfigList, 1]

- test:
    name: "TC-业务模板配置第一步：选择ofd签署并保存-物理用印-指定用户"
    api: api/esignDocs/businessPreset/addDetailInitall.yml
    variables:
      - fileFormat: 1
      - allowAddFile: 1
      - initiatorAll: 0
      - initiatorList: {
            "organizationCode": "${ENV(sign01.main.orgCode)}",
            "userCode": "${ENV(sign01.userCode)}"
       }
      - presetId: $testPresetId
      - presetName: $autoPresetName
      - templateList: []
      - workFlowModelKey: ""
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [content.success, true]

- test:
    name: "TC11-业务模板第三步：添加签署方（内部个人不指定）并启用业务模板--顺序签"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "o",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
          {
            "signNode": 2,
            "signMode": 1,
            "signerList": [
              {
                "onlyUkeySign": 0,
                "templateInitiationSignersUuid": null,
                "signerId": $randomSignerId,
                "signerSnapshotId": null,
                "signerTerritory": 1,
                "signerType": 1,
                "assignSigner": 0,
                "userName": null,
                "userCode": null,
                "userAccount": null,
                "legalSign": 0,
                "organizeName": null,
                "organizeCode": null,
                "departmentName": null,
                "departmentCode": null,
                "sealTypeCode": null,
                "sealTypeName": null,
                "autoSign": 0,
                "needGather": 0,
                "signNode": 2,
                "signOrder": 1,
                "signMode": 0,
                "signerStatus": null,
                "signerStatusStr": null,
                "signatoryList": [],
                "id": "0-0",
                "draggable": true
              }
            ],
            "id": "node-0"
          }
        ]}
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "TC-查看ofd业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.sealConfig.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 0]
      - eq: [content.data.initiatorAll, 0]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $autoPresetName]
      - length_equals: [content.data.sealConfig.businessTypeId, 32]
      - len_gt: [content.data.sealConfig.messageConfigList, 1]

- test:
    name: "TC14-获取批量发起选择页该业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]