- config:
    name: "业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.2-法人章校验"
    variables:
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode1: ${ENV(sign01.userCode)}
      - userName1: ${ENV(sign01.userName)}
      - subject: '业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.2-法人章校验'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - newTemplateUuid1: "${getTemplateId(0,2,pdf,0)}"  ##创建有2个签署区的模板，若有则返回已有
      - newVersion1: 1
      - randStr: ${getDateTime()}
      - presetName1: "企业C指定法人章类型-$randStr"
      - presetName3: "企业C不指定-$randStr"
      - orgCode1: ${ENV(sign01.main.orgCode)}
      - orgName1: ${ENV(sign01.main.orgName)}
      - autoTestDocUuid1: ${get_docConfig_type()}
      - sealTypeCodeCommon: "LEGAL-PERSON-SEAL"
      - userNo101: "tPreset101"
      - userName0: "测试用户䶮裔"
      - orgNo101: "tPreset101"
      - orgName0: "esigntest自动化测试企业一〇一有限公司"
      - sealGroupName0: "$orgNo101-Auto法人-可删"

##############调试数据##################
#      - orgCode0: "89ef451155f84be0a8a0946f968a79bd"
#      - userCode0: "test101"
#      - legalSealId0: "1811020333388279809"
#      - legalSealId1: "1811020333388279810"
#      - testPresetId1: "db766eb88f55ba1ec271ff4e741ec65a"
#      - testPresetId3: "16ca619c7440e4cd4fe1df1b7ae5117c"
#      - testBusinessTypeId1: "c6b9acf563c758fc99ea200d4ed53432"
#      - testBusinessTypeId3: "f386d130fca1e7e1a8585751ee08cf10"
##################创建内部组织和用户-组织需要有法人##############
- test:
    name: 创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName0
        customOrgNo: $orgNo101
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode:
    extract:
      - code0: content.code
      - message0: content.message
      - orgCode0: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: 创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
      data:
        - customAccountNo: $userNo101
          name: $userName0
          mobile: ""
          email: "<EMAIL>"
          licenseType: ID_CARD
          licenseNo: ""
          bankCardNo:
          mainOrganizationCode:
          mainCustomOrgNo: $orgNo101
          otherOrganization: [ ]
    extract:
      - code0: content.code
      - message0: content.message
      - userCode0: content.data.successData.0.userCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

- test:
    name: 修改内部组织-添加法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      data:
        customOrgNo: $orgNo101
        legalRepAccountNo: $userNo101
    extract:
      - code0: content.code
      - message0: content.message
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
####################新建企业印章并授权##############
- test:
    name: "Setup2-创建一枚个人章-userSealId0"
    api: api/esignSeals/v1/userseals/create.yml
    variables:
      userSeals_create_json:
        customAccountNo: $userNo101
        sealName: "个人章A-$userCode0"
        sealInfos:
          - sealPattern: 1
            sealColour: 1
    extract:
      userSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "Setup2-创建一枚个人章-userSealId1"
    api: api/esignSeals/v1/userseals/create.yml
    variables:
      userSeals_create_json:
        customAccountNo: $userNo101
        sealName: "个人章B-$userCode0"
        sealInfos:
          - sealPattern: 1
            sealColour: 1
    extract:
      userSealId1: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "Setup2-创建一枚法人章(默认)"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $userNo101
        customOrgNo: $orgNo101
        sealGroupName: $sealGroupName0
        sealTypeCode: $sealTypeCodeCommon
        sealInfos:
          - sealPattern: 1
            legalSealId: $userSealId0
            sealName: "法人章-模板印章-01"
    extract:
      legalSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

#- test:
#    name: Setup2-授权用印人所有人-legalSealId0
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
#    variables:
#      sealsigners_json:
#        authorizationScope: 2
#        sealId: $legalSealId0
#        sealsignerType:  1
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: "Setup2-创建一枚法人章（非默认）"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $userNo101
        customOrgNo: $orgNo101
        sealGroupName: $sealGroupName0
        sealTypeCode: $sealTypeCodeCommon
        sealInfos:
          - sealPattern: 1
            legalSealId: $userSealId1
            sealName: "法人章-模板印章-02"
    extract:
      legalSealId1: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

###################业务模板A-指定内部企业和用章要求为法人章####
#模板发布
- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "TC2-新建一个业务模板（企业A指定用章要求是新建类型）"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    extract:
      - testPresetId1: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC2-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId1: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName1]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId1,
        "presetName": $presetName1,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuid1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]

- test:
    name: "TC2-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId1
      presetId_addBusinessType: $testPresetId1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#- test:
#    name: "TC2-业务模板A配置-第三步：签署方设置（设置签署方）-用章要求：法人章"
#    api: api/esignDocs/businessPreset/addSigners.yml
#    variables:
#      presetId: $testPresetId1
#      status: 0
#      needGather: 0
#      needAudit: 0
#      sort: 0
#      allowAddSigner: 0
#      allowAddSealer: 1
#      signerNodeList:
#        - signNode: 2
#          signMode: 1
#          signerList: $signerList
#      signerList:
#        - autoSign: 0
#          assignSigner: 1
#          organizeCode: "$orgCode0"
#          userName: "$userName0"
#          organizeName: "$orgName0"
#          departmentName: "$orgName0"
#          legalSign: 1
#          sealTypeCode: ""
#          signerTerritory: 1
#          signerType: 2
#          userCode: "$userCode0"
#          sealTypeName: ""
#          departmentCode: "$orgCode0"
#          signatoryList: []
#    validate:
#      - contains: [ content.message, "签署方组织授权错误" ]
#      - contains: [ content.message, "经办人没有企业的印章授权权限" ]
#      - eq: [ content.status, 1606031 ]
#      - eq: [ content.success, false ]
#      - eq: [ content.data, null ]

- test:
    name: Setup2-授权用印人所有人-legalSealId1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $legalSealId1
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: "TC2-业务模板配置-第三步：签署方设置（设置签署方）-用章要求：法人章"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgName0"
          departmentName: "$orgName0"
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
          legalSign: 1
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 2
          signatoryList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId1
      - fillingList_updateFillingUsers: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]

- test:
    name: TC4-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,0]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_00: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail--模板3"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: "${add_template_role_id($_fields10,$_signerId1)}"
      _tmp_fill2: "${add_template_role_id($_fields11,$_signerId1)}"
      fields_draft21: [$_tmp_fill1,$_tmp_fill2]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft21,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId1
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


###################业务模板C-指定内部企业不指定用章要求####
- test:
    name: "TC4-新建一个业务模板C（不指定用章要求）"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName3
    extract:
      - testPresetId3: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC4-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    extract:
      - testBusinessTypeId3: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId3]
      - eq: [content.data.presetName, $presetName3]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId3,
        "presetName": $presetName3,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuid1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]

- test:
    name: "TC4-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName3
      businessTypeId: $testBusinessTypeId3
      presetId_addBusinessType: $testPresetId3
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC4-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId3
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "TC4-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId3
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgCode0"
          departmentName: ""
          sealTypeCode: ""
          legalSign: 0
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId3
      - fillingList_updateFillingUsers: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]

- test:
    name: TC4-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,0]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_01: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_01)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail--模板3"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: "${add_template_role_id($_fields10,$_signerId1)}"
      _tmp_fill2: "${add_template_role_id($_fields11,$_signerId1)}"
      fields_draft21: [$_tmp_fill1,$_tmp_fill2]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft21,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId3
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

################操作1-修改企业A对应的印章授权########
######增量授权######
- test:
    name: TC5-授权用印人-调整默认法人章的授权
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $legalSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC5-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "LEGAL-PERSON-SEAL" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: "TC5-页面业务模板详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  null]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerTerritory, 1] #内部
      - eq: [content.data.signerNodeList.0.signerList.0.signerType, 2] #企业
      - eq: [content.data.signerNodeList.0.signerList.0.assignSigner, 1] #指定签署方

- test:
      name: TC5-业务模板详情C-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId3
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId3 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC6-停用法人章的非默认印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $legalSealId1
        sealStatus: 3 #更新成4-已停用 成功
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(1)}

- test:
      name: TC6-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "LEGAL-PERSON-SEAL" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: "TC6-页面业务模板详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  null]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerTerritory, 1] #内部
      - eq: [content.data.signerNodeList.0.signerList.0.signerType, 2] #企业
      - eq: [content.data.signerNodeList.0.signerList.0.assignSigner, 1] #指定签署方

- test:
      name: TC6-业务模板详情C-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId3
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId3 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息
######从所有修改授权到只有经办人对应的企业#########
- test:
    name: TC7-授权用印人从所有人变更到经办人所在企业
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $legalSealId0
        sealsignerType:  1
        sealsignersInfos:
          - customOrgNo: $orgNo101
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC7-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "LEGAL-PERSON-SEAL" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: "TC7-页面业务模板详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  null]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerTerritory, 1] #内部
      - eq: [content.data.signerNodeList.0.signerList.0.signerType, 2] #企业
      - eq: [content.data.signerNodeList.0.signerList.0.assignSigner, 1] #指定签署方

- test:
      name: TC7-业务模板详情C-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId3
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId3 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC8-恢复数据-授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $legalSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC8-查询电子印章授权用印人
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $legalSealId0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.total, 0 ]
      - eq: [ content.data.authorizationScope, 2 ]
      - len_eq: [ content.data.records, 0 ]

- test:
    name: TC8-sealId0-授权用印人从指定企业变更到经办人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $legalSealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $orgCode0
            userCode: $userCode0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC8-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "LEGAL-PERSON-SEAL" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: "TC8-页面业务模板详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  null]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerTerritory, 1] #内部
      - eq: [content.data.signerNodeList.0.signerList.0.signerType, 2] #企业
      - eq: [content.data.signerNodeList.0.signerList.0.assignSigner, 1] #指定签署方

- test:
      name: TC8-业务模板详情C-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId3
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId3 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC9-恢复数据-授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $legalSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC9-查询电子印章授权用印人
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $legalSealId0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.total, 0 ]
      - eq: [ content.data.authorizationScope, 2 ]
      - len_eq: [ content.data.records, 0 ]

- test:
    name: TC9-授权用印人变更为非经办人(授权信息已变更)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $legalSealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $orgCode1
            userCode: $userCode1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
      name: TC9-业务模板详情A-校验状态和第三步指定情况-由于没有权限停用
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 1606064 ]
          - eq: [ content.message, "业务模板未启用" ]
          - eq: [ content.data, null ]

- test:
    name: "TC9-页面业务模板详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.status, 0]
      - eq: [content.data.changeReason,  "印章授权信息已变更" ]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

#- test:
#      name: TC9-业务模板详情C-校验状态和第三步指定情况（目前这种情况无法触发topic）
#      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
#      variables:
#          businessTypeCode: $testBusinessTypeId3
#      validate:
#          - eq: [ content.code, 1606064 ]
#          - eq: [ content.message, "业务模板未启用" ]
#          - eq: [ content.data, null ]

- test:
    name: TC10-恢复数据授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $legalSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: "TC10-业务模板配置-第三步：签署方设置（设置签署方）-用章要求：法人章"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgName0"
          departmentName: "$orgName0"
          legalSign: 1
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: TC10-legalSealId0停用-这个企业的类型下没有其他有效印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $legalSealId0
        sealStatus: 3 #更新成4-已停用 成功
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(5)}

- test:
      name: TC10-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 1606064 ]
          - eq: [ content.message, "业务模板未启用" ]
          - eq: [ content.data, null ]

- test:
    name: "TC10-页面业务模板A详情-查看业务模板信息"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.status, 0]
      - contained_by: [content.data.changeReason,  ["用章要求已变更","印章授权信息已变更"] ]
#      - contains: [content.data.changeReason,  "印章授权信息已变更" ]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

#- test:
#      name: TC10-业务模板详情C-校验状态和第三步指定情况(无法支持的场景)
#      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
#      variables:
#          businessTypeCode: $testBusinessTypeId3
#      validate:
#          - eq: [ content.code, 1606064 ]
#          - eq: [ content.message, "业务模板未启用" ]
#          - eq: [ content.data, null ]

#- test:
#    name: "TC10-页面业务模板C详情-查看业务模板信息(无法支持的场景)"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId3
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.data.presetId, $testPresetId3]
#      - eq: [content.data.status, 0]
#      - eq: [content.data.changeReason,  "印章授权信息已变更" ]
#      - eq: [content.data.presetName, $presetName3]
#      - ne: [content.data.signBusinessType.businessTypeId, null]
#      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: teardown-删除内部用户
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      data:
        customAccountNo: $userNo101
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(10)}

####异步建议在单场景校验,全量运行脚本的时候多线程异步消息慢导致未能消费校验无法通过###############
#- test:
#    name: "TC11-页面业务模板A详情-查看业务模板信息-删除经办人影响"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId1
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.data.presetId, $testPresetId1]
#      - eq: [content.data.status, 0]
#      - contains: [content.data.changeReason,  "印章授权信息已变更" ]
##      - contains: [content.data.changeReason,  "用章要求已变更" ]
#      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
#      - eq: [content.data.presetName, $presetName1]
#      - ne: [content.data.signBusinessType.businessTypeId, null]
#      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: "TC11-页面业务模板C详情-查看业务模板信息-删除经办人影响"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId3]
      - eq: [content.data.status, 0]
      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
      - eq: [content.data.presetName, $presetName3]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: teardown-删除内部组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      data:
        customOrgNo: $orgNo101
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(5)}

- test:
    name: "TC12-页面业务模板A详情-查看业务模板信息-删除主体企业影响"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.status, 0]
#      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
      - contains: [content.data.changeReason,  "签署方【$orgName0】信息已修改" ]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: "TC12-页面业务模板C详情-查看业务模板信息-删除主体企业影响"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId3]
      - eq: [content.data.status, 0]
      - contains: [content.data.changeReason,  "签署方【$orgName0】信息已修改" ]
      - eq: [content.data.presetName, $presetName3]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]