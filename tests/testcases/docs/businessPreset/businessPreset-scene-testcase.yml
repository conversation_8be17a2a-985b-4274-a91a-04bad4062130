#6.0.4创建业务模板链路场景
- config:
    name: "6.0.4创建业务模板链路场景"
    variables:
      autoPresetName: "自动化测试业务模板${get_randomNo()}"
      randomSignerId: ${get_randomNo_32()}
      fileFormat: 1

- test:
    name: "TC1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]

- test:
    name: "TC3-查看初始状态新建的业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $autoPresetName]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]

- test:
    name: "SetUp-创建1个模板，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate3.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "TC4-获取模板发起流程引擎模板列表"
    api: api/esignDocs/businessPreset/getWorkflowList.yml
    extract:
      - firstModelKey: content.data.workflowList.0.modelKey
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - length_greater_than: [content.data.workflowList, 0]

- test:
    name: "TC5-获取流程引擎模板图(流程图)"
    api: api/esignDocs/businessPreset/getWorkflowImg.yml
    variables:
      - modelKey: $firstModelKey
    validate:
      - startswith: [content, "<?xml"]

- test:
    name: "TC6-关联1个模板到业务模板并保存"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
       - allowAddFile: 1
       - initiatorAll: 1
       - initiatorList: []
       - fileFormat: $fileFormat
       - presetId: $testPresetId
       - presetName: $autoPresetName
       - templateList: [{
         "templateId": $newTemplateUuidCommon,
         "templateName": $templateNameCommon,
         "version": $newVersionCommon
       }
       ]
       - workFlowModelKey: ""
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "TC7-从业务模板配置页获取模板详情"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]

- test:
    name: "TC8-获取业务模板配置绑定企业模板列表"
    api: api/esignDocs/businessPreset/bindList.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [content.data.templateList.0.templateId, $newTemplateUuidCommon]

- test:
    name: "TC9-业务模板第二步：保存签署方式,只修改强制阅读时间为2秒,和设置签署区置于文字下方"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
        businessTypeName: $autoPresetName
        businessTypeId: $testBusinessTypeId
        presetId: $testPresetId
        sealLayerPosition: 1

    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "TC10-获取业务模板的签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - testTemplateName: content.data.templateList.0.name
#      - signatoryName1: content.data.templateList.0.simpleVOList.0.name
#      - signType1: content.data.templateList.0.simpleVOList.0.signType
#      - signatoryId1: content.data.templateList.0.simpleVOList.0.id
#      - signatoryName2: content.data.templateList.0.simpleVOList.1.name
#      - signType2: content.data.templateList.0.simpleVOList.1.signType
#      - signatoryId2: content.data.templateList.0.simpleVOList.1.id
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [ content.success, true ]

- test:
    name: "TC11-业务模板第三步：添加签署方（内部个人不指定+相对方企业）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 0,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "o",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
        {
                "signNode": 2,
                "signMode": 1,
                "signerList": [
                {
                        "templateInitiationSignersUuid": null,
                        "signerId": $randomSignerId,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 0,
                        "userName": null,
                        "userCode": null,
                        "userAccount": null,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 2,
                        "signOrder": 1,
                        "signMode": 1,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": [],
                        "id": "0-0",
                        "draggable": true
                    }
                ],
                "id": "node-0"
            }
        ]
    }
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "templateDetail-获取业务模板配置绑定企业模板详情"
    api: api/esignDocs/businessPreset/templateDetail.yml
    variables:
      - presetId: $testPresetId
      - templateId: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      _epaasTemplateId: content.data.epaasTemplateId
      _previewUrl: content.data.previewUrl
      _templateName: content.data.templateName
      _templateId: content.data.templateId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.templateId, $newTemplateUuidCommon]

- test:
    name: "TC13-添加业务模板配置绑定模板内容域数据(发起方填写+签署方填写)，并启用业务模板"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC14-获取批量发起选择页该业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC15-断言业务模板发起后设置了签署区置于文字下方"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    validate:
      - eq: [content.data.signBusinessType.sealLayerPosition, 1]