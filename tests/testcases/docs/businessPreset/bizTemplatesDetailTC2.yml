- config:
    name: "openapi:查询电子签署业务模板详情接口测试用例-16beta1-支持自定义作废协议模板"
    variables:
      fileName01: "word-仅有作废协议类型数据源.zip"
      htmlFileName01: "word-仅有作废协议类型数据源"
      fileName02: "word-作废协议-有全部基础控件（没关联数据源）.zip"
      htmlFileName02: "word-作废协议-有全部基础控件（没关联数据源）"
      fileName03: "word-作废协议-有全部基础控件(部分关联数据源).zip"
      htmlFileName03: "word-作废协议-有全部基础控件(部分关联数据源)"
      description: "自动化测试描述-word模板"
      templateNameCommon: "自动化测试作废协议通用模版-word模板-${get_randomNo_16()}"
      presetName01: "自动化作废协议模板-${get_randomNo_16()}"

##保证是验证码登录
- test:
    name: 系统参数开启自定义作废协议模板
    variables:
      moduleId: "fe9053c9992311ec87635254002d4645"
      thirdId: ""
      configValues: [
            {
              "keyId": 202053,
              "value": "1"
            }]
    api: api/esignManage/cmc/updateValues.yml
    validate:
      - eq: [ json.code, 10000000 ]
      - eq: [ json.data, True ]
      - eq: [ json.message, "执行成功" ]

#新增作废协议类型文档模板
- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: null
      - zipFileKey: ${common_upload_fileKey($fileName01)}
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuid01: content.data.templateUuid
      - newVersion01: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "templateUuid": $newTemplateUuid01,
        "version": $newVersion01,
        "isPublish": True
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName01
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName01
    extract:
      - getPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName01]

- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $getPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $getPresetId]
      - eq: [content.data.presetName, $presetName01]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $presetName01,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [],
        "customTemplateProtocol": 1,
        "templateProtocolRequest": {
           "templateId": $newTemplateUuid01,
           "templateName": $templateNameCommon,
           "version": $newVersion01
        },
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]


- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName01
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $getPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success", true ]

- test:
      name: 查询业务模板详情-关联数据源控件出参不显示
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.voidAgreementTemplate, 1 ]  #0-未指定 1-指定
          - eq: [ content.data.voidAgreementContentsControl, [] ] #作废协议,内容域实例集合,关联数据源的控件不返回

- test:
      name: 停用业务模板
      api: api/esignDocs/businessPreset/blockUp.yml
      variables:
          presetId_blockUp: $getPresetId
      validate:
          - eq: [ content.status, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.success, true ]
          - eq: [ content.data, null ]

- test:
    name: "停用文档模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      account0: $userCodeCommon
      templateUuid: $newTemplateUuid01
      version: $newVersion01
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "删除文档模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      templateUuid: $newTemplateUuid01
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: null
      - zipFileKey: ${common_upload_fileKey($fileName02)}
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuid02: content.data.templateUuid
      - newVersion02: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "templateUuid": $newTemplateUuid02,
        "version": $newVersion02,
        "isPublish": True
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $presetName01,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [],
        "customTemplateProtocol": 1,
        "templateProtocolRequest": {
           "templateId": $newTemplateUuid02,
           "templateName": $templateNameCommon,
           "version": $newVersion02
        },
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]


- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName01
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $getPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success", true ]

- test:
      name: 查询业务模板详情-关联数据源控件出参不显示
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.voidAgreementTemplate, 1 ]  #0-未指定 1-指定
          - ne: [ content.data.voidAgreementContentsControl, [] ] #作废协议,内容域实例集合,没关联数据源的控件都返回
          - len_eq: [content.data.voidAgreementContentsControl, 14]


- test:
      name: 停用业务模板
      api: api/esignDocs/businessPreset/blockUp.yml
      variables:
          presetId_blockUp: $getPresetId
      validate:
          - eq: [ content.status, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.success, true ]
          - eq: [ content.data, null ]

- test:
    name: "停用文档模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      account0: $userCodeCommon
      templateUuid: $newTemplateUuid02
      version: $newVersion02
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "删除文档模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      templateUuid: $newTemplateUuid02
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: null
      - zipFileKey: ${common_upload_fileKey($fileName03)}
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuid03: content.data.templateUuid
      - newVersion03: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "templateUuid": $newTemplateUuid03,
        "version": $newVersion03,
        "isPublish": True
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $presetName01,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [],
        "customTemplateProtocol": 1,
        "templateProtocolRequest": {
           "templateId": $newTemplateUuid03,
           "templateName": $templateNameCommon,
           "version": $newVersion03
        },
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]


- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName01
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $getPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success", true ]

- test:
      name: 查询业务模板详情-关联数据源控件出参不显示
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.voidAgreementTemplate, 1 ]  #0-未指定 1-指定
          - ne: [ content.data.voidAgreementContentsControl, [] ] #作废协议,内容域实例集合,没关联数据源的控件都返回
          - len_eq: [content.data.voidAgreementContentsControl, 11]

- test:
    name: 系统参数停用自定义作废协议模板
    variables:
      moduleId: "fe9053c9992311ec87635254002d4645"
      thirdId: ""
      configValues: [
            {
              "keyId": 202053,
              "value": "0"
            }]
    api: api/esignManage/cmc/updateValues.yml
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ json.code, 10000000 ]
      - eq: [ json.data, True ]
      - eq: [ json.message, "执行成功" ]

- test:
      name: 查询业务模板详情-新增参数为空
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.voidAgreementTemplate, null ]
          - eq: [ content.data.voidAgreementContentsControl, null ]

- test:
      name: 停用业务模板
      api: api/esignDocs/businessPreset/blockUp.yml
      variables:
          presetId_blockUp: $getPresetId
      validate:
          - eq: [ content.status, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.success, true ]
          - eq: [ content.data, null ]

- test:
    name: "停用文档模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      account0: $userCodeCommon
      templateUuid: $newTemplateUuid03
      version: $newVersion03
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "删除文档模板"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      templateUuid: $newTemplateUuid03
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]





