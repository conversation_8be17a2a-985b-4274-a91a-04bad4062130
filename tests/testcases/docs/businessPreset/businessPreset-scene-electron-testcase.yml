- config:
    name: "6.0.4创建业务模板链路场景"
    variables:
      autoPresetName: "自动化测试业务模板${get_randomNo()}"
      randomSignerId: ${get_randomNo_32()}
      fileFormat: 1

- test:
    name: "TC1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]

- test:
    name: "TC3-查看初始状态新建的业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $autoPresetName]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]

- test:
    name: "SetUp-创建1个模板，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate3.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "TC4-获取模板发起流程引擎模板列表"
    api: api/esignDocs/businessPreset/getWorkflowList.yml
    extract:
      - firstModelKey: content.data.workflowList.0.modelKey
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - length_greater_than: [content.data.workflowList, 0]

- test:
    name: "TC5-获取流程引擎模板图(流程图)"
    api: api/esignDocs/businessPreset/getWorkflowImg.yml
    variables:
      - modelKey: $firstModelKey
    validate:
      - startswith: [content, "<?xml"]

- test:
    name: "TC6-关联1个模板到业务模板并保存-指定人"
    api: api/esignDocs/businessPreset/addDetailInitall.yml
    variables:
       - allowAddFile: 1
       - initiatorAll: 0
       - initiatorList: {
            "organizationCode": "${ENV(sign01.main.orgCode)}",
            "userCode": "${ENV(sign01.userCode)}"
       }
       - presetId: $testPresetId
       - presetName: $autoPresetName
       - templateList: [{
         "templateId": $newTemplateUuidCommon,
         "templateName": $templateNameCommon,
         "version": $newVersionCommon
       }
       ]
       - workFlowModelKey: ""
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "TC7-从业务模板配置页获取模板详情"
    api: api/esignDocs/template/withoutPermissionDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]

- test:
    name: "TC8-获取业务模板配置绑定企业模板列表"
    api: api/esignDocs/businessPreset/bindList.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [content.data.templateList.0.templateId, $newTemplateUuidCommon]


- test:
    name: "TC10-获取业务模板的签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - testTemplateName: content.data.templateList.0.name
#      - signatoryName1: content.data.templateList.0.simpleVOList.0.name
#      - signType1: content.data.templateList.0.simpleVOList.0.signType
#      - signatoryId1: content.data.templateList.0.simpleVOList.0.id
#      - signatoryName2: content.data.templateList.0.simpleVOList.1.name
#      - signType2: content.data.templateList.0.simpleVOList.1.signType
#      - signatoryId2: content.data.templateList.0.simpleVOList.1.id
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "TC11-业务模板第三步：添加签署方（内部个人不指定+相对方企业）-顺序签"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 0,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": "o",
        "workFlowModelName": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [{
                "signNode": 2,
                "signMode": 1,
                "signerList": [
                {
                        "templateInitiationSignersUuid": null,
                        "signerId": $randomSignerId,
                        "signerSnapshotId": null,
                        "signerTerritory": 1,
                        "signerType": 1,
                        "assignSigner": 0,
                        "userName": null,
                        "userCode": null,
                        "userAccount": null,
                        "legalSign": 0,
                        "organizeName": null,
                        "organizeCode": null,
                        "departmentName": null,
                        "departmentCode": null,
                        "sealTypeCode": null,
                        "sealTypeName": null,
                        "autoSign": 0,
                        "needGather": 0,
                        "signNode": 2,
                        "signOrder": 1,
                        "signMode": 0,
                        "signerStatus": null,
                        "signerStatusStr": null,
                        "signatoryList": [],
                        "id": "0-0",
                        "draggable": true
                    }
                ],
                "id": "node-0"
            }]
    }
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "TC12-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
    api: api/esignDocs/businessPreset/templateDetail.yml
    variables:
      - presetId: $testPresetId
      - templateId: $newTemplateUuidCommon
      - version: $newVersionCommon
#    extract:
#      templateContentId0: content.data.contentList.0.templateContentId
#      templateContentName0: content.data.contentList.0.contentName
#      templateContentId1: content.data.contentList.1.templateContentId
#      templateContentName1: content.data.contentList.1.contentName
#      signerId0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.signerId
#      signerName0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.name
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.templateId, $newTemplateUuidCommon]
#######################################################################
############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $testPresetId
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
      - _label_0: content.data.fields.0.label #签署控件
      - _label_1: content.data.fields.1.label
      - _label_2: content.data.fields.2.label #填写控件
      - _label_3: content.data.fields.3.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
#      _tmp_fill: [ {"label": "$_label_3","templateRoleId": "$_signerId1" }]
#      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,$_tmp_fill)}"
      fields_draft1: "${update_template_role_id($_fields,$_label_3,$_signerId1)}"
      fields_draft2: "${update_template_role_id($fields_draft1,$_label_0,$_signerId1)}"
      fields_draft: "${update_template_role_id($fields_draft2,$_label_1,$_signerId1)}"
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

#######################################################################
- test:
    name: "TC13-添加业务模板配置绑定模板内容域数据(发起方填写+签署方填写)，并启用业务模板"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC14-获取批量发起选择页该业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.presetId", $testPresetId]
