- config:
    name: "openapi:查询电子签署业务模板详情接口测试用例"
    variables:
      orgCode: ${ENV(sign01.main.orgCode)}
      orgDeptNo: ${ENV(sign01.main.orgNo)}
      orgName: ${ENV(sign01.main.orgName)}
      deptCode: ${ENV(sign04.main.orgCode)}
      deptNo: ${ENV(sign04.main.orgNo)}
      sign04UserCode: ${ENV(sign04.userCode)}
      sign04AccountNo: ${ENV(sign04.accountNo)}
      sign01UserCode: ${ENV(sign01.userCode)}
      sign01AccountNo: ${ENV(sign01.accountNo)}
      commonSealTypeCode: "COMMON-SEAL" #公章
      legalPersonSealTypeCode: "LEGAL-PERSON-SEAL" #法人章
      presetName_bizDetail_01: "自动化测试电子签署较全的业务模板-${getDateTime()}"
      presetName_bizDetail_02: "自动化测试电子签署业务模板-${getDateTime()}"
      presetName_bizDetail_03: "自动化测试ofd电子签署业务模板-${getDateTime()}"
      presetName_bizDetail_04: "自动化测试pdf电子签署业务模板-word模板-${getDateTime()}"


- test:
    name: setup-获取物理用印业务模板编码
    testcase: common/businessPreset/webapiCreatePhysicalbusinessPreset.yml
    extract:
      - businessTypeCode_physical_commonCase

- test:
      name: TC-businessTypeCode为启用的物理用印业务模板-openapi查询业务模板详情
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_physical_commonCase
      validate:
          - eq: [ content.code, 1606063 ]
          - eq: [ content.message, "仅支持查询电子签署类型的业务模板" ]
          - eq: [ content.data, null ]

- test:
    name: setup-获取一个包含所有类型的内容域的pdf文档模板,有一个签署区
    testcase: common/template/buildTemplate6.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - autoTestDocUuid1Common

- test:
    name: setup-查询模板预览页面
    api: api/esignDocs/documents/template/pdfPreviewUrl.yml
    variables:
      - templateId: $newTemplateUuidCommon
    extract:
      templatePreviewUrl_detail: content.data.templatePreviewUrl
    validate:
      - eq: [ content.message, "成功"]
      - eq: [ content.code, 200 ]

- test:
    name: setup-获取一个启用状态的电子签署业务模板，3个节点分别为顺序签、无序签、或签，包含指定印章类型、自动签署、内外部指定或不指定签署方，pdf各种内容域类型
    testcase: common/businessPreset/webapiCreatebusinessPreset.yml
    variables:
      queryPresetName: "$presetName_bizDetail_01"
      fileFormat: 1 #1-PFD;2-OFD
      signAreaSignEnable: 0 #1-必签；0-非必签
      signAreaMoveEnable: 0 #1-可移动；0-不可移动
      handEnable: 0
      aiHandEnable: 0
      signEndTimeEnable: 0
      forceReadingTime: 0
      downloadEnable: 0
      allowAddSigner: 0
      signerNodeList: [  #签署方列表，包含：节点1顺序签：指定、不指定的内部用户（自动签署），节点2无序签：指定/不指定的内部企业（指定法人章、公章），节点3或签：相对方个人、相对方企业
            {
                "signMode": 0,
                "signNode": 1,
                "signerList": [
                    { #内部用户指定
                        "signOrder": 1,
                        "signerType": 1,
                        "signerTerritory": 1,
                        "allowAddAttachment": 1,
                        "draggable": true,
                        "organizeCode": "",
                        "userCode": $sign01UserCode,
                        "sealTypeCode": "",
                        "sealTypeList": [],
                        "accountList": [],
                        "organizeList": [],
                        "autoSign": 1,
                        "legalSign": 0,
                        "onlyUkeySign": 0,
                        "assignSigner": 1, #指定签署方
                        "signerId": "${getRandomNo()}",
                        "signatoryList": [],
                    },
                    { #内部用户不指定
                        "signOrder": 2,
                        "signerType": 1,
                        "signerTerritory": 1,
                        "draggable": true,
                        "organizeCode": "",
                        "userCode": null,
                        "sealTypeCode": "",
                        "sealTypeList": [],
                        "accountList": [],
                        "organizeList": [],
                        "autoSign": 0,
                        "legalSign": 0,
                        "onlyUkeySign": 1,
                        "assignSigner": 0, #不指定签署方
                        "signerId": "${getRandomNo()}",
                        "signatoryList": []
                    }
                ]
            },
            {
              "signMode": 1,
              "signNode": 2,
              "signerList": [
                { #内部企业指定
                  "signerType": 2,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": $orgCode,
                  "userCode": $sign01UserCode,
                  "sealTypeCode": "$commonSealTypeCode",
                  "accountList": [ ],
                  "organizeList": [ ],
                  "autoSign": 0,
                  "legalSign": 1, #法人章
                  "onlyUkeySign": 2,
                  "assignSigner": 1,
                  "signerId": "${getRandomNo()}",
                  "departmentCode": $orgCode,
                  "signatoryList": [ ]
                },
                { #内部企业不指定
                  "signerType": 2,
                  "signerTerritory": 1,
                  "draggable": true,
                  "organizeCode": null,
                  "userCode": null,
                  "sealTypeCode": "",
                  "accountList": [ ],
                  "organizeList": [ ],
                  "autoSign": 1,
                  "legalSign": 0,
                  "onlyUkeySign": 0,
                  "assignSigner": 0,
                  "signerId": "${getRandomNo()}",
                  "departmentCode": ,
                  "signatoryList": []
                }
              ]
            },
        {
          "signMode": 2,
          "signNode": 3,
          "signerList": [
            { #相对方个人
              "signerType": 1,
              "signerTerritory": 2, #相对方
              "draggable": true,
              "organizeCode": null,
              "userCode": null,
              "sealTypeCode": "",
              "accountList": [ ],
              "organizeList": [ ],
              "autoSign": 0,
              "legalSign": 0,
              "onlyUkeySign": 0,
              "assignSigner": 0,
              "signerId": "${getRandomNo()}",
              "departmentCode": ,
              "signatoryList": [ ]
            },
            { #相对方企业
              "signerType": 2,
              "signerTerritory": 2, #相对方
              "draggable": true,
              "organizeCode": null,
              "userCode": null,
              "sealTypeCode": "",
              "accountList": [ ],
              "organizeList": [ ],
              "autoSign": 0,
              "legalSign": 0,
              "onlyUkeySign": 0,
              "assignSigner": 0,
              "signerId": "${getRandomNo()}",
              "departmentCode": ,
              "signatoryList": [ ]
            }
          ]
        }
        ]
      initiatorAll: 0 #指定发起人权限
      initiatorList: [
        {"organizationCode":"$orgCode"}, #企业
        {"organizationCode":"$deptCode"}, #部门
        {"organizationCode":"$deptCode","userCode": "$sign04UserCode" }, #内部用户-部门
        {"organizationCode":"$orgCode","userCode": "$sign01UserCode"} #内部用户-企业
      ]
      templateList: [ #有所有类型内容域的pdf文档模板
        {"templateId": $newTemplateUuidCommon ,"templateName": $templateNameCommon, "version": $newVersionCommon}
      ]
      presetStatus_common_addSigners: 0 #先不启用
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.message, "成功" ]
        - eq: [ content.success, true ]

- test:
    name: setup-通过名称精确查询业务模板，获取业务模板编码
    api: api/esignDocs/businessPreset/list.yml
    variables:
       queryPresetName: $presetName_bizDetail_01
    extract:
      - businessTypeCode_bizDetail_01: content.data.list.0.businessTypeId
      - presetId_bizDetail_01: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName_bizDetail_01 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: "templateDetail-获取业务模板配置绑定企业模板详情"
    api: api/esignDocs/businessPreset/templateDetail.yml
    variables:
      - presetId: $presetId_bizDetail_01
      - templateId: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.templateId, $newTemplateUuidCommon]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId_bizDetail_01
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
      - _label_0: content.data.fields.0.label #签署控件
      - _label_1: content.data.fields.1.label
      - _label_2: content.data.fields.2.label #单行文本
      - _label_3: content.data.fields.3.label #多行文本
      - _label_4: content.data.fields.4.label #数字
      - _label_5: content.data.fields.5.label #日期
      - _label_6: content.data.fields.6.label #手机号
      - _label_7: content.data.fields.7.label #身份证号
      - _label_0: content.data.fields.0.label #签署控件
      - field_1: content.data.fields.0
      - field_2: content.data.fields.1 #单行文本
      - field_3: content.data.fields.2 #多行文本
      - field_4: content.data.fields.3 #数字
      - field_5: content.data.fields.4 #日期
      - field_6: content.data.fields.5 #手机号
      - field_7: content.data.fields.6 #身份证号
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      _field_1: ${add_template_role_id($field_1,$_signerId1)}
      _field_2: ${add_template_role_id($field_2,$_signerId1)} #单行文本
      _field_7: ${add_template_role_id($field_7,$_signerId1)}
#      _tmp_fill: [ {"label": "$_label_6","templateRoleId": "$_signerId1" }]
#      fields_draft1: "${getEpaasTemplateContentWithRoleId($_fields,$_tmp_fill)}"
#      fields_draft2: "${getEpaasTemplateContentWithRoleId($fields_draft1,$_tmp_fill1)}"
      fields_draft: [$_field_1,$_field_2,$field_3,$field_4,$field_5,$field_6,$_field_7]
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]
      - ne: ["content.data.0.contentId",""]
- test:
    name: setup-业务模板配置的第4步：设置签署方填写信息，将第一个内容域填写方设置为节点1签署方1填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $presetId_bizDetail_01
      - status: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#    teardown_hooks:
#      - ${sleep(3)}

- test:
      name: TC-openapi查询电子签署业务模板详情：业务模板信息为：3个节点分别为顺序签、无序签、或签，包含指定印章类型、自动签署、内外部指定或不指定签署方，pdf各种内容域类型-
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_bizDetail_01
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCode_bizDetail_01 ]
          - eq: [ content.data.bizTemplateName, $presetName_bizDetail_01 ]
          - eq: [ content.data.fileFormat, "PDF" ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 1]
          #发起人范围校验开始
          - eq: [ content.data.initiatorInfo.initiatorList.0.userType, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorList.0.userCode, null]
          - eq: [ content.data.initiatorInfo.initiatorList.0.customAccountNo, null]
          - eq: [ content.data.initiatorInfo.initiatorList.0.userName, null]
          - eq: [ content.data.initiatorInfo.initiatorList.0.departmentCode, $orgCode]
          - eq: [ content.data.initiatorInfo.initiatorList.0.customDepartmentNo, $orgDeptNo]
          - eq: [ content.data.initiatorInfo.initiatorList.0.departmentName, $orgName]
          - eq: [ content.data.initiatorInfo.initiatorList.1.userType, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.userCode, null ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.customAccountNo, null ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.userName, null ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.departmentCode, $deptCode ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.customDepartmentNo, $deptNo ]
          - eq: [ content.data.initiatorInfo.initiatorList.1.departmentName, "esigntest三级部门CI" ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.userType, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.userCode, $sign04UserCode ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.customAccountNo, $sign04AccountNo ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.userName, "测试签署四" ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.departmentCode, $deptCode ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.customDepartmentNo, $deptNo ]
          - eq: [ content.data.initiatorInfo.initiatorList.2.departmentName, "esigntest三级部门CI" ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.userType, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.userCode, $sign01UserCode ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.customAccountNo, $sign01AccountNo ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.userName, "测试签署一" ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.departmentCode, $orgCode ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.customDepartmentNo, $orgDeptNo ]
          - eq: [ content.data.initiatorInfo.initiatorList.3.departmentName, $orgName ]
          #发起人范围校验结束
          #签署模板校验开始
          - eq: [ content.data.docTemplateInfos.0.docTemplateId, $newTemplateUuidCommon ]
          - eq: [ content.data.docTemplateInfos.0.docTemplateName, $templateNameCommon ]
          - startswith: [ content.data.docTemplateInfos.0.docTemplatePreviewUrl, "http"]
          #签署模板校验结束
          #签署方信息校验开始
          #节点1，签署方1
          - eq: [ content.data.signerInfos.0.userType, 1]
          - eq: [ content.data.signerInfos.0.signerType, 1]
          - eq: [ content.data.signerInfos.0.signerId, $_signerId1]
          - eq: [ content.data.signerInfos.0.signNode, 1]
          - eq: [ content.data.signerInfos.0.signMode, 0]
          - eq: [ content.data.signerInfos.0.signOrder, 1]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.allowAddAttachment, 1]
          - eq: [ content.data.signerInfos.0.autoSign, true]
          - eq: [ content.data.signerInfos.0.UkeyOnly, 0]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.userCode, $sign01UserCode]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.customAccountNo, $sign01AccountNo]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.userName, "测试签署一"]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.departmentCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.customDepartmentNo, null]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.departmentName, null]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, null]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.customOrgNo, null]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationName, null]
          #节点1，签署方2
          - eq: [ content.data.signerInfos.1.userType, 1]
          - eq: [ content.data.signerInfos.1.signerType, 1]
          - ne: [ content.data.signerInfos.1.signerId, null]
          - eq: [ content.data.signerInfos.1.signNode, 1]
          - eq: [ content.data.signerInfos.1.signMode, 0]
          - eq: [ content.data.signerInfos.1.signOrder, 2]
          - eq: [ content.data.signerInfos.1.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.1.allowAddAttachment, 0]
          - eq: [ content.data.signerInfos.1.autoSign, false]
          - eq: [ content.data.signerInfos.1.UkeyOnly, 1] #仅UKey印章
          - eq: [ content.data.signerInfos.1.signerAccountInfo, null]
          #节点2，签署方1
          - eq: [ content.data.signerInfos.2.userType, 1]
          - eq: [ content.data.signerInfos.2.signerType, 2]
          - ne: [ content.data.signerInfos.2.signerId, null]
          - eq: [ content.data.signerInfos.2.signNode, 2]
          - eq: [ content.data.signerInfos.2.signMode, 1]
          - eq: [ content.data.signerInfos.2.signOrder, 1]
          - eq: [ content.data.signerInfos.2.sealTypeCode, "$commonSealTypeCode,$legalPersonSealTypeCode" ]
          - eq: [ content.data.signerInfos.2.allowAddAttachment, 0]
          - eq: [ content.data.signerInfos.2.autoSign, false]
          - eq: [ content.data.signerInfos.2.UkeyOnly, 2] #仅云印章
          - eq: [ content.data.signerInfos.2.signerAccountInfo.userCode, $sign01UserCode]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.customAccountNo, $sign01AccountNo]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.userName, "测试签署一"]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.departmentCode, $orgCode ]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.customDepartmentNo, $orgDeptNo]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.departmentName, $orgName]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.organizationCode, $orgCode]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.customOrgNo, $orgDeptNo]
          - eq: [ content.data.signerInfos.2.signerAccountInfo.organizationName, $orgName]
          #节点2，签署方2
          - eq: [ content.data.signerInfos.3.userType, 1 ]
          - eq: [ content.data.signerInfos.3.signerType, 2 ]
          - ne: [ content.data.signerInfos.3.signerId, null ]
          - eq: [ content.data.signerInfos.3.signNode, 2 ]
          - eq: [ content.data.signerInfos.3.signMode, 1 ]
          - eq: [ content.data.signerInfos.3.signOrder, 1 ]
          - eq: [ content.data.signerInfos.3.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.3.allowAddAttachment, 0 ]
          - eq: [ content.data.signerInfos.3.autoSign, true ]
          - eq: [ content.data.signerInfos.3.UkeyOnly, 0 ]
          - eq: [ content.data.signerInfos.3.signerAccountInfo, null ]
          #节点3，签署方1
          - eq: [ content.data.signerInfos.4.userType, 2 ]
          - eq: [ content.data.signerInfos.4.signerType, 1 ]
          - ne: [ content.data.signerInfos.4.signerId, null ]
          - eq: [ content.data.signerInfos.4.signNode, 3 ]
          - eq: [ content.data.signerInfos.4.signMode, 2 ]
          - eq: [ content.data.signerInfos.4.signOrder, 1 ]
          - eq: [ content.data.signerInfos.4.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.4.allowAddAttachment, 0 ]
          - eq: [ content.data.signerInfos.4.autoSign, false ]
          - eq: [ content.data.signerInfos.4.UkeyOnly, 2 ] #仅云印章
          - eq: [ content.data.signerInfos.4.signerAccountInfo, null ]
          #节点3，签署方2
          - eq: [ content.data.signerInfos.5.userType, 2 ]
          - eq: [ content.data.signerInfos.5.signerType, 2 ]
          - ne: [ content.data.signerInfos.5.signerId, null ]
          - eq: [ content.data.signerInfos.5.signNode, 3 ]
          - eq: [ content.data.signerInfos.5.signMode, 2 ]
          - eq: [ content.data.signerInfos.5.signOrder, 1 ]
          - eq: [ content.data.signerInfos.5.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.5.allowAddAttachment, 0 ]
          - eq: [ content.data.signerInfos.5.autoSign, false ]
          - eq: [ content.data.signerInfos.5.UkeyOnly, 2 ] #仅云印章
          - eq: [ content.data.signerInfos.5.signerAccountInfo, null ]
          #签署方信息校验结束
          #填写信息集合校验开始
          #发起方填写
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "0" ]
#          #邮箱
#          - contains: [ content.data.fillingUserInfos.0.contentsControl.0.contentName, "自动化测试" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.contentFormat, "MAIL" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.contentFormatRule, "" ]
#          #数字
#          - contains: [ content.data.fillingUserInfos.0.contentsControl.1.contentName, "自动化测试" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentFormat, "NUMBER" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentFormatRule, "2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.required, "1" ]
#          #统一社会信用代码
#          - contains: [ content.data.fillingUserInfos.0.contentsControl.2.contentName, "自动化测试" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentFormat, "UNITED_CODE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentFormatRule, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentCode, "" ]
#          #文本
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentName, "自动化测试-文本" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentFormat, "TEXT" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentFormatRule, "11" ]
#          #日期1
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentName, "自动化测试-日期1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentFormat, "DATE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentFormatRule, "yyyy年MM月dd日" ]
#          #日期2
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentName, "自动化测试-日期2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentFormat, "DATE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentFormatRule, "yyyy-MM-dd" ]
#          #日期3
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentName, "自动化测试-日期3" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentFormat, "DATE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentFormatRule, "yyyy/MM/dd" ]
#          #身份证号码
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.7.contentName, "自动化测试-身份证" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.7.contentFormat, "ID_CARD" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.7.contentFormatRule, "" ]
          #签署方填写
          #手机号
          - eq: [ content.data.fillingUserInfos.1.fillingUserType, "1" ]
          - eq: [ content.data.fillingUserInfos.1.signerId, $_signerId1 ]
          - contains: [ content.data.fillingUserInfos.1.contentsControl.0.contentName, "手机号" ]
          - contains: [ content.data.fillingUserInfos.1.contentsControl.0.contentCode, "手机号" ]
          - eq: [ content.data.fillingUserInfos.1.contentsControl.0.contentFormat, "MOBILE" ]
          - eq: [ content.data.fillingUserInfos.1.contentsControl.0.contentFormatRule, null ]
          - eq: [ content.data.fillingUserInfos.1.contentsControl.0.required, "0" ]

#######业务模板关联的的文档模板为无内容域和无签署区的pdf模板
- test:
    name: setup-获取一个pdf文档模板：无内容域和无签署区
    testcase: common/template/buildTemplate1.yml
    extract:
      - newTemplateUuidCommon_template1
      - newVersionCommon_template1
      - templateNameCommon_template1

- test:
    name: setup-获取一个启用状态的pdf电子签署业务模板：1个文档模板无内容域无签署区-发起时指定签署方-发起人范围为所有人
    testcase: common/businessPreset/webapiCreatebusinessPreset.yml
    variables:
      queryPresetName: "$presetName_bizDetail_02"
      fileFormat: 1 #1-PFD;2-OFD
      signAreaSignEnable: 0 #1-必签；0-非必签
      signAreaMoveEnable: 0 #1-可移动；0-不可移动
      handEnable: 0
      aiHandEnable: 0
      signEndTimeEnable: 1
      forceReadingTime: 0
      downloadEnable: 1
      allowAddSigner: 1 #发起时设置签署方
      allowAddFile: 0 #不允许发起时添加签署文件
      signerNodeList: []
      initiatorAll: 1 #所有人可使用
      initiatorList: []
      templateList: [ #有所有类型内容域的pdf文档模板
        {"templateId": $newTemplateUuidCommon_template1 ,"templateName": $templateNameCommon_template1, "version": $newVersionCommon_template1}
      ]
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.message, "成功" ]
        - eq: [ content.success, true ]

- test:
    name: setup-通过名称精确查询业务模板，获取业务模板编码
    api: api/esignDocs/businessPreset/list.yml
    variables:
       queryPresetName: $presetName_bizDetail_02
    extract:
      - businessTypeCode_bizDetail_02: content.data.list.0.businessTypeId
      - presetId_bizDetail_02: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName_bizDetail_02 ]
      - eq: [ content.data.total, 1 ]

- test:
      name: TC-openapi查询电子签署业务模板详情：1个文档模板无内容域无签署区-发起时指定签署方-发起人范围为所有人
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_bizDetail_02
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCode_bizDetail_02 ]
          - eq: [ content.data.bizTemplateName, $presetName_bizDetail_02 ]
          - eq: [ content.data.fileFormat, "PDF" ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 1 ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ]
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0 ] #所有用户
          - eq: [ content.data.initiatorInfo.initiatorList, null ] #发起人范围集合
          - eq: [ content.data.signerInfos, null] #签署方信息
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: setup-停用业务模板（后续验证是否可以openapi查询未启用的业务模板）
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $presetId_bizDetail_02
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
      name: TC-openapi查询电子签署业务模板详情：未启用状态的业务模板
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_bizDetail_02
      validate:
          - eq: [ content.code, 1606064 ]
          - eq: [ content.message, "业务模板未启用" ]
          - eq:  [ content.data, null ]

#######OFD类型的电子签署业务模板
- test:
    name: setup-获取一个启用状态的ofd电子签署业务模板-发起时指定签署方-发起人范围为所有人
    testcase: common/businessPreset/webapiCreatebusinessPreset.yml
    variables:
      queryPresetName: "$presetName_bizDetail_03"
      fileFormat: 2 #1-PFD;2-OFD
      signAreaSignEnable: 0 #1-必签；0-非必签
      signAreaMoveEnable: 0 #1-可移动；0-不可移动
      handEnable: 0
      aiHandEnable: 0
      signEndTimeEnable: 1
      forceReadingTime: 0
      downloadEnable: 1
      allowAddSigner: 1 #发起时设置签署方
      allowAddFile: 1 #允许发起时添加签署文件
      signerNodeList: []
      initiatorAll: 1 #所有人可使用
      initiatorList: []
      templateList: []
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.message, "成功" ]
        - eq: [ content.success, true ]

- test:
    name: setup-通过名称精确查询业务模板，获取业务模板编码
    api: api/esignDocs/businessPreset/list.yml
    variables:
       queryPresetName: $presetName_bizDetail_03
    extract:
      - businessTypeCode_bizDetail_03: content.data.list.0.businessTypeId
      - presetId_bizDetail_03: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName_bizDetail_03 ]
      - eq: [ content.data.total, 1 ]

- test:
      name: TC-openapi查询电子签署业务模板详情：ofd电子签署业务模板-发起时指定签署方-发起人范围为所有人
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_bizDetail_03
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCode_bizDetail_03 ]
          - eq: [ content.data.bizTemplateName, $presetName_bizDetail_03 ]
          - eq: [ content.data.fileFormat, "OFD" ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 1 ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0 ] #所有用户
          - eq: [ content.data.initiatorInfo.initiatorList, null ] #发起人范围集合
          - eq: [ content.data.signerInfos, null] #签署方信息
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

#######电子签署业务模板,包含一个word模板，内容域包含图片、单选、多选、动态表格
- test:
    name: setup-获取一个word文档模板：内容域包含图片（3种格式）、单选、多选、动态表格（2个，其中一个拆分了单元格）
    testcase: common/template/word-buildTemplate-wordContents.yml
    variables:
       templateName_commonBuildWordTemplate: "自动化测试通用word模版-word专有内容域-${get_randomNo_16()}"
       contents_commonBuildWordTemplate: [
            {
                "contentCode": "zdhcs-sfzhp1",
                "contentName": "图片-身份证横排",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "1",
                "formatType": 11,
                "required": 0,
                "thirdKey": "ele-1715669284680",
                "templateContentOrder": 1
            },
            {
                "contentCode": "",
                "contentName": "图片-其他不锁定比例",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "3",
                "formatType": 11,
                "required": 0,
                "thirdKey": "ele-1715669353257",
                "templateContentOrder": 2
            },
            {
                "contentCode": "",
                "contentName": "图片-身份证竖排",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "2",
                "formatType": 11,
                "required": 0,
                "thirdKey": "ele-1715669295950",
                "templateContentOrder": 3
            },
            {
                "contentCode": "",
                "contentName": "单选",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "自动化测试单选",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "2",
                "formatType": 9,
                "formatSelect": [
                    "单选选项1",
                    "单选选项2",
                    "单选选项3",
                    "单选选项4"
                ],
                "required": 0,
                "thirdKey": "ele-1715669288743",
                "templateContentOrder": 4
            },
            {
                "contentCode": "zdhcs-dx",
                "contentName": "多选",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "自动化测试",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "1",
                "formatType": 10,
                "formatSelect": [
                    "多选选项1",
                    "多选选项2",
                    "多选选项3",
                    "多选选项4",
                    "多选选项5",
                    "多选选项6"
                ],
                "required": 0,
                "thirdKey": "ele-1715669291077",
                "templateContentOrder": 5
            },
            {
                "contentCode": "",
                "contentName": "自动化测试动态表格-带表头",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "",
                "formatType": 12,
                "formatSelect": [
                    "column1",
                    "column2",
                    "column3",
                    "column4",
                    "column5"
                ],
                "required": 0,
                "tableNames": [
                    "表头1",
                    "表头2",
                    "表头3",
                    "表头4",
                    "表头5"
                ],
                "thirdKey": "ele-1715669293444",
                "templateContentOrder": 6
            },
            {
                "contentCode": "zidhcs-dtbg",
                "contentName": "动态表格-无表头-有单元格分割",
                "dataSource": "",
                "encrypted": 0,
                "sourceField": "",
                "description": "",
                "font": "SimSun",
                "fontColor": "BLACK",
                "fontSize": 12,
                "fontStyle": "Normal",
                "space": 2,
                "formatRule": "",
                "formatType": 12,
                "formatSelect": [
                    "c1",
                    "c2",
                    "c3",
                    "c4"
                ],
                "required": 0,
                "tableNames": [],
                "thirdKey": "ele-1715669925771",
                "templateContentOrder": 7
            }
        ]
    extract:
      - templateUuid_commonBuildWordTemplate
      - version_commonBuildWordTemplate
      - templateName_commonBuildWordTemplate
      - docUuid_commonBuildWordTemplate

- test:
    name: setup-获取一个启用状态的pdf电子签署业务模板：1个word文档模板，内容域包含图片（3种格式）、单选、多选、动态表格（2个，其中一个拆分了单元格）
    testcase: common/businessPreset/webapiCreatebusinessPreset.yml
    variables:
      queryPresetName: "$presetName_bizDetail_04"
      fileFormat: 1 #1-PFD;2-OFD
      signAreaSignEnable: 0 #1-必签；0-非必签
      signAreaMoveEnable: 0 #1-可移动；0-不可移动
      handEnable: 0
      aiHandEnable: 0
      signEndTimeEnable: 1
      forceReadingTime: 0
      downloadEnable: 1
      allowAddSigner: 1 #发起时设置签署方
      allowAddFile: 1 #不允许发起时添加签署文件
      signerNodeList: []
      initiatorAll: 1 #所有人可使用
      initiatorList: []
      templateList: [ #有所有类型内容域的pdf文档模板
        {"templateId": $templateUuid_commonBuildWordTemplate ,"templateName": $templateName_commonBuildWordTemplate, "version": $version_commonBuildWordTemplate}
      ]
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.message, "成功" ]
        - eq: [ content.success, true ]

- test:
    name: setup-通过名称精确查询业务模板，获取业务模板编码
    api: api/esignDocs/businessPreset/list.yml
    variables:
       queryPresetName: $presetName_bizDetail_04
    extract:
      - businessTypeCode_bizDetail_04: content.data.list.0.businessTypeId
      - presetId_bizDetail_04: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName_bizDetail_04 ]
      - eq: [ content.data.total, 1 ]

- test:
      name: TC-openapi查询电子签署业务模板详情：1个word文档模板，内容域包含图片（3种格式）、单选、多选、动态表格（2个，其中一个拆分了单元格）
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $businessTypeCode_bizDetail_04
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCode_bizDetail_04 ]
          - eq: [ content.data.bizTemplateName, $presetName_bizDetail_04 ]
          - eq: [ content.data.fileFormat, "PDF" ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 1 ]
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 1 ]
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0 ] #所有用户
          - eq: [ content.data.initiatorInfo.initiatorList, null ] #发起人范围集合
          - eq: [ content.data.signerInfos, null] #签署方信息
#            #内容域信息校验
#          - contains: [ content.data.fillingUserInfos.0.contentsControl.0.contentName, "图片-其他不锁定比例" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.contentCode, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.contentFormat, "IMAGE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.contentFormatRule, "3" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.0.required, "0" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentName, "多选" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentCode, "zdhcs-dx" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentFormat, "MULTIPLE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.contentFormatRule, '["多选选项1","多选选项2","多选选项3","多选选项4","多选选项5","多选选项6"]' ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.1.required, "0" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentName, "图片-身份证横排" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentCode, "zdhcs-sfzhp1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentFormat, "IMAGE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.contentFormatRule, "1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.2.required, "0" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentName, "自动化测试动态表格-带表头" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCode, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentFormat, "FORM" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentFormatRule, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.required, "0" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.0.columnContentName, "表头1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.0.columnContentCode, "column1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.1.columnContentName, "表头2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.1.columnContentCode, "column2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.2.columnContentName, "表头3" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.2.columnContentCode, "column3" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.3.columnContentName, "表头4" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.3.columnContentCode, "column4" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.4.columnContentName, "表头5" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.3.contentCodeInfos.4.columnContentCode, "column5" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentName, "图片-身份证竖排" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentCode, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentFormat, "IMAGE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.contentFormatRule, "2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.4.required, "0" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentName, "动态表格-无表头-有单元格分割" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCode, "zidhcs-dtbg" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentFormat, "FORM" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentFormatRule, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.required, "0" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.0.columnContentName, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.0.columnContentCode, "c1" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.1.columnContentName, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.1.columnContentCode, "c2" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.2.columnContentCode, "c3" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.5.contentCodeInfos.3.columnContentCode, "c4" ]
#
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentName, "单选" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentCode, "" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentFormat, "SINGLE" ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.contentFormatRule, '["单选选项1","单选选项2","单选选项3","单选选项4"]' ]
#          - eq: [ content.data.fillingUserInfos.0.contentsControl.6.required, "0" ]

