- config:
    name: "电子签署业务模板增加管理员--V6.0.13.0-beta3"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - fileKey0: ${ENV(fileKey)}
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode0: ${ENV(sign01.userCode)}
      - userName0: ${ENV(sign01.userName)}
#      - userCode1: ${ENV(sign01.userCode)}
      - userName2: ${ENV(userName)}
      - userCodeInit: ${ENV(csqs.userCode)}
      - subject: '业务模板增加管理员'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - commonTemplateName: "自动化pdf模板文件${get_randomNo()}"
      - presetName: "自动化业务模板${getDateTime()}"
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - autoTestDocUuid1: ${get_docConfig_type()}
        ####调试数据
#      - testPresetId: "517c8331f477020fee357ea72aa3e0e0"

#新建模板
- test:
    name: "setup1-模版新建"
    api: api/esignDocs/template/templateAdd.yml
    variables:
      - fileKey: $fileKey0
      - zipFileKey: null
      - templateName: $commonTemplateName
      - createUserOrg: $orgCode0
      - description: "自动化创建模板文件"
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",null]

- test:
    name: "setup1-添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - addSignTime: 0
      - signType: 2
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - pageNo: "1,2,5"
      - posX: 10
      - posY: 10
      - name: "signDetail2"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup1-添加内容域"
    api: api/esignDocs/template/manage/contentAdd.yml
    variables:
      - description: null
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - contentUuid: "AUTOUUID${get_randomNo()}"
      - contentCode: "AUTOCODE${get_randomNo()}"
      - contentName: "$contentCode"
      - edgeScope: 0
    extract:
      - templateContentUuid: content.data.list.0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",null]

#模板发布
- test:
    name: "setup1-模板发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup2-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]


- test:
    name: "setup2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName]

- test:
    name: "setup2-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $presetName]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

##查询组织id和用户id
- test:
    name: "setup-用户信息"
    api: api/esignDocs/user/getUserListByUserCodeName.yml
    variables:
      - userName: "$userName0"
    extract:
      - userId1: content.data.userList.0.id
      - organizationId1: content.data.userList.0.organizationId
      - organizationCode1: content.data.userList.0.organizationCode
      - getOrganizationName1: content.data.userList.0.companyName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）---增加管理员--默认是自己"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "$userId1",
          "code": $userCode0,
          "label": $userName0,
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "companyName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "userMainType": "1",
          "userCode": "$userCode0",
          "isUser": true,
          "userName": "$userName0",
          "userId": "$userId1"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-业务模板配置-第二步：签署方式（默认配置）--成功"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "setup2-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "setup2-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      authAll: 0
      authUserList: [
        {
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "companyName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "userMainType": "1",
          "userCode": "$userCode0",
          "isUser": true,
          "userName": "$userName0",
          "userId": "$userId1"
        }
      ]
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList:
            - autoSign: 0
              assignSigner: 1
              organizeCode: "$orgCode0"
              userName: "${ENV(sign01.userName)}"
              organizeName: "$orgName0"
              departmentName: "$orgName0"
              sealTypeCode:
              signerTerritory: 1
              signerType: 2
              userCode: "$userCode0"
              sealTypeName: ""
              departmentCode: "$orgCode0"
              signatoryList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#- test:
#    name: "setup-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
#    api: api/esignDocs/businessPreset/templateDetail.yml
#    variables:
#      - presetId: $testPresetId
#      - templateId: $newTemplateUuid1
#      - version: $newVersion1
#    extract:
#      templateContentId0: content.data.contentList.0.templateContentId
#      templateContentName0: content.data.contentList.0.contentName
#      signerId0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.signerId
#      signerName0: content.data.contentList.0.contentSourceTypeList.1.signerList.0.name
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.data.templateId, $newTemplateUuid1]
#
#- test:
#    name: "setup-业务模板配置的第4步：设置签署方填写信息，并启动"
#    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
#    variables:
#      - presetId: $testPresetId
#      - status: 1
#      - templateList:
#          - templateId: $newTemplateUuid1
#            version: $newVersion1
#            templateName: $commonTemplateName
#            contentList:
#              - contentId: $templateContentId0
#                contentName: $templateContentName0
#                contentSource:  2 #签署方填写
#                signerId: $signerId0
#                signerName: signerName0
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data",null ]


- test:
    name: "setup2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName]


- test:
    name: TC4-列表查询-停用
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $testPresetId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]
##查询组织id和用户id
- test:
    name: "setup-用户信息"
    api: api/esignDocs/user/getUserListByUserCodeName.yml
    variables:
      - userName: "$userName2"
    extract:
      - userId2: content.data.userList.0.id
      - organizationId2: content.data.userList.0.organizationId
      - organizationCode2: content.data.userList.0.organizationCode
      - getOrganizationName2: content.data.userList.0.companyName
      - userCode2: content.data.userList.0.userCode
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



##授权给用户不存在
- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--用户不存在"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "123",
          "code": "12222",
          "label": "测试",
          "organizationId": "2121212",
          "organizationName": "测试机构",
          "companyName": "测试机构",
          "organizationCode": "122122",
          "userMainType": "1",
          "userCode": "12222",
          "isUser": true,
          "userName": "测试",
          "userId": "123"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"管理员企业/用户全部不存在，管理员企业/用户会全部清空"]
      - eq: [content.status,1650005]
      - eq: [content.data,null]

####授权给所有
- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--授权所有"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 1
      authUserList: [
        {
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "userCode": "$userCode0",
          "userName": "$userName0",
          "userId": "$userId1"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--授权所有"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 1
      authUserList: [
        {
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "userCode": "$userCode0",
          "userName": "$userName0",
          "userId": "$userId1"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]

##授权给组织
- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--授权给组织"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "$organizationId1",
          "label": $getOrganizationName1,
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "companyName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "code": "$userCode0",
          "isUser": false,
          "type":"1",
          "parentOrganizationId":"0",
          "organizationTerritory": "1"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--授权给组织"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "$organizationId1",
          "label": $getOrganizationName1,
          "organizationId": "$organizationId1",
          "organizationName": "$getOrganizationName1",
          "companyName": "$getOrganizationName1",
          "organizationCode": "$organizationCode1",
          "code": "$userCode0",
          "isUser": false,
          "type":"1",
          "parentOrganizationId":"0",
          "organizationTerritory": "1"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]

##授权给别人
- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）--授权别人"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "$userId2",
          "code": $userCode2,
          "label": $userName2,
          "organizationId": "$organizationId2",
          "organizationName": "$getOrganizationName2",
          "companyName": "$getOrganizationName2",
          "organizationCode": "$organizationCode2",
          "userMainType": "1",
          "userCode": "$userCode2",
          "isUser": true,
          "userName": "$userName2",
          "userId": "$userId2"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "setup2-业务模板配置-第一步：填写基本信息（关联pdf模板）---无权"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      authAll: 0
      authUserList: [
        {
          "id": "$userId2",
          "code": $userCode2,
          "label": $userName2,
          "organizationId": "$organizationId2",
          "organizationName": "$getOrganizationName2",
          "companyName": "$getOrganizationName2",
          "organizationCode": "$organizationCode2",
          "userMainType": "1",
          "userCode": "$userCode2",
          "isUser": true,
          "userName": "$userName2",
          "userId": "$userId2"
        }
      ]
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"暂无权限操作此业务模板"]
      - eq: [content.status,1650006]
      - eq: [content.data,null]

- test:
    name: "setup2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 0]

