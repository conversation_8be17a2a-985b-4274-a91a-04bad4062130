- config:
    name: "业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.2-备注区"
    variables:
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode0: ${ENV(sign01.userCode)}
      - userName0: ${ENV(sign01.userName)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - subject: '业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.2-备注区'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
#      - newTemplateUuid1: "${getTemplateId(0,2,pdf,0)}"  ##创建有2个签署区的模板，若有则返回已有
#      - newVersion1: 1
      - randStr: ${getDateTime()}
      - presetName1: "自动化D指定备注区-$randStr"
      - autoTestDocUuid1: ${get_docConfig_type()}
      - fileKey0: ${ENV(fileKey)}
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - commonTemplateName: "自动化pdf模板文件${get_randomNo()}"
      - sealTypeCodeCommon: "LEGAL-PERSON-SEAL"
      - userNo101: "tPreset102"
      - userName1: "测试用户备注"
      - orgNo101: "tPreset102"
      - orgName1: "esigntest自动化测试企业一〇二有限公司"
      - sealGroupName0: "$orgNo101-Auto-可删"

##############调试数据##################
#      - orgCode0: "89ef451155f84be0a8a0946f968a79bd"
#      - userCode0: "test101"
#      - orgSealId0: "1811020333388279809"
#      - legalSealId1: "1811020333388279810"
#      - testPresetId1: "db766eb88f55ba1ec271ff4e741ec65a"
#      - testPresetId3: "16ca619c7440e4cd4fe1df1b7ae5117c"
#      - testBusinessTypeId1: "c6b9acf563c758fc99ea200d4ed53432"
#      - testBusinessTypeId3: "f386d130fca1e7e1a8585751ee08cf10"
##################创建内部组织和用户-组织需要有法人##############
- test:
    name: Setup1-创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName1
        customOrgNo: $orgNo101
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode:
    extract:
      - orgCode1: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: Setup1-创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
      data:
        - customAccountNo: $userNo101
          name: $userName1
          mobile: ""
          email: "<EMAIL>"
          licenseType: ID_CARD
          licenseNo: ""
          bankCardNo:
          mainOrganizationCode:
          mainCustomOrgNo: $orgNo101
          otherOrganization: [ ]
    extract:
      - userCode1: content.data.successData.0.userCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

- test:
    name: Setup1-修改内部组织-添加法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      data:
        customOrgNo: $orgNo101
        legalRepAccountNo: $userNo101
    extract:
      - code0: content.code
      - message0: content.message
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

#####################新建一个只有备注的PDF文档模板#############
#新建模板
- test:
    name: "setup2-模版新建"
    api: api/esignDocs/template/templateAdd.yml
    variables:
      - fileKey: $fileKey0
      - zipFileKey: null
      - templateName: $commonTemplateName
      - createUserOrg: $orgCode0
      - description: "自动化创建模板文件"
      - docUuid: $autoTestDocUuid1
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",null]

- test:
    name: "setup2-添加签名区"
    api: api/esignDocs/template/manage/signatoryAdd.yml
    variables:
      templateUuid: $newTemplateUuid1
      version: $newVersion1
      signatories:
        - addSignTime: 0
          name: "remarkPdf001"
          pageNo: "1"
          posX: 227
          posY: 227
          edgeScope: null
          leaveGroup: true
          allowMove: true
          signFieldType: 1
          signType: 1
          remarkSignatory:
            inputType: 2
            remarkFontSize: 10
            aiCheck: 0
            collectionTaskId:
            collectionTaskName:
            remarkContent: "123"
        - addSignTime: 0
          signType: 1
          name: "signPdf002"
          pageNo: "1"
          posX: 327
          posY: 327
          edgeScope: null
          leaveGroup: true
          allowMove: true
          signFieldType: 0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success", True]

#模板发布
- test:
    name: "setup2-模板发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
####################新建企业印章并授权##############
- test:
    name: "Setup3-创建一枚企业章(默认)"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $userNo101
        customOrgNo: $orgNo101
        sealGroupName: $sealGroupName0
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealName: "企业章-模板印章-01"
    extract:
      orgSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: Setup3-授权用印人所有人-orgSealId0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $orgSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

###################业务模板A-指定内部企业和用章要求为法人章####
#模板发布
- test:
    name: "setup4-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "TC2-新建一个业务模板-指定签署位置包含备注"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    extract:
      - testPresetId1: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC2-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId1: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName1]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "TC2-业务模板配置-第一步：填写基本信息（关联pdf模板）"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $testPresetId1
      allowAddFile: 0
      presetName: $presetName1
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]

- test:
    name: "TC2-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId1
      presetId_addBusinessType: $testPresetId1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC2-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId1
#    extract:
#      - simpleVOList000: content.data.templateList.0.simpleVOList
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "TC2-通过业务模板配置id获取签署区列表-排序后的详情"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
#    variables:
#      - presetId: $testPresetId1
#      - signatoryNameA0: "${dataArraySort($simpleVOList000, signType,0, name)}"
#      - signatoryIdA0: "${dataArraySort($simpleVOList000, signType,0, id)}"
#      - signatoryNameB0: "${dataArraySort($simpleVOList000, signType,1, name)}"
#      - signatoryIdB0: "${dataArraySort($simpleVOList000, signType,1, id)}"
#      - tmp_001: "${putTempEnv(signatoryNameA_000, $signatoryNameA0)}"
#      - tmp_002: "${putTempEnv(signatoryIdA_000, $signatoryIdA0}"
#      - tmp_003: "${putTempEnv(signatoryNameB_000, $signatoryNameB0)}"
#      - tmp_004: "${putTempEnv(signatoryIdB_000, $signatoryIdB0)}"
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "TC2-业务模板A配置-第三步：签署方设置（设置签署方）-企业只选择备注区和经办人签署区,报错"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode1"
          userName: "$userName1"
          organizeName: "$orgName1"
          departmentName: "$orgName1"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode1"
          sealTypeName: ""
          departmentCode: "$orgCode1"
    validate:
      - contains: [ content.message, "签署文件中至少设置一个企业签署区或是法人签署区" ]
      - eq: [ content.status, 1606048 ]
      - eq: [ content.success, false ]
      - eq: [ content.data, null ]

- test:
    name: "TC3-业务模板配置-第三步：签署方设置为个人备注-成功"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: ""
          userName: "$userName1"
          organizeName: ""
          departmentName: "$orgName1"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 1
          userCode: "$userCode1"
          sealTypeName: ""
          departmentCode: "$orgCode1"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: teardown-删除内部用户
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      data:
        customAccountNo: $userNo101
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(1)}

####异步建议在单场景校验,全量运行脚本的时候多线程异步消息慢导致未能消费校验无法通过###############
#- test:
#    name: "TC11-页面业务模板A详情-查看业务模板信息-删除经办人影响"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId1
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.data.presetId, $testPresetId1]
#      - eq: [content.data.status, 0]
#      - contains: [content.data.changeReason,  "印章授权信息已变更" ]
##      - contains: [content.data.changeReason,  "用章要求已变更" ]
#      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
#      - eq: [content.data.presetName, $presetName1]
#      - ne: [content.data.signBusinessType.businessTypeId, null]
#      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: "TC4-业务模板A配置-第三步：签署方设置（设置签署方）-企业选择备注区和企业签署区-自动签署"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 1
          assignSigner: 1
          organizeCode: "$orgCode1"
          userName: "$userName0"
          organizeName: "$orgName1"
          departmentName: "$orgName0"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC5-查看初始状态新建的业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
#    extract:
#      - testBusinessTypeId3: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 0] #不允许追加文件
      - eq: [content.data.allowAddSigner, 0] #指定签署方
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName1]
      - len_eq: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]
      - len_eq: [content.data.signerNodeList.0.signerList.0.signatoryList, 2]

- test:
      name: TC6-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, null ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC7-使用业务模板发起-自动发起带有备注报错
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - subject: "openapi-自动化发起带有备注签署"
      - businessTypeCodeCBBT: $testBusinessTypeId1
      - CCInfosCBBT: []
    validate:
      - eq: [content.code, 1612212]
      - eq: [content.data, null]
      - eq: [content.message, "发起失败：备注签署区暂不支持自动签署，请联系管理员关闭自动签署或删除备注签署区"]

- test:
    name: TC8-停用业务模板
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $testPresetId1
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: "TC9-业务模板A配置-第三步：签署方设置（设置签署方）-企业选择备注区和企业签署区"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode1"
          userName: "$userName0"
          organizeName: "$orgName1"
          departmentName: "$orgName0"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: TC10-使用业务模板发起
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - subject: "openapi-自动化发起带有备注签署"
      - businessTypeCodeCBBT: $testBusinessTypeId1
      - CCInfosCBBT: []
    extract:
      - signFlowId010: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - eq: [content.message, "成功"]
      - eq: [content.data.signFlowStatus, "7"]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(2)}

- test:
    name: detail-signFlowId010-检查签署文件可签备注和经办人自由签，备注的属性正确
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId010
      - organizeCode: $orgCode1
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - actorSignConfigList000: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "legalSeal,personSeal"]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 165]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 10]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1"]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosX, "227.0"]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosY, "227.0"]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2]
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 2]
      - eq: [ "${dataArraySort($actorSignConfigList000, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList000, sealIdentityType,0, freeMode)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList000, sealIdentityType,1, sealIdentityType)}", 3 ]
      - eq: [ "${dataArraySort($actorSignConfigList000, sealIdentityType,1, freeMode)}", 0 ]
      - len_eq: [ content.data.signConfigInfo, 1 ] #签署文档

- test:
    name: teardown-删除内部组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      data:
        customOrgNo: $orgNo101
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(1)}

- test:
    name: "TC12-页面业务模板A详情-查看业务模板信息-删除主体企业影响"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.status, 0]
#      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
      - contains: [content.data.changeReason,  "签署方【$orgName1】信息已修改" ]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]
      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]