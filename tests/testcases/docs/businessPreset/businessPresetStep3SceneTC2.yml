- config:
    name: "业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.1"
    variables:
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode0: ${ENV(sign01.userCode)}
      - userName0: ${ENV(sign01.userName)}
      - userCode1: ${ENV(csqs.userCode)}
      - subject: '业务模板内部签署方指定后印控授权的联动校验-V6.0.12.0-beta.1'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - newTemplateUuid1: "${getTemplateId(0,2,pdf,0)}"  ##创建有2个签署区的模板，若有则返回已有
      - newVersion1: 1
      - randStr: ${getDateTime()}
      - presetName1: "企业A指定新印章类型-$randStr"
      - presetName2: "企业B指定新印章类型-$randStr"
      - presetName3: "企业A指定公章和法人-$randStr"
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - orgName0: ${ENV(sign01.main.orgName)}
      - autoTestDocUuid1: ${get_docConfig_type()}
      - sealTypeCodeCommon: "Seal${get_randomNo_str()}"

##############调试数据##################
#      - sealTypeCodeCommon: "Seal141625"
#      - sealId0: "1811020333388279809"
#      - sealId0_1: "1811020333388279810"
#      - sealId1: "1811020336563367937"
#      - testPresetId1: "db766eb88f55ba1ec271ff4e741ec65a"
#      - testPresetId2: "a7305c6fbcb9d5ea670b99974cc6d40b"
#      - testPresetId3: "16ca619c7440e4cd4fe1df1b7ae5117c"
#      - testBusinessTypeId1: "c6b9acf563c758fc99ea200d4ed53432"
#      - testBusinessTypeId2: "43c61b206c533b7ad609c213d901c433"
#      - testBusinessTypeId3: "f386d130fca1e7e1a8585751ee08cf10"
####################新建企业印章并授权##############
- test:
    name: Setup-新建印章类型
    api: api/esignSeals/sealType/saveSealType.yml
    variables:
      sealTypeCode: $sealTypeCodeCommon
      sealTypeName: $sealTypeCodeCommon

- test:
    name: "TC1-企业A：创建第一枚模板印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        userCode: $userCode0
        organizationCode: $orgCode0
        sealGroupName: "自动化分组-$sealTypeCodeCommon（可删）"
        sealTypeCode: $sealTypeCodeCommon
        sealInfos:
          - sealPattern: 1
            sealName: "SCENE-TC1-模板印章"
          - sealPattern: 1
            sealName: "SCENE-TC2-模板印章"
    extract:
      sealId0: content.data.sealInfos.0.sealId
      sealId0_1: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

#授权用印人
- test:
    name: TC1-授权用印人所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: "TC1-企业B：创建第一枚模板印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        userCode: $userCode0
        organizationCode: $orgCode1
        sealGroupName: "自动化分组-$sealTypeCodeCommon（可删）"
        sealTypeCode: $sealTypeCodeCommon
        sealInfos:
          - sealPattern: 1
            sealName: "SCENE-TC1-模板印章"
    extract:
      sealId1: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

#授权用印人
- test:
    name: TC1-授权用印人所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId1
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

###################业务模板A-指定内部企业和用章要求####
#模板发布
- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "TC2-新建一个业务模板（企业A指定用章要求是新建类型）"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    extract:
      - testPresetId1: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC2-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    extract:
      - testBusinessTypeId1: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.presetName, $presetName1]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId1,
        "presetName": $presetName1,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuid1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]

#- test:
#    name: "TC2-业务模板配置-第一步：填写基本信息（关联pdf模板）"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      presetId_addDetail: $testPresetId1
#      allowAddFile: 0
#      presetName: $presetName1
#      templateList:
#        - fileKey: $fileKey0
#          templateId: $newTemplateUuid1
#          templateName: $commonTemplateName
#          version: $newVersion1
#          templateType: 1
#          contentDomainCount: 0
#          includeSpecialContent: 0
#    validate:
#      - eq: [content.message,"成功"]
#      - eq: [content.status,200]

- test:
    name: "TC2-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $testBusinessTypeId1
      presetId_addBusinessType: $testPresetId1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC2-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId1
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]



- test:
    name: "TC3-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgCode0"
          departmentName: ""
          sealTypeCode: "$sealTypeCodeCommon"
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#- test:
#    name: "节点一相对方企业+内部个人U2无序签，节点二相对方个人+内部企业O1经办人U3+内部企业O2经办人U1顺序签"
#    api: api/esignDocs/businessPreset/addSigners.yml
#    variables:
#      "signerList1": [
#        {
#          "autoSign": 0,
#          "organizeCode": "$orgCode0",
#          "sealTypeCode": "$sealTypeCodeCommon",
#          "id": "add-1",
#          "signatoryList": [ ],
#          "signerTerritory": 1,
#          "signerType": 2,
#          "userCode": "$userCode0",
#          "sealTypeName": "",
#          "departmentCode": "$orgCode0",
#          "signerId": "f8e6ed41-cc5b-4fd5-b6bd-f8bcf813fdfa"
#        }
#      ]
#      "params": {
#        "presetId": $testPresetId1,
#        "status": 0,
#        "allowAddSigner": 0,
#        "assignSigner": 1,
#        "needGather": 0,
#        "needAudit": 0,
#        "sort": 0,
#        "contentDomainCount": 0,
#        "editComponentValue": 0,
#        "fillingList": [],
#        "signerNodeList": [ {
#          "signerList": $signerList1,
#          "signMode": 1,
#          "signNode": 1,
#          "id": "node-0"
#        }]
#      }
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId1
      - fillingList_updateFillingUsers: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]


- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,0]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_00: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail--模板1"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: "${add_template_role_id($_fields10,$_signerId1)}"
      _tmp_fill2: "${add_template_role_id($_fields11,$_signerId1)}"
      fields_draft21: [$_tmp_fill1,$_tmp_fill2]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft21,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId1
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


###################业务模板B-指定内部企业和用章要求####
- test:
    name: "TC3-新建一个业务模板B（企业B指定用章要求是新建类型）"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName2
    extract:
      - testPresetId2: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC3-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    extract:
      - testBusinessTypeId2: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId2]
      - eq: [content.data.presetName, $presetName2]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId2,
        "presetName": $presetName2,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuid1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]

#- test:
#    name: "TC3-业务模板配置-第一步：填写基本信息（关联pdf模板）"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      presetId_addDetail: $testPresetId2
#      allowAddFile: 0
#      presetName: $presetName2
#      templateList:
#        - fileKey: $fileKey0
#          templateId: $newTemplateUuid1
#          templateName: $commonTemplateName
#          version: $newVersion1
#          templateType: 1
#          contentDomainCount: 0
#          includeSpecialContent: 0
#    validate:
#      - eq: [content.message,"成功"]
#      - eq: [content.status,200]

- test:
    name: "TC3-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName2
      businessTypeId: $testBusinessTypeId2
      presetId_addBusinessType: $testPresetId2
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC3-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId2
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "TC3-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId2
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode1"
          userName: "$userName0"
          organizeName: "$orgCode1"
          departmentName: ""
          sealTypeCode: "$sealTypeCodeCommon"
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode1"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId2
      - fillingList_updateFillingUsers: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]


- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId2
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,0]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_01: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_01)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail--模板2"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: "${add_template_role_id($_fields10,$_signerId1)}"
      _tmp_fill2: "${add_template_role_id($_fields11,$_signerId1)}"
      fields_draft21: [$_tmp_fill1,$_tmp_fill2]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft21,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId2
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

###################业务模板C-指定内部企业和用章要求非新创建的印章类型####
- test:
    name: "TC4-新建一个业务模板C（非新创建的印章类型）"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName3
    extract:
      - testPresetId3: content.data
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - ne: [content.data, null]

- test:
    name: "TC4-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    extract:
      - testBusinessTypeId3: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId3]
      - eq: [content.data.presetName, $presetName3]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

#- test:
#    name: "TC4-业务模板配置-第一步：填写基本信息（关联pdf模板）"
#    api: api/esignDocs/businessPreset/addDetail.yml
#    variables:
#      presetId_addDetail: $testPresetId3
#      allowAddFile: 0
#      presetName: $presetName3
#      templateList:
#        - fileKey: $fileKey0
#          templateId: $newTemplateUuid1
#          templateName: $commonTemplateName
#          version: $newVersion1
#          templateType: 1
#          contentDomainCount: 0
#          includeSpecialContent: 0
#    validate:
#      - eq: [content.message,"成功"]
#      - eq: [content.status,200]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $testPresetId3,
        "presetName": $presetName3,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuid1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]


- test:
    name: "TC4-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName3
      businessTypeId: $testBusinessTypeId3
      presetId_addBusinessType: $testPresetId3
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC4-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId3
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "TC4-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId3
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgCode0"
          departmentName: ""
          sealTypeCode: "COMMON-SEAL"
          legalSign: 1
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId3
      - fillingList_updateFillingUsers: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]


- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId3
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,0]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_02: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_02)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail--模板2"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: "${add_template_role_id($_fields10,$_signerId1)}"
      _tmp_fill2: "${add_template_role_id($_fields11,$_signerId1)}"
      fields_draft21: [$_tmp_fill1,$_tmp_fill2]
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft21,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId3
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

################操作1-修改企业A对应的印章授权########
######增量授权######
- test:
    name: TC5-授权用印人-增加同一印章类型下的印章授权
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId0_1
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC5-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
      name: TC5-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
      name: TC5-业务模板详情C-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId3
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId3 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "COMMON-SEAL,LEGAL-PERSON-SEAL" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC6-停用同一印章类型下的其他印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealId0_1
        sealStatus: 3 #更新成4-已停用 成功
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(1)}

- test:
      name: TC6-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
      name: TC6-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息
######从所有修改授权到只有经办人对应的企业#########
- test:
    name: TC7-授权用印人从所有人变更到经办人所在企业
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $orgCode0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC7-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
      name: TC7-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC8-恢复数据-授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC8-查询电子印章授权用印人
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $sealId0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.total, 0 ]
      - eq: [ content.data.authorizationScope, 2 ]
      - len_eq: [ content.data.records, 0 ]

- test:
    name: TC8-sealId0-授权用印人从指定企业变更到经办人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $orgCode0
            userCode: $userCode0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
      name: TC8-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId1 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode0" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
      name: TC8-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC9-恢复数据-授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC9-查询电子印章授权用印人
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $sealId0
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.total, 0 ]
      - eq: [ content.data.authorizationScope, 2 ]
      - len_eq: [ content.data.records, 0 ]

- test:
    name: TC9-授权用印人变更为非经办人(授权信息已变更只会影响业务模板A)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $orgCode1
            userCode: $userCode1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
      name: TC9-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 1606064 ]
          - eq: [ content.message, "业务模板未启用" ]
          - eq: [ content.data, null ]

- test:
    name: "TC9-页面业务模板详情-查看未启用原因"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  "印章授权信息已变更"]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]

- test:
      name: TC9-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息

- test:
    name: TC10-恢复数据授权所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC10-业务模板配置-第三步：签署方设置（设置签署方）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId1
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerNodeList:
        - signNode: 2
          signMode: 1
          signerList: $signerList
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgName0"
          departmentName: "$orgName0"
          sealTypeCode: "$sealTypeCodeCommon"
          signerTerritory: 1
          signerType: 2
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: TC10-sealId0停用-这个企业的类型下没有其他有效印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealId0
        sealStatus: 3 #更新成4-已停用 成功
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(1)}

- test:
      name: TC10-业务模板详情A-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId1
      validate:
          - eq: [ content.code, 1606064 ]
          - eq: [ content.message, "业务模板未启用" ]
          - eq: [ content.data, null ]

- test:
    name: "TC10-页面业务模板详情-查看未启用原因"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.presetId, $testPresetId1]
      - eq: [content.data.changeReason,  "印章授权信息已变更"]
      - eq: [content.data.presetName, $presetName1]
      - ne: [content.data.signBusinessType.businessTypeId, null]

- test:
      name: TC10-业务模板详情B-校验状态和第三步指定情况
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId2
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId2 ]
          - eq: [ content.data.signerInfos.0.sealTypeCode, "$sealTypeCodeCommon" ]
          - eq: [ content.data.signerInfos.0.signerAccountInfo.organizationCode, "$orgCode1" ]
          - eq: [ content.data.fillingUserInfos, []] #内容域信息