- config:
    name: "创建业务模板-点击下一步按钮-成功"
    variables:
      autoPresetName: "自动化测试业务模板${getDateTime()}"
      newTemplateUuid1: ${getTemplateId(0,0)}

- test:
    name: "添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取业务模版presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
      - status: ""
    extract:
      - getPresetId: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取企业模板详情"
    api: api/esignDocs/template/manage/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
    extract:
      - templateFileKey: content.data.fileKey
      - templateName: content.data.templateName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "点击下一步按钮-成功"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $autoPresetName,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "fileKey": $templateFileKey,
          "templateId": $newTemplateUuid1,
          "templateName": $templateName,
          "version": 1,
          "templateType": 1,
          "contentDomainCount": 0,
          "includeSpecialContent": 0
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message","成功"]
      - eq: ["content.status",200]