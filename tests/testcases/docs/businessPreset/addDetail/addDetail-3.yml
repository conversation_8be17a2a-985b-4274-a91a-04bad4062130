- config:
    name: "选择模板有不同属性的同名内容域-失败_内容域的校验属性不一致，名称不能重复--有bug，等epaas排期"
    variables:
      autoPresetName: "自动化测试业务模板${getDateTime()}"
      templateFileKey: ${ENV(fileKey)}
      autoTestDocUuid: ${get_a_docConfig_type()}
      commonTemplateName1: "自动化测试企业模板${get_randomNo()}"
      commonTemplateName2: "自动化测试企业模板${get_randomNo()}"
      description: "自动化测试"
      userCode: ${ENV(sign01.userCode)}
      createUserOrgCode: ${getUserMainOrg($userCode)}
      addContentName1: "自动化测试内容域名称"
      font: "SimSun"
      fontSize: "16"
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: "1"
      posX: 200
      posY: 700
      width: 216
      height: 36
      edgeScope: 0
      dataSource: ""
      sourceField: ""
      formatRule: ""


- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $templateFileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName1
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid1: content.data.templateUuid
      - newVersion1: content.data.version
      - _editUrl: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data.templateUuid", ""]
      - ne: ["content.data.editUrl", ""]
      - eq: ["content.data.version", 1]
      - eq: ["content.data.templateType", 1]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${getTplToken($_editUrl)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
#模板保存
- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $commonTemplateName1
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      creditCodeField: ${generate_field_object(内容域1, TEXT)}
      fields_draft: [$creditCodeField]
    extract:
      - _contentId1: content.data.0.contentId
      - _contentVersionId1: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: 1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $templateFileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $commonTemplateName2
      - createUserOrg: $createUserOrgCode
      - description: $description
      - docUuid: $autoTestDocUuid
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuid2: content.data.templateUuid
      - newVersion2: content.data.version
      - _editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl1)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.data.entityType","DOCUMENT"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${getTplToken($_editUrl1)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _resourceId: content.data.baseFile.resourceId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]
#模板保存
- test:
    name: "epaasTemplate-batch-save-draft--多个内容域"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($_editUrl1)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $commonTemplateName2
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      creditCodeField: ${generate_field_object(内容域1, UNIFY_THE_SOCIAL_CREDIT_CODE)}
      fields_draft: [$creditCodeField]
    extract:
      - _contentId2: content.data.0.contentId
      - _contentVersionId2: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "setup_发布"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: 1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#查询模板控件




- test:
    name: "添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "setup-获取业务模版presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
      - status: ""
    extract:
      - getPresetId: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "点击下一步按钮-成功"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $autoPresetName,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
          {
            "fileKey": $templateFileKey,
            "templateId": $newTemplateUuid1,
            "templateName": $commonTemplateName1,
            "version": 1,
            "templateType": 1,
            "contentDomainCount": 0,
            "includeSpecialContent": 0
          },{
            "fileKey": $templateFileKey,
            "templateId": $newTemplateUuid2,
            "templateName": $commonTemplateName2,
            "version": 1,
            "templateType": 1,
            "contentDomainCount": 0,
            "includeSpecialContent": 0
          }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message","成功"]
      - eq: ["content.status",200]




- test:
    name: "添加批量任务配置绑定模板内容域数据-失败"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - templateList: [
        {
          "contentList": [
          {
            "contentId": $_contentId1,
            "contentName": $addContentName1,
            "contentSource": 0
          }
          ],
          "templateName": $commonTemplateName1,
          "templateId": $newTemplateUuid1,
          "version": 1
        },
        {
          "contentList": [
          {
            "contentId": $_contentId2,
            "contentName": $addContentName1,
            "contentSource": 0
          }
          ],
          "templateName": $commonTemplateName2,
          "templateId": $newTemplateUuid2,
          "version": 1
        }
        ]
      - presetId: $getPresetId
      - status: 1
    validate:
      - contains: ["content.message","内容域的校验属性不一致，名称不能重复"]
      - eq: ["content.status",1606021]


- test:
    name: "setup_停用模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup_停用模板"
    api: api/esignDocs/template/owner/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuid2
      - version: $newVersion2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-删除模版"
    api: api/esignDocs/template/owner/templateDel.yml
    variables:
      - templateUuid: $newTemplateUuid2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]