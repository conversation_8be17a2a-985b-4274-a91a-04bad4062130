- config:
    name: "点击下一步按钮-成功_没有选择任何模板"
    variables:
      autoPresetName: "自动化测试业务模板${getDateTime()}"

- test:
    name: "添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取业务模版presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
      - status: ""
    extract:
      - getPresetId: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "点击下一步按钮-失败_没有选择任何模板_"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $autoPresetName,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelStatus": 0,
        "signatoryCount": 1,
        "fileFormat": 1,
        "checkRepetition": 1,
        "templateList": []
      }
    validate:
      - contains: ["content.message","成功"]
      - eq: ["content.status",200]
