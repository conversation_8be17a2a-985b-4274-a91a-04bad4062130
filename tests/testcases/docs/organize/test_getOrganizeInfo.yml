#组织名称参数必填项校验
#账号类型参数必填项校验
#organizeName传NULL对象
#无效的组织名称查询
#有效的组织名称查询
#有效的前后带有空格组织名称查询
#有效的前带有空格组织名称查询
#有效的后带有空格组织名称查询
#有效的中间带有空格组织名称查询
#有效的组织名称且账号类型为1内部查询
#有效的组织名称且账号类型为2外部查询
#错误的账号类型传3参数校验
#错误的账号类型传1,2参数校验
#传组织名称包含关键字的查询校验

- config:
   name: "组织信息相关"
   variables:
        headers: ${gen_main_headers()}
        innerUserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
        outerUserCode: ${ENV(wsignwb01.userCode)}

- test:
    name: "组织名称参数必填项校验"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName:
      - userType: 1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","组织名称或者组织编码不能为空" ]
      - eq: [ "content.success",false ]

- test:
    name: "账号类型参数必填项校验"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: "XX"
      - userType:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","账号类型不可为空" ]
      - eq: [ "content.success",false ]

- test:
    name: "organizeName传NULL对象"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: NULL
      - userType: 1
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","组织名称或者组织编码不能为空" ]
      - eq: [ "content.success",false ]

- test:
    name: "无效的组织名称查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: "null"
      - userType: 1
    validate:
      - eq: [ "content.data.signerList",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取内部用户信息"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $innerUserCode
    extract:
      - innerOrganizationId: content.data.organizationId
      - innerOrganizationCode: content.data.organizationCode
      - innerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取外部用户列表"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $outerUserCode
    extract:
      - outerOrganizationId: content.data.organizationId
      - outerOrganizationCode: content.data.organizationCode
      - outerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的组织名称模糊查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: $innerOrganizationName
      - userType: 1
    validate:
      - contains: [ "content.data.signerList.0.name",$innerOrganizationName ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的前后带有空格组织名称查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: ${addSpace($innerOrganizationName)}
      - userType: 1
    validate:
      - contains: [ "content.data.signerList.0.name",$innerOrganizationName]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的前带有空格组织名称查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: ${beforeAddSpace($innerOrganizationName)}
      - userType: 1
    validate:
      - contains: [ "content.data.signerList.0.name", $innerOrganizationName]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的后带有空格组织名称查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: ${afterAddSpace($innerOrganizationName)}
      - userType: 1
    validate:
      - contains: [ "content.data.signerList.0.name", $innerOrganizationName]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的中间带有空格组织名称查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: ${middleAddSpace($innerOrganizationName)}
      - userType: 1
    validate:
      - eq: [ "content.data.signerList",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的组织名称且账号类型为1内部查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: $innerOrganizationName
      - userType: 1
    validate:
      - eq: [ "content.data.signerList.0.name",$innerOrganizationName ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200]
      - eq: [ "content.success",true ]

- test:
    name: "有效的组织名称且账号类型为2外部查询"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: $outerOrganizationName
      - userType: 2
    validate:
      - contains: [ "content.data.signerList.0.name",$outerOrganizationName ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200]
      - eq: [ "content.success",true ]

- test:
    name: "错误的账号类型传3参数校验"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: "叶紫"
      - userType: 3
    validate:
      - eq: [ "content.data.signerList",null]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "错误的账号类型传1,2参数校验"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: "叶紫"
      - userType: 1,2
    validate:
      - eq: [ "content.data",null ]
      - contains: [ "content.message","userType参数错误!" ]
      - eq: [ "content.success",false ]

- test:
    name: "传组织名称包含关键字的查询校验"
    api: api/esignDocs/organize/getOrganizeInfo.yml
    variables:
      - organizeName: "%"
      - userType: 1
    validate:
      - eq: [ "content.data",null ]
#      - contains: [ "content.message","存在sql注入" ]
      - eq: [ "content.success",false ]











