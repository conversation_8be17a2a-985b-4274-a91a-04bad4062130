- config:
    name: "查询企业数据"
    variables:
      - userCode0: ${ENV(sign01.userCode)}
      - wserCode0: ${ENV(wsignwb01.userCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - userCode2: ${ENV(sign03.userCode)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - worgCode0: ${ENV(worg01.orgCode)}
      - depatCode1: ${ENV(sign03.main.departCode)}
      - sp: " "

- test:
    name: "setup-查询内部企业信息"
    api: api/esignManage/InnerOrganizations/detail.yml
    variables:
      organizationCode: $orgCode1
    extract:
      - _orgNo1: content.data.0.customOrgNo
      - _orgName1: content.data.0.name
      - _orgType1: content.data.0.organizationType
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]

- test:
    name: "setup-查询内部部门信息"
    api: api/esignManage/InnerOrganizations/detail.yml
    variables:
      organizationCode: $depatCode1
    extract:
      - _orgNo2: content.data.0.customOrgNo
      - _orgName2: content.data.0.name
      - _orgType2: content.data.0.organizationType
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]

- test:
    name: "setup-查询相对方企业信息"
    api: api/esignManage/outerOrganizations/detail.yml
    variables:
      organizationCode: $worgCode0
    extract:
      - _orgNo3: content.data.0.customOrgNo
      - _orgName3: content.data.0.name
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]

- test:
    name: "TC1-查询企业数据-正常场景-姓名右模糊匹配"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "esigntest"
          organizationTerritory: "1"
          organizationType: "1"
    extract:
      - firstOrgCode: content.data.0.organizationCode
      - firstOrgName: content.data.0.organizationName
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - contains: [content.data.0.organizationName, "esigntest"]

- test:
    name: "TC2-查询企业数据-正常场景-账号精确匹配"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "$_orgNo3"
          organizationTerritory: "2"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_eq: [content.data, 1]

- test:
    name: "TC3-查询企业数据-正常场景-账号精确匹配-部分账号查询为空"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "WORG"
          organizationTerritory: "2"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data, []]

- test:
    name: "TC4-查询企业数据-正常场景-内部部门"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "$sp$_orgNo2$sp"
          organizationTerritory: "1"
          organizationType: "2"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_eq: [content.data, 1]

- test:
    name: "TC5-查询企业数据-必填缺失-keyWord为空"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: ""
          organizationTerritory: "1"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "TC6-keyWord长度校验-查询为空"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "${generate_random_str(201)}"
          organizationTerritory: "1"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "查询企业数据-参数格式错误-organizationTerritory为字符串非法"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "esigntest"
          organizationTerritory: "abc"
          organizationType: "1"
    validate:
      - eq: [content.status, 1600015]
      - eq: [content.success, false]
      - eq: [content.message, "organizationTerritory参数错误!" ]

- test:
    name: "查询企业数据-参数格式错误-organizationTerritory=-1"
    api: api/esignDocs/organize/search.yml
    variables:
      json:
        params:
          keyWord: "esigntest"
          organizationTerritory: "-1"
          organizationType: "1"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data, [] ]

- test:
    name: "查询企业数据-无权限/未登录"
    api: api/esignDocs/organize/search.yml
    variables:
      authorization0: ""
      json:
        params:
          keyWord: "esigntest"
          organizationTerritory: "1"
          organizationType: "1"
    validate:
      - eq: [content.status, 401]
      - eq: [content.success, false]
      - eq: [content.message, "登录失效，请重新登录" ]


