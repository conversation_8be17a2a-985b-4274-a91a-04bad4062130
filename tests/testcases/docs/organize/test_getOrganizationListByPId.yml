#getOrganizationListByPId参数必填校验
#无效的outFlag参数校验
#无效的getOrganizationListByPId查询
#有效的getOrganizationListByPId根节点查询
#有效的getOrganizationListByPId根节点内部查询
#有效的getOrganizationListByPId根节点内部查询[outFlag传false]
#有效的getOrganizationListByPId根节点内部查询[outFlag传true]
#有效的getOrganizationListByPId子节点内部查询
#有效的前后带有空格的getOrganizationListByPId子节点内部查询
#有效的后带有空格的getOrganizationListByPId子节点内部查询
#有效的后带有空格的getOrganizationListByPId子节点内部查询
#有效的中间带有空格的getOrganizationListByPId子节点内部查询
#有效的getOrganizationListByPId根节点外部查询
#有效的getOrganizationListByPId子节点外部查询
#与上级组织ID不匹配的组织类型外部查询

- config:
   name: "组织信息相关"
   variables:
        headers: ${gen_main_headers()}
        innerUserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
        outerUserCode: ${ENV(wsignwb01.userCode)}

- test:
    name: "parentOrganizationId参数必填校验"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","参数parentOrganizationId必填!" ]
      - eq: [ "content.success",false ]

- test:
    name: "无效的outFlag参数校验"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory:
      - outFlag: rrr
      - parentOrganizationId:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","params,outFlag参数错误!" ]
      - eq: [ "content.success",false ]

- test:
    name: "无效的getOrganizationListByPId查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: "10003"
    validate:
      - eq: [ "content.data.organizeList",[] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId根节点查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory:
      - outFlag:
      - parentOrganizationId: "0"
    validate:
      - eq: [ "content.data.organizeList.0.parentOrganizationId","0" ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId根节点内部查询[outFlag传false]"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag: false
      - parentOrganizationId: "0"
    validate:
      - eq: [ "content.data.organizeList.0.parentOrganizationId","0" ]
      - eq: [ "content.data.organizeList.0.organizationTerritory","1" ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId根节点内部查询[outFlag传true]"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag: true
      - parentOrganizationId: "0"
    validate:
      - eq: [ "content.data.organizeList.0.parentOrganizationId","0" ]
      - eq: [ "content.data.organizeList.0.organizationTerritory","1" ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId根节点内部查询[outFlag不传]"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: "0"
    validate:
      - eq: [ "content.data.organizeList.0.parentOrganizationId","0" ]
      - eq: [ "content.data.organizeList.0.organizationTerritory","1" ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId根节点外部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 2
      - outFlag:
      - parentOrganizationId: "0"
    validate:
      - eq: [ "content.data.organizeList.0.parentOrganizationId","0" ]
      - eq: [ "content.data.organizeList.0.organizationTerritory","2" ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "获取内部用户信息"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $innerUserCode
    extract:
      - innerOrganizationId: content.data.organizationId
      - innerOrganizationCode: content.data.organizationCode
      - innerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取外部用户列表"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $outerUserCode
    extract:
      - outerOrganizationId: content.data.organizationId
      - outerOrganizationCode: content.data.organizationCode
      - outerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的getOrganizationListByPId子节点外部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 2
      - outFlag:
      - parentOrganizationId: $outerOrganizationId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的getOrganizationListByPId子节点内部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: $innerOrganizationId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的前后带有空格的getOrganizationListByPId子节点内部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: ${addSpace($innerOrganizationId)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的前带有空格的getOrganizationListByPId子节点内部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: ${beforeAddSpace($innerOrganizationId)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的后带有空格的getOrganizationListByPId子节点内部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: ${afterAddSpace($innerOrganizationId)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "有效的中间带有空格的getOrganizationListByPId子节点内部查询"
    api: api/esignDocs/organize/getOrganizationListByPId.yml
    variables:
      - organizationTerritory: 1
      - outFlag:
      - parentOrganizationId: ${middleAddSpace($innerOrganizationId)}
    validate:
      - eq: [ "content.data.organizeList",[ ] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]



