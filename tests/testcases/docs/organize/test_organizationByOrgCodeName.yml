# 无效的organizationName模糊搜索
# 有效的organizationName模糊搜索
# 有效的且前带有空格的organizationName模糊搜索
# 有效的且后带有空格的organizationName模糊搜索
# 有效的且前后带有空格的organizationName模糊搜索
# 有效的且中间带有空格的organizationName模糊搜索
# 有效的organizationName精确搜索
# 有效的且前后带有空格的organizationName精确搜索
# 有效的且中间带有空格的organizationName精确搜索
# organizationName[null]特殊字符串参数校验
# organizationName[NULL]特殊字符串参数校验
# organizationName对象NULL组织名称必填校验
# @#￥%……&*特殊字符organizationName参数校验
# 有效的organizationName组织名称或者内部状态模糊搜索
# 有效的organizationName组织名称或者外部状态模糊搜索
# 有效的organizationName组织名称或者内部状态精确搜索
# 有效的organizationName组织名称或者外部状态精确搜索
# 有效的organizationName组织名称或者错误的内部状态精确搜索

- config:
   variables:
        headers: ${gen_main_headers()}
        innerUserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
        outerUserCode: ${ENV(wsignwb01.userCode)}

- test:
    name: "获取内部用户信息"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $innerUserCode
    extract:
      - innerOrganizationId: content.data.organizationId
      - innerOrganizationCode: content.data.organizationCode
      - innerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "无效的organizationName模糊搜索(组织名称支持输入2-50个字)"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: "无效的组织名称搜索111111111111111111111111111111111111111111"
      - organizationTerritory:
    validate:
      - eq: [ "content.data",[] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

# organizationName模糊查找
- test:
    name: "有效的organizationName模糊搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: $innerOrganizationName
      - organizationTerritory:
    validate:
      - contains: [ "content.data.0.organizationName",$innerOrganizationName ]
      - eq: [ "content.data.0.children",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的且前有空格的organizationName模糊搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: ${beforeAddSpace($innerOrganizationName)}
      - organizationTerritory:
    validate:
      - contains: [ "content.data.0.organizationName",$innerOrganizationName ]
      - eq: [ "content.data.0.children",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的且后有空格的organizationName模糊搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: ${afterAddSpace($innerOrganizationName)}
      - organizationTerritory:
    validate:
      - contains: [ "content.data.0.organizationName",$innerOrganizationName ]
      - eq: [ "content.data.0.children",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的且前后带有空格的organizationName模糊搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: ${addSpace($innerOrganizationName)}
      - organizationTerritory:
    validate:
      - contains: [ "content.data.0.organizationName",$innerOrganizationName ]
      - eq: [ "content.data.0.children",null ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的且中间带有空格的organizationName模糊搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: ${middleAddSpace($innerOrganizationName)}
      - organizationTerritory:
    validate:
      - eq: [ "content.data",[ ] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "有效的organizationName精确搜索"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: $innerOrganizationName
      - organizationTerritory:
    validate:
      - eq: [ "content.data.0.organizationName",$innerOrganizationName ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

# 特殊字符串搜索(安全检查)
- test:
    name: "organizationName[null]特殊字符串参数校验"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: "null"
      - organizationTerritory:
    validate:
      - eq: [ "content.data",[ ] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "organizationName[NULL]特殊字符串参数校验"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: "NULL"
      - organizationTerritory:
    validate:
      - eq: [ "content.data",[ ] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "organizationName对象NULL组织名称必填校验"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: NULL
      - organizationTerritory:
    validate:
      - eq: [ "content.data",null]
      - eq: [ "content.message","组织名称不能为空" ]
      - eq: [ "content.status",1600017 ]

- test:
    name: "@#￥%……&*特殊字符organizationName参数校验"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: "@#￥%……&*"
      - organizationTerritory:
    validate:
      - eq: [ "content.data",null ]
#      - eq: [ "content.message","包含非法字符，存在sql注入语句" ]
      - eq: [ "content.status",917 ]

# 有效内部组织模糊情况
- test:
      name: "有效的organizationName组织名称或者内部状态模糊搜索"
      api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
      variables:
        - organizationName: $innerOrganizationName
        - organizationTerritory: 1
      validate:
        - contains: [ "content.data.0.organizationName",$innerOrganizationName ]
        - eq: [ "content.data.0.organizationTerritory","1" ]
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "获取外部用户信息"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: $outerUserCode
    extract:
      - outerOrganizationId: content.data.organizationId
      - outerOrganizationCode: content.data.organizationCode
      - outerOrganizationName: content.data.organizationName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

# 有效外部组织模糊情况
- test:
      name: "有效的organizationName组织名称或者外部状态模糊搜索"
      api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
      variables:
        - organizationName: $outerOrganizationName
        - organizationTerritory: 2
      validate:
        - contains: [ "content.data.0.organizationName",$outerOrganizationName ]
        - eq: [ "content.data.0.organizationTerritory","2" ]
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

# 有效内部组织精确情况
- test:
      name: "有效的organizationName组织名称或者内部状态精确搜索"
      api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
      variables:
        - organizationName: $innerOrganizationName
        - organizationTerritory: 1
      validate:
        - eq: [ "content.data.0.organizationName",$innerOrganizationName ]
        - eq: [ "content.data.0.children",null ]
        - eq: [ "content.data.0.organizationTerritory","1" ]
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

# 有效外部组织精确情况
- test:
      name: "有效的organizationName组织名称或者外部状态精确搜索"
      api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
      variables:
        - organizationName: $outerOrganizationName
        - organizationTerritory: 2
      validate:
        - eq: [ "content.data.0.organizationName",$outerOrganizationName ]
        - eq: [ "content.data.0.children",null ]
        - eq: [ "content.data.0.organizationTerritory","2" ]
        - eq: [ "content.message","成功" ]
        - eq: [ "content.status",200 ]

- test:
    name: "organizationName输入空字符串"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: ""
      - organizationTerritory:
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","组织名称不能为空" ]
      - eq: [ "content.success",false ]

- test:
    name: "organizationTerritory输入非法值"
    api: api/esignDocs/organize/getOrganizationListByOrgCodeName.yml
    variables:
      - organizationName: $innerOrganizationName
      - organizationTerritory: 35
    validate:
      - eq: [ "content.data",[] ]
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

