# 组织类型必填参数校验
# 组织类型为NULL参数校验
# 输入不符合规范的组织类型1,2
# 输入正确的内部组织类型1
# 输入正确的外部组织类型2
# 输入非法的组织类型3
# 输入关键字的组织类型 %
# 输入不匹配的组织类型格式
- config:
   variables:
        headers: ${gen_main_headers()}

#- test:
#    name: "查所有企业组织树"
#    api: api/esignDocs/organize/organizationTreeAll.yml
#    variables:
#      - organizationTerritory:
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true]
#
#- test:
#    name: "组织类型为NULL参数校验"
#    api: api/esignDocs/organize/organizationTreeAll.yml
#    variables:
#      - organizationTerritory: NULL
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.success",true ]

- test:
    name: "输入不符合规范的组织类型1,2"
    api: api/esignDocs/organize/organizationTreeAll.yml
    variables:
      - organizationTerritory: "1,2"
    validate:
      - eq: [ "content.data",null ]
#      - eq: [ "content.message","填写的组织类型不支持" ]
      - eq: [ "content.status","913" ]
      - eq: [ "content.success",false ]

#- test:
#    name: "输入正确的内部组织类型1"
#    api: api/esignDocs/organize/organizationTreeAll.yml
#    variables:
#      - organizationTerritory: 1
#    validate:
#      - eq: [ "content.data.0.organizationTerritory","1" ]
#      - eq: [ "content.data.0.organizationStatus","1" ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.success",true ]

#- test:
#    name: "输入正确的外部组织类型2"
#    api: api/esignDocs/organize/organizationTreeAll.yml
#    variables:
#      - organizationTerritory: 2
#    validate:
#      - eq: [ "content.data.0.organizationTerritory","2" ]
#      - eq: [ "content.data.0.organizationStatus","1" ]
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.success",true ]

- test:
    name: "输入非法的组织类型3"
    api: api/esignDocs/organize/organizationTreeAll.yml
    variables:
      - organizationTerritory: 3
    validate:
      - eq: [ "content.data",null ]
#      - eq: [ "content.message","填写的组织类型不支持" ]
      - eq: [ "content.status","913" ]
      - eq: [ "content.success",false ]

- test:
    name: "输入关键字的组织类型%"
    api: api/esignDocs/organize/organizationTreeAll.yml
    variables:
      - organizationTerritory: '%'
    validate:
      - eq: [ "content.data",null ]
#      - eq: [ "content.message","填写的组织类型不支持" ]
      - eq: [ "content.status","913" ]
      - eq: [ "content.success",false ]

- test:
    name: "输入不匹配的组织类型格式"
    api: api/esignDocs/organize/organizationTreeAll.yml
    variables:
      - organizationTerritory: "不匹配的组织类型"
    validate:
      - eq: [ "content.data",null ]
      - eq: [ "content.message","填写的组织类型不支持" ]
      - eq: [ "content.success",false ]
