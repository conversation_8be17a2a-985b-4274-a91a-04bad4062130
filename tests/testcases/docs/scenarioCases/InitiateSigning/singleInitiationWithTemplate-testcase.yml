-   config:
        name: "单份发起签署，业务模板+word模板"
        variables:
            autoPresetName: '业务模板${getDateTime()}'

-   test:
        name: "setup1-新建word模板-获取关联文件信息"
        api: api/esignDocs/docConfigure/getDocConfigureList.yml
        variables:
            docType: 2
        extract:
            autoTestDocUuid1: content.data.0.docUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.status",200]
            -   length_greater_than_or_equals: ["content.data.0.docUuid",32]


-   test:
        name: "setup2-新建word模板-上传word文件转html"
        api: api/esignDocs/template/owner/word2html.yml
        extract:
            ex_htmlFileKey: content.data.fileKey
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",True]
            -   ne: [content.data.fileKey,'']


-   test:
        name: "setup3-新建word模板-模板新建"
        api: api/esignDocs/template/owner/templateAdd.yml
        variables:
            fileKey: $ex_htmlFileKey
            docUuid: $autoTestDocUuid1
        extract:
            newTemplateUuid1: content.data.templateUuid
            newVersion1: content.data.version
            ex_templateType1: content.data.templateType
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.status",200]
            -   ne: [content.data.templateUuid,'']

-   test:
        name: "setup4-新建word模板-无签署区发布"
        api: api/esignDocs/template/owner/word_saveOrPublish.yml
        variables:
            templateUuid: $newTemplateUuid1
            version: $newVersion1
            signatories: []

        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.status",200]
            -   ne: [content.data.templateUuid,'']


-   test:
        name: "step5-新建业务模板-创建业务模板"
        api: api/esignDocs/businessPreset/create.yml
        variables:
            presetName: $autoPresetName
        extract:
            -   testPresetId: content.data
        validate:
            -   eq: [content.message, "成功"]
            -   ne: [content.data, '']
            -   eq: [content.success, true]


-   test:
        name: "step6-新建业务模板-业务模板配置，不添加签署文件，开启追加文件，不设置签署方"
        api: api/esignDocs/businessPreset/addSigners.yml
        variables:
            presetId: $testPresetId
            signerNodeList: []
        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]

-   test:
        name: "step7-发起签署-获取发起人关联的组织信息"
        api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
        variables:
            businessPresetUuid: $testPresetId
        extract:
            -   departmentCodeCommon: content.data.list.0.departmentCode
            -   departmentNameCommon: content.data.list.0.departmentName
            -   organizationCodeCommon: content.data.list.0.organizationCode
            -   organizationNameCommon: content.data.list.0.organizationName
            -   initiatorUserNameCommon: content.data.initiatorUserName
        validate:
            -   eq: ["content.message","成功"]
            -   ne: [content.data,""]
            -   eq: [content.success,True]


-   test:
        name: "step8-发起签署-上传签署pdf文件,提取fileKey"
        api: api/esignDocs/fileSystem/commonUpload.yml
        extract:
            -   ex_fileKey: content.data.fileKey
        validate:
            -   contains: [content.message, "成功"]
            -   ne: [content.data, '']
            -   eq: [content.success, true]


-   test:
        name: "step9-发起签署-获取getInfo的接口的uuid1"
        api: api/esignDocs/batchTemplateInitiation/getInfo.yml
        variables:
            params: {}
        extract:
            -   ex_uuid1: content.data.batchTemplateInitiationUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",true]
            -   eq: ["content.status",200]



-   test:
        name: "提交签署，word模板+pdf文件+无签署区+内部个人，流程发起成功"
        api: api/esignDocs/batchTemplateInitiation/submit.yml
        variables:
            businessPresetUuid: $testPresetId
            batchTemplateInitiationUuid: $ex_uuid1
            initiatorOrganizeCode: $organizationCodeCommon
            batchTemplateInitiationName: $autoPresetName
            initiatorOrganizeName: $organizationNameCommon
            initiatorUserName: $initiatorUserNameCommon
            initiatorDepartmentName: $departmentNameCommon
            initiatorDepartmentCode: $departmentCodeCommon
            appendList:
                -   appendType: 1
                    attachmentInfo:
                        fileKey: $ex_fileKey
                -   appendType: 2
                    templateInfo:
                            fileKey: $ex_htmlFileKey
                            templateId: $newTemplateUuid1
                            version: $newVersion1
                            templateType: $ex_templateType1

        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]

-   test:
        name: "setup1-新建word模板-再新建模板"
        api: api/esignDocs/template/owner/templateAdd.yml
        variables:
            fileKey: $ex_htmlFileKey
            docUuid: $autoTestDocUuid1
        extract:
            newTemplateUuid2: content.data.templateUuid
            newVersion2: content.data.version
            ex_templateType2: content.data.templateType
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.status",200]
            -   ne: [content.data.templateUuid,'']


-   test:
        name: "setup2-新建word模板-发布,添加一个签署区"
        api: api/esignDocs/template/owner/word_saveOrPublish.yml
        variables:
            templateUuid: $newTemplateUuid2
            version: $newVersion2
            signatories:
                -   addSignTime: 0
                    name: '甲方企业'
                    edgeScope:
                    thirdKey: 'ele-${getTimestamp()}'
                    keywordOrder:
                    keywordType: 0
                    signType: 1
                    "allowMove": false

        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.status",200]
            -   ne: [content.data.templateUuid,'']


-   test:
        name: "获取getInfo的接口的uuid3"
        api: api/esignDocs/batchTemplateInitiation/getInfo.yml
        variables:
            params: { }
        extract:
            -   ex_uuid3: content.data.batchTemplateInitiationUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",true]
            -   eq: ["content.status",200]

-   test:
        name: "提交签署，签署区没指定签署方，发起失败"
        api: api/esignDocs/batchTemplateInitiation/submit.yml
        variables:
            businessPresetUuid: $testPresetId
            batchTemplateInitiationUuid: $ex_uuid3
            initiatorOrganizeCode: $organizationCodeCommon
            batchTemplateInitiationName: $autoPresetName
            initiatorOrganizeName: $organizationNameCommon
            initiatorUserName: $initiatorUserNameCommon
            initiatorDepartmentName: $departmentNameCommon
            initiatorDepartmentCode: $departmentCodeCommon
            appendList:
                -   appendType: 1
                    attachmentInfo:
                        fileKey: $ex_fileKey
                -   appendType: 2
                    templateInfo:
                        fileKey: $ex_htmlFileKey
                        templateId: $newTemplateUuid2
                        version: $newVersion2
                        templateType: $ex_templateType1
        validate:
            -   contains: [content.message, "没有匹配签署区盖章"]
            -   eq: [content.status, 1606016]
            -   eq: [content.success, False]

#-   test:
#        name: "提交签署，word模板+pdf文件+指定签署区+内部个人，流程发起成功"
#        api: api/esignDocs/batchTemplateInitiation/submit.yml
#        variables:
#            businessPresetUuid: $testPresetId
#            batchTemplateInitiationUuid: $ex_uuid3
#            initiatorOrganizeCode: $organizationCodeCommon
#            batchTemplateInitiationName: $autoPresetName
#            initiatorOrganizeName: $organizationNameCommon
#            initiatorUserName: $initiatorUserNameCommon
#            initiatorDepartmentName: $departmentNameCommon
#            initiatorDepartmentCode: $departmentCodeCommon
#            appendList:
#                -   appendType: 1
#                    attachmentInfo:
#                        fileKey: $ex_fileKey
#                -   appendType: 2
#                    templateInfo:
#                        fileKey: $ex_htmlFileKey
#                        templateId: $newTemplateUuid2
#                        version: $newVersion2
#                        templateType: $ex_templateType1
##            signerList
#        validate:
#            -   contains: [content.message, "没有匹配签署区盖章"]
#            -   eq: [content.status, 200]
#            -   eq: [content.success, true]
