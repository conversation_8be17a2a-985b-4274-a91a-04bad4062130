-   config:
        name: "单份发起签署，业务模板+上传文件+无签署区"
        variables:
            autoPresetName: '业务模板${getDateTime()}'

-   test:
        name: "创建业务模板"
        api: api/esignDocs/businessPreset/create.yml
        variables:
            presetName: $autoPresetName
        extract:
            -   testPresetId: content.data
        validate:
            -   eq: [content.message, "成功"]
            -   ne: [content.data, '']
            -   eq: [content.success, true]


-   test:
        name: "业务模板配置-不添加签署文件，开启追加文件，不设置签署方"
        api: api/esignDocs/businessPreset/addSigners.yml
        variables:
            presetId: $testPresetId
            signerNodeList: []
        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]

-   test:
        name: "获取发起人关联的组织信息"
        api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
        variables:
            businessPresetUuid: $testPresetId
        extract:
            -   departmentCodeCommon: content.data.list.0.departmentCode
            -   departmentNameCommon: content.data.list.0.departmentName
            -   organizationCodeCommon: content.data.list.0.organizationCode
            -   organizationNameCommon: content.data.list.0.organizationName
            -   initiatorUserNameCommon: content.data.initiatorUserName
        validate:
            -   eq: ["content.message","成功"]
            -   ne: [content.data,""]
            -   eq: [content.success,True]

-   test:
        name: "获取getInfo的接口的uuid1"
        api: api/esignDocs/batchTemplateInitiation/getInfo.yml
        variables:
            params: {}
        extract:
            -   ex_uuid1: content.data.batchTemplateInitiationUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",true]
            -   eq: ["content.status",200]

-   test:
        name: "提交签署，未添加文件，异步流程发起失败"
        api: api/esignDocs/batchTemplateInitiation/submit.yml
        variables:
            businessPresetUuid: $testPresetId
            batchTemplateInitiationUuid: $ex_uuid1
            initiatorOrganizeCode: $organizationCodeCommon
            batchTemplateInitiationName: $autoPresetName
            initiatorOrganizeName: $organizationNameCommon
            initiatorUserName: $initiatorUserNameCommon
            initiatorDepartmentName: $departmentNameCommon
            initiatorDepartmentCode: $departmentCodeCommon
        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]


-   test:
        name: "上传签署文件,提取fileKey"
        api: api/esignDocs/fileSystem/commonUpload.yml
        extract:
            -   ex_fileKey: content.data.fileKey
        validate:
            -   contains: [content.message, "成功"]
            -   ne: [content.data, '']
            -   eq: [content.success, true]


-   test:
        name: "获取getInfo的接口的uuid2"
        api: api/esignDocs/batchTemplateInitiation/getInfo.yml
        variables:
            params: {}
        extract:
            -   ex_uuid2: content.data.batchTemplateInitiationUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",true]
            -   eq: ["content.status",200]

-   test:
        name: "提交签署，添加pdf文件，流程发起成功"
        api: api/esignDocs/batchTemplateInitiation/submit.yml
        variables:
            businessPresetUuid: $testPresetId
            batchTemplateInitiationUuid: $ex_uuid2
            initiatorOrganizeCode: $organizationCodeCommon
            batchTemplateInitiationName: $autoPresetName
            initiatorOrganizeName: $organizationNameCommon
            initiatorUserName: $initiatorUserNameCommon
            initiatorDepartmentName: $departmentNameCommon
            initiatorDepartmentCode: $departmentCodeCommon
            appendList:
                -   appendType: 1
                    attachmentInfo:
                        fileKey: ${common_upload(testppp.pdf)}
        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]


-   test:
        name: "获取getInfo的接口的uuid3"
        api: api/esignDocs/batchTemplateInitiation/getInfo.yml
        variables:
            params: {}
        extract:
            -   ex_uuid3: content.data.batchTemplateInitiationUuid
        validate:
            -   eq: ["content.message","成功"]
            -   eq: ["content.success",true]
            -   eq: ["content.status",200]

-   test:
        name: "提交签署，添加pdf文件，流程发起成功"
        api: api/esignDocs/batchTemplateInitiation/submit.yml
        variables:
            businessPresetUuid: $testPresetId
            batchTemplateInitiationUuid: $ex_uuid3
            initiatorOrganizeCode: $organizationCodeCommon
            batchTemplateInitiationName: $autoPresetName
            initiatorOrganizeName: $organizationNameCommon
            initiatorUserName: $initiatorUserNameCommon
            initiatorDepartmentName: $departmentNameCommon
            initiatorDepartmentCode: $departmentCodeCommon
            appendList:
                -   appendType: 1
                    attachmentInfo:
                        fileKey: $ex_fileKey
        validate:
            -   eq: [content.message, "成功"]
            -   eq: [content.status, 200]
            -   eq: [content.success, true]


