- config:
    name: V6.0.14.0-beta.2-历史导入文件转交
    variables:
      - name: " V6.0.14.0-beta.2-历史导入文件转交"
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign03.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - sign04: ${ENV(sign04.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - userNo0: ${ENV(sign03.accountNo)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - wsignwb01No2: ${ENV(wsignwb01.accountNo)}
      - fileKey: ${ENV(fileKey)}
      - pix: ${get_snowflake()}
      - userName_dt_1: "壹离职转交"
      - customAccountNo_dt_1: "DLLC$pix"
      - execTime0: "${getDateTime(0,1)} "
      - dingTime: ${get_dateTime(0)}

- test:
    name: openapi创建内部用户-用于离职转交电子签署流程
    variables:
      data: [
        {
          "customAccountNo": "0$customAccountNo_dt_1",
          "name": "测试导入流程转交$userName_dt_1",
          "mobile": "",
          "email": "0$customAccountNo_dt_1$<EMAIL>",
          "licenseType": "",
          "licenseNo": "",
          "bankCardNo": "",
          "mainOrganizationCode": $orgCode0,
          "mainCustomOrgNo": ,
          "otherOrganization": [
          ]
        }
      ]
    api: api/esignManage/InnerUsers/create.yml
    extract:
      _userNo_i_1: content.data.successData.0.customAccountNo
#      _userName_i_1: content.data.successData.0.userName
      _userCode_i_1: content.data.successData.0.userCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ content.data.failureCount,0 ]
      - eq: [ content.data.successCount,1 ]

- test:
    name: detail-查询内部用户-离职用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $_userCode_i_1
    extract:
      - _userName_i_1: content.data.0.name
#      - _userMobileInner0: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - eq: [ content.data.0.customAccountNo, "$_userNo_i_1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: detail-查询内部用户-sign03
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        tmp: ${checkUserToSign(1,True,True,$userNo0)}
        userCode: $userCode0
    extract:
      - userCode0: content.data.0.userCode
      - userName0: content.data.0.name
      - _userMobileInner0: content.data.0.mobile
      - _orgName1: content.data.0.mainCustomOrgName
      - _orgNo1: content.data.0.mainCustomOrgNo
      - _orgCode1: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: importSignedFile-导入记录
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "subject": "TC1-导入历史签署转交校验-$pix",
        "initiatorInfo": {
          "userName": "$userName0",
          "customAccountNo": $userNo0,
          "departmentName": $_orgName1,
          "customDepartmentNo": ""
        },
        "signFiles": [
          {
            "signedFileKey": $fileKey
          }
        ],
        "signFlowCreateTime": " 2025-01-01  09:00:00 ",
        "signFlowEndTime": " 2025-01-01  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $userNo0,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $orgNo0
          },
          {
            "userType": 2,
            "userName": "",
            "customAccountNo": $wsignwb01No2,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": ""
          }
        ]
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, null ]

- test:
    name: importSignedFile-导入记录后发起人离职
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "subject": "TC2-导入记录后发起人离职转交校验-$pix",
        "initiatorInfo": {
          "userName": "$_userName_i_1",
          "customAccountNo": $_userNo_i_1,
          "departmentName": $_orgName1,
          "customDepartmentNo": ""
        },
        "signFiles": [
          {
            "signedFileKey": $fileKey
          }
        ],
        "signFlowCreateTime": " 2025-01-01  09:00:00 ",
        "signFlowEndTime": " 2025-01-01  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "",
            "customAccountNo": $userNo0,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $orgNo0
          }
        ]
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, null ]

- test:
    name: importSignedFile-导入记录后签署人离职-不会查询到
    api: api/esignDocs/signFlow/importSignedFile.yml
    variables:
      json: {
        "subject": "TC3-导入记录后签署人离职转交发起人不影响-$pix",
        "initiatorInfo": {
          "userName": "$userName0",
          "customAccountNo": $userNo0,
          "departmentName": $_orgName1,
          "customDepartmentNo": ""
        },
        "signFiles": [
          {
            "signedFileKey": $fileKey
          }
        ],
        "signFlowCreateTime": " 2025-01-01  09:00:00 ",
        "signFlowEndTime": " 2025-01-01  09:03:00 ",
        "signType": 1,
        "signerInfos": [
          {
            "userType": 1,
            "userName": "$_userName_i_1",
            "customAccountNo": $_userNo_i_1,
            "departmentName": "",
            "customDepartmentNo": "",
            "organizationName": "",
            "customOrgNo": $orgNo0
          }
        ]
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, null ]

- test:
    name: statistics-转交统计-sign03
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId
    

- test:
    name: statistics-转交统计-sign03-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.importSignedProcessCount", 1]

- test:
    name: listTransfer-转交明细-sign03
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_1
#        dataType_list: 10
        transferType_list: 10
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 10]
      - eq: ["content.data.list.0.dataTypeName",  "已签文件-历史文件导入"]
      - ne: ["content.data.list.0.flowId",  ""]
      - contains: ["content.data.list.0.flowName",  "导入"]
      - ne: ["content.data.list.0.dataId",  ""]
      - ne: ["content.data.list.0.signerName",  ""]
      - eq: ["content.data.list.0.flowStatus",  null]
      - eq: ["content.data.list.0.initiatorTime",  "2025-01-01 09:00:00"]
      - eq: ["content.data.list.0.projectId",  "${ENV(esign.projectId)}"]
      - ne: ["content.data.list.0.projectName",  ""]
#      - ne: ["content.data.list.0.initiatorName",  ""]
      - ne: ["content.data.list.0.initiatorOrganizationName",  ""]
      - eq: ["content.data.list.0.flowStatusName",  null]

########################################################################
- test:
    name: statistics-转交统计-（离职转交）
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_i_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_2: content.data.taskId

- test:
    name: statistics-（离职转交）-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_2
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: listTransfer-转交明细
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_i_1
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_2
#        dataType_list: 10
        transferType_list: 10
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 10]
      - eq: ["content.data.list.0.dataTypeName",  "已签文件-历史文件导入"]
      - ne: ["content.data.list.0.flowId",  ""]
      - contains: ["content.data.list.0.flowName",  "导入记录"]
      - ne: ["content.data.list.0.dataId",  ""]
      - ne: ["content.data.list.0.signerName",  ""]
      - eq: ["content.data.list.0.flowStatus",  null]
      - eq: ["content.data.list.0.initiatorTime",  "2025-01-01 09:00:00"]
      - eq: ["content.data.list.0.projectId",  "${ENV(esign.projectId)}"]
      - ne: ["content.data.list.0.projectName",  ""]
#      - ne: ["content.data.list.0.initiatorName",  ""]
      - ne: ["content.data.list.0.initiatorOrganizationName",  ""]
      - eq: ["content.data.list.0.flowStatusName",  null]

- test:
    name: transfer-离职并转交-webapi-业务平台(转交全部)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $_userCode_i_1
      receiverUserCode_transfer: $userCode0
      receiverDepartmentCode_transfer: $_orgCode1
      taskId_transfer: $_taskId_2
      importSignedProcess_transfer:
        all: 1
      enterpriseSeal_transfer:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)} #转交异步处理需要等待

- test:
    name: dimissionRecord-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$_userName_i_1"
      userDimissionAccountNo: "$_userNo_i_1"
      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
      - ne: [ content.data.list.0.operator, "$_orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]
    teardown_hooks:
      - ${sleep(5)} #转交异步处理需要等待

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: $_userName_i_1
      receiverUserName_record: $userName0
      transferType_record: 10
      dataType_record: ""
      startTime_record: $execTime0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_eq: ["content.data.list", 1]