- config:
    name: V6.0.14.0-beta.2-电子签署流程转交
    variables:
      - name: "V6.0.14.0-beta.2-电子签署流程转交"
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign03.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - sign04: ${ENV(sign04.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - userNo0: ${ENV(sign03.accountNo)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - sign01: ${ENV(sign01.accountNo)}
      - fileKey: ${ENV(fileKey)}
      - pix: ${get_snowflake()}
      - userName_dt_1: "电子转交"
      - customAccountNo_dt_1: "E-LZZJ$pix"
      - execTime0: "${getDateTime(0,1)} "
      - today: ${getNowDate()}
      - outHostUrl: ${ENV(esign.projectOuterHost)}
      - inHostUrl: ${ENV(esign.projectHost)}
#      - businessTypeCodeCBBT0: "${getBusinessTypeId2(3)}" #自动化勿动3-内部企业和个人无序指定填写（发起人和签署人填写）

- test:
      name: bizTemplatesDetail-openapi查询电子签署业务模板详情
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: "${getBusinessTypeId2(3)}"
      extract:
        - signerId0: content.data.signerInfos.0.signerId
        - businessTypeCodeCBBT0: content.data.businessTypeCode
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $businessTypeCodeCBBT0 ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.signerInfos.0.userType, 1 ]  #签署方类型为相对方
          - eq: [ content.data.signerInfos.0.signMode, 1 ]  #无序
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0] #所有人

- test:
    name: openapi创建内部用户-用于离职转交电子签署流程
    variables:
      data: [
        {
          "customAccountNo": "0$customAccountNo_dt_1",
          "name": "测试电子流程转交$userName_dt_1",
          "mobile": "",
          "email": "$<EMAIL>",
          "licenseType": "",
          "licenseNo": "",
          "bankCardNo": "",
          "mainOrganizationCode": $orgCode0,
          "mainCustomOrgNo": ,
          "otherOrganization": [
          ]
        }
      ]
    api: api/esignManage/InnerUsers/create.yml
    extract:
      _userNo_e_1: content.data.successData.0.customAccountNo
      _userCode_e_1: content.data.successData.0.userCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ content.data.failureCount,0 ]
      - eq: [ content.data.successCount,1 ]

- test:
    name: detail-查询内部用户-离职用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $_userCode_e_1
    extract:
      - _userName_e_1: content.data.0.name
#      - _userMobileInner0: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - eq: [ content.data.0.customAccountNo, "$_userNo_e_1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: createByBizTemplate-电子签署流程转交-填写中流程
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $businessTypeCodeCBBT0
      - initiatorInfoCBBT:
            userCode: ""
            customAccountNo: "$_userNo_e_1"
            userType: 1
      - subject: "createByBizTemplate-电子签署流程转交-填写中流程-$pix"
      - autoFillAndSubmit: 0
      - editComponentValue: 0
      - fillingUserInfosCBBT: []
      - CCInfosCBBT: []
    extract:
      signFlowId001: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - len_eq: [content.data.fillingUserInfos, 2]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
      - len_gt: [content.data.fillingUserInfos.0.fillingUserId, 1]

- test:
    name: detail-查询内部用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $userCode0
    extract:
      - userCode0: content.data.0.userCode
      - userName0: content.data.0.name
      - _userMobileInner0: content.data.0.mobile
      - _orgName1: content.data.0.mainCustomOrgName
      - _orgNo1: content.data.0.mainCustomOrgNo
      - _orgCode1: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: createAndStart-转交电子签署流程-签署中流程
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: createAndStart-转交电子签署流程-签署中流程-$pix
      customAccountNoInitiator: $_userNo_e_1
      customOrgNoInitiator: ""
      signFiles:
        - fileKey: $fileKey
      signerInfos:
        - signNode: 1
          userCode: "$sign01"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
          tspId: "LOCAL_DEFAULT_TSP"
    extract:
      - signFlowId002: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]

- test:
    name: Create-无节点流程草稿态-应该不会出现在转交明细中
    api: api/esignSigns/signFlow/signFlowCreate.yml
    variables:
        json:
          businessTypeCode: $businessTypeCodeCBBT0
          initiatorInfo:
            userCode: $_userNo_e_1
            userType: 1
          subject: Create-无节点流程草稿态-$pix
    extract:
      - signFlowId003: content.data.signFlowId
    validate:
        - eq: ["content.code", 200]
        - eq: ["content.message", "成功"]
        - ne: ["content.data.signFlowId", null]

- test:
    name: createAndStart-转交电子签署流程-签署完成流程
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: createAndStart-转交电子签署流程-签署完成流程-$pix
      customAccountNoInitiator: $_userNo_e_1
      customOrgNoInitiator: ""
      signFiles:
        - fileKey: $fileKey
      signerInfos:
        - signNode: 1
          userCode: "$sign01"
          organizationCode: ""
          userType: 1
          signMode: 0
          autoSign: 1
          tspId: "LOCAL_DEFAULT_TSP"
          sealInfos:
            - fileKey: " $fileKey"
              signConfigs:
                - freeMode: 0
                  posY: 200
                  posX: 200
                  pageNo: 1
                  signatureType: "PERSON-SEAL"
    extract:
      - signFlowId004: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: 查询签署回调-签署中
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId002
      callbackEventType: "1"
    validate:
        - ne: ["content.callbackEventOutputs", ""]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$signFlowId002" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "1" ]
        - contains: [ "content.callbackEventOutputs.0.callBackUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.createTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.eviReportEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.failReason", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean", "" ]
        - contains: [ "content.callbackEventOutputs.0.firstRequestTime", "$today" ]
        - ge: [ "content.callbackEventOutputs.0.operationTime", 0 ]
        - eq: [ "content.callbackEventOutputs.0.physicalSealEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.preSignFileEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.projectId", "${ENV(esign.projectId)}" ]
        - eq: [ "content.callbackEventOutputs.0.requestNumber", 1 ]
        - eq: [ "content.callbackEventOutputs.0.retryFlag", "1" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackDesc", "SIGN_FLOW_START" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackEnum", 0 ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.businessNo", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelActorVO", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelCause", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelType", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.estpProcessId", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.flowStatus", 1 ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.incorrectSigner", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.newRevokeSignFlowId", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.customSignerCode", "$sign01" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.customUserCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.dynamicCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.estpNode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.estpNodeName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.fileName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.id", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousCustomSignerCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousSignerCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousUserName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.refuseDate", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.remarkSignList", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signDate", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.downloadOuterUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.downloadUrl", "http" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.fileKey", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signMode", "0" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signNode", "1" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOrder", "1" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOuterUrl", "" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOuterUrlShort", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signStatus", 0 ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signUrlShort", "http" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signerCode", "$sign01" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signerType", 2 ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.userCode", "" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.userName", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processBeginTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processEndTime", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processId", "$signFlowId002" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.refuseCause", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.refuseSigner", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.revokedreason", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signFailedFileList", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signFailedSignerList", "" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreOuterUrl", "" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreOuterUrlShort", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreUrlShort", "http" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signValidity", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signerList.0.customSignerCode", "$sign01" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.validityDaysLeft", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.ext", "" ]
        - eq: [ "content.callbackEventOutputs.0.status", "1" ]
        - eq: [ "content.callbackEventOutputs.0.transferFillingEventCallbackBean", "" ]

- test:
    name: statistics-转交统计
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_e_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId

- test:
    name: statistics-转交统计-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.electronicSignProcessCount", 1]

- test:
    name: listTransfer-转交明细-查询签署中
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: "签署中"
        flowStatus_list: "13"
        flowId_list: "$signFlowId002"
        taskId_list: $_taskId_1
        transferType_list: 11
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002_2: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 9]
      - eq: ["content.data.list.0.dataTypeName",  "电子签署"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId002"]
      - contains: ["content.data.list.0.flowName",  "签署中流程-$pix"]
      - eq: ["content.data.list.0.flowStatus",  "13"]
      - eq: ["content.data.list.0.flowStatusName",  "签署中"]

    teardown_hooks:
      - ${sleep(1)}

- test:
    name: listTransfer-转交明细-查询填写中
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_e_1
        flowName_list: ""
        flowStatus_list: "1"
        flowId_list: "$signFlowId001"
        taskId_list: $_taskId_1
        transferType_list: 11
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_000: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 9]
      - eq: ["content.data.list.0.dataTypeName",  "电子签署"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "填写中流程-$pix"]
      - eq: ["content.data.list.0.flowStatus",  "1"]
      - eq: ["content.data.list.0.flowStatusName",  "填写中"]

- test:
    name: listTransfer-转交明细-草稿态数据应该为空
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_e_1
        flowName_list: ""
        flowStatus_list: "11"
        flowId_list: "$signFlowId003"
        taskId_list: $_taskId_1
        transferType_list: 11
        pageNum_list: 1
        pageSize_list: 10
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 0]
      - eq: ["content.data.list", null]

- test:
    name: submit转交待办任务-signFlowId001
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $_userCode_e_1
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_1
      electronicSignProcess_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_000","$dataId_todo_002_2"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)}

############离职转交###############
- test:
    name: statistics-转交统计-（离职转交）
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_e_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_2: content.data.taskId

- test:
    name: statistics-（离职转交）-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_2
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.enterpriseSeal", 0]
        - eq: ["content.data.docTemplate", 0]
        - ge: ["content.data.electronicSignProcessCount", 1]
        - eq: ["content.data.autoSignProcessCount", 0]
        - eq: ["content.data.businessPreset", 0]
        - eq: ["content.data.importSignedProcessCount", 0]

- test:
    name: listTransfer-转交明细
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_e_1
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_2
        transferType_list: 11
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 9]
      - eq: ["content.data.list.0.dataTypeName",  "电子签署"]

- test:
    name: transfer-离职并转交-webapi-业务平台(转交全部)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $_userCode_e_1
      receiverUserCode_transfer: $userCode0
      receiverDepartmentCode_transfer: $_orgCode1
      taskId_transfer: $_taskId_2
      electronicSignProcess_transfer:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_002"]
      enterpriseSeal_transfer:
        all: 0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)} #转交异步处理需要等待

#################第二次转交#######################
- test:
    name: statistics-转交统计
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId

- test:
    name: statistics-转交统计-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.electronicSignProcessCount", 1]

- test:
    name: listTransfer-转交明细-查询签署中
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: "签署中"
        flowStatus_list: "13"
        flowId_list: "$signFlowId002"
        taskId_list: $_taskId_1
        transferType_list: 11
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002_2: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 9]
      - eq: ["content.data.list.0.dataTypeName",  "电子签署"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId002"]
      - contains: ["content.data.list.0.flowName",  "签署中流程-$pix"]
      - eq: ["content.data.list.0.flowStatus",  "13"]
      - eq: ["content.data.list.0.flowStatusName",  "签署中"]

    teardown_hooks:
      - ${sleep(1)}

- test:
    name: submit转交待办任务-signFlowId002
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $_userCode_e_1
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_1
      electronicSignProcess_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_002_2"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: dimissionRecord-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$_userName_e_1"
      userDimissionAccountNo: "$_userNo_e_1"
      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
      - ne: [ content.data.list.0.operator, "$_orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: $_userName_e_1
      receiverUserName_record: $userName0
      transferType_record: 11
      dataType_record: ""
      startTime_record: $execTime0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]
###################################
- test:
    name: "我管理的-正常查看电子签署详情-填写中流程"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.flowId, $signFlowId001]
      - eq: [content.data.flowStatus, "1"]
      - ne: [content.data.initiatorUserCode, $_userCode_e_1]
      - eq: [content.data.initiatorUserCode, $userCode0]
      - ne: [content.data.initiatorUserName, $_userName_e_1]
      - contains: [content.data.signedFileVOList.0.fileName, ".pdf"]
      - str_eq: [content.data.remark, "None"]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.fillingList, 1] #发起人作为填写人
      - eq: [content.data.fillingList.0.userCode, $userCode0] #接收人作为填写人

###########################电子签署列表-详情-签署详情#############
#电子签署-查看-签署详情,获取encryptedValues、authPreview
- test:
    name: 电子签署-查看-签署详情，获取encryptedValues、authPreview
    api: api/esignDocs/docFlow/manage_signPreview.yml
    variables:
      processIdPreview: "$signFlowId002"
    extract:
      - data001: content.data  #https://tianyin6-stable.tsign.cn/sign-manage-web/sign-page_preview?processId=fa6dcf1368a46e478eb5fd2acb83175d&encryptedValues=794416bb4ac2ea221aaa078839a98efd&auth=8053e2ada9ae52e16952c67c5475fd5f
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.success, true]
      - eq: [ content.data, $data001]

- test:
    name: preview-预览页面签署任务信息
    api: api/esignSigns/process/preview.yml
    variables:
      processId: "$signFlowId002"
      tmp1: "encryptedValues="
      tmp2: "&auth"
      tmp3: "auth="
      tmp4: None
      encryptedValues:  ${extract_by_markers($data001, $tmp1, $tmp2)}
      authPreview: ${extract_by_markers($data001, $tmp3, $tmp4)}
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, '成功' ]
      - eq: [ content.data.processId, "$signFlowId002" ]
      - ne: [ content.data.progressList.0.transferRecords, null ]
#      - eq: [ content.data.progressList.0.transferRecords, "" ]

#要求：对应的项目的签署内外网配置是默认选项-内部，否则地址校验会报错
- test:
    name: 查询签署回调-签署完成
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId004
      callbackEventType: "1"
    validate:
        - len_eq: ["content.callbackEventOutputs", 3]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$signFlowId004" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "1" ]
        - contains: [ "content.callbackEventOutputs.0.callBackUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.createTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.eviReportEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.failReason", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean", "" ]
        - contains: [ "content.callbackEventOutputs.0.firstRequestTime", "$today" ]
        - ge: [ "content.callbackEventOutputs.0.operationTime", 0 ]
        - eq: [ "content.callbackEventOutputs.0.physicalSealEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.preSignFileEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.projectId", "${ENV(esign.projectId)}" ]
        - eq: [ "content.callbackEventOutputs.0.requestNumber", 1 ]
        - eq: [ "content.callbackEventOutputs.0.retryFlag", "1" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackDesc", "SIGN_FLOW_START" ]
        - eq: [ "content.callbackEventOutputs.1.signFlowEventCallbackBean.callBackDesc", "SIGN_SIGNER_SIGNED" ]
        - eq: [ "content.callbackEventOutputs.2.signFlowEventCallbackBean.callBackDesc", "SIGN_FLOW_FINISH" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackEnum", 0 ]
        - eq: [ "content.callbackEventOutputs.1.signFlowEventCallbackBean.callBackEnum", 1 ]
        - eq: [ "content.callbackEventOutputs.2.signFlowEventCallbackBean.callBackEnum", 2 ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.businessNo", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelActorVO", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelCause", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.cancelType", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.estpProcessId", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.flowStatus", 1 ]
        - eq: [ "content.callbackEventOutputs.1.signFlowEventCallbackBean.callBackProcessVO.flowStatus", 1 ]
        - eq: [ "content.callbackEventOutputs.2.signFlowEventCallbackBean.callBackProcessVO.flowStatus", 2 ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.incorrectSigner", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.newRevokeSignFlowId", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.customSignerCode", "$sign01" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.customUserCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.dynamicCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.estpNode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.estpNodeName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.fileName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.id", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousCustomSignerCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousSignerCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.previousUserName", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.refuseDate", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.remarkSignList", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signDate", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.downloadOuterUrl", "$outHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.downloadUrl", "$inHostUrl" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signFileList.0.fileKey", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signMode", "0" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signNode", "1" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOrder", "1" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOuterUrl", "$outHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signOuterUrlShort", "$outHostUrl" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signStatus", 0 ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signUrl", "$inHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signUrlShort", "$inHostUrl" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signerCode", "$sign01" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.signerType", 1 ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.userCode", "" ]
        - ne: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.nextSignerList.0.userName", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processBeginTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processEndTime", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.processId", "$signFlowId004" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.refuseCause", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.refuseSigner", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.revokedreason", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signFailedFileList", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signFailedSignerList", "" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreOuterUrl", "$outHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreOuterUrlShort", "$outHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreUrl", "$inHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signPreUrlShort", "$inHostUrl" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signValidity", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.signerList.0.customSignerCode", "$sign01" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean.callBackProcessVO.validityDaysLeft", "" ]
        - eq: [ "content.callbackEventOutputs.0.transferFillingEventCallbackBean", "" ]

- test:
    name: 查询填写回调-填写回调
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId001
      callbackEventType: "4"
    validate:
        - len_eq: ["content.callbackEventOutputs", 1]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$signFlowId001" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "4" ]
        - contains: [ "content.callbackEventOutputs.0.callBackUrl", "http" ]
        - contains: [ "content.callbackEventOutputs.0.createTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.eviReportEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.failReason", "" ]
        - eq: [ "content.callbackEventOutputs.0.physicalSealEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.preSignFileEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.signFlowEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.transferFillingEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.status", "1" ]
        - ne: [ "content.callbackEventOutputs.0.firstRequestTime", "" ]
        - ne: [ "content.callbackEventOutputs.0.projectId", "" ]
        - ne: [ "content.callbackEventOutputs.0.fillingEventCallbackBean", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackDesc", "FILL_USER_TRANSFER" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackEnum", 23 ]
        - len_eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", 1 ]
        - contains: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillUrl", "$inHostUrl" ]
        - contains: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillUrlShort", "$inHostUrl" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserType", "1" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.signerType", 2 ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.userType", "1" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.customAccountNo", "$userNo0" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.customDepartmentNo", "$_orgNo1" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.customOrgNo", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.departmentCode", "$_orgCode1" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.departmentName", "$_orgName1" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.organizationCode", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.organizationName", "" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.userCode", "$userCode0" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos.0.fillingUserInfo.userName", "$userName0" ]







