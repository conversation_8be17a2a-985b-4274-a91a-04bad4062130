- config:
    name: V6.0.14.0-beta.2-本地静默签任务转交
    variables:
      - name: "V6.0.14.0-beta.2-本地静默签任务转交"
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign03.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - sign04: ${ENV(sign04.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - userNo0: ${ENV(sign03.accountNo)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - fileKey: ${ENV(fileKey)}
      - pix: ${get_snowflake()}
      - userName_dt_1: "壹离职转交"
      - customAccountNo_dt_1: "DLLC$pix"
      - execTime0: "${getDateTime(0,1)} "
      - dingTime: ${get_dateTime(0)}

- test:
    name: openapi创建内部用户-用于离职转交电子签署流程
    variables:
      data: [
        {
          "customAccountNo": "0$customAccountNo_dt_1",
          "name": "测试盖章机转交$userName_dt_1",
          "mobile": "",
          "email": "quick$<EMAIL>",
          "licenseType": "",
          "licenseNo": "",
          "bankCardNo": "",
          "mainOrganizationCode": $orgCode0,
          "mainCustomOrgNo": ,
          "otherOrganization": [
          ]
        }
      ]
    api: api/esignManage/InnerUsers/create.yml
    extract:
      _userNo_q_1: content.data.successData.0.customAccountNo
      _userCode_q_1: content.data.successData.0.userCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ content.data.failureCount,0 ]
      - eq: [ content.data.successCount,1 ]

- test:
    name: detail-查询内部用户-离职用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $_userCode_q_1
    extract:
      - _userName_q_1: content.data.0.name
      - _orgName_q_1: content.data.0.mainCustomOrgName
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - eq: [ content.data.0.customAccountNo, "$_userNo_q_1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: detail-查询内部用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        tmp: ${checkUserToSign(1,True,True,$userNo0)}
        userCode: $userCode0
    extract:
      - userCode0: content.data.0.userCode
      - userName0: content.data.0.name
      - _userMobileInner0: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: "list-查询个人章-userSealId0"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $userCode0
    extract:
      _userSealId0: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.records, 1 ]

-   test:
        name: quickSign-内部个人签署任务
        api: api/esignSigns/signFlow/quickSign.yml
        variables:
            json:
              {
                  "businessNo": "",
                  "fileKey": $fileKey,
                  "signInfo": {
                      "addSignDate": true,
                      "posBean": {
                          "keywords": "",
                          "pageNo": "1-2",
                          "posX": 50,
                          "posY": 50,
                          "width": 50
                      },
                      "sealBASE64": "",
                      "sealId": "$_userSealId0",
                      "signType": 1
                  },
                  "signIp": "",
                  "userCode":  $userCode0,
                  "userType": 1
              }
        extract:
          _signTaskId_1: content.data.signTaskId
        validate:
          - eq: [ "content.code", 200 ]
          - eq: [ "content.message", '成功' ]
          - ne: [ "content.data.signTaskId", '' ]
          - eq: [ "content.data.userCode", '$userCode0' ]
          - eq: [ "content.data.customUserNo", '$userNo0' ]
          - eq: [ "content.data.signResult", '成功' ]
          - ne: [ "content.data.signedFileKey", '' ]
          - eq: [ "content.data.businessNo", '' ]
          - eq: [ "content.data.dynamicCode", null ]

- test:
    name: TC2-查询企业印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_sealTypeCode: ""
      organizationSeals_list_organizationCode: $orgCode0
      organizationSeals_list_sealPattern: 1
    extract:
      - _sealIdOrg: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]

-   test:
        name: quickSign-内部企业签署任务
        api: api/esignSigns/signFlow/quickSign.yml
        variables:
            json:
              {
                  "businessNo": "",
                  "fileKey": $fileKey,
                  "signInfo": {
                      "addSignDate": true,
                      "posBean": {
                          "keywords": "",
                          "pageNo": "1-2",
                          "posX": 50,
                          "posY": 50,
                          "width": 50
                      },
                      "sealBASE64": "",
                      "sealId": "$_sealIdOrg",
                      "signType": 1
                  },
                  "signIp": "",
                  "userCode":  $orgCode0,
                  "userType": 2
              }
        extract:
          _signTaskId_2: content.data.signTaskId
        validate:
          - eq: [ "content.code", 200 ]
          - eq: [ "content.message", '成功' ]
          - ne: [ "content.data.signTaskId", '' ]
          - eq: [ "content.data.userCode", '$orgCode0' ]
          - ne: [ "content.data.customUserNo", '' ]
          - eq: [ "content.data.signResult", '成功' ]
          - ne: [ "content.data.signedFileKey", '' ]
          - eq: [ "content.data.businessNo", '' ]
          - eq: [ "content.data.dynamicCode", null ]


- test:
    name: statistics-转交统计-sign03
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId

- test:
    name: statistics-转交统计-sign03-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.autoSignProcessCount", 1]

- test:
    name: listTransfer-转交明细-sign03-企业盖章任务无法查询到
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: "$_signTaskId_2"
        taskId_list: $_taskId_1
        transferType_list: 12
        pageNum_list: 1
        pageSize_list: 10
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 0]
      - eq: ["content.data.list", null]

- test:
    name: listTransfer-转交明细-sign03
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_1
#        dataType_list: 10
        transferType_list: 12
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 11]
      - eq: ["content.data.list.0.dataTypeName",  "静默签署"]
      - eq: ["content.data.list.0.flowId",  "$_signTaskId_1"]
      - contains: ["content.data.list.0.flowName",  "key30page"]
      - eq: ["content.data.list.0.projectId",  "${ENV(esign.projectId)}"]
      - ne: ["content.data.list.0.projectName",  ""]
      - eq: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  ""]
      - eq: ["content.data.list.0.flowStatusName",  null]
      - ne: ["content.data.list.0.initiatorTime",  ""]

- test:
    name: submit-转交待办任务
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: $_userCode_q_1
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_1
      electronicSign_submit:
        all: 0
      autoSignProcess_submit:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(10)}

###################################
- test:
    name: "已签文件-组合查询-本地静默签署数据"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      flowId: ""
      flowName: ""
      businessNo: ""
      startTime: $execTime0
      endTime: ""
      gmtFinishBegin: ""
      gmtFinishEnd: ""
      initiatorUserName: ""
      initiatorOrganizeCode: ""
      signerUserName: $userName0
      processId: $_signTaskId_1
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 1]
      - eq: [content.data.signFileProcessVOList.0.processId, $_signTaskId_1]
      - eq: [content.data.signFileProcessVOList.0.initiatorUserName, $_userName_q_1]
      - eq: [content.data.signFileProcessVOList.0.initiatorOrganizeName, $_orgName_q_1]
      - contains: [content.data.signFileProcessVOList.0.signerUserNameList.0, $userName0]
      - eq: [content.data.signFileProcessVOList.0.sourceType, 6]

- test:
    name: teardown-删除内部用户
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      data:
        customAccountNo: $_userNo_q_1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: statistics-转交统计-离职用户
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_q_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId

- test:
    name: statistics-（离职转交）-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.autoSignProcessCount", 1]

- test:
    name: listTransfer-转交明细-离职记录转交
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_q_1
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_1
        transferType_list: 12
        pageNum_list: 1
        pageSize_list: 10
#    extract:
#      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 11]
      - eq: ["content.data.list.0.dataTypeName",  "静默签署"]
      - eq: ["content.data.list.0.flowId",  "$_signTaskId_1"]
      - contains: ["content.data.list.0.flowName",  "key30page"]
      - eq: ["content.data.list.0.projectId",  "${ENV(esign.projectId)}"]
      - ne: ["content.data.list.0.projectName",  ""]
      - eq: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  ""]
      - eq: ["content.data.list.0.flowStatusName",  null]
      - ne: ["content.data.list.0.initiatorTime",  ""]

- test:
    name: submit-转交待办任务
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $_userCode_q_1
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_1
      electronicSign_submit:
        all: 0
      autoSignProcess_submit:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(10)}

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: $userName0
      receiverUserName_record:  $_userName_q_1
      transferType_record: 12
      dataType_record: ""
      flowId_record: "$_signTaskId_1"
      startTime_record: $execTime0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]

- test:
    name: record-转交记录-离职转交的转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: $_userName_q_1
      receiverUserName_record:  $userName0
      transferType_record: 12
      dataType_record: ""
      flowId_record: "$_signTaskId_1"
      startTime_record: $execTime0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]