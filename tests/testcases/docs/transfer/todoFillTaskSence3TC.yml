- config:
    name: 场景-转交填写中的待办任务-V6.0.12.0-beta.5对于填写待办的改动 #todo
    variables:
      - name: "场景-转交填写中的待办任务-V6.0.12.0-beta.5对于填写待办的改动"
      - attachments0: []
      - CCInfosCBBT: []
      - orgCode1: ${ENV(csqs.orgCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - userNo0: "test${generate_random_str(6)}"
      - userName0: "测试用户最后删除${get_name(3)}"
      - orgName0: "esigntest新建${generate_random_str(6)}"
      - orgNo0: "DJDDD${generate_random_str(6)}"
      - orgCode2: ${ENV(sign01.main.orgCode)}
      - orgNo2: ${ENV(sign01.main.orgNo)}
      - orgName2: ${ENV(sign01.main.orgName)}
      - userCode2: ${ENV(sign01.userCode)}
  #    - userNo00: ${ENV(sign01.accountNo)}
      - csqsNo: ${ENV(csqs.accountNo)}
      - userName2: ${ENV(sign01.userName)}
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - sp: " "
      - presetName0: "自动化-发起方填写方离职转交$randomCount"
- test:
    name: setup1-创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName0
        customOrgNo: $orgNo0
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
#        legalRepUserCode: $userCode2
    extract:
      - code0: content.code
      - message0: content.message
      - orgCode0: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: setup1-创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
      data:
        - customAccountNo: $userNo0
          name: $userName0
          mobile: "${get_userMobile()}"
          email: ""
          licenseType: ID_CARD
          licenseNo: ""
          bankCardNo:
          mainOrganizationCode:
          mainCustomOrgNo: $orgNo0
          otherOrganization:  [ {"otherOrganizationCode":"$orgCode2"}]
    extract:
      - code0: content.code
      - message0: content.message
      - userCode0: content.data.successData.0.userCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "setup-业务模板配置-第一步：填写基本信息（关联pdf模板）"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "setup-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "setup-业务模板配置-第三步：签署方设置（设置签署方）,并启动-指定内部个人签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerList:
        - autoSign: 0
          assignSigner: 1
          organizeCode: ""
          userName: "$userName0"
          organizeName: ""
          departmentName: "$orgName0"
          legalSign: 0
          sealTypeCode: ""
          signerTerritory: 1
          signerType: 1
          userCode: "$userCode0"
          sealTypeName: ""
          departmentCode: "$orgCode0"
          signatoryList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "setup-管理其他签署方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $testPresetId
      - fillingList_updateFillingUsers:
          - name: "3TC-内部填写方1"
            userTerritory: 1
            key: ${get_snowflake()}
          - name: "3TC-内部填写方2"
            userTerritory: 1
            key: ${get_snowflake()}
    extract:
      signerId0: content.data.fillingList.0.signerId
      signerName0: content.data.fillingList.0.name
      signerId1: content.data.fillingList.1.signerId
      signerName1: content.data.fillingList.1.name
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.presetId, $testPresetId]

#- test:
#    name: "setup-获取业务模板配置绑定企业模板详情，并extract内容域配置相关信息"
#    api: api/esignDocs/businessPreset/templateDetail.yml
#    variables:
#      - presetId: $testPresetId
#      - templateId: $newTemplateUuid1
#      - version: $newVersion1
#    extract:
#      templateContentId0: content.data.contentList.0.templateContentId
#      templateContentName0: content.data.contentList.0.contentName
#      templateContentId1: content.data.contentList.1.templateContentId
#      templateContentName1: content.data.contentList.1.contentName
##      signerId0: content.data.contentList.0.contentSourceTypeList.2.signerList.0.signerId #其他-填写方
##      signerId1: content.data.contentList.0.contentSourceTypeList.3.signerList.0.signerId #其他-填写方
#    validate:
#      - eq: [content.message, "成功"]
#      - eq: [content.status, 200]
#      - eq: [content.data.templateId, $newTemplateUuid1]

- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,2]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_00: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields_fill10: content.data.fields.0
      - _fields_fill11: content.data.fields.1
      - _fields_sign12: content.data.fields.2
      - _fields_sign13: content.data.fields.3
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
      - _label_3: content.data.fields.2.label
      - _label_4: content.data.fields.3.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _initId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _fillId1: content.data.2.id
      - _fillId2: content.data.3.id
      - _initName0: content.data.0.name
      - _initName1: content.data.1.name
      - _fillName1: content.data.2.id
      - _fillName2: content.data.3.id
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方-都为发起方填写"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: [ { "label": "$_label_1","templateRoleId": "$_initId0" } ]
      _tmp_fill2: [ { "label": "$_label_2","templateRoleId": "$_initId0" } ]
      _tmp_sign1: [ {"label": "$_label_3","templateRoleId": "$_signerId1" }]
      _tmp_sign2: [ {"label": "$_label_4","templateRoleId": "$_signerId1" }]
      fields_draft10: "${getEpaasTemplateContentWithRoleId($_fields1,$_tmp_fill1)}"
      fields_draft11: "${getEpaasTemplateContentWithRoleId($fields_draft10,$_tmp_fill2,$_tmp_sign1)}"
      fields_draft12: "${getEpaasTemplateContentWithRoleId($fields_draft11,,$_tmp_sign2)}"
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft12,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


#- test:
#    name: "setup-业务模板配置的第4步：设置签署方填写信息"
#    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
#    variables:
#      - presetId: $testPresetId
#      - status: 1
#      - templateList:
#          - templateId: $newTemplateUuid1
#            version: $newVersion1
#            templateName: $commonTemplateName
#            contentList:
#              - contentId: $templateContentId0
#                contentName: $templateContentName0
#                contentSource:  0 #发起方填写方填写
##                signerId: $signerId0
##                signerName: $signerName0
#                contentSourceType: 0
#              - contentId: $templateContentId1
#                contentName: $templateContentName1
#                contentSource: 0 #其他填写方填写
##                signerId: $signerId1
##                signerName: $signerName1
#                contentSourceType: 0
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data",null ]

- test:
      name: setup-openapi查询电子签署业务模板详情-V6.0.12.0-beta.5新增出参其他类型的填写
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      extract: #返回是无序的，所以不建议用这里的数据，还是用业务模板第四步的接口获取signerId信息
        - fillingUserInfos000: content.data.fillingUserInfos
#        - signerId1: content.data.signerInfos.1.signerId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 0 ] #不允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]
          - len_eq: [ content.data.fillingUserInfos, 1]
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "0"]
          - eq: [ content.data.fillingUserInfos.0.fillingUserId, null]
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "0"]

- test:
    name: setup-查询内部用户的兼职
    api: api/esignSigns/esignManage/InnerUsers/detail.yml
    variables:
      userCodeDetail: $userCode0
    extract:
      - _orgCodeJZ0: content.data.0.otherOrganization.0.otherOrganizationCode
      - _orgNoJZ0: content.data.0.otherOrganization.0.otherCustomOrgNo
      - _orgNoName0: content.data.0.otherOrganization.0.otherCustomOrgName
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: TC1-业务模板发起-发起方填写
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - initiatorInfoCBBT:
            userCode: "$userCode0"
            userType: 1
      - subject: "TC1-业务模板发起-发起方填写"
      - autoFillAndSubmit: 0
      - editComponentValue: 0
      - fillingUserInfosCBBT:
          - fillingUserType: "0"
            fillingUserId: ""
            contentsControl:
              - contentName: $_label_1
                contentCode:
                contentValue: "init-001"
              - contentName: $_label_2
                contentCode:
                contentValue: "signer-001"
    extract:
      signFlowId001: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - len_eq: [content.data.fillingUserInfos, 1]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
      - len_gt: [content.data.fillingUserInfos.0.fillingUserId, 1]
      - ne: [content.data.fillingUserInfos.0.fillingUserId, $signerId0]
      - ne: [content.data.fillingUserInfos.0.fillingUserId, $signerId1]
      - eq: [content.data.fillingUserInfos.0.userType, 1]
      - eq: [content.data.fillingUserInfos.0.signerType, 1]
      - eq: [content.data.fillingUserInfos.0.fillingUserType, "0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.userCode, "$userCode0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customAccountNo, "$userNo0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.departmentCode, "$orgCode0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customDepartmentNo, "$orgNo0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.organizationCode, "$orgCode0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customOrgNo, "$orgNo0"]

- test:
    name: TC11-获取用户的待办数据-发起方填写
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId

- test:
    name: TC11-获取用户的待办数据-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: TC12-获取用户的待办任务-signFlowId001-sign01(待办体现待填写任务)
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        taskId_list: $taskId001
        transferType_list: 1
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "TC1-业务模板发起-发起方填写"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - eq: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC1-离职并转交-webapi-业务平台(转交填写待办)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $userCode0
      receiverUserCode_transfer: $userCode2
      receiverDepartmentCode_transfer: $orgCode2
      taskId_transfer: $taskId001
      electronicSign_transfer:
        all: 1
      enterpriseSeal_transfer:
        all: 0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(15)} #转交异步处理需要等待

- test:
    name: TC1-离职人员记录查询-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$userName0"
      userDimissionAccountNo: "$userNo0"
#      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
#      - ne: [ content.data.list.0.operator, "$orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]


- test:
    name: TC1-获取用户的待办数据-userCode1-转交完成确保没有转交内容
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]

- test:
    name: TC1-获取用户的待办数据-userCode1-转交完成确保没有转交内容-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.electronicSign", 0]
        - eq: ["content.data.enterpriseSeal", 0]
        - eq: ["content.data.userName", "$userName0"]


- test:
    name: "TC3-查询测试签署的待我处理任务"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: ""
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.list.0.startUserName", "$userName0"]

- test:
    name: "setup-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fillingList.0.userName, "$userName2"]
#      - eq: [content.data.initiatorUserName, "$userName0"]
      - len_eq: [content.data.signerNodeList.0.signerList, 1]
#      - len_eq: [content.data.signerNodeList.0.signerList.0.signatoryList, 2]
##删除用户
- test:
    name: "SETUP-删除用户"
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      userCode_innerUsersDelete: $userCode0
      customAccountNo_innerUsersDelete: ""
    validate:
      - eq: [ content.code, 1118015 ]
      - contains: [ content.message, "用户已离职不可删除" ]
      - eq: [ content.data, "" ]


- test:
    name: case-删除组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $orgCode0
      customOrgNo: ""
    validate:
      - eq: [ json.code, 200 ]
      - eq: [ json.message, 成功 ]
      - eq: [ json.data, "" ]