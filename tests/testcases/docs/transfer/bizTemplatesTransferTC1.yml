- config:
    name: 业务模板转交-pdf
    variables:
       presetName1: "转交业务模板-pdf1-${getDateTime()}"
       presetName2: "转交业务模板-pdf2-${getDateTime()}"
       presetName3: "转交业务模板-pdf3-${getDateTime()}"
       presetName4: "转交业务模板-pdf4-${getDateTime()}"
       userCode1: ${ENV(sign01.userCode)}
       orgCode1: ${ENV(sign01.main.orgCode)}
       jzDepartCode1: ${ENV(sign01.JZ.departCode)} #sign01兼职部门
       jzOrgCode1: ${ENV(sign01.JZ.orgCode)} #sign01兼职企业

       userCode2: ${ENV(userCodeNoSeal)}  #sign02
       orgCode2: ${ENV(userCodeNoSealOrg)} #sign02主职
       #兼职部门
       userCode3: ${ENV(signjz01.userCode)}
       orgCode3: ${ENV(sign01.JZ.orgCode)}
       jzDepartCode3: ${ENV(sign03.main.departCode)}
       jzOrgCode3: ${ENV(sign01.main.orgCode)}

       userCode4: ${ENV(csqs.userCode)}
       orgCode4: ${ENV(csqs.orgCode)}

#TC1-业务模板发起人、管理员为用户-主职，转交接收人为用户-主职
- test:
    name: "TC1-1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName1
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC1-2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName1
    extract:
      - presetId1: content.data.list.0.presetId
      - businessTypeId1: content.data.list.0.businessTypeId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName1]

- test:
    name: "TC1-3-业务模板配置-第一步：填写基本信息-文件类型为pdf-指定发起人、管理员为sign02-主职"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName1
      presetId_addDetail: $presetId1
      allowAddFile: 1
      fileFormat: 1  #模版文件选择pdf
      templateList: []
      initiatorAll: 0  #指定发起人
      initiatorList:
        - userCode: $userCode2
          organizationCode: $orgCode2
      authAll: 0  #指定管理员
      authUserList:  #管理员
        - userCode: $userCode1
          organizationCode: $orgCode1
        - userCode: $userCode2
          organizationCode: $orgCode2
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "TC1-4-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName1
      businessTypeId: $businessTypeId1
      presetId_addBusinessType: $presetId1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC1-5-业务模板配置-第三步：签署方设置（不指定签署方）,并启动"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId1
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 1
      allowAddSealer: 1
      fileFormat: 1
      signerList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#转交
- test:
    name: TC1-6-获取用户的待办数据-sign02
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode2
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId1: content.data.taskId

- test:
    name: TC1-6-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId1
    setup_hooks:
      - ${sleep(5)}
    validate:
      - eq: ["content.status", 200]
      - contains: ["content.message", "成功"]

- test:
    name: TC1-7-获取用户的业务模板身份数据-sign02
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode2
        flowName_list: ""
        flowId_list:
        taskId_list: $taskId1
        transferType_list: 7
        businessTypeId_list: "$presetId1"
        presetName_list: ""
        fileFormat_list: ""
    extract:
      - dataId_todo_1_1: content.data.list.0.dataId
      - dataId_todo_1_2: content.data.list.1.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 2]
      - eq: ["content.data.list.0.presetName", $presetName1]
      - eq: ["content.data.list.0.fileFormat", 1]
      - eq: ["content.data.list.0.businessTypeId", $presetId1]
      - eq: ["content.data.list.1.presetName", $presetName1]
      - eq: ["content.data.list.1.fileFormat", 1]
      - eq: ["content.data.list.1.businessTypeId", $presetId1]

- test:
    name: TC1-8-转交-接收人为csqs-主职-转交发起人、管理员
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode2
      receiverUserCode_submit: $userCode4
      receiverDepartmentCode_submit: $orgCode4
      taskId_submit: $taskId1
      businessPreset_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_1_1","$dataId_todo_1_2"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]


#业务模板发起人、管理员为用户-兼职，转交接收人为用户-兼职

#业务模板发起人、管理员指定用户A-兼职部门用户B兼职部门，接收人为用户B-兼职企业，发起人、管理员都会添加用户
- test:
    name: "TC2-1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName2
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName2
    extract:
      - presetId2: content.data.list.0.presetId
      - businessTypeId2: content.data.list.0.businessTypeId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName2]

- test:
    name: "TC2-3-业务模板配置-第一步：填写基本信息-文件类型为pdf，指定发起人、管理员为signjz01-兼职部门"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName2
      presetId_addDetail: $presetId2
      allowAddFile: 1
      fileFormat: 1 #模版文件选择pdf
      templateList: []
      initiatorAll: 0
      initiatorList:
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
      authAll: 0
      authUserList:  #管理员
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "TC2-4-业务模板配置-第三步：签署方设置（不设置签署方）,并启动"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId2
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 1
      allowAddSealer: 1
      fileFormat: 1
      signerList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#转交
- test:
    name: TC2-5-获取用户的待办数据-signjz01
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode3
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId2: content.data.taskId

- test:
    name: TC2-5-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId2
    setup_hooks:
      - ${sleep(5)}
    validate:
      - eq: ["content.status", 200]
      - contains: ["content.message", "成功"]

- test:
    name: TC2-6-获取用户的待办任务-signjz01
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode3
        flowName_list: ""
        flowId_list:
        taskId_list: $taskId2
        transferType_list: 7
        businessTypeId_list: "$presetId2"
        presetName_list: ""
        fileFormat_list: ""
    extract:
      - dataId_todo_2_1: content.data.list.0.dataId
      - dataId_todo_2_2: content.data.list.1.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 2]
      - eq: ["content.data.list.0.presetName", $presetName2]
      - eq: ["content.data.list.0.fileFormat", 1]
      - eq: ["content.data.list.0.businessTypeId", $presetId2]
      - eq: ["content.data.list.1.presetName", $presetName2]
      - eq: ["content.data.list.1.fileFormat", 1]
      - eq: ["content.data.list.1.businessTypeId", $presetId2]

- test:
    name: TC2-7-转交-接收人为sign01-兼职企业-转交发起人、管理员
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode3
      receiverUserCode_submit: $userCode1
      receiverDepartmentCode_submit: $jzOrgCode1
      taskId_submit: $taskId2
      businessPreset_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_2_1","$dataId_todo_2_2"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]

#业务模板发起人、管理员指定多个用户，其中指定的部分用户兼职多个组织，转交接收人为已经指定的用户-不会重复添加用户
- test:
    name: "TC3-1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName3
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC3-2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName3
    extract:
      - presetId3: content.data.list.0.presetId
      - businessTypeId3: content.data.list.0.businessTypeId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName3]

- test:
    name: "TC3-3-业务模板配置-第一步：填写基本信息-文件类型为pdf-发起人、管理员指定多个用户，其中指定的部分用户兼职多个组织，"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName3
      presetId_addDetail: $presetId3
      allowAddFile: 1
      fileFormat: 1  #模版文件选择pdf
      templateList: []
      initiatorAll: 0  #指定发起人
      initiatorList:
        - userCode: $userCode3
          organizationCode: $orgCode3
        - userCode: $userCode3
          organizationCode: $jzOrgCode3
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
      authAll: 0  #指定管理员
      authUserList:  #管理员
        - userCode: $userCode3
          organizationCode: $orgCode3
        - userCode: $userCode3
          organizationCode: $jzOrgCode3
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "TC3-4-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName3
      businessTypeId: $businessTypeId3
      presetId_addBusinessType: $presetId3
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC3-5-业务模板配置-第三步：签署方设置（不指定签署方）,并启动"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId3
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 1
      allowAddSealer: 1
      fileFormat: 1
      signerList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#转交
- test:
    name: TC3-6-获取用户的待办数据-signjz01
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode3
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId3: content.data.taskId

- test:
    name: TC3-6-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId3
    setup_hooks:
      - ${sleep(5)}
    validate:
      - eq: ["content.status", 200]
      - contains: ["content.message", "成功"]

- test:
    name: TC3-7-获取用户的业务模板身份数据-signjz01
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode3
        flowName_list: ""
        flowId_list:
        taskId_list: $taskId3
        transferType_list: 7
        businessTypeId_list: "$presetId3"
        presetName_list: ""
        fileFormat_list: ""
    extract:
      - dataId_todo_3_1: content.data.list.0.dataId
      - dataId_todo_3_2: content.data.list.1.dataId
      - dataId_todo_3_3: content.data.list.2.dataId
      - dataId_todo_3_4: content.data.list.3.dataId
      - dataId_todo_3_5: content.data.list.4.dataId
      - dataId_todo_3_6: content.data.list.5.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 6]
      - eq: ["content.data.list.0.presetName", $presetName3]
      - eq: ["content.data.list.0.fileFormat", 1]
      - eq: ["content.data.list.0.businessTypeId", $presetId3]
      - eq: ["content.data.list.1.presetName", $presetName3]
      - eq: ["content.data.list.1.fileFormat", 1]
      - eq: ["content.data.list.1.businessTypeId", $presetId3]

- test:
    name: TC3-8-转交-接收人为sign01-兼职部门-转交发起人、管理员
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode3
      receiverUserCode_submit: $userCode1
      receiverDepartmentCode_submit: $jzDepartCode1
      taskId_submit: $taskId3
      businessPreset_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_3_1","$dataId_todo_3_2","$dataId_todo_3_3","$dataId_todo_3_4","$dataId_todo_3_5","$dataId_todo_3_6"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]

#业务模板发起人、管理员指定多个用户、组织，其中指定的部分用户兼职多个组织，转交接收人为新的用户-主职
- test:
    name: "TC4-1-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName4
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC4-2-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName4
    extract:
      - presetId4: content.data.list.0.presetId
      - businessTypeId4: content.data.list.0.businessTypeId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName4]

- test:
    name: "TC4-3-业务模板配置-第一步：填写基本信息-文件类型为pdf-发起人、管理员指定多个用户，其中指定的部分用户兼职多个组织，"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName4
      presetId_addDetail: $presetId4
      allowAddFile: 1
      fileFormat: 1  #模版文件选择pdf
      templateList: []
      initiatorAll: 0  #指定发起人
      initiatorList:
        - userCode: $userCode3
          organizationCode: $orgCode3
        - userCode: $userCode3
          organizationCode: $jzOrgCode3
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
        - organizationCode: $orgCode2
          isUser: false
        - organizationCode: $orgCode4
          isUser: false
      authAll: 0  #指定管理员
      authUserList:  #管理员
        - userCode: $userCode3
          organizationCode: $orgCode3
        - userCode: $userCode3
          organizationCode: $jzOrgCode3
        - userCode: $userCode3
          organizationCode: $jzDepartCode3
        - userCode: $userCode1
          organizationCode: $jzDepartCode1
        - organizationCode: $orgCode2
          isUser: false
        - organizationCode: $orgCode4
          isUser: false
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "TC4-4-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName4
      businessTypeId: $businessTypeId4
      presetId_addBusinessType: $presetId4
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "TC4-5-业务模板配置-第三步：签署方设置（不指定签署方）,并启动"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId4
      status: 1
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 1
      allowAddSealer: 1
      fileFormat: 1
      signerList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

#转交
- test:
    name: TC4-6-获取用户的待办数据-signjz01
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode3
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId4: content.data.taskId

- test:
    name: TC4-6-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId4
    setup_hooks:
      - ${sleep(5)}
    validate:
      - eq: ["content.status", 200]
      - contains: ["content.message", "成功"]

- test:
    name: TC4-7-获取用户的业务模板身份数据-signjz01
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode3
        flowName_list: ""
        flowId_list:
        taskId_list: $taskId4
        transferType_list: 7
        businessTypeId_list: "$presetId4"
        presetName_list: ""
        fileFormat_list: ""
    extract:
      - dataId_todo_4_1: content.data.list.0.dataId
      - dataId_todo_4_2: content.data.list.1.dataId
      - dataId_todo_4_3: content.data.list.2.dataId
      - dataId_todo_4_4: content.data.list.3.dataId
      - dataId_todo_4_5: content.data.list.4.dataId
      - dataId_todo_4_6: content.data.list.5.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 6]
      - eq: ["content.data.list.0.presetName", $presetName4]
      - eq: ["content.data.list.0.fileFormat", 1]
      - eq: ["content.data.list.0.businessTypeId", $presetId4]
      - eq: ["content.data.list.1.presetName", $presetName4]
      - eq: ["content.data.list.1.fileFormat", 1]
      - eq: ["content.data.list.1.businessTypeId", $presetId4]

- test:
    name: TC4-8-转交-接收人为csqs-主职-转交发起人、管理员
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode3
      receiverUserCode_submit: $userCode4
      receiverDepartmentCode_submit: $orgCode4
      taskId_submit: $taskId4
      businessPreset_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_4_1","$dataId_todo_4_2","$dataId_todo_4_3","$dataId_todo_4_4","$dataId_todo_4_5","$dataId_todo_4_6"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]


- test:
    name: "TC1-9-查询业务模板详情-场景1"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId1
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success",true]
      - eq: ["content.data.authUserList.1.userCode",$userCode4]
      - eq: ["content.data.authUserList.1.organizationCode",$orgCode4]
      - eq: ["content.data.initiatorList.0.userCode",$userCode4]
      - eq: ["content.data.initiatorList.0.organizationCode",$orgCode4]

- test:
    name: TC1-10-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record:
      receiverUserName_record:
      transferType_record: 7
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: "$presetId1"
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 2]

- test:
    name: "TC2-8-查询业务模板详情-场景2，业务模板发起人为sign01-兼职部门,sign01-兼职企业"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId2
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success",true]
      - eq: [ "content.data.authUserList.0.userCode",$userCode1 ]
      - eq: ["content.data.authUserList.0.organizationCode",$jzOrgCode1]
      - eq: ["content.data.authUserList.1.userCode",$userCode1]
      - eq: ["content.data.authUserList.1.organizationCode",$jzDepartCode1]
      - eq: [ "content.data.initiatorList.0.userCode",$userCode1 ]
      - eq: ["content.data.initiatorList.0.organizationCode",$jzOrgCode1]
      - eq: ["content.data.initiatorList.1.userCode",$userCode1]
      - eq: ["content.data.initiatorList.1.organizationCode",$jzDepartCode1]

- test:
    name: TC2-9-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record:
      receiverUserName_record:
      transferType_record: 7
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: "$presetId2"
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 2]

- test:
    name: "TC3-8-查询业务模板详情-场景3-业务模板发起人、管理员只有sign01-兼职部门"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId3
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success",true]
      - eq: ["content.data.authUserList.0.userCode",$userCode1]
      - eq: ["content.data.authUserList.0.organizationCode",$jzDepartCode1]
      - eq: ["content.data.initiatorList.0.userCode",$userCode1]
      - eq: ["content.data.initiatorList.0.organizationCode",$jzDepartCode1]

- test:
    name: TC3-9-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record:
      receiverUserName_record:
      transferType_record: 7
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: "$presetId3"
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 6]

- test:
    name: "TC4-9-查询业务模板详情-场景4-业务模板发起人、管理员新增了接收人csqs-主职"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId4
    setup_hooks:
      - ${sleep(3)}
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.success",true]
      - eq: [ "content.data.authUserList.0.userCode",$userCode4 ] #接收人
      - eq: [ "content.data.authUserList.0.organizationCode",$orgCode4 ]
      - eq: ["content.data.authUserList.1.userCode",$userCode1]
      - eq: ["content.data.authUserList.1.organizationCode",$jzDepartCode1]
      - eq: ["content.data.authUserList.2.organizationCode",$orgCode2]
      - eq: ["content.data.authUserList.3.organizationCode",$orgCode4]
      - eq: [ "content.data.initiatorList.0.userCode",$userCode4 ] #接收人
      - eq: [ "content.data.initiatorList.0.organizationCode",$orgCode4 ]
      - eq: [ "content.data.initiatorList.1.userCode",$userCode1 ]
      - eq: [ "content.data.initiatorList.1.organizationCode",$jzDepartCode1 ]
      - eq: [ "content.data.initiatorList.2.organizationCode",$orgCode2 ]
      - eq: [ "content.data.initiatorList.3.organizationCode",$orgCode4 ]

- test:
    name: TC4-10-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record:
      receiverUserName_record:
      transferType_record: 7
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: "$presetId4"
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 6]