- config:
    name: 场景-转交签署中和待签署的待办任务
    variables:
      - name: "场景-转交签署中和待签署的待办任务"
      - attachments0: []
      - CCInfosCBBT: []
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign01.userCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - fileKey0: ${ENV(fileKey)}
      - fileKey1: ${ENV(1PageFileKey)}
      - businessTypeCode: ${ENV(businessTypeCode)}
      - userCodeInit: ${ENV(csqs.userCode)}
      - orgCodeInit: ${ENV(csqs.orgCode)}
      - presetName0: "自动化-openapi模板发起-指定双方无内容域-0808"
      - businessTypeCode_todo_001: "${getBusinessTypeId($presetName0,1,0)}"
##############调试数据##################
#      - orgCode0: "89ef451155f84be0a8a0946f968a79bd"
#      - userCode0: "test101"
#      - orgSealId0: "1811020333388279809"
#      - legalSealId1: "1811020333388279810"
#      - testPresetId1: "db766eb88f55ba1ec271ff4e741ec65a"
#      - testPresetId3: "16ca619c7440e4cd4fe1df1b7ae5117c"
#      - testBusinessTypeId1: "c6b9acf563c758fc99ea200d4ed53432"
#      - testBusinessTypeId3: "f386d130fca1e7e1a8585751ee08cf10"
##################创建内部组织和用户-组织需要有法人##############
- test:
    name: "TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序"
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序
      signFiles:
        - fileKey: $fileKey0
        - fileKey: $fileKey1
      signerInfos:
        - signNode: 1
          userCode: "$userCode0"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
          tspId: "LOCAL_DEFAULT_TSP"
        - signNode: 2
          userCode: "$userCode0"
          organizationCode: ""
          userType: 1
          signMode: 0
          tspId: "LOCAL_DEFAULT_TSP"
        - signNode: 3
          userCode: "$userCode0"
          organizationCode: "$orgCodeInit"
          userType: 1
          signMode: 0
          tspId: "LOCAL_DEFAULT_TSP"
    extract:
      - signFlowId001: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]

#######################无节点流程发起-先开启再添加签署方###########################
- test:
    name: TC2-创建无节点的签署中和待签署的流程
    api: api/esignSigns/signFlow/signFlowCreate.yml
    variables:
        json:
          businessTypeCode: $businessTypeCode
          initiatorInfo:
            userCode: $userCodeInit
            userType: 1
          subject: 'TC2-创建无节点的签署中和待签署的流程'
    extract:
      - signFlowId002: content.data.signFlowId
    validate:
        - eq: ["content.code", 200]
        - eq: ["content.message", "成功"]
        - ne: ["content.data.signFlowId", null]

- test:
    name: TC2-添加流程文件
    api: api/esignSigns/signFlow/signFlowFileAdd.yml
    variables:
        json:
          signFiles:
            - fileKey: "$fileKey0"
            - fileKey: "$fileKey1"
          signFlowId: "$signFlowId002"
    extract:
      - fileKey001: content.data.signFiles.0.fileKey
      - fileKey002: content.data.signFiles.1.fileKey
    validate:
        - eq: ["content.code", 200]
        - eq: ["content.message", "成功"]
        - eq: ["content.data.signFlowId", $signFlowId002]

- test:
    name: TC2-api开启流程
    api: api/esignSigns/signFlow/signFlowStart.yml
    variables:
        json:
          signFlowId: $signFlowId002
    validate:
        - eq: ["content.code", 200]
        - eq: ["content.message", "成功"]
        - eq: ["content.data.signFlowId", $signFlowId002]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC2-添加签署方-2个签署方顺序签署
    api: api/esignSigns/signFlow/signFlowSignersAdd.yml
    variables:
      - signersAddSignFlowId: $signFlowId002
      - signersAddSignerInfos:
          - signNode: 1
            userCode: $userCode0
            organizationCode: $orgCodeInit
            userType: 1
            signMode: 0
            tspId: "LOCAL_DEFAULT_TSP"
            sealTypeCode: "COMMON-SEAL"
            UKeyOnly: 2
            sealInfos:
              - fileKey: " $fileKey001"
                signConfigs:
                  - signatureType: "COMMON-SEAL"
                    signType: " COMMON-SIGN , EDGE-SIGN "
                    posX: 100
                    posY: 200
                    freeMode: 1
              - fileKey: " $fileKey002"
                signConfigs:
                  - signType: " COMMON-SIGN "
                    freeMode: 1
          - signNode: 2
            userCode: "$userCode0"
            organizationCode: "$orgCode0"
            userType: 1
            signMode: 0
            sealTypeCode: ""
            UKeyOnly: 2
            tspId: "LOCAL_DEFAULT_TSP"
            sealInfos:
              - fileKey: " $fileKey001"
                signConfigs:
                  - signatureType: "COMMON-SEAL"
                    signType: " COMMON-SIGN , EDGE-SIGN "
                    posX: 100
                    posY: 200
                    freeMode: 1
              - fileKey: " $fileKey002"
                signConfigs:
                  - signType: " COMMON-SIGN "
                    freeMode: 1
    extract:
      - signFlowId002: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - len_gt: [content.data.signFlowId, 1]
      - contains: [content.message, '成功']

#######################业务模板发起############################
- test:
    name: TC3-openapi业务模板发起签署中流程
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $businessTypeCode_todo_001
      - flowConfigsCBBT:
          flowConfig:
            subject: "TC3-openapi业务模板发起签署中流程"
            startMode: 0
      - fillingUserInfosCBBT: []
      - signerInfosCBBT: []
    extract:
      signFlowId003: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - eq: [content.data.signFlowStatus, "7"]
    teardown_hooks:
      - ${sleep(1)}

######################页面发起###############################
- test:
    name: "TC4-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC4-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "TC4-选择业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerNodeList001: content.data.signerNodeList
      - contentList001: content.data.contentList
      - _signerUserName0: content.data.signerNodeList.0.signerList.0.userName
      - _signerUserCode0: content.data.signerNodeList.0.signerList.0.userCode
      - _signerOrgName0: content.data.signerNodeList.0.signerList.0.organizeName
      - _signerOrgCode0: content.data.signerNodeList.0.signerList.0.organizeCode
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "staging"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName0,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "businessPresetUuid": $testPresetId,
        "saveSigners": null,
        "type": 3,
        "signersList": $signerNodeList001,
        "appendList": []
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]

- test:
    name: "getInfo-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
    extract:
      - signerNodeList001: content.data.businessPresetDetail.signerNodeList
      - templateList001: content.data.businessPresetDetail.templateList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "TC4-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName0
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      initiatorOrganizeCode: $organizationCode
      initiatorOrganizeName: "$organizationName"
      initiatorUserName: "$initiatorUserName"
      initiatorDepartmentName: "$departmentName"
      initiatorDepartmentCode: $departmentCode
      businessPresetUuid: $testPresetId
      fileFormat: 1
      signersList22: "{getInfoDealToSubmit(signerNodeList)}"
      signersList: $signerNodeList001
      appendList: []
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: submitResult-查询发起的结果-V6.0.12.0-去签署(发起人和签署人是同一个)
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "$_flowId"
#    extract:
#      - code0: content.code
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - contains: [content.data.jumpUrl, "/sign-manage-web/sign-page?processId=$_flowId"]
      - eq: [content.data.hasPermission, true]
      - eq: [content.data.message, null]
      - eq: [content.data.flowStatus, 3] #1-去填写，2-去审批，3-去签署
      - eq: [content.data.initiator.initiatorDepartmentName, "$organizationName"]
      - eq: [content.data.initiator.initiatorOrganizeName, "$organizationName"]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserName"]
      - len_eq: [content.data.signerList, 2]
      - eq: [content.data.signerList.0.signerName, "$initiatorUserName"]
      - eq: [content.data.signerList.1.signerName, "$initiatorUserName"]
      - contained_by: [content.data.signerList.0.signerOrganizationName, [$_signerOrgName0,null]]
      - eq: [content.data.fillingUserList, []]
#      - eq: [content.data.fillingUserList.0.fillingUserName, "$signerUserName_outer"]

- test:
    name: "TC4-获取当前业务模板发起成功的流程的flowId和processId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $presetName0
      flowId: ""
      startTime: ""
      endTime: ""
      flowStatus:
      initiatorUserName: ""
      page: 1
      size: 10
    extract:
      - flowId: content.data.list.0.flowId
      - signFlowId1: content.data.list.0.processId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.data.list.0.initiatorUserName",$initiatorUserName ]
#      - eq: [ "content.data.list.0.initiatorOrganizeName",$organizationName ]
      - eq: [ "content.status",200 ]

##############################################################
- test:
    name: TC5-sign01-查询待转交的任务（任务中心）
    api: api/esignSigns/portal/task/queryBulkTransferUndoTask.yml
    variables:
      authorization0: "${getPortalToken()}"
      workflowConfigNameBulk: "TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序"
      businessIdBulk: $signFlowId001
    extract:
      signFlowId_todo_001: json.data.list.0.signFlowId
      taskId_todo_001_0: json.data.list.0.id
#    setup_hooks:
#      - ${sleep(5)}
    validate:
      - eq: [ content.success,true ]
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]

- test:
    name: TC6-sign01转交任务给发起人csqs（任务中心）
    api: api/esignSigns/portal/task/bulkTransfer.yml
    variables:
      authorization0: "${getPortalToken()}"
      bulkTransferFlowIds: [{ "processId": $signFlowId_todo_001,"todoTaskId": $taskId_todo_001_0 }]
      bulkTransferSignerOrganizationCode: $orgCodeInit
      bulkTransferSignerUserCode: $userCodeInit
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - contains: [ content.message, "成功" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: TC7-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      flowId_record: $signFlowId001
      transferType_record: 1
      dataType_record: 3
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
#
#- test:
#    name: TC-sign01转交任务给发起人csqs-相同任务转交2次失败
#    api: api/esignSigns/portal/task/bulkTransfer.yml
#    variables:
#      authorization0: "${getPortalToken()}"
#      bulkTransferFlowIds: [{ "processId": $signFlowId_todo_001,"todoTaskId": $taskId_todo_001_0 }]
#      bulkTransferSignerOrganizationCode: $orgCodeInit
#      bulkTransferSignerUserCode: $userCodeInit
#    validate:
#      - eq: [ content.success, false ]
#      - eq: [ content.status, 1708881 ]
#      - contains: [ content.message, "不属于转交操作人" ]
#    teardown_hooks:
#      - ${sleep(5)}
##todo excel信息？
#
- test:
    name: TC8-sign01的后台任务信息
    api: api/esignSigns/portal/getBackendTaskList.yml
    variables:
      authorizationBackendTaskList: "${getPortalToken()}"
    extract:
      nameBackendList0: json.data.list.0.name
      fileNameBackendList0: json.data.list.0.fileName
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
#      - eq: [ json.data.list.0.allNum, 1 ] #异步的统计，有可能等待时间不足产生bug
      - contains: [ content.message, "成功" ]
      - contains: [ json.data.list.0.name, "批量转交" ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: TC9-查询内部用户-csqs
    api: api/esignSigns/esignManage/InnerUsers/detail.yml
    variables:
      userCodeDetail: $userCodeInit
    extract:
      - userCode2: content.data.0.userCode
      - userName2: content.data.0.name
      - userMobile2: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: TC10-csqs-查询待签署任务
    api: api/esignPortal/task/queryUndoTask.yml
    variables:
      authorization0: "${getVerCodeToken($userMobile2)}"
      tmp0: ${putTempEnv($userMobile2, $authorization0)}
      workflowConfigName: "TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序"
    extract:
      workflowInstanceId2: json.data.list.0.workflowInstanceId
    setup_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ content.success,true ]
      - eq: [ content.status,200 ]
      - contains: [ content.message,'成功' ]

#- test:
#    name: TC11-获取用户的待办数据-sign01(sign01这个账号的数据干不动了，先注释)
#    api: api/esignDocs/transfer/statistics.yml
#    variables:
#      userCode_statistics: $userCode0
#    validate:
#        - eq: ["content.status", 200]
#        - contains: ["content.message", "成功"]
#        - not_equals: ["content.data.taskId", ""]
#    extract:
#      taskId001: content.data.taskId
#    teardown_hooks:
#      - ${sleep(10)}

- test:
    name: TC12-获取用户的待办任务-signFlowId001-sign01(待办体现待签署任务)
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: $signFlowId001
        taskId_list: $taskId001
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 3]
      - eq: ["content.data.list.0.dataTypeName",  "签署待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - ne: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC13-获取用户的待办数据-csqs
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCodeInit
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId002: content.data.taskId

- test:
    name: TC13-获取用户的待办数据-csqs-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId002
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: TC14-获取用户的待办任务-signFlowId001-csqs
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCodeInit
        flowName_list: ""
        flowId_list: $signFlowId001
        taskId_list: $taskId002
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 3]
      - eq: ["content.data.list.0.dataTypeName",  "签署待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "TC1-一步发起sign01签署-2方同一经办人代不同企业签署-无序"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - ne: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC15-转交待办任务-signFlowId001
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCodeInit
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $taskId002
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_002"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}