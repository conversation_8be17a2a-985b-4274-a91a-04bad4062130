- config:
    name: 场景-转交填写中的待办任务
    variables:
      - name: "场景-转交填写中的待办任务"
      - attachments0: []
      - CCInfosCBBT: []
      - orgCode2: ${ENV(sign01.main.orgCode)}
      - userCode2: ${ENV(sign01.userCode)}
#      - userCode2: ${ENV(sign03.userCode)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - presetName0: "自动化-指定内部企业-$randomCount"
      - userNo0: "testFill001"
      - userName0: "测试填写一"
      - orgNo0: "CIFill001"
      - orgName0: "esigntest自动化测试填写有限公司"
      - sealGroupName0: "$orgNo0-可删"
      - notifyUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
      - today: ${getNowDate()}
      - outHostUrl: ${ENV(esign.projectOuterHost)}
      - inHostUrl: ${ENV(esign.projectHost)}
      - timestamp1: ${getTimeStamp()}

##############调试数据##################
#      - orgCode0: "89ef451155f84be0a8a0946f968a79bd"
#      - userCode0: "test101"
#      - orgSealId0: "1811020333388279809"
#      - legalSealId1: "1811020333388279810"
#      - testPresetId1: "db766eb88f55ba1ec271ff4e741ec65a"
#      - testPresetId3: "16ca619c7440e4cd4fe1df1b7ae5117c"
#      - testBusinessTypeId1: "c6b9acf563c758fc99ea200d4ed53432"
#      - testBusinessTypeId3: "f386d130fca1e7e1a8585751ee08cf10"
##################创建内部组织和用户-组织需要有法人##############
- test:
    name: setup1-创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName0
        customOrgNo: $orgNo0
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode: $userCode2
    extract:
      - code0: content.code
      - message0: content.message
      - orgCode0: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: setup1-创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
      data:
        - customAccountNo: $userNo0
          name: $userName0
          mobile: ""
          email: "<EMAIL>"
          licenseType: ID_CARD
          licenseNo: ""
          bankCardNo:
          mainOrganizationCode:
          mainCustomOrgNo: $orgNo0
          otherOrganization: [ ]
    extract:
      - userCode0: content.data.successData.0.userCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

####################新建企业印章并授权##############
- test:
    name: "setup2-创建企业印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $userNo0
        customOrgNo: $orgNo0
        sealGroupName: $sealGroupName0
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            legalSealId: ""
            sealName: "$orgNo0-模板印章A"
    extract:
      orgSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: setup2-授权用印人所有人-orgSealId0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $orgSealId0
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: "setup3-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuid1
      - version: $newVersion1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "setup3-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup3-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "setup3-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]

- test:
    name: "setup3-业务模板配置-第一步：填写基本信息（关联pdf模板）"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetName: $presetName0
      presetId_addDetail: $testPresetId
      allowAddFile: 0
      templateList:
        - fileKey: $fileKey0
          templateId: $newTemplateUuid1
          templateName: $commonTemplateName
          version: $newVersion1
          templateType: 1
          contentDomainCount: 0
          includeSpecialContent: 0
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]
      - eq: [ content.success, true ]

- test:
    name: "setup3-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $testPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]

- test:
    name: "setup3-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $testPresetId
#    extract:
#      - signatoryNameA: content.data.templateList.0.simpleVOList.0.name
#      - signatoryIdA: content.data.templateList.0.simpleVOList.0.id
#      - signatoryNameB: content.data.templateList.0.simpleVOList.1.name
#      - signatoryIdB: content.data.templateList.0.simpleVOList.1.id
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data", null]

- test:
    name: "setup3-业务模板配置-第三步：签署方设置（设置签署方）,并启动"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $testPresetId
      status: 0
      needGather: 0
      needAudit: 0
      sort: 0
      allowAddSigner: 0
      allowAddSealer: 1
      signerList:
        - autoSign: 0
          assignSigner: 1
          sealTypeCode: "COMMON-SEAL"
          signerTerritory: 1
          signerType: 2
          organizeCode: "$orgCode0"
          userName: "$userName0"
          organizeName: "$orgName0"
          departmentName: "$orgName0"
          userCode: "$userCode0"
          departmentCode: "$orgCode0"
          sealTypeName: ""
          signatoryList: []
        - autoSign: 0
          assignSigner: 1
          organizeCode: "$orgCode0"
          userName: ""
          organizeName: "$orgName0"
          departmentName: ""
          userCode: "$userCode2"
          departmentCode: "$orgCode2"
          sealTypeCode:
          signerTerritory: 1
          signerType: 2
          sealTypeName: ""
          signatoryList: []
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]



- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_00: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields_fill10: content.data.fields.0
      - _fields_fill11: content.data.fields.1
      - _fields_sign12: content.data.fields.2
      - _fields_sign13: content.data.fields.3
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
      - _label_3: content.data.fields.2.label
      - _label_4: content.data.fields.3.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _initId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerId2: content.data.2.id
      - _initName0: content.data.0.name
      - _signerName1: content.data.1.name
      - _signerName1: content.data.2.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方-都为签署方填写"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: [ { "label": "$_label_1","templateRoleId": "$_signerId1" } ]
      _tmp_fill2: [ { "label": "$_label_2","templateRoleId": "$_signerId2" } ]
      _tmp_sign1: [ {"label": "$_label_3","sealTypes": ["ORG"],"templateRoleId": "$_signerId1" }]
      _tmp_sign2: [ {"label": "$_label_4","sealTypes": ["ORG"],"templateRoleId": "$_signerId2" }]
      fields_draft10: "${getEpaasTemplateContentWithRoleId($_fields1,$_tmp_fill1)}"
      fields_draft11: "${getEpaasTemplateContentWithRoleId($fields_draft10,$_tmp_fill2,$_tmp_sign1)}"
      fields_draft12: "${getEpaasTemplateContentWithRoleId($fields_draft11,,$_tmp_sign2)}"
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft12,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $testPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

######################################发起流程#####################################
- test:
    name: TC1-业务模板发起不同经办人给同一个企业填写流程
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - flowConfigsCBBT:
          flowConfig:
            subject: "TC1-业务模板发起不同经办人给同一个企业填写流程"
            businessNo: "$randomCount"
            remark:
            signFlowExpireTime:
            chargingType: 1
            startMode: 0
          fillConfig:
            autoFillAndSubmit: 0
            editComponentValue: 1
          integrationConfig:
            fillRedirectUrl: "www.test-fill.cn"
            signRedirectUrl: "www.test-sign.cn"
            signNotifyUrl: $notifyUrl
            fillNotifyUrl: $notifyUrl
      - fillingUserInfosCBBT:
          - fillingUserType: 1
            signerId: $_signerId1
            contentsControl:
              - contentName: $_label_1
                contentCode:
                contentValue: "test123"
          - fillingUserType: 1
            signerId: $_signerId2
            contentsControl:
              - contentName: $_label_2
                contentCode:
                contentValue: "test789"
      - signerInfosCBBT: []
    extract:
      signFlowId001: content.data.signFlowId
      businessNo001: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
#    teardown_hooks:
#      - ${sleep(3)}

- test:
    name: TC2-业务模板发起2方填写中
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - flowConfigsCBBT:
          flowConfig:
            subject: "TC2-业务模板发起2方填写中"
            businessNo: ""
            remark:
            signFlowExpireTime:
            chargingType: 1
            startMode: 0
          fillConfig:
            autoFillAndSubmit: 0
            editComponentValue: 1
          integrationConfig:
            signNotifyUrl: $notifyUrl
            fillNotifyUrl: $notifyUrl
      - fillingUserInfosCBBT:
          - fillingUserType: 1
            signerId: $_signerId1
            contentsControl:
              - contentName: $_label_1
                contentCode:
                contentValue: "test123"
          - fillingUserType: 1
            signerId: $_signerId2
            contentsControl:
              - contentName: $_label_2
                contentCode:
                contentValue: "test789"
      - signerInfosCBBT: []
    extract:
      signFlowId002: content.data.signFlowId
      fillingUserInfos002: content.data.fillingUserInfos
      fillUrl002: content.data.fillingUserInfos.1.fillUrl
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token-signFlowId002"
    api: api/esignDocs/docGatherForm/querySignerFillFiles.yml
    variables:
      - templateInitiationSignersUuid: "${extractParameterValue($fillUrl002,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($fillUrl002,templateInitiationUuid)}"
      - tmp_todo_1: ${putTempEnv(_templateInitiationSignersUuid002, $templateInitiationSignersUuid)}
      - tmp_todo_2: ${putTempEnv(_templateInitiationUuid002, $templateInitiationUuid)}
    extract:
      - _fillUrl002_1: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "获取填写页信息"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl002_1,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId01: content.data.waitFillFields.0
      - contentId01: content.data.contents.0.contentId
      - documentId01: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]

- test:
    name: "填写页保存-signFlowId002"
    api: api/esignDocs/template/save-fill-data.yml
    variables:
      - tplToken_savefilldata: "${extractParameterValue($_fillUrl002_1,tplToken)}"
      - contents_savefilldata: [{"contentId":"$contentId01","documentId":"$documentId01"}]
      - fillData_savefilldata: [{"fieldId":"$fieldId01","fillValue": "填写页填充"}]
    validate:
      - eq: [content.code, 0]
      - eq: [content.code, 0]
      - eq: [content.code, 0]

- test:
    name: "signerFill-填写页-保存"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(_templateInitiationSignersUuid002)}
      hasSubmit: 0
      signerContents:
      templateInitiationUuid:  ${ENV(_templateInitiationUuid002)}
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]


- test:
    name: "填写页提交-signFlowId002"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl002_1,tplToken)}"
      - fieldId_submit: $fieldId01
      - fillValue_submit: "填写页填充"
      - contentId_submit: $contentId01
      - documentId_submit: $documentId01
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.code, 0]
      - eq: [content.success, true]

- test:
    name: "signerFill-填写页-提交"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(_templateInitiationSignersUuid002)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(_templateInitiationUuid002)}
      wordList: []
      formValues: []
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]

- test:
    name: TC3-业务模板发起1方填写-填写完成
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - flowConfigsCBBT:
          flowConfig:
            subject: "TC3-业务模板发起1方填写-填写完成"
            businessNo: ""
            remark:
            signFlowExpireTime:
            chargingType: 1
            startMode: 1
          fillConfig:
            autoFillAndSubmit: 1
            editComponentValue: 1
          integrationConfig:
            fillRedirectUrl: ""
            signRedirectUrl: ""
            signNotifyUrl: $notifyUrl
            fillNotifyUrl: $notifyUrl
      - fillingUserInfosCBBT:
          - fillingUserType: 1
            signerId: $_signerId1
            contentsControl:
              - contentName: $_label_1
                contentCode:
                contentValue: "test123"
      - signerInfosCBBT: []
    extract:
      signFlowId003: content.data.signFlowId
      fillUrl003: content.data.fillingUserInfos.0.fillUrl
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token"
    api: api/esignDocs/docGatherForm/querySignerFillFiles.yml
    variables:
      - templateInitiationSignersUuid: "${extractParameterValue($fillUrl003,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($fillUrl003,templateInitiationUuid)}"
      - tmp_todo_1: ${putTempEnv(_templateInitiationSignersUuid003, $templateInitiationSignersUuid)}
      - tmp_todo_2: ${putTempEnv(_templateInitiationUuid003, $templateInitiationUuid)}
    extract:
      - _fillUrl003_1: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "获取填写页信息"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl003_1,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId01: content.data.waitFillFields.0
      - contentId01: content.data.contents.0.contentId
      - documentId01: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]


- test:
    name: "填写页提交"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl003_1,tplToken)}"
      - fieldId_submit: $fieldId01
      - fillValue_submit: "填写页填充"
      - contentId_submit: $contentId01
      - documentId_submit: $documentId01
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.code, 0]
      - eq: [content.success, true]

- test:
    name: "signerFill-填写页-提交"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(_templateInitiationSignersUuid003)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(_templateInitiationUuid003)}
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]


############userCode0用户的填写待办只会有一条#############
- test:
    name: TC4-获取用户的待办数据-新用户
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId

- test:
    name: TC4-获取用户的待办数据-新用户-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.electronicSign", 2]
        - eq: ["content.data.enterpriseSeal", 2]
        - eq: ["content.data.docTemplate", 0]
        - eq: ["content.data.electronicSignProcessCount", 0]
        - eq: ["content.data.importSignedProcessCount", 0]
        - eq: ["content.data.autoSignProcessCount", 0]
        - eq: ["content.data.businessPreset", 0]

- test:
    name: TC6-获取用户的待办任务-signFlowId001-userCode0
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: $signFlowId001
        taskId_list: $taskId001
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "TC1-业务模板发起不同经办人给同一个企业填写流程"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - ne: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC7-获取用户的待办任务-signFlowId002-userCode0
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        flowName_list: ""
        flowId_list: $signFlowId002
        taskId_list: $taskId001
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId002"]
      - contains: ["content.data.list.0.flowName",  "TC2"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - ne: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC8-转交-转交人和任务不匹配-excel体现：任务{ID}不属于转交人
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode1
      receiverUserCode_submit: $userCode0
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_001"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]

- test:
    name: TC5-获取用户的待办数据-sign04
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: ${ENV(sign04.userCode)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId004: content.data.taskId

- test:
    name: TC5-获取用户的待办数据-sign04-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId004
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: TC9-转交-taskId和dataId不匹配-报错
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: ${ENV(sign04.userCode)}
      receiverUserCode_submit: $userCode0
      taskId_submit: $taskId004
      electronicSign_submit:
        all: 2  #0-全部不选 1-all  2-部分选择
        excludeIdList: [ ]
        includeIdList: ["$dataId_todo_001"]
    validate:
      - contained_by: ["content.status", [1640002,200]]
      - contained_by: ["content.message", ["您提交的转交内容信息已变更，请重新选择","成功"]]
      - eq: [ "content.data", null ]

- test:
    name: TC10-转交-转交人已离职
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: ${ENV(userCode.dimission)}
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - contained_by: ["content.status", [200,1111003]]
      - contained_by: ["content.message", ["用户已不存在","成功"]]
      - eq: [ "content.data", null ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC11-转交-接收人不存在
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: ${ENV(userCode.dimission)}
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - contained_by: ["content.status", [200,1111003]]
      - contained_by: ["content.message", ["用户已不存在","成功"]]
      - eq: [ "content.data", null ]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC12-转交-接收人和转交人相同
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - eq: ["content.status", 1640001]
      - eq: ["content.message", "转交人和接收人不能相同"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC13-转交-接收人和其他签署方相同-提交成功-后台任务失败,excel体现失败原因-接收人在流程中已有同签署主体的签署任务
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: $userCode2
      receiverDepartmentCode_submit: $orgCode2
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC14-提交待办任务-signFlowId001-转交填写中的任务成功
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: $userCode1
      receiverDepartmentCode_submit: $orgCode1
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_002"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC15-提交待办任务-signFlowId001-转交填写中的任务成功-二次提交转交任务
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode0
      receiverUserCode_submit: $userCode1
      receiverDepartmentCode_submit: $orgCode1
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: [ ]
        includeIdList: [ "$dataId_todo_002" ]
    validate:
      - eq: [ "content.status", 200 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data", null ]
    teardown_hooks:
      - ${sleep(20)}

- test:
    name: 查询签署回调-填写中-保存不提交
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId002
      callbackEventType: "4"
    validate:
        - ne: ["content.callbackEventOutputs", ""]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$signFlowId002" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "4" ]
        - eq: [ "content.callbackEventOutputs.0.callBackUrl", "$notifyUrl" ]
        - contains: [ "content.callbackEventOutputs.0.createTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackDesc", "FILL_USER_FILLED" ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.callBackDesc", "FILL_USER_TRANSFER" ]
        - eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.callBackDesc", "FILL_USER_SAVE" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackEnum", 21 ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.callBackEnum", 23 ]
        - eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.callBackEnum", 24 ]
        - len_eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.filledUserInfos", 2 ]
        - len_eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", 1 ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.signFlowStatus", 7 ]
        - ne: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.signFlowId", "" ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.filledUserInfos", "" ]
        - len_eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", 1 ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFlowStatus", 7 ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFlowId", "" ]
        - len_eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.docCallBackProcessVo.filledUserInfos", 2 ]
        - len_eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", 2 ]
        - eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.docCallBackProcessVo.signFlowStatus", 7 ]
        - ne: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.docCallBackProcessVo.signFlowId", "" ]
        - eq: [ "content.callbackEventOutputs.2.fillingEventCallbackBean.docCallBackProcessVo.signFiles", "" ]

- test:
    name: 查询签署回调-填写完成
    api: api/esignManage/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: $signFlowId003
      callbackEventType: "4"
    validate:
        - ne: ["content.callbackEventOutputs", ""]
        - eq: [ "content.callbackEventOutputs.0.businessId", "$signFlowId003" ]
        - eq: [ "content.callbackEventOutputs.0.callBackType", "4" ]
        - eq: [ "content.callbackEventOutputs.0.callBackUrl", "$notifyUrl" ]
        - contains: [ "content.callbackEventOutputs.0.createTime", "$today" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackDesc", "FILL_USER_FILLED" ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.callBackDesc", "FILL_STEP_FINISH" ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.callBackEnum", 21 ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.callBackEnum", 22 ]
        - len_eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.filledUserInfos", 2 ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", [] ]
        - eq: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.signFlowStatus", 7 ]
        - ne: [ "content.callbackEventOutputs.0.fillingEventCallbackBean.docCallBackProcessVo.signFlowId", "" ]
        - len_eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.filledUserInfos", 2 ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.nextfillingUserInfos", [] ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFlowStatus", 8 ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFlowId", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.customAccountNo", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.customDepartmentNo", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.customOrgNo", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.departmentCode", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.departmentName", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.organizationCode", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.organizationName", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.userCode", "" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.userName", "" ]
        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.initiatorInfo.userType", 1 ]
        - len_eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFiles", 1 ]
        - contains: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFiles.0.downloadOuterUrl", "$outHostUrl" ]
        - contains: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFiles.0.downloadUrl", "$inHostUrl" ]
        - ne: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signFiles.0.fileKey", "$fileKey0" ]
#        - len_eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos", 2 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.signMode", 1 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.signNode", 1 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.signOrder", 1 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.ukeyOnly", 2 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.userType", 1 ]
#        - eq: [ "content.callbackEventOutputs.1.fillingEventCallbackBean.docCallBackProcessVo.signerInfos.0.batchSignDefault", 1 ]

#- test:
#    name: TC20-提交待办任务-signFlowId003-转交填写完成
#    api: api/esignDocs/transfer/submit.yml
#    variables:
#      transferUserCode_submit: $userCode2
#      receiverUserCode_submit: $userCode1
#      receiverDepartmentCode_submit: $orgCode1
#      taskId_submit: $taskId003
#      electronicSign_submit:
#        all: 2
#        excludeIdList: []
#        includeIdList: ["$dataId_todo_003"]
#    validate:
#      - eq: ["content.status", 200]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data", null]
#    teardown_hooks:
#      - ${sleep(1)}

- test:
    name: teardown-删除内部用户
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      data:
        customAccountNo: $userNo0
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks:
      - ${sleep(1)}

####异步建议在单场景校验,全量运行脚本的时候多线程异步消息慢导致未能消费校验无法通过###############
#- test:
#    name: "TC11-页面业务模板A详情-查看业务模板信息-删除经办人影响"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      getPresetId: $testPresetId1
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.data.presetId, $testPresetId1]
#      - eq: [content.data.status, 0]
#      - contains: [content.data.changeReason,  "印章授权信息已变更" ]
##      - contains: [content.data.changeReason,  "用章要求已变更" ]
#      - contains: [content.data.changeReason,  "签署方【$userName0】信息已修改" ]
#      - eq: [content.data.presetName, $presetName1]
#      - ne: [content.data.signBusinessType.businessTypeId, null]
#      - eq: [content.data.signerNodeList.0.signerList.0.legalSign, 0]

- test:
    name: teardown-删除内部组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      data:
        customOrgNo: $orgNo0
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]
    teardown_hooks:
      - ${sleep(1)}