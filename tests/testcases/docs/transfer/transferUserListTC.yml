- config:
    name: 转交的时候查询接收人
    variables:
      userCode0: ${ENV(sign01.userCode)}
      userName0: ${ENV(sign01.userName)}
      orgCode1: ${ENV(sign01.main.orgCode)}

- test:
    name: TC1-模糊查询
    api: api/esignDocs/user/transferUserList.yml
    variables:
      - userName_tul: "测试签署"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - len_gt: [ content.data, 1 ]
      - ne: [ content.data.0.userCode, "" ]
      - contains: [ content.data.0.userName, "测试签署" ]
      - ne: [ content.data.0.account, "" ]
      - ne: [ content.data.0.departmentCode, "" ]
      - ne: [ content.data.0.departmentName, "" ]

- test:
    name: TC2-查询为空
    api: api/esignDocs/user/transferUserList.yml
    variables:
      - userName_tul: "签XXXXX署"
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data, [] ]