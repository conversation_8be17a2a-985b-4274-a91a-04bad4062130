- config:
    name: 场景-转交填写中的待办任务-V6.0.12.0-beta.5对于填写待办的改动 #todo
    #todo:https://forward.esign.cn/bugManagement/edit?id=38346&type=check
    #todo:https://forward.esign.cn/bugManagement/edit?id=38336&type=check
    variables:
      - name: "场景-转交填写中的待办任务-V6.0.12.0-beta.5对于填写待办的改动"
      - attachments0: []
      - CCInfosCBBT: []
      - fileKey: ${ENV(fileKey)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - userNo0: "test${generate_random_str(6)}"
      - userName0: "测试用户最后删除${get_name(3)}"
      - orgName0: "esigntest新建${generate_random_str(6)}"
      - orgNo0: "DJJJDJDJJDDD${generate_random_str(6)}"
      - orgCode2: ${ENV(sign01.main.orgCode)}
      - orgNo2: ${ENV(sign01.main.orgNo)}
      - orgName2: ${ENV(sign01.main.orgName)}
      - userCode2: ${ENV(sign01.userCode)}
      - description: "全链路自动化测试描述"
  #    - userNo00: ${ENV(sign01.accountNo)}
      - csqsNo: ${ENV(csqs.accountNo)}
      - userName2: ${ENV(sign01.userName)}
      - newTemplateUuidCommon1: ${getTemplateId(2,2,pdf,0)}
      - _templateName: "自动化测转交1-${get_randomNo_16()}"
      - autoTestDocUuid1Common: ${get_a_docConfig_type()}
      - newVersionCommon1: 1
      - randomCount: ${getDateTime()}
      - sp: " "
      - presetName0: "自动化-其他填写方离职转交$randomCount"
      - "fillingList1": [
        {
          "name": "内部填写方",
          "userTerritory": "1",
          "key": "${get_snowflake()}"
        }
      ]
- test:
    name: setup1-创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName0
        customOrgNo: $orgNo0
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
#        legalRepUserCode: $userCode2
    extract:
      - code0: content.code
      - message0: content.message
      - orgCode0: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: setup1-创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
      data:
        - customAccountNo: $userNo0
          name: $userName0
          mobile: "${get_userMobile()}"
          email: ""
          licenseType: ID_CARD
          licenseNo: ""
          bankCardNo:
          mainOrganizationCode:
          mainCustomOrgNo: $orgNo0
          otherOrganization:  [ {"otherOrganizationCode":"$orgCode2"}]
    extract:
      - userCode0: content.data.successData.0.userCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "模版新建1"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $_templateName
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon1: content.data.templateUuid
      - newVersionCommon1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_006: ${putTempEnv(_tmp_common_template_006, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_006)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_006)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      bizId: "${get_randomStr_32()}"
      bizId2: "${get_randomStr_32()}"
      fields_draft: [
        {
          "fieldId": "",
          "label": "单行文本1",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 196.13132250580045,
            "y": 770.9980665119876,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
        {
          "fieldId": "",
          "label": "单行文本2",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId2",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 404.1563676205207,
            "y": 691.3101250322247,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }
            ]
#      fields_draft: ${epaasFillContent(2,null,控件1,1)}
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板1"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取文档模板详情"
    api: api/esignDocs/template/manage/templateInfo.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
    extract:
      - commonTemplateName: content.data.templateName
      - fileKey0: content.data.fileKey
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data.fileType","pdf"]
      - eq: ["content.data.status", "PUBLISH"]
      - eq: ["content.data.status", "PUBLISH"]

- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetName0
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - getPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $presetName0]

- test:
    name: "setup-查看初始状态新建的业务模板详情，并获取businessTypeId"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $getPresetId
    extract:
      - testBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $getPresetId]
      - eq: [content.data.presetName, $presetName0]
      - length_equals: [content.data.signBusinessType.businessTypeId, 32]
      - len_gt: [content.data.signBusinessType.messageConfigList, 1]


- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $presetName0,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuidCommon1,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]


- test:
    name: "setup-业务模板配置-第二步：签署方式（默认配置）"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      businessTypeName: $presetName0
      businessTypeId: $testBusinessTypeId
      presetId_addBusinessType: $getPresetId
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]


- test:
    name: "添加签署方"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "signerList1": [
        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "id": "add-1",
          "signatoryList": [ ],
          "signerTerritory": 1,
          "signerType": 1,
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "f8e6ed41-cc5b-4fd5-b6bd-f8bcf813fdfa"
        }
      ]
      "params": {
        "presetId": $getPresetId,
        "status": 0,
        "allowAddSigner": 0,
        "needGather": 0,
        "needAudit": 0,
        "sort": 0,
        "contentDomainCount": 5,
        "editComponentValue": 1,
        "fillingList": $fillingList1,
        "signerNodeList": [ {
          "signerList": $signerList1,
          "signMode": 1,
          "signNode": 1,
          "id": "node-0"
        } ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $getPresetId
      - fillingList_updateFillingUsers: $fillingList1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.fillingList.0.name, "内部填写方" ]

- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $getPresetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,1]
      - len_eq: [content.data.signerNodeList,1]
      - len_eq: [content.data.templateList,1]
    extract:
      - _editUrl_00: content.data.editUrl


- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail--模板1"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
      - _label_2: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _fillId1: content.data.2.id
      - _signerName0: content.data.0.name
#      - _signerName1: content.data.1.name
#      - _signerName2: content.data.2.name
#      - _signerName3: content.data.3.name
#      - _signerName4: content.data.4.name
#      - _signerName5: content.data.5.name
#      - _fillName1: content.data.5.id
#      - _fillName2: content.data.5.id
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: [ { "label": "$_label_1","templateRoleId": "$_fillId1" } ]
      _tmp_fill2: [{ "label": "$_label_2","templateRoleId": "$_signerId1" }]
      fields_draft1: "${getEpaasTemplateContentWithRoleId($_fields1,$_tmp_fill2)}"
      fields_draft10: "${getEpaasTemplateContentWithRoleId($fields_draft1,$_tmp_fill1)}"
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft10,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
      name: setup-openapi查询电子签署业务模板详情-V6.0.12.0-beta.5新增出参其他类型的填写
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $testBusinessTypeId
      extract: #返回是无序的，所以不建议用这里的数据，还是用业务模板第四步的接口获取signerId信息
        - fillingUserInfos000: content.data.fillingUserInfos
        - signerId1: content.data.signerInfos.0.signerId
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $testBusinessTypeId ]
          - eq: [ content.data.fileFormat, "PDF" ] #格式需要是pdf
          - eq: [ content.data.bizTemplateConfig.allowInitiatorSetSigners, 0 ]  #不允许添加签署方
          - eq: [ content.data.bizTemplateConfig.allowInitiatorAddSignFiles, 1 ] #允许添加文件
          - eq: [ content.data.initiatorInfo.initiatorRangeType, 0]
          - len_eq: [ content.data.fillingUserInfos, 2]
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "2"]
#          - eq: [ "${dataArraySort($fillingUserInfos000, fillingUserName,0, fillingUserName)}", "$signerName0"]
          - len_gt: [ content.data.fillingUserInfos.0.fillingUserId, 1]
          - eq: [ content.data.fillingUserInfos.0.fillingUserType, "2"]

- test:
    name: setup-查询内部用户的兼职
    api: api/esignSigns/esignManage/InnerUsers/detail.yml
    variables:
      userCodeDetail: $userCode0
    extract:
      - _orgCodeJZ0: content.data.0.otherOrganization.0.otherOrganizationCode
      - _orgNoJZ0: content.data.0.otherOrganization.0.otherCustomOrgNo
      - _orgNoName0: content.data.0.otherOrganization.0.otherCustomOrgName
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: TC1-业务模板发起-其他填写方填写-2个内部填写是同一个人
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - businessTypeCodeCBBT: $testBusinessTypeId
      - subject: "TC1-业务模板发起-其他填写方填写-2个内部填写是同一个人"
      - autoFillAndSubmit: 0
      - editComponentValue: 0
      - signerInfosCBBT:
          - signerId: $_signerId1
            signerAccountInfo:
              userCode: "$sp$userCode0$sp"
              customDepartmentNo: "$_orgNoJZ0"
      - fillingUserInfosCBBT:
          - fillingUserType: "2"
            fillingUserId: $_fillId1
            fillingUserInfo:
              userCode: $sp$userCode0$sp
              departmentCode: null
            contentsControl:
              - contentName: $_label_1
                contentCode:
                contentValue: "init-001"
    extract:
      signFlowId001: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data.signFlowId, null]
      - len_eq: [content.data.fillingUserInfos, 2]
      - ne: [content.data.fillingUserInfos.0.signerId, null]
      - len_gt: [content.data.fillingUserInfos.0.fillingUserId, 1]
      - ne: [content.data.fillingUserInfos.0.fillingUserId, $signerId0]
      - ne: [content.data.fillingUserInfos.0.fillingUserId, $signerId1]
      - eq: [content.data.fillingUserInfos.0.userType, 1]
#      - eq: [content.data.fillingUserInfos.0.signerType, 2]
#      - eq: [content.data.fillingUserInfos.0.fillingUserType, "1"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.userCode, "$userCode0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customAccountNo, "$userNo0"]
#      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.departmentCode, "$orgCode0"]
#      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customDepartmentNo, "$orgNo0"]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.organizationCode, null]
      - eq: [content.data.fillingUserInfos.0.fillingUserInfo.customOrgNo, null]

- test:
    name: TC11-获取用户的待办数据
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId

- test:
    name: TC11-获取用户的待办数据-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: TC12-获取用户的待办任务-signFlowId001(待办体现待填写任务)
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        taskId_list: $taskId001
        transferType_list: 1
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 2]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "TC1-业务模板发起-其他填写方填写-2个内部填写是同一个人"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - eq: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]

- test:
    name: TC1-离职并转交-webapi-业务平台(转交填写待办)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $userCode0
      receiverUserCode_transfer: $userCode2
      receiverDepartmentCode_transfer: $orgCode2
      taskId_transfer: $taskId001
      electronicSign_transfer:
        all: 1
      enterpriseSeal_transfer:
        all: 0
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(10)} #转交异步处理需要等待

- test:
    name: TC1-离职人员记录查询-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$userName0"
      userDimissionAccountNo: "$userNo0"
#      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
#      - ne: [ content.data.list.0.operator, "$orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]


- test:
    name: TC1-获取用户的待办数据-userCode0-转交完成确保没有转交内容
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]

- test:
    name: TC1-获取用户的待办数据-userCode0-转交完成确保没有转交内容-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.electronicSign", 0]
        - eq: ["content.data.enterpriseSeal", 0]
        - eq: ["content.data.userName", "$userName0"]


- test:
    name: "TC3-查询测试签署的待我处理任务"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: ""
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#      - eq: [ "content.data.list.0.startUserName", "测试签署"]

- test:
    name: "setup-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fillingList.0.userName, "$userName2"]
      - eq: [content.data.fillingList.1.userName, "$userName2"]
#      - eq: [content.data.initiatorUserName, "测试签署"]

##删除用户
#- test:
#    name: "SETUP-删除用户"
#    api: api/esignManage/InnerUsers/delete.yml
#    variables:
#      userCode_innerUsersDelete: $userCode0
#      customAccountNo_innerUsersDelete: ""
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data, "" ]


- test:
    name: case-删除组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $orgCode0
      customOrgNo: ""
    validate:
      - eq: [ json.code, 200 ]
      - eq: [ json.message, 成功 ]
      - eq: [ json.data, "" ]