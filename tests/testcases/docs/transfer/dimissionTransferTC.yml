- config:
    name: "业务平台-组织机构-操作离职-V6.0.12.0-beta.4操作离职转交"
    variables:
      orgCode0: ${ENV(csqs.orgCode)}
      userCode0: ${ENV(csqs.userCode)}
      userName_dt_1: "壹离职转交"
      randomStr1: ${random_str(9)}
      customAccountNo_dt_1: "ZDHLZJ3$randomStr1"
      email_pix: "@tsign.cn"
      fileKey0: ${ENV(fileKey)}
      execTime0: "${getDateTime(0,1)} "


- test:
    name: setup1-openapi创建内部用户
    variables:
      data: [
        {
          "customAccountNo": "0$customAccountNo_dt_1",
          "name": "测试一号$userName_dt_1",
          "mobile": "",
          "email": "0$customAccountNo_dt_1$email_pix",
          "licenseType": "",
          "licenseNo": "",
          "bankCardNo": "",
          "mainOrganizationCode": $orgCode0,
          "mainCustomOrgNo": ,
          "otherOrganization": [
          ]
        }
      ]
    api: api/esignManage/InnerUsers/create.yml
    extract:
      _userNo1: content.data.successData.0.customAccountNo
      _userCode1: content.data.successData.0.userCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ content.data.failureCount,0 ]
      - eq: [ content.data.successCount,1 ]

- test:
    name: setup1-根据组织code查询
    variables:
      organizationCode: $orgCode0
      customOrgNo: ""
      name:
    api: api/esignManage/InnerOrganizations/detail.yml
    #    setup_hooks:
    #      - ${sleep(0.1)}
    extract:
      _orgNo1: content.data.0.customOrgNo
      _orgCode1: content.data.0.organizationCode
      _orgName1: content.data.0.name
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ $_orgCode1, $orgCode0 ]
      - ne: [ $_orgName1, "" ]
      - eq: [ content.data.0.organizationType, "COMPANY" ]

- test:
    name: setup1-按organizationName查询机构列表
    api: api/esignPortal/user/getOrganizationListByOrgCodeName.yml
    variables:
      organizationName_get1: $_orgName1
    extract:
      _orgId1: content.data.0.id
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]
      - eq: [ content.data.0.organizationStatus, "1" ]
      - eq: [ content.data.0.organizationTerritory, "1" ]
      - eq: [ content.data.0.organizationType, "1" ]

- test:
    name: setup2-webapi创建内部用户-业务平台
    variables:
      subParamsSaveUser: {
        "userName": "测试二号$userName_dt_1",
        "userType": "2",          #用户类型不能为空    [1,2]用户类型(1管理员、2普通用户)
        "userMobile": "",        #用户手机为空 AES加密
        "userEmail": "${encryptKey(1$customAccountNo_dt_1$email_pix)}",          #邮箱为空 AES加密
        "licenseType": "19",          #$|[1][3,7,8,9]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
        "licenseNumber": "",   #证件号 AES加密
        "bankCardNo": "",         #银行卡 AES加密
        "userStatus": "1",        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
        "userTerritory": "1",  #用户组织类型不能为空   用户组织类型(1内部 2外部)
        "userCode": "1$customAccountNo_dt_1",            #用户编码支持输入2-30字 不能为空
        "accountNumber": "1$customAccountNo_dt_1",   #账号支持输入2-30字     不能为空
        "organizationId": $_orgId1,   #所属组织支持输入1-36字
        "organizationCode": $orgCode0,
        "orgRoleList": [ {
          "organizationId": $_orgId1,
          "roles": [ ]
        } ],
        "ecUserParttimeIDList": [ ]  #兼职用户ID
      }
    api: api/esignPortal/user/saveUser.yml
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - ne: [ content.data,"" ]

- test:
    name: TC1-通过组织id获取用户列表分页
    api: api/esignPortal/user/getUserByOrganization.yml
    variables:
    extract:
      status: content.status
    validate:
      - eq: [ $status, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]

- test:
    name: getUserOrgListByUserName-通过userName查询用户-userCode1
    api: api/esignPortal/user/getUserOrgListByUserName.yml
    variables:
      userName_get2: "测试一号$userName_dt_1"
    extract:
      _userId1: content.data.0.uid
      _userName1: content.data.0.userName
      _userCode1: content.data.0.userCode
      _orgId1: content.data.0.oid
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]
      - len_ge: [ content.data, 1 ]

- test:
    name: TC1-通过userName查询用户-userCode2
    api: api/esignPortal/user/getUserOrgListByUserName.yml
    variables:
      userName_get2: "测试二号$userName_dt_1"
    extract:
      _userId2: content.data.0.uid
      _userName2: content.data.0.userName
      _userCode2: content.data.0.userCode
      _orgId2: content.data.0.oid
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]
      - len_eq: [ content.data, 1 ]

- test:
    name: "setup4-创建企业印章-管理员userCode1"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $_userNo1
        customOrgNo: $_orgNo1
        sealGroupName: "可删-自动化$randomStr1"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            legalSealId: ""
            sealName: "$randomStr1"
    extract:
      orgSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: "setup5-一步发起顺序签署-测试离职转交"
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: TC1-一步发起顺序签署-测试离职转交
      signFiles:
        - fileKey: $fileKey0
      signerInfos:
        - signNode: 1
          userCode: "$_userCode1"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
        - signNode: 3
          userCode: "$_userCode2"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
    extract:
      - signFlowId001: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]

########################################################################
- test:
    name: TC1-获取用户的待办数据-userCode1
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId

- test:
    name: TC1-获取用户的待办数据-userCode1-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.enterpriseSeal", 2]


- test:
    name: TC1-离职并转交-webapi-业务平台(转交全部)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $_userCode1
      receiverUserCode_transfer: $userCode0
      receiverDepartmentCode_transfer: $orgCode0
      taskId_transfer: $taskId001
      electronicSign_transfer:
        all: 1
      enterpriseSeal_transfer:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(10)} #转交异步处理需要等待

- test:
    name: TC1-离职人员记录查询-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$_userName1"
      userDimissionAccountNo: "$_userNo1"
      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
      - ne: [ content.data.list.0.operator, "$_orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]

- test:
    name: TC1-获取用户的待办数据-userCode1-转交完成确保没有转交内容
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId002: content.data.taskId

- test:
    name: TC1-获取用户的待办数据-userCode1-转交完成确保没有转交内容-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId002
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.electronicSign", 0]
        - eq: ["content.data.enterpriseSeal", 0]
        - eq: ["content.data.userName", "$_userName1"]

- test:
    name: TC1-删除内部用户-userCode2
    api: api/esignManage/InnerUsers/delete.yml
    variables:
      userCode_innerUsersDelete: "$_userCode2"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data, "" ]

- test:
    name: TC1-离职人员记录查询-openapi删除的数据
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$_userName2"
      userDimissionAccountNo: ""
      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
      - contains: [ content.data.list.0.operator, "（${ENV(esign.projectId)}）" ]

- test:
    name: TC1-获取用户的待办数据-userCode2
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode2
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId002: content.data.taskId

- test:
    name: TC1-获取用户的待办数据-userCode2-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId002
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.electronicSign", 1]
        - eq: ["content.data.enterpriseSeal", 0]

- test:
    name: TC1-获取用户的待办任务-signFlowId001-userCode2
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode2
        flowName_list: ""
        flowId_list: $signFlowId001
        taskId_list: $taskId002
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataTypeName", "签署待办"]
      - eq: ["content.data.list.0.dataType", 3]
      - eq: ["content.data.list.0.flowId", "$signFlowId001"]

- test:
    name: TC15-转交待办任务-signFlowId001
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $_userCode2
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $taskId002
      electronicSign_submit:
        all: 1
      enterpriseSeal_submit:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

#- test:
#    name: TC1-直接离职-webapi-业务平台(离职之后转交)
#    api: api/esignPortal/user/addUserDimission.yml
#    variables:
#      userDimissionId: "$_userId3"
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data, "" ]
#
#- test:
#    name: TC1-离职人员记录查询-webapi-业务平台
#    api: api/esignPortal/user/dimissionRecord.yml
#    variables:
#      userDimissionName: "$_userName3"
#      userDimissionAccountNo: ""
#      userDimissionStartDate: "$execTime0"
#      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.totalCount, 1 ]
#
#- test:
#    name: TC1-获取用户的待办数据-userCode3
#    api: api/esignDocs/transfer/statistics.yml
#    variables:
#      userCode_statistics: $_userCode3
#    validate:
#        - eq: ["content.status", 200]
#        - contains: ["content.message", "成功"]
#        - eq: ["content.data.electronicSign", 0]
#        - eq: ["content.data.enterpriseSeal", 0]
#        - eq: ["content.data.userName", "$_userName1"]