- config:
    name: V6.0.14.0-beta.2-电子签署流程转交
    variables:
      - name: "V6.0.14.0-beta.2-文档模板流程转交"
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign10.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - sign04: ${ENV(sign04.userCode)}  #转交需要使用小数据量的账户，sign01不建议使用
      - userNo0: ${ENV(sign03.accountNo)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - sign01: ${ENV(sign01.accountNo)}
      - fileKey: ${ENV(fileKey)}
      - pix: ${get_snowflake()}
      - userName_dt_1: "电子转交"
      - customAccountNo_dt_1: "E-LZZJ$pix"
      - execTime0: "${getDateTime(0,1)} "
      - dingTime: ${get_dateTime(0)}
      - templateNameCommon: "自动化创建转交模板${getRandomNo(6)}"
      - pageNo: 1
#      - businessTypeCodeCBBT0: "${getBusinessTypeId2(3)}" #自动化勿动3-内部企业和个人无序指定填写（发起人和签署人填写）

#
- test:
    name: detail-查询内部用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $userCode0
    extract:
      - userCode0: content.data.0.userCode
      - userName0: content.data.0.name
      - userNo0: content.data.0.customAccountNo
      - _userMobileInner0: content.data.0.mobile
      - _orgName1: content.data.0.mainCustomOrgName
      - _orgNo1: content.data.0.mainCustomOrgNo
      - _orgCode1: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

#创建文档模板
- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - account0: $userNo0
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: "$_orgCode1"
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup_添加签名区1"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - addSignTime: 0
      - signType: 1
      - posX: 188
      - posY: 703
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - name: "签署区A"
      - signFieldType: 0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
      - account0: $userNo0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: openapi创建内部用户-用于离职转交文档模板
    variables:
      data: [
        {
          "customAccountNo": "0$customAccountNo_dt_1",
          "name": "测试电子流程转交$userName_dt_1",
          "mobile": "",
          "email": "$<EMAIL>",
          "licenseType": "",
          "licenseNo": "",
          "bankCardNo": "",
          "mainOrganizationCode": $orgCode0,
          "mainCustomOrgNo": ,
          "otherOrganization": [
          ]
        }
      ]
    api: api/esignManage/InnerUsers/create.yml
    extract:
      _userNo_e_1: content.data.successData.0.customAccountNo
      _userCode_e_1: content.data.successData.0.userCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,"成功" ]
      - eq: [ content.data.failureCount,0 ]
      - eq: [ content.data.successCount,1 ]

- test:
    name: detail-查询内部用户-离职用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $_userCode_e_1
    extract:
      - _userName_e_1: content.data.0.name
#      - _userMobileInner0: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - eq: [ content.data.0.customAccountNo, "$_userNo_e_1" ]
      - gt: [ content.data.0.name, "1" ]



- test:
    name: statistics-转交统计
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_1: content.data.taskId

- test:
    name: statistics-转交统计-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_1
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.docTemplate", 1]

- test:
    name: listTransfer-转交明细-发布的
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        docName_list: ""
        status_list: "PUBLISH"
        templateName_list: "$templateNameCommon"
        templateUuid_list: "$newTemplateUuidCommon"
        taskId_list: $_taskId_1
        transferType_list: 9
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 8]
      - contains: ["content.data.list.0.dataTypeName",  "归属人"]
      - contains: ["content.data.list.0.templateName",  "转交模板"]
      - eq: ["content.data.list.0.status",  "PUBLISH"]


- test:
    name: submit转交文档模板
    api: api/esignDocs/transfer/submit.yml
    variables:
      receiverUserCode_submit: $_userCode_e_1
      transferUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_1
      docTemplate_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: ""
      receiverUserName_record: ""
      transferType_record: 9
      dataType_record: ""
      startTime_record: $execTime0
      templateName_record: $templateNameCommon
      templateUuid_record: $newTemplateUuidCommon
      transferIdentity_record: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]

##----------模板停用转交--------
- test:
    name: "停用草稿模板"
    api: api/esignDocs/template/manage/templateSuspend.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: statistics-转交统计
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_e_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_2: content.data.taskId

- test:
    name: statistics-转交统计-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_2
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.docTemplate", 1]

- test:
    name: listTransfer-转交明细--停用的
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_e_1
        docName_list: ""
        status_list: "SUSPEND"
        templateName_list: "$templateNameCommon"
        templateUuid_list: "$newTemplateUuidCommon"
        taskId_list: $_taskId_2
        transferType_list: 9
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_002: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 8]
      - contains: ["content.data.list.0.dataTypeName",  "归属人"]
      - contains: ["content.data.list.0.templateName",  "转交模板"]
      - eq: ["content.data.list.0.status",  "SUSPEND"]

- test:
    name: submit转交文档模板
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $_userCode_e_1
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_2
      docTemplate_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_002"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: ""
      receiverUserName_record: ""
      transferType_record: 9
      dataType_record: ""
      startTime_record: $execTime0
      templateName_record: $templateNameCommon
      templateUuid_record: $newTemplateUuidCommon
      transferIdentity_record: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]
##---------草稿状态的模板转交----
- test:
    name: "获取模板信息"
    variables:
      templateUuid: $newTemplateUuidCommon
      version: 1
    api: api/esignDocs/template/manage/templateInfo.yml
    extract:
      templatedocUid: content.data.docUid
      templatefileKey: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup_模版编辑变成草稿状态"
    api: api/esignDocs/template/manage/templateUpdate.yml
    variables:
        account0: $userNo0
        templateUuid: $newTemplateUuidCommon
        createUserOrg: "$_orgCode1"
        version: $newVersionCommon
        fileKey: $templatefileKey
        templateType: 1
        zipFileKey: null
        templateName: $templateNameCommon
        docUuid: $templatedocUid
        allRange: 1
        organizeRange: null
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: statistics-转交统计
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode0
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_3: content.data.taskId

- test:
    name: statistics-转交统计-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_3
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ge: ["content.data.docTemplate", 1]

- test:
    name: listTransfer-转交明细-草稿
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode0
        docName_list: ""
        status_list: "DRAFT"
        templateName_list: "$templateNameCommon"
        templateUuid_list: "$newTemplateUuidCommon"
        taskId_list: $_taskId_3
        transferType_list: 9
        pageNum_list: 1
        pageSize_list: 10
    extract:
      - dataId_todo_003: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 8]
      - contains: ["content.data.list.0.dataTypeName",  "归属人"]
      - contains: ["content.data.list.0.templateName",  "转交模板"]
      - eq: ["content.data.list.0.status",  "DRAFT"]


- test:
    name: submit转交文档模板
    api: api/esignDocs/transfer/submit.yml
    variables:
      receiverUserCode_submit: $_userCode_e_1
      transferUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      taskId_submit: $_taskId_3
      docTemplate_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_003"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: ""
      receiverUserName_record: ""
      transferType_record: 9
      dataType_record: ""
      startTime_record: $execTime0
      templateName_record: $templateNameCommon
      templateUuid_record: $newTemplateUuidCommon
      transferIdentity_record: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]

############离职转交###############
- test:
    name: statistics-转交统计-（离职转交）
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $_userCode_e_1
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      _taskId_4: content.data.taskId

- test:
    name: statistics-转交统计-（离职转交）-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $_taskId_4
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.docTemplate", 1]
        - ge: ["content.data.electronicSignProcessCount", 0]

- test:
    name: listTransfer-转交明细-草稿
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $_userCode_e_1
        flowName_list: ""
        flowId_list: ""
        taskId_list: $_taskId_4
        transferType_list: 9
        pageNum_list: 1
        status_list: "DRAFT"
        pageSize_list: 10
    extract:
      - dataId_todo_004: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ge: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 8]
      - contains: ["content.data.list.0.dataTypeName",  "归属人"]

- test:
    name: transfer-离职并转交-webapi-业务平台(转交全部)
    api: api/esignDocs/transfer/transfer.yml
    variables:
      transferUserCode_transfer: $_userCode_e_1
      receiverUserCode_transfer: $userCode0
      receiverDepartmentCode_transfer: $_orgCode1
      taskId_transfer: $_taskId_4
      docTemplate_transfer:
        all: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(5)} #转交异步处理需要等待

- test:
    name: dimissionRecord-webapi-业务平台
    api: api/esignPortal/user/dimissionRecord.yml
    variables:
      userDimissionName: "$_userName_e_1"
      userDimissionAccountNo: "$_userNo_e_1"
      userDimissionStartDate: "$execTime0"
      userDimissionEndDate: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.totalCount, 1 ]
      - ne: [ content.data.list.0.operator, "$_orgName1" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.userName)}" ]
      - contains: [ content.data.list.0.operator, "${ENV(sign01.main.orgName)}" ]

- test:
    name: record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      transferUserName_record: $_userName_e_1
      receiverUserName_record: $userName0
      transferType_record: 9
      dataType_record: ""
      startTime_record: $execTime0
      templateName_record: $templateNameCommon
      templateUuid_record: $newTemplateUuidCommon
      transferIdentity_record: 1
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - len_ge: ["content.data.list", 1]