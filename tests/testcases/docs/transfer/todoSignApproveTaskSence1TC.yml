- config:
    name: "审批转交"
    variables:
      - name: "场景-转交签署中和待签署的待办任务"
      - attachments0: []
      - CCInfosCBBT: []
      - newTemplateUuid1: ${getTemplateId(2,2,pdf,0)}
      - newVersion1: 1
      - randomCount: ${getDateTime()}
      - userCode0: ${ENV(sign01.userCode)}
      - userName0: ${ENV(sign01.userName)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - fileKey0: ${ENV(fileKey)}
      - fileKey1: ${ENV(1PageFileKey)}
      - businessTypeCode: ${ENV(businessTypeCode)}
      - userCodeInit: ${ENV(csqs.userCode)}
      - orgCodeInit: ${ENV(csqs.orgCode)}
      - presetName0: "自动化-openapi模板发起-指定双方无内容域"
      - businessTypeCode_todo_001: "${getBusinessTypeId($presetName0,1,0)}"
- test:
    name: "创建审批签署流程"
    testcase: common/submit/getFlowId_toAudit.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - auditUserCodeCommon
      - presetNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
      processInstanceIdCommon: content.data.list.0.processInstanceId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "列表查询-flowid+状态"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowStatus: 2
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - contains: [content.data.list.0.flowStatusName, "审批"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandler, "$auditUserCodeCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserCodeCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]

- test:
    name: TC5-获取用户的待办数据
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $auditUserCodeCommon
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId

- test:
    name: TC5-获取用户的待办数据-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]


- test:
    name: TC5-获取用户的待办任务-flowIdCommon
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $auditUserCodeCommon
        taskId_list: $taskId001
        flowId_list: $flowIdCommon
        transferType_list: 1
    extract:
      dataId001: content.data.list.0.dataId
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - eq: ["content.data.list.0.dataTypeName", "审批待办"]
        - eq: ["content.data.list.0.flowId", "$flowIdCommon"]
        - eq: ["content.data.list.0.initiatorName", "$userName0"]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC3-提交待办任务-signFlowId001-转交人已离职
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $auditUserCodeCommon
      receiverUserCode_submit: ${ENV(userCode.dimission)}
      receiverDepartmentCode_submit: $orgCode0
      timestamp_submit: "${getDateTime(0)}"
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId001"]
    validate:
      - eq: ["content.status", 1111003]
      - eq: ["content.message", "用户已不存在"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}


- test:
    name: TC3-提交待办任务-signFlowId001-转交给自己
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $auditUserCodeCommon
      receiverUserCode_submit: $auditUserCodeCommon
      receiverDepartmentCode_submit: $orgCode0
      timestamp_submit: "${getDateTime(0)}"
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId001"]
    validate:
      - eq: ["content.status", 1640001]
      - eq: ["content.message", "转交人和接收人不能相同"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: TC3-提交待办任务-signFlowId001-转交成功
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $auditUserCodeCommon
      receiverUserCode_submit: $userCode0
      receiverDepartmentCode_submit: $orgCode0
      timestamp_submit: "${getDateTime(0)}"
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId001"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(1)}

