- config:
    name: P0TL-批量催办-指定处理人
    variables:
       fileKey: ${ENV(fileKey)}
       fileKey0: ${ENV(1PageFileKey)}
       fileKey1: ${ENV(2PageFileKey)}
       fileKey2: ${ENV(fileKey)}
       O1: ${ENV(sign01.main.orgCode)} #O1
       userCodeInit: ${ENV(csqs.userCode)}
       userNoInit: ${ENV(csqs.accountNo)}
       orgNo001: ${ENV(sign01.main.orgNo)}
       orgCode002: ${ENV(csqs.orgCode)}
       orgNo002: ${ENV(csqs.orgNo)}
       userCode001: ${ENV(sign03.userCode)} #用户U1,主责是部门D1:ORG-SIGN-06，兼职是O2:sign03.main.departNo.orgNo
       userNo001: ${ENV(sign03.accountNo)}
       D1: ${ENV(sign03.main.departNo)} #D1
       O2No: ${ENV(sign03.main.departNo.orgNo)} #O2
       O2: ${ENV(sign01.JZ.depart.orgCode)} #O2的code
       userCode002: ${ENV(sign01.userCode)} #用户U2,主职O1
       userNo002: ${ENV(sign01.accountNo)}
       userCode003: ${ENV(sign07.userCode)} #用户U3，（主职O2，兼职D1)
       wUserCode001: ${ENV(wsignwb01.userCode)} #用户WU1
       wUserNo001 : ${ENV(wsignwb01.accountNo)}
       wOrgCode001: ${ENV(wsignwb01.main.orgCode)}  #企业WO1
       wOrgNo001: ${ENV(wsignwb01.main.orgNo)}
       wUserCode003: ${ENV(wsignwb04.userCode)} #用户WU3：个人相对方
       wUserNo003: ${ENV(wsignwb04.accountNo)}
       sealTypeCodeCommon1: "STA1${generate_random_str(6)}"
       sealGroupName1: "$sealTypeCodeCommon1-可删"
       sealTypeCodeCommon2: "STA1${generate_random_str(6)}"
       sealGroupName2: "$sealTypeCodeCommon2-可删"
       templateNameCommon: "全链路新建的文档模板1-${get_randomNo_16()}"
       templateNameCommon1: "全链路新建的文档模板2-${get_randomNo_16()}"
       templateNameCommon2: "全链路新建的文档模板2-${get_randomNo_16()}"
       description: "全链路自动化测试描述"
       initiatorAll: 1
       contentUuid: ""
       autoTestDocUuid1Common: ${get_a_docConfig_type()}
       autoPresetName: "全链路新建的业务模板2-${get_randomNo_16()}"
       timestamp1: ${getTimeStamp()}
       "fillingList1": [
        {
          "name": "内部填写方",
          "userTerritory": "1",
          "key": "${get_snowflake()}"
        },{
          "name": "外部填写方",
          "userTerritory": "2",
          "key": "${get_snowflake()}"
        }
      ]

#系统已有内部用企业、用户：企业O1，用户U1，用户U2。
#系统已有外部企业、用户：企业WO1，企业WO2，用户WU1（主职WO1），用户WU2（主职WO2）


#查询sign07的账号信息
- test:
    name: "step1-查询内部用户"
    api:  api/esignManage/InnerUsers/detail.yml
    variables:
        detailData:
            userCode: $userCode003
            customAccountNo:
    extract:
        userNo003: content.data.0.customAccountNo
    validate:
        - eq: [ content.code, 200 ]
        - eq: [ content.message, "成功" ]

#######只有一个已存在的外部企业，新建一个外部企业WO2和用户WU2（主职WO2）
#step1: 新增外部企业和用户：企业WO2，用户WU2

- test:
    name: "TC2-创建webapi的外部企业和用户"
    testcase: common/OrgUser/webapiCreateExternalOrgUser.yml
    extract:
      - organizationIdExternal
      - organizationCodeExternal
      - organizationNameExternal
      - userIdExternal
      - userCodeExternal
      - userNameExternal
      - userMobileExternal
      - cutsomAccountNumberExternalParam
      - cutsomOrgNo0
      - userMobileCrypt
      - orgLicenseNumberCrypt
      - userLicenseNumberCrypt
      - orgLicenseNoExternal
      - legalRepAccountNoExternal
      - legalRepUserCodeExternal
      - legalRepNameExternal
      - legalRepUserCodeExternal


#更新u3的兼职企业为D1
- test:
    name: "step8-openapi兼职三个企业"
    api: api/esignManage/InnerUsers/update.yml
    variables:
      data:
        {
          "userCode": $userCode003,
          "customAccountNo": ,
          "name": ,
          "mobile": ,
          "email": ,
          "licenseType": ,
          "licenseNo": ,
          "bankCardNo": ,
          "mainOrganizationCode": ,
          "mainCustomOrgNo": ,
          "otherOrganization": [
            {
              "otherCustomOrgNo": "$D1",
              "otherOrganizationCode": ""
            }]
        }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]
#2、创建企业O1印章，授权用印人为部门D1
- test:
    name: TC2-新建印章类型A1
    api: api/esignSeals/sealType/saveSealType.yml
    variables:
      sealTypeCode: $sealTypeCodeCommon1
      sealTypeName: $sealTypeCodeCommon1

- test:
    name: TC3-创建企业O1印章-sealId001-印章类型A1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        userCode: $userCode001
        organizationCode: $O1
        sealGroupName: $sealGroupName1
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealName: "SEAL1-国际标准"
    extract:
      sealId001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC3-创建企业O2印章-sealId002-印章类型A2
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        userCode: $userCode002
        customOrgNo: $O2No
        sealGroupName: $sealGroupName2
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealName: "SEAL1-国际标准"
    extract:
      sealId002: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC5-sealId001授权用印人部门D1、机构O2
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealId001
        sealsignerType:  1
        sealsignersInfos:
           - customOrgNo: $D1
           - organizationCode: $O2
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: TC6-sealId002授权组织O2
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealId002
        sealsignerType: 1
        sealsignersInfos:
          - organizationCode: $O2
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(2)}

###新增3份文档模板####包含5个填写域。一共包含2个为必填内容域，3个为非必填有默认值内容域
- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "模版新建1"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon1: content.data.templateUuid
      - newVersionCommon1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_006: ${putTempEnv(_tmp_common_template_006, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_006)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_006)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      bizId: "${get_randomStr_32()}"
      bizId2: "${get_randomStr_32()}"
      fields_draft: [
        {
          "fieldId": "",
          "label": "单行文本1",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 196.13132250580045,
            "y": 770.9980665119876,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
        {
          "fieldId": "",
          "label": "单行文本1",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId2",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 404.1563676205207,
            "y": 691.3101250322247,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }
            ]
#      fields_draft: ${epaasFillContent(2,null,控件1,1)}
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板1"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: $newVersionCommon1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "模版新建2"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon1
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon2: content.data.templateUuid
      - newVersionCommon2: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon2
      - version: $newVersionCommon2
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_007: ${putTempEnv(_tmp_common_template_007, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_007)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft--非必填"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_007)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      bizId: "${get_randomStr_32()}"
      bizId2: "${get_randomStr_32()}"
      fields_draft: [
        {
          "fieldId": "",
          "label": "单行文本3",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 196.13132250580045,
            "y": 770.9980665119876,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        },
        {
          "fieldId": "",
          "label": "单行文本4",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 2,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId2",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 404.1563676205207,
            "y": 691.3101250322247,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }
            ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板2"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon2
      - version: $newVersionCommon2
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "模版新建3"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon2
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon3: content.data.templateUuid
      - newVersionCommon3: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon3
      - version: $newVersionCommon3
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_008: ${putTempEnv(_tmp_common_template_008, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_008)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft--必填"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_008)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
#      fields_draft: ${epaasFillContent(1,null,非必填的,0)}
      bizId: "${get_randomStr_32()}"
      bizId2: "${get_randomStr_32()}"
      fields_draft: [
        {
          "fieldId": "",
          "label": "单行文本5",
          "custom": false,
          "type": "TEXT",
          "subType": null,
          "sort": 1,
          "formula": null,
          "style": {
            "font": "1",
            "fontSize": 42,
            "textColor": "#000",
            "width": 160,
            "height": 49,
            "bold": false,
            "italic": false,
            "underLine": false,
            "lineThrough": false,
            "verticalAlignment": "TOP",
            "horizontalAlignment": "LEFT",
            "styleExt": {
              "signDatePos": null,
              "units": "px",
              "imgType": null,
              "usePageTypeGroupId": "",
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "keyword": "",
              "groupKey": "",
              "tickOptions": null,
              "elementId": "",
              "posKey": "",
              "imgCrop": null,
              "paddingLeftAndRight": "0"
            }
          },
          "settings": {
            "defaultValue": null,
            "required": false,
            "dateFormat": null,
            "validation": {
              "type": "REGEXP",
              "pattern": ""
            },
            "selectableDataSource": [

            ],
            "numberFormat": {
              "integerDigits": null,
              "fractionDigits": null,
              "thousandsSeparator": ""
            },
            "editable": true,
            "encryptAlgorithm": "",
            "fillLengthLimit": "20",
            "overflowType": "1",
            "minFontSize": "8",
            "remarkInputType": null,
            "content": null,
            "remarkAICheck": null,
            "dateRule": null,
            "tickOptions": null,
            "configExt": {
              "cooperationerSubjectType": "",
              "icon": "epaas-icon-text",
              "fastCheck": null,
              "addSealRule": "",
              "keyPosX": null,
              "keyPosY": null,
              "ext": "{}",
              "version": null,
              "mergeId": null
            },
            "sealTypes": [

            ],
            "columnMapping": null,
            "positionMovable": null,
            "cellEditableOnFilling": true
          },
          "options": null,
          "instructions": "",
          "contentFieldId": "$bizId",
          "bizId": "$bizId2",
          "fieldKey": null,
          "fillGroupKey": "",
          "fieldValue": null,
          "defaultValue": null,
          "position": {
            "x": 196.13132250580045,
            "y": 770.9980665119876,
            "page": "1",
            "scope": "default",
            "intervalType": null
          },
          "formField": false
        }
            ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板3"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon3
      - version: $newVersionCommon3
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

###新增业务模板：不允许发起时追加签署文件，关联新增的3份文档模板。
  #第三步设置签署方，节点一相对方企业+内部个人U2无序签，节点二相对方个人+内部企业O1经办人U3+内部企业O2经办人U1顺序签。
  #在第四步添加一个内部其他填写方，一个相对方填写方。
  #指定2个加密的内容域为相对方企业签署方、相对方其他填写方填写
  #3个非必填的为内部填写方、内部企业O1经办人U3、相对方个人签署方。启用此业务模板
- test:
    name: "添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取业务模版presetId和bizId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
      - status: ""
    extract:
      - getPresetId: content.data.list.0.presetId
      - getbusinessTypeId: content.data.list.0.businessTypeId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取文档模板1详情"
    api: api/esignDocs/template/manage/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon1
      - version: 1
    extract:
      - templateFileKey1: content.data.fileKey
      - templateName1: content.data.templateName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取文档模板2详情"
    api: api/esignDocs/template/manage/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon2
      - version: 1
    extract:
      - templateFileKey2: content.data.fileKey
      - templateName2: content.data.templateName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取文档模板3详情"
    api: api/esignDocs/template/manage/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon3
      - version: 1
    extract:
      - templateFileKey3: content.data.fileKey
      - templateName3: content.data.templateName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "点击下一步按钮"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      "params": {
        "presetId": $getPresetId,
        "presetName": $autoPresetName,
        "initiatorAll": 1,
        "initiatorEdit": 0,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "sort": 0,
        "workFlowModelKey": "",
        "workFlowModelStatus": 0,
        "signatoryCount": 0,
        "templateList": [
        {
          "templateId": $newTemplateUuidCommon1,
          "version": 1,
        },{
          "templateId": $newTemplateUuidCommon2,
          "version": 1,
        },{
          "templateId": $newTemplateUuidCommon3,
          "version": 1,
        }
        ],
        "fileFormat": 1,
        "checkRepetition": 1
      }
    validate:
      - contains: ["content.message",成功]
      - eq: ["content.status",200]

- test:
    name: "TC9-业务模板第二步：保存签署方式"
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
        businessTypeName: $autoPresetName
        businessTypeId: $getbusinessTypeId
        presetId: $getPresetId
    validate:
        - eq: [ content.message, "成功" ]
        - eq: [ content.status, 200 ]
        - eq: [content.success, true]

- test:
    name: "节点一相对方企业+内部个人U2无序签，节点二相对方个人+内部企业O1经办人U3+内部企业O2经办人U1顺序签"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "signerList1": [
        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "id": "add-1",
          "signatoryList": [ ],
          "signerTerritory": 1,
          "signerType": 1,
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "f8e6ed41-cc5b-4fd5-b6bd-f8bcf813fdfa"
        },
        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "signatoryList": [ ],
          "signerTerritory": 2,
          "signerType": 2,
          "id": "add-2",
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "cc65688b-31ec-4a74-9add-01573d7b6650"
        }
      ]
      "signerList2": [
        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "signatoryList": [ ],
          "signerTerritory": 2,
          "signerType": 1,
          "id": "add-3",
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "cc65688b-31ec-4a74-9add-01573d7b6657"
        },
        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "signatoryList": [ ],
          "signerTerritory": 1,
          "signerType": 2,
          "id": "add-4",
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "cc65688b-31ec-4a74-9add-01573d7b6652"
        },        {
          "autoSign": 0,
          "organizeCode": "",
          "sealTypeCode": "",
          "signatoryList": [ ],
          "signerTerritory": 1,
          "signerType": 2,
          "id": "add-5",
          "userCode": "",
          "sealTypeName": "",
          "departmentCode": "",
          "signerId": "cc65688b-31ec-4a74-9add-01573d7b6654"
        }
      ]
      "params": {
        "presetId": $getPresetId,
        "status": 0,
        "allowAddSigner": 0,
        "needGather": 0,
        "needAudit": 0,
        "sort": 0,
        "contentDomainCount": 5,
        "editComponentValue": 1,
        "fillingList": $fillingList1,
        "signerNodeList": [ {
          "signerList": $signerList1,
          "signMode": 1,
          "signNode": 1,
          "id": "node-0"
        },{
          "signerList": $signerList2,
          "signMode": 0,
          "signNode": 2,
          "id": "node-1"
        } ]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "添加其他填写方"
    api: api/esignDocs/businessPreset/updateFillingUsers.yml
    variables:
      - presetId_updateFillingUsers: $getPresetId
      - fillingList_updateFillingUsers: $fillingList1
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [ content.data.fillingList.0.name, "内部填写方" ]
      - eq: [ content.data.fillingList.1.name, "外部填写方" ]

- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $getPresetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]
      - len_eq: [content.data.fillingList,2]
      - len_eq: [content.data.signerNodeList,2]
      - len_eq: [content.data.templateList,3]
    extract:
      - _editUrl_00: content.data.editUrl

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId1: content.data.contents.0.id
      - _entityId1: content.data.contents.0.entityId
      - _contentId2: content.data.contents.1.id
      - _entityId2: content.data.contents.1.entityId
      - _contentId3: content.data.contents.2.id
      - _entityId3: content.data.contents.2.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
    name: "epaasTemplate-detail--模板1"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId1
      entityId_detail: $_entityId1
    extract:
      - _baseFile1: content.data.baseFile
      - _originFile1: content.data.originFile
      - _fields1: content.data.fields
      - _fields10: content.data.fields.0
      - _fields11: content.data.fields.1
      - _name1: content.data.name
      - _label_1: content.data.fields.0.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-detail--模板2"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId2
      entityId_detail: $_entityId2
    extract:
      - _baseFile2: content.data.baseFile
      - _originFile2: content.data.originFile
      - _fields2: content.data.fields
      - _fields20: content.data.fields.0
      - _fields21: content.data.fields.1
      - _name2: content.data.name
      - _label_21: content.data.fields.0.label
      - _label_22: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-detail--模板3"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId3
      entityId_detail: $_entityId3
    extract:
      - _baseFile3: content.data.baseFile
      - _originFile3: content.data.originFile
      - _fields3: content.data.fields
      - _fields31: content.data.fields.0
      - _name3: content.data.name
      - _label_3: content.data.fields.0.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]


- test:
    name: "获取模版参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerId2: content.data.2.id
      - _signerId3: content.data.3.id
      - _signerId4: content.data.4.id
      - _signerId5: content.data.5.id
      - _fillId1: content.data.6.id
      - _fillId2: content.data.7.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
      - _signerName2: content.data.2.name
      - _signerName3: content.data.3.name
      - _signerName4: content.data.4.name
      - _signerName5: content.data.5.name
      - _fillName1: content.data.5.id
      - _fillName2: content.data.5.id
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft--填写控件设置参与方"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      _tmp_fill1: [ { "label": "$_label_1","templateRoleId": "$_signerId1" } ]
      _tmp_fill21: [ { "label": "$_label_21","templateRoleId": "$_signerId2" } ]
      _tmp_fill22: [ { "label": "$_label_22","templateRoleId": "$_signerId5" } ]
      _tmp_fill3: [ { "label": "$_label_3","templateRoleId": "$_fillId1" } ]
      fields_draft10: "${getEpaasTemplateContentWithRoleId($_fields1,$_tmp_fill1)}"
      fields_draft21: "${getEpaasTemplateContentWithRoleId($_fields2,$_tmp_fill21)}"
      fields_draft22: "${getEpaasTemplateContentWithRoleId($fields_draft21,$_tmp_fill22)}"
      fields_draft30: "${getEpaasTemplateContentWithRoleId($_fields3,$_tmp_fill3)}"
      json_save_draft: {
          "data": [
                    {
                        "baseFile": $_baseFile1,
                        "originFile": $_originFile1,
                        "fields": $fields_draft10,
                        "pageFormatInfoParam": null,
                        "name": $_name1,
                        "contentId": $_contentId1,
                        "entityId": $_entityId1
                    },
                    {
                        "baseFile": $_baseFile2,
                        "originFile": $_originFile2,
                        "fields": $fields_draft22,
                        "pageFormatInfoParam": null,
                        "name": $_name2,
                        "contentId": $_contentId2,
                        "entityId": $_entityId2
                    },
                    {
                      "baseFile": $_baseFile3,
                      "originFile": $_originFile3,
                      "fields": $fields_draft30,
                      "pageFormatInfoParam": null,
                      "name": $_name3,
                      "contentId": $_contentId3,
                      "entityId": $_entityId3
                    }
          ]
      }
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "启用并发布模版"
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetId
      - status: 1
      - templateList: []
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
      name: setup-openapi查询电子签署业务模板详情-businessTypeCodeCBBT1
      api: api/esignDocs/businessPreset/bizTemplatesDetail.yml
      variables:
          businessTypeCode: $getbusinessTypeId
      extract:
        - fillingUserInfos: content.data.fillingUserInfos
      validate:
          - eq: [ content.code, 200 ]
          - eq: [ content.message, "成功" ]
          - eq: [ content.data.businessTypeCode, $getbusinessTypeId ]

#通过业务模版发起签署
- test:
    name: TC3-全链路含发起方填写、其他填写方填写、签署方填写
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - subject: "P21全链路-${get_randomNo_16()}"
      - businessTypeCodeCBBT: $getbusinessTypeId
      - CCInfosCBBT: []
      - fillingUserInfosCBBT:
          - fillingUserType: 2
            signerId: $_fillId1
            fillingUserId: $_fillId1
            fillingUserInfo:
              userCode: $userCode003
              customDepartmentNo: $D1
          - fillingUserType: 1
            signerId: $_signerId1
          - fillingUserType: 1
            signerId: $_signerId2
          - fillingUserType: 1
            signerId: $_signerId5
      - signerInfosCBBT:
          - signerId: $_signerId1
            signerAccountInfo:
              userCode: "$userCode001"
              customDepartmentNo: "$D1"
          - signerId: $_signerId2
            sealTypeCode: ""
            signerAccountInfo:
              userCode: "$wUserCode001"
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: "$wOrgCode001"
              customOrgNo:
          - signerId: $_signerId3
            sealTypeCode: ""
            signerAccountInfo:
              userCode: "$wUserCode003"
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: ""
              customOrgNo:
          - signerId: $_signerId4
            sealTypeCode: ""
            estpNode: ""
            signerAccountInfo:
              userCode: $userCode001
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: $O1
              customOrgNo:
          - signerId: $_signerId5
            sealTypeCode: ""
            estpNode: ""
            signerAccountInfo:
              userCode: $userCode003
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: $O2
              customOrgNo:
    extract:
      - signFlowId001: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - ne: [content.data.signFlowId, null]
      - eq: [content.data.signFlowStatus, "7"]
      - contains: [content.message, "成功"]


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $signFlowId001
    extract:
      - currentHandler1: content.data.list.0.currentHandler
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.currentHandler, "sign03,wsignwb01,sign07,sign07"]
      - eq: [content.data.list.0.flowStatusName, "填写中"]
      - eq: [content.data.list.0.signStatus, "0"]
      - len_eq: [content.data.list.0.signUserNames, 5]
##查询签署列表
- test:
    name: "我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId001
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.fillingList.0.userCode, "$userCode003"]
      - eq: [content.data.signerNodeList.0.signMode, 1]
      - eq: [content.data.signerNodeList.0.signNode, 1]
      - len_eq: [content.data.signerNodeList.0.signerList, 2]
      - eq: [content.data.signerNodeList.1.signMode, 0]
      - eq: [content.data.signerNodeList.1.signNode, 2]
      - len_eq: [content.data.signerNodeList.1.signerList, 3]
      - len_eq: [content.data.templateNameList, 3]

#催办U2
- test:
    name: "TC-我管理的-成功催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $signFlowId001
      - currentHandler: $wUserCode001
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 1]
      - eq: [content.data.reminderResultDesc, "发起催办成功"]
#再催办全部-提示已催办
- test:
    name: "TC-我管理的-成功催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $signFlowId001
      - currentHandler: $currentHandler1
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 0]
      - contains: [content.data.reminderResultDesc, "已催办"]

#sign07的代办
- test:
    name: "TC3-查询测试签署的待我处理任务"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNo003"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",2 ]
      - len_eq: [ "content.data.list",2 ]
#sign03的代办
- test:
    name: "TC3-查询测试签署的待我处理任务"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNo001"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",1 ]
      - len_eq: [ "content.data.list",1 ]

- test:
    name: "TC1-填写中-校验新增的出参字段"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $signFlowId001
    extract:
      _fillingUserInfos001: content.data.fillingUserInfos
#      _fillUrl001: content.data.fillingUserInfos.0.fillUrl
#      _fillUrl002: content.data.fillingUserInfos.1.fillUrl
    validate:
      - ne: [ content.data.fillingUserInfos, null ]
      - ne: [ content.data.businessTypeCode, ""]
      - eq: [ content.data.signerInfos.0.signerType, 1 ]
      - len_eq: [ content.data.signerInfos, 5 ]
      - len_eq: [ content.data.fillingUserInfos, 4 ]
      - eq: [ content.data.signFlowId, "$signFlowId001" ]

#sign07去填写其中一个流程
- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token-其他填写方"
    api: api/esignDocs/docGatherForm/querySignerFillFilesInner.yml
    variables:
      - account: $userNo003
      - password: ${ENV(password01)}
      - _fillUrl001: "${dataArraySort($_fillingUserInfos001, fillingUserType,3, fillUrl)}"
      - templateInitiationSignersUuid: "${extractParameterValue($_fillUrl001,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($_fillUrl001,templateInitiationUuid)}"
      - tmp_todo_11: ${putTempEnv(templateInitiationSignersUuid001, $templateInitiationSignersUuid)}
      - tmp_todo_12: ${putTempEnv(templateInitiationUuid001, $templateInitiationUuid)}
    extract:
      - _fillUrl001_1: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]



- test:
    name: "获取填写页信息-其他填写方"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl001_1,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId01: content.data.waitFillFields.0
      - contentId01: content.data.contents.0.contentId
      - documentId01: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]

- test:
    name: "填写页提交"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl001_1,tplToken)}"
      - fieldId_submit: $fieldId01
      - fillValue_submit: "填写页填充"
      - contentId_submit: $contentId01
      - documentId_submit: $documentId01
    validate:
      - eq: [content.code, 0]
      - ne: [content.message, null]
      - ne: [content.success, false]

- test:
    name: "signerFill-填写页-提交-签署人非当前登录人"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(templateInitiationSignersUuid001)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(templateInitiationUuid001)}
      wordList: []
      formValues: []
#    teardown_hooks:
#      - ${sleep(2)}
    validate:
      - eq: [content.status, 1604012]
      - eq: [content.success, false]
      - eq: [content.message, '当前登陆用户非签署填写人']
      - eq: [content.data, null]


- test:
    name: "signerFill-填写页-提交-是当前登录人-sign07"
    api: api/esignDocs/docGatherForm/signerFillInner.yml
    variables:
      account: $userNo003
      password: ${ENV(password01)}
      templateInitiationSignersUuid: ${ENV(templateInitiationSignersUuid001)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(templateInitiationUuid001)}
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(3)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]

#查询sign07的待办-只剩下一条
- test:
    name: "TC3-查询sign07的待我处理任务-1条"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNo003"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",1 ]
      - len_eq: [ "content.data.list",1 ]


- test:
    name: TC4-获取用户的sign07待办数据
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode003
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - ne: ["content.data.taskId", ""]
    extract:
      taskId001: content.data.taskId
 
- test:
    name: TC4-获取用户的sign07待办数据-获取结果
    api: api/esignDocs/transfer/statisticsResult.yml
    variables:
      taskId_result: $taskId001
    setup_hooks:
      - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]

- test:
    name: 查询转交列表
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode003
        flowName_list: ""
        flowId_list: $signFlowId001
        taskId_list: $taskId001
    extract:
      - dataId_todo_001: content.data.list.0.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 1]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId001"]
      - contains: ["content.data.list.0.flowName",  "全链路"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - ne: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]
- test:
    name: TC14-提交待办任务-signFlowId001-转交填写中的任务成功-转交给csqs
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode003
      receiverUserCode_submit: $userCodeInit
      receiverDepartmentCode_submit: $D1
      taskId_submit: $taskId001
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(6)}

- test:
    name: TC1-10-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      flowId_record: $signFlowId001
      receiverUserName_record:
      transferUserName_record: "测试签署七"
      transferType_record: 1
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: ""
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 1]

#查询sign07的待办
- test:
    name: "TC3-查询sign07的待我处理任务-0条"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: ""
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",0 ]
      - len_eq: [ "content.data.list",0 ]

- test:
    name: "TC3-查询csqs的待我处理任务-1条"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNoInit"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId001
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",1 ]
      - len_eq: [ "content.data.list",1 ]

#再催办csqs
- test:
    name: "TC-催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $signFlowId001
      - currentHandler: $userCodeInit
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 1]
      - eq: [content.data.reminderResultDesc, "发起催办成功"]




#使用业务模版发起--发起方、签署方、其他填写方都是同一个人-全部转交
- test:
    name: TC3-全链路含发起方填写、其他填写方填写、签署方填写
    api: api/esignSigns/signFlow/createByBizTemplate.yml
    variables:
      - subject: "P21全链路-${get_randomNo_16()}"
      - businessTypeCodeCBBT: $getbusinessTypeId
      - CCInfosCBBT: []
      - flowConfigsCBBT:
          fillConfig:
            autoFillAndSubmit: 1
            editComponentValue: 1
      - fillingUserInfosCBBT:
          - fillingUserType: 2
            signerId: $_fillId1
            fillingUserId: $_fillId1
            fillingUserInfo:
              userCode: $userCode001
              customDepartmentNo: $O2No
          - fillingUserType: 1
            signerId: $_signerId1
          - fillingUserType: 1
            signerId: $_signerId2
            contentsControl:
              - contentName: "单行文本3"
                contentValue: "预填值"
          - fillingUserType: 1
            signerId: $_signerId5
      - signerInfosCBBT:
          - signerId: $_signerId1
            signerAccountInfo:
              userCode: "$userCode001"
              customDepartmentNo: "$orgNo002"
          - signerId: $_signerId2
            sealTypeCode: ""
            signerAccountInfo:
              userCode: "$wUserCode001"
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: "$wOrgCode001"
              customOrgNo:
          - signerId: $_signerId3
            sealTypeCode: ""
            signerAccountInfo:
              userCode: "$wUserCode003"
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: ""
              customOrgNo:
          - signerId: $_signerId4
            sealTypeCode: ""
            estpNode: ""
            signerAccountInfo:
              userCode: $userCode001
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode: $O1
              customOrgNo:
          - signerId: $_signerId5
            sealTypeCode: ""
            estpNode: ""
            signerAccountInfo:
              userCode: $userCode001
              customAccountNo:
              departmentCode:
              customDepartmentNo:
              organizationCode:
              customOrgNo: $orgNo002
    extract:
      - signFlowId002: content.data.signFlowId
    validate:
      - eq: [content.code, 200]
      - ne: [content.data, null]
      - ne: [content.data.signFlowId, null]
      - eq: [content.data.signFlowStatus, "7"]
      - contains: [content.message, "成功"]


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $signFlowId002
    extract:
      - currentHandler1: content.data.list.0.currentHandler
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.currentHandler, "$userCode001,$userCode001,$userCode001"]
      - eq: [content.data.list.0.flowStatusName, "填写中"]
      - eq: [content.data.list.0.signStatus, "0"]
      - len_eq: [content.data.list.0.signUserNames, 5]
##查询签署列表
- test:
    name: "我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowId002
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.fillingList.0.userCode, "$userCode001"]
      - eq: [content.data.signerNodeList.0.signMode, 1]
      - eq: [content.data.signerNodeList.0.signNode, 1]
      - len_eq: [content.data.signerNodeList.0.signerList, 2]
      - eq: [content.data.signerNodeList.1.signMode, 0]
      - eq: [content.data.signerNodeList.1.signNode, 2]
      - len_eq: [content.data.signerNodeList.1.signerList, 3]
      - len_eq: [content.data.templateNameList, 3]

#催办U2
- test:
    name: "TC-我管理的-成功催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $signFlowId002
      - currentHandler: $userCode001
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 1]
      - eq: [content.data.reminderResultDesc, "发起催办成功"]

#全部转交给sign01，sign01去填写完成
- test:
    name: TC4-获取用户的sign03待办数据
    api: api/esignDocs/transfer/statistics.yml
    variables:
      userCode_statistics: $userCode001
    validate:
        - eq: ["content.status", 200]
        - contains: ["content.message", "成功"]
        - not_equals: ["content.data.taskId", ""]
    extract:
      taskId002: content.data.taskId
    teardown_hooks:
      - ${sleep(3)}
#
- test:
    name: 查询转交列表
    api: api/esignDocs/transfer/listTransfer.yml
    variables:
        transferUserCode_list: $userCode001
        flowName_list: ""
        flowId_list: $signFlowId002
        taskId_list: $taskId002
    extract:
      - dataId_todo_001: content.data.list.0.dataId
      - dataId_todo_002: content.data.list.1.dataId
      - dataId_todo_003: content.data.list.2.dataId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.total", 3]
      - eq: ["content.data.list.0.dataType", 1]
      - eq: ["content.data.list.0.dataTypeName",  "填写待办"]
      - eq: ["content.data.list.0.flowId",  "$signFlowId002"]
      - contains: ["content.data.list.0.flowName",  "全链路"]
      - eq: ["content.data.list.0.nodeClassType",  null]
      - eq: ["content.data.list.0.signerOrganizationName",  null]
      - ne: ["content.data.list.0.initiatorName",  null]
      - ne: ["content.data.list.0.initiatorOrganizationName",  null]
- test:
    name: TC14-提交待办任务-signFlowId002-转交填写中的任务成功-转交给sign01
    api: api/esignDocs/transfer/submit.yml
    variables:
      transferUserCode_submit: $userCode001
      receiverUserCode_submit: $userCode002
      receiverDepartmentCode_submit: $orgCode002
      taskId_submit: $taskId002
      electronicSign_submit:
        all: 2
        excludeIdList: []
        includeIdList: ["$dataId_todo_001","$dataId_todo_002","$dataId_todo_003"]
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", null]
    teardown_hooks:
      - ${sleep(6)}

- test:
    name: TC1-10-record-转交记录
    api: api/esignDocs/transfer/record.yml
    variables:
      flowId_record: $signFlowId002
      receiverUserName_record:
      transferUserName_record: ""
      transferType_record: 1
      dataType_record: ""
      startTime_record: ""
      businessTypeId_record: ""
      presetName_record: ""
      fileFormat_record: ""
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message", "成功"]
      - ne: ["content.data", null]
      - eq: ["content.data.total", 3]
#
##查询sign01的待办
- test:
    name: "TC3-查询sign03的待我处理任务-0条"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNo001"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId002
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",0 ]
      - len_eq: [ "content.data.list",0 ]
#
- test:
    name: "TC3-查询sign01的待我处理任务-3条"
    api: api/esignDocs/portal/task/queryUndoTask.yml
    variables:
      - account: "$userNo002"
      - password: ""
      - currPage: 1
      - pageSize: 10
      - businessId: $signFlowId002
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.totalCount",3 ]
      - len_eq: [ "content.data.list",3 ]


- test:
    name: "TC1-填写中-校验新增的出参字段"
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $signFlowId002
    extract:
      _fillingUserInfos002: content.data.fillingUserInfos
#      _fillUrl001: content.data.fillingUserInfos.0.fillUrl
#      _fillUrl002: content.data.fillingUserInfos.1.fillUrl
    validate:
      - ne: [ content.data.fillingUserInfos, null ]
      - ne: [ content.data.businessTypeCode, ""]
      - eq: [ content.data.signerInfos.0.signerType, 1 ]
      - len_eq: [ content.data.signerInfos, 5 ]
      - len_eq: [ content.data.fillingUserInfos, 4 ]
      - eq: [ content.data.signFlowId, "$signFlowId002" ]
#
#sign07去填写其中一个流程
- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token-第一个填写"
    api: api/esignDocs/docGatherForm/querySignerFillFilesInner.yml
    variables:
      - account: $userNo002
      - password: ${ENV(password01)}
      - _fillUrl001: "${dataArraySort($_fillingUserInfos002, fillingUserType,1, fillUrl)}"
      - templateInitiationSignersUuid: "${extractParameterValue($_fillUrl001,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($_fillUrl001,templateInitiationUuid)}"
      - tmp_todo_11: ${putTempEnv(templateInitiationSignersUuid001, $templateInitiationSignersUuid)}
      - tmp_todo_12: ${putTempEnv(templateInitiationUuid001, $templateInitiationUuid)}
    extract:
      - _fillUrl001_1: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token-第2个填写"
    api: api/esignDocs/docGatherForm/querySignerFillFilesInner.yml
    variables:
      - account: $userNo002
      - password: ${ENV(password01)}
      - _fillUrl002: "${dataArraySort($_fillingUserInfos002, fillingUserType,2, fillUrl)}"
      - templateInitiationSignersUuid: "${extractParameterValue($_fillUrl002,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($_fillUrl002,templateInitiationUuid)}"
      - tmp_todo_21: ${putTempEnv(templateInitiationSignersUuid002, $templateInitiationSignersUuid)}
      - tmp_todo_22: ${putTempEnv(templateInitiationUuid002, $templateInitiationUuid)}
    extract:
      - _fillUrl001_2: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-签署人填写的表单信息页查询文件列表-获取token-第3个填写"
    api: api/esignDocs/docGatherForm/querySignerFillFilesInner.yml
    variables:
      - account: $userNo002
      - password: ${ENV(password01)}
      - _fillUrl003: "${dataArraySort($_fillingUserInfos002, fillingUserType,3, fillUrl)}"
      - templateInitiationSignersUuid: "${extractParameterValue($_fillUrl003,templateInitiationSignersUuid)}"
      - templateInitiationUuid: "${extractParameterValue($_fillUrl003,templateInitiationUuid)}"
      - tmp_todo_31: ${putTempEnv(templateInitiationSignersUuid003, $templateInitiationSignersUuid)}
      - tmp_todo_32: ${putTempEnv(templateInitiationUuid003, $templateInitiationUuid)}
    extract:
      - _fillUrl001_3: content.data.fillUrl
    validate:
      - eq: [content.message,"成功" ]
      - eq: [content.status, 200]
      - eq: [content.success, true]




- test:
    name: "获取填写页信息-sign01填写1"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl001_1,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId01: content.data.waitFillFields.0
      - contentId01: content.data.contents.0.contentId
      - documentId01: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]

- test:
    name: "填写页提交"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl001_1,tplToken)}"
      - fieldId_submit: $fieldId01
      - fillValue_submit: "填写页填充1"
      - contentId_submit: $contentId01
      - documentId_submit: $documentId01
    validate:
      - eq: [content.code, 0]
      - eq: [content.success, true]

- test:
    name: "signerFill-填写页-提交-签署人非当前登录人"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(templateInitiationSignersUuid001)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(templateInitiationUuid001)}
      wordList: []
      formValues: []
#    teardown_hooks:
#      - ${sleep(2)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $signFlowId002
    extract:
      - currentHandler1: content.data.list.0.currentHandler
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.currentHandler, "$userCode002,$userCode002"]
      - eq: [content.data.list.0.flowStatusName, "填写中"]
      - eq: [content.data.list.0.signStatus, "0"]
      - len_eq: [content.data.list.0.signUserNames, 5]

- test:
    name: "获取填写页信息-sign01填写2"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl001_2,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId02: content.data.waitFillFields.0
      - contentId02: content.data.contents.0.contentId
      - documentId02: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]

- test:
    name: "填写页提交"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl001_2,tplToken)}"
      - fieldId_submit: $fieldId02
      - fillValue_submit: "填写页填充2"
      - contentId_submit: $contentId02
      - documentId_submit: $documentId02
    validate:
      - eq: [content.code, 0]
      - eq: [content.success, true]

- test:
    name: "signerFill-填写页-提交-签署人非当前登录人"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(templateInitiationSignersUuid002)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(templateInitiationUuid002)}
      wordList: []
      formValues: []
#    teardown_hooks:
#      - ${sleep(2)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $signFlowId002
    extract:
      - currentHandler1: content.data.list.0.currentHandler
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.currentHandler, "$userCode002"]
      - eq: [content.data.list.0.flowStatusName, "填写中"]
      - eq: [content.data.list.0.signStatus, "0"]
      - len_eq: [content.data.list.0.signUserNames, 5]

- test:
    name: "获取填写页信息-sign01填写3"
    api: api/esignDocs/epaasTemplate/get_fill_task_info.yml
    variables:
      - tplToken_info: "${extractParameterValue($_fillUrl001_3,tplToken)}"
      - timestamp_info: $timestamp1
    extract:
      - fieldId03: content.data.waitFillFields.0
      - contentId03: content.data.contents.0.contentId
      - documentId03: content.data.contents.0.documentId
    validate:
      - eq: [content.code, 0]
      - ne: [content.data, null]
      - ne: [content.data.fillTaskStatus, ""]

- test:
    name: "填写页提交"
    api: api/esignDocs/epaasTemplate/submit-fill-task.yml
    variables:
      - tplToken_submit: "${extractParameterValue($_fillUrl001_3,tplToken)}"
      - fieldId_submit: $fieldId03
      - fillValue_submit: "填写页填充3"
      - contentId_submit: $contentId03
      - documentId_submit: $documentId03
    validate:
      - eq: [content.code, 0]
      - eq: [content.success, true]

- test:
    name: "signerFill-填写页-提交-签署人非当前登录人"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: ${ENV(templateInitiationSignersUuid003)}
      hasSubmit: 1
      signerContents:
      templateInitiationUuid:  ${ENV(templateInitiationUuid003)}
      wordList: []
      formValues: []
    teardown_hooks:
      - ${sleep(10)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, '成功']
      - ne: [content.data, null]



- test:
    name: "将sign07更新回去"
    api: api/esignManage/InnerUsers/update.yml
    variables:
      data:
        {
          "userCode": $userCode003,
          "customAccountNo": ,
          "name": ,
          "mobile": ,
          "email": ,
          "licenseType": ,
          "licenseNo": ,
          "bankCardNo": ,
          "mainOrganizationCode": ,
          "mainCustomOrgNo": ,
          "otherOrganization": [
            {
              "otherCustomOrgNo": "",
              "otherOrganizationCode": ""
            }]
        }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.code",200 ]