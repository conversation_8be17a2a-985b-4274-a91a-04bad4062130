#场景说明：双方签署-追加采集信息-填写-提交场景-有审批
#审批流程：开始-》发起流程-〉审批-》签署-〉结束
- config:
    name: "我管理的-对接流程引擎-签署中催办系列"
    variables:
      - maimHost: ${ENV(esign.projectHost)}

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toAudit.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
      processInstanceIdCommon: content.data.list.0.processInstanceId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "获取流程实例处理信息"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
        processInstanceId: $processInstanceIdCommon
    extract:
        processDefinitionKeyCommon: content.data.processDefinitionKey
        businessIdCommon: content.data.nodeInstance.businessId
        taskIdCommon: content.data.nodeInstance.taskId
        nodeConfigCodeCommon: content.data.nextNodeConfigList.0.nodeConfigCode
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: detail-查询内部用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      userCodeDetail: $signerUserCodeCommon
    extract:
      - _mainOrganizationCode: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: "审核提交"
    api: api/esignDocs/flow/submitTaskForAudit.yml
    variables:
      auditOpinion: "自动化测试发起人审核提交"
      businessId: $businessIdCommon
      carbonCopyList: []
      nextAssigneeList:
        - nextAssignee: $signerUserCodeCommon
          nextAssigneeOrganizationCode: $_mainOrganizationCode
      processInstanceId: $processInstanceIdCommon
      requestUrl: "$maimHost/doc-manage-web/home-workFlow?workflowId=$processInstanceIdCommon&bussinessId=$businessIdCommon&noWorkflowCodePageName=FQDZQS"
      sendNotice: 0
      subBusinessId: $businessIdCommon
      todoTaskId: $taskIdCommon
      processDefinitionKey: $processDefinitionKeyCommon
      nodeConfigCode: $nodeConfigCodeCommon
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取流程的当前环节处理人"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $flowIdCommon
    extract:
      currentHandler01: content.data.list.0.currentHandler
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC-我管理的-成功催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $flowIdCommon
      - currentHandler: $currentHandler01
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 1]
      - eq: [content.data.reminderResultDesc, "发起催办成功"]


- test:
    name: "TC-我管理的-未达到间隔时间催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $flowIdCommon
      - currentHandler: $currentHandler01
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 0]
      - contains: [content.data.reminderResultDesc, "已催办"]