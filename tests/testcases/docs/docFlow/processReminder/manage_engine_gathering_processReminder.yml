#场景说明：双方签署-追加采集信息-填写-提交场景-有审批
#审批流程：开始-》发起流程-〉审批-》签署-〉结束
- config:
    name: "我管理的-对接流程引擎-采集中催办系列"


- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_collect_audit.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: "TC-我管理的-成功催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $flowIdCommon
    teardown_hooks:
      - ${sleep(2)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 1]
      - eq: [content.data.reminderResultDesc, "发起催办成功"]


- test:
    name: "TC-我管理的-未达到间隔时间催办"
    api: api/esignDocs/docFlow/manage_processReminder.yml
    variables:
      - flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.reminderResult, 0]
      - contains: [content.data.reminderResultDesc, "已催办"]