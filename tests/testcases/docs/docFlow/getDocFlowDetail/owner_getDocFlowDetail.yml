- config:
    name: "我发起的-查看电子签署详情"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toSign.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我发起的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.businessPresetName, $presetNameCommon]
      - eq: [content.data.flowName, $batchTemplateTaskNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.signerStatus, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - eq: [content.data.signedFileVOList.0.fileName, "key30page.pdf"]
      - str_eq: [content.data.remark, "None"]
      - str_eq: [content.data.signOffTime, "None"]
      - str_eq: [content.data.businessNo, "None"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]