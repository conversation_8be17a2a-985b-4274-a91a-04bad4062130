- config:
    name: "我管理的-查看电子签署详情-openapi发起"

- test:
    name: "创建签署流程"
    testcase: common/signOpenapi/getAutoSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon
      - signFlowExpireTimeCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.flowName, $subjectCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 1]
      - str_eq: [content.data.signerNodeList.0.signerList.0.needGather, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.signerStatus, 2]
#      - eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "已签署"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 0]
      - eq: [content.data.processId, $signFlowIdCommon]
      - eq: [content.data.businessNo, $businessNoCommon]
      - eq: [content.data.signOffTime, $signFlowExpireTimeCommon]
      - str_eq: [content.data.remark, "自动化测试-备注"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]