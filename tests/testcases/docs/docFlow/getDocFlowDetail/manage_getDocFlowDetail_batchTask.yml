- config:
    name: "batchTemplateInitiationName校验"
    variables:
      - batchTemplateTaskName1: "自动化测试批量发起-${get_randomNo_16()}"
      - excelFileName1: "单方签署-外部抄送人.xlsx"
      - excelFileKey1: ${attachment_upload($excelFileName1)}
      - presetIdCommon:  ${getPreset(1,1)}
      - signerId: ${get_randomNo_32()}
      - attachmentFileKeyCommon: ${ENV(fileKey)}

- test:
    name: "业务业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]


- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存批量任务1"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "checkExcel"
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      excelFileKey:  $excelFileKey1
    api: api/esignDocs/batchTemplateInitiation/checkExcel.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-提交批量任务"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskName1,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "excelFileKey":  $excelFileKey1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "remark": "自动化测试-备注",
        "saveSigners": {
          "batchTemplateSignerList": []
        },
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "signerId": $signerId
          }
          ]
        }
        ],
        "type": 1,
        "attachments": [
        {
          "fileName": "测试附件.pdf",
          "fileKey": $attachmentFileKeyCommon
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskName1
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
    teardown_hooks:
      - ${sleep(1)}

- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.businessPresetName, $presetNameCommon]
      - eq: [content.data.flowName, $batchTemplateTaskName1]
      - eq: [content.data.signerNodeList.0.signerList.0.userName, "测试文档中心自动化用户勿改动"]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 0]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "1"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, null]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 0]
      - contains: [content.data.signedFileVOList.0.fileName, "key30page.pdf"]
      - str_eq: [content.data.remark, "自动化测试-备注"]
      - str_eq: [content.data.signOffTime, "2024-12-31"]
      - str_eq: [content.data.businessNo, "自动化测试-业务编码"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 1]
      - eq: [content.data.copyViewerVOList.0.userName, "测试文档中心自动化外部用户勿改动"]
      - eq: [content.data.attachmentSignedFileVOList.0.fileName, "测试附件.pdf"]