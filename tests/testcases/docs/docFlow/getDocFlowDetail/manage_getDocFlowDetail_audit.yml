- config:
    name: "我管理的-查看电子签署详情-待审批状态"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toAudit.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - auditUserCodeCommon
      - presetNameCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
      processInstanceIdCommon: content.data.list.0.processInstanceId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "列表查询-flowid+状态"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowStatus: 2
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - contains: [content.data.list.0.flowStatusName, "审批"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandler, "$auditUserCodeCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserCodeCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]


- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 0]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "None"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - str_eq: [content.data.processId,  ""]
      - str_eq: [content.data.businessNo, "None"]
      - str_eq: [content.data.signOffTime, "None"]
      - str_eq: [content.data.remark, "None"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]

- test:
    name: "查询流程实例--获取某个流程实例"
    api: api/esignDocs/flow/getWorkflowInstance.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - len_eq: [ content.data.nodeInstanceList, 2 ]
      - contains: [ content.data.nodeInstanceList.0.nodeConfigName, "审批" ]
      - str_eq: [ content.data.nodeInstanceList.0.auditOpinion, None ]
      - eq: [ content.data.nodeInstanceList.1.nodeConfigName, "发起电子签署" ]
