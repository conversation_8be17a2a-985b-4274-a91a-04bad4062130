- config:
    name: "我管理的-查看电子签署详情-openapi发起"

- test:
    name: "创建签署流程"
    variables:
        initiatorUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
        initiatorUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.account)}
        initiatorOrgCodeCommon: ''
        initiatorOrganizationCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
    testcase: common/signOpenapi/getSignFlow_initiator.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - signFlowIdCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      processId: $signFlowIdCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我发起的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/owner_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.data.flowId, $flowIdCommon]
