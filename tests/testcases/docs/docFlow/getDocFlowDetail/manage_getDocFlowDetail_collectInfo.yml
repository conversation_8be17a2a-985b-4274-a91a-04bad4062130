- config:
    name: "我管理的-查看电子签署详情-提交采集，待签署的流程"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_collect.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon
      - batchTemplateInitiationUuid1

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      templateInitiationSignersUuid1: content.data.signerNodeList.0.signerList.0.templateInitiationSignersUuid
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.businessPresetName, $presetNameCommon]
      - eq: [content.data.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 1]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "None"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - eq: [content.data.signedFileVOList.0.fileName, "测试.pdf"]
      - str_eq: [content.data.remark, "None"]
      - str_eq: [content.data.signOffTime, "None"]
      - str_eq: [content.data.businessNo, "None"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]

- test:
    name: "提交采集任务"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: $templateInitiationSignersUuid1
      hasSubmit: 1
      signerContents: []
      templateInitiationUuid:  $batchTemplateInitiationUuid1
      wordList: []
      phoneOrMail: "wsignwb01.account.encrypt"
      formValues: [{"elementValue":"表单内容提交测试","elementKey":"wgv6gtlj","elementType":"input","elementName":"单行文本","formatType":"","formatRule":"","asSignFile":"0","model":"input_wgv6gtlj"}]
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]

- test:
    name: "TC2-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      templateInitiationSignersUuid1: content.data.signerNodeList.0.signerList.0.templateInitiationSignersUuid
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.businessPresetName, $presetNameCommon]
      - eq: [content.data.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerStatus, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - eq: [content.data.signedFileVOList.0.fileName, "测试.pdf"]
      - str_eq: [content.data.remark, "None"]
      - str_eq: [content.data.signOffTime, "None"]
      - str_eq: [content.data.businessNo, "None"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]


- test:
    name: "tC3-校验表单内容"
    api: api/esignDocs/docGatherForm/queryGatherFormValue.yml
    variables:
      templateInitiationSignersUuid: $templateInitiationSignersUuid1
      account: "内部"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.formJson, 0]
      - contains: [content.data.formJson, "单行文本"]
      - eq: [content.data.formValues.0.elementValue, "表单内容提交测试"]