- config:
    name: "采集提交后，在审批环节驳回，再次提交采集"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_collect_audit.yml
    teardown_hooks:
      - ${sleep(10)}
    output:
      - auditOrgNameCommon
      - auditUserNameCommon
      - auditOrgCodeCommon
      - auditUserCodeCommon
      - batchTemplateTaskNameCommon
      - batchTemplateInitiationUuid1
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon
      - initiatorUserNameCommon
      - initiatorUserNameCommon
      - presetIdCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - toSignFileKeyCommon
      - mainHost

- test:
    name: "获取发起流程的flowId--填写中的列表详情"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      processInstanceIdCommon: content.data.list.0.processInstanceId
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.flowStatusName, "填写中"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, $signerUserNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]

- test:
    name: "获取templateInitiationSignersUuid1"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      templateInitiationSignersUuid1: content.data.signerNodeList.0.signerList.0.templateInitiationSignersUuid

- test:
    name: "提交采集任务"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      phoneOrMail: "wsignwb01.account.encrypt"
      templateInitiationSignersUuid: $templateInitiationSignersUuid1
      hasSubmit: 1
      signerContents: []
      templateInitiationUuid:  $batchTemplateInitiationUuid1
      wordList: []
#      phoneOrMail: "wsignwb01.account.encrypt"
      formValues: [{"elementValue":"表单内容提交测试","elementKey":"wgv6gtlj","elementType":"input","elementName":"单行文本","formatType":"","formatRule":"","asSignFile":"0","model":"input_wgv6gtlj"}]
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]


- test:
    name: "获取processInstanceId"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      processInstanceIdCommon: content.data.processInstanceId


- test:
    name: "查询流程实例--内部个人--发起人审核环节"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    extract:
      - taskIdCommon: content.data.nodeInstance.taskId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "查询驳回环节列表"
    api: api/esignDocs/flow/getTurnDownNodeList.yml
    variables:
      processInstanceId: $processInstanceIdCommon
    extract:
      - nodeCodeCommon: content.data.0.nodeCode
      - distTodoTaskIdCommon: content.data.0.id
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "审批驳回"
    api: api/esignDocs/flow/turnDownNodeInstance.yml
    variables:
      businessId: $batchTemplateInitiationUuid1
      nodeConfigCode: $nodeCodeCommon
      processInstanceId: $processInstanceIdCommon
      requestUrl: "${ENV(esign.projectHost)}/doc-manage-web/home-workFlow?workflowId=$processInstanceIdCommon&bussinessId=$batchTemplateInitiationUuid1&noWorkflowCodePageName=$nodeCodeCommon"
      sendNotice: 1
      distTodoTaskId: $distTodoTaskIdCommon
      todoTaskId: $taskIdCommon
      auditOpinion: "自动化测试-审批驳回"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]


- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "None"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]


- test:
    name: "查询流程实例--获取某个流程实例"
    api: api/esignDocs/flow/getWorkflowInstance.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - len_eq: [ content.data.nodeInstanceList, 3 ]
      - eq: [ content.data.nodeInstanceList.0.nodeConfigName, "发起电子签署-驳回" ]
      - eq: [ content.data.nodeInstanceList.1.auditOpinion, "自动化测试-审批驳回" ]
      - eq: [ content.data.nodeInstanceList.1.auditResult, "2" ]
      - contains: [ content.data.nodeInstanceList.1.nodeConfigName, "审批" ]
      - eq: [ content.data.nodeInstanceList.2.nodeConfigName, "发起电子签署" ]


- test:
    name: "获取templateInitiationSignersUuid2"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      templateInitiationSignersUuid2: content.data.signerNodeList.0.signerList.0.templateInitiationSignersUuid

- test:
    name: 修改作废类型的合同到期时间signFlowExpireTime
    variables:
      signFlowId: $flowIdCommon
      businessNo: ""
      signFlowExpireTime: "2053-10-01 14:00:00"
      contractExpireTime: "2051-10-01 13:00:00"
    api: api/esignDocs/signFlow/update.yml
    validate:
      - eq: [content.code, 1630027]
      - eq: [content.message, "当前流程状态不支持修改签署截止日期"]
      - eq: [content.data, null]
- test:
    name: "TC-校验表单内容"
    api: api/esignDocs/docGatherForm/queryGatherFormValue.yml
    variables:
      templateInitiationSignersUuid: $templateInitiationSignersUuid2
      account: "内部"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.formJson, 0]
      - contains: [content.data.formJson, "单行文本"]
      - str_eq: [content.data.formValues, "None"]

- test:
    name: "编辑批量任务-获取batchTemplateInitiationUuid2"
    api: api/esignDocs/batchTemplateInitiation/edit/manage.yml
    variables:
      flowId: $flowIdCommon
      processInstanceId: $processInstanceIdCommon
    extract:
      - batchTemplateInitiationUuid2: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]


- test:
    name: "getInfo-获取batchTemplateInitiationUuid2"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      params:
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid2
    extract:
      - templateInitiationSignersUuid1: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerSnapshotId1: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]

- test:
    name: "提交单次任务-内部个人-单文档自由签"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid2,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "flowId": $processInstanceIdCommon,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 2,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "signerSnapshotId": $signerSnapshotId1,
            "templateInitiationSignersUuid": $templateInitiationSignersUuid1
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [],
          "nextAssigneeList": [
          {
            "nextAssignee": $auditUserCodeCommon,
            "nextAssigneeOrganizationCode": $auditOrgCodeCommon,
            "nextAssigneeName": $auditUserNameCommon,
            "nextAssigneeOrganizationName": $auditOrgNameCommon
          }
          ],
          "nodeConfigCode": "BMLDSP",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid1",
          "sendNotice": "0",
          "variables": {}
        }
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon2: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "再次提交采集任务"
    api: api/esignDocs/docGatherForm/signerFill.yml
    variables:
      templateInitiationSignersUuid: $templateInitiationSignersUuid2
      hasSubmit: 1
      signerContents: []
      templateInitiationUuid:  $batchTemplateInitiationUuid2
      wordList: []
      phoneOrMail: "wsignwb01.account.encrypt"
      formValues: [{"elementValue":"2表单内容提交测试2","elementKey":"wgv6gtlj","elementType":"input","elementName":"单行文本","formatType":"","formatRule":"","asSignFile":"0","model":"input_wgv6gtlj"}]
    teardown_hooks:
      - ${sleep(5)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]

- test:
    name: "获取templateInitiationSignersUuid1"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    extract:
      templateInitiationSignersUuid1: content.data.signerNodeList.0.signerList.0.templateInitiationSignersUuid

- test:
    name: "tC3-校验表单内容"
    api: api/esignDocs/docGatherForm/queryGatherFormValue.yml
    variables:
      templateInitiationSignersUuid: $templateInitiationSignersUuid2
      account: "内部"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - len_gt: [content.data.formJson, 0]
      - contains: [content.data.formJson, "单行文本"]
      - eq: [content.data.formValues.0.elementValue, "2表单内容提交测试2"]

- test:
    name: "查询流程实例--获取某个流程实例"
    api: api/esignDocs/flow/getWorkflowInstance.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - len_eq: [ content.data.nodeInstanceList, 4 ]
      - contains: [ content.data.nodeInstanceList.0.nodeConfigName, "审批" ]
      - eq: [ content.data.nodeInstanceList.1.nodeConfigName, "发起电子签署-驳回" ]
      - contains: [ content.data.nodeInstanceList.2.nodeConfigName, "审批" ]
      - eq: [ content.data.nodeInstanceList.3.nodeConfigName, "发起电子签署" ]
      - eq: [ content.data.nodeInstanceList.2.auditOpinion, "自动化测试-审批驳回" ]