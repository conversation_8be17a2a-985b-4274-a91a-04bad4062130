- config:
    name: "我管理的-查看电子签署详情-openapi发起"

- test:
    name: "创建签署流程"
    testcase: common/signOpenapi/getSignFlow_initiator.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - signFlowIdCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      processId: $signFlowIdCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/withoutPermission_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.processId, $signFlowIdCommon]
