- config:
    variables:
      initiatorUserNameCommon: ${ENV(sign01.userName)}
      initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      initiatorOrgCodeCommon: ${getUserMainOrg($initiatorUserCodeCommon)}
      initiatorOrgNameCommon: ${get_orgInfo(None,$initiatorOrgCodeCommon)}
      businessNoCommon: "文档自动化测试-编码-${get_randomNo_16()}"
      subjectCommon: "文档自动化测试-主题-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      signerUserCodeCommon1: ${ENV(ceswdzxzdhyhwgd1.userCode)}
      signerUserNameCommon1: ${ENV(ceswdzxzdhyhwgd1.userName)}
      signerUserCodeCommon2: ${ENV(wsignwb01.userCode)}
      signerUserNameCommon2: ${ENV(wsignwb01.userName)}
      signerUserCodeCommon3: ${ENV(userCode)}
      signerUserNameCommon3: ${ENV(userName)}
      signerUserCodeCommon4: ${ENV(sign04.userCode)}
      signerUserNameCommon4: ${ENV(sign04.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      businessTypeCodeCommon: ${ENV(businessTypeCode)}


- test:
    name: "调用签署openapi创建并发起流程"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": $subjectCommon,
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "userType": "1",
          "signNode": 1,
          "signMode": 0,
          "signOrder": 1,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon
          }
          ]
        },        {
          "userCode": $signerUserCodeCommon1,
          "userType": "1",
          "signNode": 1,
          "signMode": 0,
          "signOrder": 2,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon
          }
          ]
        }, {
          "userCode": $signerUserCodeCommon2,
          "userType": "2",
          "signNode": 2,
          "signOrder": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon
          }
          ]
        },{
          "userCode": $signerUserCodeCommon3,
          "userType": "1",
          "signNode": 2,
          "signOrder": 2,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon
          }
          ]
        }
        ]
      }
    extract:
      signFlowId: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]

- test:
    name: TC5-电子签署列表-通过processId存在查询
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId
    extract:
      signFlowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 1 ]
      - contains: [ json.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - contains: [ json.data.list.0.currentHandlerName, $signerUserNameCommon1]
      - len_eq: [json.data.list.0.signerUserNameList, 4]
- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $signFlowIdCommon
    extract:
      filekey: content.data.signedFileVOList.0.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $signFlowIdCommon]
      - eq: [content.data.flowName, $subjectCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - str_eq: [content.data.signerNodeList.0.signerList.0.needGather, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - eq: [content.data.processId, $signFlowId]
      - eq: [content.data.businessNo, $businessNoCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.signNode, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.signOrder, 1]
      - eq: [content.data.signerNodeList.0.signerList.0.userName, "$signerUserNameCommon"]
      - eq: [content.data.signerNodeList.0.signerList.1.signNode, 1]
      - eq: [content.data.signerNodeList.0.signerList.1.signOrder, 2]
      - eq: [content.data.signerNodeList.0.signerList.1.userName, "$signerUserNameCommon1"]
      - eq: [content.data.signerNodeList.1.signerList.0.signNode, 2]
      - eq: [content.data.signerNodeList.1.signerList.0.signOrder, 1]
      - eq: [content.data.signerNodeList.1.signerList.0.userName, "$signerUserNameCommon2"]
      - eq: [content.data.signerNodeList.1.signerList.1.signNode, 2]
      - eq: [content.data.signerNodeList.1.signerList.1.signOrder, 2]
      - eq: [content.data.signerNodeList.1.signerList.1.userName, "$signerUserNameCommon3"]
      - str_eq: [content.data.remark, "None"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.copyViewerVOList, 0]

- test:
    name: "TC1-图片下载-sign为空"
    api: api/esignDocs/fileSystem/fileSystemimageDownload.yml
    variables:
      fileKey: $filekey
      pageNo: 1
      sign: ""
    validate:
      - eq: [content.status, 1600017]
      - eq: [content.success, false]
      - eq: [content.message, "sign不能为空"]

- test:
    name: "TC1-图片下载-页码为空"
    api: api/esignDocs/fileSystem/fileSystemimageDownload.yml
    variables:
      fileKey: $filekey
      pageNo: ""
      sign: ""
    validate:
      - eq: [content.status, 1600017]
      - eq: [content.success, false]
      - eq: [content.message, "页码不能为空"]

- test:
    name: "TC1-图片下载-filekey为空"
    api: api/esignDocs/fileSystem/fileSystemimageDownload.yml
    variables:
      fileKey: ""
      pageNo: ""
      sign: ""
    validate:
      - eq: [content.status, 1600017]
      - eq: [content.success, false]
      - eq: [content.message, "fileKey不能为空"]


- test:
    name: "TC1-图片下载-signedFileImagePreview正常-我管理的"
    api: api/esignDocs/fileSystem/signedFileImagePreviewmanage.yml
    variables:
      fileKey: $filekey
      flowId: "$signFlowIdCommon"
    extract:
      signId: content.data.sign
      pageNo1: content.data.pageNumber
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - not_equals: [content.data.pageNumber, null]
      - not_equals: [$signId, null]

- test:
    name: "TC1-图片下载-signedFileImagePreview正常-我发起的"
    api: api/esignDocs/fileSystem/signedFileImagePreviewOwn.yml
    variables:
      fileKey: $filekey
      flowId: "$signFlowIdCommon"
    extract:
      signId: content.data.sign
      pageNo1: content.data.pageNumber
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - not_equals: [content.data.pageNumber, null]
      - not_equals: [$signId, null]

- test:
    name: "TC1-图片下载-signedFileImagePreview正常-我已处理"
    api: api/esignDocs/fileSystem/signedFileImagePreview.yml
    variables:
      fileKey: $filekey
      flowId: "$signFlowIdCommon"
    extract:
      signId2: content.data.sign
      pageNo2: content.data.pageNumber
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - not_equals: [content.data.pageNumber, null]
      - not_equals: [$signId, null]

- test:
    name: "TC1-图片下载-正常"
    api: api/esignDocs/fileSystem/fileSystemimageDownload.yml
    variables:
      fileKey: $filekey
      pageNo: $pageNo1
      sign: $signId
    validate:
      - not_equals: [text, null]

- test:
    name: "TC1-图片下载-sign不对，权限不足"
    api: api/esignDocs/fileSystem/fileSystemimageDownload.yml
    variables:
      fileKey: $filekey
      pageNo: $pageNo1
      sign: 11111
    validate:
      - eq: [ content.status, 1699002 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "无操作权限，请联系管理员" ]
      - eq: [ content.data, null ]



#上传验签文件
-   test:
        name: "上传签署文件,提取fileKey"
        api: api/esignDocs/fileSystem/signVerifyUpload.yml
        extract:
            -   ex_flowId: content.data.flowId
        validate:
            -   contains: [content.message, "成功"]
            -   ne: [content.data, '']
            -   eq: [content.success, true]


- test:
    name: "TC1-图片下载-页码为空"
    api: api/esignDocs/fileSystem/nonloginmageDownload.yml
    variables:
      fileKey: ""
      pageNo: ""
      flowId: "$ex_flowId"
    validate:
      - eq: [content.status, 1703001]
      - eq: [content.success, false]
      - eq: [content.message, "pageNo 不能为空"]

- test:
    name: "TC1-图片下载-验签文件过期"
    api: api/esignDocs/fileSystem/nonloginmageDownload.yml
    variables:
      fileKey: $filekey
      pageNo: $pageNo1
    validate:
      - eq: [ content.status, 1705024 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "验签文件已过期，请重新上传验签" ]
      - eq: [ content.data, null ]

- test:
    name: "TC1-图片下载-正常"
    api: api/esignDocs/fileSystem/nonloginmageDownload.yml
    variables:
      flowId: $ex_flowId
      pageNo: $pageNo1
    validate:
      - not_equals: [text, null]

- test:
    name: "TC1-taskId为空"
    api: api/esignDocs/fileSystem/signedFileImageDownload.yml
    variables:
      pageNo: $pageNo1
      taskId1: ""
    validate:
      - eq: [ content.status, 1706013 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "文件不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "TC1-图片下载-正常"
    api: api/esignDocs/fileSystem/signedFileImageDownload.yml
    variables:
      pageNo: $pageNo1
      taskId1: "1111"
    validate:
      - eq: [ content.status, 1706013 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "文件不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "TC1-二维码图片下载-正常"
    api: api/esignDocs/fileSystem/signFilenonloginmageDownload.yml
    variables:
      pageNo: $pageNo1
      processId: "$signFlowId"
      fileKey: $filekey
    validate:
      - eq: [ content.status, 1701046 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "无文件操作权限" ]
      - eq: [ content.data, null ]

- test:
    name: "TC1-二维码图片下载-page为空"
    api: api/esignDocs/fileSystem/signFilenonloginmageDownload.yml
    variables:
      pageNo: ""
      processId: "$signFlowId"
      fileKey: $filekey
    validate:
      - eq: [ content.status, 1703001 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "页码不能为空" ]
      - eq: [ content.data, null ]

- test:
    name: "TC1-二维码图片下载-processId为空"
    api: api/esignDocs/fileSystem/signFilenonloginmageDownload.yml
    variables:
      pageNo: 1
      processId: ""
      fileKey: $filekey
    validate:
      - eq: [ content.status, 1702007 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "流程不存在" ]
      - eq: [ content.data, null ]

#- test:
#    name: "TC1-二维码图片下载-filekey为空"
#    api: api/esignDocs/fileSystem/signFilenonloginmageDownload.yml
#    variables:
#      pageNo: 1
#      processId: "$signFlowId"
#      fileKey: ""
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data, null ]

- test:
    name: "TC1-二维码图片下载-filekey是其他的"
    api: api/esignDocs/fileSystem/signFilenonloginmageDownload.yml
    variables:
      pageNo: 1
      processId: "$signFlowId"
      fileKey: "$toSignFileKeyCommon"
    validate:
      - eq: [ content.status, 1701046 ]
      - eq: [ content.success, false ]
      - eq: [ content.message, "无文件操作权限" ]
      - eq: [ content.data, null ]