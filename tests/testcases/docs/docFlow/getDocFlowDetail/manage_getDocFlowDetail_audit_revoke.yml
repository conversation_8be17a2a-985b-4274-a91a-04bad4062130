- config:
    name: "我管理的-查看电子签署详情-审批后驳回"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toAudit.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - auditUserCodeCommon
      - presetNameCommon
      - batchTemplateInitiationUuid1

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      processInstanceIdCommon: content.data.list.0.processInstanceId
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "查询流程实例--内部个人--发起人审核环节"
    api: api/esignDocs/flow/getWorkflowInstanceHandleInfo.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    extract:
      - taskIdCommon: content.data.nodeInstance.taskId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "查询驳回环节列表"
    api: api/esignDocs/flow/getTurnDownNodeList.yml
    variables:
      processInstanceId: $processInstanceIdCommon
    extract:
      - nodeCodeCommon: content.data.0.nodeCode
      - distTodoTaskIdCommon: content.data.0.id
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "审批驳回"
    api: api/esignDocs/flow/turnDownNodeInstance.yml
    variables:
      businessId: $batchTemplateInitiationUuid1
      nodeConfigCode: $nodeCodeCommon
      processInstanceId: $processInstanceIdCommon
      requestUrl: "${ENV(esign.projectHost)}/doc-manage-web/home-workFlow?workflowId=$processInstanceIdCommon&bussinessId=$batchTemplateInitiationUuid1&noWorkflowCodePageName=$nodeCodeCommon"
      sendNotice: 1
      distTodoTaskId: $distTodoTaskIdCommon
      todoTaskId: $taskIdCommon
      auditOpinion: "自动化测试-审批驳回"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]


- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "None"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]


- test:
    name: "查询流程实例--获取某个流程实例"
    api: api/esignDocs/flow/getWorkflowInstance.yml
    variables:
      - processInstanceId: $processInstanceIdCommon
    validate:
      - eq: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - len_eq: [ content.data.nodeInstanceList, 3 ]
      - eq: [ content.data.nodeInstanceList.0.nodeConfigName, "发起电子签署-驳回" ]
#      - eq: [ content.data.nodeInstanceList.1.auditOpinion, "自动化测试-审批驳回" ]
#      - eq: [ content.data.nodeInstanceList.1.auditResult, "2" ]
      - contains: [ content.data.nodeInstanceList.1.nodeConfigName, "审批" ]
      - eq: [ content.data.nodeInstanceList.2.nodeConfigName, "发起电子签署" ]
