- config:
    name: "单次发起"

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_cc_atachment.yml
    teardown_hooks:
      - ${sleep(5)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon
      - batchTemplateInitiationUuid1
      - CCOutUserNameCommon
      - CCOutUserCodeCommon
      - CCOutOrgCodeCommon
      - CCOutOrgNameCommon
      - signFlowExpireTimeCommon
      - signFlowExpireDateCommon

- test:
    name: "获取发起流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC1-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.data.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.initiatorDepartmentName, $initiatorOrgNameCommon]
      - eq: [content.data.flowId, $flowIdCommon]
      - eq: [content.data.businessPresetName, $presetNameCommon]
      - eq: [content.data.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.signerNodeList.0.signerList.0.userName, $signerUserNameCommon]
      - eq: [content.data.signerNodeList.0.signerList.0.autoSign, 0]
      - eq: [content.data.signerNodeList.0.signerList.0.needGather, 0]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatus, "1"]
      - str_eq: [content.data.signerNodeList.0.signerList.0.signerStatusStr, "None"]
      - eq: [content.data.signerNodeList.0.signerList.0.onlyUkeySign, 2]
      - eq: [content.data.signedFileVOList.0.fileName, "key30page.pdf"]
      - str_eq: [content.data.remark, "自动化测试-备注"]
      - str_eq: [content.data.signOffTime, $signFlowExpireDateCommon]
      - str_eq: [content.data.businessNo, "自动化测试-业务编码"]
      - len_eq: [content.data.signedFileVOList, 1]
      - len_eq: [content.data.signerNodeList, 1]
      - len_eq: [content.data.copyViewerVOList, 1]
      - eq: [content.data.copyViewerVOList.0.userName, $CCOutUserNameCommon]
      - eq: [content.data.copyViewerVOList.0.userCode, $CCOutUserCodeCommon]
      - eq: [content.data.copyViewerVOList.0.organizeName, $CCOutOrgNameCommon]
      - eq: [content.data.copyViewerVOList.0.organizeCode, $CCOutOrgCodeCommon]
      - eq: [content.data.attachmentSignedFileVOList.0.fileName, "测试附件.pdf"]