-   config:
        name: "(我发起的)批量催办"

-   test:
        name: "TC1-组合符合催办条件的流程"
        api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
        variables:
            flowStatus: '9'

        extract:
            flowId0: content.data.list.0.flowId
            currentHandler0: content.data.list.0.currentHandler

            flowId1: content.data.list.1.flowId
            currentHandler1: content.data.list.1.currentHandler

            flowId2: content.data.list.2.flowId
            currentHandler2: content.data.list.2.currentHandler

            flowId3: content.data.list.3.flowId
            currentHandler3: content.data.list.3.currentHandler

        validate:
            -   eq: [status_code, 200]
            -   eq: [content.success, true]
            -   eq: [content.message, '成功']


-   test:
        name: "我发起的-开始批量催办"
        api: api/esignDocs/docFlow/processBatchReminder-api.yml
        variables:
            flowRemindList:
                -   flowId: $flowId0
                    currentHandler: $currentHandler0

                -   flowId: $flowId1
                    currentHandler: $currentHandler1

                -   flowId: $flowId2
                    currentHandler: $currentHandler2

                -   flowId: $flowId3
                    currentHandler: $currentHandler3
        validate:
            -   eq: [content.data, True]
            -   eq: [content.success, True]
            -   eq: [content.message, '成功']