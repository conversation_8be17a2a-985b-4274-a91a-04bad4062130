- config:
    name: "电子签署-我发起的-导出签署文档"
    variables:
      second: 40
      randomCount: ${getDateTime()}

- test:
    name: setup-创建签署流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      userCodeSigner: ${ENV(sign01.userCode)}
      signatureType: "PERSON-SEAL"
      organizationCodeInitiator:  ${ENV(sign01.main.orgCode)}
      userCodeInitiator:  ${ENV(sign01.userCode)}
      businessNo: ${substring($randomCount,0,6)}
      subject: "一步发起流程主题${substring($randomCount,0,6)}"
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [ content.data.signFlowStatus, 1 ]
    extract:
      - flowIdCommon: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}

#- test:
#    name: "获取电子签署流程flowId"
#    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
#    variables:
#      flowName:
#    extract:
#      flowIdCommon: content.data.list.0.flowId
#    validate:
#      - eq: [status_code, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "成功"]
#      - gt: [content.data.total, 0]

- test:
    name: "导出签署文档"
    api: api/esignDocs/docFlow/owner_export.yml
    variables:
      includeIdList: ["$flowIdCommon"]
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data, null]

- test:
    name: TC-sign01的后台任务
    api: api/esignSigns/portal/getBackendTaskList.yml
    variables:
      authorizationBackendTaskList: "${getPortalToken()}"
      todayDate: ${getDateTime(0)}
      todayDateStr: ${substring($todayDate,0,8)}
    teardown_hooks:
      - ${sleep($second)}
    validate:
       - eq: [ json.status, 200 ]
       - eq: [ json.success, true ]
       - contains: [ json.data.list.0.name, "批量导出-电子签署" ]
       - contains: [ json.data.list.0.fileName, "批量导出-电子签署-$todayDateStr" ]


#- test:
#    name: "导出签署文档"
#    api: api/esignDocs/docFlow/owner_export.yml
#    variables:
#      includeIdList: []
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "成功"]
#      - eq: [content.data, null]