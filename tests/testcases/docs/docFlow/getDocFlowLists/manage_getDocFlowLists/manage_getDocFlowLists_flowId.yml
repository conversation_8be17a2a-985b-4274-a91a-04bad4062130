- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon

- test:
    name: "获取flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
      businessNo: $businessNoCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "TC1-根据流程id查询成功"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]

- test:
    name: "TC2-根据流程id查询成功-模糊搜索"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: ${substring($flowIdCommon,2,4)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC3-根据流程id查询(中间有空格)"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: ${middleAddSpace($flowIdCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC4-根据流程id查询(首尾空格)"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: ${addSpace($flowIdCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]


- test:
    name: "TC5-根据第三方签署业务id查询-异常字符"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: "%"
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, True,]
      - eq: [content.message, "成功"]
