- config:
    name: "根据发起人姓名查询"
    variables:
      - initiatorUserNameCommon0: ${ENV(csqs.userName)}

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - signerUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon

- test:
    name: "TC1-根据发起人姓名查询成功"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      initiatorUserName: $initiatorUserNameCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]

- test:
    name: "TC2-根据发起人姓名查询成功-模糊查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      initiatorUserName: "测试"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.initiatorUserName, "测试"]

- test:
    name: "TC3-根据发起人姓名查询成功-首尾空格"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      initiatorUserName: ${addSpace($initiatorUserNameCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]

- test:
    name: "TC4-根据发起人姓名查询为空-中间空格"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      initiatorUserName: ${middleAddSpace($initiatorUserNameCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC5-根据发起人姓名查询为空-特殊字符"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      initiatorUserName: "%"
    validate:
      - eq: [content.status, 917]
      - eq: [content.success, false]
      - eq: [content.message, "包含非法字符，存在sql注入语句"]
