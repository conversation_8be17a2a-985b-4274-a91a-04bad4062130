- name: "根据动态编码查询"

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getAutoSignFlow_dynamic.yml
    teardown_hooks:
      - ${sleep(10)}
    extract:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgNameCommon
      - dynamicCodeCommon
      - signerOrgNameCommon


- test:
    name: "TC1-根据动态编码查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      dynamicCode: $dynamicCodeCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]

- test:
    name: "TC2-根据动态编码查询-模糊查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      dynamicCode: ${substring($dynamicCodeCommon,1,3)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]


- test:
    name: "TC3-根据动态编码查询-首尾空格查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      dynamicCode: ${addSpace($dynamicCodeCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]

- test:
    name: "TC4-根据动态编码查询-中间空格查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      dynamicCode: ${middleAddSpace($dynamicCodeCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC5-根据动态编码查询-特殊字符"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      dynamicCode: "%"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

#####追加已签文件的校验-使用动态编码查询-签署完成到已签要等很久######
- test:
    name: "我管理的-已签署文件流程列表"
    api: api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      - "fileName": ""
      - "initiatorUserName": ""
      - "initiatorOrganizeName": ""
      - "signerUserName": ""
      - "signOrgName": ""
      - "signMode": ""
      - "includeSignedFileProcessUuidList": [ ]
      - "excludeSignedFileProcessUuidList": [ ]
      - "flowName": ""
      - "gmtSignFinishStart": ""
      - "gmtSignFinishEnd": ""
      - "page": 1
      - "size": 10
      - "dynamicCode": $dynamicCodeCommon
      - "viewType": 2
      - "businessNo": "$businessNoCommon"
      - "createOrgCodes":  ["$initiatorOrgNameCommon"]
#    extract:
#      - signedFileProcessUuid: content.data.signFileProcessVOList.0.signedFileProcessUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ge: [ "content.data.total", 0 ]