- config:
    name: "查询页面发起的流程信息"
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      tomorrowTime: ${getTomorrowTime()}
      todayDate: ${getNowDate()}

- test:
    name: "创建签署流程"
    testcase: common/submit/getFlowId_toSign.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon

- test:
    name: "TC1-根据流程主题搜索"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $batchTemplateTaskNameCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]


- test:
    name: "TC9-组合查询-所有条件"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowName: $batchTemplateTaskNameCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      initiatorUserName: $initiatorUserNameCommon
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      flowStatus: 13
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]



- test:
    name: "TC10-组合查询-所有条件+最近处理时间"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowName: $batchTemplateTaskNameCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      initiatorUserName: $initiatorUserNameCommon
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      flowStatus: 13
      gmModifiedBegin: $todayZeroTime
      gmtModifiedEnd: $tomorrowTime
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $batchTemplateTaskNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "电子签署"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtModified, $todayDate]


