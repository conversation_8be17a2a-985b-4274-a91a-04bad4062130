- config:
   name: "根据签署人姓名查询"
   variables:
      startTime: ${getDateTime(-1,1)}
      endTime: ${getDateTime(1,1)}

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - signerUserNameCommon
      - signerUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon

- test:
    name: "TC1-根据签署人姓名查询成功"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: $signerUserNameCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]

- test:
    name: "TC2-根据签署人姓名查询成功-模糊查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: ${substring($signerUserNameCommon,0,3)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.signerUserNameList.0, "测试"]

- test:
    name: "TC3-根据签署人姓名查询成功-首尾空格"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: ${addSpace($signerUserNameCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]

- test:
    name: "TC4-根据签署人姓名查询为空-中间空格"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: ${middleAddSpace($signerUserNameCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC5-根据签署人姓名查询为空-特殊字符"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: "%"
    validate:
      - eq: [content.status, 917]
      - eq: [content.success, false]
      - eq: [content.message, "包含非法字符，存在sql注入语句"]
