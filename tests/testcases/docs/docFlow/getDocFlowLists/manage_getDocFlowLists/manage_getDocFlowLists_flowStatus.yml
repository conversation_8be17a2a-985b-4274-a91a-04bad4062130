- test:
    name: "通过流程状态正常查询文档流程-状态为空"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowStatus: ""
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon
- test:
    name: "获取flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
      businessNo: $businessNoCommon
      initiatorUserName: $initiatorUserNameCommon
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      flowIdOrProcessId: $signFlowIdCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]


- test:
    name: "TC1-根据流程id和状态查询成功---签署中的流程"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $flowIdCommon
      flowStatus: "13"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.flowStatusName, "签署中"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "$initiatorUserNameCommon"]
      - eq: [content.data.list.0.initiatorOrganizeName, "$initiatorOrgNameCommon"]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, "$signerUserNameCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - eq: [content.data.list.0.gmtFinish, null]

- test:
    name: "TC1-根据流程id和状态查询成功---已过期的的流程"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: ${ENV(pdf.overdue.processId)}
      flowStatus: "6"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowStatusName, "已过期"]
      - eq: [content.data.list.0.projectName, "天印6.0"]
      - eq: [content.data.list.0.initiatorUserName, "测试签署"]
      - eq: [content.data.list.0.initiatorOrganizeName, "esigntest自动化CI测试"]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.projectId, "1000000"]
      - eq: [content.data.list.0.currentHandlerName, "$signerUserNameCommon"]
      - contains: [content.data.list.0.signerUserNameList.0, "$signerUserNameCommon"]
      - not_equals: [content.data.list.0.initiatorTime, null]
      - not_equals: [content.data.list.0.gmtFinish, null]


