
- config:
    variables:
      status0: 200
      message0: "成功"
      success0: true
      flowName: ""
      signerUserName: ""
      userAccountNumber: ""
      signOrgName: ""
      orgAccountNumber: ""
      initiatorUserName: ""
      flowIdOrProcessId: ""
      initiatorOrganizeCode: ""
      businessNo: ""
      dynamicCode: ""
      projectId: ""
      currentHandlerUserId: ""
      startTime: "2025-08-08 00:00:00"
      endTime: "2025-09-07 23:59:59"
      gmtFinishBegin: ""
      gmtFinishEnd: ""
      gmtModifiedBegin: ""
      gmtModifiedEnd: ""
      flowStatus: ""
- test:
    title: "title"
    api: api/esignDocs/docFlow/manage_activeDataSummaryInit.yml
    variables:
      - flowName: $flowName
      - signerUserName: $signerUserName
      - userAccountNumber: $userAccountNumber
      - signOrgName: $signOrgName
      - orgAccountNumber: $orgAccountNumber
      - initiatorUserName: $initiatorUserName
      - flowIdOrProcessId: $flowIdOrProcessId
      - initiatorOrganizeCode: $initiatorOrganizeCode
      - businessNo: $businessNo
      - dynamicCode: $dynamicCode
      - projectId: $projectId
      - currentHandlerUserId: $currentHandlerUserId
      - startTime: "2025-08-08 00:00:00"
      - endTime: "2025-09-07 23:59:59"
      - gmtFinishBegin: $gmtFinishBegin
      - gmtFinishEnd: $gmtFinishEnd
      - gmtModifiedBegin: $gmtModifiedBegin
      - gmtModifiedEnd: $gmtModifiedEnd
      - flowStatus: $flowStatus

    extract:
       ex_jobId: json.data.jobId
    validate:
      - eq: [content.message, $message0]
      - eq: [content.status, $status0]
      - eq: [content.success, $success0]

- test:
    title: "title"
    api: api/esignDocs/docFlow/activeData_summaryProgress.yml
    variables:
      - jobId: $ex_jobId
    validate:
      - eq: [content.message, $message0]
      - eq: [content.status, $status0]
      - eq: [content.success, $success0]
