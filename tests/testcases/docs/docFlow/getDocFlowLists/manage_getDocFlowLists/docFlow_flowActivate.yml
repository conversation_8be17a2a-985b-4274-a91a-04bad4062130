
- config:
    variables:
      status0: 200
      message0: "成功"
      success0: true

- test:
    name: "查询签署流程"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    extract:
      flowId: content.data.list.0.flowId
    validate:
      - eq: [ content.message, $message0 ]
      - eq: [ content.status, $status0 ]
      - eq: [ content.success, $success0 ]

- test:
    name: "热表中的数据进行激活"
    api: api/esignDocs/docFlow/docFlow_flowActivate.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [content.message, "未找到flowId对应的归档信息"]
      - eq: [content.status, 1617056]
      - eq: [content.success, false]
