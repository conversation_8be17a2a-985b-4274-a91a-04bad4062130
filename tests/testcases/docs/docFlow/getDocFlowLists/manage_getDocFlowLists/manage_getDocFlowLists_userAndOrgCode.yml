- config:
    name: "根据用户与组织编码数组查询（允许为空/无效/单值/多值/空格/部分无效）"
    variables:
      - fileKey1: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - wserCode0: ${ENV(wsignwb01.userCode)}
      - userCode1: ${ENV(csqs.userCode)}
      - userCode2: ${ENV(sign03.userCode)}
      - orgCode1: ${ENV(csqs.orgCode)}
      - orgCode0: ${ENV(sign01.main.orgCode)}
      - orgNo0: ${ENV(sign01.main.orgNo)}
      - worgCode0: ${ENV(worg01.orgCode)}
      - depatCode1: ${ENV(sign03.main.departCode)}
      - sp: " "


- test:
     name: setup-创建签署流程
     api: api/esignSigns/signFlow/createAndStartJson.yml
     variables:
       subject: TC1-签署列表查询
       departmentCode: $depatCode1
       userCode: $userCode2
       signFiles:
         - fileKey: $fileKey1
       signerInfos:
         - signNode: 20
           userCode: "$userCode0"
           customOrgNo: "$orgNo0"
           userType: 1
           tspId: "LOCAL_DEFAULT_TSP"
           signMode: 0
           signOrder: 4
     extract:
       - signFlowId001: content.data.signFlowId
     validate:
       - eq: [ content.code, 200 ]
       - contains: [ content.message, "成功" ]
       - ne: [ content.data, null ]
     teardown_hooks:
       - ${sleep(5)}

- test:
    name: "TC1-signerUserCodes为空数组-查询成功"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserCodes: []
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
     name: "TC2-signerUserCodes无效值-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: ["INVALID_USER_CODE_1"]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC3-signerUserCodes单个内部有效值-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: [$userCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC4-signerUserCodes单个外部有效值-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: [$wserCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC5-signerUserCodes多个有效值(内外部混合)-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: [$userCode0, $userCode1, $wserCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC6-signerUserCodes值前后空格-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: [$sp$userCode0$sp]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC7-signerUserCodes多值部分有效部分无效-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerUserCodes: ["INVALID_USER_CODE_1", $userCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC8-signerOrgCodes为空数组-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: []
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC9-signerOrgCodes无效值-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: ["INVALID_ORG_CODE_1"]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC10-signerOrgCodes单个内部企业-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: [$orgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC11-signerOrgCodes单个外部企业-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: [$worgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC12-signerOrgCodes多个内部企业-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: [$orgCode0, $orgCode1]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC13-signerOrgCodes值前后空格-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: [$sp$orgCode0$sp]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC14-signerOrgCodes多值部分有效部分无效-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       signerOrgCodes: ["INVALID_ORG_CODE_1", $orgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC15-createUserCodes为空数组-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: []
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC16-createUserCodes无效值-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: ["INVALID_USER_CODE_2"]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC17-createUserCodes单个内部有效值-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: [$userCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC18-createUserCodes多个内部有效值-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: [$userCode0, $userCode1]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC19-createUserCodes值前后空格-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: [$sp$userCode0$sp]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC20-createUserCodes多值部分有效部分无效-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: ["INVALID_USER_CODE_2", $userCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC20-createUserCodes相对方用户-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createUserCodes: [$wserCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC21-createOrgCodes为空数组-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: []
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC22-createOrgCodes无效值(外部企业不支持)-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$worgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC23-createOrgCodes单个内部企业-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$orgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC24-createOrgCodes多个内部企业与部门-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$orgCode0, $orgCode1, $depatCode1]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC25-createOrgCodes值前后空格-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$sp$orgCode0$sp]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]

- test:
     name: "TC26-createOrgCodes多值部分有效部分无效(含外部企业)-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$worgCode0, $orgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - gt: [content.data.total, 0]


- test:
     name: "TC27-createOrgCodes相对方企业-查询为空"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       createOrgCodes: [$worgCode0]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - eq: [content.data.total, 0]

- test:
     name: "TC28-createOrgCodes部门-查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       flowName: "TC"
       flowStatus: "2"
       createOrgCodes: [$depatCode1]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - ge: [content.data.total, 1]
       - ge: [content.data.list.flowId, "$signFlowId001"]

- test:
     name: "TC29-组合查询成功"
     api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
     variables:
       flowName: "TC"
       flowStatus: "2"
       signerUserCodes: [$userCode0]
       signerOrgCodes: [$orgCode0]
       createUserCodes: [$userCode3]
       createOrgCodes: [$depatCode1]
     validate:
       - eq: [content.status, 200]
       - eq: [content.success, true]
       - eq: [content.message, "成功"]
       - ge: [content.data.total, 1]
       - ge: [content.data.list.flowId, "$signFlowId001"]