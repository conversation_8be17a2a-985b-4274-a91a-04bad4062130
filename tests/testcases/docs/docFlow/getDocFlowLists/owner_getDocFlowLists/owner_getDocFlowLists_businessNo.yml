- name: "根据第三方签署业务id查询"

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}  #数据同步到文档需要挺长时间的
    extract:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgNameCommon


- test:
    name: "TC1-根据第三方签署业务id查询成功"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      businessNo: $businessNoCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - eq: [content.data.list.0.flowStatusName, "签署中"]

- test:
    name: "TC2-根据第三方签署业务id查询失败-模糊搜索"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      businessNo: ${substring($businessNoCommon,2,4)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC3-根据第三方签署业务id查询不存在(中间有空格)"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      businessNo: ${middleAddSpace($businessNoCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC4-根据第三方签署业务id查询存在(首尾空格)"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      businessNo: ${addSpace($businessNoCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - eq: [content.data.list.0.flowStatusName, "签署中"]


- test:
    name: "TC5-根据第三方签署业务id查询-异常字符"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      businessNo: "%"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]