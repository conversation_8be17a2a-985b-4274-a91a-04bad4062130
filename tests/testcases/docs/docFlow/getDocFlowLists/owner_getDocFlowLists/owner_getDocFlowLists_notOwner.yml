- config:
    name: "查询页面发起的流程信息"
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      tomorrowTime: ${getTomorrowTime()}
      todayDate: ${getNowDate()}

- test:
    name: "创建签署流程"
    testcase: common/signOpenapi/getSignFlow_initiator.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon

- test:
    name: "TC1-查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
        flowName: $subjectCommon
        businessNo: $businessNoCommon
        initiatorOrganizeCode: $initiatorOrgCodeCommon
        signerUserName: $signerUserNameCommon
        processId: $signFlowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]


- test:
    name: "TC2-查询"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
        flowName: $subjectCommon
        businessNo: $businessNoCommon
        signerUserName: $signerUserNameCommon
        flowIdOrProcessId: $signFlowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]