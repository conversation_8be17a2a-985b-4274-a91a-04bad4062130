- name: "根据流程最近处理日期查询"
- config:
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      tomorrowTime: ${getTomorrowTime()}
      todayDate: ${getNowDate()}
      gmtModifiedEnd0: ${getTimesOff(300)}

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(15)}

- test:
    name: "TC1-根据最近处理日期查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: $todayZeroTime
      gmtModifiedEnd: $tomorrowTime
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.gmtModified, $todayDate]

- test:
    name: "TC2-根据最近处理日期，修改开始日期大于修改结束时间，查询为空"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: $tomorrowTime
      gmtModifiedEnd: $todayZeroTime
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]



- test:
    name: "TC3-最近处理日期为同一天查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: $todayZeroTime
      gmtModifiedEnd: $gmtModifiedEnd0
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - ge: [content.data.total, 1]
      - contains: [content.data.list.0.gmtModified, $todayDate]

- test:
    name: "TC4-gmModifiedBegin日期格式不正确"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: "2025/02/17 00:00:00"
      gmtModifiedEnd: $tomorrowTime
    validate:
      - eq: [content.success, False]
      - eq: [content.message, "gmtModifiedBegin参数错误!"]
      - eq: [content.status, 1600015]
      - eq: [content.data,null]


- test:
    name: "TC5-gmtModifiedEnd日期格式不正确"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: $todayZeroTime
      gmtModifiedEnd: "2025/02/17 00:00:00"
    validate:
      - eq: [content.success, False]
      - eq: [content.message, "gmtModifiedEnd参数错误!"]
      - eq: [content.status, 1600015]
      - eq: [content.data, null]


- test:
    name: "TC6-最近处理日期为空查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: ""
      gmtModifiedEnd: ""
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.gmtModified, $todayDate]

- test:
    name: "TC7-最近处理日期为null查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtModifiedBegin: null
      gmtModifiedEnd: null
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.gmtModified, $todayDate]