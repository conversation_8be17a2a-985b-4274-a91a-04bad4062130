
- config:
    variables:
      status0: 200
      message0: "成功"
      success0: true
      flowName: ""
      signerUserName: ""
      userAccountNumber: ""
      signOrgName: ""
      orgAccountNumber: ""
      initiatorUserName: ""
      flowIdOrProcessId: ""
      initiatorOrganizeCode: ""
      businessNo: ""
      dynamicCode: ""
      projectId: ""
      currentHandlerUserId: ""
      startTime: "2025-08-08 00:00:00"
      endTime: "2025-09-07 23:59:59"
      gmtFinishBegin: ""
      gmtFinishEnd: ""
      gmtModifiedBegin: ""
      gmtModifiedEnd: ""
      flowStatus: ""
- test:
    name: $name
    api: api/esignDocs/docFlow/owner_activeDataSummaryInit.yml
    variables:
      - flowName: $flowName
      - signerUserName: $signerUserName
      - userAccountNumber: $userAccountNumber
      - signOrgName: $signOrgName
      - orgAccountNumber: $orgAccountNumber
      - initiatorUserName: $initiatorUserName
      - flowIdOrProcessId: $flowIdOrProcessId
      - initiatorOrganizeCode: $initiatorOrganizeCode
      - businessNo: $businessNo
      - dynamicCode: $dynamicCode
      - projectId: $projectId
      - currentHandlerUserId: $currentHandlerUserId
      - startTime: "2025-08-08 00:00:00"
      - endTime: "2025-09-07 23:59:59"
      - gmtFinishBegin: $gmtFinishBegin
      - gmtFinishEnd: $gmtFinishEnd
      - gmtModifiedBegin: $gmtModifiedBegin
      - gmtModifiedEnd: $gmtModifiedEnd
      - flowStatus: $flowStatus

#    extract:
#       ex_url01: json.data.list.0.apiUrl
#       ex_Id01: json.data.list.0.id
    validate:
      - eq: [content.message, $message0]
      - eq: [content.status, $status0]
      - eq: [content.success, $success0]
