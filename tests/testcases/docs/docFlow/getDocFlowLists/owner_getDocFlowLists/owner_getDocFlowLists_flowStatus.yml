- test:
    name: "通过流程状态正常查询文档流程"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowStatus: ""
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "查询流程状态为流程中的流程"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowStatus: "2"
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.list.0.flowStatus, "2"]
      - gt: [content.data.total, 0]



