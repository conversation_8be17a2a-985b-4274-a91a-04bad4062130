- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - subjectCommon

- test:
    name: "TC1-根据流程名称查询成功"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowName, $subjectCommon]

- test:
    name: "TC2-根据流程名称查询成功-模糊搜索"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: ${substring($subjectCommon,2,4)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]

- test:
    name: "TC3-根据流程名称查询(中间有空格)"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: ${middleAddSpace($subjectCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]

- test:
    name: "TC4-根据流程名称查询(首尾空格)"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: ${addSpace($subjectCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowName, $subjectCommon]


- test:
    name: "TC5-根据第三方签署业务id查询-异常字符"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: "%"
    validate:
      - eq: [content.status, 917]
      - eq: [content.success, False,]
      - eq: [content.message, "包含非法字符，存在sql注入语句"]
