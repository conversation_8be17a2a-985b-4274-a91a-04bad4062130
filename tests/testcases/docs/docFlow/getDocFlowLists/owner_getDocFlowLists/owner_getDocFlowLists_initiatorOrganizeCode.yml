- name: "根据发起人组织查询"

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - signerUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon

- test:
    name: "TC1-根据发起人组织查询成功"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: $initiatorOrgCodeCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]

- test:
    name: "TC2-根据发起人组织查询成功-首尾空格"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: ${addSpace($initiatorOrgCodeCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]

- test:
    name: "TC3-根据发起人组织查询为空-中间空格"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: ${middleAddSpace($initiatorOrgCodeCommon)}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]
