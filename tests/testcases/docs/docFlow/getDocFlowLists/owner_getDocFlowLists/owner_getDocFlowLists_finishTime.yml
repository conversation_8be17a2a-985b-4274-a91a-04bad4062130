- name: "根据结束日期查询"
- config:
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      tomorrowTime: ${getTomorrowTime()}
      todayDate: ${getNowDate()}

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC1-根据结束日期查询"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.gmtFinish, $todayDate]

- test:
    name: "TC2-根据结束日期查询为空"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      gmtFinishBegin: $tomorrowTime
      gmtFinishEnd: $tomorrowTime
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.total, 0]