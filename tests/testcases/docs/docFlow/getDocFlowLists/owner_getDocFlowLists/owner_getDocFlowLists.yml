- config:
    name: "查询openapi发起的流程信息"
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      tomorrowTime: ${getTomorrowTime()}
      todayDate: ${getNowDate()}

- test:
    name: "创建签署流程"
    testcase: common/signOpenapi/getAutoSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - signerUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon

- test:
    name: "TC1-组合查询-2个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: $initiatorOrgCodeCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]

- test:
    name: "TC2-组合查询-3个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
    extract:
      flowIdCommon: content.data.list.0.flowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]

- test:
    name: "TC3-组合查询-4个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]

- test:
    name: "TC4-组合查询-5个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]

- test:
    name: "TC5-组合查询-6个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
      businessNo: $businessNoCommon
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]

- test:
    name: "TC6-组合查询-7个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]

- test:
    name: "TC7-组合查询-8个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      flowId: $flowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]

- test:
    name: "TC8-组合查询-9个条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $subjectCommon
      businessNo: $businessNoCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]

- test:
    name: "TC9-组合查询-所有条件"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowName: $subjectCommon
      businessNo: $businessNoCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]


- test:
    name: "TC10-组合查询-所有条件+最近处理时间"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: $flowIdCommon
      flowName: $subjectCommon
      businessNo: $businessNoCommon
      startTime: $todayZeroTime
      endTime: $tomorrowTime
      gmtFinishBegin: $todayZeroTime
      gmtFinishEnd: $tomorrowTime
      gmtModifiedBegin: $todayZeroTime
      gmtModifiedEnd: $tomorrowTime
      initiatorOrganizeCode: $initiatorOrgCodeCommon
      signerUserName: $signerUserNameCommon
      processId: $signFlowIdCommon
      flowStatus: 3
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]
      - eq: [content.data.list.0.flowId, $flowIdCommon]
      - eq: [content.data.list.0.initiatorUserName, $initiatorUserNameCommon]
      - eq: [content.data.list.0.initiatorOrganizeName, $initiatorOrgNameCommon]
      - eq: [content.data.list.0.flowName, $subjectCommon]
      - contains: [content.data.list.0.signerUserNameList.0, $signerUserNameCommon]
      - eq: [content.data.list.0.flowTypeName, "普通签署"]
      - eq: [content.data.list.0.flowStatus, "3"]
      - eq: [content.data.list.0.flowStatusName, "签署完成"]
      - contains: [content.data.list.0.initiatorTime, $todayDate]
      - contains: [content.data.list.0.gmtFinish, $todayDate]
      - contains: [content.data.list.0.gmtModified, $todayDate]


