- config:
    name: "查询文档流程场景：一个正常的操作流程场景"
    variables:
      todayZeroTime: ${getTodayZeroTime()}
      nowTime: ${getNowTime()}

- test:
    name: "setup-创建签署流程"
    testcase: common/signOpenapi/getSignFlow.yml
    teardown_hooks:
      - ${sleep(5)}
    extract:
      - initiatorUserNameCommon
      - signerUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon

- test:
    name: "TC1-发起时间小于当前时间"
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      startTime: $todayZeroTime
      endTime: ${getTomorrowTime()}
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - gt: [content.data.total, 0]




