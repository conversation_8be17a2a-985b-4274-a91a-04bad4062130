
- config:
    variables:
      status0: 200
      message0: "成功"
      success0: true
      fileName: ""
      flowName: ""
      initiatorUserName: ""
      initiatorAccountNumber: ""
      initiatorOrganizeCode: ""
      signerUserName: ""
      userAccountNumber: ""
      signOrgName: ""
      orgAccountNumber: ""
      signMode: ""
      processId: ""
      businessNo: ""
      dynamicCode: ""
      projectId: ""
      viewType: 1
      startTime: "2025-08-08 00:00:00"
      endTime: "2025-09-07 23:59:59"
      gmtSignFinishStart: "2025-06-09 00:00:00"
      gmtSignFinishEnd: "2025-09-07 23:59:59"
- test:
    title: "title"
    api: api/esignDocs/signedFileProcess/owner_summaryInit.yml
    variables:
     - fileName: $fileName
     - flowName: $flowName
     - initiatorUserName: $initiatorUserName
     - initiatorAccountNumber: $initiatorAccountNumber
     - initiatorOrganizeCode: $initiatorOrganizeCode
     - signerUserName: $signerUserName
     - userAccountNumber: $userAccountNumber
     - signOrgName: $signOrgName
     - orgAccountNumber: $orgAccountNumber
     - signMode: $signMode
     - processId: $processId
     - businessNo: $businessNo
     - dynamicCode: $dynamicCode
     - projectId: $projectId
     - viewType: $viewType
     - startTime: $startTime
     - endTime: $endTime
     - gmtSignFinishStart: $gmtSignFinishStart
     - gmtSignFinishEnd: $gmtSignFinishEnd

    validate:
      - eq: [content.message, $message0]
      - eq: [content.status, $status0]
      - eq: [content.success, $success0]
