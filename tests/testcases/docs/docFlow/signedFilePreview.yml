#场景说明：单方签署，无审批
- config:
    variables:
      autoPresetName: "文档自动化测试发起直接到签署-${get_randomNo_16()}"
      randomSignerId0: ${get_randomNo_32()}
      randomSignerId1: ${get_randomNo_32()}
      autotestModelName: ""
      autotestModelKey: ${getWorkFlowModelKey($autotestModelName)}
      autotestWorkFlowModelName: "【电子签署】-$autotestModelName"
      userAccountKey: "ceswdzxzdhyhwgd1.account"
      userNameKey: "ceswdzxzdhyhwgd1.userName"
      userName_outer_Key: "wsignwb01.userName"
      userAccount_outer_Key: "wsignwb01.accountNo"
      userAccount: ${ENV($userAccountKey)}
      userAccount_outer: ${ENV($userAccount_outer_Key)}
      signerUserName_inner: ${ENV($userNameKey)}
      signerUserCode_inner: ${get_inner_UserCode(userAccount=$userAccount)}
      signerUserName_outer: ${ENV($userName_outer_Key)}
      signerUserCode_outer: ${get_outer_UserCode(userAccount=$userAccount_outer)}

- test:
    name: "setup-新建一个业务模板"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.success, true]

- test:
    name: "setup-查询新增的业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - status: 0
      - page: 1
      - size: 10
    extract:
      - testPresetId: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - eq: [content.data.total, 1]
      - eq: [content.data.list.0.presetName, $autoPresetName]


- test:
    name: "setup-创建无内容域无签署区的模板，用于业务模板选择模板时关联"
    testcase: common/template/buildTemplate1.yml
    extract:
      - newTemplateUuidCommon_template1
      - newVersionCommon_template1
      - templateNameCommon_template1
#      - contentNameCommon

- test:
    name: "setup-关联模板、流程引擎到业务模板并保存"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - allowAddFile: 1
      - initiatorAll: 1
      - initiatorList: []
      - presetId: $testPresetId
      - presetName: $autoPresetName
      - templateList: [{
                         "templateId": $newTemplateUuidCommon_template1,
                         "templateName": $templateNameCommon_template1,
                         "version": $newVersionCommon_template1
                       }
      ]
      - workFlowModelKey: $autotestModelKey
    validate:
      - eq: [content.message,"成功"]
      - eq: [content.status,200]



- test:
    name: "setup-业务模板第三步：添加签署方（内部个人指定（测试文档中心自动化用户勿改动）+相对方个人）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      - params: {
        "presetId": $testPresetId,
        "presetSnapshotId": null,
        "presetName": $autoPresetName,
        "status": 1,
        "initiatorAll": 1,
        "initiatorEdit": 1,
        "allowAddFile": 1,
        "allowAddSigner": 0,
        "forceReadingTime": null,
        "signEndTimeEnable": null,
        "sort": 0,
        "workFlowModelKey": $autotestModelKey,
        "workFlowModelName": $autotestWorkFlowModelName,
        "workFlowModelStatus": 1,
        "signatoryCount": 2,
        "changeReason": null,
        "signerNodeList": [
        {
          "signNode": 2,
          "signMode": 1,
          "id": "node-0",
          "signerList": [
          {
            "templateInitiationSignersUuid": null,
            "signerId": $randomSignerId0,
            "signerSnapshotId": null,
            "signerTerritory": 1,
            "signerType": 1,
            "assignSigner": 0,
            "userName": null,
            "userCode": null,
            "userAccount": null,
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 0,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": [],
            "id": "0-0",
            "draggable": true
          }
          ]
        }
        ]
      }
    validate:
      - eq: [ content.message, "成功" ]
      - eq: [ content.status, 200 ]
      - eq: [content.success, true]



#以下为发起电子签署开始
- test:
    name: "setup-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-获取getInfo的接口的batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": { }
    extract:
      - batchTemplateInitiationUuidNew: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId
    extract:
      - signerId0: content.data.signerNodeList.0.signerList.0.signerId
      - signatoryList0: content.data.signerNodeList.0.signerList.0.signatoryList
    #      - signerId1: content.data.signerNodeList.1.signerList.0.signerId
    #      - signatoryList1: content.data.signerNodeList.1.signerList.0.signatoryList
    #      - userAccount1: content.data.signerNodeList.1.signerList.0.userAccount
    #      - userCode1: content.data.signerNodeList.1.signerList.0.userCode
    #      - userName1: content.data.signerNodeList.1.signerList.0.userName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
#暂存
- test:
    name: "setup-暂存"
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $autoPresetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": $departmentName,
        "initiatorDepartmentCode": $departmentCode,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": null,
        "wordList": [ ],
        "saveSigners": null,
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "templateInitiationSignersUuid": null,
            "signerId": $signerId0,
            "signerSnapshotId": null,
            "signerTerritory": 2,
            "signerType": 1,
            "assignSigner": 0,
            "userName": null,
            "userCode": null,
            "userAccount": null,
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 1,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": $signatoryList0
          }
          ],
          "id": 0,
          "draggable": false
        }
        ],
        "type": 3,
        "contentList": [ ],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [ ],
        "attachments": [ ],
        "ccInfos": [ ],
        "auditInfo": null
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-获取getInfo的接口的签署方信息"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      "params": {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew
      }
    extract:
      - templateInitiationSignersUuid_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.templateInitiationSignersUuid
      - signerId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerId
      - signerSnapshotId_outer: content.data.businessPresetDetail.signerNodeList.0.signerList.0.signerSnapshotId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success",true ]


- test:
    name: "setup-提交电子签署（单份发起）"
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    variables:
      "params": {
        "batchTemplateInitiationName": $autoPresetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuidNew,
        "excelFileKey": null,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "initiatorDepartmentName": null,
        "initiatorDepartmentCode": null,
        "businessPresetUuid": $testPresetId,
        "businessPresetSnapshotUuid": "",
        "wordList": [],
        "saveSigners": null,
        "signersList": [
        {
          "signNode": 2,
          "signMode": 1,
          "signerList": [
          {
            "templateInitiationSignersUuid": $templateInitiationSignersUuid_outer,
            "signerId": $signerId_outer,
            "signerSnapshotId": $signerSnapshotId_outer,
            "signerTerritory": 1,
            "signerType": 1,
            "assignSigner": 0,
            "userName": $signerUserName_inner,
            "userCode": $signerUserCode_inner,
            "userAccount": "",
            "legalSign": 0,
            "organizeName": null,
            "organizeCode": null,
            "departmentName": null,
            "departmentCode": null,
            "sealTypeCode": null,
            "sealTypeName": null,
            "autoSign": 0,
            "needGather": 1,
            "signNode": 2,
            "signOrder": 1,
            "signMode": 1,
            "signerStatus": null,
            "signerStatusStr": null,
            "signatoryList": $signatoryList0
          }]}
        ],
        "type": 3,
        "contentList": [],
        "sort": 0,
        "advertisement": false,
        "chargingType": 1,
        "readComplete": false,
        "remark": null,
        "signFlowExpireTime": null,
        "businessNo": null,
        "appendList": [],
        "attachments": [],
        "ccInfos": [],
        "auditInfo": null
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-获取刚发起的流程的flowId"
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      - "flowId": ""
      - "flowName": $autoPresetName
      - "startTime": ""
      - "endTime": ""
      - "flowStatus": ""
      - "initiatorUserName": ""
      - "page": 1
      - "size": 10
    extract:
      - flowId: content.data.list.0.flowId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "setup-我管理的-正常查看电子签署详情"
    api: api/esignDocs/docFlow/manage_getDocFlowDetail.yml
    variables:
      flowId: $flowId
    extract:
      - fileKey: content.data.signedFileVOList.0.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]

- test:
    name: "TC-查看电子签署详情 =》签署文件正常预览"
    api: api/esignDocs/docFlow/signedFilePreview.yml
    variables:
      - flowId: $flowId
      - fileKey: $fileKey
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]