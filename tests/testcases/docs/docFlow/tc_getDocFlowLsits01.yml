- config:
    name: "列表信息支持通过账号查询-V6.0.11.0-beta.1"
    variables:
        - fileKey0: ${ENV(fileKey)}
        - fileKey1: ${ENV(1PageFileKey)}
        - businessTypeCode: ${ENV(businessTypeCode)}
        - name: "流程批量转交-转交后的异步信息校验"
        - userCode0: ${ENV(sign01.userCode)}
        - orgCode0: ${ENV(sign01.main.orgCode)}
        - userCodeInit: ${ENV(csqs.userCode)}
        - orgCodeInit: ${ENV(csqs.orgCode)}

- test:
    name: "TC1-签署流程信息-签署中"
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: TC1-签署流程信息-签署中
      signFiles:
        - fileKey: $fileKey0
        - fileKey: $fileKey1
      signerInfos:
        - signNode: 1
          userCode: "$userCode0"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
    extract:
      - signFlowId001: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC2-签署流程信息-已签署"
    api: api/esignSigns/signFlow/createAndStartJson.yml
    variables:
      subject: TC2-签署流程信息-已签署
      signFiles:
        - fileKey: $fileKey0
      signerInfos:
        - signNode: 1
          userCode: "$userCode0"
          organizationCode: "$orgCode0"
          userType: 1
          signMode: 0
          autoSign: 1
          sealInfos:
            - fileKey: " $fileKey0"
              signConfigs:
                - freeMode: 0
                  posY: 200
                  posX: 200
                  pageNo: 1
                  signatureType: "COMMON-SEAL"
    extract:
      - signFlowId002: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.signFlowId, 1 ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: TC1-电子签署列表-通过账号sign01查询
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId001
      userAccountNumber0: ${ENV(sign01.accountNo)}
      orgAccountNumber0: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ge: [ json.data.total, 1 ]
      - contains: [ json.data.list.0.signerUserNameList.0, "${ENV(sign01.main.orgNo)}" ]

- test:
    name: TC2-电子签署列表-通过账号sign01查询
    api: api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      flowIdOrProcessId: $signFlowId002
      userAccountNumber0: ${ENV(sign01.accountNo)}
      orgAccountNumber0: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ge: [ json.data.total, 1 ]
      - contains: [ json.data.list.0.signerUserNameList.0, "${ENV(sign01.main.orgNo)}" ]

- test:
    name: TC3-电子签署列表-我发起的-通过账号sign01查询
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowId: ""
      userAccountNumber1: ${ENV(sign01.accountNo)}
      orgAccountNumber1: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ge: [ json.data.total, 0 ]

- test:
    name: TC5-电子签署列表-通过账号不存在查询为空
    api:  api/esignDocs/docFlow/manage_getDocFlowLists.yml
    variables:
      signerUserName: "XXXXX"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list,  [] ]

- test:
    name: TC6-电子签署列表-我发起的-通过账号不存在查询为空
    api:  api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      signerUserName: "XXXXX"
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list,  [] ]

- test:
    name: TC7-物理用印签署列表-通过账号不存在查询为空
    api:  api/esignDocs/physical/getDocFlowListsManage.yml
    variables:
      userAccountNumber4: "XXXXX"
      orgAccountNumber4: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list,  [] ]

- test:
    name: TC8-物理用印签署列表-我发起的-通过账号不存在查询为空
    api:  api/esignDocs/physical/getDocFlowListsOwner.yml
    variables:
      userAccountNumber3: "XXXXX"
      orgAccountNumber3: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.list,  [] ]

- test:
    name: T9-已签署文件列表-通过账号查询
    api:  api/esignDocs/signedFileProcess/manage_list.yml
    variables:
      userAccountNumber5: ${ENV(sign01.accountNo)}
      orgAccountNumber5: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - ge: [ json.data.total, 0 ]
      - contains: [ json.data.signFileProcessVOList.0.signerUserNameList.0, "${ENV(sign01.main.orgNo)}" ]

- test:
    name: TC10-已签署文件列表-我发起的-通过账号不存在查询为空
    api:  api/esignDocs/signedFileProcess/owner_list.yml
    variables:
      userAccountNumber6: "XXXXX"
      orgAccountNumber6: ${ENV(sign01.main.orgNo)}
    validate:
      - eq: [ content.success, true ]
      - eq: [ content.status, 200 ]
      - eq: [ json.data.total, 0 ]
      - eq: [ json.data.signFileProcessVOList,  [] ]