- config:
    name: "签署作废流程-重新发起(我管理的、我发起的)"
    variables:
      fileKey: ${ENV(fileKey)}
      businessTypeCode: ${ENV(businessTypeCode)}
      organizationCodeInit: ${ENV(sign01.main.orgCod)}
      userCodeInit: ${ENV(sign01.userCode)}
      presetName: "自动化业务模板${get_randomNo()}"
      userCodeSign: ${ENV(sign01.userCode)}
      userNameSign: ${ENV(sign01.userName)}
      organizationCodeSign: ${ENV(sign01.main.orgCod)}
      appendList00: [ { "appendType": 1,"attachmentInfo": { "fileKey": "$fileKey" } } ]
      subject00: "web页面发起流程-${get_randomNo()}"
      fileFormat: 1
      presetName0: "通用发起签署业务"
      newSignerSnapshotId: ${get_randomNo_32()}
      presetName_createByBizTemplate_01:  "自动化测试电子签署业务模板1-${getDateTime()}"
      signerId0: "${getRandomNo()}"



#采集流程作废，不支持重新发起
#todo
#- test:
#    name: setup-获取一个有内容域的pdf模板
#    testcase: common/template/buildTemplate.yml
#    extract:
#      - newTemplateUuidCommon_template1
#      - newVersionCommon_template1
#      - templateNameCommon_template1




#一步发起接口发起的流程作废后不支持重新发起
- test:
    name: "setup1-获取签署中的flowid"
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      - fileKey: $fileKey
      - signType: "COMMON-SIGN"
      - signatureType: "PERSON-SEAL"
      - userTypeCC: 1
      - subject: "一步发起流程"
      - businessNo: ""
    extract:
      - signFlowId1: content.data.signFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.signFlowStatus, 1 ]
      - eq: [ content.data.signFlowId, $signFlowId1 ]
    teardown_hooks:
      - ${sleep(5)}


- test:
    name: "setup2-作废signFlowId1流程"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "reason": "123",
          "signFlowId": $signFlowId1
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.revokedSignFlowId, $revokedSignFlowId ]
    teardown_hooks:
      - ${sleep(5)}


- test:
    name: "我管理的-重新发起一步发起已作废的流程，报错"
    api: api/esignDocs/docFlow/manage_reissue.yml
    variables:
      flowIdReissue: $signFlowId1
    validate:
      - eq: [ "content.status", 1709168 ]
      - eq: [ "content.success", false ]
      - eq: [ "content.message", "该流程为openapi发起，不支持重新发起" ]
      - eq: [ "content.data", null ]




- test:
    name: "我发起的-重新发起一步发起已作废的流程，报错"
    api: api/esignDocs/docFlow/owner_reissue.yml
    variables:
      flowIdReissue: $signFlowId1
    validate:
      - eq: [ "content.status", 1709168 ]
      - eq: [ "content.success", false ]
      - eq: [ "content.message", "该流程为openapi发起，不支持重新发起" ]
      - eq: [ "content.data", null ]


#分步发起流程
- test:
    name: "TC-api签署流程创建流程正常"
    api: api/esignSigns/signFlow/signFlowCreate.yml
    variables:
        json:
          businessTypeCode: $businessTypeCode
          initiatorInfo:
            organizationCode: $organizationCodeInit
            userCode: $userCodeInit
            userType: 1
          readComplete: false
          redirectUrl: 'www.baidu.com'
          remark: '我是备注'
          signNotifyUrl: ' '
          subject: "分步发起-${getDateTime()}"
    validate:
        - eq: ["content.code", 200]
        - eq: ["content.message", "成功"]
    extract:
      - signFlowId2: content.data.signFlowId


- test:
    name: "TC-分步发起：添加签署文件"
    api: api/esignSigns/signFlow/addFiles.yml
    variables:
      jsonAddFiles:
        businessNo: ''
        signFiles:
          - fileKey: $fileKey  #必传，签署文件
        signFlowId: $signFlowId2
    extract:
      - fileKey001: content.data.signFiles.0.fileKey
    validate:
      - eq: [ content.code,200 ]
      - len_gt: [ content.data.signFlowId, 1 ]
      - eq: [ content.data.signFlowStatus, 0 ]

- test:
    name: "TC-添加签署方"
    api: api/esignSigns/signFlow/addSigners.yml
    variables:
      tmp001: [
        {
            "autoSign": true,
            "isUkeySign": 0,
            "tspId": " LOCAL_DEFAULT_TSP",
            "isLegalSignFlag": 0,
            "sealInfos": [
                {
                    "fileKey": "$fileKey001",
                    "signConfigs": [
                        {
                            "pageNo": "1",
                            "posX": 200,
                            "posY": 400,
                            "sealId": "",
                            "signatureType": "PERSON-SEAL",
                            "signType": "COMMON-SIGN",
                            "width": null
                        }
                    ]
                }
            ],
            "sealTypeCode": "",
            "signMode": "",
            "signNode": 1,
            "organizationCode": "",
            "userCode": "$userCodeInit",
            "userType": 1
        }
      ]
      signFlowId: $signFlowId2
      signerInfos: $tmp001
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]


- test:
    name: "TC-开启流程"
    api: api/esignSigns/signFlow/start.yml
    variables:
      signFlowId: $signFlowId2
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.data.signFlowId, $signFlowId2 ]
      - eq: [ content.data.signFlowStatus, 1 ]
    teardown_hooks:
      - ${sleep(30)}

- test:
    name: "TC-作废signFlowId2流程"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "reason": "123",
          "signFlowId": $signFlowId2
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
      - businessNo1: content.data.businessNo
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.revokedSignFlowId, $revokedSignFlowId ]
    teardown_hooks:
      - ${sleep(5)}


- test:
    name: "TC-我管理的-重新发起分步发起已作废的流程，报错"
    api: api/esignDocs/docFlow/manage_reissue.yml
    variables:
      flowIdReissue: $signFlowId2
    validate:
      - eq: [ "content.status", 1709168 ]
      - eq: [ "content.success", false ]
      - eq: [ "content.message", "该流程为openapi发起，不支持重新发起" ]
      - eq: [ "content.data", null ]


- test:
    name: "TC-我发起的-重新发起分步发起已作废的流程，报错"
    api: api/esignDocs/docFlow/owner_reissue.yml
    variables:
      flowIdReissue: $signFlowId2
    validate:
      - eq: [ "content.status", 1709168 ]
      - eq: [ "content.success", false ]
      - eq: [ "content.message", "该流程为openapi发起，不支持重新发起" ]
      - eq: [ "content.data", null ]


#页面发起流程作废后重新发起
- test:
    name: "setup-查询业务模板,并获取presetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetName0
    extract:
      - testPresetId0: content.data.list.0.presetId
    validate:
      - eq: [content.message, "成功"]
      - eq: [content.status, 200]
      - contains: [content.data.list.0.presetName, $presetName0]


- test:
    name: "setup-查看初始状态业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $testPresetId0
    extract:
      - testPresetId: content.data.signBusinessType.businessTypeId
      - assignedRevokeSignatureArea: content.data.signBusinessType.assignedRevokeSignatureArea
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - eq: [content.message, "成功"]
      - eq: [content.data.fileFormat, 1]
      - eq: [content.data.allowAddFile, 1]
      - eq: [content.data.allowAddSigner, 1]
      - eq: [content.data.initiatorAll, 1]
      - eq: [content.data.presetId, $testPresetId0]
      - contains: [content.data.presetName, $presetName0]
      - ne: [content.data.signBusinessType.businessTypeId, null]


- test:
    name: "choseDetail-选择业务模板"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $testPresetId0
#    extract:
#      - signerNodeList001: content.data.signerNodeList
#      - contentList001: content.data.contentList
    validate:
      - eq: [ "content.message","成功" ]
      - eq: ["content.status", 200]
      - eq: ["content.success", true]
      - ne: ["content.data", null]

- test:
    name: "getInitiatorUserInfo-获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $testPresetId0
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCode: content.data.list.0.organizationCode
      - organizationName: content.data.list.0.organizationName
      - initiatorUserName: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - eq: ["content.success", true]
      - ne: ["content.data", null]

- test:
    name: "getInfo-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "staging"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $presetName,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $organizationCode,
        "initiatorOrganizeName": $organizationName,
        "initiatorUserName": $initiatorUserName,
        "businessPresetUuid": $testPresetId0,
        "saveSigners": null,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 2,
            "userName": $userNameSign,
            "userCode": $userCodeSign,
            "signerSnapshotId": $newSignerSnapshotId
          }
          ]
        }],
        "appendList": []
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]

- test:
    name: "getInfo-获取batchTemplateInitiationUuid"
    api: api/esignDocs//batchTemplateInitiation/getInfo.yml
    variables:
      - batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
    extract:
#      - signerNodeList001: content.data.businessPresetDetail.signerNodeList
      - templateList001: content.data.businessPresetDetail.templateList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: "submit-提交发起任务"
    variables:
      batchTemplateInitiationName: $presetName
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
      initiatorOrganizeCode: $organizationCode
      initiatorOrganizeName: $organizationName
      initiatorUserName: $initiatorUserName
      initiatorDepartmentName: $departmentName
      initiatorDepartmentCode: $departmentCode
      businessPresetUuid: $testPresetId0
      fileFormat: 1
      signersList: [        {
          "signMode": 0,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 1,
            "userName": $userNameSign,
            "userCode": $userCodeSign,
            "tspId": "LOCAL_DEFAULT_TSP",
            "signerSnapshotId": $newSignerSnapshotId
          }
          ]
        }]
      appendList: [        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $fileKey,
            "fileName": "测试"
          }
        }]
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(2)}

- test:
    name: submitResult-查询发起的结果
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "$_flowId"
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.hasPermission, true] #【我发起的】按钮
      - eq: [content.data.message, null] #失败原因为空
      - eq: [content.data.initiator.initiatorDepartmentName, "$organizationName"]
      - eq: [content.data.initiator.initiatorOrganizeName, "$organizationName"]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserName"]
      - eq: [content.data.signerList.0.signerName, "$userNameSign"]
    teardown_hooks:
      - ${sleep(5)}


- test:
    name: "TC-作废webapi发起流程"
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      json:
        {
          "reason": "123",
          "signFlowId": $_flowId
        }
    extract:
      - revokedSignFlowId: content.data.revokedSignFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.revokedSignFlowId, $revokedSignFlowId ]
    teardown_hooks:
      - ${sleep(5)}

- test:
    name: "TC-我发起的-webapi发起的已作废流程，重新发起"
    api: api/esignDocs/docFlow/owner_reissue.yml
    variables:
      flowIdReissue: $_flowId
    validate:
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", true ]
      - eq: [ "content.message", "成功" ]


- test:
    name: "TC-我管理的-webapi发起的已作废流程，重新发起"
    api: api/esignDocs/docFlow/manage_reissue.yml
    variables:
      flowIdReissue: $_flowId
    validate:
      - eq: [ "content.status", 200 ]
      - eq: [ "content.success", true ]
      - eq: [ "content.message", "成功" ]




