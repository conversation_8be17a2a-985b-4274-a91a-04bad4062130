- config:
    name: "发起和审批环节上传附件"
    variables:
      - pdfFileName: "testppp.pdf"
      - docFileName: "word-测试模版-fillinguserInfos.doc"
      - docxFileName: "test.docx"
      - pngFileName: "1.png"
      - jpgFileName: "psb.jpg"
      - ofdFileName: "测试ofd文件.ofd"
      - zipFileName: "testzip.zip"
      - txtFileName: "txtfile.txt"
      - bmpFileName: "temp4.bmp"
      - gifFileName: "temp1.gif"
#      - pdfFileNameChart: "二五测试一二十测试一二五测试一二十测试一二五测试一二五测试一二十测试一二五测试一二十测试一二五测试一二五测试一二十测试一二五测试一二十测试一二五测试一二五测试一二十测试一二五测试一二十.pdf"
      - pdfFileNameTs1: "测试特殊字符@！#￥%……&&（）{【、，。、；】}XS《，。；‘’》+——=-·`er.pdf"
      - pdfFileNameTs2: "不支持 $\/:*?>|.pdf"

- test:
    name: "正常上传一个pdf文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $pdfFileName
      file_path: "data/$pdfFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]



- test:
    name: "正常上传一个doc文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $docFileName
      file_path: "data/$docFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "doc"]
      - ne: [content.data.fileKey, ""]



- test:
    name: "正常上传一个docx文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $docxFileName
      file_path: "data/$docxFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "docx"]
      - ne: [content.data.fileKey, ""]




- test:
    name: "正常上传一个png文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $pngFileName
      file_path: "data/$pngFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "png"]
      - ne: [content.data.fileKey, ""]



- test:
    name: "正常上传一个jpg文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $jpgFileName
      file_path: "data/$jpgFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "jpg"]
      - ne: [content.data.fileKey, ""]


- test:
    name: "正常上传一个ofd文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $ofdFileName
      file_path: "data/$ofdFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "ofd"]
      - ne: [content.data.fileKey, ""]

- test:
    name: "正常上传一个zip文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $zipFileName
      file_path: "data/$zipFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "zip"]
      - ne: [content.data.fileKey, ""]




- test:
    name: "正常上传一个文件名含有特殊字符的pdf文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $pdfFileNameTs1
      file_path: "data/$pdfFileNameTs1"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message, "文件上传成功"]
      - eq: [content.data.suffix, "pdf"]
      - ne: [content.data.fileKey, ""]




#######要求：管理平台的配置文件名称长度为100#####
#- test:
#    name: "异常,上传一个文件名为100个字符（包含后缀）PDF文件"
#    api: api/esignDocs/fileSystem/attachmentUpload.yml
#    variables:
#      fileName: $pdfFileNameChart
#      file_path: "data/$pdfFileNameChart"
#      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
#    validate:
#      - eq: [content.status, 1610003]
#      - eq: [content.success, False]
#      - contains: [content.message, "文件上传失败:文件上传失败"]
#      - contains: [content.message, "文件名长度超出文件系统限制"]



#
#- test:
#    name: "异常,上传一个文件名为99个字符（包含后缀）PDF文件"
#    variables:
#     file_path: "data/$pdfFileNameChart"
#     multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
#    request:
#      url: ${ENV(esign.projectHost)}/esign-docs/fileSystem/attachmentUpload
#      method: POST
#      headers:
#        Content-Type: ${multipart_content_type($multipart_encoder)}
#        X-timevale-project-id: ${ENV(esign.projectId)}
#        authorization: ${getPortalToken()}
#      files:
#        uploadFile: $multipart_encoder
#      data:
#        fileName: $pdfFileNameChart
#    validate:
#      - eq: [content.status, 200]
#      - eq: [content.success, true]
#      - eq: [content.message, "文件上传成功"]


- test:
    name: "异常，上传一个txt文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $txtFileName
      file_path: "data/$txtFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 1610033]
      - eq: [content.success, false]
      - eq: [content.message,"文件格式不支持"]


- test:
    name: "异常，上传一个bmp文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $bmpFileName
      file_path: "data/$bmpFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 1610033]
      - eq: [content.success, false]
      - eq: [content.message,"文件格式不支持"]



- test:
    name: "异常，上传一个gif文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      fileName: $gifFileName
      file_path: "data/$gifFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    validate:
      - eq: [content.status, 1610033]
      - eq: [content.success, false]
      - eq: [content.message,"文件格式不支持"]




#
#- test:
#    name: "异常，上传一个文件名含有不支持特殊字符的pdf文件"
#    api: api/esignDocs/fileSystem/attachmentUpload.yml
#    variables:
#      fileName: $pdfFileNameTs2
#      file_path: "data/$pdfFileNameTs2"
#      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
#    validate:
#      - eq: [content.status, 500]
#      - eq: [content.success, False]
#      - eq: [content.message, "Required request part 'uploadFile' is not present"]
