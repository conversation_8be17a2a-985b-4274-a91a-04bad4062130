- config:
    variables:
      - pdfFileName: "testppp.pdf"
      - txtFileName: "txtfile.txt"


- test:
    name: "上传一个pdf文件"
    variables:
     file_path: "data/$pdfFileName"
     multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/fileSystem/commonUpload
      method: POST
      headers:
        Content-Type: ${multipart_content_type($multipart_encoder)}
        X-timevale-project-id: ${ENV(esign.projectId)}
        authorization: ${getPortalToken()}
      files:
        uploadFile: $multipart_encoder
      data:
        fileName: $pdfFileName
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
    #  - endswith: [content.data.fileKey,".pdf"]
      - eq: [content.data.suffix,"pdf"]

- test:
    name: "异常TC-上传一个txt文件"
    variables:
     file_path: "data/$txtFileName"
     multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/fileSystem/commonUpload
      method: POST
      headers:
        Content-Type: ${multipart_content_type($multipart_encoder)}
        X-timevale-project-id: ${ENV(esign.projectId)}
        authorization: ${getPortalToken()}
      files:
        uploadFile: $multipart_encoder
      data:
        fileName: $txtFileName
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, false]
      - eq: [content.message,"文件内容与文件格式不一致"]
