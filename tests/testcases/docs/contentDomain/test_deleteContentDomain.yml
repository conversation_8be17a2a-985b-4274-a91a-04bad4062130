- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-del"
      contentCode: ${get_randomNo_16()}


- test:
    name: "setup-新增内容域--文本"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": "$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "setup-获取内容域列表-名称为空"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC1-删除内容域"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC2-删除后的内容域无法搜索到"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total",0 ]


- test:
    name: "TC3-删除已删除的内容域"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId }
    validate:
      - eq: [ "content.message","内容域不存在" ]
      - eq: [ "content.status",1601001 ]
      - eq: [ "content.success",false ]
      - eq: [ "content.data",null ]
