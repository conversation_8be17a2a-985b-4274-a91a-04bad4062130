- config:
    variables:
      headers: ${gen_main_headers_for_urlencoded()}


- test:
    name: "TC1-新建内容域_内容域数据源来源内部组织"
    api: api/esignDocs/contentDomain/getDataSource.yml
    variables:
      dataSource: "organize"
      type: 0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - len_eq: [ "content.data.dataSourceList",3 ]

- test:
    name: "TC2-新建内容域_内容域数据源来源内部用户"
    api: api/esignDocs/contentDomain/getDataSource.yml
    variables:
      dataSource: "user"
      type: 0
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - len_eq: [ "content.data.dataSourceList",4 ]