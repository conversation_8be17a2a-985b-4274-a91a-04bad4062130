- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-add"
      contentCode: ${get_randomNo_16()}
      varAccount: "ceswdzxzdhyhwgd1.account"
      varPassword: "ceswdzxzdhyhwgd1.password"


- test:
    name: "TC1-新增内容域--文本"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "text_$contentCode",
        "contentName": "text_$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-新增内容域--手机号"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "mobile_$contentCode",
        "contentName": "mobile_$name$contentCode",
        "dataSource": "user",
        "formatType": 1,
        "required": 1,
        "description": "寒轩测试内容域-手机号哈哈哈啦啦啦啊",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2.1-新增内容域--手机号关联手机号"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "mobile1_$contentCode",
        "contentName": "mobile1_$name$contentCode",
        "dataSource": "user",
        "formatType": 1,
        "required": 1,
        "description": "寒轩测试内容域-手机号哈哈哈啦啦啦啊",
        "formatRule": "",
        "sourceField": "userMobile"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC3-寒轩测试内容域-邮箱"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "email_$contentCode",
        "contentName": "email_$name$contentCode",
        "dataSource": "user",
        "formatType": 3,
        "required": 1,
        "description": "哈哈哈哈啦啦啦啦",
        "formatRule": "",
        "sourceField": "userEmail"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC4-寒轩测试内容域---统一社会信用代码"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "code_$contentCode",
        "contentName": "code_$name$contentCode",
        "dataSource": "user",
        "formatType": 4,
        "required": 1,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC5-寒轩测试内容域--数字"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "number_$contentCode",
        "contentName": "number_$name$contentCode",
        "dataSource": "user",
        "formatType": 6,
        "required": 1,
        "description": "顶顶顶顶",
        "formatRule": "999,0",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC6-寒轩测试内容域--日期"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "date_$contentCode",
        "contentName": "date_$name$contentCode",
        "dataSource": "user",
        "formatType": 7,
        "required": 1,
        "description": "啦啦啦啦",
        "formatRule": "yyyy年MM月dd日",
        "sourceField": "userEmail"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC7-新增内容域--身份证"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "sfz_$contentCode",
        "contentName": "sfz_$name$contentCode",
        "dataSource": "organize",
        "formatType": 2,
        "required": 1,
        "description": "ddd",
        "formatRule": "",
        "sourceField": "organizationTypeName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]