- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-upd"
      contentCode: ${get_randomNo_16()}
      space: " "


- test:
    name: "TC1-setup-新增内容域--可修改文本"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "formatType": 0,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC3-新增文本内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","$name$contentCode"]
      - eq: [ "content.data.formatType",0]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC4-更新文本内容域为不可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "formatType": 0,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC5-更新内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","$name$contentCode" ]
      - eq: [ "content.data.formatType",0 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",0 ]

- test:
    name: "TC6-setup-新增内容域--不可修改手机号"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "tel$name$contentCode",
        "formatType": 1,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "tel$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC8-新增手机号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","tel$name$contentCode"]
      - eq: [ "content.data.formatType",1]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",0]

- test:
    name: "TC9-更新手机号内容域为可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "tel$name$contentCode",
        "formatType": 1,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC10-更新手机号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","tel$name$contentCode" ]
      - eq: [ "content.data.formatType",1 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1 ]

- test:
    name: "TC11-setup-新增内容域--可修改证件号"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "sfz$name$contentCode",
        "formatType": 2,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC12-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "sfz$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC13-新增证件号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","sfz$name$contentCode" ]
      - eq: [ "content.data.formatType",2 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "14-更新证件号内容域为不可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "sfz$name$contentCode",
        "formatType": 2,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC15-更新证件号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","sfz$name$contentCode" ]
      - eq: [ "content.data.formatType",2 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",0 ]



- test:
    name: "TC16-setup-新增内容域--不可修改邮箱"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "mail$name$contentCode",
        "formatType": 3,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC17-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "mail$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC18-新增手机号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","mail$name$contentCode"]
      - eq: [ "content.data.formatType",3]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",0]

- test:
    name: "TC19-更新邮箱内容域为可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "mail$name$contentCode",
        "formatType": 3,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC20-更新邮箱内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","mail$name$contentCode" ]
      - eq: [ "content.data.formatType",3 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1 ]

- test:
    name: "TC21-setup-新增内容域--可修改信用代码"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "code$name$contentCode",
        "formatType": 4,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC22-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "code$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC23-新增信用代码内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","code$name$contentCode" ]
      - eq: [ "content.data.formatType",4 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC24-更新信用代码内容域为不可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "code$name$contentCode",
        "formatType": 4,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC25-更新信用代码内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","code$name$contentCode" ]
      - eq: [ "content.data.formatType",4 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",0 ]



- test:
    name: "TC26-setup-新增内容域--可修改数字"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "num$name$contentCode",
        "formatType": 6,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC27-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "num$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC28-新增数字内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","num$name$contentCode" ]
      - eq: [ "content.data.formatType",6 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC29-更新数字内容域为不可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "num$name$contentCode",
        "formatType": 6,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC30-更新数字内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","num$name$contentCode" ]
      - eq: [ "content.data.formatType",6 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",0]



- test:
    name: "TC31-setup-新增内容域--日期"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "",
        "contentName": "date$name$contentCode",
        "formatType": 7,
        "required": 1,
        "allowEdit": 1,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC32-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "date$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC33-新增日期内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","date$name$contentCode" ]
      - eq: [ "content.data.formatType",7]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC34-更新日期内容域为不可修改"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "date$name$contentCode",
        "formatType": 7,
        "required": 1,
        "allowEdit": 0,
        "formatRule": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC35-更新日期 内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","date$name$contentCode" ]
      - eq: [ "content.data.formatType",7 ]
      - eq: [ "content.data.required",1 ]
      - eq: [ "content.data.allowEdit",0 ]


- test:
    name: "TC36-获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]








