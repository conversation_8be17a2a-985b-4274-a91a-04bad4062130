- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-add"
      contentCode: ${get_randomNo_16()}
      ceswdzxzdhyhwgd1.account: "ceswdzxzdhyhwgd1.account"
      ceswdzxzdhyhwgd1.password: "ceswdzxzdhyhwgd1.password"


- test:
    name: "输入不存在的类型"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 120,
        "formatRule": "0",
        "formatSelect": null,
        "length": null,
        "isNeedLength": 0,
        "formatValue": "我"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - eq: [ "content.data.message","内容域校验规则不存在" ]

- test:
    name: "formatValue不传校验"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": null,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": null
      }
    validate:
      - eq: [ "content.message","校验内容不能为空" ]
      - eq: [ "content.status",1600017 ]
      - eq: [ "content.data",null ]
      - eq: [ "content.success",false ]

# 长度校验
- test:
    name: "输入文本类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 0,
        "formatRule": "0",
        "formatSelect": null,
        "length": 1,
        "isNeedLength": 1,
        "formatValue": "我我"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入文本类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 0,
        "formatRule": "0",
        "formatSelect": null,
        "length": 1,
        "isNeedLength": 1,
        "formatValue": "我"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入数字类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "0",
        "formatSelect": null,
        "length": 15,
        "isNeedLength": 1,
        "formatValue": "1211111111111111"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入数字类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "0",
        "formatSelect": null,
        "length": 15,
        "isNeedLength": 1,
        "formatValue": "121111111111111"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入邮箱类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 3,
        "formatRule": "0",
        "formatSelect": null,
        "length": 20,
        "isNeedLength": 1,
        "formatValue": "<EMAIL>"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入邮箱类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 3,
        "formatRule": "0",
        "formatSelect": null,
        "length": 20,
        "isNeedLength": 1,
        "formatValue": "<EMAIL>"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]


- test:
    name: "输入身份证类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 2,
        "formatRule": "0",
        "formatSelect": null,
        "length": 18,
        "isNeedLength": 1,
        "formatValue": "1101011990010845161"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入身份证类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 2,
        "formatRule": "0",
        "formatSelect": null,
        "length": 18,
        "isNeedLength": 1,
        "formatValue": "110101199001084516"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]


- test:
    name: "输入统一信用代码类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 4,
        "formatRule": "0",
        "formatSelect": null,
        "length": 18,
        "isNeedLength": 1,
        "formatValue": "9100000057452928U5U"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入统一信用代码类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 4,
        "formatRule": "0",
        "formatSelect": null,
        "length": 18,
        "isNeedLength": 1,
        "formatValue": "9100000057452928U5"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]


- test:
    name: "输入数字保留1位类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "1",
        "formatSelect": null,
        "length": 2,
        "isNeedLength": 1,
        "formatValue": "1.1"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入数字保留1位类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "1",
        "formatSelect": null,
        "length": 3,
        "isNeedLength": 1,
        "formatValue": "1.1"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入数字整数位的长度匹配的值(长度需要校验-保留1位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "1",
        "formatSelect": null,
        "length": 3,
        "isNeedLength": 1,
        "formatValue": "123"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入数字保留2位类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "2",
        "formatSelect": null,
        "length": 2,
        "isNeedLength": 1,
        "formatValue": "1.23"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入数字保留2位类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "2",
        "formatSelect": null,
        "length": 4,
        "isNeedLength": 1,
        "formatValue": "1.23"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入数字整数位长度匹配的值(长度需要校验-保留2位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "2",
        "formatSelect": null,
        "length": 4,
        "isNeedLength": 1,
        "formatValue": "1234"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "输入数字保留3位类型的长度不匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "3",
        "formatSelect": null,
        "length": 3,
        "isNeedLength": 1,
        "formatValue": "1.234"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式长度校验失败" ]

- test:
    name: "输入数字保留3位类型的长度匹配的值(长度需要校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "3",
        "formatSelect": null,
        "length": 5,
        "isNeedLength": 1,
        "formatValue": "1.234"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]


- test:
    name: "输入数字整数位长度匹配的值(长度需要校验-保留3位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "3",
        "formatSelect": null,
        "length": 5,
        "isNeedLength": 1,
        "formatValue": "12345"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",true ]

- test:
    name: "数字总长度不超过15位"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": "0",
        "formatSelect": null,
        "length": 16,
        "isNeedLength": 0,
        "formatValue": "1234567890987656"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","总长度不超过15位" ]

# 需要验证
- test:
    name: "数字总长度不超过15位"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "1234567890987656"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","总长度不超过15位" ]

- test:
    name: "数字格式以最后.结尾的校验"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "123456789098765."
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "数字保留1位小数位不匹配(6位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": 1,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "123.61"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "数字保留2位小数位不匹配(6位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": 1,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "12.613"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "数字保留3位小数位不匹配(6位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": 1,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "1.6135"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "数字保留3位小数位不匹配(6位)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": 1,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "1.6135"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入汉字不匹配的类型(数字类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "我哦我"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入字母不匹配的类型(数字类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "b"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入特殊符号不匹配的类型(数字类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": ",&"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入大小写字母组合不匹配的类型(数字类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "bZ"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入数字、汉字、符号、大小写字母组合(数字类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 6,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "1我#bC"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的值(身份证类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 2,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "12029393"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入匹配的值末尾带有字母(身份证类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 2,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "43242718990322794x"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入匹配的值前后带有空格(身份证类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 2,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": " 43242718990322794x "
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入符合规范的邮箱类型(邮箱类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 3,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "<EMAIL>"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不符合规范的邮箱类型(邮箱类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 3,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "<EMAIL>."
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入符合规范的前后带有空格的邮箱类型(邮箱类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 3,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": " <EMAIL>  "
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入非1开头的手机号(手机号类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 1,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "29158580023"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入12位以1开头的(手机号类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 1,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "191585800233"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入10位以1开头的(手机号类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 1,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "1915858002"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入11位以1开头的(手机号类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 1,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "19815858002"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不匹配的值(统一信用代码类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 4,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "91000000"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入统一信用代码首尾带空格(统一信用代码类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 4,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "   91000000G44DU4KQHB       "
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入匹配的统一信用代码(统一信用代码类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 4,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "91000000G44DU4KQHB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入yyyy年MM月dd日(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "yyyy年MM月dd日"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入yyyy/MM/dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "yyyy/MM/dd"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入yyyy-MM-dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "yyyy-MM-dd"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入yyyy年-MM月-dd日的日期类型(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022年10月01日"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入yyyy/MM/dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022/01/01"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入yyyy-MM-dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022-10-01"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不符合规范的yyyy年MM月dd日(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022年9月32日"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不符合规范的yyyy年MM月dd日(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022年100月2日"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的yyyy/MM/dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022/09/32"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的yyyy/MM/dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022/100/01"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的yyyy-MM-dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022-100-01"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的yyyy-MM-dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022-09-33"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的yyy.mm.dd(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "2022.09.33"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不匹配的日期值(日期类型校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 7,
        "formatRule": null,
        "formatSelect": null,
        "length": null,
        "isNeedLength": null,
        "formatValue": "w%我W"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入存在的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": ["A"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "A"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不存在的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": ["A"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "B"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入存在的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": ["A","B"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "B"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不存在的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": ["A","B"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入带转义符号不匹配的formatSelect格式(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": [\"A\" ^ \"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入带转义符号不匹配的formatSelect格式(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": [\"A\" | \"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入带转义符号的formatSelect格式，匹配到value的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": [\"A\",\"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "A"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入带转移符号的formatSelect格式，匹配不到value的值(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 9,
        "formatRule": null,
        "formatSelect": [\"A\",\"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "formatSelect输入空选项，匹配value为空的校验(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": [\"\",\"\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": ""
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "formatSelect输入空选项，匹配不到value校验(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": [\"\",\"\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "A"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入带转移符号的formatSelect格式(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": [\"A\",\"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入带转移符号的formatSelect格式匹配到value(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": [\"A\",\"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "A"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入不带转移符号的formatSelect格式匹配不到value(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": ["A","B"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

- test:
    name: "输入不带转移符号的formatSelect格式匹配到value(多选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": ["A","B"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "A"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "输入带转义符号不匹配的formatSelect格式(单选校验)"
    api: api/esignDocs/contentDomain/validContentDomain.yml
    variables:
      params: {
        "formatType": 10,
        "formatRule": null,
        "formatSelect": [\"A\" | \"B\"],
        "length": null,
        "isNeedLength": null,
        "formatValue": "AB"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.result",false ]
      - contains: [ "content.data.message","格式填写错误" ]

