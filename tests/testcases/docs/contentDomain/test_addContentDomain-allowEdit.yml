- config:
    name: 新增内容域属性可修改6.0.13.0-beta.5
    variables:
      page: 1
      size: 20
      name: "hx-test-add"
      contentCode: ${get_randomNo_16()}
      varAccount: "ceswdzxzdhyhwgd1.account"
      varPassword: "ceswdzxzdhyhwgd1.password"


- test:
    name: "TC1-新增内容域--文本"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "text$name$contentCode",
        "dataSource": "",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName",
        "allowEdit": 1
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "text$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增文本内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","text$name$contentCode"]
      - eq: [ "content.data.formatType",0]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC2-新增内容域--手机号"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "mobile$name$contentCode",
        "dataSource": "",
        "formatType": 1,
        "required": 1,
        "description": "",
        "formatRule": "",
        "sourceField": "userName",
        "allowEdit": 1
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "mobile$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增手机号内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","mobile$name$contentCode"]
      - eq: [ "content.data.formatType",1]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC3-测试内容域-邮箱"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "email$name$contentCode",
        "dataSource": "",
        "formatType": 3,
        "required": 1,
        "description": "哈哈哈哈啦啦啦啦",
        "formatRule": "",
        "sourceField": "userEmail",
        "allowEdit": 1
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "email$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增邮箱内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","email$name$contentCode"]
      - eq: [ "content.data.formatType",3]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]


- test:
    name: "TC4-统一社会信用代码"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "code$name$contentCode",
        "dataSource": "",
        "formatType": 4,
        "required": 1,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "userName",
        "allowEdit": 1
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "code$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增信用代码内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","code$name$contentCode"]
      - eq: [ "content.data.formatType",4]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]



- test:
    name: "TC5-测试内容域--数字"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "number$name$contentCode",
        "dataSource": "",
        "formatType": 6,
        "required": 1,
        "description": "顶顶顶顶",
        "formatRule": "999,0",
        "sourceField": "userName",
        "allowEdit": 1
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "number$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增数字内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","number$name$contentCode"]
      - eq: [ "content.data.formatType",6]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC6-测试内容域--日期"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "date$name$contentCode",
        "dataSource": "",
        "formatType": 7,
        "required": 1,
        "description": "啦啦啦啦",
        "formatRule": "yyyy年MM月dd日",
        "sourceField": "userEmail",
        "allowEdit": 1
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "date$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增日期内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","date$name$contentCode"]
      - eq: [ "content.data.formatType",7]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]

- test:
    name: "TC7-新增内容域--身份证"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "",
        "contentName": "sfz$name$contentCode",
        "dataSource": "",
        "formatType": 2,
        "required": 1,
        "description": "ddd",
        "formatRule": "",
        "sourceField": "organizationTypeName",
        "allowEdit": 1
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC-setup-查询domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "sfz$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC-新增身份证内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode", ""]
      - eq: [ "content.data.contentName","sfz$name$contentCode"]
      - eq: [ "content.data.formatType",2]
      - eq: [ "content.data.required",1]
      - eq: [ "content.data.allowEdit",1]


- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]