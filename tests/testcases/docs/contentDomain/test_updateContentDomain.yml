- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-upd"
      contentCode: ${get_randomNo_16()}
      space: " "

- test:
    name: "TC1-setup-新增内容域--文本"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-setup-新增内容域--文本2"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "second-$contentCode",
        "contentName": "second-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-更新内容域"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "edit-$contentCode",
        "contentName": "edit-$name$contentCode",
        "dataSource": "organize",
        "formatType": 1,
        "required": 0,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC5-更新内容域--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentCode","edit-$contentCode" ]
      - eq: [ "content.data.contentName","edit-$name$contentCode" ]
      - eq: [ "content.data.dataSource","organize" ]
      - eq: [ "content.data.sourceField","organizationName" ]
      - eq: [ "content.data.formatType",1 ]
      - eq: [ "content.data.required",0 ]

- test:
    name: "TC6-编辑内容域_输入内容域名称两端包含空格"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "edit-$contentCode",
        "contentName": "$space-edit-$name$contentCode$space",
        "dataSource": "organize",
        "formatType": 1,
        "required": 0,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-编辑内容域_输入内容域名称两端包含空格--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","-edit-$name$contentCode" ]

- test:
    name: "TC8-编辑内容域_输入内容域名称中间包含空格"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "edit-$contentCode",
        "contentName": "edit-$name$space$contentCode",
        "dataSource": "organize",
        "formatType": 1,
        "required": 0,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC9-编辑内容域_输入内容域名称中间包含空格--断言"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    headers: ${gen_main_headers_for_urlencoded()}
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - eq: [ "content.data.contentName","edit-$name$space$contentCode" ]

- test:
    name: "TC10-setup-新增内容域--文本3"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "repeat-$contentCode",
        "contentName": "repeat-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC12-编辑内容域_内容域名称不能重复"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "repeat-$name$contentCode",
        "dataSource": "organize",
        "formatType": 1,
        "required": 0,
        "description": "哈哈",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","内容域名称已存在" ]
      - eq: [ "content.success",false ]
      - eq: [ "content.status",1601002 ]

- test:
    name: "TC13-setup-新增内容域--文本3"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "three-$contentCode",
        "contentName": "three-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC14-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "three-$name$contentCode" }
    extract:
      - domainId3: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "setup-删除内容域"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId3 }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC15-编辑内容域_名称可以和已删除内容域名称重复"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "three-$contentCode",
        "contentName": "three-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC16-编辑内容域_是否必填选是"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "three-$contentCode",
        "contentName": "three-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC17-编辑内容域_是否必填选否"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "three-$contentCode",
        "contentName": "three-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 0,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC18-setup-新增内容域--文本4"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "four-$contentCode",
        "contentName": "four-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC19-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId4: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC20-setup-删除内容域"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId4 }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC21-编辑内容域_参数编码可以和已删除内容域编码重复"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "four-$contentCode",
        "contentName": "four-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC22-setup-新增内容域--文本5"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "five-$contentCode",
        "contentName": "five-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC23-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId5: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC24-编辑内容域_内容域参数编码不能重复"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId5,
        "contentCode": "four-$contentCode",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域参数编码已存在" ]
      - eq: [ "content.status",1601003 ]
      - eq: [ "content.success",false ]

- test:
    name: "TC25-编辑内容域_内容域参数编码非必填"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "four-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC26-编辑内容域_内容域关联数据源非必填"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId,
        "contentCode": "",
        "contentName": "four-$name$contentCode",
        "dataSource": null,
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC27-setup-新增内容域--文本6"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "six-$contentCode",
        "contentName": "six-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC28-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId6: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC29-setup-删除内容域"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId6 }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC30-编辑内容域_内容域被删除"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId6,
        "contentCode": "",
        "contentName": "four-$name$contentCode",
        "dataSource": null,
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域不存在" ]
      - eq: [ "content.success",false ]
      - eq: [ "content.status",1601001 ]



- test:
    name: "TC31-setup-新增内容域--文本7"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC32-setup-查询-domainId"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId7: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC33-编辑文本域_格式修改为文本"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC34-编辑文本域_编辑内容域_格式修改为手机号"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 1,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC35-编辑文本域_编辑内容域_格式修改为身份证号"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 2,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC36-编辑文本域_编辑内容域_格式修改为邮箱"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 3,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC37-编辑文本域_编辑内容域_格式修改为统一社会信用"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "user",
        "formatType": 4,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC38-编辑文本域_编辑内容域_格式修改为数字"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "",
        "formatType": 6,
        "required": 1,
        "description": "",
        "formatRule": "1",
        "sourceField": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC39-编辑文本域_编辑内容域_格式修改为日期"
    api: api/esignDocs/contentDomain/update.yml
    variables:
      params: {
        "domainId": $domainId7,
        "contentCode": "seven-$contentCode",
        "contentName": "seven-$name$contentCode",
        "dataSource": "",
        "formatType": 7,
        "required": 1,
        "description": "",
        "formatRule": "yyyy年MM月dd日",
        "sourceField": ""
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
