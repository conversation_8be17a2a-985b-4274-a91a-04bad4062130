- config:
    variables:
      page: 1
      size: 20
      name: "hx自动化测试内容域-权限"
      contentCode: ${get_randomNo_16()}
      space: " "
      varAccount: "ceswdzxzdhyhwgd1.account"
      varPassword: "ceswdzxzdhyhwgd1.password"

#--集团模板管理员登录操作内部组织所有的内容域---1--
- test:
    name: "TC1-新建内容域_选择的内容域数据来源保存正确-组织名称"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-获取内容域列表"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: { "page": $page,"size": $size,"contentName": $name }
    extract:
      - domainId1: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-新建内容域_选择的内容域数据来源保存正确-上级组织"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "parentOrganizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC4-获取内容域列表"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: { "page": $page,"size": $size,"contentName": $name }
    extract:
      - domainId3: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC8-获取内容域列表"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: { "page": $page,"size": $size,"contentName": $name }
    extract:
      - domainId4: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC9-新建内容域_选择的内容域数据来源保存正确-组织分类"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "organizationTypeName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC10-获取内容域列表"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: { "page": $page,"size": $size,"contentName": $name }
    extract:
      - domainId5: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


##----统一删除--------
- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      account: $varAccount
      password: $varPassword
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


