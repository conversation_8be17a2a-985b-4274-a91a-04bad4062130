- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-check"
      contentCode: ${get_randomNo_16()}
      space: " "

- test:
    name: "setup-hx-test新建内容域"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC1-新建内容域_内容域名称必填"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": null,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","内容域名称不能为空" ]
      - eq: [ "content.status",1600017 ]
      - eq: [ "content.success",false ]

- test:
    name: "TC2-新建内容域_输入内容域名称两端包含空格"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "twoSpace_$contentCode",
        "contentName": $space$name$contentCode$space,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域名称已存在" ]
      - eq: [ "content.status",1601002 ]
      - eq: [ "content.success",false ]


- test:
    name: "TC3-新建内容域_输入内容域名称中间包含空格"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "midSpace_$contentCode",
        "contentName": $name$space$space$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC4-新建内容域_内容域名称不能重复"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域名称已存在" ]
      - eq: [ "content.status",1601002 ]
      - eq: [ "content.success",false ]

- test:
    name: "TC5-新建内容域_输入内容域类型名称包含不被允许的特殊字符"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "no_allow_$contentCode",
        "contentName": "\/:*?",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域名称不能包含特殊字符" ]
      - eq: [ "content.status",1600017 ]
      - eq: [ "content.success",false ]


- test:
    name: "TC6/7-setup-新建内容域_名称可以和已删除内容域名称重复"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "del-$contentCode",
        "contentName": "del-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC6/7-setup-获取内容域列表-新建内容域_名称可以和已删除内容域名称重复"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "del-$name$contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC6/7-setup-删除内容域-新建内容域_名称可以和已删除内容域名称重复"
    api: api/esignDocs/contentDomain/deleteContentDomain.yml
    variables:
      params: { "domianId": $domainId }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC6-新建内容域_名称可以和已删除内容域名称重复"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "del-code-$contentCode",
        "contentName": "del-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-新建内容域_参数编码可以和已删除内容域参数编码重复"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "del-$contentCode",
        "contentName": "del--name-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC8-新建内容域_内容域名称最长100"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "100-$contentCode",
        "contentName": "$contentCode$contentCode$contentCode$contentCode$contentCode$contentCode$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域名称长度不能超过100" ]
      - eq: [ "content.status",1600017 ]
      - eq: [ "content.success",false ]

- test:
    name: "TC9-新建内容域_是否必填选是"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "yes-$contentCode",
        "contentName": "yes-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC10-新建内容域_是否必填选否"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "NO-$contentCode",
        "contentName": "No-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 0,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC11-新建内容域_后端幂等性"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    times: 2
    variables:
      params: {
        "domainId": "",
        "contentCode": "md-$contentCode",
        "contentName": "md-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

- test:
    name: "TC12-新建内容域_后端幂等性-断言"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: {
        "page": "$page",
        "size": "$size",
        "contentName": "md-$name$contentCode"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.total",1 ]

- test:
    name: "TC13-新建内容域_内容域说明非必填"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "noNeed-$contentCode",
        "contentName": "noNeed-$name$contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": null,
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC14-新建内容域_内容域参数编码不能重复"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","内容域名称已存在" ]
      - eq: [ "content.success",false ]
      - eq: [ "content.status",1601002 ]


- test:
    name: "TC15-新建内容域_内容域参数编码非必填"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": null,
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": null,
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC16-新建内容域_内容域关联数据源非必填"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": null,
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": null,
        "formatType": 0,
        "required": 1,
        "description": null,
        "formatRule": "",
        "sourceField": "userName"
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "TC17-新建内容域_选择的内容域数据来源保存正确-用户"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC18-新建内容域_选择的内容域数据来源保存正确-用户"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userMobile"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC19-新建内容域_选择的内容域数据来源保存正确-邮箱"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userEmail"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC20-新建内容域_选择的内容域数据来源保存正确-所属组织"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC21-新建内容域_选择的内容域数据来源保存正确-组织名称"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "organizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC22-新建内容域_选择的内容域数据来源保存正确-组织分类"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "organizationTypeName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC23-新建内容域_选择的内容域数据来源保存正确-上级组织"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name${get_randomNo_16()}",
        "dataSource": "organize",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "parentOrganizationName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


