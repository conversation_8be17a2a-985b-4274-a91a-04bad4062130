- config:
    variables:
      page: 1
      size: 20
      name: "hx-test-list"
      space: " "
      contentCode: ${get_randomNo_16()}

- test:
    name: "setup-hx-test新建内容域"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": $contentCode,
        "contentName": $name$contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-hx-test新建内容域2"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": $name$contentCode$name,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]

- test:
    name: "setup-hx-test新建内容域3"
    api: api/esignDocs/contentDomain/addContentDomain.yml
    variables:
      params: {
        "domainId": "",
        "contentCode": "${get_randomNo_16()}",
        "contentName": "$name $contentCode",
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "123",
        "formatRule": "",
        "sourceField": "userName"
      }

    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]


- test:
    name: "TC1-获取内容域列表-名称为空"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": null }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - len_ge: [ "content.data.list",1 ]


- test:
    name: "TC2-获取内容域列表-名称正常搜索"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name$contentCode }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - len_ge: [ "content.data.list",2]



- test:
    name: "TC3-获取内容域"
    api: api/esignDocs/contentDomain/getSingleContentDomain.yml
    variables:
      domainId: $domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - ne: [ "content.data",null ]


- test:
    name: "TC4-搜索内容域_输入内容域名称前后有空格"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "$space$name$contentCode$space" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - len_ge: [ "content.data.list",2]

- test:
    name: "TC5-搜索内容域_输入内容域名称中间包含空格"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": "$name $contentCode" }
    extract:
      - domainId: content.data.list.0.domainId
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.success",true ]
      - len_ge: [ "content.data.list",1]


- test:
    name: "获取内容域列表--删除"
    api: api/esignDocs/contentDomain/getContentDomainList.yml
    variables:
      params: { "page": $page,"size": $size,"contentName": $name }
    teardown_hooks:
      - ${deleteContentDomainByContentName($name)}
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]