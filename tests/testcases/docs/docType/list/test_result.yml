- config:
    name: "文档类型测试"
    variables:
      docName: "0402自动化测试延平文件类型4删除"
      docCode: "zdhcswjlx03026"

- test:
    name: "TC1-当前页够每页条数"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "0402自动化测试延平文件类型多条",
              "pageNo": 1,
              "pageSize": 3 }
    setup_hooks:
      - ${insert_doc_data7()}
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.total",5 ]
      - len_eq: [ "content.data.docTypeList",3 ]

- test:
    name: "TC2-正常查询"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "延平",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC3-当前页不够每页条数"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "0402自动化测试延平文件类型多条",
              "pageNo": 2,
              "pageSize": 3 }
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.total",5 ]
      - len_eq: [ "content.data.docTypeList",2 ]

- test:
    name: "TC4-查询被删除的文件类型存在"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "0402自动化测试延平文件类型4删除",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docName,$docCode)}
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.data.total",1 ]

- test:
    name: "TC5-存在的文件类型删除"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "0402自动化测试延平文件类型4删除",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
