- config:
    name: "测试pageSize"
    variables:
      docTypeName1: "0402自动化测试延平文件类型1"
      docTypeCode1: "zdhcswjlx04021"

- test:
    name: "TC1-正常查询"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "延平",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docTypeName1,$docTypeCode1)}
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC2-pageNo必填，不传"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docTypeName1,$docTypeCode1)}
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","pageNo不能为null" ]

- test:
    name: "TC3-pageNo为null"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": null,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","pageNo不能为null" ]

- test:
    name: "TC4-pageNo为空格"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": " ",
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","pageNo不能为null" ]

- test:
    name: "TC5-pageNo为非int类型，为String"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": "哈哈哈",
              "pageSize": 10 }
    validate:
      - eq: [ "content.message","pageNo参数错误!" ]

- test:
    name: "TC6-pageNo为非正整数，为-1"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": -1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","pageNo不能小于1" ]

- test:
    name: "TC7-pageNo为2147483647"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 2147483647,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC8-pageNo为2147483648"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 2147483648,
              "pageSize": 10 }
    validate:
      - eq: [ "content.message","pageNo参数错误!" ]

- test:
    name: "TC9-pageNo为非int类型，为1.5"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1.5,
              "pageSize": 10 }
    validate:
      - eq: [ "content.message","pageNo参数错误!" ]


- test:
    name: "TC10-pageSize为非正整数，为0"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 0,
              "pageSize": 10 }
    validate:
      - eq: [ "content.message","pageNo不能小于1" ]

- test:
    name: "TC11-pageSize为1"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]