- config:
    name: "文档类型测试"
    variables:
      docName: "0402自动化测试延平文件类型2"
      docCode: "zdhcswjlx01023"
      docName2: "0402自动化测试 延平文件类型2"
      docCode2: "zdhcswjlx01024"
      docName4: "这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字自动化测试"
      docCode4: "zdhcswjlx05025"
      docName5: "这五十个字这五十个字这五十个字这五十个字自动化测试"
      docCode5: "zdhcswjlx04026"



- test:
    name: "TC1-docTypeName非必填"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docName,$docCode)}
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC2-docTypeName模糊搜索"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "402自动化测试延平",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC3-docTypeName两端带有空格"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": " 0402自动化测试延平文件类型 ",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC4-docTypeName中间带有空格"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "0402自动化测试 延平文件类型2",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docName2,$docCode2)}
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC5-docTypeName为null"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": null,
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC6-docTypeName为空格"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "   ",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC7-docTypeName为非String类型，为数字"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": 402,
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

#- test:
#    name: "TC8-docTypeName为非String类型，为boolean"
#    api: api/esignDocs/documents/docType/list.yml
#    variables:
#      json: { "docTypeName": true,
#              "pageNo": 1,
#              "pageSize": 10 }
#    validate:
#      - eq: [ "content.code",200 ]
#      - ge: [ "content.data.total",1 ]

- test:
    name: "TC9-docTypeName长度为1"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "延",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC10-docTypeName长度为50"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字自动化测试",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docName4,$docCode4)}
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC11-docTypeName长度为25"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "这五十个字这五十个字这五十个字这五十个字自动化测试",
              "pageNo": 1,
              "pageSize": 10 }
    setup_hooks:
      - ${insert_doc_data1($docName5,$docCode5)}
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC12-docTypeName长度为51"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字这五十个字多",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",1600017 ]
      - eq: [ "content.message","名称长度不能大于50" ]

- test:
    name: "TC13-docTypeName带有特殊字符"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "\/:*?<>|",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",1600017 ]
      - startswith: [ "content.message","docTypeName包含特殊字符" ]