- config:
    name: "测试pageSize"
    variables:
      docTypeName1: "0402自动化测试延平文件类型1"
      docTypeCode1: "zdhcswjlx04021"

- test:
    name: "TC1-pageSize必填，不传"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1 }
    setup_hooks:
      - ${insert_doc_data1($docTypeName1,$docTypeCode1)}
    validate:
     # - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能为null" ]

- test:
    name: "TC2-pageSize为null"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": null }
    validate:
    #  - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能为null" ]

- test:
    name: "TC3-pageSize为非int类型，为String"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": "adf" }
    validate:
   #   - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC4-pageSize为非int类型，为2147483648"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 2147483648 }
    validate:
   #   - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC5-pageSize为非int类型，为0"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 0 }
    validate:
    #  - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能小于1" ]

- test:
    name: "TC6-pageSize为非int类型，为-1"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": -1 }
    validate:
   #   - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能小于1" ]

- test:
    name: "TC7-pageSize为非int类型，为2.46"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 2.46 }
    validate:
  #    - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize参数错误!" ]

- test:
    name: "TC8-pageSize为2147483647"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 2147483647 }
    validate:
   #   - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能大于50" ]

- test:
    name: "TC9-pageSize为3"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 3 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC10-pageSize为50"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 50 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]

- test:
    name: "TC11-pageSize为51"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "",
              "pageNo": 1,
              "pageSize": 51 }
    validate:
   #   - eq: [ "content.code",400 ]
      - eq: [ "content.message","pageSize不能大于50" ]

- test:
    name: "TC12-正常查询"
    api: api/esignDocs/documents/docType/list.yml
    variables:
      json: { "docTypeName": "延平",
              "pageNo": 1,
              "pageSize": 10 }
    validate:
      - eq: [ "content.code",200 ]
      - ge: [ "content.data.total",1 ]