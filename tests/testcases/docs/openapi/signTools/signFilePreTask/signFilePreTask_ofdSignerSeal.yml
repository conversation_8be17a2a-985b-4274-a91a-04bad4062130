- config:
    name: "pdf文件支持中国标准和国际印章"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.sealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(org01.sealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}

       #公章下的印章id
       org01CommonSeal: ${ENV(org01.sealId)}
      #内部用户的主责是部门
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
#       fileKey1: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(2PageFileKey)}
#       signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey1 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]


- test:
    name: "signFilePreTask-企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]

- test:
    name: "save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey1,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-企业+企业经办人指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "sealId": "${ENV(org01.cloud.guomi.SealId)}",
                     "signatureType": "COMMON-SEAL"
                   },{
                   "sealId": "${ENV(org01.sealId)}",
                   "signatureType": "COMMON-SEAL"
                 },
                   {
                     "sealId": "${ENV(sign01.cloud.SealId)}",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.sealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.cloud.guomi.SealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]
- test:
    name: "signFilePreTask-企业+企业经办人+法人指定印章只有商密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                   "sealId": "${ENV(org01.sealId)}",
                   "signatureType": "COMMON-SEAL"
                 },
                   {
                     "sealId": "${ENV(sign01.cloud.SealId)}",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.sealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]
- test:
    name: "signFilePreTask-法人指定印章只有商密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "sealId": "${ENV(org01.legal.sealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]
- test:
    name: "signFilePreTask-企业+企业经办人指定印章只有国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "sealId": "${ENV(org01.cloud.guomi.SealId)}",
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "sealId": "${ENV(sign01.cloud.guomi.SealId)}",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.cloud.guomi.SealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]

- test:
    name: "save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey1,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "sealId": "${ENV(org01.cloud.guomi.SealId)}",
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "sealId": "${ENV(sign01.cloud.SealId)}",
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "sealId": "${ENV(org01.legal.cloud.guomi.SealId)}",
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask指定印章类型未指定印章id"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [

                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "sealTypeCode": "COMMON-SEAL,LEGAL-PERSON-SEAL",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]


- test:
    name: "signFilePreTask-内部个人--ukey商密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                 {
                   "sealId": "${ENV(sign01.ukey.business.SealId)}",
                   "signatureType": "PERSON-SEAL"
                 }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": ""
           }
         ]

    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]
- test:
    name: "signFilePreTask-内部个人--ukey国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                 {
                   "sealId": "${ENV(sign01.ukey.guomi.SealId)}",
                   "signatureType": "PERSON-SEAL"
                 }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": ""
           }
         ]
    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]

- test:
    name: "signFilePreTask-内部个人--ukey国密+商密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                 {
                      "sealId": "${ENV(sign01.ukey.business.SealId)}",
                      "signatureType": "PERSON-SEAL"
                    },
                 {
                   "sealId": "${ENV(sign01.ukey.guomi.SealId)}",
                   "signatureType": "PERSON-SEAL"
                 }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": ""
           }
         ]
    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]
- test:
    name: "signFilePreTask-内部个人--商密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                 {
                      "sealId": "${ENV(sign01.cloud.SealId)}",
                      "signatureType": "PERSON-SEAL"
                    }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": ""
           }
         ]

    validate:
         - eq: [content.code, 1623068]
         - contains: [content.message,"不是中国标准印章"]
         - eq: [content.data, null]

- test:
    name: "signFilePreTask-内部个人--国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                 {
                      "sealId": "${ENV(sign01.cloud.guomi.SealId)}",
                      "signatureType": "PERSON-SEAL"
                    }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": ""
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]
