- config:
    name: "openapi链路用例：【1、签署区设置任务发起->保存签署区设置->openapi查看详情->使用详情参数一步发起】【2、新老数据兼容】"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.sealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(org01.sealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealIdGuomi: ${ENV(org01.cloud.guomi.SealId)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}
       legalSealTypeCode22: "LEGAL-PERSON-SEAL"

       #公章下的印章id
       org01CommonSeal: ${ENV(org01.sealId)}
      #内部用户的主责是部门
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
       fileKey0: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(2PageFileKey)}
       domainHost: ${ENV(esign.projectHost)}
       outerDomainHost: ${ENV(esign.projectOuterHost)}
       signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]
       roleorgId0: "$orgCode0:$userCode0"

- test:
    name: "无序签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]
         #60140-beta1-内外网隔离-接口新增出参
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId0
    extract:
      - editUrl0: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl0)}
      extract:
        - contentId0: content.data.0.contentId
        - documentId0: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId0,null]
      - ne: [$documentId0,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId0
          entityId1: $documentId0
          tplToken_content: ${getTplToken($editUrl0)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId0: content.data.baseFile.resourceId
        - baseFile0: content.data.baseFile

- test:
      name: "保存草稿-只设置其中一个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl0)}
          baseFile_draft: $baseFile0
          originFile_draft: {resourceId: "$resourceId0"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId0
          entityId_draft: $documentId0
          signpersonfield0: ${epaasSignContentField(1,$userCode0,1)}
#          signorgfield0: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield0]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId0 ]


- test:
      name: "只设置其中一个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "详情查询"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.signerInfos.0.sealInfos, [] ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signNode, 2 ]
      - eq: [ content.data.signerInfos.1.signNode, 2 ]
      - eq: [ content.data.signerInfos.0.signMode, 1 ]
      - eq: [ content.data.signerInfos.1.signMode, 1 ]
- test:
      name: "保存草稿-设置其中2个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl0)}
          baseFile_draft: $baseFile0
          originFile_draft: {resourceId: "$resourceId0"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId0
          entityId_draft: $documentId0
          signpersonfield0: ${epaasSignContentField(1,$userCode0,1)}
          signorgfield0: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield0,$signorgfield0]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId0 ]


- test:
      name: "设置2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "无序签署-signFilePreTaskDetail查询详情--有bug"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.signerInfos.0.sealInfos, null ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signNode, 2 ]
      - eq: [ content.data.signerInfos.1.signNode, 2 ]
      - eq: [ content.data.signerInfos.0.signMode, 1 ]
      - eq: [ content.data.signerInfos.1.signMode, 1 ]

- test:
    name: "无序签署-使用签署区设置的详情参数到签署一步发起--有bug"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

#顺序签
- test:
    name: "顺序签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0"
           }
         ]
    extract:
        filePreTaskId1: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]
         #60140-beta1-内外网隔离-接口新增出参
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId1
    extract:
      - editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl1)}
      extract:
        - contentId11: content.data.0.contentId
        - documentId11: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId0,null]
      - ne: [$documentId0,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId11
          entityId1: $documentId11
          tplToken_content: ${getTplToken($editUrl1)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId1: content.data.baseFile.resourceId
        - baseFile1: content.data.baseFile

- test:
      name: "保存草稿-只设置其中一个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl1)}
          baseFile_draft: $baseFile1
          originFile_draft: {resourceId: "$resourceId1"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId11
          entityId_draft: $documentId11
          signpersonfield0: ${epaasSignContentField(1,$userCode0,1)}
#          signorgfield0: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield0]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId11 ]


- test:
      name: "只设置其中一个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "顺序签签署-signFilePreTaskDetail查询详情--有bug"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.signerInfos.0.sealInfos, [] ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signNode, 1 ]
      - eq: [ content.data.signerInfos.1.signNode, 2 ]
- test:
      name: "保存草稿-设置其中2个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl1)}
          baseFile_draft: $baseFile1
          originFile_draft: {resourceId: "$resourceId1"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId11
          entityId_draft: $documentId11
          signpersonfield1: ${epaasSignContentField(1,$userCode0,1)}
          signorgfield1: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield1,$signorgfield1]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId11 ]


- test:
      name: "设置2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "顺签签署-signFilePreTaskDetail查询详情--有bug"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.signerInfos.0.sealInfos, null ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signMode, 0 ]
      - eq: [ content.data.signerInfos.1.signMode, 0 ]
      - eq: [ content.data.signerInfos.0.signNode, 1 ]
      - eq: [ content.data.signerInfos.1.signNode, 2 ]
- test:
    name: "顺签签署-使用签署区设置的详情参数到签署一步发起--有bug"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles1
      signerInfos: $signerInfos1
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

#或签
- test:
    name: "或签-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 2,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 2,
             "userType": 1,
             "userCode": "$userCode0"
           }
         ]
    extract:
        filePreTaskId2: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]
         #60140-beta1-内外网隔离-接口新增出参
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "获取盖章配置详情-或签"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId2
    extract:
      - editUrl2: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl2)}
      extract:
        - contentId2: content.data.0.contentId
        - documentId2: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId0,null]
      - ne: [$documentId0,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId2
          entityId1: $documentId2
          tplToken_content: ${getTplToken($editUrl2)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId2: content.data.baseFile.resourceId
        - baseFile2: content.data.baseFile

- test:
      name: "保存草稿-只设置其中一个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl2)}
          baseFile_draft: $baseFile2
          originFile_draft: {resourceId: "$resourceId2"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId2
          entityId_draft: $documentId2
          signpersonfield0: ${epaasSignContentField(1,$userCode0,1)}
#          signorgfield0: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield0]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId2 ]


- test:
      name: "只设置其中一个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "或签签署-signFilePreTaskDetail查询详情--有bug"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - eq: [ content.data.signerInfos.0.sealInfos, [] ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signNode, 1 ]
      - eq: [ content.data.signerInfos.1.signNode, 1 ]
- test:
      name: "保存草稿-设置其中2个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl2)}
          baseFile_draft: $baseFile2
          originFile_draft: {resourceId: "$resourceId2"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId2
          entityId_draft: $documentId2
          signpersonfield2: ${epaasSignContentField(1,$userCode0,1)}
          signorgfield2: ${epaasSignContentField(1,$roleorgId0,2)}
          fields_draft: [$signpersonfield2,$signorgfield2]
      validate:
        - eq: [content.code,0]
        
        - eq: [ content.data.0.contentId, $contentId2 ]


- test:
      name: "设置2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "或签签署-signFilePreTaskDetail查询详情--有bug"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data.signerInfos.0.sealInfos, null ]
      - ne: [ content.data.signerInfos.1.sealInfos, null ]
      - eq: [ content.data.signerInfos.0.signMode, 2 ]
      - eq: [ content.data.signerInfos.1.signMode, 2 ]
      - eq: [ content.data.signerInfos.0.signNode, 1 ]
      - eq: [ content.data.signerInfos.1.signNode, 1 ]
- test:
    name: "或签签署-使用签署区设置的详情参数到签署一步发起--有bug"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles2
      signerInfos: $signerInfos2
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "签署方内部企业，指定印章进行企业、经办人、法人签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
          {
          "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealId0"
                    },
                    {
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "sealId": "$org01LegalSealId"
                    },
                    {
                        "signatureType": "PERSON-SEAL",
                        "sealId": $userSealId0,
                    }
                ]
            }
        ],
        "legalSignFlag": true,
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0"
    }
        ]
    extract:
        filePreTaskId3: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]