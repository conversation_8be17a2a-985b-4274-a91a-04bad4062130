- config:
    name: "校验签署区"

- test:
    name: $name
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      expirationDate: $expirationDate
      signerInfos: $signerInfos
    extract:
      - filePreTaskId0: content.data.filePreTaskId
    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message]
      - ne: [ content.data, $data ]


#- test:
#    name: "查询filePreTaskId详情"
#    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
#    variables:
#      filePreTaskId: $filePreTaskId0
#    validate:
#      - eq: [ content.code, $code ]
#      - contains: [ content.message, $message]
#      - ne: [ content.data, $data ]