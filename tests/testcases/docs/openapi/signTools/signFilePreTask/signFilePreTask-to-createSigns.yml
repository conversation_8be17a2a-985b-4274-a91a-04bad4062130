- config:
    name: "openapi链路用例：【1、签署区设置任务发起->保存签署区设置->openapi查看详情->使用详情参数一步发起】【2、新老数据兼容】"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.sealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(org01.sealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealIdGuomi: ${ENV(org01.cloud.guomi.SealId)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}
       legalSealTypeCode22: "LEGAL-PERSON-SEAL"

       #公章下的印章id
       org01CommonSeal: ${ENV(org01.sealId)}
      #内部用户的主责是部门
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
       fileKey0: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(2PageFileKey)}
       domainHost: ${ENV(esign.projectHost)}
       outerDomainHost: ${ENV(esign.projectOuterHost)}
       signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]


- test:
    name: "无序签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]
         #60140-beta1-内外网隔离-接口新增出参
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "setup-查询用户的企业授权印章个数-法人章-云国际标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealTypeCode: "$legalSealTypeCode22"
      organizationSeals_list_sealPattern: 1 #国际标准标准印章
    extract:
      totalSeals1_legal: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]
      - gt: [ content.data.total, 0]

- test:
    name: "setup-查询用户的企业授权印章个数-所有印章类型-云国际标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealPattern: 1 #国际标准标准印章
    extract:
      totalSeals1: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]
      - gt: [ content.data.total, 2]

- test:
    name: "setup-查询用户的企业授权印章个数-法人章-云中国标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealTypeCode: "$legalSealTypeCode22"
      organizationSeals_list_sealPattern: 3 #中国标准标准印章
    extract:
      totalSeals2_legal: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]

- test:
    name: "setup-查询用户的企业授权印章个数-所有印章类型-云中国标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealPattern: 3 #中国标准标准印章
    extract:
      totalSeals2: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]


- test:
    name: "无序签署-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            },{
                 "sealInfos": [
                    {
                       "fileKey": $fileKey0,
                       "signConfigs": [
                          {
                             "addSignDate": false,
                             "keywordInfo": null,
                             "pageNo": "1",
                             "posX": 416,
                             "posY": 623,
                             "edgeScope": null,
                             "sealSignDatePositionInfo": null,
                             "signPosName": ,
                             "signType": "COMMON-SIGN",
                             "signatureType": "PERSON-SEAL",
                             "allowMove": false
                          }
                       ],
                       "sealIdList": [ ],
                       "signatureTypeList": [
                          "PERSON-SEAL"
                       ]
                    }
                 ],
                 "signerId": $userCode0,
                 "userType": 1,
                 "userCode": $userCode0,
                 "userName": $userName0,
                 "signerType": 1,
                 "legalSignFlag": 0,
                 "userId": null,
                 "organizationId": null,
                 "organizationCode": null,
                 "organizationName": null,
                 "departmentName": $orgName0,
                 "departmentCode": $departmentCode0,
                 "sealTypeCode": null,
                 "sealTypeName": null,
                 "needShowSeal": 0,
                 "personRealnameStatus": true,
                 "orgRealnameStatus": false
              }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "无序签署-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      #60140-beta1-内外网隔离-接口新增出参
      - contains: [ content.data.filePreUrl, "$domainHost" ]
      - contains: [ content.data.filePreOuterUrl, "$outerDomainHost" ]

- test:
    name: "无序签署-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

#####第2个场景
- test:
    name: "或签签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0" } ]
    extract:
        filePreTaskId1: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]

- test:
    name: "或签签署-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            },{
                 "sealInfos": [
                    {
                       "fileKey": $fileKey0,
                       "signConfigs": [
                          {
                             "addSignDate": false,
                             "keywordInfo": null,
                             "pageNo": "1",
                             "posX": 416,
                             "posY": 623,
                             "edgeScope": null,
                             "sealSignDatePositionInfo": null,
                             "signPosName": ,
                             "signType": "COMMON-SIGN",
                             "signatureType": "PERSON-SEAL",
                             "allowMove": false
                          }
                       ],
                       "sealIdList": [ ],
                       "signatureTypeList": [
                          "PERSON-SEAL"
                       ]
                    }
                 ],
                 "signerId": $userCode0,
                 "userType": 1,
                 "userCode": $userCode0,
                 "userName": $userName0,
                 "signerType": 1,
                 "legalSignFlag": 0,
                 "userId": null,
                 "organizationId": null,
                 "organizationCode": null,
                 "organizationName": null,
                 "departmentName": $orgName0,
                 "departmentCode": $departmentCode0,
                 "sealTypeCode": null,
                 "sealTypeName": null,
                 "needShowSeal": 0,
                 "personRealnameStatus": true,
                 "orgRealnameStatus": false
              }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "或签签署-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "或签签署-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles1
      signerInfos: $signerInfos1
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]
#####第3个场景
- test:
    name: "顺序签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 10,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ]
    extract:
        filePreTaskId2: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]

- test:
    name: "顺序签署-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            },{
                 "sealInfos": [
                    {
                       "fileKey": $fileKey0,
                       "signConfigs": [
                          {
                             "addSignDate": false,
                             "keywordInfo": null,
                             "pageNo": "1",
                             "posX": 416,
                             "posY": 623,
                             "edgeScope": null,
                             "sealSignDatePositionInfo": null,
                             "signPosName": ,
                             "signType": "COMMON-SIGN",
                             "signatureType": "PERSON-SEAL",
                             "allowMove": false
                          }
                       ],
                       "sealIdList": [ ],
                       "signatureTypeList": [
                          "PERSON-SEAL"
                       ]
                    }
                 ],
                 "signerId": $userCode0,
                 "userType": 1,
                 "userCode": $userCode0,
                 "userName": $userName0,
                 "signerType": 1,
                 "legalSignFlag": 0,
                 "userId": null,
                 "organizationId": null,
                 "organizationCode": null,
                 "organizationName": null,
                 "departmentName": $orgName0,
                 "departmentCode": $departmentCode0,
                 "sealTypeCode": null,
                 "sealTypeName": null,
                 "needShowSeal": 0,
                 "personRealnameStatus": true,
                 "orgRealnameStatus": false
              }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "顺序签署-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "顺序签署-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles2
      signerInfos: $signerInfos2
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

#####第4个场景
- test:
    name: "签署方内部企业，指定印章进行企业、经办人、法人签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
          {
          "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealId0"
                    },
                    {
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "sealId": "$org01LegalSealId"
                    },
                    {
                        "signatureType": "PERSON-SEAL",
                        "sealId": $userSealId0,
                    }
                ]
            }
        ],
        "legalSignFlag": true,
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0"
    }
        ]
    extract:
        filePreTaskId3: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
    name: "签署方内部企业，指定印章进行企业、经办人、法人签署-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
        filePreTaskId: $filePreTaskId3
        filePreTaskInfos:
          [
            {
            "sealInfos": [
              {
                "fileKey": $fileKey0,
                "signConfigs": [
                  {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 223,
                      "posY": 695,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": $org01LegalSealId,
                      "signPosName": $userCode0-$orgCode0,
                      "signType": "COMMON-SIGN",
                      "signatureType": "LEGAL-PERSON-SEAL",
                      "allowMove": false
                  },
                  {
                    "addSignDate": false,
                    "keywordInfo": null,
                    "pageNo": "1",
                    "posX": 299,
                    "posY": 519,
                    "edgeScope": null,
                    "sealSignDatePositionInfo": null,
                    "sealId": $userSealId0,
                    "signPosName": $userCode0-$orgCode0,
                    "signType": "COMMON-SIGN",
                    "signatureType": "PERSON-SEAL",
                    "allowMove": false
                  },
                  {
                    "addSignDate": false,
                    "keywordInfo": null,
                    "pageNo": "1",
                    "posX": 334,
                    "posY": 228,
                    "edgeScope": null,
                    "sealSignDatePositionInfo": null,
                    "sealId": $sealId0,
                    "signPosName": $userCode0-$orgCode0,
                    "signType": "COMMON-SIGN",
                    "signatureType": "COMMON-SEAL",
                    "allowMove": false
                  },
                  {
                    "addSignDate": false,
                    "keywordInfo": null,
                    "pageNo": "1",
                    "posX": 433,
                    "posY": 401,
                    "edgeScope": null,
                    "sealSignDatePositionInfo": null,
                    "sealId": $org01LegalSealId,
                    "signPosName": $userCode0-$orgCode0,
                    "signType": "COMMON-SIGN",
                    "signatureType": "LEGAL-PERSON-SEAL",
                    "allowMove": false
                  },
                  {
                    "addSignDate": false,
                    "keywordInfo": null,
                    "pageNo": "2",
                    "posX": 223,
                    "posY": 636,
                    "edgeScope": null,
                    "sealSignDatePositionInfo": null,
                    "sealId": $userSealId0,
                    "signPosName": $userCode0-$orgCode0,
                    "signType": "COMMON-SIGN",
                    "signatureType": "PERSON-SEAL",
                    "allowMove": false
                  }
                ],
                "sealIdList": [
                  {
                  "signatureType": "COMMON-SEAL",
                  "sealId": $sealId0
                },
                  {
                  "signatureType": "LEGAL-PERSON-SEAL",
                  "sealId": $org01LegalSealId
                },
                  {
                  "signatureType": "PERSON-SEAL",
                  "sealId": $userSealId0
                }
                ],
                "signatureTypeList": [
                    "LEGAL-PERSON-SEAL",
                    "PERSON-SEAL",
                    "COMMON-SEAL"
                ]
              }
            ]
          ,
            "signerId": $userCode0,
            "userType": 1,
            "userCode": $userCode0,
            "userName": $userName0,
            "signerType": 2,
            "legalSignFlag": 1,
            "userId": null,
            "organizationId": null,
            "organizationCode": $orgCode0,
            "organizationName": $orgName0,
            "departmentName": $orgName0,
            "departmentCode": $orgCode0,
            "sealTypeCode": null,
            "sealTypeName": null,
            "needShowSeal": 1,
            "personRealnameStatus": true,
            "orgRealnameStatus": true
            }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "签署方内部企业，指定印章进行企业、经办人、法人签署-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "签署方内部企业，指定印章进行企业、经办人、法人签署-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]
#####第5个场景
- test:
    name: "签署方内部个人，指定印章Id-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "PERSON-SEAL",
                        "sealId": "$userSealId0"
                    }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0"
    }
]
    extract:
        filePreTaskId4: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId4]

- test:
    name: "签署方内部个人，指定印章Id-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": $fileKey0,
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 675,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$userSealId0",
                        "signPosName": "$userName0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [
                      {
                        "signatureType": "PERSON-SEAL",
                        "sealId": "$userSealId0"
                      }
                    ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 1,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "签署方内部个人，指定印章Id-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId4
    extract:
      - signerInfos4: content.data.signerInfos
      - signFiles4: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "签署方内部个人，指定印章Id-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles4
      signerInfos: $signerInfos4
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


#####第6个场景
- test:
    name: "签署方内部企业，指定国际标准印章Id-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "",
                        "sealId": "$sealId0"
                    }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0"
    }
]
    extract:
        filePreTaskId5: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]


- test:
    name: "签署方内部企业，指定国际标准印章Id-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 314,
                        "posY": 657,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$sealId0",
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [
                      {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealId0"
                      }
                    ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 1,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "签署方内部企业，指定国际标准印章Id-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "签署方内部企业，指定国际标准印章Id-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

####
- test:
    name: "签署方内部企业，指定中国标准印章Id-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealIdGuomi"
                    }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0"
    }
]
    extract:
        filePreTaskId5: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]

- test:
    name: "签署方内部企业，指定中国标准印章Id-签署区配置页面查询企业签署区的印章id列表"
    api: api/esignDocs/openapi/signTools/seals.yml
    variables:
      organizationCode: "$orgCode0"
      userCode: "$userCode0"
      sealIdList: [ { "signatureType": "COMMON-SEAL","sealId": "$sealIdGuomi" } ]
    extract:
      officialSealId: content.data.officialSeal.seals.0.sealId
    validate:
      - eq: [ content.status,$successCode ]
      - eq: [ content.message,$successMessage ]
      - eq: [ $officialSealId, $sealIdGuomi ]

- test:
    name: "签署方内部企业，指定中国标准印章Id-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 314,
                        "posY": 657,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$sealIdGuomi",
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [
                      {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealIdGuomi"
                      }
                    ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 1,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "签署方内部企业，指定中国标准印章Id-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "签署方内部企业，指定中国标准印章Id-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

###
- test:
    name: "签署方内部企业，指定多个印章：中国标准印章id和国际标准印章id-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealIdGuomi"
                    },
                  {
                    "signatureType": "COMMON-SEAL",
                    "sealId": "$sealId0"
                  }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0"
    }
]
    extract:
        filePreTaskId5: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]

- test:
    name: "签署方内部企业，指定多个印章：中国标准印章id和国际标准印章id-签署区配置页面查询企业签署区的印章id列表"
    api: api/esignDocs/openapi/signTools/seals.yml
    variables:
      organizationCode: "$orgCode0"
      userCode: "$userCode0"
      sealIdList: [{"signatureType":"COMMON-SEAL","sealId":"$sealIdGuomi"},{"signatureType":"COMMON-SEAL","sealId":"$sealId0"}]
    validate:
      - eq: [ content.status,$successCode ]
      - eq: [ content.message,$successMessage]
      - len_eq: [ content.data.officialSeal.seals, 2 ]
      - eq: [ content.data.officialSeal.seals.0.sealId, "$sealIdGuomi"]
      - eq: [ content.data.officialSeal.seals.1.sealId, "$sealId0"]

#####第7个场景
- test:
    name: "内部企业签署，指定印章类型sealTypeCode-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": ""
                    }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0",
        "sealTypeCode": " ${ENV(orgSealTypeCode)} "
    }
]
    extract:
        filePreTaskId6: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId6]

- test:
    name: "内部企业签署，指定印章类型sealTypeCode-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId6
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 273,
                        "posY": 714,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "1526436822345682946", #todo 改为根据sealTypeCode获取印章
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": "COMMON-SEAL",
                "sealTypeName": "公章",
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "内部企业签署，指定印章类型sealTypeCode-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId6
    extract:
      - signerInfos6: content.data.signerInfos
      - signFiles6: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "内部企业签署，指定印章类型sealTypeCode-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles6
      signerInfos: $signerInfos6
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

######
- test:
    name: "内部企业签署，指定印章类型sealTypeCode为公章（公章下包含中国标准印章和国际标准印章）-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos: [
    {
        "sealInfos": [
            {
                "fileKey": "$fileKey0",
                "signConfigs": [
                    {
                        "signatureType": "COMMON-SEAL",
                        "sealId": ""
                    }
                ]
            }
        ],
        "signNode": 1,
        "signMode": 0,
        "userType": 1,
        "userCode": "$userCode0",
        "organizationCode": "$orgCode0",
        "sealTypeCode": ""COMMON-SEAL""
    }
]
    extract:
        filePreTaskId6: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId6]

- test:
    name: "setup-内部企业签署，指定印章类型sealTypeCode为公章（公章下包含中国标准印章和国际标准印章）-查询用户的企业授权印章个数-云国际标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealPattern: 1 #国际标准标准印章
      organizationSeals_list_sealTypeCode: ""COMMON-SEAL""
    extract:
      totalSeals1_commonSeal: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]
      - gt: [ content.data.total, 2]

- test:
    name: "setup-内部企业签署，指定印章类型sealTypeCode为公章（公章下包含中国标准印章和国际标准印章）-查询用户的企业授权印章个数-云中国标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_organizationCode: "$orgCode0"
      organizationSeals_list_userCode: "$userCode0"
      organizationSeals_list_sealPattern: 3 #中国标准标准印章
      organizationSeals_list_sealTypeCode: ""COMMON-SEAL""
    extract:
      totalSeals2_commonSeal: content.data.total
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]

- test:
    name: "内部企业签署，指定印章类型sealTypeCode为公章（公章下包含中国标准印章和国际标准印章）-签署区配置页面查询企业签署区的印章id列表"
    api: api/esignDocs/openapi/signTools/seals.yml
    variables:
      organizationCode: "$orgCode0"
      userCode: "$userCode0"
      sealTypeCode: ""COMMON-SEAL""
    extract:
      officialSeal_commonSeal: content.data.officialSeal.seals
    validate:
      - eq: [ content.status,$successCode ]
      - eq: [ content.message,$successMessage ]
      - eq: [ "${lists_len($officialSeal_commonSeal)}", "${int_sum($totalSeals1_commonSeal,$totalSeals2_commonSeal)}"]
#####


#####第8个场景
- test:
    name: "多签署区指定不同的印章sealId-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL",
                     "sealId": "$sealId0"
                   },
                   {
                     "signatureType": "COMMON-SEAL",
                     "sealId": "$sealId2"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId7: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId7]

 #todo 这条有bug，开发改完后再加用例

#####第9个场景
- test:
    name: "相同企业不同经办人-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0",
             "departmentCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 1,
             "userType": 1,
             "userCode": "$signjz01UserCode",
             "organizationCode": "$orgCode0",
             "departmentCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId8: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId8]

- test:
    name: "相同企业不同经办人-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId8
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 174,
                        "posY": 757,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": true
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": $fileKey0,
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 382,
                        "posY": 595,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $signjz01UserCode-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$signjz01UserCode",
                "userType": 1,
                "userCode": "$signjz01UserCode",
                "userName": "$signjz01UserName",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "相同企业不同经办人-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId8
    extract:
      - signerInfos8: content.data.signerInfos
      - signFiles8: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "相同企业不同经办人-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles8
      signerInfos: $signerInfos8
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


#####第10个场景
- test:
    name: "个人签和企业经办人签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {

                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 1,
             "userType": 1,
             "userCode": "$userCode0"
           }
         ]
    extract:
        filePreTaskId9: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId9]
#todoo  印章列表匹配改为2个印章id列表匹配，但是查授权印章列表接口都是有分页的
#- test:
#    name: "setup-个人签和企业经办人签署-查询用户生效的个人印章列表"
#    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
#    variables:
#      sealStatus: "g"


##企业下印章太多超时
#- test:
#    name: "个人签和企业经办人签署-签署区配置页面查询个人签署方的可用印章id列表"
#    api: api/esignDocs/openapi/signTools/seals.yml
#    variables:
#      organizationCode: "$orgCode0"
#      userCode: "$userCode0"
#    extract:
#      officialSealNoSealTypeCode: content.data.officialSeal.seals
#      legalSealNoSealTypeCode: content.data.legalSeal.seals
#      personalSeal: content.data.personalSeal.seals
#    validate:
#      - eq: [ content.status,$successCode ]
#      - eq: [ content.message,$successMessage ]
#      - gt: [ "${lists_len($officialSealNoSealTypeCode)}", "${int_sum($totalSeals1,$totalSeals2)}" ]


- test:
    name: "个人签和企业签署-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId9
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 210,
                        "posY": 675,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 411,
                        "posY": 649,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL",
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": $userCode0,
                "userType": 1,
                "userCode": $userCode0,
                "userName": $userName0,
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": $orgCode0,
                "organizationName": $orgName0,
                "departmentName": $orgName0,
                "departmentCode": $orgCode0,
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 233,
                        "posY": 324,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": $userCode0,
                "userType": 1,
                "userCode": $userCode0,
                "userName": $userName0,
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": $orgName0,
                "departmentCode": $orgCode0,
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "个人签和企业签署-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId9
    extract:
      - signerInfos9: content.data.signerInfos
      - signFiles9: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.signNode,1]
      - eq: [ content.data.signerInfos.0.signMode,1 ]
      - eq: [content.data.signerInfos.1.signNode,1]
      - eq: [ content.data.signerInfos.1.signMode,1 ]

- test:
    name: "个人签和企业签署-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles9
      signerInfos: $signerInfos9
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


#####第11个场景
- test:
    name: "企业签署，不指定signConfigs自由签署-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [ ]
               }
             ],
             "legalSignFlag": 1,
             "signNode": 1,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId10: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId10]

###### 第12个场景
- test:
    name: "内部企业-sealTypeCode指定多个-签署不指定印章id-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "filePreRedirectUrl": "",
        "expirationDate": 300,
        "signerInfos": [
          {
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [
                ]
              }
            ],
            "signNode": 1,
            "signMode": 0,
            "userType": 1,
            "sealTypeCode": "$org01CommonSealTypeCode1,$org01CommonSealTypeCode2,$legalSealTypeCode22",
            "userCode": "$userCode0",
            "organizationCode": "$orgCode0",
            "departmentCode": "$orgCode0"
          }
        ]
      }
    extract:
        filePreTaskId11: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId11]

- test:
    name: "内部企业-sealTypeCode指定多个-签署不指定印章id-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId11
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 126,
                        "posY": 713,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 308,
                        "posY": 696,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": true,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 315,
                        "posY": 432,
                        "edgeScope": null,
                        "sealSignDatePositionInfo":
                          { posX: 308.5, posY: 592, sealSignDateFormat: 1, fontSize: "16" },
                        "signPosName": $userCode0-$orgCode0,
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL",
                      "LEGAL-PERSON-SEAL"
                    ]
                  }
                ],
                "signerId":  $userCode0,
                "userType": 1,
                "userCode":  $userCode0,
                "userName":  $userName0,
                "signerType": 2,
                "userId": null,
                "organizationId": null,
                "organizationCode": $orgCode0,
                "organizationName": $orgName0,
                "departmentName": $orgName0,
                "departmentCode": $orgCode0,
                "sealTypeCode": "$org01CommonSealTypeCode1,$org01CommonSealTypeCode2,$legalSealTypeCode22",
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              }
            ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "内部企业-sealTypeCode指定多个-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId11
    extract:
      - signerInfos11: content.data.signerInfos
      - signFiles11: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.sealTypeCode,"$org01CommonSealTypeCode1,$org01CommonSealTypeCode2,$legalSealTypeCode22"]

- test:
    name: "内部企业-sealTypeCode指定多个-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles11
      signerInfos: $signerInfos11
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]
#场景13
#todo，在上面的场景新增一个指定多个印章类型，签署区设置指定了印章id

####### 场景14
- test:
    name: "相对方企业，sealTypeCode指定公章，开启法人，指定页码-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/docs",
        "signerInfos": [
          {
            "userType": "2",
            "userCode": $userCodeOpposit,
            "customAccountNo": "",
            "departmentCode": "",
            "customDepartmentNo": "",
            "organizationCode": $orgCodeOpposit,
            "customOrgNo": "",
            "signNode": 1,
            "signMode": 0,
            "legalSignFlag": 1,
            "sealTypeCode": "PUBLIC,$legalSealTypeCode22",
            "sealInfos": [
              {
                "fileKey": $fileKey0,
                "signConfigs": [
                ]
              }
            ]
          }
        ],
        "filePreRedirectUrl": "",
      }
    extract:
        filePreTaskId13: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId13]

- test:
    name: "相对方企业，sealTypeCode指定公章，开启法人，指定页码-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
        filePreTaskId: $filePreTaskId13
        filePreTaskInfos:
          [
            {
              "sealInfos": [
                {
                  "fileKey": "$fileKey0",
                  "signConfigs": [
                    {
                      "addSignDate": true,
                      "keywordInfo": null,
                      "pageNo": "1-3,9,11,13-14",
                      "posX": 283,
                      "posY": 623,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": {
                        "posX": 283.5,
                        "posY": 519,
                        "sealSignDateFormat": 1,
                        "fontSize": "16"
                      },
                      "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": true
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "14",
                      "posX": 294,
                      "posY": 305,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "14",
                      "posX": 439,
                      "posY": 384,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "14",
                      "posX": 149,
                      "posY": 111,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "LEGAL-PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "30",
                      "posX": 519,
                      "posY": 167,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "LEGAL-PERSON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "COMMON-SEAL",
                    "PERSON-SEAL",
                    "LEGAL-PERSON-SEAL"
                  ]
                }
              ],
              "signerId": "$userCodeOpposit",
              "userType": 2,
              "userCode": "$userCodeOpposit",
              "userName": "$userNameOpposit",
              "signerType": 2,
              "legalSignFlag": 1,
              "userId": null,
              "organizationId": null,
              "organizationCode": "$orgCodeOpposit",
              "organizationName": "$orgNameOpposit",
              "departmentName": "$orgNameOpposit",
              "departmentCode": "$orgCodeOpposit",
              "sealTypeCode": "PUBLIC,LEGAL-PERSON-SEAL",
              "sealTypeName": null,
              "needShowSeal": 0,
              "personRealnameStatus": true,
              "orgRealnameStatus": true
            }
          ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "相对方企业，sealTypeCode指定公章，开启法人，指定页码-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId13
    extract:
      - signerInfos13: content.data.signerInfos
      - signFiles13: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - contains: [content.data.signerInfos.0.sealTypeCode,"PUBLIC"]

- test:
    name: "相对方企业，sealTypeCode指定公章，开启法人，指定页码-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles13
      signerInfos: $signerInfos13
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

######第15个场景-数据兼容-新接口_esign-docs_v1_documents_signTools_signFilePreTask发起的任务数据使用老详情接口查看
- test:
    name: "【数据兼容】新发起接口-老详情接口（内外部企业和内外部个人）-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       signerInfos:
         [
           #内部个人指定印章id
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "PERSON-SEAL",
                     "sealId": "$userSealId0"
                   }
                 ]
               }
             ],
             "signNode": 1,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0"
           },
           #内部企业指定印章id
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL",
                     "sealId": "$sealId0"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           },
           #相对方企业指定印章类型
           {
             "userType": "2",
             "userCode": $userCodeOpposit,
             "customAccountNo": "",
             "departmentCode": "",
             "customDepartmentNo": "",
             "organizationCode": $orgCodeOpposit,
             "customOrgNo": "",
             "signNode": 3,
             "signMode": 0,
             "sealTypeCode": "PUBLIC,$legalSealTypeCode22",
             "sealInfos": [
               {
                 "fileKey": $fileKey0,
                 "signConfigs": [
                 ]
               }
             ]
           },
           #相对方个人
           {
             "userType": "2",
             "userCode": $userCodeOpposit,
             "customAccountNo": "",
             "departmentCode": "",
             "customDepartmentNo": "",
             "organizationCode": "",
             "customOrgNo": "",
             "signNode": 4,
             "signMode": 0,
             "legalSignFlag": 0,
             "sealInfos": [
               {
                 "fileKey": $fileKey0,
                 "signConfigs": [
                 ]
               }
             ]
           }
         ]
    extract:
        filePreTaskId14: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId14]

- test:
    name: "【数据兼容】新发起接口-老详情接口（内外部企业和内外部个人）-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId14
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 116,
                        "posY": 727,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$userSealId0",
                        "signPosName": "$userName0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [
                      {
                        "signatureType": "PERSON-SEAL",
                        "sealId": "$userSealId0"
                      }
                    ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 1,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              },
              {
                "sealInfos": [
                  {
                    "fileKey": $fileKey0,
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 354,
                        "posY": 774,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$sealId0",
                        "signPosName": "$userCode0-$orgCode0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [
                      {
                        "signatureType": "COMMON-SEAL",
                        "sealId": "$sealId0"
                      }
                    ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 1,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 114,
                        "posY": 567,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 362,
                        "posY": 586,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 469,
                        "posY": 618,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": $userCodeOpposit-$orgCodeOpposit,
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL",
                      "LEGAL-PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCodeOpposit",
                "userType": 2,
                "userCode": "$userCodeOpposit",
                "userName": "$userNameOpposit",
                "signerType": 2,
                "legalSignFlag": 1,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCodeOpposit",
                "organizationName": "$orgNameOpposit",
                "departmentName": "$orgNameOpposit",
                "departmentCode": "$orgCodeOpposit",
                "sealTypeCode": "PUBLIC,$legalSealTypeCode22",
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 150,
                        "posY": 234,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userNameOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCodeOpposit",
                "userType": 2,
                "userCode": "$userCodeOpposit",
                "userName": "$userNameOpposit",
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "",
                "organizationName": null,
                "departmentName": "$orgNameOpposit",
                "departmentCode": "$orgCodeOpposit",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null]

- test:
    name: "【数据兼容】新发起接口-老详情接口（内外部企业和内外部个人）-filePreTaskDetai"
    api: api/esignDocs/openapi/signTools/filePreTaskDetail-api.yml
    variables:
      filePreTaskId: $filePreTaskId14
    validate:
      - eq: [ content.code, $successCode ]
      - eq: [ content.message, $successMessage ]
      - ne: [ content.data, null ]


####场景16-老发起接口_esign-docs_v1_documents_signTools_filePreTask发起的任务使用新详情接口查看
- test:
    name: "【数据兼容】老发起接口-新详情接口（内外部企业和内外部个人）-filePreTask"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
          {
            "fileKey": $fileKey0
          }
        ],"signerList": [
          {
            "userCode": $userCode0,
            "legalSignFlag": 0,
            "userType": 1
          },
          {
            "userCode": "$userCode0",
            "organizationCode": "$orgCode0",
            "legalSignFlag": 1,
            "sealTypeCode": $sealTypeCode0,
            "userType": 1
          },
          {
            "userCode": $userCodeOpposit,
            "organizationCode": $orgCodeOpposit,
            "legalSignFlag": 0,
            "sealTypeCode": "PUBLIC",
            "userType": 2
          },
          {
            "userCode": $userCodeOpposit,
            "legalSignFlag": 0,
            "userType": 2
          }
        ]
      }
    extract:
        filePreTaskId15: content.data.filePreTaskId
    validate:
        -   eq: [content.code,$successCode]
        -   eq: [content.message,$successMessage]
        -   len_gt: [content.data.filePreTaskId, 1]
        - startswith: [content.data.filePreURL,"http"]
        - contains: [content.data.filePreURL, $filePreTaskId15]

- test:
    name: "【数据兼容】老发起接口-新详情接口（内外部企业和内外部个人）-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId15
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 145,
                        "posY": 669,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCode0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 388,
                        "posY": 578,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "sealId": "$org01CommonSeal",
                        "signPosName": "$userCode0-$orgCode0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 172,
                        "posY": 396,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCode0-$orgCode0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 480,
                        "posY": 337,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCode0-$orgCode0",
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL",
                      "LEGAL-PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCode0",
                "userType": 1,
                "userCode": "$userCode0",
                "userName": "$userName0",
                "signerType": 2,
                "legalSignFlag": 1,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCode0",
                "organizationName": "$orgName0",
                "departmentName": "$orgName0",
                "departmentCode": "$orgCode0",
                "sealTypeCode": "COMMON-SEAL",
                "sealTypeName": "公章",
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 166,
                        "posY": 146,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "2",
                        "posX": 230,
                        "posY": 596,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCodeOpposit",
                "userType": 2,
                "userCode": "$userCodeOpposit",
                "userName": "$userNameOpposit",
                "signerType": 2,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": "$orgCodeOpposit",
                "organizationName": "$orgNameOpposit",
                "departmentName": "$orgNameOpposit",
                "departmentCode": "$orgCodeOpposit",
                "sealTypeCode": "PUBLIC",
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": true
              },
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "2",
                        "posX": 286,
                        "posY": 300,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "$userNameOpposit",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "$userCodeOpposit",
                "userType": 2,
                "userCode": "$userCodeOpposit",
                "userName": "$userNameOpposit",
                "signerType": 1,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": "$orgNameOpposit",
                "departmentCode": "$orgCodeOpposit",
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": true,
                "orgRealnameStatus": false
              }
            ]
    validate:
#      - eq: [ content.status, $successCode]
#      - eq: [ content.message, $successMessage]
#      - eq: [ content.data, null ]
      - eq: [ content.status, 1623030]
      - contains: [ content.message, "不支持法人章签署区"]
      - eq: [ content.data, null ]

#- test:
#    name: "【数据兼容】老发起接口-新详情接口（内外部企业和内外部个人）-signFilePreTaskDetail"
#    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
#    variables:
#      filePreTaskId: $filePreTaskId15
#    extract:
#      - signerInfos15: content.data.signerInfos
#      - signFiles15: content.data.signFiles
#    validate:
#      - eq: [ content.code, $successCode]
#      - eq: [ content.message, $successMessage]
#      - ne: [ content.data, null ]
#
#
#- test:
#    name: "【数据兼容】老发起接口-老详情接口（内外部企业和内外部个人）-filePreTaskDetai"
#    api: api/esignDocs/openapi/signTools/filePreTaskDetail-api.yml
#    variables:
#      filePreTaskId: $filePreTaskId15
#    validate:
#      - eq: [ content.code, $successCode]
#      - eq: [ content.message, $successMessage]
#      - ne: [ content.data, null ]

####场景17
- test:
    name: "不指定任何签署方，legalSignFlag开启-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
         {
           "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/docs",
           "signerInfos": [
             {
               "legalSignFlag": 1,
               "sealInfos": [
                 {
                   "fileKey": $fileKey0,
                   "signConfigs": [
                   ]
                 }
               ]
             }
           ],
           "filePreRedirectUrl": "",
           "expirationDate": ""
         }
    extract:
        filePreTaskId16: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId16]

- test:
    name: "不指定任何签署方，legalSignFlag开启--save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId16
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": $fileKey0,
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 170,
                        "posY": 717,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方企业",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 400,
                        "posY": 566,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方个人",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "12",
                        "posX": 290,
                        "posY": 407,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方企业",
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL",
                      "LEGAL-PERSON-SEAL"
                    ]
                  }
                ],
                "signerId": "NO_SIGNER",
                "userType": null,
                "userCode": null,
                "userName": null,
                "signerType": null,
                "legalSignFlag": 1,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": null,
                "departmentCode": null,
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": false,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "不指定任何签署方，legalSignFlag开启-signFilePreTaskDetail"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId16
    extract:
      - signerInfos16: content.data.signerInfos
      - signFiles16: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

####场景18
- test:
    name: "不指定任何签署方，legalSignFlag关闭-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
         {
           "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/docs",
           "signerInfos": [
             {
               "legalSignFlag": 0,
               "sealInfos": [
                 {
                   "fileKey": $fileKey0,
                   "signConfigs": [
                   ]
                 }
               ]
             }
           ],
           "filePreRedirectUrl": "",
           "expirationDate": ""
         }
    extract:
        filePreTaskId17: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId17]

- test:
    name: "不指定任何签署方，legalSignFlag关闭--save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId17
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": $fileKey0,
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 170,
                        "posY": 717,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方企业",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 400,
                        "posY": 566,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方个人",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL",
                      "PERSON-SEAL",
                    ]
                  }
                ],
                "signerId": "NO_SIGNER",
                "userType": null,
                "userCode": null,
                "userName": null,
                "signerType": null,
                "legalSignFlag": 0,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": null,
                "departmentCode": null,
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": false,
                "orgRealnameStatus": false
              }
            ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "不指定任何签署方，legalSignFlag关闭-signFilePreTaskDetail"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId17
    extract:
      - signerInfos17: content.data.signerInfos
      - signFiles17: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

####场景19
- test:
    name: "不指定任何签署方，多个签署文件-signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/docs",
        "signerInfos": [
          {
            "legalSignFlag": 1,
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [
                  {
                    "signatureType": "COMMON-SEAL" },
                  {
                    "signatureType": "PERSON-SEAL" }
                ]
              },
              {
                "fileKey": "$2PageFileKey",
                "signConfigs": [
                  {
                    "signatureType": "COMMON-SEAL" }
                ]
              }
            ]
          }
        ],
        "filePreRedirectUrl": "",
        "expirationDate": ""
      }
    extract:
        filePreTaskId18: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId18]

- test:
    name: "不指定任何签署方，多个签署文件--save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId18
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {
                    "fileKey": "$fileKey0",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 175,
                        "posY": 718,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方个人",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": false
                      },
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 377,
                        "posY": 561,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方企业",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "PERSON-SEAL",
                      "COMMON-SEAL"
                    ]
                  },
                  {
                    "fileKey": "$fileKey1",
                    "signConfigs": [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 269,
                        "posY": 625,
                        "edgeScope": null,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "甲方企业",
                        "signType": "COMMON-SIGN",
                        "signatureType": "COMMON-SEAL",
                        "allowMove": false
                      }
                    ],
                    "sealIdList": [ ],
                    "signatureTypeList": [
                      "COMMON-SEAL"
                    ]
                  }
                ],
                "signerId": "NO_SIGNER",
                "userType": null,
                "userCode": null,
                "userName": null,
                "signerType": null,
                "legalSignFlag": 1,
                "userId": null,
                "organizationId": null,
                "organizationCode": null,
                "organizationName": null,
                "departmentName": null,
                "departmentCode": null,
                "sealTypeCode": null,
                "sealTypeName": null,
                "needShowSeal": 0,
                "personRealnameStatus": false,
                "orgRealnameStatus": false
              }
            ]

    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "不指定任何签署方，多个签署文件-signFilePreTaskDetail"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId17
    extract:
      - signerInfos17: content.data.signerInfos
      - signFiles17: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

#####场景20
- test:
    name: "内部企业指定公章+外部个人（关键字定位-骑缝章，章很多）--signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "filePreRedirectUrl": "",
        "expirationDate": 200,
        "signerInfos": [
          {
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [

                ]
              }
            ],
            "signNode": 1,
            "signMode": 0,
            "userType": 1,
            "legalSignFlag": 1,
            "userCode": "$userCode0",
            "organizationCode": "$orgCode0",
            "sealTypeCode": "COMMON-SEAL"
          },
          {
            "userType": "2",
            "userCode": "$userCodeOpposit",
            "customAccountNo": "",
            "departmentCode": "",
            "customDepartmentNo": "",
            "organizationCode": "",
            "customOrgNo": "",
            "signNode": 2,
            "signMode": 0,
            "legalSignFlag": 0,
            "sealTypeCode": "",
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [ ]
              }
            ]
          }
        ]
      }
    extract:
        filePreTaskId19: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId19]

- test:
    name: "内部企业指定公章+外部个人（关键字定位-骑缝章，章很多）--save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
        filePreTaskId: $filePreTaskId19
        filePreTaskInfos:
          [
            {
              "sealInfos": [
                {
                  "fileKey": "$fileKey0",
                  "signConfigs": [
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 191,
                      "posY": 741,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": true,
                      "keywordInfo": null,
                      "pageNo": "3",
                      "posX": 402,
                      "posY": 443,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": {
                        "posX": 400.5234375,
                        "posY": 334,
                        "sealSignDateFormat": 2,
                        "fontSize": "16"
                      },
                      "sealId": "$org01CommonSeal",
                      "signPosName": "允许移动",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": true
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 1
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 748,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 687,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 510,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 495,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 479,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 464,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 224,
                      "posY": 464,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 354,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": {
                        "keyword": "测试",
                        "offsetPosX": 0,
                        "offsetPosY": 0,
                      },
                      "pageNo": "1",
                      "posX": 90,
                      "posY": 339,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "2-10",
                      "posX": null,
                      "posY": 241,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "sealId": "$org01CommonSeal",
                      "signPosName": "testPos",
                      "signType": "EDGE-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "COMMON-SEAL",
                    "PERSON-SEAL",
                    "LEGAL-PERSON-SEAL"
                  ]
                }
              ],
              "signerId": "$userCode0",
              "userType": 1,
              "userCode": "$userCode0",
              "userName": "$userName0",
              "signerType": 2,
              "legalSignFlag": 1,
              "userId": null,
              "organizationId": null,
              "organizationCode": "$orgCode0",
              "organizationName": "$orgName0",
              "departmentName": "$orgName0",
              "departmentCode": "$orgCode0",
              "sealTypeCode": "COMMON-SEAL",
              "sealTypeName": "公章",
              "needShowSeal": 0,
              "personRealnameStatus": true,
              "orgRealnameStatus": true
            },
            {
              "sealInfos": [
                {
                  "fileKey": "$fileKey0",
                  "signConfigs": [
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 377,
                      "posY": 583,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 324,
                      "posY": 391,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": true,
                      "keywordInfo": null,
                      "pageNo": "1-30",
                      "posX": 160,
                      "posY": 161,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": {
                        "posX": 160.5,
                        "posY": 57,
                        "sealSignDateFormat": 1,
                        "fontSize": "16"
                      },
                      "signPosName": "$userNameOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "PERSON-SEAL"
                  ]
                }
              ],
              "signerId": "$userCodeOpposit",
              "userType": 2,
              "userCode": "$userCodeOpposit",
              "userName": "$userNameOpposit",
              "signerType": 1,
              "legalSignFlag": 0,
              "userId": null,
              "organizationId": null,
              "organizationCode": "",
              "organizationName": null,
              "departmentName": "$orgNameOpposit",
              "departmentCode": "$orgCodeOpposit",
              "sealTypeCode": "",
              "sealTypeName": null,
              "needShowSeal": 0,
              "personRealnameStatus": true,
              "orgRealnameStatus": false
            }
          ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "内部企业指定公章+外部个人（关键字定位-骑缝章，章很多）-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId19
    extract:
      - signerInfos19: content.data.signerInfos
      - signFiles19: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.sealTypeCode,"COMMON-SEAL"]
      - len_eq: [content.data.signerInfos.0.sealInfos.0.signConfigs,12]


- test:
    name: "内部企业指定公章+外部个人（关键字定位-骑缝章，章很多）-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles19
      signerInfos: $signerInfos19
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

#######场景21 （6.0.8.1（20231020）版本之后支持）
- test:
    name: "内部企业+外部个人（多文档，多签署区,企业不加盖企业签署区）--signFilePreTask"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "filePreRedirectUrl": "",
        "expirationDate": 200,
        "signerInfos": [
          {
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [ {
                  "signatureType": "LEGAL-PERSON-SEAL"
                },{
                  "signatureType": "PERSON-SEAL",
                  "sealId": ""
                }
                ]
              },{
                "fileKey": "$fileKey1page",
                "signConfigs": [ {
                  "signatureType": "COMMON-SEAL"
                }]
              }
            ],
            "signNode": 1,
            "signMode": 0,
            "userType": 1,
            "userCode": "$userCode0",
            "organizationCode": "$orgCode0",
            "sealTypeCode": ""
          },
          {
            "userType": "2",
            "userCode": "$userCodeOpposit",
            "customAccountNo": "",
            "departmentCode": "",
            "customDepartmentNo": "",
            "organizationCode": "",
            "customOrgNo": "",
            "signNode": 2,
            "signMode": 0,
            "legalSignFlag": 0,
            "sealTypeCode": "",
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [ {"signatureType": "PERSON-SEAL"} ]
              },{
                "fileKey": "$fileKey1page",
                "signConfigs": [ {"signatureType": "PERSON-SEAL"} ]
              }
            ]
          }
        ]
      }
    extract:
        filePreTaskId21: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId21]

- test:
    name: "内部企业+外部个人（多文档，多签署区,企业不加盖企业签署区）--save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
        filePreTaskId: $filePreTaskId21
        filePreTaskInfos:
          [
            {
              "sealInfos": [
                {
                  "fileKey": "$fileKey0",
                  "signConfigs": [
                    {
                      "addSignDate": true,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 489,
                      "posY": 187,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": {
                        "posX": 490.78125,
                        "posY": 62,
                        "sealSignDateFormat": 2,
                        "fontSize": "16"
                      },
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "LEGAL-PERSON-SEAL",
                      "allowMove": false
                    },
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "3",
                      "posX": 402,
                      "posY": 443,
                      "edgeScope": null,
                      "signPosName": "允许移动",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": true
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "PERSON-SEAL",
                    "LEGAL-PERSON-SEAL"
                  ]
                },{
                  "fileKey": "$fileKey1page",
                  "signConfigs": [
                    {
                      "addSignDate": true,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 489,
                      "posY": 187,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": {
                        "posX": 490.78125,
                        "posY": 62,
                        "sealSignDateFormat": 2,
                        "fontSize": "16"
                      },
                      "signPosName": "$userCode0-$orgCode0",
                      "signType": "COMMON-SIGN",
                      "signatureType": "LEGAL-PERSON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "LEGAL-PERSON-SEAL"
                  ]
                }
              ],
              "signerId": "$userCode0",
              "userType": 1,
              "userCode": "$userCode0",
              "userName": "$userName0",
              "signerType": 2,
              "legalSignFlag": 1,
              "userId": null,
              "organizationId": null,
              "organizationCode": "$orgCode0",
              "organizationName": "$orgName0",
              "departmentName": "$orgName0",
              "departmentCode": "$orgCode0",
#              "sealTypeCode": "COMMON-SEAL",
#              "sealTypeName": "公章",
              "needShowSeal": 0,
              "personRealnameStatus": true,
              "orgRealnameStatus": true
            },
            {
              "sealInfos": [
                {
                  "fileKey": "$fileKey0",
                  "signConfigs": [
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 377,
                      "posY": 583,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "PERSON-SEAL"
                  ]
                },{
                  "fileKey": "$fileKey1page",
                  "signConfigs": [
                    {
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "1",
                      "posX": 377,
                      "posY": 583,
                      "edgeScope": null,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "$userCodeOpposit",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "allowMove": false
                    }
                  ],
                  "sealIdList": [ ],
                  "signatureTypeList": [
                    "PERSON-SEAL"
                  ]
                }
              ],
              "signerId": "$userCodeOpposit",
              "userType": 2,
              "userCode": "$userCodeOpposit",
              "userName": "$userNameOpposit",
              "signerType": 1,
              "legalSignFlag": 0,
              "userId": null,
              "organizationId": null,
#              "organizationCode": "$orgCodeOpposit",
              "organizationName": null,
              "departmentName": "$orgNameOpposit",
              "departmentCode": "$orgCodeOpposit",
              "sealTypeCode": "",
              "sealTypeName": null,
              "needShowSeal": 0,
              "personRealnameStatus": true,
              "orgRealnameStatus": false
            }
          ]
    validate:
      - eq: [ content.status, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data, null ]

- test:
    name: "内部企业+外部个人（多文档，多签署区,企业不加盖企业签署区）-signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId21
    extract:
      - signerInfos21: content.data.signerInfos
      - signFiles21: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.sealTypeCode, null]
      - len_eq: [content.data.signerInfos.0.sealInfos.0.signConfigs,2]


- test:
    name: "内部企业指定公章+外部个人（多文档，多签署区,企业不加盖企业签署区）-使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles21
      signerInfos: $signerInfos21
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]