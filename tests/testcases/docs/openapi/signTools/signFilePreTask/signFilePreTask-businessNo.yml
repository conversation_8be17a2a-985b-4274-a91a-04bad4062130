- config:
    name: "openapi:签署区设置-入参和回调增加businessNo"
    variables:
        userCode0: ${ENV(sign01.userCode)}
        userName0: ${ENV(sign01.userName)}
        orgCode0: ${ENV(sign01.main.orgCode)}
        orgName0: ${ENV(sign01.main.orgName)}
        fileKey0: ${ENV(fileKey)}
        successCode: 200
        successMessage: 成功
        projectId0: ${ENV(esign.projectId)}
        signpersonfield1: ${epaasSignContentField(1,$orgCode0,2)} #企业1正文签

- test:
    name: "TC1-设置签署区（预盖章签署页面）businessNo长度超过191-报错"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      businessNo: "${random_str(192)}"
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.data, null ]
      - eq: [ content.message,"业务编号不可超过191个字符！" ]

- test:
    name: "TC2-设置签署区（预盖章签署页面）businessNo为空"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      businessNo: " "
      signerInfos:
        [
          {
            "sealInfos": [
              {
                "fileKey": "$fileKey0",
                "signConfigs": [
                  {
                    "signatureType": "COMMON-SEAL"
                  }
                ]
              }
            ],
            "signNode": 1,
            "signMode": 0,
            "userType": 1,
            "userCode": "$userCode0",
            "organizationCode": "$orgCode0"
          }
        ]
    validate:
      - eq: [content.code,$successCode]
      - eq: [content.message,$successMessage]
      - ne: [ content.data.filePreTaskId," " ]
      - contains: [ content.data.filePreUrl,"http" ]
      - contains: [ content.data.filePreOuterUrl,"http" ]
    extract:
      - filePreTaskId0: content.data.filePreTaskId

- test:
    name: "TC2-detail签署区设置页详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: "$filePreTaskId0"
    extract:
      - editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl1)}
      extract:
        - contentId0: content.data.0.contentId
        - documentId0: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId0,null]
      - ne: [$documentId0,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId0
          entityId1: $documentId0
          tplToken_content: ${getTplToken($editUrl1)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId0: content.data.baseFile.resourceId
        - baseFile0: content.data.baseFile


- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${getTplToken($editUrl1)}
      baseFile_draft: $baseFile0
      originFile_draft: {resourceId: "$resourceId0"}
      name_draft: "key30page.pdf"
      contentId_draft: $contentId0
      entityId_draft: $documentId0
      fields_draft: [$signpersonfield1]
#    extract:
#      - _contentId: content.data.0.contentId
#      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
      name: "TC3-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [
            ]
          callbackType: 1
          operateType: 1
      validate:
        - eq: [ content.status,200 ]
        - eq: [ content.message, "成功" ]
        - eq: [ content.data, null ]

- test:
    name: 查询设置签署区回调
    api: api/custom/getCallBackEvent.yml
    variables:
      callbackEventBusinessId: filePreTaskId
      callbackEventType: "2"
    validate:
        - eq: ["content.callbackEventOutputs.projectId", "$projectId0" ]
        - eq: ["content.callbackEventOutputs.businessNo", ""]
        - eq: ["content.callbackEventOutputs.filePreFinished", "1" ]
        - eq: ["content.callbackEventOutputs.filePreTaskId", "$filePreTaskId0" ]
        - eq: ["content.callbackEventOutputs.signFiles.0.fileKey", "$fileKey0" ]
#        - eq: ["content.callbackEventOutputs.signerInfos", "$signerInfos0" ]