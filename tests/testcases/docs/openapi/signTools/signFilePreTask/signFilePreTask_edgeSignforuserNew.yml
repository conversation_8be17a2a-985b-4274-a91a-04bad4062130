- config:
    name: "个人支持骑缝章"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.cloud.guomi.SealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(sign01.cloud.guomi.SealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}
       org01CommonSeal: ${ENV(org01.sealId)}
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
       fileKey0: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(3PageFileKey)}
       domainHost: ${ENV(esign.projectHost)}
       outerDomainHost: ${ENV(esign.projectOuterHost)}
       roleorgId0: "$orgCode0:$userCode0"

- test:
    name: "signFilePreTask-企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId0
    extract:
      - editUrl0: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl0)}
    extract:
      - contentId0: content.data.contents.0.id
      - entityId0: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId0
          entityId1: $entityId0
          tplToken_content: ${getTplToken($editUrl0)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId0: content.data.baseFile.resourceId
        - baseFile0: content.data.baseFile

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl0)}
    extract:
      - _signerId0: content.data.0.id #发起方填写
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "保存草稿-设置签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl0)}
          baseFile_draft: $baseFile0
          originFile_draft: {resourceId: "$resourceId0"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId0
          entityId_draft: $entityId0
          signpersonfield0: ${epaasSignContentField(1,$_signerId0,3)}
          signorgfield0: ${epaasSignContentField(1,$_signerId0,4)}
          fields_draft: [$signpersonfield0,$signorgfield0]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId0 ]
      teardown_hooks:
        - ${sleep(0.5)}
- test:
      name: "保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-不指定签署方-一个文件"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
          [
           {
             "legalSignFlag": false,
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                 ]
               }
             ]
           }
         ]
    extract:
        filePreTaskId1: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]

- test:
    name: "获取盖章配置详情1"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId1
    extract:
      - editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
    extract:
      - contentId1: content.data.contents.0.id
      - entityId1: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
      name: "查询recourceId1"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId1
          entityId1: $entityId1
          tplToken_content: ${getTplToken($editUrl1)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId1: content.data.baseFile.resourceId
        - baseFile1: content.data.baseFile

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl1)}
    extract:
      - _signerId0: content.data.0.id #发起方填写
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]


- test:
      name: "保存草稿-设置签署区1"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl1)}
          baseFile_draft: $baseFile1
          originFile_draft: {resourceId: "$resourceId1"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId1
          entityId_draft: $entityId1
          signpersonfield1: ${epaasSignContentField(1,$_signerId0,1)}
          fields_draft: [$signpersonfield1]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId1 ]
      teardown_hooks:
        - ${sleep(0.5)}
- test:
      name: "保存签署区设置1"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "signFilePreTaskDetail查询详情1"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-不指定签署方-多个文件"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
          [
           {
             "legalSignFlag": false,
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                 ]
               },               {
                 "fileKey": "$2PageFileKey",
                 "signConfigs": [
                 ]
               }
             ]
           }
         ]
    extract:
        filePreTaskId2: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]

- test:
    name: "获取盖章配置详情2"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId2
    extract:
      - editUrl2: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl2)}
    extract:
      - contentId2: content.data.contents.0.id
      - entityId2: content.data.contents.0.entityId
      - contentId21: content.data.contents.1.id
      - entityId21: content.data.contents.1.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
      name: "查询recourceId2"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId2
          entityId1: $entityId2
          tplToken_content: ${getTplToken($editUrl2)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId2: content.data.baseFile.resourceId
        - baseFile2: content.data.baseFile

- test:
      name: "查询recourceId21"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId21
          entityId1: $entityId21
          tplToken_content: ${getTplToken($editUrl2)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId21: content.data.baseFile.resourceId
        - baseFile21: content.data.baseFile

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl2)}
    extract:
      - _signerId0: content.data.0.id #发起方填写
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]


- test:
      name: "保存草稿-设置签署区2"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl2)}
          signpersonfield2: ${epaasSignContentField(1,$_signerId0,1)}
          fields_draft21: [$signpersonfield2]
          signpersonfield22: ${epaasSignContentField(1,$_signerId0,1)}
          fields_draft22: [$signpersonfield22]
          json_save_draft: {
              "data": [
                        {
                            "baseFile": $baseFile2,
                            "originFile": {resourceId: "$resourceId2"},
                            "fields": $fields_draft21,
                            "pageFormatInfoParam": null,
                            "name": "key30page.pdf",
                            "contentId": $contentId2,
                            "entityId": $entityId2
                        },
                        {
                            "baseFile": $baseFile21,
                            "originFile": {resourceId: "$resourceId21"},
                            "fields": $fields_draft22,
                            "pageFormatInfoParam": null,
                            "name": "key30page.pdf",
                            "contentId": $contentId21,
                            "entityId": $entityId21
                        }
              ]
          }
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId2 ]
      teardown_hooks:
        - ${sleep(1)}
- test:
      name: "保存签署区设置2"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情2"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-企业+企业经办人+法人指定印章"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "sealId": "$sealId0",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "$org01LegalSealId",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId3: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
    name: "获取盖章配置详情3"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId3
    extract:
      - editUrl3: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl3)}
    extract:
      - contentId3: content.data.contents.0.id
      - entityId3: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]


- test:
      name: "查询recourceId3"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId3
          entityId1: $entityId3
          tplToken_content: ${getTplToken($editUrl3)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId3: content.data.baseFile.resourceId
        - baseFile3: content.data.baseFile

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl3)}
    extract:
      - _signerId0: content.data.0.id #发起方填写
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "保存草稿-设置签署区3"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl3)}
          baseFile_draft: $baseFile3
          originFile_draft: {resourceId: "$resourceId3"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId3
          entityId_draft: $entityId3
          signpersonfield3: ${epaasSignContentField(1,$_signerId0,1)}
          signorgfield3: ${epaasSignContentField(1,$_signerId0,2)}
          fields_draft: [$signpersonfield3,$signorgfield3]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId3 ]
      teardown_hooks:
        - ${sleep(1)}
- test:
      name: "保存签署区设置3"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情3"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起3"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-外部企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 2,
             "userCode": "$userCodeOpposit",
             "organizationCode": "$orgCodeOpposit"
           }
         ]
    extract:
        filePreTaskId5: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]

- test:
    name: "获取盖章配置详情5"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId5
    extract:
      - editUrl5: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl5)}
    extract:
      - contentId5: content.data.contents.0.id
      - entityId5: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]



- test:
      name: "查询recourceId5"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId5
          entityId1: $entityId5
          tplToken_content: ${getTplToken($editUrl5)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId5: content.data.baseFile.resourceId
        - baseFile5: content.data.baseFile

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl5)}
    extract:
      - _signerId0: content.data.0.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "保存草稿-设置签署区5"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl5)}
          baseFile_draft: $baseFile5
          originFile_draft: {resourceId: "$resourceId5"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId5
          entityId_draft: $entityId5
          signpersonfield5: ${epaasSignContentField(1,$_signerId0,1)}
          signorgfield5: ${epaasSignContentField(1,$_signerId0,2)}
          signlegalfield5: ${epaasSignContentField(2,$_signerId0,4)}
          fields_draft: [$signpersonfield5,$signorgfield5,$signlegalfield5]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId5 ]
      teardown_hooks:
        - ${sleep(0.5)}
- test:
      name: "保存签署区设置5"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,200]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情5"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起5"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]