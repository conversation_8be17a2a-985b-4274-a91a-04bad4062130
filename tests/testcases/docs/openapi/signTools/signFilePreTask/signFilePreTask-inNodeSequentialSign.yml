- config:
    name: "openapi:签署区设置任务发起。场景：同一个节点内支持顺序签署-v6.0.6.5"
    variables:
        successCode: 200
        successMessage: 成功
        userCodeInit: ${ENV(csqs.userCode)}
        orgCodeInit: ${ENV(csqs.orgCode)}
      # 内部账号
        innerUserCode2: ${ENV(csqs.userCode)}
        innerUserName2: ${ENV(csqs.userName)}
        innerUserCode1: ${ENV(sign01.userCode)}
        innerUserName1: ${ENV(sign01.userName)}
        innerUserCode3: ${ENV(sign03.userCode)}
      # 内部企业
        innerOrgNo1: ${ENV(sign01.main.orgNo)}
        innerOrgCode1: ${ENV(sign01.main.orgCode)}
        innerOrgName1: ${ENV(sign01.main.orgName)}
        innerOrgNo2: ${ENV(csqs.main.orgNo)}
        innerOrgCode2: ${ENV(csqs.orgCode)}
        innerOrgNo3: ${ENV(sign03.main.departCode)}
      #相对方用户
        userCodeOpposit: ${ENV(wsignwb01.userCode)}
        fileKey0: ${ENV(fileKey)}
        sealInfos_person: [
                {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                            "signatureType": "PERSON-SEAL",
                            "sealId": ""
                        }
                    ]
                }
            ]
        sealInfos_org: [
                {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                            "signatureType": "COMMON-SEAL",
                            "sealId": ""
                        }
                    ]
                }
            ]
        signConfigs_person_save: [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 675,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": 0
                      }
                    ]
        sealInfos_person_save: [
          {
            "fileKey": $fileKey0,
            "signConfigs": $signConfigs_person_save,
            "sealIdList": [
            ],
            "signatureTypeList": [
              "PERSON-SEAL"
            ]
          }
        ]
        signpersonfield1: ${epaasSignContentField(1,$innerUserCode1,1)} #个人1正文签
        signorgfield1: ${epaasSignContentField(1,innerOrgCode1,2)} #企业1正文签
        signpersonfield2: ${epaasSignContentField(1,$innerUserCode2,1)} #个人2正文签
        signorgfield2: ${epaasSignContentField(1,innerOrgCode2,2)} #企业1正文签
- test:
      name: "TC1-场景：顺序签节点内多个签署人_signMode_0_signOrder不传-发起失败"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode2,
                userType: 1
              }
            ]
      validate:
         - eq: [content.code,1623089]
         - eq: [content.message,"节点1内顺序签，签署顺序1不能重复"]
         - eq: [content.data,null]

- test:
      name: "TC2-场景：顺序签节点内多个签署人_signMode_0_signOrder值重复-发起失败"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode2,
                "signOrder": 1,
                userType: 1
              }
            ]
      validate:
         - eq: [content.code,1623089]
         - eq: [content.message,"节点1内顺序签，签署顺序1不能重复"]
         - eq: [content.data,null]

- test:
      name: "TC3-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId3: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId3
    extract:
      - editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl1)}
      extract:
        - contentId2: content.data.0.contentId
        - documentId2: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId2,null]
      - ne: [$documentId2,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId2
          entityId1: $documentId2
          tplToken_content: ${getTplToken($editUrl1)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId1: content.data.baseFile.resourceId
        - baseFile1: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl1)}
          baseFile_draft: $baseFile1
          originFile_draft: {resourceId: "$resourceId1"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId2
          entityId_draft: $documentId2
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId2 ]
      teardown_hooks:
        - ${sleep(1)}
#
#
- test:
      name: "TC3-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]

- test:
    name: "TC3-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.sealInfos.0.signConfigs.0.signatureType,"PERSON-SEAL"]
      - eq: [content.data.signerInfos.0.sealInfos.0.signConfigs.0.signatureType,"PERSON-SEAL"]

- test:
    name: "TC3-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC4-场景：无序签节点内多个签署人_signMode_1_signOrder不传-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode2,
                userType: 1
              }
            ]
      extract:
        filePreTaskId4: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId4]



- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId4
    extract:
      - editUrl4: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl4)}
      extract:
        - contentId4: content.data.0.contentId
        - documentId4: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId4,null]
      - ne: [$documentId4,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId4
          entityId1: $documentId4
          tplToken_content: ${getTplToken($editUrl4)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId4: content.data.baseFile.resourceId
        - baseFile4: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl4)}
          baseFile_draft: $baseFile4
          originFile_draft: {resourceId: "$resourceId4"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId4
          entityId_draft: $documentId4
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1,$signpersonfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId4 ]
      teardown_hooks:
        - ${sleep(1)}

- test:
      name: "TC4-场景：无序签节点内多个签署人_signMode_1_signOrder不传-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]

- test:
    name: "TC4-场景：无序签节点内多个签署人_signMode_1_signOrder不传-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId4
    extract:
      - signerInfos4: content.data.signerInfos
      - signFiles4: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.signNode,1]
      - eq: [ content.data.signerInfos.0.signMode,1 ]
      - eq: [ content.data.signerInfos.0.signOrder,1 ]
      - eq: [content.data.signerInfos.1.signNode,1]
      - eq: [ content.data.signerInfos.1.signMode,1 ]
      - eq: [ content.data.signerInfos.1.signOrder,1 ]

- test:
    name: "TC4-场景：无序签节点内多个签署人_signMode_1_signOrder不传-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles4
      signerInfos: $signerInfos4
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


- test:
      name: "TC5-场景：无序签节点内多个签署人_signMode_1_signOrder传值-发起成功,默认是无序签处理"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode2,
                "signOrder": 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId5: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]


- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId5
    extract:
      - editUrl5: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl5)}
      extract:
        - contentId5: content.data.0.contentId
        - documentId5: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId5,null]
      - ne: [$documentId5,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId5
          entityId1: $documentId5
          tplToken_content: ${getTplToken($editUrl5)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId5: content.data.baseFile.resourceId
        - baseFile5: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl5)}
          baseFile_draft: $baseFile5
          originFile_draft: {resourceId: "$resourceId5"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId5
          entityId_draft: $documentId5
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1,$signpersonfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId5 ]
      teardown_hooks:
        - ${sleep(1)}
- test:
      name: "TC5-场景：无序签节点内多个签署人_signMode_1_signOrder传值-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]



- test:
    name: "TC5-场景：无序签节点内多个签署人_signMode_1_signOrder传值-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC5-场景：无序签节点内多个签署人_signMode_1_signOrder传值-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


- test:
      name: "TC6-场景：或签节点多个签署人signMode_2_signOrder不传值-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode2,
                userType: 1
              }
            ]
      extract:
        filePreTaskId6: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId6]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId6
    extract:
      - editUrl6: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl6)}
      extract:
        - contentId6: content.data.0.contentId
        - documentId6: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId6,null]
      - ne: [$documentId6,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId6
          entityId1: $documentId6
          tplToken_content: ${getTplToken($editUrl6)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId6: content.data.baseFile.resourceId
        - baseFile6: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl6)}
          baseFile_draft: $baseFile6
          originFile_draft: {resourceId: "$resourceId6"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId6
          entityId_draft: $documentId6
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1,$signpersonfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId6 ]
      teardown_hooks:
        - ${sleep(1)}

- test:
      name: "TC6-场景：或签节点多个签署人signMode_2_signOrder不传值-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId6
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]

- test:
    name: "TC6-场景：或签节点多个签署人signMode_2_signOrder不传值-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId6
    extract:
      - signerInfos6: content.data.signerInfos
      - signFiles6: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC6-场景：或签节点多个签署人signMode_2_signOrder不传值-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles6
      signerInfos: $signerInfos6
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC7-场景：或签节点多个签署人signMode_2_signOrder传值-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode2,
                "signOrder": 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId7: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId7]
- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId7
    extract:
      - editUrl7: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl7)}
      extract:
        - contentId7: content.data.0.contentId
        - documentId7: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId7,null]
      - ne: [$documentId7,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId7
          entityId1: $documentId7
          tplToken_content: ${getTplToken($editUrl7)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId7: content.data.baseFile.resourceId
        - baseFile7: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl7)}
          baseFile_draft: $baseFile7
          originFile_draft: {resourceId: "$resourceId7"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId7
          entityId_draft: $documentId7
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1,$signpersonfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId7 ]
      teardown_hooks:
        - ${sleep(1)}
- test:
      name: "TC7-场景：或签节点多个签署人signMode_2_signOrder不传值-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId7
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]

- test:
    name: "TC7-场景：或签节点多个签署人signMode_2_signOrder不传值-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId7
    extract:
      - signerInfos7: content.data.signerInfos
      - signFiles7: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC7-场景：或签节点多个签署人signMode_2_signOrder不传值-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles7
      signerInfos: $signerInfos7
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC8-场景：或签节点内签署人有内部用户和相对方用户-发起失败"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $userCodeOpposit,
                userType: 2
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode2,
                userType: 1
              }
            ]
      validate:
         - eq: [content.code,1623075]
         - eq: [content.message,"节点1内或签，不支持同时存在“内部用户”与“相对方用户”"]

- test:
      name: "TC9-场景：顺序签节点内多个签署人_signMode_0_signOrder值正确-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode2,
                "signOrder": 2,
                userType: 1
              }
            ]
      extract:
        filePreTaskId9: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - ne: [content.data,null]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId9
    extract:
      - editUrl9: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl9)}
      extract:
        - contentId9: content.data.0.contentId
        - documentId9: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId9,null]
      - ne: [$documentId9,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId9
          entityId1: $documentId9
          tplToken_content: ${getTplToken($editUrl9)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId9: content.data.baseFile.resourceId
        - baseFile9: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl9)}
          baseFile_draft: $baseFile9
          originFile_draft: {resourceId: "$resourceId9"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId9
          entityId_draft: $documentId9
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1,$signpersonfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId9 ]
      teardown_hooks:
        - ${sleep(1)}
- test:
      name: "TC9-场景：顺序签节点内多个签署人_signMode_0_signOrder值正确-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId9
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        
        - eq: [ content.data, null ]

- test:
    name: "TC7-场景：顺序签节点内多个签署人_signMode_0_signOrder值正确-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId9
    extract:
      - signerInfos9: content.data.signerInfos
      - signFiles9: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.signNode,1]
      - eq: [ content.data.signerInfos.0.signMode,0 ]
      - eq: [ content.data.signerInfos.0.signOrder,1 ]
      - eq: [content.data.signerInfos.1.signNode,1]
      - eq: [ content.data.signerInfos.1.signMode,0 ]
      - eq: [ content.data.signerInfos.1.signOrder,2 ]

- test:
    name: "TC7-场景：顺序签节点内多个签署人_signMode_0_signOrder值正确-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles9
      signerInfos: $signerInfos9
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


- test:
      name: "TC8-场景：多节点_节点一两个签署人或签_节点二两个签署人顺序签_节点三两个签署人无序签-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 1,
                signMode: 2,
                userCode: $innerUserCode2,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_org,
                signNode: 2,
                signMode: 0,
                userCode: $innerUserCode1,
                "organizationCode": $innerOrgCode1,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_org,
                signNode: 2,
                signMode: 0,
                userCode: $innerUserCode2,
                "organizationCode": $innerOrgCode2,
                "signOrder": 2,
                userType: 1
              },
              {
                sealInfos: $sealInfos_person,
                signNode: 3,
                signMode: 1,
                userCode: $innerUserCode3,
                "signOrder": 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_org,
                signNode: 3,
                signMode: 1,
                userCode: $innerUserCode3,
                "organizationCode": $innerOrgCode1,
                "signOrder": 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId7: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId7]