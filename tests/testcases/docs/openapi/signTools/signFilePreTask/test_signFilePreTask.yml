- config:
    name: "获取签署区配置页-参数校验"

- test:
    name: "callbackUrl长度超过1000-失败"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      callbackUrl: "${random_str(1001)}"
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.data, null ]
      - eq: [ "content.message","签署区设置页任务的信息回调URL长度不能超过1000" ]
- test:
    name: "callbackUrl长度为1000字符-成功"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/${random_str(946)}"
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]

- test:
    name: "callbackUrl字段不传"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      callbackUrl: ''
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.data, null ]
      - eq: [ content.message,"签署区设置页任务的信息回调URL不能为空" ]

- test:
    name: "callbackUrl字段包含首尾空格"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      callbackUrl: '  http://datafactory.smlk8s.esign.cn/simpleTools/notice/  '
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]

- test:
    name: "fileKey为不存在的文件"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      fileKey: '1234'
    validate:
      - eq: [ content.code, 1623003 ]
      - eq: [ content.message,"1234文件不存在" ]
      - eq: [ content.data, null ]
- test:
    name: "fileKey包含异常文件"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      fileKey: ${attachment_upload(selfzip.zip)}
    validate:
      - eq: [ content.code, 1623003 ]
      - eq: [ content.message,"zip文件类型不支持" ]
      - eq: [ content.data, null ]
- test:
    name: "userCode_未查询到匹配用户"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ${ENV(sign01.main.orgNo)}
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ''
      userCode: 'sign011111'
      userType: 1
    validate:
      - eq: [ content.code, 1623007 ]
      - eq: [ content.message,"userCode:sign011111 对应用户不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "customAccountNo_未查询到匹配用户"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: 'sign011111'
      customDepartmentNo: ''
      customOrgNo: ${ENV(sign01.main.orgNo)}
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ''
      userCode: ''
      userType: 1
    validate:
      - eq: [ content.code, 1623008 ]
      - eq: [ content.message,"customAccountNo:sign011111 对应用户不存在" ]
      - eq: [ content.data, null ]
- test:
    name: "departmentCode_未查到匹配机构"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ${ENV(sign01.userCode)}
      customDepartmentNo: ''
      customOrgNo: ${ENV(sign01.main.orgNo)}
      departmentCode: 'ORG-SIGN-0111111'
      legalSignFlag: 0
      organizationCode: ''
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623025 ]
      - eq: [ content.message,"departmentCode:ORG-SIGN-0111111 对应机构不存在" ]
      - eq: [ content.data, null ]
- test:
    name: "customDepartmentNo_未查到匹配机构"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ${ENV(sign01.userCode)}
      customDepartmentNo: 'ORG-SIGN-0111111'
      customOrgNo: ${ENV(sign01.main.orgNo)}
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ''
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623026 ]
      - eq: [ content.message,"customDepartmentNo:ORG-SIGN-0111111 对应机构不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "organizationCode_未查到匹配机构"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ${ENV(sign01.userCode)}
      customDepartmentNo: ''
      customOrgNo: ${ENV(sign01.main.orgNo)}
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: 'ORG-SIGN-0111111'
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623014 ]
      - eq: [ content.message,"organizeCode:ORG-SIGN-0111111 对应机构不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "customOrgNo_未查到匹配机构"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ${ENV(sign01.userCode)}
      customDepartmentNo: ''
      customOrgNo: 'ORG-SIGN-0111111'
      departmentCode: ''
      legalSignFlag: 0
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623015 ]
      - eq: [ content.message,"customOrgNo:ORG-SIGN-0111111 对应机构不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "sealTypeCode_sealId两者都有值但不匹配"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ${ENV(sign01.userCode)}
      customDepartmentNo: ''
      customOrgNo: 'ORG-SIGN-0111111'
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ''
      sealId: ${ENV(sign01.sealId)}
      sealTypeCode: "COMMON-SEAL"
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623015 ]
      - eq: [ content.message,"customOrgNo:ORG-SIGN-0111111 对应机构不存在" ]
      - eq: [ content.data, null ]

- test:
    name: "创建签署配置，个人签署使用法人章，配置失败"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ''
      departmentCode: ''
      legalSignFlag: 1
      organizationCode:
      sealId: ${ENV(org01.legal.sealId)}
      signatureType: 'LEGAL-PERSON-SEAL'
      sealTypeCode:
      userCode: ${ENV(sign01.userCode)}
      userType: 1
    validate:
      - eq: [ content.code, 1623082 ]
      - contains: [ content.message,"签署方测试签署一（用户编码：sign01）不支持设置企业签署区" ]
      - str_eq: [ content.data, None ]


- test:
    name: "创建签署配置，内部企业指定企业印章类型，不指定印章id"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ''
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ${ENV(sign01.main.orgCode)}
      sealTypeCode: "COMMON-SEAL"
      userCode: ${ENV(sign01.userCode)}
      userType: 1
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]

- test:
    name: "filePreRedirectUrl长度超过1000-失败"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      filePreRedirectUrl: "${random_str(1001)}"
    validate:
      - eq: [ content.code, 1600017 ]
      - eq: [ content.data, null ]
      - eq: [ "content.message","签署区设置页完成重定向地址长度不能超过1000" ]
- test:
    name: "filePreRedirectUrl长度为1000字符-成功"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      filePreRedirectUrl: "http://${random_str(993)}"
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]

- test:
    name: "filePreRedirectUrl字段不传"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      filePreRedirectUrl: ''
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]

- test:
    name: "filePreRedirectUrl字段包含首尾空格"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      filePreRedirectUrl: '  http://datafactory.smlk8s.esign.cn/simpleTools/notice/  '
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
      - len_gt: [ content.data.filePreTaskId, 1 ]
      - contains: [ content.data,"filePreUrl" ]


- test:
    name: "外部organizationCode传主职企业，departmentCode传主职企业-6076迭代修改"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ''
      departmentCode: ${ENV(wsignwb01.main.orgCode)}
      legalSignFlag: 0
      organizationCode: ${ENV(wsignwb01.main.orgCode)}
      userCode: ${ENV(wsignwb01.userCode)}
      userType: 2
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]

- test:
    name: "organizationCode传主职企业，departmentCode不传-6076迭代修改"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo:
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ${ENV(wsignwb01.main.orgCode)}
      userCode: ${ENV(wsignwb01.userCode)}
      userType: 2
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]

- test:
    name: "organizationCode传兼职企业，departmentCode传兼职企业-6076迭代修改"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ''
      departmentCode: ${ENV(externalOrganizationCode)}
      legalSignFlag: 0
      organizationCode: ${ENV(externalOrganizationCode)}
      userCode: ${ENV(externalUserCode)}
      userType: 2
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]

- test:
    name: "organizationCode传兼职企业，departmentCode不传-6076迭代修改"
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: ''
      customDepartmentNo: ''
      customOrgNo: ''
      departmentCode: ''
      legalSignFlag: 0
      organizationCode: ${ENV(externalOrganizationCode)}
      userCode: ${ENV(externalUserCode)}
      userType: 2
      signatureType: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message,"成功" ]
