- config:
    name: "个人支持骑缝章"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.cloud.guomi.SealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(sign01.cloud.guomi.SealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}

       #公章下的印章id
       org01CommonSeal: ${ENV(org01.sealId)}
      #内部用户的主责是部门
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
       fileKey0: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(3PageFileKey)}
#       signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]


- test:
    name: "signFilePreTask-企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]

- test:
    name: "save保存签署区设置-个人骑缝、法人骑缝"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-3",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-10",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]


- test:
    name: "signFilePreTask-不指定签署方-一个文件"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
          [
           {
             "legalSignFlag": false,
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                 ]
               }
             ]
           }
         ]
    extract:
        filePreTaskId1: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]

- test:
    name: "save保存签署区设置-无签署方"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1-2",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "EDGE-SIGN",
                           "signatureType": "PERSON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-3",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-10",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": "NO_SIGNER",
               "userType": null,
               "userCode": null,
               "userName": null,
               "signerType": null,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": null,
               "organizationName": null,
               "departmentName": null,
               "departmentCode": null,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]


- test:
    name: "signFilePreTask-不指定签署方-多个文件"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
          [
           {
             "legalSignFlag": false,
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                 ]
               },               {
                 "fileKey": "$2PageFileKey",
                 "signConfigs": [
                 ]
               }
             ]
           }
         ]
    extract:
        filePreTaskId2: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]

- test:
    name: "save保存签署区设置-无签署方"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1-2",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "EDGE-SIGN",
                           "signatureType": "PERSON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-3",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-10",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": "NO_SIGNER",
               "userType": null,
               "userCode": null,
               "userName": null,
               "signerType": null,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": null,
               "organizationName": null,
               "departmentName": null,
               "departmentCode": null,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]


- test:
    name: "signFilePreTask-企业+企业经办人+法人指定印章"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey1",
                 "signConfigs": [
                   {
                     "sealId": "$sealId0",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "$org01LegalSealId",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId3: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
    name: "save保存签署区设置-个人骑缝、法人骑缝"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey1,
                     "signConfigs": [
                        {
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-3",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false,
                       "sealId": "$sealId0"
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-10",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "EDGE-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false,
                       "sealId": "$org01LegalSealId"
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-外部企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 2,
             "userCode": "$userCodeOpposit",
             "organizationCode": "$orgCodeOpposit"
           }
         ]
    extract:
        filePreTaskId5: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]

- test:
    name: "save保存签署区设置-个人骑缝、法人骑缝"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-3",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                       "signType": "",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1-10",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCodeOpposit-$orgCodeOpposit",
                       "signType": "EDGE-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCodeOpposit,
               "userType": 2,
               "userCode": $userCodeOpposit,
               "userName": $userNameOpposit,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCodeOpposit,
               "organizationName": $orgNameOpposit,
               "departmentName": $orgNameOpposit,
               "departmentCode": $orgCodeOpposit,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]