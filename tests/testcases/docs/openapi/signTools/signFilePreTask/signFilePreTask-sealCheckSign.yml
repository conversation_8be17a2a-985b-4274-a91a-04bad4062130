- config:
    name: "openapi:签署区设置控制签署区是否必选印章"
    variables:
        successCode: 200
        successMessage: 成功
        userCodeInit: ${ENV(csqs.userCode)}
        orgCodeInit: ${ENV(csqs.orgCode)}
      # 内部账号
        innerUserCode2: ${ENV(csqs.userCode)}
        innerUserName2: ${ENV(csqs.userCode)}
        innerUserCode1: ${ENV(sign01.userCode)}
        innerUserName1: ${ENV(sign01.userName)}
        innerUserCode3: ${ENV(sign03.userCode)}
      # 内部企业
        innerOrgNo1: ${ENV(sign01.main.orgNo)}
        innerOrgCode1: ${ENV(sign01.main.orgCode)}
        innerOrgName1: ${ENV(sign01.main.orgName)}
        innerOrgNo2: ${ENV(csqs.main.orgNo)}
        innerOrgCode2: ${ENV(csqs.orgCode)}
        innerOrgNo3: ${ENV(sign03.main.departCode)}
      #相对方用户
        userCodeOpposit: ${ENV(wsignwb01.userCode)}
        fileKey0: ${ENV(fileKey)}
        sealId01: ${ENV(sign01.sealId)}
        sealIdorg: ${ENV(org01.sealId)}
        sealIdorglegal: ${ENV(org01.legal.sealId)}
        sealInfos_person: [
                {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                            "signatureType": "PERSON-SEAL",
                            "sealId": ""
                        }
                    ]
                }
            ]
        sealInfos_person0: [
          {
            "fileKey": $fileKey0,
            "sealCheck": 0,
            "signConfigs": [
              {
                "signatureType": "PERSON-SEAL",
                "sealId": ""
              }
            ]
          }
        ]
        sealInfos_person1: [
          {
            "fileKey": $fileKey0,
            "sealCheck": 1,
            "signConfigs": [
              {
                "signatureType": "PERSON-SEAL",
                "sealId": ""
              }
            ]
          }
        ]
        sealInfos_person5: [
          {
            "fileKey": $fileKey0,
            "sealCheck": 5,
            "signConfigs": [
              {
                "signatureType": "PERSON-SEAL",
                "sealId": ""
              }
            ]
          }
        ]
        sealInfos_org: [
                {
                     "fileKey": $fileKey0,
                     "sealCheck": 1,
                     "signConfigs": [
                        {
                            "signatureType": "COMMON-SEAL",
                            "sealId": ""
                        },              {
                "signatureType": "PERSON-SEAL",
                "sealId": ""
              }
                    ]
                }
            ]
        sealInfos_org_legal: [
          {
            "fileKey": $fileKey0,
            "sealCheck": 0,
            "signConfigs": [
              {
                "signatureType": "LEGAL-PERSON-SEAL",
                "sealId": ""
              },              {
                "signatureType": "PERSON-SEAL",
                "sealId": ""
              }
            ]
          }
        ]
        signConfigs_person_save: [
                      {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "signFieldType": 0,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 675,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": 0
                      }
                    ]
        sealInfos_person_save: [
          {
            "fileKey": $fileKey0,
            "signConfigs": $signConfigs_person_save,
            "sealIdList": [
            ],
            "signatureTypeList": [
              "PERSON-SEAL"
            ]
          }
        ]
        sealInfos_person_save2: [
          {
            "fileKey": $fileKey0,
            "signConfigs": [
              {
                "addSignDate": false,
                "keywordInfo": null,
                "signFieldType": 0,
                "pageNo": "1",
                "posX": 270,
                "posY": 675,
                "sealSignDatePositionInfo": null,
                "signPosName": "自动化测试签署区2",
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL",
                "sealId": $sealId01,
                "allowMove": 0
              }
            ],
            "sealIdList": [
            ],
            "signatureTypeList": [
                "PERSON-SEAL"
            ]
          }
        ]
        sealInfos_person_save3: [
          {
            "fileKey": $fileKey0,
            "signConfigs": [
              {
                "addSignDate": false,
                "keywordInfo": null,
                "signFieldType": 0,
                "pageNo": "1",
                "posX": 270,
                "posY": 675,
                "sealSignDatePositionInfo": null,
                "signPosName": "自动化测试签署区2",
                "signType": "COMMON-SIGN",
                "signatureType": "PERSON-SEAL",
                "sealId": "",
                "allowMove": 0
              }
            ],
            "sealIdList": [
            ],
            "signatureTypeList": [
                "PERSON-SEAL"
            ]
          }
        ]
        roleorgId1: "$innerOrgCode1:$innerUserCode1"
        roleorgId2: "$innerOrgCode2:$innerUserCode2"
        signpersonfield1: ${epaasSignContentField(1,$innerUserCode1,1)} #个人1正文签
        signpersonbeizhufield1: ${epaasSignContentField(3,$innerUserCode1,1)} #个人1备注签
        signorgfield1: ${epaasSignContentField(1,$roleorgId1,2)} #企业1正文签
        signpersonfield2: ${epaasSignContentField(1,$innerUserCode2,1)} #个人2正文签
        signorgfield2: ${epaasSignContentField(1,$roleorgId2,2)} #企业2正文签
        signorglegfield2: ${epaasSignContentField(1,$roleorgId2,4)} #企业2法人章
        signpersonfield3: ${epaasSignContentField(1,$roleorgId1,3)} #经办人企业1正文签
        signorgfield3: ${epaasSignContentField(1,$roleorgId2,3)} #经办人企业2正文签

- test:
      name: "TC1-1-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_person1,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId3: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId3
    extract:
      - editUrl3: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl3)}
      extract:
        - contentId3: content.data.0.contentId
        - documentId3: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId3,null]
      - ne: [$documentId3,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId3
          entityId1: $documentId3
          tplToken_content: ${getTplToken($editUrl3)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId3: content.data.baseFile.resourceId
        - baseFile3: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl3)}
          baseFile_draft: $baseFile3
          originFile_draft: {resourceId: "$resourceId3"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId3
          entityId_draft: $documentId3
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonfield1]
      validate:
        - eq: [content.code,0]
        - eq: [content.message,$successMessage]
        - eq: [ content.data.0.contentId, $contentId3 ]


- test:
      name: "TC1-1-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1不传印章ID-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,1623099]
        - eq: [content.message,"【key30page.pdf】模板中签署区【个人签署区】未选择印章，请先选择印章"]
        - eq: [ content.data, null ]

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl3)}
          baseFile_draft: $baseFile3
          originFile_draft: {resourceId: "$resourceId3"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId3
          entityId_draft: $documentId3
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: []
      validate:
        - eq: [content.code,0]
        - eq: [content.message,$successMessage]
        - eq: [ content.data.0.contentId, $contentId3 ]

- test:
      name: "TC1-2-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1不传签署区-save保存设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]


- test:
      name: "保存草稿-含备注签"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl3)}
          baseFile_draft: $baseFile3
          originFile_draft: {resourceId: "$resourceId3"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId3
          entityId_draft: $documentId3
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signpersonbeizhufield1]
      validate:
        - eq: [content.code,0]
        - eq: [content.message,$successMessage]
        - eq: [ content.data.0.contentId, $contentId3 ]

- test:
      name: "TC1-3-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1传备注区-save保存设置成功"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]
#todo个人印章选择页面有bug，等修完了再加这个case
#- test:
#      name: "TC1-4-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1传印章ID-save保存签署区设置"
#      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
#      variables:
#        params:
#          filePreTaskId: $filePreTaskId3
#          filePreTaskInfos:
#            [
#
#            ]
#      validate:
#        - eq: [content.status,$successCode]
#        - eq: [content.message,$successMessage]
#        - eq: [ content.data, null ]
#

#
#
- test:
    name: "TC1-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.signOrder,1]

- test:
    name: "TC1-场景：顺序签节点内一个签署人_signMode_0_signOrder不传-sealCheck为1-一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC2-场景：无序签节点内多个签署人_签署人1sealCheck为1,签署人2sealCheck为0-发起成功"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_org,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                userType: 1,
                organizationCode: $innerOrgCode1
              },
              {
                sealInfos: $sealInfos_org_legal,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode2,
                userType: 1,
                organizationCode: $innerOrgCode2
              }
            ]
      extract:
        filePreTaskId4: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId4]

- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId4
    extract:
      - editUrl4: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
      name: "查询epaas文档模板"
      api: api/esignDocs/epaasTemplate/custom-group.yml
      variables:
          tplToken_content: ${getTplToken($editUrl4)}
      extract:
        - contentId4: content.data.0.contentId
        - documentId4: content.data.0.documentId
      validate:
      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: [$contentId4,null]
      - ne: [$documentId4,null]


- test:
      name: "查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId4
          entityId1: $documentId4
          tplToken_content: ${getTplToken($editUrl4)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId4: content.data.baseFile.resourceId
        - baseFile4: content.data.baseFile

- test:
      name: "保存草稿"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl4)}
          baseFile_draft: $baseFile4
          originFile_draft: {resourceId: "$resourceId4"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId4
          entityId_draft: $documentId4
#          signfield: ${epaasSignContentField(1,$innerUserCode1,1)}
          fields_draft: [$signorgfield1,$signorglegfield2]
      validate:
        - eq: [content.code,0]
        - eq: [content.message,$successMessage]
        - eq: [ content.data.0.contentId, $contentId4 ]


#
- test:
      name: "TC2-场景：无序签节点内多个签署人_签署人1sealCheck为1,签署人2sealCheck为0-都不传印章id-save保存签署区设置"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,1623099]
        - contains: [content.message,"未选择印章，请先选择印章"]
        - eq: [ content.data, null ]

#查询企业的sealId
- test:
    name: "无序签署-签署区配置页面查询企业签署区的印章id列表（未指定sealTypeCode和sealId）"
    api: api/esignDocs/openapi/signTools/seals.yml
    variables:
      userCode: "$innerUserCode1"
      organizationCode: "$innerOrgCode1"
      batchTemplateInitiationUuid: $filePreTaskId4
    extract:
      officialSeal4: content.data.officialSeal.seals.0.sealId
      legalSeal4: content.data.legalSeal.seals.0.sealId
      personalSeal4: content.data.personalSeal.seals.0.sealId
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,"成功" ]
      - ne: [$officialSeal4,null]
#      - eq: [ "${lists_len($officialSealNoSealTypeCode)}", "${int_cal($totalSeals1,$totalSeals2,$totalSeals2_legal,$totalSeals1_legal)}" ]


- test:
      name: "保存草稿--企业1公章都指定印章id，经办人未指定id"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl4)}
          baseFile_draft: $baseFile4
          originFile_draft: {resourceId: "$resourceId4"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId4
          entityId_draft: $documentId4
          signfield11: ${epaasSignContentField(1,$roleorgId1,2,$officialSeal4)}
          signfield12: ${epaasSignContentField(2,$roleorgId1,3,$personalSeal4)} #经办人企业1正文签
          fields_draft: [$signfield11,$signorglegfield2]
      validate:
        - eq: [content.code,0]
        - eq: [content.message,"成功"]
        - eq: [ content.data.0.contentId, $contentId4 ]

- test:
      name: "TC2-场景：无序签节点内多个签署人_签署人1公章和经办人sealCheck为1,签署人2sealCheck为0-签署人1经办人未指定-失败"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,1623099]
        - contains: [content.message,"未选择印章，请先选择印章"]
        - eq: [ content.data, null ]

- test:
      name: "保存草稿--签署人1公章和经办人章都指定id--成功"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl4)}
          baseFile_draft: $baseFile4
          originFile_draft: {resourceId: "$resourceId4"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId4
          entityId_draft: $documentId4
          signorglegfield22: ${epaasSignContentField(1,$roleorgId1,4,$legalSeal4)}
          signfield11: ${epaasSignContentField(1,$roleorgId1,2,$officialSeal4)}
          signfield12: ${epaasSignContentField(2,$roleorgId1,3,$personalSeal4)} #经办人企业1正文签
          fields_draft: [$signfield11,$signfield12,$signorglegfield2]
      validate:
        - eq: [content.code,0]
        - eq: [content.message,"成功"]
        - eq: [ content.data.0.contentId, $contentId4 ]


- test:
      name: "签署人1公章和经办人章都指定id,签署人2未指定--成功"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [

            ]
      validate:
        - eq: [content.status,200]
        - contains: [content.message,"成功"]
        - eq: [ content.data, null ]


- test:
    name: "TC2-场景：无序签节点内多个签署人_签署人1sealCheck为1,签署人2sealCheck为0-查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId4
    extract:
      - signerInfos4: content.data.signerInfos
      - signFiles4: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]
      - eq: [content.data.signerInfos.0.signNode,1]
      - eq: [ content.data.signerInfos.0.signMode,1 ]
      - eq: [ content.data.signerInfos.0.signOrder,1 ]
      - eq: [content.data.signerInfos.1.signNode,1]
      - eq: [ content.data.signerInfos.1.signMode,1 ]
      - eq: [ content.data.signerInfos.1.signOrder,1 ]

- test:
    name: "TC2-场景：无序签节点内多个签署人_签署人1sealCheck为1,签署人2sealCheck为0-一步发起---有bug"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles4
      signerInfos: $signerInfos4
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]
