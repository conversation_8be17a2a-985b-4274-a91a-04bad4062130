- config:
    name: "支持页面设置备注签署区-V6.0.12.0-beta.1"
    variables:
        successCode: 200
        successMessage: 成功
        userCodeInit: ${ENV(csqs.userCode)}
        orgCodeInit: ${ENV(csqs.orgCode)}
      # 内部账号
        innerUserCode2: ${ENV(csqs.userCode)}
        innerUserName2: "测试签署"
        innerUserCode1: ${ENV(sign01.userCode)}
        innerUserName1: ${ENV(sign01.userName)}
        innerUserCode3: ${ENV(sign03.userCode)}
      # 内部企业
        innerOrgNo1: ${ENV(sign01.main.orgNo)}
        innerOrgCode1: ${ENV(sign01.main.orgCode)}
        innerOrgName1: ${ENV(sign01.main.orgName)}
        innerOrgNo2: ${ENV(csqs.main.orgNo)}
        innerOrgCode2: ${ENV(csqs.orgCode)}
        innerOrgNo3: ${ENV(sign03.main.departCode)}
      #相对方用户
        userCodeOpposit: ${ENV(wsignwb01.userCode)}
        orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
        fileKey0: ${ENV(fileKey)}
        fileKey1: ${ENV(1PageFileKey)}
        fileKey2: ${ENV(3PageFileKey)}
        fileKeyOFD0: ${ENV(ofdFileKey)}
        fileKeyOFD1: ${ENV(1PageOFDFileKey)}
        fileKeyOFD2: ${ENV(3PageOFDFileKey)}
        collectionTaskId0: "${getRemarkCollectionTaskId()}"
        domainHost: ${ENV(esign.projectHost)}
        outerDomainHost: ${ENV(esign.projectOuterHost)}
        roleorgId0: "$innerOrgCode1:$innerUserCode1"
        sealInfos_null: [{"fileKey": $fileKey0,"signConfigs": [] }]
        sealInfo_person: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"PERSON-SEAL","sealId":""}]}
        sealInfo_org: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"COMMON-SEAL","sealId":""}]}
        sealInfo_legal: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"LEGAL-PERSON-SEAL","sealId":""}]}
        sealInfo_org_legal_person: {"fileKey":$fileKey0,"signConfigs":[
          {"signatureType":"PERSON-SEAL","sealId":""},
          {"signatureType":"COMMON-SEAL","sealId":""},
          {"signatureType":"LEGAL-PERSON-SEAL","sealId":""}
        ]}
        remarkSignConfig_inputType1:
          {
            "aiCheck": 0,
            "collectionTaskId": "",
            "inputType": 1,
            "remarkContent": "signFilePreTaskRemarkSceneTC.yml",
            "remarkFontSize": 20
          }
        remarkSignConfig_inputType2:
          {
            "aiCheck": 0,
            "collectionTaskId": "",
            "inputType": 2,
            "remarkContent": "signFilePreTaskRemarkSceneTC.yml",
            "remarkFontSize": 12
          }
        signConfigs_remark_save: {
            "addSignDate": false,
            "keywordInfo": null,
            "pageNo": "1",
            "posX": 300,
            "posY": 300,
            "sealSignDatePositionInfo": null,
            "signPosName": "自动化测试备注",
            "signType": "COMMON-SIGN",
            "signatureType": "PERSON-SEAL",
            "signFieldType": 1,
            "remarkSignConfig": $remarkSignConfig_inputType2,
            "allowMove": 0
          }
        signConfigs_person_save: {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 200,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试个人签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": 0
                      }
        signConfigs_legal_save: {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 100,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试法人签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": 0
                      }
        signConfigs_org_save: {
          "addSignDate": false,
          "keywordInfo": null,
          "pageNo": "1",
          "posX": 270,
          "posY": 400,
          "sealSignDatePositionInfo": null,
          "signPosName": "自动化测试企业签署区",
          "signType": "COMMON-SIGN",
          "signatureType": "COMMON-SEAL",
          "allowMove": 0
        }
        sealInfos_remark_save: {
            "fileKey": $fileKey0,
            "signConfigs": [$signConfigs_remark_save],
            "sealIdList": [],
            "signatureTypeList": ["PERSON-SEAL"]
          }
        sealInfos_person_remark_save: {
            "fileKey": $fileKey0,
            "signConfigs": [$signConfigs_person_save,$signConfigs_remark_save],
            "sealIdList": [],
            "signatureTypeList": ["PERSON-SEAL"]
          }
        sealInfos_legal_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_legal_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","LEGAL-PERSON-SEAL" ]
        }
        sealInfos_org_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_org_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","COMMON-SEAL" ]
        }
        sealInfos_org_legal_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_org_save,$signConfigs_legal_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","COMMON-SEAL","LEGAL-PERSON-SEAL" ]
        }

- test:
      name: "TC1：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId1: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "TC1：获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId1
    extract:
      - editUrl1: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC1：epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl1)}
    extract:
      - contentId11: content.data.contents.0.id
      - entityId11: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
      name: "TC1：查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId11
          entityId1: $entityId11
          tplToken_content: ${getTplToken($editUrl1)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId1: content.data.baseFile.resourceId
        - baseFile1: content.data.baseFile

- test:
    name: "TC1：获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl1)}
    extract:
      - _signerId1: content.data.0.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "TC1：保存草稿-设置签署区-页面设置签署方1-F1个人_备注2个签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl1)}
          baseFile_draft: $baseFile1
          originFile_draft: {resourceId: "$resourceId1"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId11
          entityId_draft: $entityId11
          signpersonfield1: ${epaasSignContentField(1,$_signerId1,1)}
          signpersonremarkfield1: ${epaasSignContentField(3,$_signerId1,1)}
          signpersonremarkfield2: ${epaasSignContentField(3,$_signerId1,1)}
          fields_draft: [$signpersonfield1,$signpersonremarkfield1,$signpersonremarkfield2]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId11 ]
      teardown_hooks:
        - ${sleep(0.5)}

- test:
      name: "TC1：save-页面设置签署方1-F1个人_备注2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "TC1：signFilePreTaskDetail-签署方1-F1个人_备注2个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "TC1：createAndStart-签署方1-F1个人_备注2个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC1-签署方1-F1个人_备注2个签署区"
      signFiles: $signFiles1
      signerInfos: $signerInfos1
    extract:
      - signFlowId1: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC2：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId2: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "TC2：获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId2
    extract:
      - editUrl2: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2：epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl2)}
    extract:
      - contentId2: content.data.contents.0.id
      - entityId2: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
      name: "TC2：查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId2
          entityId1: $entityId2
          tplToken_content: ${getTplToken($editUrl2)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId2: content.data.baseFile.resourceId
        - baseFile2: content.data.baseFile

- test:
    name: "TC2：获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl2)}
    extract:
      - _signerId2: content.data.0.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "TC2：保存草稿-设置签署区-F1法人_经办人_备注3个签署区(指定用章要求：公章+法人章)"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl2)}
          baseFile_draft: $baseFile2
          originFile_draft: {resourceId: "$resourceId2"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId2
          entityId_draft: $entityId2
          signpersonfield2: ${epaasSignContentField(1,$_signerId2,3)}
          signpersonremarkfield1: ${epaasSignContentField(3,$_signerId2,3)}
          signlegalfield1: ${epaasSignContentField(2,$_signerId2,4)}
          fields_draft: [$signpersonfield2,$signpersonremarkfield1,$signlegalfield1]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId2 ]
      teardown_hooks:
        - ${sleep(0.5)}

- test:
      name: "TC2：save-页面设置签署方1-F1法人_经办人_备注3个签署区(指定用章要求：公章+法人章)"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "TC2：signFilePreTaskDetail-签署方1-F1法人_经办人_备注3个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "TC2：createAndStart-签署方1-F1法人_经办人_备注3个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC2-签署方1-F1法人_经办人_备注3个签署区"
      signFiles: $signFiles2
      signerInfos: $signerInfos2
    extract:
      - signFlowId2: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC3：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey": $fileKey0,"signConfigs": [] },{"fileKey": $fileKey1,"signConfigs": [] }],
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode2,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId3: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "TC3：获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId3
    extract:
      - editUrl3: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3：epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl3)}
    extract:
      - contentId3: content.data.contents.0.id
      - entityId3: content.data.contents.0.entityId
      - contentId31: content.data.contents.1.id
      - entityId31: content.data.contents.1.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
      name: "TC3：查询recourceId3"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId3
          entityId1: $entityId3
          tplToken_content: ${getTplToken($editUrl3)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId3: content.data.baseFile.resourceId
        - baseFile3: content.data.baseFile


- test:
      name: "TC3：查询recourceId31"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId31
          entityId1: $entityId31
          tplToken_content: ${getTplToken($editUrl3)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId31: content.data.baseFile.resourceId
        - baseFile31: content.data.baseFile


- test:
    name: "TC3：获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl3)}
    extract:
      - _signerId3: content.data.0.id
      - _signerId31: content.data.1.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "TC3：保存草稿-设置签署区签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl3)}
          signorgpersonfield51: ${epaasSignContentField(1,$_signerId3,3)}
          signlegalfield51: ${epaasSignContentField(1,$_signerId3,4)}
          signremarkfield511: ${epaasSignContentField(3,$_signerId3,1)}
          signremarkfield512: ${epaasSignContentField(3,$_signerId3,1)}
          signremarkfield513: ${epaasSignContentField(3,$_signerId3,1)}
          signpersonfield52: ${epaasSignContentField(1,$_signerId31,1)}
          fields_draft30: [$signorgpersonfield51,$signlegalfield51,$signremarkfield511,$signremarkfield512,$signremarkfield513,$signpersonfield52]
          signorgpersonfield52: ${epaasSignContentField(1,$_signerId3,3)}
          signremarkfield521: ${epaasSignContentField(3,$_signerId31,1)}
          signremarkfield522: ${epaasSignContentField(3,$_signerId31,1)}
          fields_draft31: [$signorgpersonfield52,$signremarkfield521,$signremarkfield522]
          json_save_draft: {
              "data": [
                        {
                            "baseFile": $baseFile3,
                            "originFile": {resourceId: "$resourceId3"},
                            "fields": $fields_draft30,
                            "pageFormatInfoParam": null,
                            "name": "key30page.pdf",
                            "contentId": $contentId3,
                            "entityId": $entityId3
                        },
                        {
                            "baseFile": $baseFile31,
                            "originFile": {resourceId: "$resourceId31"},
                            "fields": $fields_draft31,
                            "pageFormatInfoParam": null,
                            "name": "一页.pdf",
                            "contentId": $contentId31,
                            "entityId": $entityId31
                        }
              ]
          }
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId3 ]
      teardown_hooks:
        - ${sleep(0.5)}

- test:
      name: "TC3：save-页面设置签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "TC3：signFilePreTaskDetail-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "TC3：createAndStart-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC3-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    extract:
      - signFlowId3: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC4：signFilePreTask-内部企业指定签署区经办人+企业+法人章"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [$sealInfo_org_legal_person],
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId4: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId4]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "TC4：获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId4
    extract:
      - editUrl4: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4：epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl4)}
    extract:
      - contentId4: content.data.contents.0.id
      - entityId4: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
      name: "TC4：查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId4
          entityId1: $entityId4
          tplToken_content: ${getTplToken($editUrl4)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId4: content.data.baseFile.resourceId
        - baseFile4: content.data.baseFile

- test:
    name: "TC4：获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl4)}
    extract:
      - _signerId4: content.data.0.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "TC4：保存草稿-设置签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl4)}
          baseFile_draft: $baseFile4
          originFile_draft: {resourceId: "$resourceId4"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId4
          entityId_draft: $entityId4
          signpersonfield4: ${epaasSignContentField(1,$_signerId4,3)}
          signorgfield4: ${epaasSignContentField(1,$_signerId4,2)}
          signlegalfield4: ${epaasSignContentField(1,$_signerId4,4)}
          fields_draft: [$signpersonfield4,$signorgfield4,$signlegalfield4]
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId4 ]
      teardown_hooks:
        - ${sleep(0.5)}

- test:
      name: "TC4：save-页面设置签署方1-F1企业_法人_经办人_备注4个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "TC4：signFilePreTaskDetail-签署方1-F1企业_法人_经办人_备注4个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId4
    extract:
      - signerInfos4: content.data.signerInfos
      - signFiles4: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "TC4：createAndStart-签署方1-F1企业_法人_经办人_备注4个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC4-指定签署区类型后-签署方1-F1企业_法人_经办人_备注4个签署区"
      signFiles: $signFiles4
      signerInfos: $signerInfos4
    extract:
      - signFlowId4: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC5：signFilePreTask-相对方企业和个人或签"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey": $fileKey0,"signConfigs": [] },{"fileKey": $fileKey1,"signConfigs": [
                  { "signatureType": "PERSON-SEAL","sealId": "" },
                  { "signatureType": "COMMON-SEAL","sealId": "" }
                ] }],
                signNode: 1,
                signMode: 2,
                userCode: $userCodeOpposit,
                organizationCode: $orgCodeOpposit,
                sealTypeCode: "PUBLIC",
                signOrder: 1,
                userType: 2
              },
              {
                sealInfos: [$sealInfo_person],
                signNode: 1,
                signMode: 2,
                userCode: $userCodeOpposit,
                signOrder: 1,
                userType: 2
              }
            ]
      extract:
        filePreTaskId5: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]
         - contains: [content.data.filePreUrl, "$domainHost"]
         - contains: [content.data.filePreOuterUrl, "$outerDomainHost"]

- test:
    name: "TC5：获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId5
    extract:
      - editUrl5: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC5：epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($editUrl5)}
    extract:
      - contentId5: content.data.contents.0.id
      - entityId5: content.data.contents.0.entityId
      - contentId51: content.data.contents.1.id
      - entityId51: content.data.contents.1.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]



- test:
      name: "TC5：查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId5
          entityId1: $entityId5
          tplToken_content: ${getTplToken($editUrl5)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId5: content.data.baseFile.resourceId
        - baseFile5: content.data.baseFile

- test:
      name: "TC5：查询recourceId"
      api: api/esignDocs/epaasTemplate/contentDraft.yml
      variables:
          contentId1: $contentId51
          entityId1: $entityId51
          tplToken_content: ${getTplToken($editUrl5)}
      validate:
        - eq: [content.code,0]
        - ne: [ content.data, null ]
      extract:
        - resourceId51: content.data.baseFile.resourceId
        - baseFile51: content.data.baseFile

- test:
    name: "TC5：获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${getTplToken($editUrl5)}
    extract:
      - _signerId5: content.data.0.id
      - _signerId51: content.data.1.id
    validate:
      - eq: ["content.code", 0]
      - ne: ["content.data.0.id",""]

- test:
      name: "TC5：保存草稿-设置签署区"
      api: api/esignDocs/epaasTemplate/batch-save-draft.yml
      variables:
          tplToken_draft: ${getTplToken($editUrl5)}
          baseFile_draft: $baseFile5
          originFile_draft: {resourceId: "$resourceId5"}
          name_draft: "key30page.pdf"
          contentId_draft: $contentId5
          entityId_draft: $entityId5
          signpersonfield5: ${epaasSignContentField(1,$_signerId5,3)}
          signorgfield5: ${epaasSignContentField(1,$_signerId5,2)}
          signremarkfield5: ${epaasSignContentField(3,$_signerId51,3)}
          fields_draft10: [$signpersonfield5,$signorgfield5,$signremarkfield5]
          signremarkfield51: ${epaasSignContentField(1,$_signerId51,1)}
          signremarkfield52: ${epaasSignContentField(1,$_signerId51,1)}
          signorgpersonfield52: ${epaasSignContentField(1,$_signerId5,3)}
          fields_draft20: [$signremarkfield51,$signremarkfield52,$signorgpersonfield52]
          json_save_draft: {
              "data": [
                        {
                            "baseFile": $baseFile5,
                            "originFile": {resourceId: "$resourceId5"},
                            "fields": $fields_draft10,
                            "pageFormatInfoParam": null,
                            "name": "key30page.pdf",
                            "contentId": $contentId5,
                            "entityId": $entityId5
                        },
                        {
                            "baseFile": $baseFile51,
                            "originFile": {resourceId: "$resourceId51"},
                            "fields": $fields_draft20,
                            "pageFormatInfoParam": null,
                            "name": "一页.pdf",
                            "contentId": $contentId51,
                            "entityId": $entityId51
                        }
              ]
          }
      validate:
        - eq: [content.code,0]
        - eq: [ content.data.0.contentId, $contentId5 ]
      teardown_hooks:
        - ${sleep(0.5)}

- test:
      name: "TC5：save-页面设置签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,"成功"]
        - eq: [ content.data, null ]

- test:
    name: "TC5：signFilePreTaskDetail-签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, 200]
      - eq: [ content.message, "成功"]
      - ne: [ content.data, null ]

- test:
    name: "TC5：createAndStart-签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC5-相对方签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    extract:
      - signFlowId5: content.data.signFlowId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]