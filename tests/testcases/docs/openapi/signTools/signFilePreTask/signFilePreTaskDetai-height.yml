- config:
    name: "签署区设置页支持自定义宽高"
    variables:
       successCode: 200
       successMessage: 成功
       userName0: ${ENV(sign01.userName)}
       userNameOpposit: ${ENV($userCodeOpposit.userName)}
       orgName0: ${ENV(sign01.main.orgName)}
       orgNameOpposit: ${ENV($userCodeOpposit.main.orgName)}
       signjz01UserCode: ${ENV(signjz01.userCode)}
       signjz01UserName: ${ENV(signjz01.userName)}
       org01LegalSealId: ${ENV(org01.legal.sealId)}
       org01CommonSealTypeCode1: ${ENV(orgSealTypeCode)}
       org01CommonSealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealId0: ${ENV(org01.sealId)}
       userSealId0: ${ENV(sign01.sealId)}
       sealTypeCode0: ${ENV(orgSealTypeCode)}
       sealId2: ${ENV(orgSealId2)}
       sealTypeCode2: ${ENV(orgSealTypeCode2)}
       sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
       userSealIdStop: ${ENV(userSealStop)}
       userSealIdDelete: ${ENV(userSealDelete)}
       userSealIdNotPublic: ${ENV(userSealNoPublic)}
       fileKey1page: ${ENV(1PageFileKey)}
       signatureType0: " PERSON-SEAL "
       signatureType1: " COMMON-SEAL "
       userCode0: ${ENV(sign01.userCode)}
       userCodeOpposit: ${ENV(wsignwb01.userCode)}
       userCodeInit: ${ENV(csqs.userCode)}
       orgCodeInit: ${ENV(csqs.orgCode)}
       orgCode0: ${ENV(sign01.main.orgCode)}
       orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
       departmentCode0: ${ENV(sign01.main.orgCode)}
       customAccountNoSigner0: ${ENV(sign01.accountNo)}

       #公章下的印章id
       org01CommonSeal: ${ENV(org01.sealId)}
      #内部用户的主责是部门
       customAccountNoSigner1: ${ENV(sign03.accountNo)}
       departmentNo1: ${ENV(sign03.main.departNo)}
       orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
       customAccountNoSigner2: ${ENV(sign01.accountNo)}
       customAccountNoSigner3: ${ENV(sign03.accountNo)}
       departmentNo2: ${ENV(sign01.JZ.departNo)}
       orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
       customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
       customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
       customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
       customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
       sp: " "
       customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
       fileKey0: ${ENV(fileKey)}
       fileKey1: ${ENV(ofdFileKey)}
       2PageFileKey: ${ENV(2PageFileKey)}
#       signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]


- test:
    name: "signFilePreTask-企业+企业经办人+法人不指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "signatureType": "COMMON-SEAL"
                   },
                   {
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]
##todo--新增宽高
- test:
    name: "save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "areaType": 1,
                           "height": 180,
                           "width": 200,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 320,
                       "posY": 728,
                       "height": "",
                       "width": "",
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 220,
                       "posY": 528,
                       "edgeScope": null,
                       "height": 149,
                       "width": 149,
                       "areaType": 1,
                       "sealSignDatePositionInfo": null,
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]

#查询也新增宽高
- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.0.width, "200"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.0.height, "180"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.width, ""]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.height, ""]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.width, "149"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.height, "149"]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
    name: "signFilePreTask-企业+企业经办人指定印章有商密+国密"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
       expirationDate: 30
       signerInfos:
         [
           {
             "sealInfos": [
               {
                 "fileKey": "$fileKey0",
                 "signConfigs": [
                   {
                     "sealId": "${ENV(org01.cloud.guomi.SealId)}",
                     "signatureType": "COMMON-SEAL"
                   },{
                   "sealId": "${ENV(org01.sealId)}",
                   "signatureType": "COMMON-SEAL"
                 },
                   {
                     "sealId": "${ENV(sign01.cloud.SealId)}",
                     "signatureType": "PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.sealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   },
                   {
                     "sealId": "${ENV(org01.legal.cloud.guomi.SealId)}",
                     "signatureType": "LEGAL-PERSON-SEAL"
                   }
                 ]
               }
             ],
             "signNode": 2,
             "signMode": 0,
             "userType": 1,
             "userCode": "$userCode0",
             "organizationCode": "$orgCode0"
           }
         ]
    extract:
        filePreTaskId0: content.data.filePreTaskId
    validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId0]

- test:
    name: "save保存签署区设置--再加一个备注签"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId0
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "height": "50",
                           "width": "50",
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "sealId": "${ENV(org01.sealId)}",
                           "signPosName": "$userCode0-$orgCode0",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false
                        },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 320,
                       "posY": 728,
                       "edgeScope": null,
                       "height": "500",
                       "width": "500",
                       "sealSignDatePositionInfo": null,
                       "sealId": "${ENV(sign01.cloud.SealId)}",
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "PERSON-SEAL",
                       "allowMove": false
                     },{
                       "addSignDate": false,
                       "keywordInfo": null,
                       "pageNo": "1",
                       "posX": 220,
                       "posY": 528,
                       "height": "150",
                       "width": "150",
                       "edgeScope": null,
                       "sealSignDatePositionInfo": null,
                       "sealId": "${ENV(org01.legal.cloud.guomi.SealId)}",
                       "signPosName": "$userCode0-$orgCode0",
                       "signType": "COMMON-SIGN",
                       "signatureType": "LEGAL-PERSON-SEAL",
                       "allowMove": false
                     },{
                                "addSignDate": false,
                                "keywordInfo": null,
                                "pageNo": "1",
                                "posX": 464,
                                "posY": 614,
                                "width": 258,
                                "height": 135,
                                "edgeScope": null,
                                "sealSignDatePositionInfo": null,
                                "signPosName": "测试签署一-esigntest自动化签署中心CI测试",
                                "signType": "COMMON-SIGN",
                                "signatureType": "PERSON-SEAL",
                                "allowMove": false,
                                "remarkSignConfig": {
                                    "inputType": 2,
                                    "remarkFontSize": 14,
                                    "aiCheck": 0,
                                    "collectionTaskId": "",
                                    "collectionTaskName": "",
                                    "remarkContent": "",
                                    "remarkFieldWidth": 258,
                                    "remarkFieldHeight": 135,
                                    "remarkPosX": 464,
                                    "remarkPosY": 614
                                },
                                "signFieldType": 1,
                                "areaType": 1
                            }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $userCode0,
               "userType": 1,
               "userCode": $userCode0,
               "userName": $userName0,
               "signerType": 2,
               "legalSignFlag": 1,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode0,
               "organizationName": $orgName0,
               "departmentName": $orgName0,
               "departmentCode": $departmentCode0,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,$successCode]
      - eq: [content.message,$successMessage]
      - eq: [ content.data, null ]
#
- test:
    name: "signFilePreTaskDetail查询详情"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId0
    extract:
      - signerInfos0: content.data.signerInfos
      - signFiles0: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.0.width, "50"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.0.height, "50"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.width, "500"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.height, "500"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.width, "150"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.height, "150"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.3.remarkSignConfig.remarkFieldHeight, 135]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.3.remarkSignConfig.remarkFieldWidth, 258]
      - ne: [ content.data, null ]

- test:
    name: "使用签署区设置的详情参数到签署一步发起"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      signFiles: $signFiles0
      signerInfos: $signerInfos0
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]
