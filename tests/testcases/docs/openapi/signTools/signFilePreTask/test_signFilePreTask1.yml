- config:
    name: "获取签署区配置页-参数校验(用于testsuite)"

- test:
    name: $name
    api: api/esignDocs/openapi/signTools/signFilePreTask.yml
    variables:
      customAccountNo: $customAccountNoSigner
      customDepartmentNo: $customDepartmentNoSigner
      customOrgNo: $customOrgNoSigner
      departmentCode: $departmentCodeSigner
      legalSignFlag: 0
      organizationCode: $organizationCodeSigner
      signatureType: $signatureType
      userCode: $userCodeSigner
      userType: 1
    validate:
      - eq: [ content.code, $code ]
      - contains: [ content.message, $message]
      - ne: [ content.data, $data ]
