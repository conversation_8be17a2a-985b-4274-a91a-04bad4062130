- config:
    name: "支持页面设置备注签署区-V6.0.12.0-beta.1"
    variables:
        successCode: 200
        successMessage: 成功
        userCodeInit: ${ENV(csqs.userCode)}
        orgCodeInit: ${ENV(csqs.orgCode)}
      # 内部账号
        innerUserCode2: ${ENV(csqs.userCode)}
        innerUserName2: "测试签署"
        innerUserCode1: ${ENV(sign01.userCode)}
        innerUserName1: ${ENV(sign01.userName)}
        innerUserCode3: ${ENV(sign03.userCode)}
      # 内部企业
        innerOrgNo1: ${ENV(sign01.main.orgNo)}
        innerOrgCode1: ${ENV(sign01.main.orgCode)}
        innerOrgName1: ${ENV(sign01.main.orgName)}
        innerOrgNo2: ${ENV(csqs.main.orgNo)}
        innerOrgCode2: ${ENV(csqs.orgCode)}
        innerOrgNo3: ${ENV(sign03.main.departCode)}
      #相对方用户
        userCodeOpposit: ${ENV(wsignwb01.userCode)}
        orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
        fileKey0: ${ENV(fileKey)}
        fileKey1: ${ENV(1PageFileKey)}
        fileKey2: ${ENV(3PageFileKey)}
        fileKeyOFD0: ${ENV(ofdFileKey)}
        fileKeyOFD1: ${ENV(1PageOFDFileKey)}
        fileKeyOFD2: ${ENV(3PageOFDFileKey)}
        collectionTaskId0: "${getRemarkCollectionTaskId()}"
        sealInfos_null: [{"fileKey": $fileKey0,"signConfigs": [] }]
        sealInfo_person: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"PERSON-SEAL","sealId":""}]}
        sealInfo_org: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"COMMON-SEAL","sealId":""}]}
        sealInfo_legal: {"fileKey":$fileKey0,"signConfigs":[{"signatureType":"LEGAL-PERSON-SEAL","sealId":""}]}
        sealInfo_org_legal_person: {"fileKey":$fileKey0,"signConfigs":[
          {"signatureType":"PERSON-SEAL","sealId":""},
          {"signatureType":"COMMON-SEAL","sealId":""},
          {"signatureType":"LEGAL-PERSON-SEAL","sealId":""}
        ]}
        remarkSignConfig_inputType1:
          {
            "aiCheck": 0,
            "collectionTaskId": "",
            "inputType": 1,
            "remarkContent": "signFilePreTaskRemarkSceneTC.yml",
            "remarkFontSize": 20
          }
        remarkSignConfig_inputType2:
          {
            "aiCheck": 0,
            "collectionTaskId": "",
            "inputType": 2,
            "remarkContent": "signFilePreTaskRemarkSceneTC.yml",
            "remarkFontSize": 12
          }
        signConfigs_remark_save: {
            "addSignDate": false,
            "keywordInfo": null,
            "pageNo": "1",
            "posX": 300,
            "posY": 300,
            "sealSignDatePositionInfo": null,
            "signPosName": "自动化测试备注",
            "signType": "COMMON-SIGN",
            "signatureType": "PERSON-SEAL",
            "signFieldType": 1,
            "remarkSignConfig": $remarkSignConfig_inputType2,
            "allowMove": 0
          }
        signConfigs_person_save: {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 200,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试个人签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "PERSON-SEAL",
                        "allowMove": 0
                      }
        signConfigs_legal_save: {
                        "addSignDate": false,
                        "keywordInfo": null,
                        "pageNo": "1",
                        "posX": 270,
                        "posY": 100,
                        "sealSignDatePositionInfo": null,
                        "signPosName": "自动化测试法人签署区",
                        "signType": "COMMON-SIGN",
                        "signatureType": "LEGAL-PERSON-SEAL",
                        "allowMove": 0
                      }
        signConfigs_org_save: {
          "addSignDate": false,
          "keywordInfo": null,
          "pageNo": "1",
          "posX": 270,
          "posY": 400,
          "sealSignDatePositionInfo": null,
          "signPosName": "自动化测试企业签署区",
          "signType": "COMMON-SIGN",
          "signatureType": "COMMON-SEAL",
          "allowMove": 0
        }
        sealInfos_remark_save: {
            "fileKey": $fileKey0,
            "signConfigs": [$signConfigs_remark_save],
            "sealIdList": [],
            "signatureTypeList": ["PERSON-SEAL"]
          }
        sealInfos_person_remark_save: {
            "fileKey": $fileKey0,
            "signConfigs": [$signConfigs_person_save,$signConfigs_remark_save],
            "sealIdList": [],
            "signatureTypeList": ["PERSON-SEAL"]
          }
        sealInfos_legal_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_legal_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","LEGAL-PERSON-SEAL" ]
        }
        sealInfos_org_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_org_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","COMMON-SEAL" ]
        }
        sealInfos_org_legal_person_remark_save: {
          "fileKey": $fileKey0,
          "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ,$signConfigs_org_save,$signConfigs_legal_save],
          "sealIdList": [ ],
          "signatureTypeList": [ "PERSON-SEAL","COMMON-SEAL","LEGAL-PERSON-SEAL" ]
        }

- test:
      name: "TC1：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId1: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId1]

- test:
      name: "TC1：save-页面设置签署方1-F1个人_备注2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [
              {
                "sealInfos": [ $sealInfos_person_remark_save ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "legalSignFlag": 0,
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC1：signFilePreTaskDetail-签署方1-F1个人_备注2个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId1
    extract:
      - signerInfos1: content.data.signerInfos
      - signFiles1: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.inputType, 2 ]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkFieldWidth, 149 ]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkFieldHeight, 149 ]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkFontSize, 12 ]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkPageNo, "1"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkPosX, "300"]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.1.remarkSignConfig.remarkPosY, "300"]
      - ne: [ content.data, null ]

- test:
    name: "TC1：createAndStart-签署方1-F1个人_备注2个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC1-签署方1-F1个人_备注2个签署区"
      signFiles: $signFiles1
      signerInfos: $signerInfos1
    extract:
      - signFlowId1: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC2：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId2: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId2]

- test:
      name: "TC2：save-页面设置签署方1-F1法人_经办人_备注3个签署区(指定用章要求：公章+法人章)"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId2
          filePreTaskInfos:
            [
              {
                "sealInfos": [ $sealInfos_legal_person_remark_save ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "sealTypeCode": "COMMON-SEAL,LEGAL-PERSON-SEAL",
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC2：signFilePreTaskDetail-签署方1-F1法人_经办人_备注3个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId2
    extract:
      - signerInfos2: content.data.signerInfos
      - signFiles2: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC2：createAndStart-签署方1-F1法人_经办人_备注3个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC2-签署方1-F1法人_经办人_备注3个签署区"
      signFiles: $signFiles2
      signerInfos: $signerInfos2
    extract:
      - signFlowId2: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC3：signFilePreTask-不指定签署区"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey": $fileKey0,"signConfigs": [] },{"fileKey": $fileKey1,"signConfigs": [] }],
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode2,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId3: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId3]

- test:
      name: "TC3：save-页面设置签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId3
          filePreTaskInfos:
            [
              {
                "sealInfos": [ $sealInfos_legal_person_remark_save,
                  {
                    "fileKey": $fileKey1,
                    "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ],
                    "sealIdList": [ ],
                    "signatureTypeList": [ "PERSON-SEAL" ]
                  }
                ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "legalSignFlag": 0,
                "needShowSeal": 1
              },
              {
                "sealInfos": [ $sealInfos_person_remark_save ],
                "signerId": "$innerUserCode2",
                "userType": 1,
                "userCode": $innerUserCode2,
                "userName": $innerUserName2,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode2",
                "legalSignFlag": 0,
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC3：signFilePreTaskDetail-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId3
    extract:
      - signerInfos3: content.data.signerInfos
      - signFiles3: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC3：createAndStart-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC3-签署方1-F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区无序"
      signFiles: $signFiles3
      signerInfos: $signerInfos3
    extract:
      - signFlowId3: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC4：signFilePreTask-内部企业指定签署区经办人+企业+法人章"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [$sealInfo_org_legal_person],
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId4: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId4]

- test:
      name: "TC4：save-页面设置签署方1-F1企业_法人_经办人_备注4个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId4
          filePreTaskInfos:
            [
              {
                "sealInfos": [ $sealInfos_org_legal_person_remark_save ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "sealTypeCode": "",
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC4：signFilePreTaskDetail-签署方1-F1企业_法人_经办人_备注4个签署区"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId4
    extract:
      - signerInfos4: content.data.signerInfos
      - signFiles4: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC4：createAndStart-签署方1-F1企业_法人_经办人_备注4个签署区"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC4-指定签署区类型后-签署方1-F1企业_法人_经办人_备注4个签署区"
      signFiles: $signFiles4
      signerInfos: $signerInfos4
    extract:
      - signFlowId4: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC5：signFilePreTask-相对方企业和个人或签"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey": $fileKey0,"signConfigs": [] },{"fileKey": $fileKey1,"signConfigs": [
                  { "signatureType": "PERSON-SEAL","sealId": "" },
                  { "signatureType": "COMMON-SEAL","sealId": "" }
                ] }],
                signNode: 1,
                signMode: 2,
                userCode: $userCodeOpposit,
                organizationCode: $orgCodeOpposit,
                sealTypeCode: "PUBLIC",
                signOrder: 1,
                userType: 2
              },
              {
                sealInfos: [$sealInfo_person],
                signNode: 1,
                signMode: 2,
                userCode: $userCodeOpposit,
                signOrder: 1,
                userType: 2
              }
            ]
      extract:
        filePreTaskId5: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId5]

- test:
      name: "TC5：save-页面设置签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId5
          filePreTaskInfos:
            [
              {
                "sealInfos": [ $sealInfos_org_person_remark_save,
                  {
                    "fileKey": $fileKey1,
                    "signConfigs": [ $signConfigs_person_save,$signConfigs_remark_save ],
                    "sealIdList": [ ],
                    "signatureTypeList": [ "PERSON-SEAL" ]
                  }
                ],
                "signerId": $userCodeOpposit,
                "userType": 2,
                "userName": "",
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$orgCodeOpposit",
                userCode: $userCodeOpposit,
                organizationCode: $orgCodeOpposit,
                "legalSignFlag": 0,
                "needShowSeal": 1
              },
              {
                "sealInfos": [ $sealInfos_person_remark_save ],
                "signerId": $userCodeOpposit,
                "userType": 2,
                "userCode": $userCodeOpposit,
                "userName": "",
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$orgCodeOpposit",
                "legalSignFlag": 0,
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC5：signFilePreTaskDetail-签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId5
    extract:
      - signerInfos5: content.data.signerInfos
      - signFiles5: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC5：createAndStart-签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC5-相对方签署方1-F1企业_经办人_备注3个签署区和F2经办人_备注2个签署区+签署方2-F1个人_备注2个签署区或签"
      signFiles: $signFiles5
      signerInfos: $signerInfos5
    extract:
      - signFlowId5: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC6：signFilePreTask"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId6: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId6]

- test:
      name: "TC6：save-页面设置签署方1-备注1个签署区(用章要求：只有公章)"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId6
          filePreTaskInfos:
            [
              {
                "sealInfos": [ {
                  "fileKey": $fileKey0,
                  "signConfigs": [ $signConfigs_remark_save ,$signConfigs_org_save],
                  "sealIdList": [ ],
                  "signatureTypeList": [ "PERSON-SEAL","COMMON-SEAL" ]
                } ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "sealTypeCode": "COMMON-SEAL",
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC6：signFilePreTaskDetail-签署方1-备注1个签署区(用章要求：只有公章)"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId6
    extract:
      - signerInfos6: content.data.signerInfos
      - signFiles6: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode]
      - eq: [ content.message, $successMessage]
      - ne: [ content.data, null ]

- test:
    name: "TC6：createAndStart-签署方1-备注1个签署区(用章要求：只有公章)"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC6-签署方1-备注1个签署区(用章要求：只有公章)-自动添加经办人自由签签署区"
      signFiles: $signFiles6
      signerInfos: $signerInfos6
    extract:
      - signFlowId6: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC7：signFilePreTask"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey": $fileKeyOFD0,"signConfigs": [] }],
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId7: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId7]

- test:
      name: "TC7：save-页面设置签署方1-一份文件上拖3种备注类型"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId7
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {"fileKey": $fileKeyOFD0,"signConfigs": [
                    $signConfigs_remark_save,$signConfigs_legal_save,
                    {
                      "pageNo": "1,3",
                      "posX": 200,
                      "posY": 200,
                      "signatureType": "PERSON-SEAL",
                      "signFieldType": 1,
                      "remarkSignConfig": $remarkSignConfig_inputType1,
                      "allowMove": 0
                    },{
                      "addSignDate": false,
                      "keywordInfo": null,
                      "pageNo": "2-6",
                      "posX": 300,
                      "posY": 300,
                      "sealSignDatePositionInfo": null,
                      "signPosName": "自动化测试备注",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL",
                      "signFieldType": 1,
                      "remarkSignConfig": {
                          "aiCheck": 0,
                          "collectionTaskId": "$collectionTaskId0",
                          "inputType": 3,
                          "remarkContent": "signFilePreTaskRemarkSceneTC.yml",
                          "remarkFontSize": 12
                        },
                      "allowMove": 1
                    }
                  ],"sealIdList": [],"signatureTypeList": ["PERSON-SEAL"] }
                ],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 1,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "sealTypeCode": "LEGAL-PERSON-SEAL",
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC7：signFilePreTaskDetail-签署方1-一份文件上拖3种备注类型(用章要求：只有法人)"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId7
    extract:
      - signerInfos7: content.data.signerInfos
      - signFiles7: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.remarkSignConfig.remarkFieldWidth, 124 ]
      - eq: [ content.data.signerInfos.0.sealInfos.0.signConfigs.2.remarkSignConfig.remarkFieldHeight, 124 ]
      - ne: [ content.data, null ]

- test:
    name: "TC7：createAndStart-签署方1-一份文件上拖3种备注类型(用章要求：只有法人)"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC7-签署方1-一份文件上拖3种备注类型(用章要求：只有法人)"
      signFiles: $signFiles7
      signerInfos: $signerInfos7
    extract:
      - signFlowId7: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [content.data.signFlowStatus,1]
      - ne: [ content.data, null ]

- test:
      name: "TC8：signFilePreTask-只设置法人签署区OFD流程"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: [{"fileKey":$fileKeyOFD0,"signConfigs":[{"signatureType":"LEGAL-PERSON-SEAL","sealId":""}]}],
                signNode: 1,
                signMode: 0,
                userCode: $innerUserCode1,
                organizationCode: $innerOrgCode1,
                signOrder: 1,
                userType: 1
              }
            ]
      extract:
        filePreTaskId8: content.data.filePreTaskId
      validate:
         - eq: [content.code,$successCode]
         - eq: [content.message,$successMessage]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]
         - contains: [content.data.filePreUrl, $filePreTaskId8]

- test:
    name: "TC8：获取签署区设置页详情-校验页面只展示法人签署区"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      filePreTaskIdDetail: $filePreTaskId8
    extract:
      filePreTaskInfos8: content.data.filePreTaskInfos
    validate:
       - eq: [content.status,$successCode]
       - eq: [content.message,$successMessage]
       - eq: [ content.data.docList.0.fileKey, "$fileKeyOFD0" ]
       - eq: [ content.data.filePreTaskInfos.0.sealInfos.0.signConfigs, null ]
       - eq: [ content.data.filePreTaskInfos.0.sealInfos.0.signatureTypeList.0, "LEGAL-PERSON-SEAL" ]

#- test:
#      name: "TC8：save-页面设置签署方1-加盖备注类型（目前接口不校验，能够添加成功）"
#      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
#      variables:
#        params:
#          filePreTaskId: $filePreTaskId8
#          filePreTaskInfos:
#            [
#              {
#                "sealInfos": [{"fileKey": $fileKeyOFD0,"signConfigs": [$signConfigs_remark_save]}],
#                "signerId": "$innerUserCode1",
#                "userType": 1,
#                "userCode": $innerUserCode1,
#                "userName": $innerUserName1,
#                "signerType": 1,
#                "departmentName": "",
#                "departmentCode": "$innerOrgCode1",
#                "organizationCode": "$innerOrgCode1",
#                "sealTypeCode": "LEGAL-PERSON-SEAL",
#                "needShowSeal": 1
#              }
#            ]
#      validate:
#        - eq: [content.status,$successCode]
#        - eq: [content.message,$successMessage]
#        - eq: [ content.data, null ]

- test:
      name: "TC8：save-页面设置签署方1-法人签署区保存成功"
      api: api/esignDocs/batchTemplateInitiation/signature/save.yml
      variables:
        params:
          filePreTaskId: $filePreTaskId8
          filePreTaskInfos:
            [
              {
                "sealInfos": [
                  {"fileKey": $fileKeyOFD0,"signConfigs": [$signConfigs_legal_save]}],
                "signerId": "$innerUserCode1",
                "userType": 1,
                "userCode": $innerUserCode1,
                "userName": $innerUserName1,
                "signerType": 2,
                "departmentName": "",
                "departmentCode": "$innerOrgCode1",
                "organizationCode": "$innerOrgCode1",
                "sealTypeCode": "LEGAL-PERSON-SEAL",
                "needShowSeal": 1
              }
            ]
      validate:
        - eq: [content.status,$successCode]
        - eq: [content.message,$successMessage]
        - eq: [ content.data, null ]

- test:
    name: "TC8：signFilePreTaskDetail-签署方1-只有法人签署区成功"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId8
    extract:
      - signerInfos8: content.data.signerInfos
      - signFiles8: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode ]
      - eq: [ content.message, $successMessage ]
      - ne: [ content.data, null ]

- test:
    name: "TC8：createAndStart-签署方1-报错之后使用正确的法人签署数据发起流程"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC8-报错之后使用正确的法人经办人签署数据发起流程"
      signFiles: $signFiles8
      signerInfos: $signerInfos8
    extract:
      - signFlowId8: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [ content.data.signFlowStatus,1 ]
      - ne: [ content.data, null ]

- test:
    name: "TC9：signFilePreTask-设置不同指定条件"
    api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
    variables:
      signerInfos:
        [
          {
            sealInfos: [{"fileKey":$fileKey0,"signConfigs":[{"signatureType":"COMMON-SEAL","sealId":""}]},
                        {"fileKey":$fileKey1,"signConfigs":[{"signatureType":"LEGAL-PERSON-SEAL","sealId":""}]},
                        {"fileKey":$fileKey2,"signConfigs":[{"signatureType":"PERSON-SEAL","sealId":"${ENV(sign01.sealId)}"},
                                                            {"signatureType":"PERSON-SEAL","sealId":""}
                        ]}
            ],
            signNode: 1,
            signMode: 1,
            userCode: $innerUserCode1,
            organizationCode: $innerOrgCode1,
            signOrder: 1,
            userType: 1
          },{
            sealInfos: [{"fileKey":$fileKey0,"signConfigs":[{"signatureType":"LEGAL-PERSON-SEAL","sealId":"${ENV(org01.legal.sealId)}"}]},
                        {"fileKey":$fileKey2,"signConfigs":[{"signatureType":"PERSON-SEAL","sealId":""}]}
            ],
            signNode: 1,
            signMode: 1,
            userCode: $innerUserCode2,
            organizationCode: $innerOrgCode1,
            sealTypeCode: "LEGAL-PERSON-SEAL",
            signOrder: 1,
            userType: 1
          },{
            sealInfos: [{"fileKey":$fileKey1,"signConfigs":[{"signatureType":"PERSON-SEAL","sealId":"${ENV(sign01.sealId)}"}]}
            ],
            signNode: 1,
            signMode: 1,
            userCode: $innerUserCode1,
            organizationCode: "",
            signOrder: 1,
            userType: 1
          },{
            sealInfos: [{"fileKey":$fileKey0,"signConfigs":[]}
            ],
            signNode: 1,
            signMode: 1,
            userCode: $userCodeOpposit,
            organizationCode: "$orgCodeOpposit",
            sealTypeCode: "PUBLIC",
            signOrder: 1,
            userType: 2
          }
        ]
    extract:
      filePreTaskId9: content.data.filePreTaskId
    validate:
      - eq: [ content.code,$successCode ]
      - eq: [ content.message,$successMessage ]
      - len_eq: [ content.data.filePreTaskId, 32 ]
      - startswith: [ content.data.filePreUrl,"http" ]
      - contains: [ content.data.filePreUrl, $filePreTaskId9 ]

- test:
    name: "TC9：save-页面设置签署方1-3份文件4个签署方"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
        filePreTaskId: $filePreTaskId9
        filePreTaskInfos:
          [
            {
              "sealInfos": [
                { "fileKey": $fileKey0,"signConfigs": [
                  $signConfigs_org_save
                ],"sealIdList": [ ],"signatureTypeList": [ "COMMON-SEAL" ] },
                { "fileKey": $fileKey1,"signConfigs": [
                  $signConfigs_legal_save
                ],"sealIdList": [ ],"signatureTypeList": [ "LEGAL-PERSON-SEAL" ] },
                { "fileKey": $fileKey2,"signConfigs": [
                  $signConfigs_remark_save,$signConfigs_person_save
                ],"sealIdList": [ ],"signatureTypeList": [ "PERSON-SEAL" ] }
              ],
              "signerId": "$innerUserCode1",
              "userType": 1,
              "userCode": $innerUserCode1,
              "userName": $innerUserName1,
              "signerType": 1,
              "departmentName": "",
              "departmentCode": "$innerOrgCode1",
              "organizationCode": "$innerOrgCode1",
              "sealTypeCode": "",
              "needShowSeal": 1
            },{
              "sealInfos": [
                { "fileKey": $fileKey0,"signConfigs": [
                  $signConfigs_legal_save
                ],"sealIdList": [ ],"signatureTypeList": [ "LEGAL-PERSON-SEAL" ] },
                { "fileKey": $fileKey1,"signConfigs": [
                  $signConfigs_remark_save,$signConfigs_person_save
                ],"sealIdList": [ ],"signatureTypeList": [ "PERSON-SEAL" ] }
              ],
              "signerId": "$innerUserCode2",
              "userType": 1,
              "userCode": $innerUserCode2,
              "userName": $innerUserName2,
              "signerType": 1,
              "departmentName": "",
              "departmentCode": "",
              "organizationCode": "$innerOrgCode1",
              "sealTypeCode": "LEGAL-PERSON-SEAL",
              "needShowSeal": 1
            },{
              "sealInfos": [
                { "fileKey": $fileKey0,"signConfigs": [
                  $signConfigs_remark_save,$signConfigs_person_save
                ],"sealIdList": [ ],"signatureTypeList": [ "PERSON-SEAL" ] }
              ],
              "signerId": "$innerUserCode1",
              "userType": 1,
              "userCode": $innerUserCode1,
              "userName": $innerUserName1,
              "signerType": 1,
              "departmentName": "",
              "departmentCode": "$innerOrgCode1",
              "organizationCode": "",
              "sealTypeCode": "",
              "needShowSeal": 1
            },{
              "sealInfos": [
                { "fileKey": $fileKey0,"signConfigs": [
                  $signConfigs_org_save
                ],"sealIdList": [ ],"signatureTypeList": [ "COMMON-SEAL" ] },
                { "fileKey": $fileKey1,"signConfigs": [
                  $signConfigs_remark_save,$signConfigs_person_save
                ],"sealIdList": [ ],"signatureTypeList": [ "PERSON-SEAL" ] }
              ],
              "signerId": "$userCodeOpposit",
              "userType": 2,
              "userCode": $userCodeOpposit,
              "userName": "",
              "signerType": 1,
              "departmentName": "",
              "departmentCode": "$orgCodeOpposit",
              "organizationCode": "$orgCodeOpposit",
              "sealTypeCode": "PUBLIC",
              "needShowSeal": 1
            }
          ]
    validate:
      - eq: [ content.status,$successCode ]
      - eq: [ content.message,$successMessage ]
      - eq: [ content.data, null ]

- test:
    name: "TC9：signFilePreTaskDetail-3份文件3个签署方混合指定用章要求签署区和sealId"
    api: api/esignDocs/openapi/signTools/signFilePreTaskDetail.yml
    variables:
      filePreTaskId: $filePreTaskId9
    extract:
      - signerInfos9: content.data.signerInfos
      - signFiles9: content.data.signFiles
    validate:
      - eq: [ content.code, $successCode ]
      - eq: [ content.message, $successMessage ]
      - ne: [ content.data, null ]

- test:
    name: "TC9：createAndStart-3份文件4个签署方混合指定用章要求签署区和sealId"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      subject: "TC9-3份文件4个签署方混合指定用章要求签署区和sealId"
      signFiles: $signFiles9
      signerInfos: $signerInfos9
    extract:
      - signFlowId9: content.data.signFlowId
    validate:
      - eq: [ content.code, $successCode ]
      - contains: [ content.message, $successMessage ]
      - eq: [ content.data.signFlowStatus,1 ]
      - ne: [ content.data, null ]

####################验证#######################
- test:
    name: "detail-signFlowId1-检查F1个人_备注2个签署区"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId1
      - organizeCode: ""
      - approvalProcessId: ""
      - requestSource: 2
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "personSeal" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.status, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.sealIdentityType, 1 ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.signIdentity, 1 ]
      - eq: [ content.data.signConfigInfo.0.isFreeSign, false ]

- test:
    name: "detail-signFlowId2-检查F1法人_经办人_备注3个签署区(指定用章要求：公章+法人章)"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId2
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo2: content.data.signConfigInfo
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "COMMON-SEAL,legalSeal,personSeal" ]
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 2 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 1 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosX, "300.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPosY, "300.0" ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.allowMove, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]

- test:
    name: detail-signFlowId2-出参详情校验
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId2
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
      - actorSignConfigList_0: ${dataArraySort($signConfigInfo2, docId,0, actorSignConfigList)}
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - len_eq: [ "$actorSignConfigList_0", 2 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, freeMode)}", 0 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, posX)}", "270.0" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, posY)}", "200.0" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, sealIdentityType)}", 3 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, freeMode)}", 0 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, posX)}", "270.0" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, posY)}", "100.0" ]

- test:
    name: "detail-signFlowId3-检查F1法人_经办人_备注3个签署区和F2经办人_备注2个签署区"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId3
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo3: content.data.signConfigInfo
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo3, docId,0, sealTypes)}", "legalSeal,personSeal" ]
      - eq: [ "${dataArraySort($signConfigInfo3, docId,1, sealTypes)}", ",personSeal" ]
      - len_eq: [ "$signConfigInfo3", 2 ] #签署文档
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.status, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]
      - eq: [ content.data.signConfigInfo.1.remarkSignConfigList.0.inputType, 2 ]

- test:
    name: detail-signFlowId3-出参详情校验
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId3
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
      - actorSignConfigList_0: ${dataArraySort($signConfigInfo3, docId,0, actorSignConfigList)}
      - actorSignConfigList_1: ${dataArraySort($signConfigInfo3, docId,1, actorSignConfigList)}
      - remarkSignConfigList_0: ${dataArraySort($signConfigInfo3, docId,0, remarkSignConfigList)}
      - remarkSignConfigList_1: ${dataArraySort($signConfigInfo3, docId,1, remarkSignConfigList)}
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - len_eq: [ "$actorSignConfigList_0", 2 ]
      - len_eq: [ "$actorSignConfigList_1", 1 ]
      - len_eq: [ "$remarkSignConfigList_0", 1 ]
      - len_eq: [ "$remarkSignConfigList_1", 1 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, inputType)}", 2 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFontSize)}", 12 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkPosY)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, inputType)}", 2 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFontSize)}", 12 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkPosY)}", "300.0" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, freeMode)}", 0 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, sealIdentityType)}", 3 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, freeMode)}", 0 ]

- test:
    name: "detail-signFlowId4-检查F1企业_法人_经办人_备注4个签署区"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId4
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo4: content.data.signConfigInfo
      - actorSignConfigList_0: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo4, docId,0, sealTypes)}", "legalSeal,personSeal" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, sealIdentityType)}", 2 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,2, sealIdentityType)}", 3 ]
      - len_eq: [ "$signConfigInfo4", 1 ] #签署文档
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 3 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 1 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.status, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]

- test:
    name: "detail-signFlowId5-检查相对方企业2份文件-F1经办人_备注_企业+F2经办人_备注"
    api: api/esignSigns/process/detail.yml
    variables:
      - tmp_mobile: "${get_outeruser_customAccountNo($userCodeOpposit,mobile)}"
      - authorization0: '${getVerCodeToken($tmp_mobile,2)}'
      - processId: $signFlowId5
      - organizeCode: "$orgCodeOpposit"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo5: content.data.signConfigInfo
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo5, docId,0, sealTypes)}", ",personSeal" ]
      - len_eq: [ "$signConfigInfo5", 2 ] #签署文档
      - len_eq: [ "${dataArraySort($signConfigInfo5, docId,0, actorSignConfigList)}", 2 ]
      - len_eq: [ "${dataArraySort($signConfigInfo5, docId,1, actorSignConfigList)}", 1 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 1 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.status, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]

- test:
    name: "detail-signFlowId6-检查F1备注1个签署区(用章要求：只有公章)"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId6
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo6: content.data.signConfigInfo
      - actorSignConfigList_0: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo6, docId,0, sealTypes)}", "COMMON-SEAL,personSeal" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, freeMode)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, sealIdentityType)}", 2 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, freeMode)}", 0 ]
      - len_eq: [ "$signConfigInfo6", 1 ] #签署文档
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 2 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 1 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldHeight, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFieldWidth, 149 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.inputType, 2 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkFontSize, 12 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.status, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.aiCheck, 0 ]
      - eq: [ content.data.signConfigInfo.0.remarkSignConfigList.0.remarkPageNo, "1" ]

- test:
    name: "detail-signFlowId7-检查一份OFD文件上拖3种备注类型的属性值正确且自动添加经办人自由签署区"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId7
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo7: content.data.signConfigInfo
      - actorSignConfigList_0: content.data.signConfigInfo.0.actorSignConfigList
      - remarkSignConfigList_0: content.data.signConfigInfo.0.remarkSignConfigList
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo7, docId,0, sealTypes)}", "legalSeal,personSeal" ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, sealIdentityType)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,0, freeMode)}", 1 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, sealIdentityType)}", 3 ]
      - eq: [ "${dataArraySort($actorSignConfigList_0, sealIdentityType,1, freeMode)}", 0 ]
      - len_eq: [ "$signConfigInfo6", 1 ] #签署文档
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 2 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 3 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, inputType)}", 1 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFieldWidth)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFieldHeight)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkPageNo)}", "1,3" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkPosX)}", "200.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkPosY)}", "200.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,0, remarkFontSize)}", 20 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, inputType)}", 2 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkFieldWidth)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkFieldHeight)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkPageNo)}", "1" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkPosY)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,1, remarkFontSize)}", 12 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, inputType)}", 3 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkFieldWidth)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkFieldHeight)}", 124 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkPageNo)}", "2-6" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkPosY)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_0, inputType,2, remarkFontSize)}", 12 ]

- test:
    name: "detail-signFlowId8-检查OFD流程使用法人签署区"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId8
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo8: content.data.signConfigInfo
      - actorSignConfigList_0: content.data.signConfigInfo.0.actorSignConfigList
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ content.data.signConfigInfo.0.sealTypes, "legalSeal" ]
      - eq: [ content.data.signConfigInfo.0.actorSignConfigList.0.sealIdentityType, 3 ]
      - len_eq: [ "$signConfigInfo6", 1 ] #签署文档
      - len_eq: [ content.data.signConfigInfo.0.actorSignConfigList, 1 ]
      - len_eq: [ content.data.signConfigInfo.0.remarkSignConfigList, 0 ]

- test:
    name: "detail-signFlowId9-检查3份文件4个签署方-签署方1"
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId9
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
    extract:
      - signConfigInfo9: content.data.signConfigInfo
    validate:
      - eq: [ content.status, $successCode ]
      - contains: [ content.message, $successMessage]
      - eq: [ "${dataArraySort($signConfigInfo9, docId,0, sealTypes)}", "" ]
      - eq: [ "${dataArraySort($signConfigInfo9, docId,1, sealTypes)}", "legalSeal" ]
      - eq: [ "${dataArraySort($signConfigInfo9, docId,2, sealTypes)}", ",personSeal" ]
      - len_eq: [ "$signConfigInfo9", 3 ] #签署文档

- test:
    name: detail-signFlowId9-出参详情校验
    api: api/esignSigns/process/detail.yml
    variables:
      - processId: $signFlowId9
      - organizeCode: "$innerOrgCode1"
      - approvalProcessId: ""
      - requestSource: 2
      - actorSignConfigList_0: ${dataArraySort($signConfigInfo9, docId,0, actorSignConfigList)}
      - actorSignConfigList_1: ${dataArraySort($signConfigInfo9, docId,1, actorSignConfigList)}
      - remarkSignConfigList_1: ${dataArraySort($signConfigInfo9, docId,2, remarkSignConfigList)}
      - actorSignConfigList_2: ${dataArraySort($signConfigInfo9, docId,2, actorSignConfigList)}
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.message, "成功"]
      - len_eq: [ "$actorSignConfigList_0", 1 ]
      - len_eq: [ "$actorSignConfigList_1", 1 ]
      - len_eq: [ "$actorSignConfigList_2", 1 ]
      - len_eq: [ "$remarkSignConfigList_1", 1 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, aiCheck)}", 0 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, inputType)}", 2 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFieldWidth)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFieldHeight)}", 149 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkFontSize)}", 12 ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkPageNo)}", "1" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkPosX)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, remarkPosY)}", "300.0" ]
      - eq: [ "${dataArraySort($remarkSignConfigList_1, inputType,0, collectionTaskId)}", null ]


