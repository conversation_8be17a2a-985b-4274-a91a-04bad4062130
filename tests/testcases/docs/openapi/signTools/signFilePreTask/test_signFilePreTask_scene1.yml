- config:
    name: "openapi:签署区设置任务发起。场景：发起无序签正常"
    #https://forward.esign.cn/bugManagement/edit?id=49551&type=check
    variables:
        successCode: 200
        successMessage: 成功
        userCodeInit: ${ENV(csqs.userCode)}
        orgCodeInit: ${ENV(csqs.orgCode)}
      # 内部账号
        innerUserCode2: ${ENV(csqs.userCode)}
        innerUserName2: ${ENV(csqs.userName)}
        innerUserCode1: ${ENV(sign01.userCode)}
        innerUserName1: ${ENV(sign01.userName)}
        innerUserCode3: ${ENV(sign03.userCode)}
      # 内部企业
        innerOrgNo1: ${ENV(sign01.main.orgNo)}
        innerOrgCode1: ${ENV(sign01.main.orgCode)}
        innerOrgName1: ${ENV(sign01.main.orgName)}
        innerOrgNo2: ${ENV(csqs.main.orgNo)}
        innerOrgCode2: ${ENV(csqs.orgCode)}
        innerOrgNo3: ${ENV(sign03.main.departCode)}

        fileKey0: ${ENV(fileKey)}
        sealInfos_org: [
                {
                     "fileKey": $fileKey0,
                     "signConfigs": [
                        {
                            "signatureType": "COMMON-SEAL",
                            "sealId": ""
                        }
                    ]
                }
            ]
- test:
      name: "TC1-场景：无序签署-指定内部企业签署+不指定签署方组合"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_org,
                signNode: 1,
                signMode: 1,
                userCode: $innerUserCode1,
                customOrgNo: $innerOrgNo1,
                userType: 1
              },
              {
                sealInfos: $sealInfos_org,
                signNode: 1,
                signMode: 1,
                userCode: "",
                userType: 1
              }
            ]
      validate:
         - eq: [content.code,200]
         - eq: [content.message,"成功"]
         - ne: [content.data,null]