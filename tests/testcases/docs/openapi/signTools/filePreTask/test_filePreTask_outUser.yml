- config:
    name: "获取签署区配置页-相对方个人"
    variables:
      - outUserCode: ${ENV(wsignwb01.userCode)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "不指定"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $outUserCode,
          "departmentCode": "",
          "organizationCode": "",
          "legalSignFlag": 0,
          "sealId": "",
          "sealTypeCode": "",
          "userType": 2
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]