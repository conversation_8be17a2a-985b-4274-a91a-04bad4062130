-   config:
        name: "获取签署区配置页-内部机构"
        variables:
            -   innerUserCode: ${ENV(sign01.userCode)}
            -   innerOrgCode: ${ENV(sign01.main.orgNo)}
            -   innersealCode: "COMMON-SEAL"
            -   innerSealId: ${ENV(org01.sealId)}
            -   signPdfFileName: "testppp.pdf"
            -   signPdfFileKey: ${attachment_upload($signPdfFileName)}

-   test:
        name: "不指定-开启法人"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
                "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
                "fileKeyList": [
                    {
                        "fileKey": $signPdfFileKey
                    }
                ],"signerList": [
                    {
                        "customAccountNo": $innerUserCode,
                        "customDepartmentNo": "",
                        "customOrgNo": $innerOrgCode,
                        "userCode": "",
                        "departmentCode": "",
                        "organizationCode": "",
                        "legalSignFlag": 1,
                        "sealId": "",
                        "sealTypeCode": "",
                        "userType": 1
                    }
                ]
            }
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]

-   test:
        name: "不指定-不开启法人"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
                "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
                "fileKeyList": [
                    {
                        "fileKey": $signPdfFileKey
                    }
                ],"signerList": [
                    {
                        "customAccountNo": $innerUserCode,
                        "customDepartmentNo": "",
                        "customOrgNo": $innerOrgCode,
                        "userCode": "",
                        "departmentCode": "",
                        "organizationCode": "",
                        "legalSignFlag": 0,
                        "sealId": "",
                        "sealTypeCode": "",
                        "userType": 1
                    }
                ]
            }
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]


-   test:
        name: "指定印章类型"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
                "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
                "fileKeyList": [
                    {
                        "fileKey": $signPdfFileKey
                    }
                ],"signerList": [
                    {
                        "customAccountNo": $innerUserCode,
                        "customDepartmentNo": "",
                        "customOrgNo": $innerOrgCode,
                        "userCode": "",
                        "departmentCode": "",
                        "organizationCode": "",
                        "legalSignFlag": 1,
                        "sealId": "",
                        "sealTypeCode": $innersealCode,
                        "userType": 1
                    }
                ]
            }
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]

-   test:
        name: "指定印章id"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
                "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
                "fileKeyList": [
                    {
                        "fileKey": $signPdfFileKey
                    }
                ],"signerList": [
                    {
                        "customAccountNo": $innerUserCode,
                        "customDepartmentNo": "",
                        "customOrgNo": $innerOrgCode,
                        "userCode": "",
                        "departmentCode": "",
                        "organizationCode": "",
                        "legalSignFlag": 1,
                        "sealId": $innerSealId,
                        "sealTypeCode": "",
                        "userType": 1
                    }
                ]
            }
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]

-   test:
        name: "指定印章id指定sealTypeCode"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
                "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
                "fileKeyList": [
                    {
                        "fileKey": $signPdfFileKey
                    }
                ],"signerList": [
                    {
                        "customAccountNo": $innerUserCode,
                        "customDepartmentNo": "",
                        "customOrgNo": $innerOrgCode,
                        "userCode": "",
                        "departmentCode": "",
                        "organizationCode": "",
                        "legalSignFlag": 1,
                        "sealId": $innerSealId,
                        "sealTypeCode": $innersealCode,
                        "userType": 1
                    }
                ]
            }
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]
