- config:
    name: "线上问题：机构只有法人章获取签署区设置页"
    variables:
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - userCode1: ${ENV(sign01.userCode)}
      - userName1: ${ENV(sign01.userName)}
      - subject: '线上问题：机构只有法人章获取签署区设置页'
      - docNameOfFolder: "自动化文件类型专用"
      - docCodeOfFolder: "signs-AUTOTEST"
      - newTemplateUuid1: "${getTemplateId(0,2,pdf,0)}"  ##创建有2个签署区的模板，若有则返回已有
      - newVersion1: 1
      - randStr: ${getDateTime()}
      - presetName1: "企业C指定法人章类型-$randStr"
      - presetName3: "企业C不指定-$randStr"
      - orgCode11: ${ENV(sign01.main.orgCode)}
      - orgNo11: ${ENV(sign01.main.orgNo)}
#      - orgName1: ${ENV(sign01.main.orgName)}
      - autoTestDocUuid1: ${get_docConfig_type()}
      - sealTypeCodeCommon: "LEGAL-PERSON-SEAL"
      - userName0: "测试用户最后删除${get_name(3)}"
      - orgNo1: "tPresetorg${generate_random_str(6)}"
      - orgName1: "esigntest新建${generate_random_str(6)}"
      - sealGroupName2: "法人章分组-${generate_random_str(6)}"
      - sign05jz: ${ENV(orgCodeNoCert)}
      - sign05orgName: "esigntest二级部门CI"
      - userCode3: ${ENV(userCodeNoCert)}
      - userName3: "测试签署五"
      - userNo3: ${ENV(userCodeNoCert)}
      - fileKey: ${ENV(fileKey)}
      - odffilekey: ${ENV(ofdFileKey)}
      - sealInfos_null: [{"fileKey": $fileKey,"signConfigs": [] }]
- test:
    name: 创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName1
        customOrgNo: $orgNo1
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode:
    extract:
      - code0: content.code
      - message0: content.message
      - orgCode1: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]


- test:
    name: 修改内部组织-添加法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      data:
        customOrgNo: $orgNo1
        legalRepAccountNo: $userCode1
    extract:
      - code0: content.code
      - message0: content.message
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

##新建法人印章分组
- test:
    name: TC2-step1-印章总管理员新建云印章-国际标准-法人章分组
    api: api/esignSeals/seals/enterprise/electronic/createGroup.yml
    variables:
      autoPushSeal: "0"
      draftOrProduction: "0"
      sealConfigList: []
      sealGroupConfig:
        organizationCode: $orgCode1
        organizationName: "$orgName1"
        sealGroupDesc: ""
        sealGroupId: ""
        sealGroupName: $sealGroupName2
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealTypeModelKey: ""
        sealTypeName: ""
        sealNumLimit: 0
    extract:
      sealGroupId002: content.data.sealGroupId
    validate:
      - eq: [json.status, 200]
      - eq: [json.success, true]
      - ne: [json.data, ""]
      - eq: [content.data.sealThumbnailFileKey, ""]

####################新建企业印章并授权##############
#- test:
#    name: "Setup2-创建一枚个人章-userSealId0"
#    api: api/esignSeals/v1/userseals/create.yml
#    variables:
#      userSeals_create_json:
#        customAccountNo: $userCode1
#        sealName: "${generate_random_str(10)}"
#        sealInfos:
#          - sealPattern: 1
#            sealColour: 1
#    extract:
#      userSealId0: content.data.sealInfos.0.sealId
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - len_ge: [ content.data.sealInfos, 1 ]
#      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "Setup2-查询个人章-userSealId0"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $userCode1
    extract:
      userSealId0: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.records, 1 ]

- test:
    name: "Setup2-创建一枚法人章(默认)"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $userCode1
        customOrgNo: $orgNo1
        sealGroupName: $sealGroupName2
        sealTypeCode: $sealTypeCodeCommon
        sealInfos:
          - sealPattern: 1
            legalSealId: $userSealId0
            sealName: "企业法人章${generate_random_str(22)}"
    extract:
      legalSealId0: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC9-授权用印人-sign05
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $legalSealId0
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $sign05jz
            userCode: $userCode3
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
    teardown_hooks:
      - ${sleep(3)}

##获取签署区设置页---老接口
-   test:
        name: "未指定印章"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
              "callbackUrl": "http://t-ierp.qs-tech.com/ierp/kapi/v2/lhit/devportal/common/commonback",
              "fileKeyList": [
                {
                  "fileKey": "$fileKey"
                }],
              "signerList": [
                {
                  "customAccountNo": "$userNo3",
                  "organizationCode": "$orgCode1",
                  "userType": 1
                }]}
        extract:
          filePreTaskId: content.data.filePreTaskId
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]


-   test:
        name: "未指定印章-文件是ofd文档"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            json: {
              "callbackUrl": "http://t-ierp.qs-tech.com/ierp/kapi/v2/lhit/devportal/common/commonback",
              "fileKeyList": [
                {
                  "fileKey": "$odffilekey"
                }],
              "signerList": [
                {
                  "customAccountNo": "$userNo3",
                  "organizationCode": "$orgCode1",
                  "userType": 1
                }]}
        validate:
            -   eq: [content.code, 1623046]
            -   contains: [content.message,"无有效的中国标准印章"]
            -   eq: [content.data, null]

#保存签署区设置页
- test:
    name: "仅设置法人章-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1-5",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode3-$sign05jz",
                           "signType": "EDGE-SIGN",
                           "signatureType": "LEGAL-PERSON-SEAL",
                           "allowMove": false,
                           "signFieldType": 0
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $orgCode1:$userCode3,
               "userType": 1,
               "userCode": $userCode3,
               "userName": $userName3,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode1,
               "organizationName": $orgName1,
               "departmentName": $sign05orgName,
               "departmentCode": $sign05jz,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,200]
      - contains: [content.message,"成功"]
      - eq: [ content.data, null ]
- test:
    name: "仅设置公章-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode3-$sign05jz",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false,
                           "signFieldType": 0
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $orgCode1:$userCode3,
               "userType": 1,
               "userCode": $userCode3,
               "userName": $userName3,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode1,
               "organizationName": $orgName1,
               "departmentName": $sign05orgName,
               "departmentCode": $sign05jz,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,200]
      - contains: [content.message,"成功"]
      - eq: [ content.data, null ]

#新接口场景
- test:
      name: "TC1：signFilePreTask-不指定签署区--新接口"
      api: api/esignDocs/openapi/signTools/signFilePreTaskJson.yml
      variables:
          signerInfos:
            [
              {
                sealInfos: $sealInfos_null,
                signNode: 1,
                signMode: 0,
                userCode: $userCode3,
                signOrder: 1,
                userType: 1,
                organizationCode: $orgCode1
              }
            ]
      extract:
        filePreTaskId1: content.data.filePreTaskId
      validate:
         - eq: [content.code,200]
         - eq: [content.message,"成功"]
         - len_eq: [ content.data.filePreTaskId, 32 ]
         - startswith: [content.data.filePreUrl,"http"]

- test:
    name: "新接口获取的签署区设置页，仅设置公章-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode3-$sign05jz",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false,
                           "signFieldType": 0
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $orgCode1:$userCode3,
               "userType": 1,
               "userCode": $userCode3,
               "userName": $userName3,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode1,
               "organizationName": $orgName1,
               "departmentName": $sign05orgName,
               "departmentCode": $sign05jz,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,200]
      - contains: [content.message,"成功"]
      - eq: [ content.data, null ]

- test:
    name: "仅设置公章-save保存签署区设置"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params:
          filePreTaskId: $filePreTaskId1
          filePreTaskInfos:
            [{
               "sealInfos": [
                  {
                     "fileKey": $fileKey,
                     "signConfigs": [
                        {
                           "addSignDate": false,
                           "keywordInfo": null,
                           "pageNo": "1",
                           "posX": 220,
                           "posY": 728,
                           "edgeScope": null,
                           "sealSignDatePositionInfo": null,
                           "signPosName": "$userCode3-$sign05jz",
                           "signType": "COMMON-SIGN",
                           "signatureType": "COMMON-SEAL",
                           "allowMove": false,
                           "signFieldType": 0
                        }
                     ],
                     "sealIdList": [ ],
                     "signatureTypeList": [
                        "COMMON-SEAL",
                        "PERSON-SEAL",
                        "LEGAL-PERSON-SEAL"
                     ]
                  }
               ],
               "signerId": $orgCode1:$userCode3,
               "userType": 1,
               "userCode": $userCode3,
               "userName": $userName3,
               "signerType": 2,
               "legalSignFlag": 0,
               "userId": null,
               "organizationId": null,
               "organizationCode": $orgCode1,
               "organizationName": $orgName1,
               "departmentName": $sign05orgName,
               "departmentCode": $sign05jz,
               "sealTypeCode": null,
               "sealTypeName": null,
               "needShowSeal": 0,
               "personRealnameStatus": true,
               "orgRealnameStatus": true
            }]
    validate:
      - eq: [content.status,200]
      - contains: [content.message,"成功"]
      - eq: [ content.data, null ]
- test:
    name: case-删除组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $orgCode1
      customOrgNo: ""
    validate:
      - eq: [ json.code, 200 ]
      - eq: [ json.message, 成功 ]
      - eq: [ json.data, "" ]