-   config:
        name: "获取签署区配置页-参数校验"


-   test:
        name: "callbackUrl长度超过500-失败"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/333333333333333333333333/100/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/200/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/300/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/400/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/500//"
        validate:
            -   eq: [content.code, 1600017]
            -   eq: ["content.message","callbackUrl最大支持500字符"]
-   test:
        name: "callbackUrl长度为500字符-成功"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/333333333333333333333333/100/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/200/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/300/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/400/http://datafactory.smlk8s.esign.cn/simpleTools/notice/22222222/64/68/71/33333333333333333333333/500"
        validate:
            -   eq: [content.code, 200]
            -   eq: ["content.message","成功"]

-   test:
        name: "callbackUrl字段不传"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            callbackUrl: ''
        validate:
            -   eq: [content.code, 1600017]
            -   eq: [content.message,"callbackUrl为必填"]
            -   eq: [content.data, null]
-   test:
        name: "callbackUrl字段包含首尾空格"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            callbackUrl: '  http://datafactory.smlk8s.esign.cn/simpleTools/notice/  '
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]


-   test:
        name: "fileKeyList为空"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            fileKeyList: ''
        validate:
            -   eq: [content.code, 1600015]
            -   eq: [content.message,"fileKeyList参数错误!"]
            -   eq: [content.data, null]
-   test:
        name: "fileKey为不支持的文件类型"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signFileName: 'selfzip.zip'
        validate:
            -   eq: [content.code, 1623003]
            -   eq: [content.message,"zip文件类型不支持"]
            -   eq: [content.data, null]

-   test:
        name: "fileKey为不存在的文件"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            fileKeyList:
                -   fileKey: '1234'
        validate:
            -   eq: [content.code, 1623003]
            -   eq: [content.message,"1234文件不存在"]
            -   eq: [content.data, null]

-   test:
        name: "fileKeyList多文件包含异常文件"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            fileKeyList:
                -   fileKey: ${attachment_upload(selfzip.zip)}
                -   fileKey: ${attachment_upload(testppp.pdf)}
                -   fileKey: '1234'
        validate:
            -   eq: [content.code, 1623003]
            -   contains: [content.message, "文件类型不支持"]
            -   eq: [content.data, null]

-   test:
        name: "fileKeyList包含多个文件"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            fileKeyList:
                -   fileKey: ${attachment_upload(定位关键字.pdf)}
                -   fileKey: ${attachment_upload(testppp.pdf)}
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]


-   test:
        name: "signerList为空"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList: ''

        validate:
            -   eq: [content.code, 1600015]
            -   eq: [content.message,"signerList参数错误!"]
            -   eq: [content.data, null]

-   test:
        name: "userCode_未查询到匹配用户"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: sign011111
                    userType: 1
        validate:
            -   eq: [content.code, 1623007]
            -   eq: [content.message,"userCode:sign011111 对应用户不存在"]
            -   eq: [content.data, null]

-   test:
        name: "customAccountNo_未查询到匹配用户"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: 'sign011111'
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: ''
                    userType: 1

        validate:
            -   eq: [content.code, 1623008]
            -   eq: [content.message,"customAccountNo:sign011111 对应用户不存在"]
            -   eq: [content.data, null]
-   test:
        name: "departmentCode_未查到匹配机构"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: 'ORG-SIGN-0111111'
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623025]
            -   eq: [content.message,"departmentCode:ORG-SIGN-0111111 对应机构不存在"]
            -   eq: [content.data, null]
-   test:
        name: "customDepartmentNo_未查到匹配机构"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: 'ORG-SIGN-0111111'
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623026]
            -   eq: [content.message,"customDepartmentNo:ORG-SIGN-0111111 对应机构不存在"]
            -   eq: [content.data, null]

-   test:
        name: "organizationCode_未查到匹配机构"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: 'ORG-SIGN-0111111'
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623014]
            -   eq: [content.message,"organizeCode:ORG-SIGN-0111111 对应机构不存在"]
            -   eq: [content.data, null]

-   test:
        name: "customOrgNo_未查到匹配机构"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: 'ORG-SIGN-0111111'
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623015]
            -   eq: [content.message,"customOrgNo:ORG-SIGN-0111111 对应机构不存在"]
            -   eq: [content.data, null]

-   test:
        name: "sealTypeCode_sealId两者都有值但不匹配"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: 'ORG-SIGN-0111111'
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ${ENV(sign01.sealId)}
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                    sealTypeCode: "COMMON-SEAL"
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623015]
            -   eq: [content.message,"customOrgNo:ORG-SIGN-0111111 对应机构不存在"]
            -   eq: [content.data, null]

-   test:
        name: "签署人重复添加"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag:
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                        -   sealId: ${ENV(org01.sealId)}
                            signatureType: 'COMMON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

                -   customAccountNo: ${ENV(sign01.userCode)}
                    customDepartmentNo: ''
                    customOrgNo: ${ENV(sign01.main.orgNo)}
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ''
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(sign01.sealId)}
                            signatureType: 'PERSON-SEAL'
                        -   sealId: ${ENV(org01.sealId)}
                            signatureType: 'COMMON-SEAL'
                    sealTypeCode: ''
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 1623024]
            -   eq: [content.message,"签署方不允许重复"]
            -   eq: [content.data, null]

-   test:
        name: "创建签署配置，个人签署使用法人章，配置失败"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ''
                    customDepartmentNo: ''
                    customOrgNo: ''
                    departmentCode: ''
                    legalSignFlag: 1
                    organizationCode:
                    sealId: ''
                    sealIdList:
                        -   sealId: ${ENV(org01.legal.sealId)}
                            signatureType: 'LEGAL-PERSON-SEAL'
                    sealTypeCode:
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
#            -   eq: [content.code, 1623011]
#            -   contains: [content.message,"个人不支持指定法人签署区"]
#            -   str_eq: [content.data, None]
            -   eq: [content.code, 1623049]
            -   contains: [content.message,"个人不支持指定法人签署"]
            -   str_eq: [content.data, None]

-   test:
        name: "创建签署配置，内部企业指定企业印章类型，不指定印章id"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            signerList:
                -   customAccountNo: ''
                    customDepartmentNo: ''
                    customOrgNo: ''
                    departmentCode: ''
                    legalSignFlag: 0
                    organizationCode: ${ENV(sign01.main.orgCode)}
                    sealId:
                    sealIdList:
                    sealTypeCode: "COMMON-SEAL"
                    userCode: ${ENV(sign01.userCode)}
                    userType: 1

        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   len_gt: [content.data.filePreTaskId, 1]













