- config:
    name: "获取签署区配置页-不指定签署方"
    variables:
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "不指定"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]