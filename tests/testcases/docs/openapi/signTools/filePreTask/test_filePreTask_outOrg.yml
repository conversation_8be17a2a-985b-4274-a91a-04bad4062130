- config:
    name: "获取签署区配置页-相对方机构"
    variables:
      - outUserCode: ${ENV(wsignwb01.userCode)}
      - outOrgCode: ${ENV(wsignwb01.main.orgCode)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "不指定，开启法人"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $outUserCode,
          "departmentCode": "",
          "organizationCode": $outOrgCode,
          "legalSignFlag": 1,
          "sealId": "",
          "sealTypeCode": "",
          "userType": 2
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]

- test:
    name: "不指定，不开启法人"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $outUserCode,
          "departmentCode": "",
          "organizationCode": $outOrgCode,
          "legalSignFlag": 0,
          "sealId": "",
          "sealTypeCode": "",
          "userType": 2
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]

- test:
    name: "指定印章类型-public"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $outUserCode,
          "departmentCode": "",
          "organizationCode": $outOrgCode,
          "legalSignFlag": 0,
          "sealId": "",
          "sealTypeCode": "PUBLIC",
          "userType": 2
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]