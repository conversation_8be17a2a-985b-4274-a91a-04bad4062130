-   config:
        name: "获取签署配置（预盖章签署页面）"
        variables:
            pdfFileName: "测试文件.pdf"
            ofdFileName: "测试文件.ofd"
            pdfFileKey1: ${ENV(3PageFileKey)}
#            ofdFileKey1: ${attachment_upload($ofdFileName)}
#            pdfFileKey2: ${attachment_upload($pdfFileName)}
#            ofdFileKey2: ${attachment_upload($ofdFileName)}


-   test:
        name: "filePreTaskId不存在"
        api: api/esignDocs/openapi/signTools/filePreTaskDetail-api.yml
        variables:
            filePreTaskId: '123'
        validate:
            -   eq: [content.code, 1623001]
            -   eq: [content.message, "签署区设置id不存在"]
            -   str_eq: [content.data, None]

-   test:
        name: "获取pdf签署区配置页,获取taskId"
        api: api/esignDocs/openapi/signTools/filePreTask.yml
        variables:
            fileKey: $pdfFileKey1
        extract:
            -   preTaskId: content.data.filePreTaskId
        validate:
            -   eq: [content.code, 200]
            -   eq: [content.message,"成功"]
            -   ne: [content.data.filePreTaskId,""]


-   test:
        name: "保存pdf签署区配置"
        api: api/esignDocs/signTools/saveFilePreTaskDetail-api.yml
        variables:
            filePreTaskId: $preTaskId
            fileKey: $pdfFileKey1
            sealInfos:
              - fileKey: $fileKey
                signConfigs:
                  - addSignDate: false
                    keywordInfo: null
                    pageNo: '1-2'
                    posX: 62
                    posY: 779
                    edgeScope: null
                    sealSignDatePositionInfo: null
                    signPosName: 测试签署一-esigntest自动化签署中心CI测试
                    signType: COMMON-SIGN
                    signatureType: COMMON-SEAL
        validate:
            -   eq: [content.status, 200]
            -   eq: [content.success, True]
            -   eq: [content.message,"成功"]

-   test:
        name: "openapi接口获取pdf签署配置"
        api: api/esignDocs/openapi/signTools/filePreTaskDetail-api.yml
        variables:
            filePreTaskId: $preTaskId

        validate:
            -   eq: [content.message,"成功"]
            -   ne: [content.data.filePreURL, '']
            -   eq: [content.code, 200]



#-   test:
#        name: "获取ofd文件签署区配置页,获取taskId"
#        api: api/esignDocs/openapi/signTools/filePreTask.yml
#        variables:
#            fileKey: $ofdFileKey1
#        extract:
#            -   ofdPreTaskId: content.data.filePreTaskId
#        validate:
#            -   eq: [content.code, 200]
#            -   eq: [content.message,"成功"]
#            -   ne: [content.data.filePreTaskId,""]
#
#
#-   test:
#        name: "保存ofd文件签署区配置"
#        api: api/esignDocs/signTools/saveFilePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $ofdPreTaskId
#            fileKey: $ofdFileKey1
#        validate:
#            -   eq: [content.status, 200]
#            -   eq: [content.success, True]
#            -   eq: [content.message,"成功"]
#
#-   test:
#        name: "页面接口获取ofd文件签署配置"
#        api: api/esignDocs/signTools/filePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $ofdPreTaskId
#
#        validate:
#            -   eq: [content.message,"成功"]
#            -   ne: [content.data.docList, '']
#            -   eq: [content.status, 200]
#
#
#
#-   test:
#        name: "创建签署配置pdf，内部用户使用法人章+经办人章+企业章"
#        api: api/esignDocs/openapi/signTools/filePreTask.yml
#        variables:
#            fileKey: $pdfFileKey2
#            signerList:
#                -   customAccountNo: ''
#                    customDepartmentNo: ''
#                    customOrgNo: ''
#                    departmentCode: ''
#                    legalSignFlag: 1
#                    organizationCode: ${ENV(sign01.main.orgCode)}
#                    sealId: ''
#                    sealIdList:
#                        -   sealId: ${ENV(org01.legal.sealId)}
#                            signatureType: 'LEGAL-PERSON-SEAL'
#                        -   sealId: ${ENV(org01.sealId)}
#                            signatureType: 'COMMON-SEAL'
#                        -   sealId: ${ENV(sign01.cloud.SealId)}
#                            signatureType: 'PERSON-SEAL'
#                    sealTypeCode: ''
#                    userCode: ${ENV(sign01.userCode)}
#                    userType: 1
#        extract:
#            -   pdfPreTaskId2: content.data.filePreTaskId
#        validate:
#            -   eq: [content.code, 200]
#            -   eq: [content.message,"成功"]
#            -   len_gt: [content.data.filePreTaskId, 1]
#
#
#-   test:
#        name: "保存pdf文件签署区配置"
#        api: api/esignDocs/signTools/saveFilePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $pdfPreTaskId2
#            fileKey: $pdfFileKey2
#        validate:
#            -   eq: [content.status, 200]
#            -   eq: [content.success, True]
#            -   eq: [content.message,"成功"]
#
#
#-   test:
#        name: "页面接口获取pdf文件签署配置"
#        api: api/esignDocs/signTools/filePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $pdfPreTaskId2
#
#        validate:
#            -   eq: [content.message,"成功"]
#            -   ne: [content.data.docList, '']
#            -   eq: [content.status, 200]
#
#
#-   test:
#        name: "创建签署配置ofd，内部用户使用法人章+经办人章+企业章"
#        api: api/esignDocs/openapi/signTools/filePreTask.yml
#        variables:
#            fileKey: $ofdFileKey2
#            signerList:
#                -   customAccountNo: ''
#                    customDepartmentNo: ''
#                    customOrgNo: ''
#                    departmentCode: ''
#                    legalSignFlag: 1
#                    organizationCode: ${ENV(sign01.main.orgCode)}
#                    sealId: ''
#                    sealIdList:
#                        -   sealId: ${ENV(org01.legal.sealId)}
#                            signatureType: 'LEGAL-PERSON-SEAL'
#                        -   sealId: ${ENV(org01.sealId)}
#                            signatureType: 'COMMON-SEAL'
#                        -   sealId: ${ENV(sign01.cloud.SealId)}
#                            signatureType: 'PERSON-SEAL'
#                    sealTypeCode: ''
#                    userCode: ${ENV(sign01.userCode)}
#                    userType: 1
#        extract:
#            -   ofdPreTaskId2: content.data.filePreTaskId
#        validate:
#            -   eq: [content.code, 200]
#            -   eq: [content.message,"成功"]
#            -   len_gt: [content.data.filePreTaskId, 1]
#
#
#-   test:
#        name: "保存ofd文件签署区配置"
#        api: api/esignDocs/signTools/saveFilePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $ofdPreTaskId2
#            fileKey: $ofdFileKey2
#        validate:
#            -   eq: [content.status, 200]
#            -   eq: [content.success, True]
#            -   eq: [content.message,"成功"]
#
#
#-   test:
#        name: "页面接口获取ofd文件签署配置"
#        api: api/esignDocs/signTools/filePreTaskDetail-api.yml
#        variables:
#            filePreTaskId: $ofdPreTaskId2
#
#        validate:
#            -   eq: [content.message,"成功"]
#            -   ne: [content.data.docList, '']
#            -   eq: [content.status, 200]