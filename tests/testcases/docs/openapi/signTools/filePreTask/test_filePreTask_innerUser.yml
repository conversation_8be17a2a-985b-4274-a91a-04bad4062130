- config:
    name: "获取签署区配置页-内部个人"
    variables:
      - innerUserCode: ${ENV(sign01.userCode)}
      - innerOrgCode: ${ENV(sign01.main.orgNo)}
      - innersealCode: "COMMON-SEAL"
      - innerSealId: ${ENV(sign01.sealId)}
      - innerSealId2: ${ENV(org01.sealId)}
      - signPdfFileName: "testppp.pdf"
      - signPdfFileKey: ${attachment_upload($signPdfFileName)}

- test:
    name: "不指定"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $innerUserCode,
          "departmentCode": "",
          "organizationCode": "",
          "legalSignFlag": 0,
          "sealId": "",
          "sealTypeCode": "",
          "userType": 1
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]

- test:
    name: "指定印章"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $innerUserCode,
          "departmentCode": "",
          "organizationCode": "",
          "legalSignFlag": 0,
          "sealId": $innerSealId,
          "sealTypeCode": "",
          "userType": 1
        }
        ]
      }
    validate:
        -   eq: [content.code, 200]
        -   eq: [content.message,"成功"]
        -   len_gt: [content.data.filePreTaskId, 1]

- test:
    name: "指定印章不匹配"
    api: api/esignDocs/openapi/signTools/filePreTask.yml
    variables:
      json: {
        "callbackUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
        "fileKeyList": [
        {
          "fileKey": $signPdfFileKey
        }
        ],"signerList": [
        {
          "customAccountNo": "",
          "customDepartmentNo": "",
          "customOrgNo": "",
          "userCode": $innerUserCode,
          "departmentCode": "",
          "organizationCode": "",
          "legalSignFlag": 0,
          "sealId": $innerSealId2,
          "sealTypeCode": "",
          "userType": 1
        }
        ]
      }
    validate:
        -   eq: [content.code, 1623020]
        -   str_eq: [content.data, None]
        - contains: ["content.message","指定印章不存在"]