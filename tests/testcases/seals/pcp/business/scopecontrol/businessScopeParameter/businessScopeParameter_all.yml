- config:
    name: "获得业务范围控制参数   校验"
    base_url: ${ENV(esign.projectHost)}
    variables:
      accountNumber: ${ENV(userCode)}
      password: ${ENV(password_ceshy)}

- test:
    name: 获得业务范围控制参数
    api: api/esignSeals/pcp/business/scopecontrol/businessScopeParameter.yml
    extract:
      status: json.status
      message: json.message
      success: json.success
      businessScope: json.data.businessScope
      businessPhysicsSupplier: json.data.businessPhysicsSupplier
    validate:
      - eq: ["status_code", 200]
      - eq: [$status, 200]
      - eq: [$success, true]
      - contains: [$message, "成功"]
# 这个地方和配置相关，配置变更会导致这里报错
#      - eq: [$businessScope, "all"]
#      - eq: [$businessPhysicsSupplier, "qunje_physics"]
      - len_gt: [$businessScope, ""]
      - len_gt: [$businessPhysicsSupplier, ""]