- config:
    name: "个人印章-defaultSeal字段场景校验"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      password01: ${ENV(passwordEncrypt)}
      userCode01: ${ENV(userCodeNoSeal)}
      sealName01: 自动化测试个人默认印章1-（可删除）
      sealName02: 自动化测试个人默认印章2-（可删除）
      sealName03: 自动化测试个人默认印章3-（可删除）
      sealName04: 自动化测试个人默认印章4-（可删除）
      sealSourceName01: 自动化测试个人默认印章


##新增个人印章-个人无印章时，新增的印章默认为默认印章-国际标准
- test:
    name: TC1-openapi-新增个人国际城标准印章
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
      userCode: $userCode01
      sealName: $sealName01
      sealSourceName: $sealSourceName01
      sealPattern: 1
      defaultSeal:
    extract:
      personSealId01: content.data.sealInfos.0.sealId
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", '成功']
      - eq: ["content.data.sealInfos.0.userCode", $userCode01]

#- test:
#    name: TC5-openqapi查询个人印章-个人无印章时，新增的印章默认为默认印章
#    api: api/esignSeals/v1/userseals/list.yml
#    variables:
#      userCode: $userCode01
#    extract:
#      - sealIdPerson: content.data.records.0.sealId
#    validate:
#      - eq: [ content.message,成功 ]
#      - eq: [ content.code,200 ]
#      - ge: [ content.data.total, 1 ]

- test:
    name: webapi-查询个人印章1-个人无印章时，新增的印章默认为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: TC1-openapi-修改个人印章1，default=0-发布状态无法更新印章
    api: api/esignSeals/v1/sealcontrols/userseals/update.yml
    variables:
      sealId: $personSealId01
      defaultSeal: 0
    validate:
      - eq: [ "content.code", 1325039]
      - eq: [ "content.message", "个人印章仅停用状态支持编辑"]
      - eq: ["content.data", null]

- test:
    name: TC1-openapi-更新个人印章1为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId01
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId01]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-openapi-修改个人印章1，default=0-无效
    api: api/esignSeals/v1/sealcontrols/userseals/update.yml
    variables:
      sealId: $personSealId01
      defaultSeal: 0
    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.sealId", $personSealId01 ]

- test:
    name: TC1-openapi-更新个人印章1为发布状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId01
      sealStatus: 1  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId01]
      - eq: ["content.data.sealStatus", "1"]

- test:
    name: webapi查询个人印章1-仍为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: TC1-webapi-停用个人印章1-停用成功
    api: api/esignSeals/seals/personal/stopPersonalSeal.yml
    variables:
      id: $personSealId01
      accountNumber: $userCode01
    validate:
      - eq: [ "content.status", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data", $personSealId01]

- test:
    name: webapi查询个人印章1-为停用态
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.sealStatus, "h"]

- test:
    name: TC1-webapi-发布个人印章1
    api: api/esignSeals/seals/personal/publishPersonalSeal.yml
    variables:
      id: $personSealId01
      accountNumber: $userCode01
    validate:
      - eq: [ "content.status", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data", $personSealId01]

- test:
    name: webapi-查询个人印章1-仍为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]
      - eq: [ content.data.list.0.sealStatus, "g"]

- test:
    name: TC1-openapi-再次新增个人印章2-defaultSeal=0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
      userCode: $userCode01
      sealName: $sealName02
      sealSourceName: $sealSourceName01
      sealPattern: 1
      defaultSeal: 0
    extract:
      personSealId02: content.data.sealInfos.0.sealId
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", '成功']
      - eq: ["content.data.sealInfos.0.userCode", $userCode01]

- test:
    name: webapi-查询个人印章2-defaultSeal=0-不是默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId02
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, false]

- test:
    name: webapi-查询个人印章1-仍为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: TC1-openapi-再次新增个人印章3-defaultSeal=1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
      userCode: $userCode01
      sealName: $sealName03
      sealSourceName: $sealSourceName01
      sealPattern: 1
      defaultSeal: 1
    extract:
      personSealId03: content.data.sealInfos.0.sealId
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", '成功']
      - eq: ["content.data.sealInfos.0.userCode", $userCode01]

- test:
    name: webapi-查询个人印章3-defaultSeal=1-是默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId03
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: webapi-查询个人印章1-不为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, false]


- test:
    name: TC1-webapi-停用个人印章3-停用失败
    api: api/esignSeals/seals/personal/stopPersonalSeal.yml
    variables:
      id: $personSealId03
      accountNumber: $userCode01
    validate:
      - eq: [ "content.status", 1904039]
      - eq: [ "content.message", "请将该所有人下另一电子章设为默认章后，进行停用"]
      - eq: ["content.success", false]
      - eq: ["content.data", null]

#todo 有Bug
- test:
    name: TC1-openapi-更新个人印章3为停用状态-个人不是仅有一个印章时，无法将默认印章停用-有bug
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 1325039]
      - contains: [ "content.message", "失败"]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-openapi-更新个人印章3为发布-bug修复后删除
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 1  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealStatus", "1"]

- test:
    name: TC1-webapi-将印章1设为，默认印章。设置成功
    api: api/esignSeals/seals/personal/setDefault.yml
    variables:
      setDefaultSealId: $personSealId01
      authorization0: ${getPortalToken($userCode01, $password01)}
    validate:
      - eq: [ "content.status", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.success", true]
      - eq: ["content.data", $personSealId01]

- test:
    name: webapi-查询个人印章1-为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: webapi-查询个人印章3-不为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId03
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, false]

- test:
    name: TC1-更新个人印章3为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-修改个人印章3，default=1
    api: api/esignSeals/v1/sealcontrols/userseals/update.yml
    variables:
      sealId: $personSealId03
      defaultSeal: 1
    validate:
      - eq: [ "content.code", 200 ]
      - contains: [ "content.message", "成功" ]
      - eq: [ "content.data.sealId", $personSealId03 ]

- test:
    name: TC1-更新个人印章3为发布状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 1  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]
      - eq: ["content.data.sealStatus", "1"]


#todo 有bug
- test:
    name: webapi查询个人印章3-defaultSeal=1-是默认印章-有Bug
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId03
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: webapi查询个人印章1-不为默认印章-有Bug
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId01
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, false]

- test:
    name: TC1-更新个人印章1为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId01
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId01]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-更新个人印章2为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId02
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId02]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-更新个人印章3为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-删除个人印章1
    api: api/esignSeals/v1/userseals/delete.yml
    variables:
      sealId: $personSealId01
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId01]

- test:
    name: TC1-删除个人印章2
    api: api/esignSeals/v1/userseals/delete.yml
    variables:
      sealId: $personSealId02
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId02]

- test:
    name: TC1-openapi-新增个人国际城标准印章4-defaultSeal=0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
      userCode: $userCode01
      sealName: $sealName04
      sealSourceName: $sealSourceName01
      sealPattern: 1
      defaultSeal: 0
    extract:
      personSealId04: content.data.sealInfos.0.sealId
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", '成功']
      - eq: ["content.data.sealInfos.0.userCode", $userCode01]

- test:
    name: webapi查询个人印章4-为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId04
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: TC1-更新个人印章3为发布状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 1  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]
      - eq: ["content.data.sealStatus", "1"]

- test:
    name: webapi查询个人印章4-为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId04
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, true]

- test:
    name: webapi查询个人印章3-不为默认印章
    api: api/esignSeals/seals/personal/pagePersonalSealList.yml
    variables:
      authorization0: ${getPortalToken($userCode01, $password01)}
      sealName:
      currPage: 1
      pageSize: 10
      sealStatus:
      sealId: $personSealId03
    validate:
      - eq: [ content.status, 200]
      - eq: [content.success, true]
      - contains: [content.message,"成功"]
      - eq: [ content.data.list.0.isDefault, false]

- test:
    name: TC1-更新个人印章3为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId03
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-更新个人印章4为停用状态
    api: api/esignSeals/v1/userseals/updateStatus.yml
    variables:
      sealId: $personSealId04
      sealStatus: 2  #1-已发布 2-已停用
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId04]
      - eq: ["content.data.sealStatus", "2"]

- test:
    name: TC1-删除个人印章3
    api: api/esignSeals/v1/userseals/delete.yml
    variables:
      sealId: $personSealId03
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId03]

- test:
    name: TC1-删除个人印章4
    api: api/esignSeals/v1/userseals/delete.yml
    variables:
      sealId: $personSealId04
    validate:
      - eq: [ "content.code", 200]
      - contains: [ "content.message", "成功"]
      - eq: ["content.data.sealId", $personSealId04]
