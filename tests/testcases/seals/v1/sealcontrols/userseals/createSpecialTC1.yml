- config:
    name: "新建个人印章-中国标准-校验制章者证书取主责企业创建时间最早"
    variables:
        userCode01: ${ENV(sign01.userCode)}
        customAccountNo01: ${ENV(sign01.accountNo)}
        mainOrgCode: ${ENV(sign01.main.orgCode)}
        otherOrgCode: ${ENV(sign01.JZ.orgCode)}

#1、新增兼职企业SM2印章
- test:
    name: case1-创建企业SM2云证书-兼职企业
    api: api/esignSeals/v1/sealcontrols/organizationCerts/create.yml
    variables:
      json:
        algorithm: 2 #SM2证书
        organizationCode: $otherOrgCode
    extract:
      orgCertId01: content.data.certId
    validate:
      - eq: [content.code,200]
      - contains: [content.message,"成功"]
      - eq: [content.data.organizationCode,$otherOrgCode]
#2、查询主职企业创建时间最早的SM2证书
- test:
    name: case2-查询机构所有生效的云证书-主职
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        json:
          {
            "algorithm": "2",
            "certPattern": "1",
            "certSN": "",
            "customOrgNo": "",
            "keySNList": [ ],
            "organizationCode": "$mainOrgCode"
          }
    extract:
        orgCertId02: content.data.0.certId
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]
#3、创建个人SM2证书
- test:
    name: case3-创建个人SM2证书
    api: api/esignSeals/v1/sealcontrols/userCerts/create.yml
    variables:
        json:
          {
              userCode: $userCode01,
              customAccountNo: "",
              algorithm: "2",
              certName: ""
          }
    extract:
        personCertId01: content.data.certId
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]
#4、新增个人中国标准印章
- test:
    name: case4-新增个人中国标准印章
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        json:
          {
              "customAccountNo": "",
              "description": "",
              "sealInfos": [
                  {
                      "base64img": "",
                      "defaultSeal": "",
                      "sealColour": 4,
                      "sealFileKey": "",
                      "sealHeight": "",
                      "sealMakerCertId": "$orgCertId02",
                      "sealOldStyle": "0",
                      "sealOpacity": 20,
                      "sealPattern": "3",
                      "sealSource": "2",
                      "sealTemplateStyle": "1",
                      "sealWidth": "",
                      "signerCertInfos": [
                          {
                              "sealSignercertId": "$personCertId01"
                          }
                      ]
                  }
              ],
              "sealName": "自动化测试国密印章",
              "sealSourceName": "自动化测试国密印章",
              "userCode": "$userCode01"
          }
    extract:
      personSealId01: content.data.sealInfos.0.sealId
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: ["content.data.sealInfos.0.userCode", $userCode01]

#
- test:
    name: case5-查看个人印章-核对制章者证书为主职企业的第一本SM2证书，
    api: api/esignSeals/seals/personal/ownerManager/getPersonalSeal.yml
    variables:
        sealId: $personSealId01
    validate:
        - eq: [ "content.status", 200]
        - contains: [ "content.message", '成功']
        - eq: ["content.data.sealId", $personSealId01]
        - eq: ["content.data.asn1SealInfoVO.makeSealCertId", $orgCertId02]
        - eq: ["content.data.ownerOrganizationCode", $mainOrgCode]
        - eq: ["content.data.asn1SealInfoVO.signSealCertIds.0", $personCertId01]


- test:
    name: case6-更新个人印章为已停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $personSealId01
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - eq: [ "content.message", '成功']
        - eq: [ "content.data.sealId", $personSealId01]
        - eq: [ "content.data.sealStatus", "2"]

- test:
    name: case7-删除个人印章
    api: api/esignSeals/v1/sealcontrols/userseals/delete.yml
    variables:
        sealId: $personSealId01
    validate:
        - eq: [ "content.code", 200]
        - eq: [ "content.message", '成功']
        - eq: [ "content.data.sealId", $personSealId01]

- test:
    name: case8-吊销个人证书
    api: api/esignSeals/v1/sealcontrols/userCerts/certRevoke.yml
    variables:
      - certId: $personCertId01
    validate:
      - contains: [ "content.message", '成功' ]
      - eq: ["content.code", 200]
      - eq: [content.data, null ]


- test:
    name: case9-吊销企业证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/certRevoke.yml
    variables:
      - certId: $orgCertId01
    validate:
      - contains: [ "content.message", '成功' ]
      - eq: ["content.code", 200]
      - eq: [content.data, null ]

