- config:
    name: 批量查询用户的生效个人印章列表
    variables:
      userCode001: ${ENV(userCode)}
      customAccountNo001: ${ENV(customAccountNo)}
      customAccountNo002: ${ENV(customAccountNo1)}
      userCodeMiddleSpace: ${str_insert_space($userCode001, 2)}
      customAccountNoMiddleSpace: ${str_insert_space($customAccountNo001, 2)}
      orgCode001: ${ENV(csqs.orgCode)}
      orgNo001: ${ENV(csqs.orgNo)}
      SP: "  "
      userNo0: "testFill0011"
      userName0: "测试填写用户印章"
      sealName0: "测试用户批量印章名称123"
      sign01UserCode: ${ENV(sign01.userCode)}
      jpgFile: data/psb.jpg
      base64file: ${get_file_base64($jpgFile)}
      keySNSM: "FFD0FFDD63B17EC8"  #sign01.ukey.business.SealId
      keySNGM: "2C5157B9F07D8175"   #sign01.ukey.guomi.SealId
      pix: ${generate_random_str(12)}
      userCode0: ${ENV(userCode)}
      orgCode0: ${ENV(userCode)}

#pageNo异常校验
- test:
    name: 报错-pageNo传空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       pageNo: " "
       pageSize: 10
       userInfos: [
         { userCode: $userCode001 }
       ]
    validate:
      - eq: ["status_code", 200]
      - eq: [ json.code, 1300000 ]
      - eq: [ json.message,"当前页数不能为空" ]

- test:
    name: 报错-pageNo传0
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 0
      pageSize: 10
      userInfos: [
         { userCode: $userCode001 }
       ]
    validate:
      - eq: ["status_code", 200]
      - eq: [ json.code, 1300000 ]
      - eq: [ json.message,"页号不能小于1" ]

- test:
    name: 报错-pageNo传负数
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: -2
      pageSize: 10
      userInfos: [
         { userCode: $userCode001 }
       ]
    validate:
      - eq: ["status_code", 200]
      - eq: [ json.code, 1300000 ]
      - eq: [ json.message,"页号不能小于1" ]

#pageSize异常校验
- test:
    name: 报错-pageSize传空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: " "
      userInfos: [
         { userCode: $userCode001 }
       ]
    validate:
      - eq: ["status_code", 200]
      - eq: [ json.code, 1300000 ]
      - eq: [ json.message,"每页记录数不能为空" ]

- test:
    name: 报错-pageSize传0
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: 0
      userInfos: [
         { userCode: $userCode001 }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"每页记录数不能小于1" ]


- test:
    name: 报错-pageSize传负数
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: -5
      userInfos: [
         { userCode: $userCode001 }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"每页记录数不能小于1" ]

- test:
    name: 报错-pageSize传51
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: 51
      userInfos: [
         { userCode: $userCode001 }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"每页记录数不能大于50" ]


# userInfos异常校验
- test:
    name: 报错-userInfos传空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: 10
      userInfos: []
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"用户信息列表不能为空" ]


- test:
    name: 报错，输入ukey，userCode或customAccountNo为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ {
                     keySNList: [
                      "$keySNSM"
                     ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1913012 ]
      - eq: [ $message,"用户编码与用户账号必填其一" ]


- test:
    name: 报错-userCode、customAccountNo同时传空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: 10
      userInfos: [
         { userCode: "",customAccountNo: ""  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1913012 ]
      - eq: [ $message,"用户编码与用户账号必填其一" ]

- test:
    name: 报错 - userCode传参为null，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      pageNo: 1
      pageSize: 10
      userInfos: [
         { userCode: null, customAccountNo: ""  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1913012]
      - eq: [$message, "用户编码与用户账号必填其一"]

- test:
    name: 报错-userCode传一个已经被删除的用户
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       pageNo: 1
       pageSize: 10
       userInfos: [ { userCode: "${ENV(deletedUserCode)}",customAccountNo: ""  }  ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1111003]
      - eq: [ $message,"用户已不存在" ]

- test:
    name: 报错-userCode传一个已经被注销的用户
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       userInfos: [
         { userCode: "${ENV(loggedOutUserCode)}",customAccountNo: ""  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1111003]
      - eq: [ $message,"用户已不存在" ]





- test:
    name: 报错-userCode为外部用户编码，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       userInfos: [
         { userCode: "${ENV(externalUserCode)}",customAccountNo: ""  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312107]
      - eq: [$message, "用户非内部用户"]

- test:
    name: 报错-userCode为不存在的用户编码，customAccountNo为存在内部用户账号
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [
        { userCode: "qianfengbucunzai",customAccountNo: "${ENV(customAccountNo)}" }
      ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1111003]
      - eq: [$message, "用户已不存在"]

- test:
    name: 报错-userCode传任意的34位字符串
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [
        { userCode: "0123456789012345678901234567890123",customAccountNo: "" }
      ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1111003]
      - eq: [$message, "用户已不存在"]



- test:
    name: 报错-userCode传任意的35位字符串
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [
        { userCode: "012345678901234567890123456789012123",customAccountNo: "" }
      ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"用户编码不能超过34个字符"]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的\
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{userCode: "code\\"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的/
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{userCode: "code/"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的：
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{userCode: "code:"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的*
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       userInfos: [{userCode: "code*"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的?
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{userCode: "code?"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的"
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{userCode: "code\""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]

#- test:
#    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的<
#    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
#    variables:
#      userInfos:  [{userCode: "code<"}]
#    extract:
#      code: json.code
#      message: json.message
#    validate:
#      - eq: ["status_code", 200]
#      - eq: [ $code, 1300000 ]
#      - eq: [ $message,"userCode不能包含特殊字符" ]

#- test:
#    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的>
#    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
#    variables:
#      userInfos:  [{userCode: "code>"}]
#    extract:
#      code: json.code
#      message: json.message
#    validate:
#      - eq: ["status_code", 200]
#      - eq: [ $code, 1300000 ]
#      - eq: [ $message,"userCode不能包含特殊字符" ]

- test:
    name: 报错-userCode中存在特殊字符\/:*?\"<>|中的|
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos:  [{userCode: "code|"}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1300000 ]
      - eq: [ $message,"userCode不能包含特殊字符" ]


- test:
    name: 报错-userCode中存在中间空格
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos:  [{userCode: $userCodeMiddleSpace}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [ $code, 1111003 ]
      - eq: [ $message,"用户已不存在" ]




- test:
    name: 报错userCode传参长度超过34个字符，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "012345678901234567890123456789012345678901234567890123" ,customAccountNo: ""} ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "用户编码不能超过34个字符"]




- test:
    name: 报错-customAccountNo传参为不存在的用户账号， userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "********"} ]

    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312104]
      - eq: [$message, "用户不存在或用户为非在职状态"]

- test:
    name: 报错-customAccountNo传参为外部的用户账号，userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "${ENV(externalCustomAccountNo)}" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312104]
      - eq: [$message, "用户不存在或用户为非在职状态"]

- test:
    name: 报错-customAccountNo传参为删除的用户账号， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "${ENV(deletedcustomAccountNo)}" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312104]
      - eq: [$message, "用户不存在或用户为非在职状态"]


- test:
    name: 报错-customAccountNo传参长度超过34个字符， userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "012345678901234567890123456789012345678901234567890123"} ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "用户账号不能超过34个字符"]


- test:
    name: 报错，customAccountNo 传参内部账号中有空格， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "$customAccountNoMiddleSpace" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312104]
      - eq: [$message, "用户不存在或用户为非在职状态"]

- test:
    name: 报错-customAccountNo传参存在特殊字符\， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no\\" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]

- test:
    name: 报错-customAccountNo传参存在特殊字符/， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no/" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]

- test:
    name: 报错-customAccountNo传参存在特殊字符:， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no:" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]

- test:
    name: 报错-customAccountNo传参存在特殊字符*， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no*" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]

- test:
    name: 报错-customAccountNo传参存在特殊字符?， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no?" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]


#- test:
#    name: 报错-customAccountNo传参存在特殊字符<， userCode 传参为空
#    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
#    variables:
#      userInfos: [ { userCode: "" ,customAccountNo: "no<" } ]
#    extract:
#      code: json.code
#      message: json.message
#    validate:
#      - eq: ["status_code", 200]
#      - eq: [$code, 1300000]
#      - eq: [$message, "customAccountNo不能包含特殊字符"]



#- test:
#    name: 报错-customAccountNo传参存在特殊字符>， userCode 传参为空
#    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
#    variables:
#      userInfos: [ { userCode: "" ,customAccountNo: "no>" } ]
#    extract:
#      code: json.code
#      message: json.message
#    validate:
#      - eq: ["status_code", 200]
#      - eq: [$code, 1300000]
#      - eq: [$message, "customAccountNo不能包含特殊字符"]



- test:
    name: 报错-customAccountNo传参存在特殊字符|， userCode 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: "no|" } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "customAccountNo不能包含特殊字符"]




- test:
    name: 报错-userInfos超过200个用户
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },
                  { userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  },{ userCode: "123"  } ]

    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1300000]
      - eq: [$message, "用户信息列表最多不能超过200个"]


#####正常用例
- test:
    name: 正常用例-pageNo传1,pageSize传50
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode001,  customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [$code, 200 ]

- test:
    name: 正常用例-userCode传参为内部用户编码，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode001,  customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [$code, 200 ]

- test:
    name: 正常用例-userCode传参为内部用户编码前置空格，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $SP$userCode001,  customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [$code, 200 ]

- test:
    name: 正常用例-userCode传参为内部用户编码后置空格，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode001$SP,  customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]

- test:
    name: 正常用例-userCode传参为内部用户编码前后置空格，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $SP$userCode001$SP,  customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]

- test:
    name: 正常-customAccountNo传参为存在的内部用户账号， userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: $customAccountNo001} ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200]
      - len_gt: [$message, 0]

- test:
    name: 正常用例-customAccountNo传参内部账号前置空格，userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
        userInfos: [ { userCode: "" ,customAccountNo: $SP$customAccountNo001} ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200]
      - len_gt: [$message, 0]

- test:
    name: 正常用例-customAccountNo传参内部账号后置空格， userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: $customAccountNo001$SP} ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200]
      - len_gt: [$message, 0]

- test:
    name: 正常用例-customAccountNo传参内部账号前后空格， userCode传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "" ,customAccountNo: $SP$customAccountNo001$SP } ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200]
      - len_gt: [$message, 0]

- test:
    name: 正常用例-userCode传参为存在的内部用户编码，customAccountNo 传参一致
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: $userCode001 ,customAccountNo: $customAccountNo001 }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]



- test:
    name: setup1-创建内部用户
    api: api/esignManage/InnerUsers/create.yml
    variables:
        customAccountNo: $userNo0
        name: $userName0
        mobile: ""
        email: "<EMAIL>"
        licenseType: ID_CARD
        licenseNo: ""
        bankCardNo:
        mainOrganizationCode: $orgCode001
        mainCustomOrgNo:
        otherOrganization: []
    extract:
      - userCode0: content.data.successData.0.userCode
      - userAccountNo0: content.data.successData.0.customAccountNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - eq: [content.data.successCount, 1]

- test:
    name: setup-组织名称查询
    variables:
      data: {
        "params": {
          "organizationName": "esigntest自动化CI测试",
          "organizationTerritory": "1" },
        "domain": "admin_platform"
      }
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]
    extract:
      - _organizationId: json.data.0.id

- test:
    name: setup-通过组织id获取用户列表分页
    variables:
      data: {
        "params": {
          "organizationId": "$_organizationId",
          "currPage": 1,
          "pageSize": 10,
          "searchType": "1",
          "accountNumber": $userAccountNo0,
          "allChildOrganizationFlag": true,
          "userStatusList": [ "1" ] },
        "domain": "admin_platform" }
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    validate:
      - eq: [ status_code,200 ]
      - eq: [ json.status,200 ]
      - eq: [ json.data.list.0.accountNumber,$userAccountNo0 ]
      - eq: [ json.data.list.0.userCode,$userCode0 ]
    extract:
      - _userid: json.data.list.0.id
      - _userCode: json.data.list.0.userCode
      - _accountNumber: json.data.list.0.accountNumber



- test:
    name: setup2-新建个人印章-云国际标准
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode0
        sealName: $sealName0
        sealPattern: "1"
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode0 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]
    extract:
        - sealId0: content.data.sealInfos.0.sealId
    teardown_hooks:
        - ${sleep(10)}

- test:
    name: TC-查询userCode0的生效印章有一个
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: $userCode0 ,customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 1 ]
      - eq: [content.data.records.0.sealId, $sealId0]
      - eq: [content.data.records.0.sealName, $sealName0]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]


- test:
    name: TC-新建印章-上传本地印章
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode0
        sealName: "本地$sealName0"
        sealSource: 1
        base64img: $base64file
        sealFileKey: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode0 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
    extract:
        - sealId01: content.data.sealInfos.0.sealId

- test:
    name: TC-查询userCode0的生效印章有2个
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: $userCode0 ,customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 2 ]
      - eq: [content.data.records.0.sealId, $sealId01]
      - eq: [content.data.records.0.sealName, "本地$sealName0"]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]
      - eq: [content.data.records.1.sealId, $sealId0]
      - eq: [content.data.records.1.sealName, $sealName0]
      - contains: [content.data.records.1.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.1.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.1.keySN, ""]
      - eq: [content.data.records.1.sealSN, ""]
      - eq: [content.data.records.1.userCode, $userCode0]
      - eq: [content.data.records.1.customAccountNo, $userNo0]
      - ne: [content.data.records.1.userName, ""]

- test:
    name: TC-批量查询生效印章-ukey印章输入为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [
        { userCode: "$userCode0" ,
                     keySNList: [ ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 2 ]
      - eq: [content.data.records.0.sealId, $sealId01]
      - eq: [content.data.records.0.sealName, "本地$sealName0"]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]
      - eq: [content.data.records.1.sealId, $sealId0]
      - eq: [content.data.records.1.sealName, $sealName0]
      - contains: [content.data.records.1.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.1.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.1.keySN, ""]
      - eq: [content.data.records.1.sealSN, ""]
      - eq: [content.data.records.1.userCode, $userCode0]
      - eq: [content.data.records.1.customAccountNo, $userNo0]
      - ne: [content.data.records.1.userName, ""]


- test:
    name: 正常用例-userCode传参为存在的内部用户编码，customAccountNo传参不一致,以userCode为准
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: $userCode0 ,customAccountNo: $customAccountNo002 }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 2 ]
      - eq: [content.data.records.0.sealId, $sealId01]
      - eq: [content.data.records.0.sealName, "本地$sealName0"]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]

- test:
    name: TC-查询内部用户
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        userCode: $userCode0
    extract:
      - _userCode0: content.data.0.userCode
      - _userName0: content.data.0.name
      - _userNo0: content.data.0.customAccountNo
      - _orgName0: content.data.0.mainCustomOrgName
      - _orgNo0: content.data.0.mainCustomOrgNo
      - _orgCode0: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - eq: [ content.data.0.customAccountNo, "$_userNo0" ]
      - gt: [ content.data.0.name, "1" ]


- test:
    name: 草稿
    api: api/esignSeals/seals/personal/ownerManager/savePersonalSeal.yml
    variables:
        "id":
        "managerOrgCode": $_orgCode0
        "managerOrgName": $_orgName0
        "sealAngle": 0
        "sealColour": "1"
        "sealDefinition": 20
        "sealDesc": ""
        "sealHeight": 10
        "sealName": "草稿$sealName0"
        "sealBodyStructure": "1"
        "sealOpacity": 0.6
        "sealScale": 0.5
        "sealShape": "1"
        "sealSource": "2"
        "sealStatus": "1"
        "sealThumbnailUrl":
        "sealUserName":  ${generate_random_str(10)}
        "sealWidth": 20
        "ownerCode": $_userCode0
        "ownerName": $_userName0
        "ownerOrganizationName": $_orgName0
        "ownerOrganizationCode": $_orgCode0
        "customSealType": ""
        "handPaintedSealId": ""
        "stampRule": "2"
        "oldStyle": "2"
        "sealDefinitionType": "1"
        "sealWidthPixels": "100"
        "sealHeightPixels": "100"
    extract:
        sealId02: content.data
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - len_gt: [ content.data, 1 ]
      - contains: [ content.message,"成功" ]

- test:
    name: TC-查询userCode0的生效印章仍有2个，草稿印章不查询
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: $userCode0 ,customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 2 ]
      - eq: [content.data.records.0.sealId, $sealId01]
      - eq: [content.data.records.0.sealName, "本地$sealName0"]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]
      - eq: [content.data.records.1.sealId, $sealId0]
      - eq: [content.data.records.1.sealName, $sealName0]
      - contains: [content.data.records.1.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.1.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.1.keySN, ""]
      - eq: [content.data.records.1.sealSN, ""]
      - eq: [content.data.records.1.userCode, $userCode0]
      - eq: [content.data.records.1.customAccountNo, $userNo0]
      - ne: [content.data.records.1.userName, ""]


- test:
    name: setup-创建个人SM2证书
    api: api/esignSeals/v1/sealcontrols/userCerts/create.yml
    variables:
        json:
          {
              userCode: $userCode001,
              customAccountNo: "",
              algorithm: "2",
              certName: ""
          }
    extract:
        personCertId01: content.data.certId
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: setup-新建个人印章-云中国标准
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
      json:
        {
          "customAccountNo": "",
          "description": "",
          "sealInfos": [
            {
              "base64img": "",
              "defaultSeal": "",
              "sealColour": 4,
              "sealFileKey": "",
              "sealHeight": "",
              "sealMakerCertId": "",
              "sealOldStyle": "0",
              "sealOpacity": 20,
              "sealPattern": "3",
              "sealSource": "2",
              "sealTemplateStyle": "1",
              "sealWidth": "",
              "signerCertInfos": [
                {
                  "sealSignercertId": "$personCertId01"
                }
              ]
            }
          ],
          "sealName": "国密$sealName0",
          "sealSourceName": "自动化测试国密印章",
          "userCode": "$userCode001"
        }
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode001 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]
    extract:
        - sealId1: content.data.sealInfos.0.sealId



- test:
    name: TC-查询userCode001的生效印章
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode001, customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - ge: [content.data.total, 2 ]
      - eq: [content.data.records.0.sealId, $sealId1]
      - eq: [content.data.records.0.sealName, "国密$sealName0"]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode001]
      - eq: [content.data.records.0.customAccountNo, $customAccountNo001]
      - ne: [content.data.records.0.userName, ""]




- test:
    name: TC-查询多人生效印章
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode0, customAccountNo: ""},{userCode: $userCode001},{customAccountNo: $customAccountNo002}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - gt: [content.data.total, 1 ]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, ""]
      - eq: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $userCode0]
      - eq: [content.data.records.0.customAccountNo, $userNo0]
      - ne: [content.data.records.0.userName, ""]


- test:
    name: setup-更新个人印章1发布到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $sealId0
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']


- test:
    name: setup-更新个人印章2发布到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $sealId1
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

- test:
    name: setup-更新个人印章3发布到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $sealId01
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

- test:
    name: TC-查询userCode0的生效印章有0个
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode0, customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 0 ]


- test:
    name: teardown-删除个人印章1
    api: api/esignSeals/v1/sealcontrols/userseals/delete.yml
    variables:
        sealId: $sealId0
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

- test:
    name: teardown-删除个人印章2
    api: api/esignSeals/v1/sealcontrols/userseals/delete.yml
    variables:
        sealId: $sealId1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']


- test:
    name: teardown-删除个人印章3
    api: api/esignSeals/v1/sealcontrols/userseals/delete.yml
    variables:
        sealId: $sealId01
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

- test:
    name: deletePersonalSeal-删除草稿印章
    api: api/esignSeals/seals/personal/ownerManager/deletePersonalSeal.yml
    variables:
      "id": $sealId02
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - len_gt: [ content.data, 1 ]
      - contains: [ content.message,"成功" ]

- test:
    name: TC-查询userCode0的生效印章有0个
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode0, customAccountNo: ""}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - eq: [content.data.total, 0 ]

- test:
    name: setup-离职用户$userCode0
    variables:
      handoverDate: ${get_date(0)}
      handoverDesc: "测试列表"
      handoverStatus: 2
      needToResign: 2
      userDimissionId: $_userid
      userReceiveId: ""
    api: api/esignManage/OrgUser/dimission/addUserDimission.yml
    validate:
      - eq: [ json.status,200 ]
      - eq: [ json.message,"成功" ]


- test:
    name: 报错-userCode为离职的用户编码，customAccountNo 传参为空
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       userInfos: [
         { userCode: "$userCode0", customAccountNo: ""  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1111003]
      - eq: [$message, "用户已不存在"]


- test:
    name: 报错-userCode为空，customAccountNo为离职的用户账号
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
       userInfos: [
         { userCode: "", customAccountNo: "$_accountNumber"  }
       ]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1312104]
      - eq: [$message, "用户不存在或用户为非在职状态"]



- test:
    name: TC-查询多人生效印章-有一个用户不存在，报错
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [{ userCode: $userCode0, customAccountNo: ""},{customAccountNo: $customAccountNo002}]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 1111003 ]
      - eq: [$message, 用户已不存在]
      - eq: [content.data, null]

- test:
    name: TC-批量查询生效印章-ukey印章输入一个不存在的keySN
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      sign01UserCode: ${ENV{sign01.userCode}}
      userInfos: [ { userCode: "$sign01UserCode" ,
                     keySNList: [   "8522F7F383AA"   ]
                }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: [ "status_code", 200 ]
      - eq: [ $code, 200 ]
      - eq: [ $message, 成功 ]
      - eq: [ content.data.total, 0 ]
      - eq: [ content.data.records, [] ]

- test:
    name: TC-批量查询生效印章-ukey印章输入存在的商密keySN
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "$sign01UserCode" ,
                     keySNList: [
                       "$keySNSM"
                     ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - ge: [content.data.total, 1 ]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, $keySNSM]
      - ne: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $sign01UserCode]
      - eq: [content.data.records.0.customAccountNo, $sign01UserCode]
      - eq: [content.data.records.0.userName, "测试签署一"]



- test:
    name: TC-批量查询生效印章-ukey印章输入存在的商密keySN+不存在的keySN
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "$sign01UserCode" ,
                     keySNList: [
                       "$keySNSM","123AA"
                     ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - ge: [content.data.total, 1 ]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, $keySNSM]
      - ne: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $sign01UserCode]
      - eq: [content.data.records.0.customAccountNo, $sign01UserCode]
      - eq: [content.data.records.0.userName, "测试签署一"]


- test:
    name: TC-批量查询生效印章-ukey印章输入存在的国密keySN
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "$sign01UserCode" ,
                     keySNList: [
                       "$keySNGM"
                     ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - ge: [content.data.total, 1 ]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, $keySNGM]
      - ne: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $sign01UserCode]
      - eq: [content.data.records.0.customAccountNo, $sign01UserCode]
      - eq: [content.data.records.0.userName, "测试签署一"]



- test:
    name: TC-批量查询生效印章-ukey印章输入存在的国密keySN+商密keySN
    api: api/esignSeals/v1/sealcontrols/userseals/batch.yml
    variables:
      userInfos: [ { userCode: "$sign01UserCode" ,
                     keySNList: [
                      "$keySNSM", "$keySNGM"
                     ]
      }]
    extract:
      code: json.code
      message: json.message
    validate:
      - eq: ["status_code", 200]
      - eq: [$code, 200 ]
      - len_gt: [$message, 0]
      - ge: [content.data.total, 2 ]
      - contains: [content.data.records.0.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.0.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.0.keySN, "$keySNGM"]
      - ne: [content.data.records.0.sealSN, ""]
      - eq: [content.data.records.0.userCode, $sign01UserCode]
      - eq: [content.data.records.0.customAccountNo, $sign01UserCode]
      - eq: [content.data.records.0.userName, "测试签署一"]
      - contains: [content.data.records.1.sealImageDownloadUrl, "http"]
      - contains: [content.data.records.1.sealImageDownloadOuterUrl, "http"]
      - eq: [content.data.records.1.keySN, "$keySNSM"]
      - ne: [content.data.records.1.sealSN, ""]
      - eq: [content.data.records.1.userCode, $sign01UserCode]
      - eq: [content.data.records.1.customAccountNo, $sign01UserCode]
      - eq: [content.data.records.1.userName, "测试签署一"]