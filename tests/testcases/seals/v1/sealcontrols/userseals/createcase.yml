- config:
    name: "新建个人印章"
    variables:
        userCode1: ${ENV(sign01.userCode)}
        customAccountNo1: ${ENV(sign01.accountNo)}
        userCode2: ${ENV(csqs.userCode)}
        wuserCode: ${ENV(wsignwb01.userCode)}
        code101: "abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a"
        sp: " "
        pngFile: ${ENV(pngPageFileKey)}
        jpgFile: tests/data/psb.jpg
        base64file: ${get_file_base64($jpgFile)}
        certIdRSA: ${select_personal_certId($userCode1,1,1,0)} #生效中的rsa证书
        certIdSM2: ${select_personal_certId($userCode1,1,2,0)} #生效中的sm2证书
        certIdDel: ${select_personal_certId($userCode1,1,1,1)} #已删除的rsa证书
        certIdOver: ${select_personal_certId($userCode1,3,1,0)} #已过期的rsa证书
        certIdOverSM2: ${select_personal_certId($userCode1,3,2,0)} #已过期的SM2证书
        certIdRevo: ${select_personal_certId($userCode1,4,1,0)} #已吊销的rsa证书


- test:
    name: TL1-customAccountNo和usercode均为空
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        customAccountNo: ""
        userCode: ""
    validate:
        - eq: [ "content.code", 1325051]
        - eq: [ "content.message", 'userCode和customAccountNo二选一必填']
        - eq: ["content.data",null]
- test:
    name: TL2-customAccountNo和usercode有值不匹配,以usercode为主
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode2
        customAccountNo: $customAccountNo1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: ["content.data.sealInfos.0.userCode", $userCode2]
        - ne: ["content.data.sealInfos.0.sealId", ""]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL3-customAccountNo和usercode匹配,新建个人印章成功
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode1
        customAccountNo: $customAccountNo1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL4-customAccountNo加前后空格
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $sp $userCode1 $sp
        customAccountNo: " "
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL5-usercode加前后空格
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $sp $userCode1 $sp
        customAccountNo: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL6-customAccountNo超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: ""
        customAccountNo: $code101
    validate:
        - eq: [ "content.code", 1325054]
        - eq: [ "content.message", 'customAccountNo错误：创建人账号{abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a}不存在']


- test:
    name: TL7-usercode超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $code101
        customAccountNo: ""
    validate:
        - eq: [ "content.code", 1325002]
        - contains: [ "content.message", 'userCode错误：用户编码{']
        - contains: [ "content.message", '}不存在']


- test:
    name: TL8-usercode为相对方用户
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $wuserCode
        customAccountNo: ""
    validate:
        - eq: [ "content.code", 1325002]
        - contains: ["content.message","userCode错误：用户编码{"]

- test:
    name: TL9-usercode不存在
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode1 123
        customAccountNo: ""
    validate:
        - eq: [ "content.code", 1325002]
        - contains: ["content.message","userCode错误：用户编码{"]

- test:
    name: TL10-customAccountNo不存在
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: ""
        customAccountNo: $customAccountNo1 123
    validate:
        - eq: [ "content.code", 1325054]
        - contains: [ "content.message", 'customAccountNo错误：创建人账号{']
        - contains: [ "content.message", '}不存在']


- test:
    name: TL11-description超过100字
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        description: $code101
    validate:
        - eq: [ "content.code", 1325046]
        - eq: [ "content.message", 'description错误：印章说明{abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a}字数不能超过100个字']
        - eq: ["content.data",null]

- test:
    name: TL12-description为空
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        description: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL13-sealName超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealName: $code101
    validate:
        - eq: [ "content.code", 1325044]
        - contains: [ "content.message", 'sealName错误：印章名称{']


- test:
    name: TL14-sealName为不支持的特殊字符
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealName: "/:*? \" <>|"
    validate:
        - eq: [ "content.code", 1325045]
        - contains: [ "content.message", 'sealName错误：印章名称']


#- test:
#    name: TL15-sealName为空取创建人名称
#    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
#    variables:
#        sealName: ""
#    validate:
#        - eq: [ "content.code", 200]
#        - contains: [ "content.message", '成功']
#        - contains: ["content.data",""]

- test:
    name: TL16-sealSourceName超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSourceName: $code101
    validate:
        - eq: [ "content.code", 1325042]
        - eq: [ "content.message", 'sealSourceName错误：印章章面名称{abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a}字数支持2~26个字']
        - eq: ["content.data",null]

- test:
    name: TL16-sealSourceName为空,取创建人姓名
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSourceName: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL16-sealSourceName为正常值
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSourceName: "测试章面姓名"
    validate:
        - eq: [ "content.code", 200]
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL17-sealSource为空,默认为2
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]

- test:
    name: TL18-sealSource为3
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 3
    validate:
        - eq: [ "content.code", 1325047]
        - eq: [ "content.message", 'sealSource错误：印章来源{3}仅支持1和2']
        - eq: ["content.data",null]

- test:
    name: TL19-sealSource为-1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: -1
    validate:
        - eq: [ "content.code", 1325047]
        - contains: [ "content.message", '仅支持1和2']
        - eq: ["content.data",null]

- test:
    name: TL20-sealSource为abc
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: "abc"
    validate:
        - eq: [ "content.code", 1322222]
        - eq: [ "content.message", '不支持传入特殊字符']
        - eq: ["content.data",null]

- test:
    name: TL21-sealSource为0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 0
    validate:
        - eq: [ "content.code", 1325047]
        - eq: [ "content.message", 'sealSource错误：印章来源{0}仅支持1和2']
        - eq: ["content.data",null]

- test:
    name: TL23-sealSource为小数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 3.55
    validate:
        - eq: [ "content.code", 1325047]
        - eq: [ "content.message", 'sealSource错误：印章来源{3}仅支持1和2']
        - eq: ["content.data",null]

- test:
    name: TL24-base64img和sealFileKey均为空
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 1
        base64img: ""
        sealFileKey: ""
    validate:
        - eq: [ "content.code", 1325028]
        - eq: [ "content.message", '自定义印章，印章fileKey和印章base64不能都为空']
        - eq: ["content.data",null]

- test:
    name: TL25-base64img正确,创建印章成功
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 1
        base64img: $base64file
#        sealFileKey: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL26-base64img和sealFileKey均有值,以filekey为主
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 1
        base64img: $base64file
        sealFileKey: $pngFile
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL27-filekey正确
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 1
        base64img: ""
        sealFileKey: $pngFile
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL28-defaultSeal为空
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL29-defaultSeal为1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL30-defaultSeal为0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: 0
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL31-defaultSeal为 2
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: 2
    validate:
        - eq: [ "content.code", 1325063]
        - contains: [ "content.message","defaultSeal错误：是否为默认印章{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]


- test:
    name: TL32-defaultSeal为 3
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: 3
    validate:
        - eq: [ "content.code", 1325063]
        - contains: [ "content.message","defaultSeal错误：是否为默认印章{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]

- test:
    name: TL33-defaultSeal为 -1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: -1
    validate:
        - eq: [ "content.code", 1325063]
        - contains: [ "content.message","defaultSeal错误：是否为默认印章{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]

- test:
    name: TL34-defaultSeal为小数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        defaultSeal: 1.57
    validate:
        - eq: [ "content.code", 1325063]
        - contains: [ "content.message","defaultSeal错误：是否为默认印章{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]

- test:
    name: TL35-sealColour为1,sealSource为1不生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 1
        sealColour: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]


- test:
    name: TL36-sealColour为1,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 1
        name: "${get_randomNo()} "
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL37-sealColour为2,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL38-sealColour为3,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 3
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL39-sealColour为4,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 4
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL40-sealColour为5,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 5
    validate:
        - eq: [ "content.code", 1325050]
        - contains: [ "content.message","sealColour错误：印章颜色{" ]
        - contains: [ "content.message", '仅支持1~4' ]
        - eq: ["content.data",null]

- test:
    name: TL41-sealColour为0,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 0
    validate:
        - eq: [ "content.code", 1325050]
        - eq: [ "content.message", 'sealColour错误：印章颜色{0}仅支持1~4']

- test:
    name: TL42-sealColour为-1,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: -1
    validate:
        - eq: [ "content.code", 1325050]
        - contains: [ "content.message","sealColour错误：印章颜色{" ]
        - contains: [ "content.message", '仅支持1~4' ]
        - eq: ["content.data",null]


- test:
    name: TL43-sealColour为小数,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: 5.5
    validate:
        - eq: [ "content.code", 1325050]
        - contains: [ "content.message","sealColour错误：印章颜色{" ]
        - contains: [ "content.message", '仅支持1~4' ]
        - eq: ["content.data",null]


- test:
    name: TL44-sealColour为abc,sealSource为2生效
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealSource: 2
        sealColour: abc
    validate:
        - eq: [ "content.code", 1322222]
        - contains: [ "content.message","不支持传入特殊字符" ]


- test:
    name: TL45-sealHeight、sealWidth为空,默认20
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: ""
        sealHeight: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL46-sealWidth为abc,
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: "abc"
        sealHeight: ""
    validate:
        - eq: [ "content.code", 1322222]
        - eq: [ "content.message", '不支持传入特殊字符']

- test:
    name: TL47-sealHeight为abc报错
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: ""
        sealHeight: "abc"
    validate:
        - eq: [ "content.code", 1322222]
        - eq: [ "content.message", '不支持传入特殊字符']

- test:
    name: TL48-sealHeight、sealWidth均为100
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: 100
        sealHeight: 100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL49-sealWidth为负数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: -10
        sealHeight: 100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL50-sealWidth为负数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: 20
        sealHeight: -100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL51-sealWidth为10，sealHeigh为100
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealWidth: 10
        sealHeight: 100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL52-sealTemplateStyle为正方形，sealWidth和sealHeigh不一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 3
        sealWidth: 20
        sealHeight: 30
    validate:
        - eq: [ "content.code", 1325036]
        - eq: [ "content.message", '正方形印章宽高必须一致']


- test:
    name: TL53-sealTemplateStyle为正方形，sealWidth和sealHeigh不一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 4
        sealWidth: 20
        sealHeight: 30
    validate:
        - eq: [ "content.code", 1325036]
        - eq: [ "content.message", '正方形印章宽高必须一致']

- test:
    name: TL54-sealTemplateStyle为正方形，sealWidth和sealHeigh不一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 5
        sealWidth: 20
        sealHeight: 30
    validate:
        - eq: [ "content.code", 1325036]
        - eq: [ "content.message", '正方形印章宽高必须一致']

- test:
    name: TL55-sealTemplateStyle为正方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 3
        sealWidth: 20
        sealHeight: 20
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL56-sealTemplateStyle为正方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 4
        sealWidth: 10
        sealHeight: 10
    validate:
        - eq: [ "content.code", 1325037]
        - eq: [ "content.message", '正方形印章边长仅支持20mm、18mm、16mm']


- test:
    name: TL57-sealTemplateStyle为正方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 5
        sealWidth: 100
        sealHeight: 100
    validate:
        - eq: [ "content.code", 1325037]
        - eq: [ "content.message", '正方形印章边长仅支持20mm、18mm、16mm']

- test:
    name: TL58-sealTemplateStyle为长方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 1
        sealWidth: 100
        sealHeight: 100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL59-sealTemplateStyle为长方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 2
        sealWidth: 10
        sealHeight: 10
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL60-sealTemplateStyle为长方形，sealWidth和sealHeigh一致
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 1
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL61-sealTemplateStyle为-1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: -1
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 1325048]
        - eq: [ "content.message", 'sealTemplateStyle错误：印章模板样式{-1}仅支持1~5']


- test:
    name: TL62-sealTemplateStyle为0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 0
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 1325048]
        - eq: [ "content.message", 'sealTemplateStyle错误：印章模板样式{0}仅支持1~5']

- test:
    name: TL63-sealTemplateStyle为abc
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: abc
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 1322222]
        - eq: [ "content.message", '不支持传入特殊字符']

- test:
    name: TL64-sealTemplateStyle为6.1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 6.1
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 1325048]
        - contains: [ "content.message","sealTemplateStyle错误：印章模板样式{" ]
        - contains: [ "content.message", '}仅支持1~5' ]
        - eq: ["content.data",null]

- test:
    name: TL65-sealTemplateStyle为100
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealTemplateStyle: 100
        sealWidth: 30
        sealHeight: 50
    validate:
        - eq: [ "content.code", 1325048]
        - eq: [ "content.message", 'sealTemplateStyle错误：印章模板样式{100}仅支持1~5']

#- test:
#    name: TL66-sealTemplateStyle为100
#    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
#    variables:
#        sealTemplateStyle: 100
#        sealWidth: 30
#        sealHeight: 50
#    validate:
#        - eq: [ "content.code", 200]
#        - eq: [ "content.message", '报错']
#        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
#        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
#        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL67-sealPattern为1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL67-sealPattern为3
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL68-sealPattern为2
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 2
    validate:
        - eq: [ "content.code", 1325041]
        - eq: [ "content.message", 'sealPattern错误：印章形态{2}仅支持1和3']

- test:
    name: TL69-sealPattern为0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 0
    validate:
        - eq: [ "content.code", 1325041]
        - eq: [ "content.message", 'sealPattern错误：印章形态{0}仅支持1和3']

- test:
    name: TL70-sealPattern为abc
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: "abc"
    validate:
        - eq: [ "content.code", 1322222]
        - eq: [ "content.message", '不支持传入特殊字符']

- test:
    name: TL71-sealMakerCertId为空，sealPattern为国密
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
        sealMakerCertId: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL72-sealMakerCertId为不存在的值
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
        sealMakerCertId: 123
    validate:
        - eq: [ "content.code", 1325009]
        - contains: [ "content.message","sealMakerCertId错误：制章者证书{" ]
        - contains: [ "content.message", '}不可用' ]
        - eq: ["content.data",null]

- test:
    name: TL73-sealMakerCertId为国密印章绑定sm2证书
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
        sealMakerCertId: $certIdSM2
    validate:
        - eq: [ "content.code", 1325009]
        - contains: [ "content.message","sealMakerCertId错误：制章者证书{" ]
        - contains: [ "content.message", '}不可用' ]
        - eq: ["content.data",null]

- test:
    name: TL74-sealMakerCertId为已删除
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
        sealMakerCertId: $certIdDel
    validate:
        - eq: [ "content.code", 1325009]
        - contains: [ "content.message","sealMakerCertId错误：制章者证书{" ]
        - contains: [ "content.message", '}不可用' ]
        - eq: ["content.data",null]


- test:
    name: TL75-sealMakerCertId为已吊销
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 3
        sealMakerCertId: $certIdRevo
    validate:
        - eq: [ "content.code", 1325009]
        - contains: [ "content.message","sealMakerCertId错误：制章者证书{" ]
        - contains: [ "content.message", '}不可用' ]
        - eq: ["content.data",null]

- test:
    name: TL76-sealMakerCertId为商密绑定rsa
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealMakerCertId: $certIdRSA
        sealPattern: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL77-sealMakerCertId为国密绑定rsa,失败
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealMakerCertId: $certIdRSA
        sealPattern: 3
    validate:
        - eq: [ "content.code", 1325009]
        - contains: [ "content.message","sealMakerCertId错误：制章者证书{" ]
        - contains: [ "content.message", '}不可用' ]
        - eq: ["content.data",null]
          
- test:
    name: TL78-sealMakerCertId为商密印章绑定sm2证书，失败
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealPattern: 1
        sealMakerCertId: $certIdSM2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL79-sealOldStyle不传
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL80-sealOldStyle传1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL81-sealOldStyle传0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: 0
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL82-sealOldStyle传负数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: -10
    validate:
        - eq: [ "content.code", 1325066]
        - contains: [ "content.message","sealOldStyle错误：印章图片做旧{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]

- test:
    name: TL83-sealOldStyle传小数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: 1.35
    validate:
        - eq: [ "content.code", 1325066]
        - contains: [ "content.message","sealOldStyle错误：印章图片做旧{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]

- test:
    name: TL82-sealOldStyle传abc
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOldStyle: "abc"
    validate:
        - eq: [ "content.code", 1325066]
        - contains: [ "content.message","sealOldStyle错误：印章图片做旧{" ]
        - contains: [ "content.message", '}仅支持0和1' ]
        - eq: ["content.data",null]


- test:
    name: TL84-sealOpacity不传
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: ""
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL85-sealOpacity传1
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: 1
    validate:
        - eq: [ "content.code", 1325049]
        - eq: [ "content.message", 'sealOpacity错误：印章不透明度{1}仅支持20~100']

- test:
    name: TL86-sealOpacity传0
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: 0
    validate:
        - eq: [ "content.code", 1325049 ]
        - eq: [ "content.message", 'sealOpacity错误：印章不透明度{0}仅支持20~100' ]

- test:
    name: TL87-sealOpacity传负数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: -10
    validate:
        - eq: [ "content.code", 1325049 ]
        - eq: [ "content.message", 'sealOpacity错误：印章不透明度{-10}仅支持20~100' ]

- test:
    name: TL88-sealOpacity传小数
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: 1.35
    validate:
        - eq: [ "content.code", 1325049 ]
        - eq: [ "content.message", 'sealOpacity错误：印章不透明度{1}仅支持20~100' ]

- test:
    name: TL89-sealOpacity传abc
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: "abc"
    validate:
        - eq: [ "content.code", 1322222 ]
        - eq: [ "content.message", '不支持传入特殊字符' ]

- test:
    name: TL90-sealOpacity传100
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: 100
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - eq: [ "content.data.sealInfos.0.customAccountNo", $customAccountNo1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]

- test:
    name: TL91-sealOpacity传1000
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        sealOpacity: 1000
    validate:
        - eq: [ "content.code", 1325049 ]
        - eq: [ "content.message", 'sealOpacity错误：印章不透明度{1000}仅支持20~100' ]