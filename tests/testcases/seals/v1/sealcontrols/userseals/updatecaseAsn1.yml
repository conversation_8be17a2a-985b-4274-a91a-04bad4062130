- config:
    name: "更新个人印章"
    variables:
        userCode1: ${ENV(sign01.userCode)}

- test:
    name: step1-新建个人印章成功-云中国标准
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode1
        sealPattern: "3"
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]
    extract:
        - sealId0: content.data.sealInfos.0.sealId

- test:
    name: step2-更新个人印章发布到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $sealId0
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

#todo https://forward.esign.cn/bugManagement/edit?id=50191&type=check
#- test:
#    name: TC1-修改sealId-修改国际标准形态
#    api: api/esignSeals/v1/sealcontrols/userseals/update.yml
#    variables:
#        sealSourceName: "测试章面姓名"
#        sealPattern: 1
#        sealId: $sealId0
#    validate:
#        - eq: [ "content.code", 200]
#        - contains: [ "content.message", '成功']
#
#- test:
#    name: TC2-sealSourceName为正常值
#    api: api/esignSeals/v1/sealcontrols/userseals/update.yml
#    variables:
#        sealSourceName: "测试章面姓名"
#        sealPattern: 3
#        sealId: $sealId0
#        signerCertInfos: null
#    validate:
#        - eq: [ "content.code", 200]
#        - eq: [ "content.data.userCode", $userCode1 ]
#        - contains: [ "content.message", '成功']
#        - ne: [ "content.data.sealId", "" ]
#        - eq: ["content.data.sealStatus", "1"]