- config:
    name: "更新个人印章状态"
    variables:
        userCode1: ${ENV(sign01.userCode)}
        code101: "${generate_random_str(101)}"
        sp: " "
#sealStatus: 印章状态(1草稿 g发布 h停用 5吊销) 印章状态：1-已发布 2-已停用 3-已吊销


- test:
    name: TL1-sealid不存在
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: 123
        sealStatus: 2
    validate:
        - eq: [ "content.code", 1313018]
        - eq: [ "content.message", '该个人印章[123]不存在']


- test:
    name: TL2-sealid为空
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealStatus: 2
    validate:
        - eq: [ "content.code", 1300000]
        - eq: [ "content.message", '印章id不能为空']

- test:
    name: TL1-sealStatus为空
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: ""
    validate:
        - eq: [ "content.code", 1300000]
        - eq: [ "content.message", '印章状态不能为空']

- test:
    name: TL1-sealId超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $code101
        sealStatus: 1
    validate:
        - eq: [ "content.code", 1300000]
        - eq: [ "content.message", '印章id不超过36个字']


- test:
    name: TL1-sealStatus为超过最大长度
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: $code101
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.data", null]
        - contains: [ "content.message", 'sealStatus错误：印章状态不支持传入']

- test:
    name: TL1-sealStatus为4
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 4
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.message", 'sealStatus错误：印章状态不支持传入[4]']

- test:
    name: TL1-sealStatus为0
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 0
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.message", 'sealStatus错误：印章状态不支持传入[0]']


- test:
    name: TL1-sealStatus为0
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 0.5
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.message", 'sealStatus错误：印章状态不支持传入[0.5]']


- test:
    name: TL1-sealStatus为abc
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: abc
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.message", 'sealStatus错误：印章状态不支持传入[abc]']


- test:
    name: TL1-sealId包含首位空格
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $sp ${getUserSealsByStatus($userCode1,g)} $sp
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - ne: ["content.data.sealStatus", 2]

#todo 新建草稿态个人印章然后再更新状态到发布态
#- test:
#    name: TL1-更新个人印章状态草稿态到发布
#    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
#    variables:
#        sealId: ${getUserSealsByStatus($userCode1,1)}
#        sealStatus: 1
#    validate:
#        - eq: [ "content.code", 200]
#        - contains: [ "content.message", '成功']
#        - ne: ["content.data.sealStatus", 2]

- test:
    name: TL2-更新个人印章状态发布到发布
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 1
    validate:
        - eq: [ "content.code", 1313027]
        - contains: [ "content.message", '发布状态不支持发布，请修改印章状态为已停用']

- test:
    name: TL2-更新个人印章状态停用到发布
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,h)}
        sealStatus: 1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']

- test:
    name: TL2-更新个人印章吊销到发布
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,5)}
        sealStatus: 1
    validate:
        - eq: [ "content.code", 1313027]
        - contains: [ "content.message", '吊销状态不支持发布，请修改印章状态为已停用']

- test:
    name: TL1-更新个人印章状态草稿态到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,1)}
        sealStatus: 2
    validate:
        - eq: [ "content.code", 1313028]
        - contains: [ "content.message", '草稿状态不支持停用，请修改印章状态为已发布']

- test:
    name: TL2-更新个人印章状态发布到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - ne: ["content.data.sealStatus", 2]

- test:
    name: TL2-更新个人印章状态停用到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,h)}
        sealStatus: 2
    validate:
        - eq: [ "content.code", 1313028]
        - contains: [ "content.message", '停用状态不支持停用，请修改印章状态为已发布']

- test:
    name: TL2-更新个人印章吊销到停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,5)}
        sealStatus: 2
    validate:
        - eq: [ "content.code", 1313028]
        - contains: [ "content.message", '吊销状态不支持停用，请修改印章状态为已发布']


- test:
    name: TL2-更新个人印章状态发布到吊销
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: ${getUserSealsByStatus($userCode1,g)}
        sealStatus: 3
    validate:
        - eq: [ "content.code", 1313035]
        - eq: [ "content.message", 'sealStatus错误：印章状态不支持传入[3]']

