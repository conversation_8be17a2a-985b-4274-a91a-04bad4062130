- config:
    name: "查询机构所有生效的云证书 - customOrgNo 查询"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      param_customOrgNo: ${ENV(csqs.orgNo)}
      sp: " "
      organizationCodeSpace: $sp$param_customOrgNo$sp

# 浅风        
- test:
    name: organizationCode 传参为空, customOrgNo传参为 null
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: null
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913011]
       - eq: [$message, "企业编码与企业账号必填其一"]
       - eq: [content.data, null ]   
           
- test:
    name: organizationCode 传参为null, customOrgNo传参为 null
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: null
        customOrgNo: null
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913011]
       - eq: [$message, "企业编码与企业账号必填其一"]
       - eq: [content.data, null ]     
       
#- test:
#    name: organizationCode 传参为空, customOrgNo传参为 存在的内部机构账号
#    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
#    variables:
#        organizationCode: ""
#        customOrgNo: ${ENV(csqs.orgNo)}
#    extract:
#      code: json.code
#      message: json.message
#      data: json.data
#    validate:
#       - eq: [$code, 200]
#       - contains: [$message, "成功"]
#       - len_gt: [$data, 0]
         
- test:
    name: organizationCode 传参为空, customOrgNo传参为 不存在的内部机构账号
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: "QFBUCUNZAIORGNO"
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ]   
         
- test:
    name: organizationCode 传参为空, customOrgNo传参为 外部的机构账号
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: ${ENV(wsignwb01.main.orgNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - eq: [$data, []]
#
- test:
    name: organizationCode 传参为空, customOrgNo传参为 注销的机构账号
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: ${ENV(loggedOutcustomOrgNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ]      
         
- test:
    name: organizationCode 传参为空, customOrgNo传参为 删除的机构账号
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: ${ENV(deletedcustomOrgNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ]     
         
- test:
    name: organizationCode 传参为空, customOrgNo传参为 长度超过54个字符
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: ${generate_random_str(55)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1300000]
       - eq: [$message, "组织账号不能超过54个字符"] 
       - eq: [content.data, null ]
         
- test:
    name: organizationCode 传参为空, customOrgNo传参为 内部机构账号前后带空格
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: $organizationCodeSpace
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]