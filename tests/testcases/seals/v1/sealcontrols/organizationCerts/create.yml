- test:
    name: $name
    api: api/esignSeals/v1/sealcontrols/organizationCerts/create.yml
    variables:
      - organizationCode: $organizationCode
      - customOrgNo: $customOrgNo
      - algorithm: $algorithm
      - certName: $certName
#    extract:
#      - certId1: content.data.certId
#      - orgCode: content.data.organizationCode
        
    validate:
      - contains: [ "content.message", $message ]
      - eq: ["content.code", $code]
      - ne: ["content.data",$data]
