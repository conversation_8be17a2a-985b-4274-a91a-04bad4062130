- config:
    name: "查询机构云证书列表-status、有效期时间开始时间、有效期结束时间"
    variables:
       orgCode: ${ENV(sign01.main.orgCode)}
       sp: " "
       expirationTimeS: ${getDateTime(-1,1)}
       expirationTimeE: ${getDateTime(367,1)}


- test:
    name: setup-正常用例-新增离线企业证书-参数正确
    api: api/esignSeals/certs/enterprise/saveEnterpriseCert.yml
    variables:
      organizationCode:  ${ENV(csqs.orgCode)}
      organizationName: ${ENV(csqs.orgName)}
      licenseNumber: ${ENV(licenseNumber)}
      licenseType: 12
      certAlgorithm: 1
      applyMethod: 2
      certName: 离线证书
      certType: 1
    extract:
      status: json.status
      success : json.success
      message: json.message
      certID: json.data
    validate:
       - eq: [ "status_code", 200]
       - eq: [ $status, 200 ]
       - eq: [ $success, true ]
       - contains: [ $message, "成功" ]
       - len_gt: [$certID, 1]
    teardown_hooks:
      - ${sleep(30)}


- test:
    name: setup-正常用例-新增企业证书
    api: api/esignSeals/certs/enterprise/saveEnterpriseCert.yml
    variables:
      organizationCode:  ${ENV(csqs.orgCode)}
      organizationName: ${ENV(csqs.orgName)}
      licenseNumber: ${ENV(licenseNumber)}
      licenseType: 12
      certAlgorithm: 1
      applyMethod: 1
      certName: 测试证书1
      certType: 1
    extract:
      status: json.status
      success : json.success
      message: json.message
      certID: json.data
    validate:
       - eq: [ "status_code", 200]
       - eq: [ $status, 200 ]
       - eq: [ $success, true ]
       - contains: [ $message, "成功" ]
       - len_gt: [$certID, 1]


- test:
    name: 查询机构云证书列表-status非1、2、3-汉字，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "汉字"
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询机构云证书列表-status非1、2、3-英文，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "en"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]



- test:
    name: 查询机构云证书列表-status非1、2、3-字符，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "$"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]
- test:
    name: 查询机构云证书列表-status非1、2、3-字符<>，相当于空，把<>过滤掉了-bug?
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "<>"
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询机构云证书列表-status非1、2、3-数字11，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 11
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询机构云证书列表-status非1、2、3-数字4，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 4
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询机构云证书列表-status非1、2、3-数字0，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 0
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询机构云证书列表-status非1、2、3-数字-1，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: -1
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询机构云证书列表-status含1、2、3之外有其他数字，报错
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "0,1,2,3,4"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]


- test:
    name: 查询机构云证书列表status不传,返回正常证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询机构云证书列表status传null,返回正常证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus:
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询机构云证书列表status传"",返回正常证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: ""
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询机构云证书列表status传"  ",返回正常证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: "  "
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]


- test:
    name: 查询机构云证书列表status-1,返回正常证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 1
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - eq: ["json.data.0.status","1"]
        - ne: ["json.data.0.startTime",""]
        - ne: ["json.data.0.endTime",""]
        - eq: ["json.data.0.applyCertUrl",""]
        - len_gt: ["json.data",1]

- test:
    name: 查询机构云证书列表status-2,申请中
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 2
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - eq: ["json.data.0.status","2"]
        - eq: ["json.data.0.startTime",""]
        - eq: ["json.data.0.endTime",""]
        - contains: ["json.data.0.applyCertUrl","http"]
        - len_gt: ["json.data",1]


- test:
    name: 查询机构云证书列表status-3,已过期
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $orgCode
        certsStatus: 3
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - ne: ["json.data",[]]



- test:
      name: 查询机构云证书列表status传多个,返回申请中、已过期、正常状态的证书
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          certsStatus: "1,2,3"
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]
        

- test:
      name: 查询机构云证书列表expirationStartTime格式错误1，报错
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          expirationStartTime: "2025/08/12 11:20:00"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data",null]


- test:
      name: 查询机构云证书列表expirationStartTime格式错误2，报错
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          expirationStartTime: "2025-08-12 11:20"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data",null]

- test:
      name: 查询机构云证书列表expirationEndTime格式错误，报错
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          expirationEndTime: "2025/08/12 11:20:00"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data",null]



- test:
      name: 查询机构云证书列表expirationEndTime格式错误，报错
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          expirationEndTime: "2025-08-12 11:00"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data",null]



- test:
      name: 查询机构云证书列表expirationEndTime比expirationStartTime时间小，报错
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          expirationStartTime: "2025-08-12 11:20:00"
          expirationEndTime: "2025-08-10 11:20:00"
      validate:
          - eq: [ "json.code", 1316057 ]
          - eq: [ "json.message", '证书过期结束时间不能早于证书过期开始时间' ]
          - eq: [ "json.data",null]

- test:
    name: 查询机构云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的有效证书
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
      organizationCode: $orgCode
      certsStatus: 1
      expirationStartTime: $sp$expirationTimeS$sp
      expirationEndTime: $sp$expirationTimeE$sp
    validate:
      - eq: [ "json.code", 200 ]
      - contains: [ "json.message", '成功' ]
      - len_gt: [ "json.data",1 ]

- test:
      name: 查询机构云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的有效证书
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          certsStatus: 1$sp
          expirationStartTime: $sp$expirationTimeS$sp
          expirationEndTime: $sp$expirationTimeE$sp
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]
            
            
- test:
      name: 查询机构云证书列表expirationEndTime、expirationStartTime输入正确值，查询申请中证书,返回空
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          certsStatus: 2
          expirationStartTime: $expirationTimeS
          expirationEndTime: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data",[] ]


- test:
      name: 查询机构云证书列表查询正常证书，提取过期时间
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          certsStatus: 1
          expirationStartTime:
          expirationEndTime:
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data.0.status", '1' ]
          - len_gt: [ "json.data",1 ]
      extract:
        expirationTime0: json.data.0.endTime


- test:
      name: 查询机构云证书列表查询正常证书，过期开始时间和过期结束时间相同
      api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
      variables:
          organizationCode: $orgCode
          certsStatus: 1
          expirationStartTime: $expirationTime0
          expirationEndTime: $expirationTime0
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data.0.status", '1' ]
          - ne: [ "json.data.0.certId", "" ]
          - eq: [ "json.data.0.applyCertUrl", "" ]