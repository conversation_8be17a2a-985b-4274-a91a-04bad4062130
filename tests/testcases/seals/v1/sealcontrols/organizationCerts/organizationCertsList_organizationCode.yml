- config:
    name: "查询机构所有生效的云证书 - organizationCode 查询"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      organizationCode1: ${ENV(csqs.orgCode)}
      param_organizationCode: ${ENV(csqs.orgCode)}
      organizationCodeBeforeSpace: ${str_insert_space($param_organizationCode, 1)}
      organizationCodeMiddleSpace: ${str_insert_space($param_organizationCode, 2)}
      organizationCodeAfterSpace: ${str_insert_space($param_organizationCode, 3)}        
      sp: " "

- test:
    name: 查询机构所有生效的云证书-正常organizationCode
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${ENV(csqs.orgCode)}
        customOrgNo: ${ENV(csqs.orgNo)}

    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询机构所有生效的云证书-organizationCode为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode:
        customOrgNo:

    validate:
        - eq: [ "json.code", 1913011 ]
        - eq: [ "json.message", '企业编码与企业账号必填其一' ]

#- test:
#    name: 查询机构所有生效的云证书-organizationCode为空  正常的customOrgNo
#    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
#    variables:
#        organizationCode:
#        customOrgNo: ${ENV(csqs.orgNo)}
#    validate:
#        - eq: [ "json.code", 200 ]
#        - eq: [ "json.message", '成功' ]

- test:
    name: 查询机构所有生效的云证书-organizationCode为随机值  正常的customOrgNo
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${generate_random_str(8)}
        customOrgNo: ${ENV(csqs.orgNo)}

    validate:
        - eq: [ "json.code", 1312103 ]
        - eq: [ "json.message", '企业不存在或企业当前为非存续状态' ]

#- test:
#    name: 查询机构所有生效的云证书-organizationCode为特殊字符 正常的customOrgNo
#    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
#    variables:
#        organizationCode: "|*&^%"
#        customOrgNo: ${ENV(csqs.orgNo)}
#
#    validate:
#        - eq: [ "json.code", 1300000 ]
#        - eq: [ "json.message", 'organizationCode不能包含特殊字符' ]
#- test:
#    name: 查询机构所有生效的云证书-organizationCode为正常 特殊字符的的customOrgNo
#    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
#    variables:
#        organizationCode: ${ENV(csqs.orgCode)}
#        customOrgNo:  "|*&^%"
#
#    validate:
#        - eq: [ "json.code", 1300000 ]
#        - eq: [ "json.message", 'customOrgNo不能包含特殊字符' ]

- test:
    name: 查询机构所有生效的云证书-organizationCode前后空格
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $sp$organizationCode1$sp
        customOrgNo: ${ENV(csqs.orgNo)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询机构所有生效的云证书-organizationCode中间空格
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: "30fb6c46ca544b   73acc5488d83f59550"
        customOrgNo: ${ENV(csqs.orgNo)}

    validate:
        - eq: [ "json.code", 1312103 ]
        - eq: [ "json.message", '企业不存在或企业当前为非存续状态' ]
        
# 浅风        
- test:
    name: organizationCode 传参为空, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ""
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913011]
       - eq: [$message, "企业编码与企业账号必填其一"]
       - eq: [content.data, null ] 
       
- test:
    name: organizationCode 传参为null, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: null
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913011]
       - eq: [$message, "企业编码与企业账号必填其一"]
       - eq: [content.data, null ] 
       
- test:
    name: organizationCode 传参为存在的内部的机构编码, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${ENV(csqs.orgCode)}
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0] 
       
- test:
    name: organizationCode 传参为不存在的内部机构编码, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: "qianfengbucunzaiorgcode"
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ] 
       
- test:
    name: organizationCode 传参为外部机构编码, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${ENV(externalOrganizationCode)}
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - eq: [$data, []] 
       
- test:
    name: organizationCode 传参为注销的机构编码, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${ENV(loggedOutOrganizationCode)}
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ] 
       
- test:
    name: organizationCode 传参为删除的机构编码, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${ENV(deletedOrganizationCode)}
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ] 
       
- test:
    name: organizationCode 长度超过54个字符, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: ${generate_random_str(55)}
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1300000]
       - eq: [$message, "机构编码不能超过54个字符"] 
       - eq: [content.data, null ] 
              
- test:
    name: organizationCode 前置空格, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $organizationCodeBeforeSpace
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]  
            
- test:
    name: organizationCode 中有空格, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $organizationCodeMiddleSpace
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ]      
       
- test:
    name: organizationCode 后置空格, customOrgNo传参为空
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $organizationCodeAfterSpace
        customOrgNo: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]       
        
- test:
    name: organizationCode 存在的内部机构编码，customOrgNo 不一致
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: $param_organizationCode
        customOrgNo: ${ENV(businesscustomOrgNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]   
            
- test:
    name: organizationCode 不存在的内部机构编码，customOrgNo 存在的内部机构账号
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
        organizationCode: "qianfengtestbucunzaiorgcode"
        customOrgNo: ${ENV(csqs.orgNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312103]
       - eq: [$message, "企业不存在或企业当前为非存续状态"] 
       - eq: [content.data, null ]              