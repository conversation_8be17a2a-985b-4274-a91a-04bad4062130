- config:
    name: "查询用户证书列表-status、有效期时间开始时间、有效期结束时间"
    variables:
       userCode1: ${ENV(sign01.userCode)}
       userNo1: ${ENV(sign01.accountNo)}
       userName1: ${ENV(sign01.userName)}
       sp: " "
       expirationTimeS: ${getDateTime(360,1)}
       expirationTimeE: ${getDateTime(367,1)}

- test:
    name: "setup-创建内部个人离线证书"
    api: api/esignSeals/personal/savePersonalCert.yml
    variables:
      certName_savePersonalCert: $userName1  #证书名称
      userCode_savePersonalCert: $userCode1 #用户编码
      userName_savePersonalCert: $userName1
      applyMethod_savePersonalCert: 2
    extract:
      - userCerId1: content.data
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, "服务器成功返回" ]
      - ne: [ content.data.certId, "" ]
      - eq: [ content.data.userName, $userName1 ]

- test:
    name: setup-创建个人证书
    api: api/esignSeals/v1/sealcontrols/userCerts/create.yml
    variables:
        json:
          {
              userCode: $userCode1,
              customAccountNo: "",
              algorithm: "1",
              certName: "测试证书${get_randomNo1()}"
          }
    extract:
        - userCertId0: content.data.certId
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]



- test:
    name: 查询用户云证书列表-status非1、2、3-汉字，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "汉字"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询用户云证书列表-status非1、2、3-英文，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "en"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]



- test:
    name: 查询用户云证书列表-status非1、2、3-字符，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "$"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]


- test:
    name: 查询用户云证书列表-status非1、2、3-字符<>，相当于空，把<>过滤掉了-bug?
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "<>"
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]
- test:
    name: 查询用户云证书列表-status非1、2、3-数字11，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 11
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询用户云证书列表-status非1、2、3-数字4，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 4
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]

- test:
    name: 查询用户云证书列表-status非1、2、3-数字0，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 0
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]


- test:
    name: 查询用户云证书列表-status非1、2、3-数字-1，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: -1
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]


- test:
    name: 查询用户云证书列表-status含1、2、3之外有其他数字，报错
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "1,2,3，4"
    validate:
        - eq: [ "json.code", 1316055]
        - eq: [ "json.message", '证书状态只支持1,2,3']
        - eq: ["json.data",null]



- test:
    name: 查询用户云证书列表status不传,返回正常证书
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询用户云证书列表status传null,返回正常证书
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList:
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询用户云证书列表status传"",返回正常证书
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: ""
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询用户云证书列表status传"  ",返回正常证书
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: "  "
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]


- test:
    name: 查询用户云证书列表status为1,返回正常证书
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 1
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - eq: ["json.data.0.status","1"]
        - ne: ["json.data.0.startTime",""]
        - ne: ["json.data.0.endTime",""]
        - eq: ["json.data.0.applyCertUrl",""]
        - len_gt: ["json.data",1]

- test:
    name: 查询用户云证书列表status-2,申请中
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 2
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - eq: ["json.data.0.status","2"]
        - eq: ["json.data.0.startTime",""]
        - eq: ["json.data.0.endTime",""]
        - contains: ["json.data.0.applyCertUrl","http"]
        - len_gt: ["json.data",1]


- test:
    name: 查询用户云证书列表status-3,已过期
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        certsStatus_userCertsList: 3
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - ne: ["json.data",[] ]



- test:
      name: 查询用户云证书列表status传多个,返回申请中、已过期、正常状态的证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: "1,2,3"
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",2]
        

- test:
      name: 查询用户云证书列表expirationStartTime格式错误1，报错
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationStartTime_userCertsList: "2025/08/12 11:20:00"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data", null ]

- test:
      name: 查询用户云证书列表expirationStartTime格式错误2，报错
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationStartTime_userCertsList: "2025-08-12 11:20"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data", null ]


- test:
      name: 查询用户云证书列表expirationEndTime格式错误1，报错
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationEndTime_userCertsList: "2025/08/12 11:20:00"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data", null ]

- test:
      name: 查询用户云证书列表expirationEndTime格式错误2，报错
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationEndTime_userCertsList: "2025-08-12 11:20"
      validate:
          - eq: [ "json.code", 1316056 ]
          - eq: [ "json.message", '证书有效期时间格式只支持:yyyy-MM-dd HH:mm:ss' ]
          - eq: [ "json.data", null ]

- test:
      name: 查询用户云证书列表expirationEndTime比expirationStartTime时间小，报错
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationStartTime_userCertsList: "2025-08-12 11:20:00"
          expirationEndTime_userCertsList: "2025-08-10 11:20:00"
      validate:
          - eq: [ "json.code", 1316057 ]
          - eq: [ "json.message", '证书过期结束时间不能早于证书过期开始时间' ]
          - eq: [ "json.data",null ]


- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询在这个区间段的有效和过期证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationStartTime_userCertsList: ${getDateTime(-100,1)}
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]

- test:
      name: 查询用户云证书列表，只传expirationStartTime，查询证书有效期结束时间大于等于expirationStartTime有效证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationStartTime_userCertsList: ${getDateTime(-100,1)}
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]


- test:
      name: 查询用户云证书列表，只传expirationEndTime，查询证书有效期结束时间小于等于expirationEndTime有效证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]

- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的申请证书，返回数据为空
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 2
          expirationStartTime_userCertsList: $expirationTimeS
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - eq: [ "json.data",[]]

- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的有效证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 1
          expirationStartTime_userCertsList: $expirationTimeS
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]

- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的有效证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 1
          expirationStartTime_userCertsList: ${getDateTime(-360,1)}
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]


- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询过期时间段的有效证书
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 1$sp
          expirationStartTime_userCertsList: ${getDateTime(-360,1)}
          expirationEndTime_userCertsList: $sp$expirationTimeE$sp
      validate:
          - eq: [ "json.code", 200 ]
          - contains: [ "json.message", '成功' ]
          - len_gt: [ "json.data",1 ]



- test:
      name: 查询用户云证书列表expirationEndTime、expirationStartTime输入正确值，查询申请中证书,返回空
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 2
          expirationStartTime_userCertsList: $expirationTimeS
          expirationEndTime_userCertsList: $expirationTimeE
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data",[] ]


- test:
      name: 查询用户云证书列表查询正常证书，提取过期时间
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 1
          expirationStartTime_userCertsList:
          expirationEndTime_userCertsList:
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data.0.status", '1' ]
          - len_gt: [ "json.data",1 ]
      extract:
        expirationTime0: json.data.0.endTime


- test:
      name: 查询用户云证书列表查询正常证书，过期开始时间和过期结束时间相同
      api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
      variables:
          userCode_userCertsList: $userCode1
          certsStatus_userCertsList: 1
          expirationStartTime_userCertsList: $expirationTime0
          expirationEndTime_userCertsList: $expirationTime0
      validate:
          - eq: [ "json.code", 200 ]
          - eq: [ "json.message", '成功' ]
          - eq: [ "json.data.0.status", '1' ]
          - ne: [ "json.data.0.certId", "" ]
          - eq: [ "json.data.0.applyCertUrl", "" ]