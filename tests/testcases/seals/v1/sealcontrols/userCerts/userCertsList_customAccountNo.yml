- config:
    name: "查询用户所有生效云证书"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
#       param_customAccountNo: ${ENV(customAccountNo)}
       param_customAccountNo: ${ENV(sign01.accountNo)}
       customAccountNoBeforeSpace: ${str_insert_space($param_customAccountNo, 1)}
       customAccountNoMiddleSpace: ${str_insert_space($param_customAccountNo, 2)}
       customAccountNoAfterSpace: ${str_insert_space($param_customAccountNo, 3)}

- test:
    name: "customAccountNo 传参为 null，userCode 传参为空"
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: null
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913012]
       - eq: [$message, "用户编码与用户账号必填其一"] 
       - eq: [content.data, null ] 

- test:
    name: customAccountNo 传参为 null，userCode 传参为 null
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: null
        customAccountNo_userCertsList: null
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913012]
       - eq: [$message, "用户编码与用户账号必填其一"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 存在的内部用户账号，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ${ENV(customAccountNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]

- test:
    name: customAccountNo 传参为 不存在的内部用户账号，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: "qianfengbucunzai"
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312104]
       - eq: [$message, "用户不存在或用户为非在职状态"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 外部用户账号，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ${ENV(externalCustomAccountNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312104]
       - eq: [$message, "用户不存在或用户为非在职状态"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 删除的用户账号，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ${ENV(deletedcustomAccountNo)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312104]
       - eq: [$message, "用户不存在或用户为非在职状态"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 离职的用户账号，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ${ENV(customAccountNoDimission_qf)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - contained_by: [$code, [ 1312104, 200]]
       - contained_by: [$message, ["用户不存在或用户为非在职状态","成功"]]
       - ne: [content.data, "" ]
#       - eq: [ content.code, 200 ]
#       - contains: [ content.message, "成功" ]
#       - eq: [ content.data, [] ]

- test:
    name: customAccountNo 传参为 长度超过34个字符，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ${generate_random_str(35)}
        algorithm_userCertsList: "1"
        certPattern_userCertsList: "1"
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1300000]
       - eq: [$message, "用户账号不能超过34个字符"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 内部用户账号前置空格，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: $customAccountNoBeforeSpace
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]

- test:
    name: customAccountNo 传参为 内部用户账号中有空格，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: $customAccountNoMiddleSpace
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312104]
       - eq: [$message, "用户不存在或用户为非在职状态"]
       - eq: [content.data, null ]

- test:
    name: customAccountNo 传参为 内部用户账号后置空格，userCode 传参为 空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: $customAccountNoAfterSpace
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]

- test:
    name: customAccountNo 传参为 不存在的内部用户账号，userCode 传参为 存在的内部用户编码
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${ENV(userCode)}
        customAccountNo_userCertsList: "qianfengbucunzai"
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"]
       - len_gt: [$data, 0]
       
#- test:
#    name: customAccountNo 传参为 存在特殊字符，userCode 传参为 空
#    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
#    variables:
#        userCode_userCertsList: ""
#        customAccountNo_userCertsList: "|*&^%"
#    extract:
#      code: json.code
#      message: json.message
#      data: json.data
#    validate:
#       - eq: [$code, 1300000]
#       - eq: [$message, "customAccountNo不能包含特殊字符"]
#       - eq: [content.data, null ]