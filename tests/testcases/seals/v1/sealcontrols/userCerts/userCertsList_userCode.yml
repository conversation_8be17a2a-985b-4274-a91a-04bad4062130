- config:
    name: "查询用户所有生效云证书"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
#       userCode1: ${ENV(userCode)}
       userCode1: ${ENV(sign01.userCode)}
       userNo1: ${ENV(sign01.accountNo)}
       sp: " "
       userCodeBeforeSpace: ${str_insert_space($userCode1, 1)}
       userCodeMiddleSpace: ${str_insert_space($userCode1, 2)}
       userCodeAfterSpace: ${str_insert_space($userCode1, 3)}

- test:
    name: 查询用户所有生效云证书-正常userCode customAccountNo
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        customAccountNo_userCertsList: $userNo1
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询用户所有生效云证书-userCode为空 customAccountNo正常
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList:
        customAccountNo_userCertsList: $userNo1
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]

- test:
    name: 查询用户所有生效云证书-userCode为空 customAccountNo为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList:
        customAccountNo_userCertsList:
    validate:
        - eq: [ "json.code",  1913012]
        - eq: [ "json.message", "用户编码与用户账号必填其一"]

- test:
    name: 查询用户所有生效云证书-userCode为随机值   customAccountNo正常
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${generate_random_str(8)}
        customAccountNo_userCertsList: $userNo1

    validate:
        - eq: [ "json.code", 1111003 ]
        - eq: [ "json.message", '用户已不存在' ]

- test:
    name: 查询用户所有生效云证书-userCode前空格
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $sp$userCode1$sp
        customAccountNo_userCertsList: $userNo1

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询用户所有生效云证书-userCode中间空格
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: "ces   yk"
        customAccountNo_userCertsList: $userNo1

    validate:
        - eq: [ "json.code", 1111003 ]
        - eq: [ "json.message", '用户已不存在' ]
        
        
# 浅风
# 浅风
- test:
    name: userCode 传参为空, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ""
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913012]
       - eq: [$message, "用户编码与用户账号必填其一"] 
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为null, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: null
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1913012]
       - eq: [$message, "用户编码与用户账号必填其一"] 
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为 存在的内部用户编码, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"] 
       - len_gt: [$data, 0]   

- test:
    name: userCode 传参为 不存在的内部用户编码, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: "qianfengbucunzai"
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1111003]
       - eq: [$message, "用户已不存在"] 
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为 外部用户编码, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${ENV(externalUserCode)}
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1312107]
       - eq: [$message, "用户非内部用户"] 
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为 删除的用户编码, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${ENV(deletedUserCode)}
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1111003]
       - eq: [$message, "用户已不存在"] 
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为 离职的用户编码, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${ENV(userCodeDimission_qf)}
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1111003]
       - eq: [$message, "用户已不存在"]
       - eq: [content.data, null ]   

- test:
    name: userCode 传参为 长度超过34个字符, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: ${generate_random_str(35)}
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1300000]
       - eq: [$message, "用户编码不能超过34个字符"] 
       - eq: [content.data, null ]  

- test:
    name: userCode 传参为 内部用户编码前置空格, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCodeBeforeSpace
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"] 
       - len_gt: [$data, 0]   

- test:
    name: userCode 传参为 内部用户编码中有空格, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCodeMiddleSpace
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1111003]
       - eq: [$message, "用户已不存在"] 
       - eq: [content.data, null ]    

- test:
    name: userCode 传参为 内部用户编码后置空格, customAccountNo传参为空
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCodeAfterSpace
        customAccountNo_userCertsList: ""
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"] 
       - len_gt: [$data, 0]   
        
- test:
    name: userCode 传参为 存在的内部用户编码, customAccountNo 一致
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        customAccountNo_userCertsList: $userNo1
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"] 
       - len_gt: [$data, 0]   

- test:
    name: userCode 传参为 存在的内部用户编码, customAccountNo 不一致
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: $userCode1
        customAccountNo_userCertsList: ${ENV(customAccountNo1)}
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 200]
       - contains: [$message, "成功"] 
       - len_gt: [$data, 0]   

- test:
    name: userCode 传参为 不存在的内部用户编码, customAccountNo 为存在内部用户账号
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
        userCode_userCertsList: "qianfengbucunzai"
        customAccountNo_userCertsList: $userNo1
    extract:
      code: json.code
      message: json.message
      data: json.data
    validate:
       - eq: [$code, 1111003]
       - eq: [$message, "用户已不存在"] 
       - eq: [content.data, null ]   
       
#- test:
#    name: userCode 传参为 特殊字符, customAccountNo 为空
#    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
#    variables:
#        userCode_userCertsList: "|*&^%"
#        customAccountNo_userCertsList: ""
#    extract:
#      code: json.code
#      message: json.message
#      data: json.data
#    validate:
#       - eq: [$code, 1300000]
#       - eq: [$message, "userCode不能包含特殊字符"]
#       - eq: [content.data, null ]