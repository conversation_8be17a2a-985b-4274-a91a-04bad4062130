- config:
    name: "查询企业物理印章授权的用印人信息分页列表"
    base_url: ${ENV(esign.gatewayHost)}


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-正常sealCode
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(okeOrganiztionSealCode)}
        pageNo: 1
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-sealCode为空
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-sealCode为随机值
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${generate_random_str(8)}
        pageNo: 1
        pageSize: 10
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

#- test:
#    name: 查询企业物理印章授权的用印人信息分页列表-sealCode为特殊字符
#    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
#    variables:
#        sealCode: "|*&^%"
#        pageNo: 1
#        pageSize: 10
#
#    validate:
#        - eq: [ "json.code", 1300000 ]
#        - eq: [ "json.message", 'sealCode不能包含特殊字符' ]


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-sealCode 前空格
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: "  ASDS"
        pageNo: 1
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-sealCode后空格
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: "ASDS  "
        pageNo: 1
        pageSize: 10
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-sealCode中间空格
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: "AS  DS"
        pageNo: 1
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]