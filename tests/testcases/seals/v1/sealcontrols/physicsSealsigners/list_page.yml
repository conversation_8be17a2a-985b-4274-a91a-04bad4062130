- config:
    name: "查询企业物理印章授权的用印人信息分页列表"
    base_url: ${ENV(esign.gatewayHost)}


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageNo=1
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(physicalSealCode)}
        pageNo: 1
        pageSize: 10
        sealId: '213213'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageNo=100000
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(physicalSealCode)}
        pageNo: 100000
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageNo为空
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(okeOrganiztionSealCode)}
        pageNo:
        pageSize: 10
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 1300000 ]
        - eq: [ "json.message", '当前页数不能为空' ]


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageSize=0
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(okeOrganiztionSealCode)}
        pageNo: 1
        pageSize: 0
        sealId: '1'
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 1300000 ]
        - eq: [ "json.message", '每页记录数不能小于1' ]


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageSize=100000
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(okeOrganiztionSealCode)}
        pageNo: 1
        pageSize: 100000
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 1300000 ]
        - eq: [ "json.message", '每页记录数不能大于50' ]


- test:
    name: 查询企业物理印章授权的用印人信息分页列表-pageSize为空
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
        sealCode: ${ENV(okeOrganiztionSealCode)}
        pageNo: 1
        pageSize:
        unionSealCode: ${ENV(unionSealCode)}

    validate:
        - eq: [ "json.code", 1300000 ]
        - eq: [ "json.message", '每页记录数不能为空' ]
