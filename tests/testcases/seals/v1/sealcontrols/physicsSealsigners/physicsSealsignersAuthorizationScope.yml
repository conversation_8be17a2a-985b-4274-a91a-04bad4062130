- config:
    name: "物理印章授权"
    variables:
      base_url: ${ENV(esign.gatewayHost)}
      #organizationCode: ${ENV(csqs.orgCode)}
      organizationCode1: ${ENV(sign01.main.orgCode)}
      userCode1: ${ENV(sign01.userCode)}
      sealsignersInfos: [ { "organizationCode": "${ENV(csqs.orgCode)}" },{ "organizationCode": "${ENV(sign01.main.orgCode)}" } ]


####TC1-创建一枚物理模板印章
- test:
    name: TC1-创建一枚自定义物理模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo:  ${ENV(sign01.userCode)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-物理模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 2
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC2-授权物理用印人-MYTL-只填组织编码-组织账号，授权指定组织
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 2
      organizationCode: $organizationCode1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]

- test:
    name: SETUP3-根据sealTC1_001查询groupid
    api: api/esignSeals/seals/enterprise/electronic/groupList.yml
    variables:
      json:
        params:
          sealName: ""
          currPage: 1
          organizationName:
          pageSize: 10
          sealTypeCode:
          sealStatus:
          remoteSealId: $sealTC1_001
          sealPattern:
          defaultSeal: false
          showChildOrganizeSeal:
          myChargeSealGroup: false
        domian: "seal_system"
    extract:
        sealGroupId: content.data.list.0.sealGroupId
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.success, true ]

- test:
    name: SETUP3-根据ex_sealId,sealGroupId查询sealId
    api: api/esignSeals/seals/enterprise/electronic/list.yml
    variables:
      json:
        params:
          sealName: ""
          currPage: 1
          organizationName:
          pageSize: 10
          sealTypeCode:
          sealStatus:
          sealGroupId: $sealGroupId
          remoteSealId: $sealTC1_001
          sealPattern:
        domian: "seal_system"
    extract:
        bizSealId: content.data.list.0.sealId
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.success, true ]



#- test:
#    name: TC4-根据sealId查询bizSealId
#    api: api/esignSeals/seals/enterprise/physical/enterprisePageList.yml
#    variables:
#        remoteSealId: $sealTC1_001
#        currPage: 1
#        organizationName:
#        pageSize: 10
#        sealTypeCode:
#        sealStatus:
#        groupId: $sealGroupId
#    extract:
#      bizSealId: json.data.list.0.sealId
#    validate:
#      - eq: [ content.status, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ json.data.totalCount, 1 ]



- test:
    name: TC5-授权用印人-根据sealId查询物理授权用印人列表
    api: api/esignSeals/v1/sealcontrols/physicsSealsigners/physicsSealsignersList.yml
    variables:
      unionSealCode:
      sealCode: $bizSealId
      pageNo: 1
      pageSize: 10
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ json.data.total, 0 ]

- test:
    name: TC6-物理印章授权用印人-只填userCode,不允许只传userCode，预期结果应该报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3   #授权范围 2-授权所有用户 3-授权指定用户 默认为3 电子印章授权用印人支持授
        sealId: $sealTC1_001   #印章id
        sealsignerType: 2   #授权用印人类型 1-电子印章用印人 2-物理印章用印人 3-应急用印人 默认为1
        sealsignersInfos:
          - customAccountNo:    #用户账号
            organizationCode:    #用户所属企业编码
            customOrgNo:    #用户所属企业账号
            userCode: $userCode1   #用户编码
    validate:
      - eq: [ content.code, 1313033 ]
      - eq: [ content.message, "organizationCode和customOrgNo二选一必填" ]
      - eq: [content.data, null]

- test:
    name: TC7-物理印章授权用印人-授权指定用印人个人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 2
      organizationCode: $organizationCode1
      userCode: $userCode1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [json.data.sealId, $sealTC1_001]


- test:
    name: TC7-授权用印人-MYTL-物理印章授权多个组织编码
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      sealId: $sealTC1_001
      authorizationScope: 3
      sealsignerType: 2
      sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]



- test:
    name: TC8-物理用印对授权组织解绑
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealsignerType: 2
      sealCode:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
      organizationCode: $organizationCode1
    validate:
        - eq: [ content.code, 200 ]
        - contains: [ content.message, "成功" ]
        - eq: [ json.data.sealId, $sealTC1_001 ]

- test:
    name: TC9-授权应急用印人-授权多个组织
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      sealId: $sealTC1_001
      authorizationScope: 3
      sealsignerType: 3
      sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]

- test:
    name: TC10-授权应急用印人-授权指定用户
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      sealId: $sealTC1_001
      authorizationScope: 3
      sealsignerType: 3
      organizationCode: $organizationCode1
      userCode: $userCode1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]