- config:
    name: "印章授权只填写userCode"
    variables:
      base_url: ${ENV(esign.gatewayHost)}
      userCode: ${ENV(csqs.userCode)}

####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo:  ${ENV(sign01.userCode)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC2-授权用印人-只填userCode
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 1
      userCode: $userCode
    validate:
      - eq: [ content.code, 1313033 ]
      - eq: [ content.message, "organizationCode和customOrgNo二选一必填" ]
      - eq: [json.data, ""]