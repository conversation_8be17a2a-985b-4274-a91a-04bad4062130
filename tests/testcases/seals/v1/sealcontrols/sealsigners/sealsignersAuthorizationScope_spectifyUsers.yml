- config:
    name: "电子印章授权指定多个用户,再查询电子印章授权的用印人信息列表"
    variables:
      base_url: ${ENV(esign.gatewayHost)}
      organizationCode: ${ENV(csqs.orgCode)}
      userCode: ${ENV(csqs.userCode)}
      organizationCode1: ${ENV(sign01.main.orgCode)}
      userCode1: ${ENV(sign01.userCode)}
      sealsignersInfos1: [{"organizationCode": $organizationCode,"userCode":$userCode}]
      sealsignersInfos2: [{"organizationCode": $organizationCode,"userCode":$userCode},{"organizationCode": $organizationCode1,"userCode": $userCode1}]

####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo:  ${ENV(sign01.userCode)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC2-授权用印人-MYTL-只填组织编码-组织账号，授权指定用户
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 1
      sealsignersInfos: $sealsignersInfos1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]

- test:
    name: TC3-授权用印人-根据sealId查询授权用印人列表
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ json.data.total, 1 ]
      - eq: [ json.data.sealUseRange, "2" ]
      - eq: [ json.data.authorizationScope, 3 ]

- test:
    name: TC4-授权多个指定用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 1
      sealsignersInfos: $sealsignersInfos2
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]

- test:
    name: TC5-根据sealId查询授权用印人列表
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ json.data.total, 2 ]
      - eq: [ json.data.sealUseRange, "2" ]
      - eq: [ json.data.authorizationScope, 3 ]