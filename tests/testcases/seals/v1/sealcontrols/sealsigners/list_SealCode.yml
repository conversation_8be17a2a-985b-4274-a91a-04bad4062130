- config:
    name: "查询电子印章授权的用印人信息列表"
    base_url: ${ENV(esign.gatewayHost)}

####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚模板印章并发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: ${ENV(csqs.accountNo)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "自动化测试印章（允许删除）"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealCode为空
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealCode为随机值
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ${generate_random_str(8)}
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealCode为特殊字符
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: "|*&^%"
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]


- test:
    name: 查询电子印章授权的用印人信息列表-sealCode 前空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: "  ASDS"
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealCode后空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: "ASDS  "
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealCode中间空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: "AS  DS"
        pageNo: 1
        pageSize: 10
        sealId: $sealTC1_001
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: SETUP-sealId更新印章为停用态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC1_001"
        sealStatus: 3
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]