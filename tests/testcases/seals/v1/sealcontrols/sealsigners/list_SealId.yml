- config:
    name: "查询电子印章授权的用印人信息列表"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      sealName: "SCENE-TC1-模板印章${generate_random_str(8)}"


####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚模板印章并发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: ${ENV(csqs.accountNo)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "自动化测试印章（允许删除）"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: $sealName
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealId_ex: content.data.sealInfos.0.sealId
      groupId_ex: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]



- test:
    name: 根据印章id查询印章code
    api: api/esignSeals/seals/enterprise/electronic/pageElectronicSealList.yml
    variables:
        sealName: $sealName
        currPage: 1
        organizationName:
        sealGroupId: $groupId_ex
        pageSize: 10
        sealTypeCode:
        sealStatus:
        sealId: $sealId_ex
        sealPattern:
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - len_gt: [$message,1]


- test:
    name: 查询电子印章授权的用印人信息列表-正常sealId
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealId_ex
    validate:
        - eq: [ "json.code", 200]
        - contains: [ "json.message", '成功']
        - len_gt: ["json.data",1]

#- test:
#    name: 查询电子印章授权的用印人信息列表-sealId为空
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#        sealCode: $sealCode1
#        pageNo: 1
#        pageSize: 10
#        sealId: ''
#    validate:
#        - eq: [ "json.code", 200]
#        - contains: [ "json.message", '成功']

- test:
    name: 查询电子印章授权的用印人信息列表-sealId和sealcode都为空
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode:
        pageNo: 1
        pageSize: 10
        sealId: ''
    validate:
        - eq: [ "json.code", 1913014 ]
        - eq: [ "json.message", 'sealCode和sealId二选一必填' ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealId为随机值
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealId: ${generate_random_str(8)}
        pageNo: 1
        pageSize: 10
    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

#- test:
#    name: 查询电子印章授权的用印人信息列表-sealId为特殊字符
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#        sealCode:
#        pageNo: 1
#        pageSize: 10
#        sealId: "|*&^%"
#
#    validate:
#        - eq: [ "json.code", 19***** ]
#        - eq: [ "json.message", 'sealId不能为特殊字符' ]
#
#- test:
#    name: 查询电子印章授权的用印人信息列表-sealId为特殊字符-sql注入
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#        sealCode:
#        pageNo: 1
#        pageSize: 10
#        sealId: "' or 1=1"
#
#    validate:
#        - eq: [ "json.code", 1300000
#        - eq: [ "json.message", '包含非法字符，存在sql注入语句' ]


- test:
    name: 查询电子印章授权的用印人信息列表-sealId 前空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealId: "  ASDS"
        pageNo: 1
        pageSize: 10
        sealCode: ""

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealId后空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: "ASAS  "

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]

- test:
    name: 查询电子印章授权的用印人信息列表-sealId中间空格
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: 'AS  DS'

    validate:
        - eq: [ "json.code", 200 ]
        - eq: [ "json.message", '成功' ]
        - len_gt: [ "json.data",1 ]