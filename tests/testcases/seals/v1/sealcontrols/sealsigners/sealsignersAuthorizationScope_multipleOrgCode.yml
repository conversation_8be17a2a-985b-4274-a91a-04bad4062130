- config:
    name: "印章授权多个组织编码，再查询印章授权结果"
    variables:
      base_url: ${ENV(esign.gatewayHost)}
      #organizationCode: ${ENV(csqs.orgCode)}
      #organizationCode1: ${ENV(sign01.main.orgCode)}
      sealsignersInfos: [ { "organizationCode": "${ENV(csqs.orgCode)}" },{ "organizationCode": "${ENV(sign01.main.orgCode)}"} ]



####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo:  ${ENV(sign01.userCode)}
        organizationCode: ${ENV(csqs.orgCode)}
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC2-授权用印人-MYTL-印章授权多个组织编码
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC1_001
      sealsignerType: 1
      sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ json.data.sealId, 1 ]


- test:
    name: TC3-授权用印人-根据sealId查询授权用印人列表
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
     # sealCode:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
    extract:
      organizationCodeEX1: json.data.records.0.organizationCode
      organizationCodeEX2: json.data.records.1.organizationCode
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
#      - eq: [ json.data.total, 2 ]
      - eq: [ json.data.sealUseRange, "2" ]
      - eq: [ json.data.authorizationScope, 3 ]

- test:
    name: TC4-解绑TC3中的一个用印组织
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealCode:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
      organizationCode: $organizationCodeEX1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ json.data.sealId, $sealTC1_001]

- test:
    name: TC5-授权用印人-根据sealId查询授权用印人列表
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
     # sealCode:
      sealId: $sealTC1_001
      pageNo: 1
      pageSize: 10
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ json.data.total, 1 ]
      - eq: [ json.data.sealUseRange, "2" ]
      - eq: [ json.data.authorizationScope, 3 ]
