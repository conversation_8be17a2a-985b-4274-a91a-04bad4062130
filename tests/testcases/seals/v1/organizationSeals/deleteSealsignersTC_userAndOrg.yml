- config:
    name: "授权用印人"
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customAccountNo: ""
      customOrgNo: ""
      authorizationScope: 3
      sealsignerType: 1
      organizationCode: $org01Code
      userCode: $sign01Code
      code0: 200
      message0: "成功"
      data0: 1
      name: "授权用印人-校验用印人关系"

- test:
    name: $name
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: $authorizationScope
        sealId: $sealId   #印章id
        sealsignerType: $sealsignerType   #授权用印人类型 1-电子印章用印人 2-物理印章用印人 3-应急用印人 默认为1
        sealsignersInfos:
          - customAccountNo: $customAccountNo   #用户账号
            organizationCode: $organizationCode   #用户所属企业编码
            customOrgNo: $customOrgNo   #用户所属企业账号
            userCode: $userCode   #用户编码
    validate:
      - eq: [ content.code, $code0 ]
      - contains: [ content.message, $message0 ]
      - eq: [ content.data, null ]