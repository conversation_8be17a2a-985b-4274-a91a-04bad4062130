- config:
    name: "更新企业印章-sealRelease字段校验"
    variables:
      sign01No: ${ENV(csqs.accountNo)}
      sign01Code: ${ENV(csqs.userCode)}
      customOrgNo: ${ENV(sign01.main.orgNo)}
      sign01_sealId: ${ENV(sign01.sealId)}
      sealGroupName1: "印章分组自动化可删-${generate_random_str(6)}"
#      SP: "  "

- test:
    name: SETUP-创建一枚印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: $sealGroupName1
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 1
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC-模板印章1"
            sealSource: 2
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
      sealGroupId_001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
    extract:
      sealId_001: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章-sealRelease传字母,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "aqq"
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章-sealRelease传汉字,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "汉字汉字"
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章-sealRelease传入特殊字符，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "<>|"
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章正常发布-sealRelease传（非0非1数字）-1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: -1
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]
    setup_hooks:
      - ${sleep(10)}

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章正常发布-sealRelease传（非0非1数字）19
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: 19
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]



- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 企业印章正常发布-sealRelease为null发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: null
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: 更新企业印章正常发布-sealRelease为空，发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: ""
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]




- test:
    name: 更新企业印章-sealRelease传入false，未发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: false
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "3"]

- test:
    name: 更新企业印章-sealRelease传入0，未发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: 0
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "3"]




- test:
    name: 更新企业印章-sealRelease传入true，发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: true
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A1"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章0001"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
- test:
    name: 更新企业印章-sealRelease传入1，发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: 1
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-AA001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-AA更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]
- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章-sealRelease传入字符1，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "1"
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]



- test:
    name: 更新企业印章-sealRelease传入1前后有空格，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "           1            "
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-AA001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-AA更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]


- test:
    name: 更新企业印章-sealRelease传入字符0，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "0"
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-A22001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-A22更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]



- test:
    name: 更新企业印章-sealRelease传入字母0前后有空格，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: "          0              "
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-A2001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-A2更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]


- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_001
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]