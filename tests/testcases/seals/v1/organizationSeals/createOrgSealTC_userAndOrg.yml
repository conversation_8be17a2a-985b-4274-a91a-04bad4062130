- config:
    name: "创建企业印章"
    variables:
      base64img:
      sealHeight: 40
      sealGroupName: "印控自动化创建"
      sealTemplateStyle: 3
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealTypeCode: "COMMON-SEAL"
      sealOpacity:
      sealSource:
      legalSealId:
      sealWidth: 40
      sealUpperText: "自动化测试专用章"
      sealFileKey:
      sealOldStyle:
      sealPattern: 1
      sealGroupDesc: "自动化测试专用的组"
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "自动化测试专用章"
      sealBottomText:
      sealSignercertId:
      sealShape:
      sealColour: 1
      code0: 200
      message0: "成功"
      data0: 1
      name: "创建企业印章-验证组织用户和法人印章信息"
      sealFileKey0: ""  ##临时数据，后续bug修复完成可以为空
- test:
    name: $name
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $customAccountNo
        customOrgNo: $customOrgNo
        organizationCode: $organizationCode
        sealGroupName: "create-userOrg"
        sealInfos:
          - sealUpperText: "校验组织用户信息和法人印章ID"
            sealPattern: 1
            sealHeight: 40
            sealTemplateStyle: 1
            sealFileKey: ""
            sealName: "测试"
            sealSource: 2
            legalSealId: $legalSealId
            sealWidth: 40
        sealRelease: 1
        sealTypeCode: $sealTypeCode
        userCode: $userCode
    validate:
      - eq: [ content.code, $code0 ]
      - contains: [ content.message, $message0 ]
      - ne: [ content.data, $data0 ]