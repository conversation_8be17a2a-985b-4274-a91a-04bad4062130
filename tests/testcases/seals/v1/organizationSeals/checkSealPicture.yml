- config:
    name: "校验创建的印章生成的印章图片文字内容是否正确-线上Bug:https://forward.esign.cn/mainBugManagement/edit?id=26884&type=check"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sealUpperText1: "测试印章上弦文"
      sealBottomText1: "测试印章下弦文"
      sealHorizontalText1: "测试印章横向文"

####TC1-创建一枚模板印章，模板样式一，有上弦文、下弦文、横向文
- test:
    name: TC1-创建一枚模板印章,有上弦文、下弦文、横向文
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: $sealUpperText1
            sealBottomText: $sealBottomText1
            sealHorizontalText: $sealHorizontalText1
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealGroupId001: content.data.sealGroupId
      seal001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: SETUP-OPENAPI的sealId对应页面的remoteSealId需要调用页面接口转换成页面的sealId
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: $sealId001
    extract:
      ex_sealId001: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: 查看印章详情-创建的印章为默认印章
    api: api/esignSeals/seals/enterprise/electronic/getSealDetailsById.yml
    variables:
      "id": $ex_sealId001
      "disablePhysicalSeal": false
    extract:
      cryptoSealThumbnailFileKey001: content.data.cryptoSealThumbnailFileKey
      sealThumbnailFileKey001: content.data.sealThumbnailFileKey
    validate:
        - eq: [ json.status, 200]
        - eq: [ json.success, true]
        - contains: [json.message,"成功"]
        - eq: [ json.data.defaultSealFlag, true]











