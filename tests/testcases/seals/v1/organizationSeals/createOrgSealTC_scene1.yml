- config:
    name: "创建企业印章-印章场景--sealRelease、sealFileKey、base64img、sealSignercertId、sealColour"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}
      jpgFile: tests/data/psb.jpg
      base64file: ${get_file_base64($jpgFile)}
####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章sealFileKey不填
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: ""
            sealPattern: 1
            base64img: ""
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    validate:
      - eq: [ content.code, 1325028 ]
      - contains: [ content.message, "自定义印章，印章fileKey和印章base64不能都为空" ]
      - eq: [content.data, null ]
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章，有sealFileKey
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: ""
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
      sealTC1_002: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-sealTC001-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC1_001
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 1 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
      - eq: [ content.data.records.0.sealId, $sealTC1_001]
      - eq: [ content.data.records.0.sealStatus, "2" ]

- test:
    name: TC1-创建一枚自定义印章和一枚模板印章--无sealFileKey，有base64img,但是不对
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: ""
            sealPattern: 1
            base64img: "a4bc2794cc8c3bfc629dcdc726f1991b8d9ebf8041493454dd63670e8d4590cf"
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    validate:
      - eq: [ content.code, 1325075 ]
      - contains: [ content.message, "base64img错误：自定义印章获取宽高失败" ]
      - eq: [content.data, null ]

- test:
    name: TC1-创建一枚自定义印章和一枚模板印章--有sealFileKey，有base64img
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: "$base64file"
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_003: content.data.sealInfos.0.sealId
      sealTC1_004: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-sealTC001-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC1_004
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 1 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
      - eq: [ content.data.records.0.sealId, $sealTC1_004 ]
      - eq: [ content.data.records.0.sealStatus, "2" ]

- test:
    name: TC1-创建一枚自定义印章和一枚模板印章，sealFileKey、base64img都有--sealRelease=0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: "$base64file"
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_005: content.data.sealInfos.0.sealId
      sealTC1_006: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: TC-sealTC001-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC1_006
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records,[] ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户

- test:
    name: TC1-创建一枚自定义印章和一枚模板印章，sealFileKey、base64img都有--sealRelease=1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 1
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: "$base64file"
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_007: content.data.sealInfos.0.sealId
      sealTC1_008: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-sealTC001-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC1_007
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 1 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
      - eq: [ content.data.records.0.sealId, $sealTC1_007 ]
      - eq: [ content.data.records.0.sealStatus, "2" ]