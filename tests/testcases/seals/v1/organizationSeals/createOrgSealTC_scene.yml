- config:
    name: "创建企业印章-印章场景"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}

####TC1-创建一枚自定义印章和一枚模板印章
- test:
    name: TC1-创建一枚自定义印章和一枚模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: ""
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
      sealTC1_002: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

####TC3-创建一枚只有必填内容，其他都是默认的模板印章
- test:
    name: TC3-创建一枚只有必填内容，其他都是默认的模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

####TC4-创建多枚国际标准印章
- test:
    name: TC4-创建多枚国际标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "SCENE-TC4-模板印章2"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章3"
            sealSource: 2
    extract:
      sealTC4_001: content.data.sealInfos.0.sealId
      sealTC4_002: content.data.sealInfos.1.sealId
      sealTC4_003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

####TC5-创建多枚中国标准印章
- test:
    name: TC5-创建多枚中国标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 3
            sealTemplateStyle: 1
            sealName: "SCENE-TC5-模板印章1"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 1
            sealFileKey: "${ENV(pngPageFileKey)}"
          - sealPattern: 3
            sealTemplateStyle: 2
            sealName: "SCENE-TC5-模板印章2"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
          - sealPattern: 3
            sealTemplateStyle: 3
            sealName: "SCENE-TC5-模板印章3"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
    extract:
      sealTC5_001: content.data.sealInfos.0.sealId
      sealTC5_002: content.data.sealInfos.1.sealId
      sealTC5_003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]
####TC6-创建sealInfos空数组
- test:
    name: TC6-创建sealInfos空数组
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos: [ ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - not_equals: [ content.data, "" ]

- test:
    name: TC7-创建sealInfos空对象
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos: [ { } ]
    extract:
      sealTC7_001: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC8-不能制作物理的动态模板印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 2
            sealTemplateStyle: 6
            sealSource: 2
    validate:
      - eq: [ content.code, 1325060 ]
      - eq: [ content.message, "sealTemplateStyle错误：模版样式{6}物理章不支持动态模版印章" ]
      - eq: [content.data, null ]

- test:
    name: TC9-制作物理印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 2
            sealTemplateStyle: 2
            sealSource: 2
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC10-设置类型为法人印章，其他模板印章的参数乱填不生效
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealInfos:
          - sealUpperText: "随意传值的信息"
            sealFileKey: ""
            sealOldStyle: 2
            sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
            base64img: "123456"
            defaultSeal: 0
            sealHeight: -1
            sealMaterial: 6
            sealTemplateStyle: 6
            sealName: $sealName   #印章名称，不超过100字；默认取印章分组名称
            sealShape: 5
            sealColour: 5
            sealOpacity: 0.01
            sealSource: 0
            legalSealId: $sign01_sealId   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
            sealWidth: -1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC11-创建中国标准的印章，制章者所属企业没有可用的sm2证书
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: ${ENV(sign03.accountNo)}
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
            sealTemplateStyle: 3
            sealName: "没有签章者证书"
    validate:
      - eq: [ content.code, 1325010 ]
      - eq: [ content.message, "创建人所属企业没有可用的制章者证书" ]
      - eq: [content.data, null ]

- test:
    name: TC12-创建中国标准的印章，企业没有sm2证书
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: ${ENV(sign01.JZ.orgNo)}
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
            sealTemplateStyle: 3
            sealName: "没有制章者证书"
    validate:
      - eq: [ content.code, 1325040 ]
      - eq: [ content.message, "印章所属企业没有签章者证书" ]
      - eq: [content.data, null ]

- test:
    name: SETUP-sealId更新印章为停用态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC7_001
        sealStatus: 3
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: TEARDOWN-删除印章-已停用不能删除
    api: api/esignSeals/v1/sealcontrols/organizationSeals/delete.yml
    variables:
      json:
        sealId: $sealTC7_001
    validate:
      - eq: [ content.code, 1313047 ]
      - contains: [ content.message, "非待发布状态，不能删除" ]
      - eq: [content.data, null ]


- test:
    name: 印章名称超过100个字符
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是已"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    validate:
      - eq: [ content.code, 1325007 ]
      - contains: [ content.message, "不能超过100字" ]
      - eq: [ content.data, null ]