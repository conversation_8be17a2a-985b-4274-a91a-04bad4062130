- config:
    name: "更新企业印章-sealOpacity(透明度)字段校验"
    variables:
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      sign01No: ${ENV(csqs.accountNo)}
      sign01Code: ${ENV(csqs.userCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      legalSealId1: ${ENV(org01.legal.sealId)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}
      sealGroupName1: "印章分组自动化可删-${generate_random_str(6)}"
      SP: "  "

- test:
    name: SETUP-创建一枚印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: $sealGroupName1
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 1
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC-模板印章1"
            sealSource: 2
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
      sealGroupId_001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
    extract:
      sealId_001: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章-sealOpacity传字母,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      ensealUpperText:
      sealOpacity: "aa"
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章-sealOpacity传汉字,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: "汉字汉字"
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章-sealOpacity传入特殊字符，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: "<>|"
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 1322222]
      - eq: [content.message, "不支持传入特殊字符"]
      - eq: [content.data, null]

- test:
    name: 更新企业印章正常发布-sealOpacity传数字19，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 19
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1325016]
      - eq: [content.message, "sealOpacity错误：印章不透明度{19}超出区间[20-100]"]
      - eq: [content.data, null]
    setup_hooks:
      - ${sleep(2)}


-  test:
    name: 更新企业印章正常发布-sealOpacity传数字101，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 101
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
      - eq: [content.code, 1325016]
      - eq: [content.message, "sealOpacity错误：印章不透明度{101}超出区间[20-100]"]
      - eq: [content.data, null]
    setup_hooks:
      - ${sleep(1)}



- test:
    name: 企业印章正常发布-sealOpacity传null默认100，
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询印章详情--印章透明度0.2
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_001]
      - eq: [ content.data.sealOpacity, 1.0]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: 更新企业印章正常发布-sealOpacity为空默认100
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: ""
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询印章详情--印章透明度1.0
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_001]
      - eq: [ content.data.sealOpacity, 1.0]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]




- test:
    name: 更新企业印章-sealOpacity传20正常
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 20
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询印章详情--印章透明度0.2
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_001]
      - eq: [ content.data.sealOpacity, 1.0]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章-sealOpacity传入100，正常
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 100
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询印章详情--印章透明度100%（1.0）
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_001]
      - eq: [ content.data.sealOpacity, 1.0]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]



- test:
    name: 更新企业印章-sealOpacity传入数字前后有空格，忽略空格
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 30$SP
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-A2001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-A2更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealTC1_001]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询印章详情--印章透明度0.3
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_001]
      - eq: [ content.data.sealOpacity, 1.0]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_001
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]

- test:
    name: TC-查询印章分组
    api: api/esignSeals/seals/smc/seals/group/listPageGroup.yml
    variables:
       myChargeSealGroup: false
       currPage: 1
       defaultSeal: false
       pageSize: 10
       remoteSealId: $legalSealId1
       sealName: ""
       sealPatterns: []
       sealStatus: ""
    extract:
      sealGroupId001: content.data.list.0.sealGroupId
    validate:
      - eq: [json.status, 200]
      - eq: [json.success, true]
      - ne: [json.data, ""]

- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      remoteSealId: $legalSealId1
    extract:
      sealId_002: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: TC-查询企业法人印章详情--印章透明度为null
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_002
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_002]
      - eq: [ content.data.sealOpacity, null]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $legalSealId1
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业法人印章-sealOpacity传入30，不起作用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $legalSealId1
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 30
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-A2001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-A2更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $legalSealId1]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询企业法人印章详情--印章透明度为null
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_002
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_002]
      - eq: [ content.data.sealOpacity, null]

- test:
    name: TC-创建一枚自定义图片印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealFileKey: "${ENV(pngPageFileKey)}"
            sealPattern: 1
            base64img: ""
            defaultSeal: 0
            sealHeight: 50
            sealName: "SCENE-TC1-自定义印章"
            sealSource: 1
            sealWidth: 50
    extract:
      sealIdTC_003: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-查询印章分组
    api: api/esignSeals/seals/smc/seals/group/listPageGroup.yml
    variables:
       myChargeSealGroup: false
       currPage: 1
       defaultSeal: false
       pageSize: 10
       remoteSealId: $sealIdTC_003
       sealName: ""
       sealPatterns: []
       sealStatus: ""
    extract:
      sealGroupId003: content.data.list.0.sealGroupId
    validate:
      - eq: [json.status, 200]
      - eq: [json.success, true]
      - ne: [json.data, ""]

- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId003
      remoteSealId: $sealIdTC_003
    extract:
      sealId_003: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: TC-查询企业上传图片印章详情--印章透明度为null
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_003
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_003]
      - eq: [ content.data.sealOpacity, 1.0]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealIdTC_003
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新上传图片印章-sealOpacity传入50，不起作用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealIdTC_003
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity: 50
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新测试-A2001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "T-A2更新印章"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, 成功]
      - eq: [content.data.sealId, $sealIdTC_003]
      - eq: [content.data.sealStatus, "2"]

- test:
    name: TC-查询企业上传图片印章详情--印章透明度为null
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $sealId_003
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.sealId, $sealId_003]
      - eq: [ content.data.sealOpacity, 1.0]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealIdTC_003
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_003
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]
