- config:
    name: "更新企业印章-印章明细"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      name: "更新企业印章"
      sealId: ""
      sealHeight: 40
      sealGroupName: "印控自动化创建"
      userCode: $sign01Code
      sealTemplateStyle: 3
      customAccountNo: $sign01No
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease: 0
      ensealUpperText:
      org01No: ${ENV(sign01.main.orgNo)}
      sealTypeCode: "COMMON-SEAL"
      sign01Code: ${ENV(sign01.userCode)}
      sealOpacity:
      sealSource:
      legalSealId:
      sealWidth: 40
      sealUpperText: "自动化测试专用章"
      sealFileKey:
      sealOldStyle: 0
      sign01No: ${ENV(sign01.accountNo)}
      sealPattern: 1
      sealGroupDesc: "自动化测试专用的组"
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "自动化测试专用章"
      sealBottomText:
      organizationCode: $org01Code
      sealSignercertId:
      sealShape:
      signerCertInfos:
      sealColour: 1
      org01Code: ${ENV(sign01.main.orgCode)}
      customOrgNo: $org01No
      code0: 200
      message0: "成功"
      data0: 1
- test:
    name: $name
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealId
        sealRelease: $sealRelease
        sealUpperText: $sealUpperText   #上弦文（50字） 不传取印章所属企业名称；sealTypeCode为法人章时不
        sealFileKey: $sealFileKey   #印章filekey 章面来源为2-自定义印章时filekey或base64二选
        sealOldStyle: $sealOldStyle   #印章图片做旧 0否，1是；默认为0 sealTypeCode为法人章时不生效；
        signerCertInfos: $signerCertInfos   #签章者证书id，不传取印章创建人的任意一本有效云证书
        sealPattern: $sealPattern   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        base64img: $base64img   #印章base64 章面来源为2-自定义印章时filekey或base64二选一
        defaultSeal: $defaultSeal   #是否为默认印章 0否，1是；默认为0  同一机构同一印章类型同一印章形态只能有一
        sealHeight: $sealHeight   #印章高度（mm） 默认30.sealTypeCode为法人章时不生效
        sealMaterial: $sealMaterial   #印章材质 1-橡胶章 2-牛角章 3-铜章 4-回墨章  5-光敏章
        sealHorizontalTextfirst: $sealHorizontalTextfirst   #横向文一（不超过20字）
        sealTemplateStyle: $sealTemplateStyle   #模板样式 1-椭圆形章带五角星 2-椭圆形章不带五角星 3-圆形章带五角星
        sealMakerCertId: $sealMakerCertId   #国密印章绑定的制章者证书id。不传取印章创建人所属/兼职企业的任意一本
        sealName: $sealName   #印章名称，不超过30字；默认取印章分组名称
        sealHorizontalText: $sealHorizontalText   #横向文（15字）
        sealHorizontalTextsecond: $sealHorizontalTextsecond   #横向文二（不超过20字）
        sealBottomText: $sealBottomText   #下弦文（15字）
        ensealUpperText: $ensealUpperText   #英文上弦文（50字）
        sealShape: $sealShape   #印章形状 1-椭圆形 2-圆形 3-长方形 4-正方形 默认取1；
        sealColour: $sealColour   #印章颜色 1-红色 2-蓝色 3-黑色 4-紫色 默认为1.
        sealOpacity: $sealOpacity   #不透明度（%） 0～100之间，默认为100。
        sealSource: $sealSource   #章面来源 1-模板制章 2-上传图片 默认取1；
        legalSealId: $legalSealId   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
        sealWidth: $sealWidth   #印章宽度（mm） 默认40.sealTypeCode为法人章时不生效
    validate:
      - eq: [ content.code, $code0 ]
      - contains: [ content.message, $message0 ]
      - ne: [ content.data, $data0 ]