- config:
    name: "授权用印人-sealId验证"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}
      sealIdDraf: ${getOrganizationSealsByStatus(, 1)}
      sealIdRevoke: ${getOrganizationSealsByStatus(, 4)}
      sealsignersInfos:
        - organizationCode: $org01Code
          userCode: $sign01Code

- test:
    name: SETUP-创建多枚国际标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章1"
            sealSource: 2
    extract:
      sealTC002: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: SETUP-创建待发布的印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-005"
            sealSource: 2
    extract:
      sealTC005: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: sealId为空
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: ""
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不能为空" ]
      - eq: [content.data, null ]

- test:
    name: sealId为null
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: null
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不能为空" ]
      - eq: [content.data, null ]

- test:
    name: sealId为不存在
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: "XXXX"
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1313017 ]
      - eq: [ content.message, "该企业印章[XXXX]不存在" ]
      - eq: [content.data, null ]

- test:
    name: 授权用印人印章sealId特殊字符异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: '!@#\\/:*?\"<>|'
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId英文全角异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: ｑｕａｎｊｉａｏ
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId英文大小写异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: Test
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId前后空格异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: ' banjiao '
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId繁体字异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: 發纔
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId新疆名异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: 达吾提·阿西木
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId英文名异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: Rosalette Hazal Royston
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1313017]
    - contains: [ content.message, "不存在" ]
    - eq: [content.data, null ]
- test:
    name: 授权用印人印章sealId长度101异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      sealId: abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a
      sealsignersInfos: $sealsignersInfos
    validate:
    - eq: [content.code, 1300000]
    - contains: [ content.message, "印章id不超过36个字" ]
    - eq: [content.data, null ]

- test:
    name: SETUP-sealId更新印章为停用态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC002"
        sealStatus: 3
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: TC2-sealId为已停用状态，可以解绑用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: "$sealTC002"
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1313045 ]
      - contains: [ content.message, "]尚未绑定用印人无需解绑" ]
      - eq: [content.data, null ]

- test:
    name: TC4-sealId草稿态，可以解绑用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: "$sealIdDraf"
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1313044 ]
      - contains: [ content.message, "状态草稿不支持解绑用印人" ]
      - eq: [content.data, null ]

- test:
    name: TC5-sealId吊销态，可以解绑用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: "$sealIdRevoke"
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1313044 ]
      - contains: [ content.message, "]的状态吊销不支持解绑用印人" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealId待发布态，可以解绑用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealsignersInfos: $sealsignersInfos
    validate:
      - eq: [ content.code, 1313045 ]
      - contains: [ content.message, "]尚未绑定用印人无需解绑" ]
      - eq: [content.data, null ]


