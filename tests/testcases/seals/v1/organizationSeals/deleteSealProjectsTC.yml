- config:
    name: "删除印章可用授权项目"
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      organizationCode: $org01Code
      userCode: $sign01Code
      projectId1: ${ENV(esign.projectId)}
      num: "${get_randomNo()}"
      projectName1: "测试可用项目1$num"
      projectName2: "测试可用项目2$num"

- test:
    name: SETUP-创建多枚国际标准印章,默认授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "SCENE-电子印章"
            sealSource: 2
            sealMaterial: 1
            sealShape: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章3"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
      sealTC003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC-projectScope传入非1和2数字，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 3
      sealId: $sealTC001
      sealProjectIds: ["$projectId1"]
    validate:
      - eq: [ content.code, 1313050 ]
      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[3]" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-projectScope传入字母，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: qa
      sealId: $sealTC001
      sealProjectIds: ["$projectId1"]
    validate:
      - eq: [ content.code, 1313050 ]
      - contains: [ content.message, "projectScope错误：可用项目授权范围不支持传入" ]
      - ne: [ content.data, "" ]

- test:
    name: TC-projectScope传入中文，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 中
      sealId: $sealTC001
      sealProjectIds: ["$projectId1"]
    validate:
      - eq: [ content.code, 1313050 ]
      - contains: [ content.message, "projectScope错误：可用项目授权范围不支持传入" ]
      - ne: [ content.data, "" ]


- test:
    name: TC-projectScope传入字符，报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: "%$"
      sealId: $sealTC001
      sealProjectIds: ["$projectId1"]
    validate:
      - eq: [ content.code, 1313050 ]
      - contains: [ content.message, "projectScope错误：可用项目授权范围不支持传入" ]
      - ne: [ content.data, "" ]


- test:
    name: TC-projectScope传入空，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]


- test:
    name: TC-projectScope传入null，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: null
      sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-projectScope不传，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]


- test:
    name: TC-印章1授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC001]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章2授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC002]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章3授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC003]
      - eq: [ content.data.projectScope,"1"]



- test:
    name: TC-projectScope传入空，sealProjectIds传入字母，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
      sealProjectIds: ["aa"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]

- test:
    name: TC-projectScope传入空，sealProjectIds传入项目id，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC002
      sealProjectIds: ["$projectId1"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-projectScope传入""，sealProjectIds传入字符，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC003
      sealProjectIds: [ "￥@" ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-印章1授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC001]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章2授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC002]
      - eq: [ content.data.projectScope, "1"]
- test:
    name: TC-印章3授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC003]
      - eq: [ content.data.projectScope, "1"]


- test:
    name: TC-projectScope传入空，sealProjectIds传入中文，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
      sealProjectIds: ["中国"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]

- test:
    name: TC-projectScope传入1，sealProjectIds传入null，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC002
      sealProjectIds: []
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-projectScope传入1，sealProjectIds传入""，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC003
      sealProjectIds: [ "" ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, "" ]





- test:
    name: TC-印章1授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC001]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章2授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC002]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章3授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC003]
      - eq: [ content.data.projectScope, "1"]


- test:
    name: TC-projectScope传入1，sealProjectIds传入中文，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
      sealProjectIds: ["中国"]
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data, "" ]

- test:
    name: TC-projectScope传入1，sealProjectIds传入字母，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC002
      sealProjectIds: ["aa"]
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-projectScope传入1，sealProjectIds传入字符，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC003
      sealProjectIds: [ "@#" ]
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data, "" ]



- test:
    name: TC-印章1授权全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: ""
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC001]
      - eq: [ content.data.projectScope, "1"]

- test:
    name: TC-印章2授权项目1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: [ "$projectId1" ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC002]
      - eq: [ content.data.projectScope, "2"]

- test:
    name: TC-印章3授权项目1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC003
      sealProjectIds: [ "$projectId1" ]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, $sealTC003]
      - eq: [ content.data.projectScope, "2"]


- test:
    name: TC-projectScope传入2，印章绑定全部项目，sealProjectIds传"",报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC001
      sealProjectIds: [""]
    validate:
      - eq: [ content.code, 1904066 ]
      - contains: [ content.message, "项目ID" ]
      - ne: [ content.data, "" ]

- test:
    name: TC-projectScope传入2，印章绑定全部项目，sealProjectIds传null,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC001
      sealProjectIds: []
    validate:
      - eq: [ content.code, 1313051 ]
      - eq: [ content.message, "授权可用项目集合不能为空" ]
      - eq: [ content.data, null ]


- test:
    name: TC-projectScope传入2，印章绑定全部项目，sealProjectIds不传,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 1313051 ]
      - eq: [ content.message, "授权可用项目集合不能为空" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传非项目1
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: ["1000001"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, "2" ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC002
      sealProjectIds: ["1000001"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, "2" ]

- test:
    name: TC-projectScope传入2，印章未绑定项目，解绑全部项目
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 1
      sealId: $sealTC002
      sealProjectIds: ["1000001"]
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, "2" ]

- test:
    name: TC-projectScope传入2，印章未绑定项目，解绑指定项目,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: [ "1000001" ]
    validate:
      - eq: [ content.code, 1313054 ]
      - contains: [ content.message, "暂未授权可用项目，无需解绑" ]
      - eq: [ content.data, null ]
- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds不存在,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: ["999990001"]
    validate:
      - eq: [ content.code, 1904066 ]
      - eq: [ content.message, "项目ID 999990001 不存在" ]
      - eq: [ content.data, null ]


- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传字母,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: ["aa"]
    validate:
      - eq: [ content.code, 1904066 ]
      - eq: [ content.message, "项目ID aa 不存在" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传汉字,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: ["多少度"]
    validate:
      - eq: [ content.code, 1904066 ]
      - eq: [ content.message, "项目ID 多少度 不存在" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传字符,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: ["%%"]
    validate:
      - eq: [ content.code, 1904066 ]
      - eq: [ content.message, "项目ID %% 不存在" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传"",报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: [""]
    validate:
      - eq: [ content.code, 1904066 ]
      - contains: [ content.message, "项目ID" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传null,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
      sealProjectIds: []
    validate:
      - eq: [ content.code, 1313051 ]
      - eq: [ content.message, "授权可用项目集合不能为空" ]
      - eq: [ content.data, null ]

- test:
    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds不传,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
    variables:
      projectScope: 2
      sealId: $sealTC002
    validate:
      - eq: [ content.code, 1313051 ]
      - eq: [ content.message, "授权可用项目集合不能为空" ]
      - eq: [ content.data, null ]





#- test:
#    name: setup-getProjectConfigPageList-1000000列表
#    variables:
#      projectId_list: 1000000
#    api: api/esignManage/proconfig/getProjectConfigPageList.yml
#    extract:
#      - _id: content.data.list.0.id
#      - _projectId: content.data.list.0.projectId
#      - _projectSecret: content.data.list.0.projectSecret
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - ne: [ content.data.list, [ ] ]
#      - len_eq: [ content.data.list, 1 ]
#      - eq: [ content.data.list.0.projectStatus, "1" ]
#
#- test:
#    name: setup-getProjectConfigInfo-1000000详情
#    variables:
#      id_detail: $_id
#    api: api/esignManage/proconfig/projectConfig/getProjectConfigInfo.yml
#    extract:
#      - _esignAppId: content.data.esignAppId
#      - _esignAppSecret: content.data.esignAppSecret
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.data.projectId, "$_projectId" ]
#      - eq: [ content.data.id, "$_id" ]
#      - ne: [ content.data.esignAppId, "" ]
#      - ne: [ content.data.esignAppSecret, "" ]
#
#- test:
#    name: setup-saveProjectConfigInfo-新建项目1
#    variables:
#      projectName_save: $projectName1
#      projectDesc_save: "python测试项目"
#      esignAppId_save: "$_esignAppId"
#      esignAppSecret_save: "$_esignAppSecret"
#      internalSignUrl_save: "1"
#      openApiAuthStatus_save: "1"
#    api: api/esignManage/proconfig/projectConfig/saveProjectConfigInfo.yml
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.data,"" ]
#
#- test:
#    name: getProjectConfigPageList-新项目1
#    variables:
#      projectName_list: $projectName1
#    api: api/esignManage/proconfig/getProjectConfigPageList.yml
#    extract:
#      - _id1: content.data.list.0.id
#      - _projectId1: content.data.list.0.projectId
#      - _projectSecret1: content.data.list.0.projectSecret
#      - _projectName1: content.data.list.0.projectName
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - ne: [ content.data.list, [ ] ]
#      - len_eq: [ content.data.list, 1 ]
#      - eq: [ content.data.list.0.projectStatus, "1" ]
#      - eq: [ content.data.list.0.projectName, $projectName1 ]
#      - ne: [ content.data.list.0.projectId, "" ]
#      - ne: [ content.data.list.0.createTime, "" ]
#
#
#- test:
#    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传新项目1,报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC002
#      sealProjectIds: ["$_projectId1"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "不支持解绑项目" ]
#      - ne: [ content.data, "" ]
#
#
#- test:
#    name: TC-projectScope传入2，印章2绑定多个项目，项目1、新项目1
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: [ "$projectId1","$_projectId1" ]
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data, "" ]
#
#- test:
#    name: TC-验证查询用户的企业授权印章列表-授权指定项目1、新项目1
#    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
#    variables:
#      json: {
#        pageNo: 1,
#        pageSize: 10,
#        sealPattern: 1,
#        sealId: $sealTC001
#      }
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[$projectId1,$_projectId1]" ]
#      - eq: [ content.data.records.0.sealId, $sealTC001 ]
#      - eq: [ content.data.records.total, 1 ]
#
#
#- test:
#    name: TC-验证查询电子印章授权的用印人信息列表-授权指定项目项目1、新项目1
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#      sealCode: ""
#      pageNo: 1
#      pageSize: 10
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[$projectId1,$_projectId1]" ]
#      - eq: [ content.data.records.total, 1 ]
#
#- test:
#    name: TC-projectScope传入2，印章绑定项目1，sealProjectIds传项目1，解绑成功
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC002
#      sealProjectIds: ["$projectId1"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: TC-验证查询用户的企业授权印章列表-授权指定项目1、新项目1
#    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
#    variables:
#      json: {
#        pageNo: 1,
#        pageSize: 10,
#        sealPattern: 1,
#        sealId: $sealTC001
#      }
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[$_projectId1]" ]
#      - eq: [ content.data.records.0.sealId, $sealTC001 ]
#      - eq: [ content.data.records.total, 1 ]
#
#
#- test:
#    name: TC-验证查询电子印章授权的用印人信息列表-授权指定项目项目1、新项目1
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#        sealCode: ""
#        pageNo: 1
#        pageSize: 10
#        sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[$_projectId1]" ]
#      - eq: [ content.data.records.total, 1 ]
#
###禁用项目
#- test:
#    name: openProjectConfigInfo-新项目1-禁用
#    variables:
#      id_open: $_id1
#    api: api/esignManage/proconfig/projectConfig/openProjectConfigInfo.yml
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.data, "" ]
#      - eq: [ content.success, true ]
#
#- test:
#    name: TC-projectScope传入2，印章绑定项目1(禁用)，解绑新项目1，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC002
#      sealProjectIds: ["$_projectId1"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "不支持解绑项目" ]
#      - ne: [ content.data, "" ]
#
###删除新项目1
#- test:
#    name: openProjectConfigInfo-新项目1-启用
#    variables:
#      id_open: $_id1
#      status_open: 1
#    api: api/esignManage/proconfig/projectConfig/openProjectConfigInfo.yml
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.data, "" ]
#      - eq: [ content.success, true ]
#
#- test:
#    name: TC-projectScope传入2，印章绑定项目1，解绑项目1
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC002
#      sealProjectIds: ["$projectId1"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: TC-projectScope传入2，印章2绑定新项目1
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: [ "$_projectId1" ]
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data, "" ]
#
#
#- test:
#    name: TC-验证查询用户的企业授权印章列表-授权指定项目1、新项目1
#    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
#    variables:
#      json: {
#        pageNo: 1,
#        pageSize: 10,
#        sealPattern: 1,
#        sealId: $sealTC001
#      }
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[]" ]
#      - eq: [ content.data.records.0.sealId, $sealTC001 ]
#      - eq: [ content.data.records.total, 1 ]
#
#
#- test:
#    name: TC-验证查询电子印章授权的用印人信息列表-授权指定项目项目1、新项目1
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#      sealCode: ""
#      pageNo: 1
#      pageSize: 10
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - eq: [ content.data.records.projectScope, 2 ]
#      - eq: [ content.data.records.sealProjectIds, "[]" ]
#      - eq: [ content.data.records.total, 1 ]
#
#- test:
#    name: 删除新项目1
#    variables:
#        id_remove: $_id1
#    api: api/esignManage/proconfig/projectConfig/removeProjectConfigInfo.yml
#    validate:
#        - contains: [ content.message,"成功" ]
#        - eq: [ content.status,200 ]
#        - eq: [ content.data,"" ]
#- test:
#    name: TC-projectScope传入2，印章绑定新项目1（删除），解绑新项目1
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC002
#      sealProjectIds: [ "$projectId1" ]
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "不支持解绑项目" ]
#      - ne: [ content.data, "" ]