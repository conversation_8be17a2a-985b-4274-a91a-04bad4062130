- config:
    name: "授权用印人-allowAutoSign验证"
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      organizationCode: $org01Code
      userCode: $sign01Code
      sealsignersInfos:
        - organizationCode: $org01Code
          userCode: $sign01Code

- test:
    name: SETUP-创建多枚待发布印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC2-模板印章1"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]


- test:
    name: SETUP-创建物理用印
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 2
            sealTemplateStyle: 2
            sealName: "SCENE-物理印章"
            sealSource: 2
            sealMaterial: 1
            sealShape: 2
    extract:
      sealTC003: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: SETUP-创建一枚已发布的印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 1
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-003"
            sealSource: 2
    extract:
      sealTC004: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]


- test:
    name: TC1-allowAutoSign不传,印章默认自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC001" ]


- test:
    name: TC2-查询用户的企业授权印章列表-allowAutoSign不传-允许自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: TC3-查询电子印章授权的用印人信息列表-allowAutoSign不传-允许自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign, 1 ]
      - eq: [ content.data.projectScope, 1 ]


- test:
    name: TC4-allowAutoSign传null,印章默认自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC002
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: null
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC002" ]


- test:
    name: TC5-查询用户的企业授权印章列表-allowAutoSign传null-允许自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC002
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC002 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: TC6-查询电子印章授权的用印人信息列表-allowAutoSign传null-允许自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign, 1 ]
      - eq: [ content.data.projectScope, 1 ]
    setup_hooks:
      - ${sleep(5)}


- test:
    name: SETUP-创建多枚待发布印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC2-模板印章1"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: TC7-allowAutoSign传"",印章默认自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: ""
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC001" ]


- test:
    name: TC8-查询用户的企业授权印章列表-allowAutoSign传""-允许自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: TC9-查询电子印章授权的用印人信息列表-allowAutoSign传""-允许自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign, 1 ]
      - eq: [ content.data.projectScope, 1 ]


- test:
    name: TC10-allowAutoSign传1,印章允许自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC002
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 1
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC002" ]


- test:
    name: TC11-查询用户的企业授权印章列表-allowAutoSign传1-允许自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC002
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC002 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: TC12-查询电子印章授权的用印人信息列表-allowAutoSign传1-允许自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign, 1 ]
      - eq: [ content.data.projectScope, 1 ]
    setup_hooks:
      - ${sleep(5)}

- test:
    name: SETUP-创建一枚待发布印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC1-模板印章1"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]


- test:
    name: TC13-allowAutoSign传0,印章禁止自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 0
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC001" ]


- test:
    name: TC14-查询用户的企业授权印章列表-allowAutoSign传0-禁止自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 0 ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: TC15-查询电子印章授权的用印人信息列表-allowAutoSign传1-禁止自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign, 0 ]




- test:
    name: TC19-已发布印章allowAutoSign传1,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC004
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 1
        sealPattern: 1
    validate:
      - eq: [ content.code, 1325073 ]
      - eq: [ content.message, "企业印章待发布、停用状态支持编辑" ]
      - eq: [ content.data, null ]

- test:
    name: TC20-已发布印章allowAutoSign传0,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC004
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 0
        sealPattern: 1
    validate:
      - eq: [ content.code, 1325073 ]
      - eq: [ content.message, "企业印章待发布、停用状态支持编辑" ]
      - eq: [ content.data, null ]
    setup_hooks:
      - ${sleep(5)}




- test:
    name: TC24-allowAutoSign传英文字母,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: ewwe
        sealPattern: 1
    validate:
      - eq: [ content.code, 1322222 ]
      - eq: [ content.message, "不支持传入特殊字符" ]
      - eq: [ content.data, null]




- test:
    name: TC25-allowAutoSign传中文,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 中午
        sealPattern: 1
    validate:
      - eq: [ content.code, 1322222 ]
      - eq: [ content.message, "不支持传入特殊字符" ]
      - eq: [ content.data, null]

- test:
    name: TC26-allowAutoSign传字符,报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC001
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: "%$"
        sealPattern: 1
    validate:
      - eq: [ content.code, 1322222 ]
      - eq: [ content.message, "不支持传入特殊字符" ]
      - eq: [ content.data, null]




- test:
    name: TC28-allowAutoSign传0和1以外的数字3,允许自动签署
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $sealTC002
        sealsignersInfos: $sealsignersInfos
        allowAutoSign: 3
        sealPattern: 1
        sealRelease: 1
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.sealId, "$sealTC002" ]


- test:
    name: TC29-查询用户的企业授权印章列表-allowAutoSign传数字3-允许自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC002
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.allowAutoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC002 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC30-查询电子印章授权的用印人信息列表-allowAutoSign传3-允许自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.allowAutoSign,1 ]