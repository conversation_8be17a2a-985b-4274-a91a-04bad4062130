- config:
    name: "授权用印人"
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      organizationCode: $org01Code
      userCode: $sign01Code

- test:
    name: SETUP-创建多枚国际标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章1"
            sealSource: 2
          - sealPattern: 2
            sealTemplateStyle: 2
            sealName: "SCENE-物理印章"
            sealSource: 2
            sealMaterial: 1
            sealShape: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章3"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
      sealTC003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: SETUP-创建待发布的印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-005"
            sealSource: 2
    extract:
      sealTC005: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: TC1-物理印章不支持授权所有用户
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      authorizationScope: 2
      sealId: $sealTC002
      sealsignerType: 2
    validate:
      - eq: [ content.code, 1313029 ]
      - contains: [ content.message, "]不支持授权所有用户" ]
      - eq: [content.data, null ]

#物理印章授权物理印章用印人
- test:
    name: TC2-物理印章授权物理印章用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC002
      sealsignerType: 2
      sealsignersInfos:
        - organizationCode: $organizationCode
          userCode: $userCode
    extract:
      sealId2: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_ge: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId2-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 2,
        sealId: $sealId2
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 3 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
      - eq: [ content.data.records.0.sealId, $sealId2 ]
      - len_gt: [ content.data.records.0.sealsignersInfos, 0 ]
#物理印章授权应急用印人
- test:
    name: TC3-物理印章授权应急用印人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      authorizationScope: 3
      sealId: $sealTC002
      sealsignerType: 3
      sealsignersInfos:
        - organizationCode: $organizationCode
          userCode: $userCode
    extract:
      sealId3: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId3-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 2,
        sealId: $sealId3
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 3 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
      - eq: [ content.data.records.0.sealId, $sealId2 ]
      - len_gt: [ content.data.records.0.sealsignersInfos, 0 ]

#授权用印人多个人
- test:
    name: TC4-授权用印人多个人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC001
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - userCode: ${ENV(sign03.userCode)}
            organizationCode: ${ENV(sign03.main.departCode)}
          - customAccountNo: ${ENV(csqs.accountNo)}
            customOrgNo: ${ENV(csqs.orgNo)}
    extract:
      sealId4: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
#授权用印人已授权的用户再次授权
- test:
    name: TC5-授权用印人多个人-重复调用授权接口-允许只是程序会处理去重
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC001
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - userCode: ${ENV(sign03.userCode)}
            organizationCode: ${ENV(sign03.main.departCode)}
          - customAccountNo: ${ENV(csqs.accountNo)}
            customOrgNo: ${ENV(csqs.orgNo)}
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId4-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealId4
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 3 ]  #授权范围：1-无授权用户 2-授权所有用户 3-授权指定用户
#      - len_eq: [ content.data.records.0.sealsignersInfos, 3 ]
#授权用印人解绑已授权的用户后再次授权
- test:
    name: TC6-解绑用印人多个人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: 3
        sealId: $sealTC001
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - userCode: ${ENV(sign03.userCode)}
            organizationCode: ${ENV(sign03.main.departCode)}
          - customAccountNo: ${ENV(csqs.accountNo)}
            customOrgNo: ${ENV(csqs.orgNo)}
    extract:
      sealId6: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId6-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealId6
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 3 ]
      - len_eq: [ content.data.records.0.sealsignersInfos, 0 ]

- test:
    name: TC7-授权用印人解绑已授权的用户后再次授权
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC001
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - userCode: ${ENV(sign03.userCode)}
            organizationCode: ${ENV(sign03.main.departCode)}
          - customAccountNo: ${ENV(csqs.accountNo)}
            customOrgNo: ${ENV(csqs.orgNo)}
    extract:
      sealId7: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId7-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealId7
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 3 ]
      - eq: [ content.data.records.0.sealId, $sealId7 ]
      - len_eq: [ content.data.records.0.sealsignersInfos, 3 ]

- test:
    name: TC8-解绑用印人-所有人（授权给部分人的印章）-解绑成功
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: 2
        sealId: $sealTC001
        sealsignerType:  1
    extract:
      sealId8: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC9-解绑用印人-解绑部分人（授权给部分人的印章）-验证上一步解绑成功
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: 3
        sealId: $sealTC001
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
    validate:
      - eq: [ content.code, 1313045 ]
      - contains: [ content.message, "]尚未绑定用印人无需解绑" ]
      - eq: [content.data, null ]

- test:
    name: TC-sealId8-查询印章信息
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealId8
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 1 ]
      - len_eq: [ content.data.records.0.sealsignersInfos, 0 ]

- test:
    name: TC10-授权用印人-所有人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 2
        sealId: $sealTC003
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - organizationCode: $organizationCode
            userCode: $userCode
    extract:
      sealId10: content.data.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC-sealId10-核对印章的授权范围
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealId10
      }
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.authorizationScope, 2 ]  #验证确实授权给所有人
      - len_eq: [ content.data.records.0.sealsignersInfos, 0 ]


- test:
    name: TC11-解绑用印人-解绑部分人（授权给部分人的印章）-验证授权所有人不可解绑单个人
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: 3
        sealId: $sealTC003
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
    validate:
      - eq: [ content.code, 1313046 ]
      - contains: [ content.message, "]为授权所有人，不支持解绑指定用印人" ]
      - eq: [content.data, null ]

- test:
    name: TC12-解绑用印人-所有人（授权给所有人的印章）
    api: api/esignSeals/v1/sealcontrols/organizationSeals/deleteSealsigners.yml
    variables:
      json:
        authorizationScope: 2
        sealId: $sealTC003
        sealsignerType:  1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
#授权用印人多个对象重复
- test:
    name: TC13-授权用印人多个对象重复
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC003
        sealsignerType:  1
        sealsignersInfos:
          - organizationCode: $organizationCode
            userCode: $userCode
          - organizationCode: $organizationCode
            userCode: $userCode
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealId, 1 ]
#授权用印人空数组
- test:
    name: TC14-授权用印人空数组
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC003
        sealsignerType:  1
        sealsignersInfos: []
    validate:
      - eq: [ content.code, 1313030 ]
      - eq: [ content.message, "授权用印人集合不能为空" ]
      - eq: [content.data, null ]
#授权用印人空对象
- test:
    name: TC15-授权用印人空对象
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
    variables:
      sealsigners_json:
        authorizationScope: 3
        sealId: $sealTC003
        sealsignerType:  1
        sealsignersInfos: [{}]
    validate:
      - eq: [ content.code, 1313033 ]
      - eq: [ content.message, "organizationCode和customOrgNo二选一必填" ]
      - eq: [content.data, null ]