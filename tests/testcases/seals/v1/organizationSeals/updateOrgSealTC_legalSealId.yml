- config:
    name: "更新企业印章-legalSealId字段校验"
    variables:
      org01No: ${ENV(sign01.main.orgNo)}
      userCode1: ${ENV(sign01.userCode)}
#      sign01Name: ${ENV(sign01.userName)}
#      sign01No: ${ENV(sign01.accountNo)}
      orgNo1: "SEAL1${generate_random_str(6)}"
      orgName1: "esigntest新建${generate_random_str(6)}"
      sealGroupName1: "法人章分组-${generate_random_str(6)}"
      sign01Code: ${ENV(csqs.userCode)}
      SP: "          "




- test:
    name: 创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName1
        customOrgNo: $orgNo1
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode:
    extract:
      - orgCode1: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]


- test:
    name: 修改内部组织-添加法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      data:
        customOrgNo: $orgNo1
        legalRepAccountNo: $sign01Code
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]



- test:
    name: "list-查询个人章-userSealId0"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $sign01Code
    extract:
      _userSealId0: content.data.records.0.sealId
      _userSealId1: content.data.records.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.records, 1 ]

- test:
    name: "list-查询个人章-带有中国标准印章"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $sign01Code
    extract:
      _userSealId2: content.data.records.0.sealId
      _userSealId3: content.data.records.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.records, 1 ]



- test:
    name: "create-创建一枚法人章(默认)-orgNo1-国际标准印章和物理印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01Code
        customOrgNo: $orgNo1
        sealGroupName: $sealGroupName1
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            legalSealId: $_userSealId0
            sealName: "企业法人章${generate_random_str(10)}"
          - sealPattern: 2
            sealTemplateStyle: 2
            sealSource: 2
            sealName: "企业法人章物理${generate_random_str(5)}"
            legalSealId: $_userSealId0
    extract:
      _legalSealId0: content.data.sealInfos.0.sealId
      _legalSealId1: content.data.sealInfos.0.sealId
      sealGroupId001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "create-创建一枚法人章(默认)-org01No-中国标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01Code
        customOrgNo: $org01No
        sealGroupName: "ORG01测试法人章更新"
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 3
            legalSealId: "${ENV(sign01.cloud.guomi.SealId)}"
            sealName: "企业法人章${generate_random_str(3)}"
    extract:
      sealGroupId002: content.data.sealGroupId
      _legalSealId2: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]


- test:
    name: update-orgSeal-更新法人章-关联的个人章不存在
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: "18999"   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325030 ]
      - eq: [ content.message, 'legalSealId错误：法人章id{18999}不可用，请检查印章是否存在且状态为发布' ]
      - ne: [ content.data, "" ]

- test:
    name: TC-新建个人印章
    api: api/esignSeals/v1/sealcontrols/userseals/create.yml
    variables:
        userCode: $userCode1
    validate:
        - eq: [ "content.code", 200]
        - contains: [ "content.message", '成功']
        - eq: [ "content.data.sealInfos.0.userCode", $userCode1 ]
        - ne: [ "content.data.sealInfos.0.sealId", "" ]
        - eq: ["content.data.sealInfos.0.sealStatus", "1"]
    extract:
      userSealId: content.data.sealInfos.0.sealId

- test:
    name: update-orgSeal-更新法人章-关联的个人章不是企业法人的个人章(内部用户)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: $userSealId   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325052 ]
      - contains: [ content.message, '不是所属印章企业的法人章' ]
      - eq: [ content.data, null ]

- test:
    name: update-orgSeal-更新法人章-关联的个人章不是企业法人的个人章（相对方用户）
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ${ENV(wsignwb01.sealId)}   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325030 ]
      - eq: [ content.message, 'legalSealId错误：法人章id{cf0bbaa3-c031-484d-88c4-10c19d15fb00}不可用，请检查印章是否存在且状态为发布' ]
      - eq: [ content.data, null ]

- test:
    name: update-orgSeal-更新法人章-关联的个人章长度超过X字符
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: "13432222222222222222222222222222222222222222222222222222222rewffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
        213888834444444444444444444444444443e3e8388888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888"   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325030 ]
      - contains: [ content.message, '不可用，请检查印章是否存在且状态为发布' ]
      - eq: [ content.data, null ]


- test:
    name: update-orgSeal-更新法人章-legalSealId为空,更新成功，不更换印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "更新法人章"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ""   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - ne: [ content.data, "" ]

- test:
    name: update-orgSeal-更新法人章-legalSealId为null,更新成功，不更换印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "更新法人章"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 50   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - ne: [ content.data, "" ]

- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: $_legalSealId0
    extract:
      _legalSealId0_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "更新法人章" ]
      - eq: [ content.data.list.0.remoteSealId, "$_legalSealId0" ]
      - eq: [ content.data.list.0.sealStatus, "3" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]

- test:
    name: update-orgSeal-更新法人章-legalSealId为null,更新成功，不更换印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 50   #不透明度（%） 0～100之间，默认为100。
        legalSealId:  $SP$_userSealId1$SP  #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - ne: [ content.data, "" ]


- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: $_legalSealId0
    extract:
      _legalSealId0_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "更新法人章信息" ]
      - eq: [ content.data.list.0.remoteSealId, "$_legalSealId0" ]
      - eq: [ content.data.list.0.sealStatus, "3" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]

- test:
    name: update-orgSeal-更新法人章-_legalSealId0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: $_userSealId1   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - ne: [ content.data, "" ]

- test:
    name: update-orgSeal-更新法人章-_legalSealId1-(中国标准印章更新关联法人章为国际标准,报错)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId2
        sealRelease: 0
        sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "更新法人章信息-从中国标准印章更换成国际标准印章"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 99   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ${ENV(sign01.cloud.SealId)}   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325010 ]
      - eq: [ content.message, '创建人所属企业没有可用的制章者证书' ]
      - eq: [ content.data, null ]


- test:
    name: update-orgSeal-更新法人章-关联的个人章与更新前相同
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "更新法人章信息信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 30   #不透明度（%） 0～100之间，默认为100。
        legalSealId: $_userSealId1   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - ne: [ content.data, "" ]


- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: $_legalSealId0
    extract:
      _legalSealId0_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "更新法人章信息信息" ]
      - eq: [ content.data.list.0.remoteSealId, "$_legalSealId0" ]
      - eq: [ content.data.list.0.sealStatus, "3" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]

- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: $_legalSealId1
    extract:
      _legalSealId0_uuid1: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]


- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId002
      sealId: $_legalSealId2
    extract:
      _legalSealId1_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]

- test:
    name: detail-页面企业印章详情-查询关联的法人章
    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
    variables:
      sealId: $_legalSealId0_uuid0
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.relatedSealId, $_userSealId1 ]
      - eq: [ content.data.remoteSealId, $_legalSealId0 ]


- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $_legalSealId0
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $_legalSealId0_uuid0
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]
- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $_legalSealId0_uuid1
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]

- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $_legalSealId1_uuid0
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]

- test:
    name: delete-删除组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $orgCode1
      customOrgNo: ""
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, 成功 ]
      - eq: [content.data, "" ]

- test:
    name: 更新个人印章状态为停用
    api: api/esignSeals/v1/sealcontrols/userseals/updateStatus.yml
    variables:
        sealId: $userSealId
        sealStatus: 2
    validate:
        - eq: [ "content.code", 200]
        - eq: [ "content.message", '成功']
        - eq: [ "content.data.sealId", $sealId ]
        - eq: [ "content.data.sealStatus", "2" ]


- test:
    name: 删除个人印章
    api: api/esignSeals/v1/sealcontrols/userseals/delete.yml
    variables:
      - sealId: $userSealId
    validate:
      - eq: [ "content.message", '成功' ]
      - eq: [ "content.code", 200 ]
      - eq: [ "content.data.sealId", $userSealId ]