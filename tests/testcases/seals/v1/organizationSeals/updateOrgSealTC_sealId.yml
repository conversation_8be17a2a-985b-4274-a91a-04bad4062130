- config:
    name: "更新企业印章-sealId字段校验"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
#      org01Code: ${ENV(sign01.main.orgCode)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01Name: ${ENV(sign01.userName)}
      sign01No: ${ENV(sign01.accountNo)}
      orgNo1: "SEAL1${generate_random_str(6)}"
      orgName1: "esigntest新建${generate_random_str(6)}"
      sealGroupName2: "法人章分组-${generate_random_str(6)}"

- test:
    name: 创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      data:
        name: $orgName1
        customOrgNo: $orgNo1
        organizationType: COMPANY
        parentOrgNo:
        parentCode: 0
        licenseType: CREDIT_CODE
        licenseNo: ""
        legalRepAccountNo:
        legalRepUserCode:
    extract:
      - orgCode1: content.data.organizationCode
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]


- test:
    name: 修改内部组织-添加法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      data:
        customOrgNo: $orgNo1
        legalRepAccountNo: $sign01Code
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

- test:
    name: SETUP-创建多枚企业标准印章-org01
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "更新印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 3
            sealTemplateStyle: 1
            sealName: "UPDATE2-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "UPDATE2-模板印章2"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "UPDATE2-模板印章3"
          - sealPattern: 2
            sealTemplateStyle: 2
            sealSource: 2
            sealName: "UPDATE2-模板印章4-物理印章"
    extract:
      sealGroupId001: content.data.sealGroupId
      sealTC1_001: content.data.sealInfos.0.sealId
      sealTC1_002: content.data.sealInfos.1.sealId
      sealTC1_003: content.data.sealInfos.2.sealId
      sealTC1_004: content.data.sealInfos.3.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: updateStatus-seal002-待发布到已停用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC1_002"
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: updateStatus-seal003-待发布到已停用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC1_003"
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: update-orgSeal-sealId为空
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: ""
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: null   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1300000 ]
      - contains: [ content.message, '印章id不能为空' ]
      - eq: [content.data, null ]

- test:
    name: update-orgSeal-sealId为不存在
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "XXXXXXXXX-333344444"
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: null   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325077 ]
      - contains: [ content.message, '企业印章不存在' ]
      - eq: [content.data, null ]

- test:
    name: update-orgSeal-sealId长度超过65
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "${generate_random_str(300)}"
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: null   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325077 ]
      - contains: [ content.message, '企业印章不存在' ]
      - eq: [content.data, null ]

- test:
    name: update-orgSeal-sealId中文字符
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "中文字符"
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: null   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325077 ]
      - contains: [ content.message, '企业印章不存在' ]
      - eq: [content.data, null ]

- test:
    name: update-orgSeal-sealId正确值-状态待发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_001"
        sealRelease: 0
        sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "编辑待发布的印章的信息"   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 40   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ""   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - eq: [ content.data.sealId, "$sealTC1_001" ]
      - eq: [ content.data.sealStatus, "1" ]

- test:
    name: update-orgSeal-不允许更新印章形态（中国标准和国际标准的更换）
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_001"
        sealRelease: 0
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: null   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 40   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ""   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325077 ]
      - contains: [ content.message, '企业印章不存在' ]
      - eq: [content.data, null ]

- test:
    name: list-orgSeal-sealTC1_001-无法查询待发布印章
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
       sealId: $sealTC1_001
       customOrgNo: ""
       customAccountNo: ""
       sealPattern:  3
       pageSize: 10
       pageNo: 1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records, [] ]

- test:
    name: listPageEnterprise-查询印章分组中印章-sealTC1_001
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
      sealId: "$sealTC1_001"
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "编辑待发布的印章的信息" ]
      - eq: [ content.data.list.0.remoteSealId, "$sealTC1_001" ]
      - eq: [ content.data.list.0.sealStatus, "3" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]
      - eq: [ content.data.list.0.sealBodyStructure, "2" ]

- test:
    name: updateStatus-seal001-待发布到已发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC1_001"
        sealStatus: 2
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "2" ]

- test:
    name: updateStatus-seal003-停用到吊销
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC1_003"
        sealStatus: 4
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "4" ]

- test:
    name: update-orgSeal-sealId正确值-状态已发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_001"
        sealRelease: null
        sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "编辑已发布的印章的信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325073 ]
      - contains: [ content.message, '企业印章待发布、停用状态支持编辑' ]
      - eq: [content.data, null ]

- test:
    name: list-orgSeal-sealTC1_001
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
       sealId: $sealTC1_001
       customOrgNo: ""
       customAccountNo: ""
       sealPattern:  3
       organizationCode: ""
       sealTypeCode: ""
       sealName: ""
       userCode: ""
       pageSize: 10
       pageNo: 1
#    extract:
#      sealCode001: content.data.records.0.sealCode
#      unionSealCode001: content.data.records.0.unionSealCode
#      unionSealId001: content.data.records.0.unionSealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.sealId, $sealTC1_001 ]
      - eq: [ content.data.records.0.unionSealId, $sealTC1_001 ]
      - eq: [ content.data.records.0.sealName, "编辑待发布的印章的信息" ]
      - len_eq: [ content.data.records, 1 ]

- test:
    name: update-orgSeal-sealId正确值-状态已停用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_002"
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "编辑已停用的印章的信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - eq: [ content.data.sealId, "$sealTC1_002" ]

- test:
    name: list-orgSeal-sealTC1_002
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
       sealId: $sealTC1_002
       customOrgNo: ""
       customAccountNo: ""
       sealPattern:  1
       organizationCode: ""
       sealTypeCode: ""
       sealName: ""
       userCode: ""
       pageSize: 10
       pageNo: 1
#    extract:
#      sealCode001: content.data.records.0.sealCode
#      unionSealCode001: content.data.records.0.unionSealCode
#      unionSealId001: content.data.records.0.unionSealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records.0.sealId, $sealTC1_002 ]
      - eq: [ content.data.records.0.unionSealId, $sealTC1_002 ]
      - eq: [ content.data.records.0.sealName, "编辑已停用的印章的信息" ]
      - len_eq: [ content.data.records, 1 ]

- test:
    name: update-orgSeal-sealId正确值-状态已吊销
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_003"
        sealRelease: null
        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName:  "编辑已吊销的印章的信息"    #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: null   #不透明度（%） 0～100之间，默认为100。
        legalSealId: null   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 1325073 ]
      - contains: [ content.message, '企业印章待发布、停用状态支持编辑' ]
      - eq: [content.data, null ]

- test:
    name: list-orgSeal-sealTC1_003-无法查询吊销印章
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
       sealId: $sealTC1_003
       sealPattern:  1
       pageSize: 10
       pageNo: 1
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.records, [] ]

- test:
    name: listPageEnterprise-查询印章分组中印章-sealTC1_003
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId001
#      accountNumber: ""
      sealId: "$sealTC1_003"
#    extract:
#      sealId002: content.data.list.0.sealId
#      remoteSealId002: content.data.list.0.remoteSealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "UPDATE2-模板印章3" ]
      - eq: [ content.data.list.0.remoteSealId, "$sealTC1_003" ]
      - eq: [ content.data.list.0.sealStatus, "5" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "吊销" ]

##新建法人印章分组
- test:
    name: createGroup-印章总管理员新建云印章-国际标准-法人章分组
    api: api/esignSeals/seals/enterprise/electronic/createGroup.yml
    variables:
      autoPushSeal: "0"
      draftOrProduction: "0"
      sealConfigList: []
      sealGroupConfig:
        organizationCode: $orgCode1
        organizationName: "$orgName1"
        sealGroupDesc: ""
        sealGroupId: ""
        sealGroupName: $sealGroupName2
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealTypeModelKey: ""
        sealTypeName: ""
        sealNumLimit: 0
    extract:
      sealGroupId002: content.data.sealGroupId
    validate:
      - eq: [json.status, 200]
      - eq: [json.success, true]
      - ne: [json.data, ""]
      - eq: [content.data.sealThumbnailFileKey, ""]

####################新建企业印章并授权##############
- test:
    name: "list-查询个人章-userSealId0"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $sign01Code
    extract:
      _userSealId0: content.data.records.0.sealId
      _userSealId1: content.data.records.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.records, 1 ]

- test:
    name: "list-查询个人章-userSealId0-带有中国标准印章"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $sign01Code
    extract:
      _userSealId2: content.data.records.0.sealId
      _userSealId3: content.data.records.1.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_gt: [ content.data.records, 1 ]

- test:
    name: "create-创建一枚法人章(默认)-orgNo1-国际标准印章和物理印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01Code
        customOrgNo: $orgNo1
        sealGroupName: $sealGroupName2
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            legalSealId: $_userSealId0
            sealName: "企业法人章${generate_random_str(10)}"
          - sealPattern: 2
            sealTemplateStyle: 2
            sealSource: 2
            sealName: "企业法人章物理${generate_random_str(5)}"
            legalSealId: $_userSealId0
    extract:
      _legalSealId0: content.data.sealInfos.0.sealId
      _legalSealId2: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "create-创建一枚法人章(默认)-org01No-中国标准印章"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01Code
        customOrgNo: $org01No
        sealGroupName: "ORG01测试法人章更新"
        sealTypeCode: "LEGAL-PERSON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 3
            legalSealId: "${ENV(sign01.cloud.guomi.SealId)}"
            sealName: "企业法人章${generate_random_str(3)}"
    extract:
      sealGroupId003: content.data.sealGroupId
      _legalSealId1: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

#- test:
#    name: update-orgSeal-更新法人章-_legalSealId0
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
#    variables:
#      json:
#        sealId: "$_legalSealId0"
#        sealRelease: 0
#        sealPattern: 1   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
#        sealName:  "更新法人章信息"    #印章名称，不超过30字；默认取印章分组名称
#        sealOpacity: 99   #不透明度（%） 0～100之间，默认为100。
#        legalSealId: "$_userSealId1"   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, '成功' ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: update-orgSeal-更新法人章-_legalSealId1-(中国标准印章更新关联法人章为国际标准，报错)
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
#    variables:
#      json:
#        sealId: "$_legalSealId1"
#        sealRelease: 0
#        sealPattern: 3   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
#        sealName:  "更新法人章信息-从中国标准印章更换成国际标准印章"    #印章名称，不超过30字；默认取印章分组名称
#        sealOpacity: 99   #不透明度（%） 0～100之间，默认为100。
#        legalSealId: "${ENV(sign01.cloud.SealId)}"   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, '成功' ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: listPageEnterprise-查询印章分组中印章-_legalSealId0
#    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
#    variables:
#      sealGroupId: $sealGroupId002
##      accountNumber: ""
#      sealId: "$_legalSealId0"
#    extract:
#      _legalSealId0_uuid: content.data.list.0.sealId
##      remoteSealId002: content.data.list.0.remoteSealId
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#      - eq: [ content.data.totalCount, 1 ]
#      - eq: [ content.data.list.0.sealName, "更新法人章信息-从中国标准印章更换成国际标准印章" ]
#      - eq: [ content.data.list.0.remoteSealId, "$_legalSealId0" ]
#      - eq: [ content.data.list.0.sealStatus, "3" ]
#      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]
#
#- test:
#    name: detail-页面企业印章详情-查询关联的法人章
#    api: api/esignSeals/seals/smc/seals/enterprise/detailEnterprise.yml
#    variables:
#      sealId: $_legalSealId0_uuid
#    validate:
#      - eq: [ content.status, 200 ]
#      - eq: [ content.success, true ]
#      - eq: [ content.data.relatedSealId, "${ENV(sign01.cloud.SealId)}" ]
#      - eq: [ content.data.remoteSealId, "$_legalSealId0" ]

- test:
    name: listPageEnterprise-查询印章分组中印章-_legalSealId1
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId003
#      accountNumber: ""
      sealId: "$_legalSealId1"
    extract:
      _legalSealId1_uuid: content.data.list.0.sealId
#      remoteSealId002: content.data.list.0.remoteSealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.remoteSealId, "$_legalSealId1" ]
      - eq: [ content.data.list.0.sealStatus, "3" ]
      - eq: [ content.data.list.0.sealStatusTranslation, "待发布" ]

- test:
    name: update-orgSeal-sealId更新物理企业印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      json:
        sealId: "$sealTC1_004"
        sealRelease: 0
        sealPattern: 2   #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章 默认取1
        sealName: "编辑物理印章信息的印章的信息"   #印章名称，不超过30字；默认取印章分组名称
        sealOpacity: 40   #不透明度（%） 0～100之间，默认为100。
        legalSealId: ""   #法人章id 印章所属企业法人发布的个人印章id；sealTypeCode为法人章
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, '成功' ]
      - eq: [ content.data.sealId, "$sealTC1_004" ]
      - eq: [ content.data.sealStatus, "1" ]

- test:
    name: delete-删除组织
    api: api/esignManage/InnerOrganizations/delete.yml
    variables:
      organizationCode: $orgCode1
      customOrgNo: ""
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, 成功 ]
      - eq: [content.data, "" ]

