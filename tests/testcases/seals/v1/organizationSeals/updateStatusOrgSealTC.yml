- config:
    name: "更新印章状态 1-待发布 2-已发布 3-已停用 4-已吊销 待发布状态印章可以变成已发布、已停用 已发布状态印章可以变成已停用 已停用状态印章可以变成已吊销 已吊销的印章状态不可变更"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}
      sealIdDraf: ${getOrganizationSealsByStatus(, 1)}
      sealIdRevoke: ${getOrganizationSealsByStatus(, 4)}

- test:
    name: SETUP-创建多枚国际标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "SCENE-TC4-模板印章2"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC4-模板印章3"
            sealSource: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
      sealTC003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: SETUP-创建待发布的印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-005"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-005"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-005"
            sealSource: 2
    extract:
      sealTC005: content.data.sealInfos.0.sealId
      sealTC006: content.data.sealInfos.0.sealId
      sealTC007: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: sealId为空
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: ""
        sealStatus: 1
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不能为空" ]
      - eq: [content.data, null ]

- test:
    name: sealId为null
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: null
        sealStatus: 1
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不能为空" ]
      - eq: [content.data, null ]

- test:
    name: sealId为不存在
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "XXXX"
        sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: 更新状态印章sealId空字符串异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: ' '
      sealStatus: 1
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不能为空" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId特殊字符异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: '!@#\\/:*?\"<>|'
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId英文全角异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: ｑｕａｎｊｉａｏ
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId英文大小写异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: Test
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId前后空格异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: ' banjiao '
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId繁体字异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: 發纔
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId新疆名异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: 达吾提·阿西木
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId英文名异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: Rosalette Hazal Royston
      sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]
- test:
    name: 更新状态印章sealId长度101异常用例
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      sealId: abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a
      sealStatus: 1
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章id不超过36个字" ]
      - eq: [content.data, null ]
      
- test:
    name: TC1-sealId为已发布状态变更（已发布状态印章可以变成3-已停用），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 1 #更新成1-待发布 失败
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: TC2-sealId为已发布状态变更（已发布状态印章可以变成3-已停用），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 2 #更新成2-已发布 失败
    validate:
      - eq: [ content.code, 1313024 ]
      - contains: [ content.message, "形态已发布状态不支持发布，请修改印章状态为待发布" ]
      - eq: [content.data, null ]

- test:
    name: TC3-sealId为已发布状态变更（已发布状态印章可以变成3-已停用），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 4 #更新成4-已吊销 失败
    validate:
      - eq: [ content.code, 1313026 ]
      - contains: [ content.message, "形态已发布状态不支持吊销，请修改印章状态为已停用" ]
      - eq: [content.data, null ]

- test:
    name: TC4-sealId为已发布状态变更更新成4-已停用 成功
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 3 #更新成4-已停用 成功
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(6)}

- test:
    name: TC5-sealId为停用状态变更（已停用状态印章可以变成4-已吊销），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 1 #更新成1-待发布 失败
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: TC7-sealId为停用状态变更（已停用状态印章可以变成4-已吊销），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 3 #更新成3-已停用 失败
    validate:
      - eq: [ content.code, 1313025 ]
      - contains: [ content.message, "形态已停用状态不支持停用，请修改印章状态为待发布或已发布" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealId为停用状态，停用到发布
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 2 #更新成2-已发布 失败
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "2" ]



- test:
    name: TC8-sealId为停用状态变更（已停用状态印章可以变成4-已吊销），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 4 #更新成4-已吊销
    validate:
      - eq: [ content.code, 1313026 ]
      - contains: [ content.message, "已发布状态不支持吊销，请修改印章状态为已停用" ]
      - eq: [content.data, null ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(6)}

- test:
    name: TC7-印章停用
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 3 #更新成3-已停用 失败
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: TC8-停用到吊销
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 4 #更新成4-已吊销
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "4" ]
    teardown_hooks: ##印控更新有锁，锁要5秒
      - ${sleep(6)}


- test:
    name: TC9-sealId为吊销状态变更（已吊销的印章状态不可变更），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: TC10-sealId为吊销状态变更（已吊销的印章状态不可变更），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 2
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: TC11-sealId为吊销状态变更（已吊销的印章状态不可变更），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 3
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: TC12-sealId为吊销状态变更（已吊销的印章状态不可变更），其他状态更新状态失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC001"
        sealStatus: 4
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: TC13-sealId草稿态，不可更新状态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealIdDraf"
        sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: TC14-sealId草稿态，不可更新状态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealIdDraf"
        sealStatus: 2
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: TC15-sealId草稿态，不可更新状态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealIdDraf"
        sealStatus: 3
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: TC16-sealId草稿态，不可更新状态
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealIdDraf"
        sealStatus: 4
    validate:
      - eq: [ content.code, 1313048 ]
      - contains: [ content.message, "非待发布、已发布或已停用状态，不能更新" ]
      - eq: [content.data, null ]

- test:
    name: sealStatus为空，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: ""
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章状态不能为空" ]
      - eq: [content.data, null ]

- test:
    name: sealStatus为null，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: null
    validate:
      - eq: [ content.code, 1300000 ]
      - eq: [ content.message, "印章状态不能为空" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值0，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 0
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值0.05，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 0.05
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值-1，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: -1
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值True，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: True
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值是，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: "是"
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值6，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 6
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC6-sealStatus为非指定值99999999，更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 99999999
    validate:
      - eq: [ content.code, 1313035 ]
      - contains: [ content.message, "sealStatus错误：印章状态不支持传入" ]
      - eq: [content.data, null ]

- test:
    name: TC17-sealId待发布态（待发布状态印章可以变成已发布、已停用），其他状态更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 1
    validate:
      - eq: [ content.code, 1313035 ]
      - eq: [ content.message, "sealStatus错误：印章状态不支持传入[1]" ]
      - eq: [content.data, null ]

- test:
    name: TC18-sealId待发布态（待发布状态印章可以变成已发布、已停用），其他状态更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 4
    validate:
      - eq: [ content.code, 1313026 ]
      - contains: [ content.message, "形态待发布状态不支持吊销，请修改印章状态为已停用" ]
      - eq: [content.data, null ]

- test:
    name: TC19-sealId待发布态（待发布状态印章可以变成已发布、已停用），其他状态更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC005"
        sealStatus: 2
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "2" ]

- test:
    name: TC20-sealId待发布态（待发布状态印章可以变成已发布、已停用），其他状态更新失败
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: "$sealTC006"
        sealStatus: 3
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]