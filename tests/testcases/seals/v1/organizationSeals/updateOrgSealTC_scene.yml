- config:
    name: "创建企业印章-sealName印章场景"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(csqs.userCode)}
      sign01No: ${ENV(csqs.accountNo)}
      customOrgNo: $org01No
      sealGroupName1: "自动化分组可删-${generate_random_str(6)}"

- test:
    name: SETUP-创建多枚中国标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "更新印章场景测试"
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC5-模板印章1"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "SCENE-TC5-模板印章2"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-TC5-模板印章3"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
    extract:
      sealTC1_001: content.data.sealInfos.0.sealId
      sealTC1_002: content.data.sealInfos.1.sealId
      sealTC1_003: content.data.sealInfos.2.sealId
      sealGroupId_001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - len_gt: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
    extract:
      sealId_001: content.data.list.0.sealId
      sealId_002: content.data.list.0.sealId
      sealId_003: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_001
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: 更新企业印章正常用例-修改模板印章的数据
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_001
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "TC1-更新印章A001"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    times: 1
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, 成功]
    - eq: [content.data.sealId, $sealTC1_001]


- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "TC1-更新印章A001" ]

- test:
    name: setup停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: 更新企业印章名称，名称超过100个字符
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-A001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是测试印章不能唱过就是已"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1325007]
    - contains: [content.message, "不能超过100字"]
    - eq: [content.data, null]



- test:
    name: "更新企业印章名称为空"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: ""
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]

- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "TC1-更新印章A001" ]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: "更新企业印章名称为null"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: null
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]

- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "TC1-更新印章A001" ]

- test:
    name: 更新企业印章名称，名称含特殊字符/
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章/"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]

- test:
    name: "更新企业印章名称，名称含特殊字符:"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章:"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]


- test:
    name: "更新企业印章名称，名称含特殊字符*"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章*"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]


- test:
    name: "更新企业印章名称，名称含特殊字符?"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章?"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]


- test:
    name: "更新企业印章名称，名称含特殊字符<"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章<"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]


- test:
    name: "更新企业印章名称，名称含特殊字符>"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章>"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]

- test:
    name: "更新企业印章名称，名称含特殊字符|"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章|"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]

- test:
    name: "更新企业印章名称，名称中间含空格"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印 沙发上"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 1338016]
    - contains: [content.message, '印章名称不支持以下特殊符号：/:*?']
    - eq: [content.data, null]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: 更新企业印章名称，名称1个字符
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "测"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]

- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_002
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "测" ]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: "更新企业印章名称，名称含数字，更新成功"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章123"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]

- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_002
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "更新印章123" ]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: "更新企业印章名称，只有数字，更新成功"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "123"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]


- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]

- test:
    name: "更新企业印章名称，只有字母，更新成功"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "aaaaaabbbb"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]

- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_002
    extract:
      _legalSealId0_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "aaaaaabbbb" ]

- test:
    name: 停用印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/updateStatus.yml
    variables:
      json:
        sealId: $sealTC1_002
        sealStatus: 3 #更新成3-已停用
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.sealStatus, "3" ]


- test:
    name: "更新企业印章名称，中文+字母+数字+符号，更新成功"
    api: api/esignSeals/v1/sealcontrols/organizationSeals/update.yml
    variables:
      sealId: $sealTC1_002
      base64img: ""
      sealHeight: 40
      sealTemplateStyle:
      sealMakerCertId:
      sealHorizontalText:
      sealHorizontalTextsecond:
      sealRelease:
      ensealUpperText:
      sealOpacity:
      sealSource: 2
      legalSealId:
      sealWidth: 40
      sealUpperText: "更新印章测试-B001"
      sealFileKey:
      sealOldStyle: 0
      sealPattern: 1
      defaultSeal:
      sealMaterial:
      sealHorizontalTextfirst:
      sealName: "更新印章aaaaaabbbb11222@$"
      sealBottomText:
      sealSignercertId:
      sealShape: 2
      sealColour: 2
    validate:
    - eq: [content.code, 200]
    - eq: [content.message, "成功"]
    - eq: [content.data.sealId, $sealTC1_002]


- test:
    name: listPageEnterprise-查询印章分组中印章名称
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
      sealId: $sealTC1_002
    extract:
      _legalSealId0_uuid0: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.data.totalCount, 1 ]
      - eq: [ content.data.list.0.sealName, "更新印章aaaaaabbbb11222@$" ]


- test:
    name: 吊销印章1
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_001
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]
- test:
    name: 吊销印章2
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_002
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]
- test:
    name: 吊销印章3
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId_003
    validate:
        - eq: [ content.status, 200]
        - eq: [ content.success, true]
        - eq: [ content.message, "服务器成功返回" ]
      