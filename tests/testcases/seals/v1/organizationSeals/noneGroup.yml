- config:
    name: "创建企业印章-印章场景"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      org01Code: ${ENV(sign01.main.orgCode)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org01No: ${ENV(sign01.main.orgNo)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      customOrgNo: $org01No
      sign01_sealId: ${ENV(sign01.sealId)}

####TC1-创建sealInfos空数组
- test:
    name: TC1-创建sealInfos空数组
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "新建空分组"
        sealTypeCode: "COMMON-SEAL"
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [content.data, null ]

####TC1-创建sealInfos空数组
- test:
    name: TC2-创建sealInfos空数组
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组仅限一枚印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: 1
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [content.data, null ]

####TC1-创建sealInfos空数组sealNumLimit为空
- test:
    name: TC3-创建sealInfos空数组sealNumLimit为空,默认为0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组仅限一枚印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: ""
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [content.data, null ]

####TC1-创建sealInfos空数组sealNumLimit为负数
- test:
    name: TC4-创建sealInfos空数组sealNumLimit为负数报错
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组仅限一枚印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: -3
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "失败" ]
      - eq: [content.data, null ]

- test:
    name: TC5-创建sealInfos空数组sealNumLimit为0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组不限制印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: 0
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [content.data, null ]

####TC6-创建sealInfos空数组sealNumLimit为1,同时新建多枚印章

- test:
    name: TC6-创建sealInfos空数组sealNumLimit为0
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组仅限一枚印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: -3
        sealInfos: [ ]
    extract:
      sealGroupId0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [content.data, null ]

####todo：报错
- test:
    name: TC7-sealNumLimit为1新建多枚中国标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: "空分组仅限一枚印章"
        sealTypeCode: "COMMON-SEAL"
        sealNumLimit: 1
        sealInfos:
          - sealPattern: 3
            sealTemplateStyle: 1
            sealName: "SCENE-TC5-模板印章1"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 1
            sealFileKey: "${ENV(pngPageFileKey)}"
          - sealPattern: 3
            sealTemplateStyle: 2
            sealName: "SCENE-TC5-模板印章2"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
          - sealPattern: 3
            sealTemplateStyle: 3
            sealName: "SCENE-TC5-模板印章3"
            sealMakerCertId: $org01CertId
            signerCertInfos:
              - sealSignercertId: $org01CertId
            sealSource: 2
    extract:
      sealTC5_001: content.data.sealInfos.0.sealId
      sealTC5_002: content.data.sealInfos.1.sealId
      sealTC5_003: content.data.sealInfos.2.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "报错" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]