- config:
    name: "授权可用项目"
    variables:
      org01No: ${ENV(csqs.orgNo)}
      sign01No: ${ENV(csqs.accountNo)}
      projectId1: "${projectSelection()}"
      sealGroupName1: "印章测试场景${generate_random_str(6)}"
      sealGroupName2: "印章测试场景2${generate_random_str(6)}"
      accountNumber_tc: ${ENV(csqs.userCode)}
      passwordEncrypt_tc: ${ENV(passwordEncrypt)}
      authorization0: ${getPortalToken($accountNumber_tc, $passwordEncrypt_tc)}

- test:
    name: SETUP-创建多枚国际标准印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: $sealGroupName1
        sealTypeCode: "COMMON-SEAL"
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-模板印章1"
            sealSource: 2
          - sealPattern: 1
            sealTemplateStyle: 2
            sealName: "SCENE-模板章"
            sealSource: 2
            sealMaterial: 1
            sealShape: 2
    extract:
      sealTC001: content.data.sealInfos.0.sealId
      sealTC002: content.data.sealInfos.1.sealId
      sealGroupId_001: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]



- test:
    name: TC-验证查询用户的企业授权印章列表-印章1默认授权全部项目
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.projectScope, 1 ]
      - eq: [ content.data.records.0.projectSealInfos, null ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章1默认授权全部项目
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 1 ]
      - eq: [ content.data.projectSealInfos, []]


- test:
    name: TC-验证查询用户的企业授权印章列表-印章2默认授权全部项目
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC002
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.projectScope, 1 ]
      - eq: [ content.data.records.0.projectSealInfos, null ]
      - eq: [ content.data.records.0.sealId, $sealTC002 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章2默认授权全部项目
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC002
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 1 ]
      - eq: [ content.data.projectSealInfos, []]


- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_001
    extract:
      sealTC_001: content.data.list.0.sealId
      remoteSealId_001: content.data.list.0.remoteSealId
      sealTC_002: content.data.list.1.sealId
      remoteSealId_002: content.data.list.1.remoteSealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: 企业电子印章管理-授权指定可用项目1
    api: api/esignSeals/seals/enterprise/electronic/authSealProject.yml
    variables:
        authorization0: $authorization0_tc_auth
        sealId: $sealTC_001
        sealProjectRange: "2"
        projectIds: ["$projectId1"]
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ $message, "服务器成功返回"]


- test:
    name: TC--印章项目1授权自动签署
    api: api/esignSeals/seals/enterprise/electronic/authAutoSignSealProject.yml
    variables:
      sealId: $sealTC_001
      projectIds: ["$projectId1"]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "服务器成功返回"]


- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章1授权项目是项目1且自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealCode: ""
      pageNo: 1
      pageSize: 10
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 2 ]
      - eq: [ content.data.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.projectSealInfos.0.autoSign, 1 ]


- test:
    name: TC-验证查询用户的企业授权印章列表-印章1授权项目是项目1且自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.projectScope, 2 ]
      - eq: [ content.data.records.0.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.records.0.projectSealInfos.0.autoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]


- test:
    name: 企业电子印章管理-授权可用项目1和2
    api: api/esignSeals/seals/enterprise/electronic/authSealProject.yml
    variables:
        authorization0: $authorization0_tc_auth
        sealId: $sealTC_001
        sealProjectRange: "2"
        projectIds: ["$projectId1", "1000000"]
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ $message, "服务器成功返回"]

- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章1授权项目是项目1保留自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealCode: ""
      pageNo: 1
      pageSize: 10
      sealId: $sealTC001
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 2 ]
      - eq: [ content.data.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.projectSealInfos.0.autoSign, 1 ]
      - eq: [ content.data.projectSealInfos.1.projectId, "1000000" ]
      - eq: [ content.data.projectSealInfos.1.autoSign, 0]


- test:
    name: TC-验证查询用户的企业授权印章列表-印章1授权项目是项目1保留自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC001
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.projectScope, 2 ]
      - eq: [ content.data.records.0.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.records.0.projectSealInfos.0.autoSign, 1 ]
      - eq: [ content.data.records.0.projectSealInfos.1.projectId, "1000000" ]
      - eq: [ content.data.records.0.projectSealInfos.1.autoSign, 0 ]
      - eq: [ content.data.records.0.sealId, $sealTC001 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: 停用电子印章1
    api: api/esignSeals/smc/seals/stopEnterpriseSeal.yml
    variables:
      stopSeal_sealId: $sealTC_001
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - len_gt: [$message, 1]

- test:
    name: 停用电子印章2
    api: api/esignSeals/smc/seals/stopEnterpriseSeal.yml
    variables:
      stopSeal_sealId: $sealTC_002
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - len_gt: [$message, 1]

- test:
    name: 吊销印章1
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealTC_001
    extract:
        status: json.status
        success : json.success
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ json.message, "服务器成功返回" ]
- test:
    name: 吊销印章2
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealTC_002
    extract:
        status: json.status
        success : json.success
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ json.message, "服务器成功返回" ]

- test:
    name: SETUP-创建待发布的印章
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sign01No
        customOrgNo: $org01No
        sealGroupName: $sealGroupName2
        sealTypeCode: "COMMON-SEAL"
        sealRelease: 0
        sealInfos:
          - sealPattern: 1
            sealTemplateStyle: 1
            sealName: "SCENE-004"
            sealSource: 2
    extract:
      sealTC003: content.data.sealInfos.0.sealId
      sealGroupId_002: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]
    setup_hooks:
      - ${sleep(5)}

- test:
    name: TC-验证查询用户的企业授权印章列表-印章3待发布印章查询为空
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC003
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records, []]
      - eq: [ content.data.total, 0 ]

- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章3默认授权全部项目
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
        sealCode: ""
        pageNo: 1
        pageSize: 10
        sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 1 ]
      - eq: [ content.data.projectSealInfos, []]


- test:
    name: TC-查询印章分组中印章id
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId_002
    extract:
      sealTC_003: content.data.list.0.sealId
      remoteSealId_003: content.data.list.0.remoteSealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: 企业电子印章管理-授权指定可用项目1
    api: api/esignSeals/seals/enterprise/electronic/authSealProject.yml
    variables:
        authorization0: $authorization0_tc_auth
        sealId: $sealTC_003
        sealProjectRange: "2"
        projectIds: ["$projectId1"]
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ $message, "服务器成功返回"]


- test:
    name: TC--印章项目3授权自动签署
    api: api/esignSeals/seals/enterprise/electronic/authAutoSignSealProject.yml
    variables:
      sealId: $sealTC_003
      projectIds: ["$projectId1"]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "服务器成功返回"]


- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章3授权项目是项目1且自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealCode: ""
      pageNo: 1
      pageSize: 10
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 2 ]
#      - eq: [ content.data.projectSealInfos.0.projectId, $projectId1 ] #印章发发布状态，查询不到这个参数
#      - eq: [ content.data.projectSealInfos.0.autoSign, 1 ]


- test:
    name: TC-验证查询用户的企业授权印章列表-印章3待发布印章查询为空
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC003
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records, [] ]
      - eq: [ content.data.total, 0 ]
#发布印章
- test:
    name: 发布印章
    api: api/esignSeals/seals/enterprise/electronic/publishSeal.yml
    variables:
      "sealId": $sealTC_003
    validate:
      - eq: [ json.status, 200 ]
      - eq: [ json.success, True ]
      - eq: [ json.message, "服务器成功返回" ]
 #发布后的印章可通过接口”查询电子印章授权的用印人“查询详情
- test:
    name: TC-验证查询用户的企业授权印章列表-印章3授权项目是项目1且自动签署
    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
    variables:
      json: {
        pageNo: 1,
        pageSize: 10,
        sealPattern: 1,
        sealId: $sealTC003
      }
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.records.0.projectScope, 2 ]
      - eq: [ content.data.records.0.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.records.0.projectSealInfos.0.autoSign, 1 ]
      - eq: [ content.data.records.0.sealId, $sealTC003 ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC-验证查询电子印章授权的用印人信息列表-印章3授权项目是项目1且自动签署
    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
    variables:
      sealCode: ""
      pageNo: 1
      pageSize: 10
      sealId: $sealTC003
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - eq: [ content.data.projectScope, 2 ]
      - eq: [ content.data.projectSealInfos.0.projectId, $projectId1 ]
      - eq: [ content.data.projectSealInfos.0.autoSign, 1 ]

- test:
    name: 停用电子印章
    api: api/esignSeals/smc/seals/stopEnterpriseSeal.yml
    variables:
      stopSeal_sealId: $sealTC_003
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - len_gt: [$message, 1]

- test:
    name: 吊销印章3
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealTC_003
    extract:
        status: json.status
        success : json.success
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [ json.message, "服务器成功返回" ]
#- test:
#    name: TC-删除印章3
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/delete.yml
#    variables:
#      json:
#        sealId: $sealTC003
#    validate:
#      - eq: [ content.code, 200 ]
#      - contains: [ content.message, "成功" ]
#      - not_equals: [ content.data, "" ]


# 废弃
#- test:
#    name: TC-projectScope传入非1和2的数字10，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 10
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code,  1313050]
#      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[10]" ]
#      - eq: [ content.data, null ]
#
#
#- test:
#    name: TC-projectScope传入非1和2的数字0，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 0
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code,  1313050]
#      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[0]" ]
#      - eq: [ content.data, null ]
#
#
#- test:
#    name: TC-projectScope传入字母，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: "aa"
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 1313050 ]
#      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[aa]" ]
#      - eq: [ content.data, null ]
#
#
#- test:
#    name: TC-projectScope传入汉字，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: "中国"
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 1313050 ]
#      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[中国]" ]
#      - eq: [ content.data, null ]
#
#- test:
#    name: TC-projectScope传入字符，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: "&^%"
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code,  1313050]
#      - eq: [ content.message, "projectScope错误：可用项目授权范围不支持传入[&^%]" ]
#      - eq: [ content.data, null ]



#
#
#- test:
#    name: TC-待发布印章，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: ""
#      sealId: $sealTC004
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC004" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#
#- test:
#    name: TC-projectScope传入空，sealProjectIds传null，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: ""
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#    setup_hooks:
#      - ${sleep(5)}
#
#
#
#- test:
#    name: TC-projectScope传入null，sealProjectIds传null，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: null
#      sealId: $sealTC001
#      sealProjectIds: []
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope不传，sealProjectIds不传，授权待发布印章全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      sealId: $sealTC003
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId,"$sealTC003" ]
#      - eq: [ content.data.projectScope, "1"]
#      - eq: [ content.data.sealProjectIds, null ]
#
#
#- test:
#    name: TC-projectScope传入null，sealProjectIds传空，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: null
#      sealId: $sealTC001
#      sealProjectIds: [""]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope传入null，sealProjectIds传入任意参数，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: null
#      sealId: $sealTC001
#      sealProjectIds: ["231232","2434"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope传入1，sealProjectIds传入任意参数，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: null
#      sealId: $sealTC001
#      sealProjectIds: ["2312323"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1"]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope传入1，sealProjectIds传入项目id1，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 1
#      sealId: $sealTC001
#      sealProjectIds: ["$projectId1"]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope传入1，sealProjectIds传入null，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 1
#      sealId: $sealTC001
#      sealProjectIds: [ ]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-projectScope传入1，sealProjectIds传入""，授权全部项目
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 1
#      sealId: $sealTC001
#      sealProjectIds: [""]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "1" ]
#      - eq: [ content.data.sealProjectIds, null ]
#
#- test:
#    name: TC-验证查询用户的企业授权印章列表-授权全部项目
#    api: api/esignSeals/sealcontrols/organizationSeals/list.yml
#    variables:
#      json: {
#        pageNo: 1,
#        pageSize: 10,
#        sealPattern: 1,
#        sealId: $sealTC001
#      }
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.records.0.projectScope, 1 ]
#      - eq: [ content.data.records.0.sealProjectIds, null ]
#      - eq: [ content.data.records.0.sealId, $sealTC001 ]
#      - eq: [ content.data.total, 1 ]
#
#- test:
#    name: TC-验证查询电子印章授权的用印人信息列表-授权全部项目
#    api: api/esignSeals/v1/sealcontrols/sealsigners/sealsignersList.yml
#    variables:
#        sealCode: ""
#        pageNo: 1
#        pageSize: 10
#        sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.projectScope, 1 ]
#      - eq: [ content.data.sealProjectIds, [] ]
#
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传入""，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: [""]
#    validate:
#      - eq: [ content.code, 1904066 ]
#      - contains: [ content.message, "不存在" ]
#      - eq: [ content.data, null ]
#
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传入null，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: []
#    validate:
#      - eq: [ content.code, 1313051 ]
#      - eq: [ content.message, "授权可用项目集合不能为空" ]
#      - eq: [ content.data, null ]
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds不传，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#    validate:
#      - eq: [ content.code, 1313051 ]
#      - eq: [ content.message, "授权可用项目集合不能为空" ]
#      - eq: [ content.data, null ]
#
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传英文，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: ['qq']
#    validate:
#      - eq: [ content.code, 1904066 ]
#      - eq: [ content.message, "项目ID qq 不存在" ]
#      - eq: [ content.data, null ]
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传中文，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: ['为']
#    validate:
#      - eq: [ content.code, 1904066 ]
#      - eq: [ content.message, "项目ID 为 不存在" ]
#      - eq: [ content.data, null ]
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传字符，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: ['@%']
#    validate:
#      - eq: [ content.code, 1904066 ]
#      - eq: [ content.message, "项目ID @% 不存在" ]
#      - eq: [ content.data, null ]
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传不存在的projectId，报错
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: ['11212222']
#    validate:
#      - eq: [ content.code, 1904066 ]
#      - eq: [ content.message, "项目ID 11212222 不存在" ]
#      - eq: [ content.data, null ]
#
#
#
#- test:
#    name: TC-projectScope传入2，sealProjectIds传projectId1,授权指定项目projectId1
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealProjects.yml
#    variables:
#      projectScope: 2
#      sealId: $sealTC001
#      sealProjectIds: [ "$projectId1" ]
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - eq: [ content.data.sealId, "$sealTC001" ]
#      - eq: [ content.data.projectScope, "2" ]
#      - eq: [ content.data.sealProjectIds, ['$projectId1'] ]