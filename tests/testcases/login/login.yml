- config:
    name: "手机号/邮箱登录单接口"


- test:
    name: $name
    variables:
        - account: $account_1
        - accountType: $accountType_1
        - dynamicCode: $dynamicCode_1
        - platform: $platform_1
        - userCode: $userCode_1
        - verificationCode: $verificationCode_1
        - headerVerificationCode: ${get_2()}
        - userTerritory: $userTerritory
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",$except_code_4]
#        - eq: [ "content.message", $except_message_4]