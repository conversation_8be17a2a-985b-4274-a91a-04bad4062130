- config:
    name: 重置密码
    variables:
        - accounts: ${ENV(zidhdl.userCode)}
        - passwords: ${ENV(login.password)}
        - userCodes: "Q9TMvHkqhkfGxG3A/bdUvF3/sLiUBBNxaEDdhRRby7xFHrlVPHM8lw1V9HAV1QCMAX48TPQ6m3dsr8wH9h/md+v/NB0wQhyb0pkHMliU1qTGfjbGL5qi6ybzSSHLg8gBzUXWJHkdT31GwxgT0imARcuq+i5Gtb70mTPHcqIg+4vIviS8GPnPOIf01RuJFnwK08RNk3t3jS+sOAxG3HHM2X3nRhCQGgIyadHkszXo+APTzYg4iXikSgvFj16at88cspMxV96hZ4Eap0eUrX66lJoVsm2DsDcRaiAapShHW6i6Ba/H+tPqMOKoNbgQkiIOyM0ZD0ySoW9sMnsgJ7JMLA=="
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 1
#- test:
#    name: 获取内部账号信息
#    api: api/esignLogin/sso/listAccountInfo.yml
#    variables:
#        - account: $accounts
#        - accountType: $accountTypes
#        - dynamicCode: ""
#    extract:
#        - code: content.data.0.userCode
#        - password1: content.data.0.password
#    validate:
#        - eq: [content.status, 200]
#- test:
#    name: 重置密码
#    api: api/esignLogin/sso/retrievePassword.yml
#    variables:
#        - accountType: $accountTypes
#        - passwordOld: $password1
#        - password: $passwordResets
#        - confirmPassword: $passwordResets
#        - userCode: $userCodes
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]
#- test:
#    name: 再次获取外部账号信息
#    api: api/esignLogin/sso/listAccountInfo.yml
#    variables:
#        - account: $accounts
#        - accountType: $accountTypes
#        - dynamicCode: ""
#    extract:
#        - code: content.data.0.userCode
#        - password2: content.data.0.password
#    validate:
#        - eq: ["content.status", 200]
#- test:
#    name: 重置成原来的密码
#    api: api/esignLogin/sso/retrievePassword.yml
#    variables:
#        - accountType: $accountTypes
#        - passwordOld: $password2
#        - password: $passwords
#        - confirmPassword: $passwords
#        - userCode: $userCodes
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]

- test:
    name: 老的和新的一样
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwords
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    setup_hooks:
        - ${sleep(2)}
    validate:
        - eq: ["content.status", 1412017]
        - eq: ["content.message", 新密码不能与原密码相同]
- test:
    name: 重置密码
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwords
        - password: $passwordResets
        - confirmPassword: $passwordResets
        - userCode: $userCodes
    extract:
        - moudle: content.data
    setup_hooks:
        - ${sleep(2)}
    validate:
        - eq: ["content.status", 200]

- test:
    name: 重置成原来的密码
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwordResets
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    setup_hooks:
        - ${sleep(2)}
    validate:
        - eq: ["content.status", 200]

- test:
    name: 老密码空的
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: ""
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 旧密码不能为空]
- test:
    name: 新密码空的
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwords
        - password: ""
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 密码不能为空]

- test:
    name: 密码确认空的
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwords
        - password: $passwords
        - confirmPassword: ""
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 密码不能为空]

- test:
    name: 用户code为空
    api: api/esignLogin/sso/resetPassword.yml
    variables:
        - passwordOld: $passwords
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: ""
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412999]
        - eq: ["content.success", false]