- config:
    name: 外部用户手机号登录-手机号对应2个账号
    variables:
        - accounts: ${ENV(zidhdl.mobileCoded)}
        - passwords: ${ENV(login.password)}
#        - userCodes: ${ENV(userCodeWb)}
        - passwordResets: ${ENV(login.passwordReset)}
        - loginUrl: ${ENV(esign.projectHost)}
- test:
    name: 发送动态验证码-正确
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 发送动态验证码-没mock
    variables:
        - account: "g1NIAsCA3bJaJRYcCwuLPg=="
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 发送动态验证码-错误账号
    variables:
        - account: "41NIAsCA3bJaJRYcCwuLPg=="
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412999]

- test:
    name: 发送动态验证码-图形验证码格式不正确
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "34j4"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412012]
        - eq: [ "content.message", 图形验证码格式不正确]
- test:
    name: 发送动态验证码-图形验证码已过期
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: "hdwduwyey883"
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412010]
        - eq: [ "content.message", 图形验证码已过期]


- test:
    name: 发送动态验证码-类型不存在
    variables:
        - account: $accounts
        - accountType: 1
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 账号类型不正确]

- test:
    name: 发送动态验证码-手机号不存在
    variables:
        - account: "x+LKb5gZA/9tjYDN663Kwg=="
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.message", 手机号/邮箱或账号状态不正确]

- test:
    name: 发送动态验证码-手机号不存在
    variables:
        - account: "x+LKb5gZA/9tjYDN663Kwg=="
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.message", 手机号/邮箱或账号状态不正确]

- test:
    name: 发送动态验证码-手机号格式不正确
    variables:
        - account: "c0dxb6rhmTQZnYu8xG309vJ+0EdWI1Nz9OSnx2kTLOM="
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412008]
        - eq: [ "content.message", 手机号格式不正确]

- test:
    name: 发送动态验证码-类型空的
    variables:
        - account: $accounts
        - accountType: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]

- test:
    name: 发送动态验证码-验证码空的
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ""
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412011]
        - eq: [ "content.message", 图形验证码不能为空]
- test:
    name: 发送动态验证码-账号空的
    variables:
        - account: ""
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: "2"
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 账号不能为空]

- test:
    name: 手机号登录
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: $userCodes
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 手机号登录
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: $userCodes
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 手机号登录-错误
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "367848"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412013]

- test:
    name: 手机号登录-验证码错误
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "1234"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412003]



- test:
    name: 手机号登录-格式不正确
    variables:
        - account: "fjRK6z8jHXTm9cO+8L0NlhLAifYR7g0gw9+iHTWvX64="
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412008]
        - eq: [ "content.message",手机号格式不正确]
- test:
    name: 手机号登录-手机号不存在
    variables:
        - account: "x+LKb5gZA/9tjYDN663Kwg=="
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: "2"
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.message",手机号/邮箱或账号状态不正确]