- config:
    name: 内部用户手机号登录-手机号对应2个账号
    variables:
        - accounts: ${ENV(zidhdl.mobileCoded)}
        - passwords: ${ENV(login.password)}
        - userCodes: ${ENV(zidhdl.userCode)}
        - passwordResets: ${ENV(login.passwordReset)}
        - loginUrl: ${ENV(esign.projectHost)}
        #不存在的手机号
        - mobileNotExist: "RmL9x/SywtfAXhyVm1ty1hLii89bYx85ZgK2rvL9XipWQIR2G1E5LrICfJm8N7vTJXIS/lsEFDg/uNtjlNDlMRhECZZdlxP4N5xs/RuxYXQmsOba/8vb/1oytBZo9Jk+NLS2J1287WTYPb7yacyHcdK+yi6OhsI+le6wK2687/YpQm8wDsoz6PNe3aubVC/s2rwy/ef2a8UB0+O5kdMNmHeqHsRWh80O0Tlubg8nPlhh8QdonYWQAZ2fZnEnKf7FELXRS5qubm7UiJ9jRj9aMMH3sbl6Hiae4N71GMaeALQ34MgHo9DPZ0sSIS7cKIaEEDUqxnFFd+V8Z10bthH0nw=="
        # 错误的手机格式
        - mobileError: "RmL8x/SywtfAXhyVm1ty1hLii89bYx85ZgK2rvL9XipWQIR2G1E5LrICfJm8N7vTJXIS/lsEFDg/uNtjlNDlMRhECZZdlxP4N5xs/RuxYXQmsOba/8vb/1oytBZo9Jk+NLS2J1287WTYPb7yacyHcdK+yi6OhsI+le6wK2687/YpQm8wDsoz6PNe3aubVC/s2rwy/ef2a8UB0+O5kdMNmHeqHsRWh80O0Tlubg8nPlhh8QdonYWQAZ2fZnEnKf7FELXRS5qubm7UiJ9jRj9aMMH3sbl6Hiae4N71GMaeALQ34MgHo9DPZ0sSIS7cKIaEEDUqxnFFd+V8Z10bthH0nw=="

- test:
    name: 发送动态验证码-正确
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

#- test:
#    name: 发送动态验证码-没mock
#    variables:
#        - account: "g1NIAsCA3bJaJRYcCwuLPg=="
#        - accountType: 4
#        - verificationDict: ${get_verification_dict()}
#        - verificationCode: "9999"
#        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
#        - scene: 1
#        - userTerritory: null
#    api: api/esignLogin/sso/sendDynamicCode.yml
#    validate:
#        - eq: [ "content.status",200]
#        - eq: [ "content.message", 成功]

- test:
    name: 发送动态验证码-错误账号
    variables:
        - account: "$mobileError"
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
        - scene: 1
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message",'入参有误']

- test:
    name: 发送动态验证码-图形验证码格式不正确
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "34j4"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412012]
        - eq: [ "content.message", 图形验证码格式不正确]
- test:
    name: 发送动态验证码-图形验证码已过期
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: "hdwduwyey883"
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412010]
        - eq: [ "content.message", 图形验证码已过期]


- test:
    name: 发送动态验证码-类型不存在
    variables:
        - account: $accounts
        - accountType: 1
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 账号类型不正确]

- test:
    name: 发送动态验证码-手机号不存在
    variables:
        - account: "$mobileNotExist"
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.data",""]
        - eq: [ "content.message", "手机号/邮箱或账号状态不正确"]

- test:
    name: 发送动态验证码-手机号格式不正确
    variables:
        - account: "$mobileError"
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
#        - eq: [ "content.status",1412008]
#        - eq: [ "content.message", 手机号格式不正确]
        - eq: [ "content.status",1412022 ]
        - eq: [ "content.message",入参有误 ]
#    teardown_hooks:
#        - ${sleep(60)}

- test:
    name: 发送动态验证码-类型空的
    variables:
        - account: $accounts
        - accountType: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]


- test:
    name: 发送动态验证码-验证码空的
    variables:
        - account: $accounts
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ""
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412011]
        - eq: [ "content.message", 图形验证码不能为空]
- test:
    name: 发送动态验证码-账号空的
    variables:
        - account: ""
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 账号不能为空]

- test:
    name: 手机号登录-account正确，userCode为空
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - scene: 1
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
#        - sendDynamicCode: ${get_dynamicCode($accounts,$verificationCode,$headerVerificationCode)}
        - sendDynamicCode: ${get_dynamicCode($accounts,$verificationCode}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 手机号登录-account正确，userCode也正确
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: $userCodes
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 手机号登录-动态验证码错误
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "2367848"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412022]
        - contains: [ "content.message","动态验证码格式不正确"]

- test:
    name: 手机号登录-验证码错误
    variables:
        - account: $accounts
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "1234"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412003]
        - contains: [ "content.message","图形验证码错误"]


- test:
    name: 手机号登录-格式不正确
    variables:
        - account: "$mobileError"
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
#        - eq: [ "content.status",1412008]
#        - eq: [ "content.message",手机号格式不正确]
        - eq: [ "content.status",1412022]
        - eq: [ "content.message",入参有误]
- test:
    name: 手机号登录-手机号不存在
    variables:
        - account: "$mobileNotExist"
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.message", "手机号/邮箱或账号状态不正确"]