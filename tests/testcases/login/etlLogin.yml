- config:
    name: 单点登录openapi
    variables:
        - accountneibu: ${ENV(sign01.accountNo)}
#        - accountneibu: "yitao"
        - accountwaibu: ${ENV(wsignwb01.accountNo)}
        - key: ${ENV(esign.projectSecret)}
        - accountNo: etlloginuser${generate_random_str(6)}
        - email: $<EMAIL>
        - organizationCode1: ${ENV(sign01.main.orgCode)}

##创建一个内部用户和外部用户，账号相同
- test:
    name: 创建外部用户
    variables:
        data: [
          {
            "customAccountNo": $accountNo,
            "name": "测试重复",
            "mobile": ,
            "email": $email,
            "licenseType": ,
            "licenseNo": ,
            "bankCardNo": '',
            "mainOrganizationCode": ,
            "mainCustomOrgNo": ,
            "otherOrganization": [
            ]
          }
        ]
    extract:
      userCode11: content.data.successData.0.userCode
    api: api/esignManage/outerUsers/create.yml
    validate:
      - eq: [ json.code,200 ]
      - eq: [ json.message,"成功" ]

##创建内部用户
- test:
    name: setup-创建内部用户
    variables:
        data: [
                {
                    "customAccountNo": $accountNo,
                    "name": "诸葛一",
                    "mobile": "",
                    "email": "$email",
                    "licenseType": ,
                    "licenseNo": ,
                    "bankCardNo": ,
                    "mainOrganizationCode": $organizationCode1,
                    "mainCustomOrgNo": ,
                    "otherOrganization": [
                        {
                            "otherCustomOrgNo": "",
                            "otherOrganizationCode": ""
                        }
                    ]
                }

            ]
    api: api/esignManage/InnerUsers/create.yml
    setup_hooks:
      - ${sleep(0.1)}
    validate:
      - eq: [ json.code,200 ]
      - eq: [ json.message,"成功" ]

- test:
    name: clientId、clientSecret都为空
    variables:
        - clientId:
        - clientSecret:
    api: api/esignLogin/getAccessToken.yml
    validate:
        - eq: [ "content.status",2001]
        - eq: [ "content.message", "clientId不能为空"]
        - eq: [ "content.success", false]

- test:
    name: clientId都为空
    variables:
        - clientId:
    api: api/esignLogin/getAccessToken.yml
    validate:
        - eq: [ "content.status",2001]
        - eq: [ "content.message", "clientId不能为空"]
        - eq: [ "content.success", false]
- test:
    name: clientSecret都为空
    variables:
        - clientSecret:
    api: api/esignLogin/getAccessToken.yml
    validate:
        - eq: [ "content.status",2001]
        - eq: [ "content.message", "clientSecret不能为空"]
        - eq: [ "content.success", false]


- test:
    name: clientSecret不对
    variables:
        - clientId: "99999888"
    api: api/esignLogin/getAccessToken.yml
    validate:
        - eq: [ "content.status",1260202]
        - eq: [ "content.message", clientId没有找到]
        - eq: [ "content.success", false]

- test:
    name: clientId不存在
    variables:
        - clientId: "99999888"
    api: api/esignLogin/getAccessToken.yml
    validate:
        - eq: [ "content.status",1260202]
        - eq: [ "content.message", clientId没有找到]
        - eq: [ "content.success", false]

- test:
    name: 正常获取到accessToken
    variables:
    api: api/esignLogin/getAccessToken.yml
    extract:
        accessToken1: content.data.accessToken
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]

##获取ssotoken
- test:
    name: 获取ssotoken--正常
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountneibu)}
    api: api/esignLogin/getSsoToken.yml
#    extract:
#        accessToken: $content.data.accessToken
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

##获取ssotoken
- test:
    name: 获取ssotoken--userType不传，取内部
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
    api: api/esignLogin/getSsoToken.yml
#    extract:
#        accessToken: $content.data.accessToken
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: 获取ssotoken--userType传内部
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 1
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: 获取ssotoken--userType传外部
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: 获取ssotoken--redirecturl传空
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
        - redirectUrl: ""
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - eq: [ "content.data.ssoRedirectUrl", null]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: 获取ssotoken--redirecturl传null
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
        - redirectUrl: null
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - eq: [ "content.data.ssoRedirectUrl", null]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: 获取ssotoken--clientId传null
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
        - clientId: null
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",2001]
        - eq: [ "content.message", "clientId不能为空"]
        - eq: [ "content.success", false]

- test:
    name: 获取ssotoken--clientId传空
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
        - clientId: ""
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",2001]
        - eq: [ "content.message", "clientId不能为空"]
        - eq: [ "content.success", false]

- test:
    name: 获取ssotoken--access不对
    variables:
        - accessToken: "dhsahdasjdhasjd"
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",1260206]
        - contains: [ "content.message", "accessToken已失效"]
        - eq: [ "content.success", false]


- test:
    name: 获取ssotoken--loginAccount不加密的
    variables:
        - accessToken: $accessToken1
        - loginAccount: $accountNo
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",1260209]
        - eq: [ "content.message", 用户账号解码失败]
        - eq: [ "content.success", false]
        - eq: [ "content.data", null]

- test:
    name: 获取ssotoken--loginAccount不传
    variables:
        - accessToken: $accessToken1
        - loginAccount: ""
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.message", "登录账号不能为空"]
        - eq: [ "content.success", false]
        - eq: [ "content.status", 2001]
- test:
    name: 获取ssotoken--loginAccount传null
    variables:
        - accessToken: $accessToken1
        - loginAccount: null
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.message", "登录账号不能为空"]
        - eq: [ "content.success", false]
        - eq: [ "content.status", 2001]

- test:
    name: 获取ssotoken--accessToken不传
    variables:
        - accessToken: ""
        - loginAccount: "${get_aes_encrypt1($accountNo)}"
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",2001]
        - contains: [ "content.message", "accessToken不能为空"]
        - eq: [ "content.success", false]


- test:
    name: 获取ssotoken--accessToken为null
    variables:
        - accessToken: null
        - loginAccount: "${get_aes_encrypt1($accountNo)}"
        - userType: 2
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",2001]
        - contains: [ "content.message", "accessToken不能为空"]
        - eq: [ "content.success", false]

- test:
    name: 删除内部用户
    variables:
        data:
            userCode:
            customAccountNo: $accountNo
    api: api/esignManage/InnerUsers/delete.yml
    validate:
        - eq: [ json.code,200 ]
        - eq: [ json.message,"成功" ]

- test:
    name: 删除外部用户
    variables:
        customAccountNo: $accountNo
        userCode:
    api: api/esignManage/outerUsers/delete.yml
    validate:
        - eq: [ json.code,200 ]
        - eq: [ json.message,"成功" ]