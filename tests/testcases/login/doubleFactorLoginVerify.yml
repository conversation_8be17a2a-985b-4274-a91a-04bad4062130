- config:
    name: "登录验证-双因子登录"
- test:
    name: $casename
    variables:
        - account: $account_1
        - password: $password_1
        - platform: $platform_1
        - target: $target_1
        - verificationCode: $verificationCode_1
    api: api/esignLogin/sso/doubleFactorLoginVerify.yml
#    extract:
#        account_1:
#        accountEncrypt_1:

    validate:
        - eq: [ "content.status",$except_code_1]
        - eq: [ "content.message", $except_message_1]
        - eq: [ "content.success",$except_succes_1]