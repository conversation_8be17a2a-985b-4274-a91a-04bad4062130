- config:
    name: 内部用户账号登录
    variables:
        - mobile01: ${ENV(zidhdl.mobile)}
        - accounts: ${ENV(zidhdl.userCode)}
        - passwords: ${ENV(login.password)}
        - userCodes: ${ENV(zidhdl.userCode)}
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 4  # 账号类型 1用户编码 2账号 3邮箱 4手机号
        - mobileCoded: ${ENV(zidhdl.mobileCoded)}
- test:
    name: 发送动态验证码-正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationCode: 9999
        - headerVerificationCode: ${get_2()}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", true]
- test:
    name: 获取内部账号信息
    api: api/esignLogin/sso/listAccountInfo.yml #该接口用于登录页-忘记密码
    variables:
        - account: $mobile01
        - accountType: $accountTypes
        - sendDynamic: ${get_dynamicCode1($accounts)}
        - dynamicCode: "123456"
    extract:
        - code: content.data.0.userCode
        - password1: content.data.0.password
        - account1: content.data.0.accountNumber
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["$account1", $accounts]
- test:
    name: 重置密码
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password1
        - password: $passwordResets
        - confirmPassword: $passwordResets
        - userCode: $userCodes
    extract:
        - moudle: content.data
    setup_hooks:
        - ${sleep(5)}
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]
- test:
    name: 再次获取内部账号信息
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobile01
        - accountType: $accountTypes
        - sendDynamic: ${get_dynamicCode1($accounts)}
        - dynamicCode: "123456"
    extract:
        - code: content.data.0.userCode
        - password2: content.data.0.password
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]
- test:
    name: 重置成原来的密码
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password2
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]
- test:
    name: 登录
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: $passwords
        - type: 1
        - platform: H5
        - verificationCodeHeader: ${get_verificationCode1()}
    extract:
        - jumpUrl: content.data.jumpUrl
        - tCode1: content.data.tCode
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.data.userCode", $userCodes]

- test:
    api: api/esignLogin/sso/getTokenByCode.yml
    variables:
        - tCode: $tCode1
    extract:
        - token1: content.data.token
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]
- test:
    name: 账号校验
    api: api/esignLogin/sso/accountVerify.yml
    variables:
        - userCode: $userCodes
        - referer: "www.baidu.com"
        - platform: "PC"
        - token: $token1
    validate:
        - eq: [content.status, 200]
        - eq: ["content.message", 成功]
        - eq: ["content.data.whetherMatch", false]
- test:
    name: 登出-token正确
    api: api/esignLogin/sso/logout.yml
    variables:
        - token: $token1
#    extract:
#        - moudle: content.data
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]

- test:
    name: 查询冻结状态-未冻结-入参未加密
    api: api/esignLogin/sso/findAccountFreezeStatus.yml
    variables:
        - userCode: $userCodes
    validate:
        - eq: ["content.status", 1412823]
        - eq: ["content.message", "入参解密失败,请检查传参是否规范"]
        - eq: ["content.success", false]
        - eq: ["content.data", ""]

- test:
    name: 查询冻结状态-未冻结
    api: api/esignLogin/sso/findAccountFreezeStatus.yml
    variables:
        - userCode: ${encryptKey($userCodes)}
    validate:
        - eq: ["content.status", 200]
        - eq: ["content.message", 成功]
        - eq: ["content.success", true]