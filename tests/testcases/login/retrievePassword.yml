- config:
    name: 找回密码单接口用例
    variables:
        - accounts: ${ENV(zidhdl.userCode)}
        - passwords: ${ENV(login.password)}
        - userCodes: ${ENV(zidhdl.userCode)}
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 2
        - mobileDj: ${ENV(zidhdl.mobile)}
        - mobileCoded: ${ENV(zidhdl.mobileCoded)}

- test:
    name: 发送动态验证码-正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationCode: 9999
        - headerVerificationCode: ${get_2()}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 获取外部账号信息
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobileDj
        - accountType: 4
        - dynamicCode: "123456"
    extract:
        - code: content.data.0.userCode
        - password1: content.data.0.password
    validate:
        - eq: [content.status, 200]

- test:
    name: 密码不一致
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password1
        - password: $passwordResets
        - confirmPassword: "CFpzB8omj6IXRsChADI7LYy304FTejs3nnriKYKhLDDdplQndMUEvJt20c9y0nqsnOoQoRndSiM5H4V9en/N/lKL5dvskv0cwFbolf+kWwtlhbZnMGsq1ZAO6AV6PXAHkmwqltizxjEpl794IalAlbO7r9PQhHPXtfQNOYksGgwIgmx/GeuIon7ksaHC7KUM0rgMY7zUegaw0qAw51Nr5EylCTPOkY1MEN+VPKbjJLvGEqez1GpgXSCSBUfBQx5fg63QPxtPonOhi+ZHYBqo/9tJroxaDuQyLtXKgas5kD6r1zEsLhQOKj/oecVw8QaOFarpvq75aIpmZCG1gxx0bA=="
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412016]
        - eq: ["content.message", 两次输入的密码不一致，请重新输入]
- test:
    name: 密码格式有误
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $passwords
        - password: $passwords
        - confirmPassword: $passwords
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412014]
        - eq: ["content.message", 密码输入有误]

- test:
    name: passwordOld为空
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: ""
        - password: $passwordResets
        - confirmPassword: $passwordResets
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 旧密码不能为空]
- test:
    name: password为空
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password1
        - password: ""
        - confirmPassword: $passwordResets
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 密码不能为空]

- test:
    name: confirmPassword为空
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password1
        - password: $passwordResets
        - confirmPassword: ""
        - userCode: $userCodes
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412022]
        - eq: ["content.message", 密码不能为空]

- test:
    name: userCode为空
    api: api/esignLogin/sso/retrievePassword.yml
    variables:
        - accountType: $accountTypes
        - passwordOld: $password1
        - password: $passwordResets
        - confirmPassword: $passwordResets
        - userCode: ""
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 913]