- config:
    name: "登录单接口"
    variables:
      - host: ${ENV(esign.projectHost)}

- test:
    name: $casename
    variables:
      - account: $account_1
      - password: $password_1
      - type: $accountType_1
      - platform: pc
      - verificationCodeHeader: ${get_verificationCode1()}
    api: api/esignLogin/sso/accountLogin.yml
    validate:
      - eq: [ "content.status",$except_code_4 ]
      - contains: [ "content.message", $except_message_4 ]