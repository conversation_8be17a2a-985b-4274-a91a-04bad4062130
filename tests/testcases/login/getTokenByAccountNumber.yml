- config:
    name: "根据用户账号获取Token"

- test:
    name: 入参为空
    variables:
        - accountNumber: ""
    api: api/esignLogin/sso/getTokenByAccountNumber.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 用户账号不能为空]

- test:
    name: 入参为空
    variables:
        - accountNumber: null
    api: api/esignLogin/sso/getTokenByAccountNumber.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 用户账号不能为空]

- test:
    name: 账号存在
    variables:
        - accountNumber: admin
    api: api/esignLogin/sso/getTokenByAccountNumber.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 账号不存在
    variables:
        - accountNumber: duizduizduiz
    api: api/esignLogin/sso/getTokenByAccountNumber.yml
    validate:
        - eq: [ "content.status",1412019]
        - eq: [ "content.message", 账号、密码或账号状态不正确]