- config:
    name: "正式登录-双因子登录"
- test:
    name: $casename_2
    variables:
        - account: $account_2
        - accountType: $accountType_2
        - dynamicCode: $dynamicCode_2
        - platform: $platform_2
        - referer: $referer_2
        - target: $target_2
        - userCode: $userCode_2
        - userTerritory: $userTerritory_2
    api: api/esignLogin/sso/doubleFactorLogin.yml
#    extract:
#        account_1:
#        accountEncrypt_1:
    validate:
        - eq: [ "content.status",$except_code_2]
        - eq: [ "content.message", $except_message_2]
        - eq: [ "content.success",$except_succes_2]