- config:
    name: 外部用户账号登录---只能用手机号邮箱登录
    variables:
        - mobile01: ${ENV(zidhdl.mobile)}
#        - accounts: ${ENV(accountWb)}
        - passwords: ${ENV(login.password)}
#        - userCodes: ${ENV(userCodeWb)}
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 4

#- test:
#    name: 获取外部账号信息
#    api: api/esignLogin/sso/listAccountInfo.yml
#    variables:
#        - account: $mobile01
#        - accountType: $accountTypes
#        - sendDynamic: ${get_dynamicCode1($accounts)}
#        - dynamicCode: "123456"
##    extract:
##        - code: content.data.0.userCode
##        - password1: content.data.0.password
#    validate:
#        - eq: [content.status, 1412027]
#        - eq: [content.message, 手机号/邮箱不存在或账号状态异常]
#门户不允许外部用户登录
#- test:
#    name: 重置密码
#    api: api/esignLogin/sso/retrievePassword.yml
#    variables:
#        - accountType: $accountTypes
#        - passwordOld: $password1
#        - password: $passwordResets
#        - confirmPassword: $passwordResets
#        - userCode: $userCodes
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]
#- test:
#    name: 再次获取外部账号信息
#    api: api/esignLogin/sso/listAccountInfo.yml
#    variables:
#        - account: $accounts
#        - accountType: $accountTypes
#        - dynamicCode: ""
#    extract:
#        - code: content.data.0.userCode
#        - password2: content.data.0.password
#    validate:
#        - eq: ["content.status", 200]
#- test:
#    name: 重置成原来的密码
#    api: api/esignLogin/sso/retrievePassword.yml
#    variables:
#        - accountType: $accountTypes
#        - passwordOld: $password2
#        - password: $passwords
#        - confirmPassword: $passwords
#        - userCode: $userCodes
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]
#
#- test:
#    name: 登录
#    api: api/esignLogin/sso/accountLogin.yml
#    variables:
#        - account: $accounts
#        - password: $passwords
#        - type: 1
#        - platform: pc
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]


- test:
    name: 登出-无效请求
    api: api/esignLogin/sso/logout.yml
    variables:
        - token: hdjhjd
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.message", 无效的请求]

- test:
    name: 登出-无效请求
    api: api/esignLogin/sso/logout.yml
    variables:
        - token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb21lIjoicGF5bG9hZCJ9.4twFt5NiznN84AWoo1d7KO1T_yoc0Z6XOpOVswacPZg"
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.message", 无效的请求]

- test:
    name: 登录-admin无权登录
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: admin
        - password: mQmh5IYz53oICq0fg5tmmXZJb1983gybGuJkTczUoKw=
        - type: 1
        - platform: pc
        - verificationCodeHeader: ${get_verificationCode1()}
    extract:
        - moudle: content.data
    validate:
        - eq: ["content.status", 1412805]
        - eq: ["content.message", "admin 无权登录业务系统"]