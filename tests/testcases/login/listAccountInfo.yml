- config:
    name: 获取账号信息-找回密码
    variables:
        - mobile: ${ENV(zidhdl.mobile)}
        - mobileCoded: ${ENV(zidhdl.mobileCoded)}
        - accountTypes: 4
        #系统不存在的手机号
        - mobileCoded02: XlptQtrIsHmFNlHs5t24XCMmOhgzeTp82WLVGO4xhxJaP67Z0trQ8w5giPD9HL3aj7j+XhpNABgnQFWNB4KelrDEsUVOzntLapEFbjG4zOU0kZfLLYpIzvDiDQBPivpTRlqkU2gOrJ0GGdHvcpv8cSqIiK4ZZ7QYbvlvme/1uAcjEwAJZ9/neoHT7LDA2a9W3tlfA/iQkyZ5Vc3u0c8CZVcPRsIgoLp3j3P+Rox+n4iZ7g91lRhMk3MYfFTpcRXSuP3l5MfjoFOeO7IItq9OVmEAz9M0Vl2REQMWmN5cN8kPOu3ALHRhnjtGIpRphMC4w9jRKY8zV8urUueB6W4UHA==

- test:
    name: 发送动态验证码-图形验证码为空
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationCode: ""
        - headerVerificationCode: ""
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status", 1412011]
        - eq: [ "content.message", "图形验证码不能为空"]
        - eq: [ "content.success", False]

- test:
    name: 发送动态验证码-图形验证码不正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "1234"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412003]
        - eq: [ "content.message", 图形验证码错误]
        - eq: [ "content.success", False]

- test:
    name: 发送动态验证码-图形验证码格式不正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "34j4"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412012]
        - eq: [ "content.message", 图形验证码格式不正确]
        - eq: [ "content.success", False]

- test:
    name: 发送动态验证码-图形验证码已过期
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: "hdwduwyey883"
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412010]
        - eq: [ "content.message", 图形验证码已过期]
        - eq: [ "content.success", False]

- test:
    name: 发送动态验证码-手机号不存在
    variables:
        - account: $mobileCoded02
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412020]
        - eq: [ "content.message", 手机号/邮箱或账号状态不正确]
        - eq: [ "content.success", False]

- test:
    name: 发送动态验证码-手机号不存在-图形验证码错误
    variables:
        - account: $mobileCoded02
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: 1234
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412003 ]
        - eq: [ "content.message", 图形验证码错误 ]
        - eq: [ "content.success", False ]

- test:
    name: 发送动态验证码-图形验证码正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 2
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", true]

- test:
    name: 获取内部账号信息-验证码格式错误
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobile
        - accountType: $accountTypes
        - dynamicCode: 23234
    validate:
        - eq: [ "content.status", 1412022]
        - eq: [ "content.message", "动态验证码格式不正确" ]
        - eq: [ "content.success", False ]

- test:
    name: 获取内部账号信息-验证码错误
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobile
        - accountType: $accountTypes
        - dynamicCode: 232342
    validate:
        - eq: [ "content.status", 1412013]
        - contains: [ "content.message", 动态验证码错误 ]
        - eq: [ "content.success", False ]

- test:
    name: 获取内部账号信息
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobile
        - accountType: $accountTypes
        - dynamicCode: 123456
    extract:
        - code: content.data.0.userCode
        - password1: content.data.0.password
    validate:
        - eq: [ "content.status",200 ]
        - eq: [ "content.message", 成功 ]
        - eq: [ "content.success", true ]
        - ne: [ "content.data", null]

- test:
    name: 获取内部账号信息-类型空的
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobile
        - accountType: ""
        - dynamicCode: 123456
    validate:
        - eq: [ "content.status", 1412022 ]
        - eq: [ "content.message", "填写的用户类型不支持" ]
        - eq: [ "content.success", False ]

- test:
    name: 获取内部账号信息-账号空的
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: ""
        - accountType: $accountTypes
        - dynamicCode: 123456
    validate:
        - eq: [content.status, 1412022]
        - eq: [content.message, 账号不能为空]
        - eq: [content.success, False ]


- test:
    name: 获取内部账号信息-账号已删除
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: "***********"
        - accountType: $accountTypes
        - dynamicCode: 123456
    validate:
        - eq: [content.status, 1412020]
        - eq: [content.message, "手机号/邮箱或账号状态不正确"]
        - eq: [content.success, False ]