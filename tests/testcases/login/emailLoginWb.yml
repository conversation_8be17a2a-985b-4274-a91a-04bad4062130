- config:
    name: 外部用户邮箱登录
    variables:
        - accounts: ${ENV(zidhdl.amailCoded)}
        - passwords: ${ENV(login.password)}
#        - userCodes: ${ENV(userCodeWb)}
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 2
        - loginUrl: ${ENV(esign.projectHost)}
        - userTerritorys: 2
- test:
    name: 发送动态验证码-正确
    variables:
        - account: $accounts
        - accountType: 3
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]

- test:
    name: 发送动态验证码-类型空的
    variables:
        - account: $accounts
        - accountType: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]

- test:
    name: 发送动态验证码-验证码空的
    variables:
        - account: $accounts
        - accountType: 3
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ""
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412011]
        - eq: [ "content.message", 图形验证码不能为空]
- test:
    name: 发送动态验证码-账号空的
    variables:
        - account: ""
        - accountType: 3
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - scene: 7
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",1412022]
        - eq: [ "content.message", 账号不能为空]

#- test:
#    name: 邮箱登录
#    variables:
#        - account: $accounts
#        - accountType: 3
#        - dynamicCode: "123456"
#        - platform: "PC"
#        - userCode: ""
#        - verificationCode: ${get_1()}
#        - headerVerificationCode: ${get_2()}
#    api: api/esignLogin/sso/login.yml
#    validate:
#        - eq: [ "content.status",200]
#        - eq: [ "content.message", 成功]

- test:
    name: 邮箱登录-错误
    variables:
        - account: $accounts
        - accountType: "3"
        - dynamicCode: "367848"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: ${get_value($verificationDict,verification_code)}
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412013]

- test:
    name: 邮箱登录-验证码错误
    variables:
        - account: $accounts
        - accountType: "3"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: 1234
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412003]

- test:
    name: 邮箱登录-格式不正确
    variables:
        - account: "g1NIAsCA3bJaJRYcCwuLPg=="
        - accountType: "3"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: "9999"
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: $userTerritorys
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",1412009]
        - eq: [ "content.message",邮箱格式不正确]
