- config:
    name: 账号冻结(这个允许如果有问题的话，可能是后台管理系统-系统参数-登录配置-登录失败冻结次数设置的不是5导致的)
    variables:
        - accounts: ${ENV(accountDj.accountNo)}
        - mobile: ${ENV(zidhdl.mobile)}
        - passwords: ${ENV(login.password)}
        - userCodes: ${ENV(accountDj.userCode)}
        - passwordResets: ${ENV(login.passwordReset)}
        - accountTypes: 4
        - mobileCoded: ${ENV(accountDj.mobileCoded)}
        - loginUrl: ${ENV(esign.projectHost)}
        - mobileDj: ${ENV(accountDj.mobile)}


- test:
    name: 发送动态验证码-正确
    variables:
        - account: $mobileCoded
        - accountType: 4
        - verificationCode: 9999
        - headerVerificationCode: ${get_2()}
        - scene: 1
        - userTerritory: null
    api: api/esignLogin/sso/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", True]

- test:
    name: 手机号登录
    variables:
        - account: $mobileCoded
        - accountType: "4"
        - dynamicCode: "123456"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: 9999
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", True]

- test:
    name: 登录-输入有误
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: "6yGE1KjC5Te8CDbhs+RkHQ=="
        - type: 1
        - platform: pc
    validate:
        - eq: ["content.status", 1412014]
        - eq: ["content.message", "密码输入有误"]
        - eq: [ "content.success", False]

- test:
    name: 登录-错误一次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412015]
        - eq: [ "content.success", False]
        - contains: [ "content.message", "密码错误，4次之后，账号将被冻结"]
- test:
    name: 登录-错误2次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412015]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "密码错误，3次之后，账号将被冻结" ]

- test:
    name: 登录-错误3次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412015]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "密码错误，2次之后，账号将被冻结" ]

- test:
    name: 登录-错误4次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: ["content.status", 1412015]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "密码错误，1次之后，账号将被冻结" ]

- test:
    name: 登录-错误5次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: ["content.status", 1412007]
        - eq: [ "content.success", False ]
        - contains: ["content.message", "账号已冻结"]
- test:
    name: 登录-错误6次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412007 ]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "账号已冻结" ]
- test:
    name: 登录-错误7次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412007 ]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "账号已冻结" ]
- test:
    name: 登录-错误8次
    api: api/esignLogin/sso/accountLogin.yml
    variables:
        - account: $accounts
        - password: ${ENV(login.password)}
        - type: 1
        - platform: pc
    validate:
        - eq: [ "content.status", 1412007 ]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "账号已冻结" ]
- test:
    name: 手机号登录-错误3次
    variables:
        - account: $mobileCoded
        - accountType: "4"
        - dynamicCode: "233233"
        - platform: "PC"
        - userCode: ""
        - verificationDict: ${get_verification_dict()}
        - verificationCode: 9999
        - headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
        - userTerritory: null
    api: api/esignLogin/sso/login.yml
    validate:
        - eq: [ "content.status", 1412007 ]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "账号已冻结" ]
#    teardown_hooks:
#         - ${sleep(60)}

- test:
    name: 获取账号信息
    api: api/esignLogin/sso/listAccountInfo.yml
    variables:
        - account: $mobileDj
        - accountType: $accountTypes
        - dynamicCode: "123456"
#    extract:
#        - code: content.data.0.userCode
#        - password1: content.data.0.password
    validate:
        - eq: [ "content.status", 1412007 ]
        - eq: [ "content.success", False ]
        - contains: [ "content.message", "账号已冻结" ]
#- test:
#    name: 重置密码
#    api: api/esignLogin/sso/retrievePassword.yml
#    variables:
#        - accountType: $accountTypes
#        - passwordOld: $password1
#        - password: $passwordResets
#        - confirmPassword: $passwordResets
#        - userCode: $userCodes
#    extract:
#        - moudle: content.data
#    validate:
#        - eq: ["content.status", 200]
#- test:
#    name: 再次获取外部账号信息
#    api: api/esignLogin/sso/listAccountInfo.yml
#    variables:
#        - account: $mobileDj
#        - accountType: $accountTypes
#        - dynamicCode: "123456"
#    extract:
#        - code: content.data.0.userCode
#        - password2: content.data.0.password
#    validate:
#        - eq: ["content.status", 200]

#- test:
#    name: 查询冻结状态-冻结的
#    api: api/esignLogin/sso/findAccountFreezeStatus.yml
#    variables:
#        - userCode: "Q9TMvHkqhkfGxG3A/bdUvF3/sLiUBBNxaEDdhRRby7xFHrlVPHM8lw1V9HAV1QCMAX48TPQ6m3dsr8wH9h/md+v/NB0wQhyb0pkHMliU1qTGfjbGL5qi6ybzSSHLg8gBzUXWJHkdT31GwxgT0imARcuq+i5Gtb70mTPHcqIg+4vIviS8GPnPOIf01RuJFnwK08RNk3t3jS+sOAxG3HHM2X3nRhCQGgIyadHkszXo+APTzYg4iXikSgvFj16at88cspMxV96hZ4Eap0eUrX66lJoVsm2DsDcRaiAapShHW6i6Ba/H+tPqMOKoNbgQkiIOyM0ZD0ySoW9sMnsgJ7JMLA=="
#    validate:
#        - eq: ["content.status",200]