- config:
    name: 获取退出登录和登录页的url
    variables:
        - accountneibu: ${ENV(sign01.accountNo)}
        - usercodeneibu: ${ENV(sign01.userCode)}
#        - accountneibu: "yitao"
        - accountwaibu: ${ENV(wsignwb01.accountNo)}
        - key: ${ENV(esign.projectSecret)}
        - accountNo: etlloginuser${generate_random_str(6)}
        - email: $<EMAIL>
        - organizationCode1: ${ENV(sign01.main.orgCode)}

##创建一个内部用户和外部用户，账号相同
- test:
    name: 创建外部用户
    variables:
        data: [
          {
            "customAccountNo": $accountNo,
            "name": "测试重复",
            "mobile": ,
            "email": $email,
            "licenseType": ,
            "licenseNo": ,
            "bankCardNo": '',
            "mainOrganizationCode": ,
            "mainCustomOrgNo": ,
            "otherOrganization": [
            ]
          }
        ]
    extract:
      userCode11: content.data.successData.0.userCode
    api: api/esignManage/outerUsers/create.yml
    validate:
      - eq: [ json.code,200 ]
      - eq: [ json.message,"成功" ]

##创建内部用户
- test:
    name: setup-创建内部用户
    variables:
        data: [
                {
                    "customAccountNo": $accountNo,
                    "name": "诸葛一",
                    "mobile": "",
                    "email": "$email",
                    "licenseType": ,
                    "licenseNo": ,
                    "bankCardNo": ,
                    "mainOrganizationCode": $organizationCode1,
                    "mainCustomOrgNo": ,
                    "otherOrganization": [
                        {
                            "otherCustomOrgNo": "",
                            "otherOrganizationCode": ""
                        }
                    ]
                }

            ]
    api: api/esignManage/InnerUsers/create.yml
    setup_hooks:
      - ${sleep(0.1)}
    validate:
      - eq: [ json.code,200 ]
      - eq: [ json.message,"成功" ]


- test:
    name: 正常获取到accessToken
    variables:
    api: api/esignLogin/getAccessToken.yml
    extract:
        accessToken1: content.data.accessToken
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]

##获取ssotoken
- test:
    name: 获取内部的ssotoken
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountneibu)}
    api: api/esignLogin/getSsoToken.yml
    extract:
        ssoToken1: content.data.ssoToken
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

#查询内部用户信息


- test:
    name: 获取外部用户的ssotoken--userType传外部
    variables:
        - accessToken: $accessToken1
        - loginAccount: ${get_aes_encrypt1($accountNo)}
        - userType: 2
    extract:
        ssoToken2: content.data.ssoToken
    api: api/esignLogin/getSsoToken.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.message", 成功]
        - eq: [ "content.success", true]
        - contains: [ "content.data.ssoSuffix", "tCode="]
        - contains: [ "content.data.ssoRedirectUrl", "tCode="]
        - ne: [ "content.data.ssoToken", ""]
        - ne: [ "content.data.ssoCode", ""]

- test:
    name: "查询外部用户"
    api:  api/esignManage/outerUsers/detail.yml
    variables:
        customAccountNo: $accountNo
    extract:
        customAccountNo2: content.data.0.customAccountNo
        userCode2: content.data.0.userCode
    validate:
        - eq: [ content.code, 200 ]
        - eq: [ content.message, "成功" ]

##token错误
- test:
    name: token为空，跳到登录页
    variables:
        - token: ""
    api: api/esignLogin/getLogoutUrl.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.data", "http"]
        - eq: [ "content.success", true]
        - eq: [ "content.message", "成功"]

- test:
    name: token错误,跳到登录页
    variables:
        - token: "4321fsdfsdfs"
    api: api/esignLogin/getLogoutUrl.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.data", "http"]
        - eq: [ "content.success", true]
        - eq: [ "content.message", "成功"]
##token正确
- test:
    name: token为正确-内部用户
    variables:
        - token: "$ssoToken1"
    api: api/esignLogin/getLogoutUrl.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.data", "http"]
        - eq: [ "content.success", true]
        - eq: [ "content.message", "成功"]
- test:
    name: token为正确-外部用户
    variables:
        - token: "$ssoToken2"
    api: api/esignLogin/getLogoutUrl.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.data", "http"]
        - eq: [ "content.success", true]
        - eq: [ "content.message", "成功"]
- test:
    name: 删除内部用户
    variables:
        data:
            userCode:
            customAccountNo: $accountNo
    api: api/esignManage/InnerUsers/delete.yml
    validate:
        - eq: [ json.code,200 ]
        - eq: [ json.message,"成功" ]

- test:
    name: 删除外部用户
    variables:
        customAccountNo: $accountNo
        userCode:
    api: api/esignManage/outerUsers/delete.yml
    validate:
        - eq: [ json.code,200 ]
        - eq: [ json.message,"成功" ]

#获取登录页面地址
- test:
    name: 获取登录页面地址
    api: api/esignLogin/loginUrl.yml
    validate:
        - eq: [ "content.status",200]
        - contains: [ "content.data", "http"]
        - eq: [ "content.success", true]
        - eq: [ "content.message", "成功"]