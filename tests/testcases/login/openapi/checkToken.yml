- config:
    name: "校验token有效单接口"
    variables:
      - host: ${ENV(esign.projectHost)}

- test:
    name: $casename
    variables:
      -token: $token_1
      -usercode: $usercode_1
      -customAccountNo: $customAccountNo_1
    api: api/esignLogin/sso/openapi/checkToken.yml
    extract:
      -status: content.status
      -success: content.success
      -message: content.message
    validate:
      - eq: ["content.status", $status]
      - eq: ["content.message", $message]
      - eq: ["content.success", $success]
