- config:
    name: "校刷新用户token单接口"
#    variables:
#      - host: ${ENV(esign.projectHost)}

- test:
    name: $casename
    variables:
      -refreshToken: $refreshToken_1
      -userCode: $usercode_1
      -customAccountNo: $customAccountNo_1
    api: api/esignLogin/sso/openapi/refreshToken.yml
    extract:
      -status: content.status
      -message: content.message
      -success: content.success
    validate:
      - eq: ["content.status", $status]
      - eq: [ "content.message", $message ]
      - eq: [ "content.success", $success ]

