- config:
    name: "我的待办-查询抄送我的"
    esign.projectHost: ${ENV(esign.projectHost)}

- test:
    name: 查询抄送我的-currPage 为 1
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 1
        workflowStatus: ${get_portal_data(queryCarbonCopy, workflowStatus)}
        pageSize: ${get_portal_data(queryCarbonCopy, pageSize)}
        timeType: ${get_portal_data(queryCarbonCopy, timeType)}
        startUserName: ${get_portal_data(queryCarbonCopy, startUserName)}
        workflowConfigName: ${get_portal_data(queryCarbonCopy, workflowConfigName)}
        workflowConfigCode: ${get_portal_data(queryCarbonCopy, workflowConfigCode)}
        startTime: ${get_portal_data(queryCarbonCopy, startTime)}
        endTime: ${get_portal_data(queryCarbonCopy, endTime)}
        workflowCategory: ${get_portal_data(queryCarbonCopy, workflowCategory)}
        status: ${get_portal_data(queryCarbonCopy, status)}
    extract:
        ex_status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $ex_status, 200]
        - eq: [ $success, true]
        - ne: [ json.data, ""]

- test:
    name: 查询抄送我的-currPage 为 128
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 128
        workflowStatus: ${get_portal_data(queryCarbonCopy, workflowStatus)}
        pageSize: ${get_portal_data(queryCarbonCopy, pageSize)}
        timeType: ${get_portal_data(queryCarbonCopy, timeType)}
        startUserName: ${get_portal_data(queryCarbonCopy, startUserName)}
        workflowConfigName: ${get_portal_data(queryCarbonCopy, workflowConfigName)}
        workflowConfigCode: ${get_portal_data(queryCarbonCopy, workflowConfigCode)}
        startTime: ${get_portal_data(queryCarbonCopy, startTime)}
        endTime: ${get_portal_data(queryCarbonCopy, endTime)}
        workflowCategory: ${get_portal_data(queryCarbonCopy, workflowCategory)}
        status: ${get_portal_data(queryCarbonCopy, status)}
    extract:
        ex_status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $ex_status, 200]
        - eq: [ $success, true]
        - ne: [ json.data, ""]

- test:
    name: 查询抄送我的-currPage 为 null
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 
        workflowStatus: ${get_portal_data(queryCarbonCopy, workflowStatus)}
        pageSize: ${get_portal_data(queryCarbonCopy, pageSize)}
        timeType: ${get_portal_data(queryCarbonCopy, timeType)}
        startUserName: ${get_portal_data(queryCarbonCopy, startUserName)}
        workflowConfigName: ${get_portal_data(queryCarbonCopy, workflowConfigName)}
        workflowConfigCode: ${get_portal_data(queryCarbonCopy, workflowConfigCode)}
        startTime: ${get_portal_data(queryCarbonCopy, startTime)}
        endTime: ${get_portal_data(queryCarbonCopy, endTime)}
        workflowCategory: ${get_portal_data(queryCarbonCopy, workflowCategory)}
        status: ${get_portal_data(queryCarbonCopy, status)}
    extract:
        ex_status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ "status_code", 200]
        - eq: [ $ex_status, 1501001]
        - eq: [ $success, false]
        - eq: [ json.data, ""]

- test:
    name: 查询抄送我的-currPage 为 0
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 0
        workflowStatus: ${get_portal_data(queryCarbonCopy, workflowStatus)}
        pageSize: ${get_portal_data(queryCarbonCopy, pageSize)}
        timeType: ${get_portal_data(queryCarbonCopy, timeType)}
        startUserName: ${get_portal_data(queryCarbonCopy, startUserName)}
        workflowConfigName: ${get_portal_data(queryCarbonCopy, workflowConfigName)}
        workflowConfigCode: ${get_portal_data(queryCarbonCopy, workflowConfigCode)}
        startTime: ${get_portal_data(queryCarbonCopy, startTime)}
        endTime: ${get_portal_data(queryCarbonCopy, endTime)}
        workflowCategory: ${get_portal_data(queryCarbonCopy, workflowCategory)}
        status: ${get_portal_data(queryCarbonCopy, status)}
    extract:
        ex_status: json.status
        success : json.success
        message: json.message
        data: json.data
    validate:
        - eq: [$ex_status, 1501001]
        - eq: [$success, false]
        - eq: [$data, null ]
#        - eq: [$message, "当前页号不能小于1"]
        - len_gt: [$message, 0]

- test:
    name: 查询抄送我的-currPage 为 100000000
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 100000000
        workflowStatus: ${get_portal_data(queryCarbonCopy, workflowStatus)}
        pageSize: ${get_portal_data(queryCarbonCopy, pageSize)}
        timeType: ${get_portal_data(queryCarbonCopy, timeType)}
        startUserName: ${get_portal_data(queryCarbonCopy, startUserName)}
        workflowConfigName: ${get_portal_data(queryCarbonCopy, workflowConfigName)}
        workflowConfigCode: ${get_portal_data(queryCarbonCopy, workflowConfigCode)}
        startTime: ${get_portal_data(queryCarbonCopy, startTime)}
        endTime: ${get_portal_data(queryCarbonCopy, endTime)}
        workflowCategory: ${get_portal_data(queryCarbonCopy, workflowCategory)}
        status: ${get_portal_data(queryCarbonCopy, status)}
    extract:
        ex_status: json.status
        success : json.success
        message: json.message
        data: json.data
    validate:
        - eq: [$ex_status, 1501001]
        - eq: [$success, false]
        - eq: [$data, null ]
#        - eq: [$message, "当前页号不能大于10000000"]
        - len_gt: [$message, 0]        

# 正常的 case
- test:
    name: 查询抄送我的- 正常的case
    api: api/esignPortal/task/queryCarbonCopy.yml
    variables:
        currPage: 1
        workflowStatus: ""
        pageSize: 10
        timeType: 3
        startUserName: ""
        workflowConfigName: ""
        workflowConfigCode: ""
        startTime: ${getFormatTime(7, before)} 
        endTime: ${getFormatTime(0, add)} 
        workflowCategory: ""
        status: ""
    extract:
        status: json.status
        success : json.success
        message: json.message
        data: json.data
    validate:
        - eq: [$status, 200]
        - eq: [$success, true]
        - eq: [$message, "成功"]
        - len_gt: [$data, 0]
