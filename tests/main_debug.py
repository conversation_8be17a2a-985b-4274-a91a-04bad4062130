#!/usr/bin/env python
# -- coding: utf-8 --
# @Time : DATEDATE{TIME}
# <AUTHOR> gan
# @File : main_debug.py
from httprunner.api import HttpRunner

runner = HttpRunner(
    failfast=False,
    save_tests=True,
    log_level="Debug",
    log_file="test.log"

)

path_or_tests = r"testcases/debugTC.yml"
# path_or_tests = r"testcases/signs/signFlow/signFlowList/signFlowListDynamicCode.yml"
# path_or_tests = r"testsuites\debugTS.yml"
summary = runner.run(path_or_tests)
