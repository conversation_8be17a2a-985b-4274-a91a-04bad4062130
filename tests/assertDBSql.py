# -*-coding:utf-8-*-
# <AUTHOR> wenmin
# @Time : 2022/5/26 11:35
# @Description : 签署流程需要做校验使用到的sql

class DBAssertSql(object):
    """
    常量类
    """

    class ConstError(TypeError):
        pass

    class ConstCaseError(ConstError):
        pass

    def __setattr__(self, name: str, value: any):
        """
        设置常量内置方法
        :param name: 常量名称
        :type name: str
        :param value: 常量值
        :type value: any
        """
        if name in self.__dict__:
            raise self.ConstError("已被赋值得常量不允许修改.%s" % name)
        if not name.isupper():
            raise self.ConstCaseError('常量名"%s"必须大写' % name)

        self.__dict__[name] = value


DBAssertSql = DBAssertSql()
# sql语句定义

'''
任务中心的sql查询语句
'''
# 根据流程uuid获取流程的待办信息
DBAssertSql.GET_TODO_TASK_BY_UUID = "select * from wf_todo_task where workflow_config_code = '{signFlowId}'"

# 根据流程uuid获取抄送任务
DBAssertSql.GET_CARBON_COPY_BY_UUID = "select * from wf_carbon_copy where workflow_config_code = '{signFlowId}'"

# 根据流程uuid获取我发起的任务
DBAssertSql.GET_MY_START_BY_UUID = "select * from wf_my_start where workflow_config_code = '{signFlowId}'"

# 根据流程uuid获取流程
DBAssertSql.GET_PROCESS_BY_UUID = "select * from process where uuid = '{signFlowId}'"

# 根据流程状态获取流程ID
DBAssertSql.GET_PROCESS_BY_STATUS = "select uuid from process where sign_status = '{signStatus}' " \
                                    "and init_status =1 and subject like '自动化%' LIMIT 1"

# 根据流程uuid获取流程扩展信息
DBAssertSql.GET_PROCESS_EXT_BY_UUID = "select * from process_ext where id = {signFlowId}"

# 根据流程id和角色类型获取流程发起人
DBAssertSql.GET_PROCESS_INITIATOR = \
    "select * from process_actor actor left join process_signer signer on actor.id = signer.id " \
    "where actor.process_id = {processId} and actor.role_type = {roleType} and " \
    "actor.deleted = 0 order by signer.sign_model, signer.sign_order "

# 根据流程id获取流程快照配置
DBAssertSql.GET_PROCESS_CONFIG_BY_PROCESS_ID = "select * from process_config where process_id = {processId}"

# 根据业务类型id获取业务类型信息
DBAssertSql.GET_BUSINESS_BY_ID = "select * from business_type where id = {businessId}"


if __name__ == '__main__':
    signFlowId = "38120f7fdf0f7c7699c1d36f26c8a43d"
    processParam = {"signFlowId": signFlowId}
    print(DBAssertSql.GET_TODO_TASK_BY_UUID.format(**processParam))
