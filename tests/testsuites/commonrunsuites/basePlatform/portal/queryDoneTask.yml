config:
    name: "我的待办-查询抄送我的"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: currPage
      testcase: testcases/portal/task/queryDoneTask/currPage.yml
    - name: pageSize
      testcase: testcases/portal/task/queryDoneTask/pageSize.yml
    - name: timeType
      testcase: testcases/portal/task/queryDoneTask/timeType.yml
    - name: startUserName
      testcase: testcases/portal/task/queryDoneTask/startUserName.yml
    - name: workflowConfigName
      testcase: testcases/portal/task/queryDoneTask/workflowConfigName.yml
    - name: workflowConfigCode
      testcase: testcases/portal/task/queryDoneTask/workflowConfigCode.yml
    - name: startTime
      testcase: testcases/portal/task/queryDoneTask/startTime.yml
    - name: endTime
      testcase: testcases/portal/task/queryDoneTask/endTime.yml
    - name: workflowCategory
      testcase: testcases/portal/task/queryDoneTask/workflowCategory.yml
    - name: other
      testcase: testcases/portal/task/queryDoneTask/other.yml


