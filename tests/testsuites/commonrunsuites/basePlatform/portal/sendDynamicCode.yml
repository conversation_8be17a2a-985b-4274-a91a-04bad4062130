config:
    name: "用户信息-发送手机/邮箱验证码"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: 发送手机/邮箱验证码-address
      testcase: testcases/portal/user/sendDynamicCode/address.yml
    - name: 发送手机/邮箱验证码-addressType
      testcase: testcases/portal/user/sendDynamicCode/addressType.yml
    - name: 发送手机/邮箱验证码-scenes
      testcase: testcases/portal/user/sendDynamicCode/addressType.yml
    - name: 发送手机/邮箱验证码-email
      testcase: testcases/portal/user/sendDynamicCode/email.yml
    - name: 发送手机/邮箱验证码-phone
      testcase: testcases/portal/user/sendDynamicCode/phone.yml

