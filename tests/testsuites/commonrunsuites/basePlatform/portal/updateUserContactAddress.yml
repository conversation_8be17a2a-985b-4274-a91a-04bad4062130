config:
    name: "用户信息-新增/更新用户手机或者邮箱地址"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: 新增/更新用户手机或者邮箱地址-address
      testcase: testcases/portal/user/updateUserContactAddress/address.yml
    - name: 新增/更新用户手机或者邮箱地址-addressType
      testcase: testcases/portal/user/updateUserContactAddress/addressType.yml
    - name: 新增/更新用户手机或者邮箱地址-verificationCode
      testcase: testcases/portal/user/updateUserContactAddress/verificationCode.yml

