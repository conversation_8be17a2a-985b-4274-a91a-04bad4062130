config:
    name: "我的待办-查询抄送我的"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: currPage
      testcase: testcases/portal/task/queryCarbonCopy/currPage.yml
    - name: workflowStatus
      testcase: testcases/portal/task/queryCarbonCopy/workflowStatus.yml
    - name: pageSize
      testcase: testcases/portal/task/queryCarbonCopy/pageSize.yml
    - name: timeType
      testcase: testcases/portal/task/queryCarbonCopy/timeType.yml
    - name: startUserName
      testcase: testcases/portal/task/queryCarbonCopy/startUserName.yml
    - name: workflowConfigName
      testcase: testcases/portal/task/queryCarbonCopy/workflowConfigName.yml
    - name: workflowConfigCode
      testcase: testcases/portal/task/queryCarbonCopy/workflowConfigCode.yml
    - name: startTime
      testcase: testcases/portal/task/queryCarbonCopy/startTime.yml
    - name: endTime
      testcase: testcases/portal/task/queryCarbonCopy/endTime.yml
    - name: workflowCategory
      testcase: testcases/portal/task/queryCarbonCopy/workflowCategory.yml
    - name: status
      testcase: testcases/portal/task/queryCarbonCopy/status.yml
    - name: other
      testcase: testcases/portal/task/queryCarbonCopy/other.yml

