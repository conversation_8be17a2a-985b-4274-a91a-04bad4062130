config:
    name: "用户信息-根据手机或者邮箱更新签署密码"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: 根据手机或者邮箱更新签署密码-address
      testcase: testcases/portal/user/updateSignPwdByContactAddress/address.yml
    - name: 根据手机或者邮箱更新签署密码-addressType
      testcase: testcases/portal/user/updateSignPwdByContactAddress/addressType.yml
    - name: 用户信息-根据手机或者邮箱更新签署密码-newPwd
      testcase: testcases/portal/user/updateSignPwdByContactAddress/newPwd.yml
    - name: 根据手机或者邮箱更新签署密码-verificationCode
      testcase: testcases/portal/user/updateSignPwdByContactAddress/verificationCode.yml
    - name: 根据手机或者邮箱更新签署密码-other
      testcase: testcases/portal/user/updateSignPwdByContactAddress/other.yml


