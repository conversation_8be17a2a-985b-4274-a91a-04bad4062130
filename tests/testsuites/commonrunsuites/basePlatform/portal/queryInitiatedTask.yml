config:
    name: "我的待办-查询我发起的"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: currPage
      testcase: testcases/portal/task/queryInitiatedTask/currPage.yml
    - name: workflowStatus
      testcase: testcases/portal/task/queryInitiatedTask/workflowStatus.yml
    - name: pageSize
      testcase: testcases/portal/task/queryInitiatedTask/pageSize.yml
    - name: timeType
      testcase: testcases/portal/task/queryInitiatedTask/timeType.yml
    - name: workflowConfigName
      testcase: testcases/portal/task/queryInitiatedTask/workflowConfigName.yml
    - name: workflowConfigCode
      testcase: testcases/portal/task/queryInitiatedTask/workflowConfigCode.yml
    - name: startTime
      testcase: testcases/portal/task/queryInitiatedTask/startTime.yml
    - name: endTime
      testcase: testcases/portal/task/queryInitiatedTask/endTime.yml
    - name: workflowCategory
      testcase: testcases/portal/task/queryInitiatedTask/workflowCategory.yml
    - name: other
      testcase: testcases/portal/task/queryInitiatedTask/other.yml
