config:
    name: "用户信息-根据手机/邮箱更新登录密码"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: 根据手机/邮箱更新登录密码-address
      testcase: testcases/portal/user/updateLoginPwdByContactAddress/address.yml
    - name: 根据手机/邮箱更新登录密码-addressType
      testcase: testcases/portal/user/updateLoginPwdByContactAddress/addressType.yml
    - name: 根据手机/邮箱更新登录密码-newPwd
      testcase: testcases/portal/user/updateLoginPwdByContactAddress/newPwd.yml
    - name: 根据手机/邮箱更新登录密码-verificationCode
      testcase: testcases/portal/user/updateLoginPwdByContactAddress/verificationCode.yml


