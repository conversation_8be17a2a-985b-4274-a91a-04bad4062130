config:
    name: "我的待办-查询我的待办"
    base_url: ${ENV(esign.projectHost)}
testcases:
    - name: currPage
      testcase: testcases/portal/task/queryUndoTask/currPage.yml
    - name: pageSize
      testcase: testcases/portal/task/queryUndoTask/pageSize.yml
    - name: timeType
      testcase: testcases/portal/task/queryUndoTask/timeType.yml
    - name: startUserName
      testcase: testcases/portal/task/queryUndoTask/startUserName.yml
    - name: workflowConfigName
      testcase: testcases/portal/task/queryUndoTask/workflowConfigName.yml
    - name: workflowConfigCode
      testcase: testcases/portal/task/queryUndoTask/workflowConfigCode.yml
    - name: startTime
      testcase: testcases/portal/task/queryUndoTask/startTime.yml
    - name: endTime
      testcase: testcases/portal/task/queryUndoTask/endTime.yml
    - name: workflowCategory
      testcase: testcases/portal/task/queryUndoTask/workflowCategory.yml
    - name: other
      testcase: testcases/portal/task/queryUndoTask/other.yml


