config:
    name: "用户实名"
    base_url: ${ENV(esign.projectHost)}
testcases:
  - name: 内部用户门户、签署页、证书查实名状态、两边信息不一致比对用户
    testcase: testcases/realName/user/realNameForUserTC.yml
  - name: 内部机构门户、签署页、证书查实名状态
    testcase: testcases/realName/org/realNameForOrgTC.yml
  - name: 内部机构两边信息不一致比对，去实名
    testcase: testcases/realName/org/realNameForOrgTCTest.yml
#  - name: 外部机构两边信息不一致比对机构
#    testcase: testcases/realName/org/realNameForOutOrgTC111.yml
  - name: 外部用户、签署页、证书查实名状态、两边信息不一致比对
    testcase: testcases/realName/user/realNameForOuterUserTC.yml