config:
    name: "账号登录单接口用例"
    variables:
        account01: ${ENV(zidhdl.userCode)}

testcases:
-
    name: 账号登录-报错用例
    testcase: testcases/login/accountLogin.yml
    parameters:
        - account_1-password_1-accountType_1-except_code_4-except_message_4-casename:
            - [$account01,"${ENV(login.password)}","",200,"成功","登录方式为空"]
            - [$account01,"","2",1412022,"密码不能为空","密码不能为空"]
            - ["zhouglll","fff","2",1412019,"账号、密码或账号状态不正确","账号未注册"]
            - [$account01,"hdsahdjadh","2",1412014,"密码输入有误","冻结"]
            - [$account01,"${ENV(login.password)}","2",200,"成功","登录成功"]

