config:
    name: "账号校验单接口用例"

testcases:
-
    name: 账号校验
    testcase: testcases/login/accountVerify.yml
    parameters:
        - userCode_1-referer_1-platform_1-token_1-except_code_4-except_message_4-casename:
            - ["","www.baidu.com","pc","hjhjhjd",1412022,"用户编码不能为空","TC1-用户编码不能为空"]
            - ["${ENV(zidhdl.userCode)}","","pc","hjhjhjd",1412022,"引用地址不能为空","TC2-引用地址不能为空"]
            - ["${ENV(zidhdl.userCode)}","www.baidu.com","","hjhjhjd",1412022,"平台不能为空","TC3-平台不能为空"]
            - ["${ENV(zidhdl.userCode)}","www.baidu.com","pc","",1412030,"authorization为空","TC4-authorization为空"]
            - ["${ENV(zidhdl.userCode)}","www.baidu.com","pc","jshfdhfsjfs",1412004,"token校验失败","TC5-登录失效，请重新登录"]
            - ["jjj","www.baidu.com","pc","jshfdhfsjfs",1412004,"token校验失败","TC6-登录失效，请重新登录"]
#            - ["zhougl","","2",1412022,"密码不能为空","密码不能为空"]
#            - ["zhougl","fff","2",1412019,"账号、密码或账号状态不正确","账号未注册"]
#            - ["${ENV(zidhdl.userCode)}","${ENV(password)}","2",200,"请求成功","登录成功"]
#            - ["${ENV(zidhdl.userCode)}","hdsahdjadh","2",1412015,"密码错误，5次之后，账号将被冻结","冻结"]
