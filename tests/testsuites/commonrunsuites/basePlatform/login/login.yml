config:
    name: "手机号/邮箱登录单接口用例"

testcases:
-
    name: 手机号/邮箱登录单接口用例
    testcase: testcases/login/login.yml
    parameters:
        - account_1-accountType_1-dynamicCode_1-platform_1-userCode_1-verificationCode_1-except_code_4-name-userTerritory:
            - ["","3","123456","PC","${ENV(zidhdl.userCode)}","1234",1412022,"账号不能为空",null]
            - ["${ENV(zidhdl.userCode)}","","123456","PC","${ENV(zidhdl.userCode)}","1234",1412022,"账号类型不能为空",null]
            - ["${ENV(zidhdl.userCode)}","3","","PC","${ENV(zidhdl.userCode)}","1234",1412022,"动态验证码不能为空",null]
            - ["${ENV(zidhdl.userCode)}","3","123456","","${ENV(zidhdl.userCode)}","1234",1412022,"平台不能为空",null]
#            - ["${ENV(zidhdl.userCode)}","4","123456","PC","","1234",1412999,"null","usercode不能为空"]
#            - ["jjj","4","123456","PC","","1234",1412022,"null","账号不存在","账号不存在"]
#            - ["${ENV(zidhdl.userCode)}","","pc","hjhjhjd",1412022,"引用地址不能为空","引用地址不能为空"]
#            - ["${ENV(zidhdl.userCode)}","www.baidu.com","","hjhjhjd",1412022,"平台不能为空","平台不能为空"]
#            - ["${ENV(zidhdl.userCode)}","www.baidu.com","pc","",1412030,"authorization为空","authorization为空"]
#            - ["${ENV(zidhdl.userCode)}","www.baidu.com","pc","jshfdhfsjfs",1412004,"登录失效，请重新登录","登录失效，请重新登录"]
#            - ["zhougl","","2",1412022,"密码不能为空","密码不能为空"]
#            - ["zhougl","fff","2",1412019,"账号、密码或账号状态不正确","账号未注册"]
#            - ["${ENV(zidhdl.userCode)}","${ENV(password)}","2",200,"请求成功","登录成功"]
#            - ["${ENV(zidhdl.userCode)}","hdsahdjadh","2",1412015,"密码错误，5次之后，账号将被冻结","冻结"]
