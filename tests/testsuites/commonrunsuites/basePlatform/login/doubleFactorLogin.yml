#双因子登录接口验证只能启动普通登录方式
config:
    name: "双因子账号登录单接口用例"
    variables:
        account01: ${ENV(zidhdl.userCode)}
        password01: ${ENV(login.password)}
        verificationCode01: ${ENV(verificationCode)}
        target01: "${ENV(esign.projectHost)}/main-index-web/home"
        referer01: "${ENV(esign.projectHost)}/main-index-web/task"

testcases:
-
    name: 登录验证-双因子登录-报错用例
    testcase: testcases/login/doubleFactorLoginVerify.yml
    parameters:
        - casename-account_1-password_1-platform_1-target_1-verificationCode_1-except_code_1-except_message_1-except_succes_1:
            - ["账密都为空","","","pc",$target01,$verificationCode01,1412022,"账号格式不正确",False]
            - ["密码都为空",$account01,"","pc",$target01,$verificationCode01,1412022,"密码不能为空",False]
            - ["来源平台为空",$account01,$password01,"",$target01,$verificationCode01,1412022,"平台不能为空",False]
            #- ["目标平台为空",$account01,$password01,"pc","",$verificationCode01,1412022,"平台不能为空",False]
            - ["图形验证码为空",$account01,$password01,"pc",$target01,null,1412022,"图形验证码不能为空",False]
            - ["图形验证码不填",$account01,$password01,"pc",$target01,"",1412022,"图形验证码格式不正确",False]
#            - ["密码错误",$account01,"hdsahdjadh","pc",$target01,$verificationCode01,1412022,"平台不能为空",False]
#            - ["账号未注册","zhouglll","fff","pc",$target01,$verificationCode01,1412022,"平台不能为空",False]
#            - ["登录成功",$account01,$password01,"pc",$target01,$verificationCode01,200,"平台不能为空",False]

-
    name: 正式登录-双因子登录-报错用例
    testcase: testcases/login/doubleFactorLogin.yml
    parameters:
        - casename_2-account_2-accountType_2-dynamicCode_2-platform_2-referer_2-target_2-userCode_2-userTerritory_2-except_code_2-except_message_2-except_succes_2:
            - ["账号为空","","3","123456","pc",$referer01,$target01,"","1",1412022,"账号不能为空",False]
            - ["动态验证码为空",$account01,"3","","pc",$referer01,$target01,"","1",1412022,"动态验证码不能为空",False]
            - ["账号类型为空",$account01,"","123456","pc",$referer01,$target01,"","1",1412022,"账号类型不正确",False]
            - ["账号类型为非3和4",$account01,"1","123456","pc",$referer01,$target01,"","1",1412022,"账号类型不正确",False]
            - ["来源平台为空",$account01,"3","123456","",$referer01,$target01,"","1",1412022,"平台不能为空",False]
            - ["用户类型为外部2",$account01,"3","123456","pc",$referer01,$target01,"","2",1412022,"入参有误",False]