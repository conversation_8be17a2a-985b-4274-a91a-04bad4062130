config:
    name: 企业实名认证通过后更新实名信息并存证


testcases:

-
  name: 企业实名认证通过后更新实名信息并存证
  testcase: testcases/manage/organizations/tc_authFlowId.yml
  variables:
    #外部用户
#    sql2: select * from ec_user where user_territory='2' and deleted=0
#    user_code2: ${get_param_by_sql($sql2, user_code)}
    user_code2: ${ENV(wsignwb02.approve.userCode)}
    #已离职用户
#    sql3: select * from ec_user where user_territory='1' and user_status='2' and deleted=0
#    user_code3: ${get_param_by_sql($sql3, user_code)}
    user_code3: ${ENV(userCode.dimission)}
    #已删除的内部用户
#    sql4: select * from ec_user where user_territory='1' and deleted=1
#    user_code4: ${get_param_by_sql($sql4, user_code)}
    user_code4: ${ENV(userCode.delete)}
    #注销企业
#    orgsql: select * from ec_organization where organization_territory=1 and organization_type=1 and organization_status=2 and deleted =0
#    orgno1: ${get_param_by_sql($orgsql, account_number)}
    orgno1: ${ENV(deletedcustomOrgNo)}

    #外部企业
#    orgsql2: select * from ec_organization where organization_territory=2 and deleted =0
#    orgno2: ${get_param_by_sql($orgsql2, account_number)}
    orgno2: ${ENV(worg01.orgNo)}
    #删除企业
#    orgsql3: select * from ec_organization where deleted =1
#    orgno3: ${get_param_by_sql($orgsql3, account_number)}
    #部门
#    orgsql4: select * from ec_organization where organization_territory=1 and organization_type=2 and organization_status=1 and deleted =0
#    orgno4: ${get_param_by_sql($orgsql4, account_number)}
    orgno4: ${ENV(sign03.main.departNo)}

    applysql: select * from ec_unifiedauth_apply where unifiedauth_type=2 and unifiedauth_result=1
#    appid1: ${get_param_by_sql($applysql, id)}
    appid1: "123456"
#    flid: ${get_param_by_sql($applysql, auth_flow_id)}
    flid: "********"
#    user: ${get_param_by_sql($applysql, user_code)}
    user: ${ENV(csqs.userCode)}
#    orgcode: ${get_param_by_sql($applysql, organization_code)}
    orgcode: ${ENV(csqs.orgCode)}
    #其他用户
#    otherusersql: select * from ec_user where user_territory='1' and deleted=0 and user_status='1'
#    otheruser: ${get_param_by_sql($otherusersql, user_code,3)}
    otheruser: ${ENV(userCodeNoCert)}

  parameters:
    - name-userCode-customAccountNo-applyId-flowId-customOrgNo-organizationCode-vcode-realNameStatus-code-message-data:
        - [ "用户账号编码都为空","","","$appid1","$flid","","$orgcode","1000","0",1111278,"用户账号/用户编码不能全部为空","" ]
        - [ "用户账号不存在","","123htjh","$appid1","$flid","","$orgcode","1000","0",1111003,"用户已不存在",""  ]
        - [ "用户编码不存在","grshgrhtr","","$appid1","$flid","","$orgcode","1000","0",1111003,"用户已不存在",""  ]
        - [ "用户已离职","$user_code3","","$appid1","$flid","","$orgcode","1000","0",1111003,"用户已不存在",""  ]
        - [ "用户已删除","$user_code4","","$appid1","$flid","","$orgcode","1000","0",1111003,"用户已不存在",""  ]
        - [ "授权码传空","$user","","$appid1","$flid","","$orgcode","","0",913,"授权code不能为空",""  ]
        - [ "实名状态非0、1","$user","","$appid1","$flid","","$orgcode","1000","3",1120017,"realNameStatus错误",""  ]
        - [ "实名状态传空","$user","","$appid1","$flid","","$orgcode","1000","",913,"实名状态不能为空",""  ]
        - [ "意愿申请id不传","$user","","","$flid","","$orgcode","1000","1",913,"申请认证id不能为空",""  ]
        - [ "意愿申请id不存在","$user","","123","$flid","","$orgcode","1000","1",1111370,"该意愿申请不存在",""  ]
#        - [ "flowId不传","$user","","$appid1","","","$orgcode","1000","1",913,"参数flowId必填!",""  ]
#        - [ "flowId不存在","$user","","$appid1","123","","$orgcode","1000","1",1450007,"授权流程id不存在",""  ]
        - [ "组织账号编码不存在","","$user","$appid1","$flid","","","1000","1",1111280,"组织账号/组织编码不能全部为空",""  ]
        - [ "组织账号不存在","","$user","$appid1","$flid","123tete","","1000","1",1111102,"组织已不存在",""  ]
        - [ "组织编码不存在","","$user","$appid1","$flid","","123tete","1000","1",1111102,"组织已不存在",""  ]
#        - [ "组织已删除","","$user","$appid1","$flid","$orgno3","","1000","1",1111102,"组织已不存在",""  ]
        - [ "组织已注销","","$user","$appid1","$flid","$orgno1","","1000","1",1111102,"组织已不存在",""  ]
        - [ "组织外部企业","","$user","$appid1","$flid","$orgno2","","1000","1",1111102,"组织已不存在",""  ]
        - [ "组织是部门","","$user","$appid1","$flid","$orgno4","","1000","1",1111378,"部门不支持获取实名状态",""  ]
        - [ "组织用户不匹配","","$otheruser","$appid1","$flid","","$orgcode","1000","1",1120018,"当前用户信息和组织信息不匹配！",""  ]