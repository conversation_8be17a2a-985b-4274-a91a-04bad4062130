config:
    name: 获取企业实名链接用例

testcases:
-
  name: 获取企业实名链接用例-字段校验
  testcase: testcases/manage/organizations/tc_psn-auth-url_1.yml
  variables:
#    orgsql: select * from ec_organization where realname_status = '0' and organization_territory = '1' and organization_type='1' and organization_status='1' and deleted =0
#    orgsql1: select * from ec_organization where realname_status = '0' and organization_territory = '1' and organization_type='1' and organization_status='2' and deleted =0
#    orgsql2: select * from ec_organization where realname_status = '0' and organization_territory = '1' and organization_type='2' and organization_status='1' and deleted =0
#    orgsql3: select * from ec_organization where realname_status = '0' and organization_territory = '2' and organization_type='1' and organization_status='1' and deleted =0
#    orgsql4: select * from ec_organization where realname_status = '1' and organization_territory = '1' and organization_type='1' and organization_status='1' and deleted =0
#    orgid: ${get_param_by_sql($orgsql, id)}
    #正常企业账号
#    orgNo: ${get_param_by_sql($orgsql, account_number)}
    orgNo: ${ENV(ceswdzxzdhyhwgd1.orgNo)}
    #已注销 内部企业账号
#    orgNo1: ${get_param_by_sql($orgsql1, account_number)}
    orgNo1: ${ENV(deletedcustomOrgNo)}
    #部门账号
#    orgNo2: ${get_param_by_sql($orgsql2, account_number)}
    orgNo2: ${ENV(sign03.main.departNo)}
    #外部企业
#    orgNo3: ${get_param_by_sql($orgsql3, account_number)}
    orgNo3: ${ENV(worg01.orgNo)}
    #企业已实名
#    orgNo4: ${get_param_by_sql($orgsql4, account_number)}
    orgNo4: ${ENV(csqs.orgNo)}
    #未实名内部企业
#    orgCode: ${get_param_by_sql($orgsql, organization_code)}
#    usersql: select * from ec_user where user_territory='1' and user_status= '1' and deleted =0 and organization_id = $orgid
#    usersql2: select * from ec_user where user_territory='1' and user_status= '1' and deleted =1
#    usersql3: select * from ec_user where user_territory='1' and user_status= '2' and deleted =0
#    usersql4: select * from ec_user where user_territory='1' and user_status= '1' and deleted =0 and organization_id != $orgid
    #未实名内部企业下用户编码
#    user_code1: ${get_param_by_sql($usersql, user_code)}
    user_code1: ${ENV(ceswdzxzdhyhwgd1.userCode)}
    #未实名内部企业下用户账号
#    account_number1: ${get_param_by_sql($usersql, account_number)}
    account_number1: ${ENV(ceswdzxzdhyhwgd1.account)}
    #已删除内部用户
#    account_number2: ${get_param_by_sql($usersql2, account_number)}
    #已离职内部用户编码
#    user_code2: ${get_param_by_sql($usersql3, user_code)}
#    user_code4: ${get_param_by_sql($usersql4, user_code)}
    user_code2: ${ENV(userCode.dimission)}
    user_code4: ${ENV(userCode.delete)}
    url1: ${ENV(esign.projectHost)}/main-index-web/home
    url2: ftp://main-index-web/home
  parameters:
    - name-userCode-customAccountNo-clientType-customOrgNo-organizationCode-noticeTypes-notifyUrl-redirectUrl-code-message:
        - [ "用户账号、编码都为空","","","","$orgNo","","","","$url1",1111278,"用户账号/用户编码不能全部为空" ]
        - [ "组织账号、编码都为空","$user_code1","","","","","","","$url1",1111280,"组织账号/组织编码不能全部为空" ]
        - [ "redirectUrl为空","$user_code1","","","$orgNo","","","","",1112004,"URL格式不正确" ]
        - [ "redirectUrl不符合http（s）格式","$user_code1","","","$orgNo","","","","$url2",1112004,"URL格式不正确" ]
#        - [ "用户已删除，账号不存在","","$account_number2","","$orgNo","","","","$url1",1111003,"用户已不存在" ]
        - [ "用户已离职编码不存在","$user_code2","","","$orgNo","","","","$url1",1111003,"用户已不存在" ]
        - [ "用户编码、账号同时存在，编码错误","123","$account_number1","","$orgNo","","","","$url1",1111003,"用户已不存在" ]
        - [ "用户编码不存在","123","","","$orgNo","","","","$url1",1111003,"用户已不存在" ]
        - [ "组织账号不存在","$user_code1","","","1123","","","","$url1",1111102,"组织已不存在" ]
        - [ "组织编码不存在，组织账号编码都传","$user_code1","","","$account_number1","1123","","","$url1",1111102,"组织已不存在" ]
        - [ "组织已注销","$user_code1","","","$orgNo1","","","","$url1",1111102,"组织已不存在" ]
        - [ "组织传部门","$user_code1","","","$orgNo2","","","","$url1",1111378,"部门不支持获取实名状态" ]
        - [ "组织传外部组织","$user_code1","","","$orgNo3","","","","$url1",1111102,"组织已不存在" ]
#        - [ "组织已实名","${ENV(csqs.userCode)}","","","$orgNo4","","","","$url1",200,"成功" ]
        - [ "用户不在组织下","${ENV(csqs.userCode)}","","","$orgNo","","","","$url1",1120018,"当前用户信息和组织信息不匹配！" ]
        - [ "clientType不正确","$user_code1","","test","$orgNo","","","","$url1",1120016,"clientType错误" ]
        - [ "noticeTypes不正确","$user_code1","","","$orgNo","","999","","$url1",1120015,"通知方式只支持 1-短信通知   2-邮件通知" ]
        - [ "notifyUrl不符合http（s）格式","$user_code1","","","$orgNo","","","","$url2",1112004,"URL格式不正确" ]

-
  name: 获取企业实名正常用例
  testcase: testcases/manage/organizations/tc_psn-auth-url_2.yml