config:
  name: "获取所有汉字拼音首字母字符串用例集"

testcases:

  - name: 自动化测试获取所有汉字拼音首字母字符串字段校验-$title1
    testcase: testcases/manage/core/tc_getAllChineseInitialPinyinStr.yml
    parameters:
      title1-chineseString1-isLowerCase1-status1-message1-data1:
        - [ "中文字符不传",null,false,913,"请求的中文字符串不能为空!","" ]
        - [ "中文字符传空字符串","",false,913,"请求的中文字符串不能为空!","" ]
        - [ "大小写不传","是否",null,913,"请求转拼音串的大小写不能为空!","" ]
        - [ "大小写传空字符串","是否","",913,"请求转拼音串的大小写不能为空!","" ]
        - [ "大小写传true和false之外其他字段","是否","xxxxxx",801,"请求格式错误，请重试","" ]
        - [ "中文字符传大写英文大小写为大写","ABC",false,200,"成功","ABC" ]
        - [ "中文字符传大写英文大小写为小写","ABC",true,200,"成功","abc" ]
        - [ "中文字符传小写英文大小写为大写","abc",false,200,"成功","ABC" ]
        - [ "中文字符传小写英文大小写为小写","abc",true,200,"成功","abc" ]
        - [ "中文字符传中文大小写为大写","测试",false,200,"成功","CS" ]
        - [ "中文字符传中文大小写为小写","测试",true,200,"成功","cs" ]
        - [ "中文字符传特殊字符大小写为大写","&*.",false,200,"成功","&*." ]
        - [ "中文字符传特殊字符大小写为小写","&*.",true,200,"成功","&*." ]
        - [ "字符含前后空格","      ABC        ",true,200,"成功","abc" ]
