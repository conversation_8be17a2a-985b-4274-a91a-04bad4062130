config:
    name: "[内]05-系统工具-消息管理-保存消息模板信息"

testcases:

-
    name: 自动化测试保存消息模板信息字段校验-$title1
    testcase: testcases/manage/systools/message/tc_saveMessageTemplate1.yml
    parameters:
       title1-id1-message1-code1-jumpUrl1-messageText1-messageTitle1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["模板ID传空",null,"模板id不能为空",913,"","正文123","","","","","","1000",""]
          - ["模板ID不传","","模板id必须在1-36之间",913,"","正文123","","","","","","1000",""]
          - ["模板不存在","123","请检查入参是否正确",1124000,"","正文123","","","","","","1000",""]
          - ["模板ID长度大于36","123456789012345678901234567890123456789","模板id必须在1-36之间",913,"","正文123","","","","","","1000",""]
          - ["消息标题超过30个字符","123","消息标题不能超过30个字符",913,"","正文123","12345678901234567890123456789012","","","","","1000",""]
          - ["消息正文为空","123","消息正文不能长度为1-300个字符",913,"","","123","","","","","1000",""]
          - ["消息正文超过300个字符","123","消息正文不能长度为1-300个字符",913,"","${generate_random_str(301)}","","","","","","1000",""]
          - ["跳转地址超过30个字符","123","跳转地址不能超过30个字符",913,"12345678901234567890123456789012","正文123","","","","","","1000",""]
          - ["模板ID长度小于1","","模板id必须在1-36之间",913,"","123","","","","","","1000",""]
