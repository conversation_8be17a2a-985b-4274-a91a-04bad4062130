config:
    name: "[内]05-系统工具-消息管理-保存消息模板组信息"

testcases:

-
    name: 自动化测试保存消息模板组信息字段校验-$title1
    testcase: testcases/manage/systools/message/tc_saveMessageTemplateGroup1.yml
    parameters:
       title1-id1-message1-code1-templateDesc1-templateVoList1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["模板ID不传","123","模板id必须在1-36之间",913,"",[{"id":"","selectStatus":"0"}],"","","","","1000",""]
          - ["模板组ID为空",null,"模板组id不能为空",913,"",[],"","","","","1000",""]
          - ["模板组ID不传","","模板组id必须在1-36之间",913,"",[],"","","","","1000",""]
          - ["模板组不存在","123","请检查入参是否正确",1124000,"",[],"","","","","1000",""]
          - ["模板组ID长度大于36","123456789012345678901234567890123456789","模板组id必须在1-36之间",913,"",[],"","","","","1000",""]
          - ["描述长度大于200","123","描述必须在0-200之间",913,"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789011",[],"","","","","1000",""]
          - ["模板ID为空","123","模板id不能为空",913,"",[{"id":null,"selectStatus":"0"}],"","","","","1000",""]
          - ["模板ID长度大于36","123","模板id必须在1-36之间",913,"",[{"id":"123456789012345678901234567890123456789","selectStatus":"0"}],"","","","","1000",""]
          - ["是否选中传空","123","是否选中不能为空",913,"",[{"id":"133","selectStatus":null}],"","","","","1000",""]
          - ["是否选中不传","123","是否选中只支持填写0或1(0未选中1选中)",913,"",[{"id":"133","selectStatus":""}],"","","","","1000",""]
          - ["是否选中传规定以外的参数","123","是否选中只支持填写0或1(0未选中1选中)",913,"",[{"id":"133","selectStatus":"3"}],"","","","","1000",""]
          - ["模板组ID长度小于1","","模板组id必须在1-36之间",913,"",[],"","","","","1000",""]
          - ["模板ID长度小于0","123","模板id必须在1-36之间",913,"",[{"id":"","selectStatus":"0"}],"","","","","1000",""]