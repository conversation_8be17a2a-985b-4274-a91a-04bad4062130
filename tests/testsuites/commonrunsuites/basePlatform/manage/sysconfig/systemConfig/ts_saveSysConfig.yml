config:
    name: "[内]03-应用配置-系统配置-保存系统配置"

testcases:

-
    name: 自动化测试保存系统配置字段校验-$title1-(配置整合之后不用了)
    testcase: testcases/manage/sysconfig/systemConfig/tc_saveSysConfig1.yml
    parameters:
       title1-message1-code1-paramCode1-paramValue1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["参数编码不传","参数编码不能为空",913,null,"6","","","","","1000",""]
         - ["参数编码传空","参数编码不能为空",913,"","6","","","","","1000",""]
#         - ["参数编码不存在","fdasf:参数编码不存在",1121012,"fdasf","","6","","","","","1000",""]
#         - ["参数值传空","参数值不能为空",1121000,"login_allow_timelimit","","","","","","1000",""]
#         - ["本地用户登录缓存时间小于0","本地用户登录缓存时间只支持大于等于0，小于等于1440的数字(包含一位小数)",1121002,"login_allow_timelimit","-1","","","","","1000",""]
#         - ["本地用户登录缓存时间大于1440","本地用户登录缓存时间只支持大于等于0，小于等于1440的数字(包含一位小数)",1121002,"login_allow_timelimit","1441","","","","","1000",""]
#         - ["内部用户意愿认证缓存时间小于0","内部用户意愿认证缓存时间只支持大于等于0，小于等于30的数字(包含一位小数)",1121004,"inner_will_timelimit","-1","","","","","1000",""]
#         - ["内部用户意愿认证缓存时间大于30","内部用户意愿认证缓存时间只支持大于等于0，小于等于30的数字(包含一位小数)",1121004,"inner_will_timelimit","31","","","","","1000",""]
#         - ["文本超过30个字符","文本不能超过30个字符",1121005,"file_preview_waterprint","12345678901234567890123456789011","","","","","1000",""]
         - ["密码有效期小于0","密码有效期只支持大于等于0小于等于180的正整数",1121006,"password_timelimit","-1","","","","","1000",""]
         - ["密码有效期小于0大于180","密码有效期只支持大于等于0小于等于180的正整数",1121006,"password_timelimit","181","","","","","1000",""]
         - ["密码有效性为空","参数值不能为空",1121000,"password_overdue_type","","","","","","1000",""]
         - ["密码失败冻结次数小于0","登录失败冻结次数只支持大于等于0小于等于10的正整数",1121008,"login_fail_limit","-1","","","","","1000",""]
         - ["密码失败冻结次数大于10","登录失败冻结次数只支持大于等于0小于等于10的正整数",1121008,"login_fail_limit","11","","","","","1000",""]
         - ["帐号冻结解冻间隔小于0","账号冻结自动解冻间隔只支持大于0小于等于1440的数字(包含位小数)",1121009,"login_thew_time","-1","","","","","1000",""]
         - ["帐号冻结解冻间隔大于1440","账号冻结自动解冻间隔只支持大于0小于等于1440的数字(包含位小数)",1121009,"login_thew_time","1441","","","","","1000",""]
         - ["验证码单日发送次数小于0","验证码-单手机号单日发送次数只支持大于等于0的正整数",1121010,"auth_code_limit","-1","","","","","1000",""]
         - ["验证码有效期小于0","验证码-有效期只支持大于60小于等于600的正整数",1121011,"auth_code_timelimit","-1","","","","","1000",""]
         - ["验证码有效期大于600","验证码-有效期只支持大于60小于等于600的正整数",1121011,"auth_code_timelimit","900","","","","","1000",""]
         - ["验证码非60倍数","验证码-有效期请输入60的整数倍",1121015,"auth_code_timelimit","61","","","","","1000",""]
         - ["催办时间小于15","催办时间间隔只支持大于等于15，小于等于1440的正整数",1121014,"urging_interval","14","","","","","1000",""]
         - ["催办时间小于大于1440","催办时间间隔只支持大于等于15，小于等于1440的正整数",1121014,"urging_interval","1441","","","","","1000",""]


