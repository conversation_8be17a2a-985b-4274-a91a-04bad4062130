config:
  name: 同步组织
  variables:
    - legalPersonId1: ""
    - parentOrganizationCode1: ""
    - organizationName1: ""
    - organizationCode1: ""
    - licenseType1: ""
    - licenseNumber1: ""
    - organizationTerritory1: ""
    - accountNumber1: ""
    - orgCode1: ""
    - deleted1: ""
    - orderNum1: ""

testcases:
  - name: $name
    testcase: testcases/manage/sync/sync_org.yml
    parameters:
     - name-tenantCode-syncType-thirdType-organizationStatus-legalPersonId-parentOrganizationCode-organizationName-organizationCode-licenseType-licenseNumber-organizationTerritory-accountNumber-orgCode-deleted-orderNum-message-code:
        - [ "TC1同步增量组织机构状态为存续", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC2同步全量组织机构状态为存续", "0","ALL", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC3同步全量组织机构状态为注销", "0","ALL", null, 0 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC4同步全量组织机构状态为未使用", "0","ALL", null, 3 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC5同步增量组织机构状态为注销", "0","INCREMENT", null, 0 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC6同步增量组织机构状态为未使用", "0","INCREMENT", null, 3 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC7同步增量组织机构法人为测试签署一", "0","INCREMENT", null, 1 ,"sign01",$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC8同步增量组织机构上级组织编码为po", "0","INCREMENT", null, 1 ,$legalPersonId1,"163fd315ba954c6a9fb5e204d4e80a9d",$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC9同步增量组织机构名称为XX测试企业", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,"esigntest慕雅测试环境内部企业77",$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC10同步增量组织机构code为XX测试企业", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,"3535891195e84113a2a0964bd3f9a910",$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC11同步增量组织机构证件类型为组织机构代码", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,11,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC12同步增量组织机构证件类型为社会信用代码", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,12,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC13同步增量组织机构证件类型为工商注册号", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,13,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC14同步增量组织机构证件类型为其他", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,34,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC15同步增量组织机构证件类型不存在", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,77,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC16同步增量组织机构证件号为XX", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,"500102200203042909",$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC17同步增量组织机构地域为内部", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC18同步增量组织机构地域为外部", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,2,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC19同步增量组织机账号为XX", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,"ESIGNTESTMYCSHJNBQY66",$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC20同步增量组织机编码为XX", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,"3535891195e84113a2a0964bd3f9a910",$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC21同步增量组织机状态已删除的", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,1,$orderNum1,"服务器成功返回",200]
        - [ "TC22同步增量组织状态未删除的", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,0,$orderNum1,"服务器成功返回",200]
        - [ "TC23同步增量组织排序为100的", "0","INCREMENT", null, 1 ,$legalPersonId1,$parentOrganizationCode1,$organizationName1,$organizationCode1,$licenseType1,$licenseNumber1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,100,"服务器成功返回",200]
        - [ "TC24同步增量组织信息完且正确", "0","INCREMENT", null, 1 ,"muyatest","163fd315ba954c6a9fb5e204d4e80a9d","esigntest慕雅测试环境内部企业33","81d725cc0d14a16885b45d5bde31f1f",11,"9100000075182591F9",1,"ESIGNTESTMYCSHJNBQY33",$orgCode1,1,1,"服务器成功返回",200]
        - [ "TC25同步增量组织信息完且不正确", "0","INCREMENT", null, 2 ,"muyatest","163fd315ba954c6a9fb5e204d4e80a9d","esigntest慕雅测试环境内部企业33","81d725cc0d14a16885b45d5bde31f1f",19,"9100000075182591F9",1,"ESIGNTESTMYCSHJNBQY33",$orgCode1,1,88,"服务器成功返回",200]