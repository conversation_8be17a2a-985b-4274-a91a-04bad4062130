config:
    name: "创建同步用户"
    variables:
      - userStatus: ""
      - accountNumber: ""
      - userName1: ""
      - userMobile1: ""
      - userEmail1: ""
      - organizationCode1: ""
      - licenseType1: ""
      - licenseNumber1: ""
      - bankCardNo1: ""
      - userWechat1: ""
      - entryTime1: ${getTodayZeroTime()}
      - dimissionTime1: ""
      - orderNum1: ""
      - parttimeId1: ""
      - userTerritory1: ""
      - useLanguage1: ""
      - deleted1: ""

testcases:
  - name: $title
    parameters:
      title-tenantCode-syncType-thirdType-userStatus-accountNumber-userName-userMobile-userEmail-organizationCode-licenseType-licenseNumber-bankCardNo-userWechat-entryTime-dimissionTime-orderNum-parttimeId-userTerritory-useLanguage-deleted-message-code:
        - [ "TC1增量同步在职的用户", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC2增量同步离职的用户", "0","INCREMENT", null, 2 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC3增量同步活跃的用户", "0","INCREMENT", null, 3 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC4增量同步注销的用户", "0","INCREMENT", null, 4 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC5增量同步未启用的用户", "0","INCREMENT", null, 5 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC6增量同步在职的用户", "0","ALL", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC7增量同步离职的用户", "0","ALL", null, 2 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC8增量同步活跃的用户", "0","ALL", null, 3 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC9增量同步注销的用户", "0","ALL", null, 4 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC10增量同步未启用的用户", "0","ALL", null, 5 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC11增量同步在职的用户账号为XX", "0","INCREMENT", null, 1 ,"cesymg",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC12增量同步在职的用户姓名为测试喻敏孤", "0","INCREMENT", null, 1 ,"","测试喻敏孤",$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC13增量同步在职的用户手机号为XX", "0","INCREMENT", null, 1 ,"",$userName1,"***********",$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC14增量同步在职的用户邮箱为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,"<EMAIL>",$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC15增量同步在职的用户组织code的为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,"71a39331568b4ba4a3c16eaccc1cc30c",$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC16增量同步在职的用户证件类型为护照", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,13,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC17增量同步在职的用户证件类型为港澳居民来往内地通行证", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,17,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC18增量同步在职的用户证件类型为护照台湾居民来往大陆通行证", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,18,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC19增量同步在职的用户证件类型为大陆身份证", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,19,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC20增量同步在职的用户证件类型为其他", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,23,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC21增量同步在职的用户证件号为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,"******************",$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC22增量同步在职的用户银行卡号为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,"6252475187797023023",$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC23增量同步在职的用户入职时间", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC24增量同步在职的用户离职时间", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC25增量同步在职的用户排序为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,100,$parttimeId1,$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC26增量同步在职的用户兼职组织id为XX", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,"1743094796074360833",$userTerritory1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC27增量同步在职的用户地域为内部的", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC28增量同步在职的用户地域为外部的", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,2,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC29增量同步在职的已删除的用户", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,1,"服务器成功返回",200]
        - [ "TC30增量同步在职的未删除的用户", "0","INCREMENT", null, 1 ,"",$userName1,$userMobile1,$userEmail1,$organizationCode1,$licenseType1,$licenseNumber1,$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,$userTerritory1,$useLanguage1,0,"服务器成功返回",200]
        - [ "TC31增量同步在职信息完整的内部用户", "0","INCREMENT", null, 1 ,"cesymg","测试喻敏孤","***********","<EMAIL>","71a39331568b4ba4a3c16eaccc1cc30c",19,"******************","6252475187797023023",$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,"1743094796074360833",1,$useLanguage1,$deleted1,"服务器成功返回",200]
        - [ "TC32增量同步在职信息完整的外部用户", "0","INCREMENT", null, 1 ,"cesxh","测试玄火","***********",$userEmail1,"74e7339f02b643808fd63a2af6a0f590",19,"350781196403078640",$bankCardNo1,$userWechat1,$entryTime1,$dimissionTime1,$orderNum1,$parttimeId1,2,$useLanguage1,$deleted1,"服务器成功返回",200]

    testcase: testcases/manage/sync/sync_orgUser.yml
