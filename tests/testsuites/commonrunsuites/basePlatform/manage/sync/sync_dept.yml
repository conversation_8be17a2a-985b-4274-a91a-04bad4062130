config:
    name: "创建同步部门"
    variables:
      - organizationStatus: ""
      - parentOrganizationCode1: ""
      - organizationName1: ""
      - organizationCode1: ""
      - organizationTerritory1: ""
      - accountNumber1: ""
      - orgCode1: ""
      - deleted1: ""
      - orderNum1: ""

testcases:
  - name: $name
    parameters:
      name-tenantCode-syncType-thirdType-organizationStatus-parentOrganizationCode-organizationName-organizationCode-organizationTerritory-accountNumber-orgCode-deleted-orderNum-message-code:
        - [ "TC1增量同步部门状态为存续的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC2增量同步部门状态为注销的", "0","INCREMENT", null, 2 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC3增量同步部门状态为未启用的", "0","INCREMENT", null, 3 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC4全量同步部门状态为存续的", "0","ALL", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC5全量同步部门状态为注销的", "0","ALL", null, 2 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC6全量同步部门状态为未启用的", "0","ALL", null, 3 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC7增量同步部门上级组织编码为XX", "0","INCREMENT", null, 1 ,"9018f523c4534cc5b29e78f7dfb7f730",$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC8增量同步部门组织名称为XX", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,"esigntest慕雅测试环境内部企业22",$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200 ]
        - [ "TC9增量同步部门组织编码为XX", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,"9018f523c4534cc5b29e78f7dfb7f730",$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC10增量同步部门地域为内部的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,1,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC11增量同步部门状态为外部的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,2,$accountNumber1,$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC12增量同步部门账号为XX", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,"ESIGNTESTMYCSHJNBQY22",$orgCode1,$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC13增量同步部门编码为XX", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,"9018f523c4534cc5b29e78f7dfb7f730",$deleted1,$orderNum1,"服务器成功返回",200]
        - [ "TC14增量同步部门状态已删除的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,1,$orderNum1,"服务器成功返回",200]
        - [ "TC15增量同步部门状态为存续的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,0,$orderNum1,"服务器成功返回",200]
        - [ "TC16增量同步部门排序为100的", "0","INCREMENT", null, 1 ,$parentOrganizationCode1,$organizationName1,$organizationCode1,$organizationTerritory1,$accountNumber1,$orgCode1,$deleted1,100,"服务器成功返回",200]
        - [ "TC17增量同步部门信息完整", "0","INCREMENT", null, 1 ,"163fd315ba954c6a9fb5e204d4e80a9d","esigntest慕雅测试环境内部企业22","9018f523c4534cc5b29e78f7dfb7f730",1,"ESIGNTESTMYCSHJNBQY22","9018f523c4534cc5b29e78f7dfb7f730",1,100,"服务器成功返回",200]
        - [ "TC18增量同步部门信息错误", "0","INCREMENT", null, 1 ,"e0e66af482ba4db297e0b76be33a7967","esigntest慕雅测试环境内部企业22","9018f523c4534cc5b29e78f7dfb7f730",1,"LSDCSZZ","9018f523c4534cc5b29e78f7dfb7f730",1,0000,"服务器成功返回",200]

    testcase: testcases/manage/sync/sync_dept.yml