config:
    name: "[内]05-系统工具-页面配置-保存页面配置"

testcases:

#-
#    name: 自动化测试保存页面配置字段校验-$title1
#    testcase: testcases/manage/pageConfig/pageConfigManage/tc_savePageConfig.yml
#    parameters:
#       title1-message1-code1-requestList1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#         - ["页面配置明细传空","页面配置明细不能为空",913,null,"","","","",1000,""]
#         - ["页面配置明细不传","页面配置明细不能为空",913,[],"","","","",1000,""]
#         - ["参数编码不传","参数编码不能为空",913,[{"paramCode": "","fileKey": "","paramValue": "fdsa"}],"","","","",1000,""]
#         - ["参数编码为空","参数编码不能为空",913,[{"paramCode": null,"fileKey": "","paramValue": "fdsa"}],"","","","",1000,""]
#         - ["参数值不传","参数值不能为空",913,[{"paramCode": "esign_sys_name","fileKey": "","paramValue": ""}],"","","","",1000,""]
#         - ["参数值传空","参数值不能为空",913,[{"paramCode": "esign_sys_name","fileKey": "","paramValue": null}],"","","","",1000,""]
#         - ["参数值超过30个字符","参数值不能超过30个字符",1123001,[{"paramCode": "esign_sys_name","fileKey": "","paramValue": "12345678901234567890123456789011"}],"","","","",1000,""]
#         - ["参数值超过100个字符","参数值不能超过100个字符",1123002,[{"paramCode": "brand_brief","fileKey": "","paramValue": "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012"}],"","","","",1000,""]
#         - ["参数值超过1000个字符","参数值不能超过1000个字符",1123004,[{"paramCode": "brand_logo","fileKey": "","paramValue": "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012"}],"","","","",1000,""]
#         - ["参数编码不正确","参数编码不正确",1123003,[{"paramCode": "fdsa","fileKey": "","paramValue": "2131232"}],"","","","",1000,""]
#
#

-
    name: 自动化测试保存页面配置场景校验-$title1
    testcase: testcases/manage/pageConfig/pageConfigManage/tc_savePageConfig2.yml
    parameters:
      title1-message1-code1-requestList1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - ["成功查询出修改的页面配置","成功",200,[{"paramCode": "esignpro_sys_name","fileKey": "","paramValue": "醉生自动化更新页面配置"}],"","","","",1000,""]