config:
    name: "[内]05-系统工具-页面配置-获取页面配置列表"

testcases:

-
    name: 自动化测试获取页面配置列表字段校验-$title1
    testcase: testcases/manage/pageConfig/pageConfigManage/tc_getPageConfigList.yml
    parameters:
       title1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["历史接口-目前页面应该没用到","No permission",1111208,"","","","","1000",""]


#-
#  name: 自动化测试获取页面配置列表场景校验-$title1
#  testcase: testcases/manage/pageConfig/pageConfigManage/tc_getPageConfigList2.yml
#  parameters:
#    title1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#      - ["成功查询出修改的页面配置","成功",200,"","","","","1000",""]