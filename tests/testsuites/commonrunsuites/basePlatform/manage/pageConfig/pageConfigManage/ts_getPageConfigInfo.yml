config:
    name: "[内]05-系统工具-页面配置-获取页面配置信息"

testcases:

-
    name: 自动化测试获取页面配置信息字段校验-$title1
    testcase: testcases/manage/pageConfig/pageConfigManage/tc_getPageConfigInfo.yml
    parameters:
       title1-message1-code1-tenantCode2-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["租户编码不传","成功",200,"","","","","",1000,""]
         - ["租户编码传空","成功",200,null,"","","","",1000,""]
#         - ["租户编码加密规则不正确","租户编码验证不正确",1111204,"fdsafdsaf","","","","",1000,""]