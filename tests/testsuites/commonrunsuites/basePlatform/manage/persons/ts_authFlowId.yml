config:
    name: 用户实名认证通过后更新实名信息并存证


testcases:

-
  name: 用户实名认证通过后更新实名信息并存证异常用例
  testcase: testcases/manage/persons/tc_authFlowId.yml
  variables:
    #正常内部用户
#    sql1: select * from ec_user where user_territory='1' and deleted=0
#    user_code1: ${get_param_by_sql($sql1, user_code)}
    #外部用户
    sql2: select * from ec_user where user_territory='2' and deleted=0
    user_code2: ${get_param_by_sql($sql2, user_code)}
    #已离职用户
    sql3: select * from ec_user where user_territory='1' and user_status='2' and deleted=0
    user_code3: ${get_param_by_sql($sql3, user_code)}
    #已删除的内部用户
    sql4: select * from ec_user where user_territory='1' and deleted=1
    user_code4: ${get_param_by_sql($sql4, user_code)}
    applysql: select * from ec_unifiedauth_apply where unifiedauth_type=1 and unifiedauth_result=1 and user_code in (select user_code from ec_user where user_territory='1' and user_status='1' and deleted=0)
    appid1: ${get_param_by_sql($applysql, id)}
    flid: ${get_param_by_sql($applysql, auth_flow_id)}
    user: ${get_param_by_sql($applysql, user_code)}
  parameters:
    - name-userCode-customAccountNo-applyId-flowId-realNameStatus-code-message-data:
        - [ "账号编码都为空","","","123","123","1",1111278,"用户账号/用户编码不能全部为空","" ]
        - [ "账号不存在","","123htjh","123","123","1",1111003,"用户已不存在",""  ]
        - [ "编码不存在","grshgrhtr","","123","123","1",1111003,"用户已不存在",""  ]
        - [ "用户已离职","$user_code3","","123","123","1",1111003,"用户已不存在",""  ]
        - [ "用户已删除","$user_code4","","123","123","1",1111003,"用户已不存在",""  ]
        - [ "内部部用户，实名状态不传","$user","","$appid1","$flid","",1120017,"realNameStatus错误",""  ]
        - [ "内部部用户，实名状态非0、1","$user","","$appid1","$flid","2",1120017,"realNameStatus错误",""  ]
        - [ "意愿申请id不传","$user","","","$flid","1",913,"申请认证id不能为空",""  ]
        - [ "意愿申请id不存在","$user","","123","$flid","1",1111370,"该意愿申请不存在",""  ]
        - [ "flowId不传","$user","","$appid1","","1",913,"流程id不能为空",""  ]
#        - [ "flowId不存在","$user","","$appid1","123","1",1450007,"授权流程id不存在",""  ]



-
  name: 用户实名认证通过后更新实名信息并存证正常用例
  testcase: testcases/manage/persons/tc_authFlowId_2.yml