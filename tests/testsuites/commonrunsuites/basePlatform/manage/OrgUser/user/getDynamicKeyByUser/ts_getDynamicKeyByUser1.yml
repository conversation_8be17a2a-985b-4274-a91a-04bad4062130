config:
    name: "根据当前登录用户信息获取动态秘钥-有效期为当天-字段校验"

testcases:

   # name: 自动化测试删除用户场景
   # testcase: testcases/manage/OrgUser/user/deleteUserById/tc_deleteUserById2.yml

-
    name: 自动化测试根据当前登录用户信息获取动态秘钥字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getDynamicKeyByUser/tc_getDynamicKeyByUser1.yml
    parameters:
       title1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["接口成功返回","==",200,"","","","","1000","132156741231566"]