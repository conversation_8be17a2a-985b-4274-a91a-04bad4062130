config:
    name: "修改用户Admin"
    variables:
        num: ${get_snowflake()}
        code: zszdhcsadmin$num
        data1:
         {
            "customOrgNo": ZDHCSXGYHGLYYH$num,
            "name": 醉生自动化修改管理员用户$num,
            "organizationType": COMPANY,
            "parentOrgNo": ,
            "parentCode": "0",
            "licenseType": ,
            "licenseNo": ,
            "legalRepAccountNo": ,
            "legalRepUserCode":
         }
        res1: ${create_inner_organizations($data1)}
        setup_organizationCode: ${get_organizationCode($res1)}
        userdata:
         {
            "customerIP": "",
            "deptId": "",
            "domain": "",
            "params": {
               "userName": "醉生自动化修改用户Admin",          #用户姓名不能为空   ^[a-zA-Z\u4e00-\u9fa5\s·]{2,26}$
               "userType": "2",          #用户类型不能为空    [1,2]用户类型(1管理员、2普通用户)
               "userWechat": "",        #微信支持输入0-50字
               "userMobile": "6B5NO25CB3TU4zHIjJvzTw==",        #用户手机不能为空 AES加密
               "userEmail": "YmYJkeoYf1ir7HC8It7bfQ==",          #邮箱不能为空 AES加密
               "licenseType": 19,          #$|[1][3,7,8,9]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
               "licenseNumber": "",   #证件号 AES加密
               "bankCardNo": "",         #银行卡 AES加密
               "userStatus": "1",        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
               "userTerritory": "1",  #用户组织类型不能为空   用户组织类型(1内部 2外部)
               "userCode": $code,            #用户编码支持输入2-30字 不能为空
               "userWorkWechat": "",  #企业微信支持输入0-50字
               "accountNumber": $code,
               "userDingTalk": "",     #钉钉支持输入0-50字
               "userFeiShu": "",          #飞书支持输入0-50字
#               "entryTime": "2022-04-27 00:00:00",           #入职/注册时间不能为空   入职/注册时间必须在1-19之间  入职/注册时间格式必须是yyyy-MM-dd HH:mm:ss
               "dimissionTime": "",   #离职/注销时间必须在1-19之间     离职/注销时间格式必须是yyyy-MM-dd HH:mm:ss
               "organizationId": null,   #所属组织支持输入1-36字
               "organizationCode": $setup_organizationCode,  #所属组织编码支持输入1-36字 所属组织编码不能为空
               "ecUserParttimeIDList": [ ]
            },
            "platform": "",
            "tenantCode": "1000",
            "userCode": $code
         }
        res2: ${save_inner_user($userdata)}
        getdata:
         {
            "customerIP": "",
            "deptId": "",
            "domain": "",
            "params": {
               "userName": "醉生自动化修改用户Admin",
            },
            "platform": "",
            "tenantCode": "1000",
            "userCode": $code
         }
        res3: ${get_user_by_username($getdata)}
        adminUserId: ${get_value($res3,uid)}
        organizationId: ${get_value($res3,oid)}

testcases:

-
    name: 自动化测试修改用户Admin字段校验-$title1
    testcase: testcases/manage/OrgUser/user/updateUserAdmin/tc_updateUserAdmin1.yml
    parameters:
       userId-title1-message1-code1-userEmail1-userMobile1-userWechat1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - [ $adminUserId,"用户邮箱不传","用户验证不正确",1111020,"","17601320213","22","","","","","1000","ZUISHENG$num"]
          - [ $adminUserId,"用户邮箱传空","用户验证不正确",1111020,null,"17601320213","22","","","","","1000","ZUISHENG1$num"]
          - [ $adminUserId,"用户手机不传","用户验证不正确",1111020,"<EMAIL>","","22","","","","","1000","ZUISHENG2$num"]
          - [ $adminUserId,"用户手机传空","用户验证不正确",1111020,"<EMAIL>",null,"22","","","","","1000","ZUISHENG3$num"]
          - [ $adminUserId,"用户微信长度大于50","微信支持输入0-50字",913,"<EMAIL>","17601320213","12345678901234567890123456789012345678901234567890123456","","","","","1000","ZUISHENG4$num"]


-
    name: 删除组织、用户
    testcase: testcases/manage/OrgUser/user/updateUser/teardown_delete.yml
    parameters:
      id-oId:
        - [ $adminUserId,$organizationId ]