config:
    name: "[内]01-组织用户-用户管理-根据用户Code获取用户信息"
    variables:
      sys_role_code: SUB_SYS_ADMIN
      #      # 系统管理员 角色id
      sys_ternary_id: ${get_ternary_distribution_id($sys_role_code)}

testcases:

-
    name: 自动化测试根据用户Code获取用户信息字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserByUserCode/tc_getUserByUserCode1.yml
    parameters:
       title1-userCode1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1:
          - ["用户编码为空",null,"用户编码不能为空",913,"","","","","1000"]
          - ["用户编码不传","","用户编码不能为空",913,"","","","","1000"]

#- 注:开启该用例需要邮件支持,创建用户时的邮箱,和getpassword()的邮箱相同
#    name: 自动化测试根据用户Code获取用户信息场景校验-$title1
#    testcase: testcases/manage/OrgUser/user/getUserByUserCode/tc_getUserByUserCode2.yml
#    parameters:
#      title1-userName1-message1-code1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1:
#          - ["根据新增用户Code获取新增用户成功","醉生测试根据用户Code获取用户信息","成功",200,"醉生测试根据用户Code获取用户信息场景校验","","","","","1000"]