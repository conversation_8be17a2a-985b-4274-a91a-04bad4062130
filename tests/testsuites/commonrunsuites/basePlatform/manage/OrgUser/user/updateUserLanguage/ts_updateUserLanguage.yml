config:
    name: "修改用户的国际化语言"
    variables:
       sys_role_code: SUB_SYS_ADMIN
     #      # 系统管理员 角色id
       sys_ternary_id: ${get_ternary_distribution_id($sys_role_code)}


testcases:


-
    name: 自动化测试修改用户的国际化语言字段校验-$title1
    testcase: testcases/manage/OrgUser/user/updateUserLanguage/tc_updateUserLanguage1.yml
    parameters:
       title1-message1-code1-useLanguage1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["语言为空","语言不能为空",913,null,"","","","","1000",""]
          - ["语言不传","输入的语言不支持",1111250,"","","","","","1000",""]

#-   注:开启该用例需要邮件支持,创建用户时的邮箱,和getpassword()的邮箱相同
#    name: 自动化测试修改用户的国际化语言场景校验-$title1
#    testcase: testcases/manage/OrgUser/user/updateUserLanguage/tc_updateUserLanguage2.yml
#    parameters:
#      title1-message1-code1-useLanguage1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#        - ["修改用户国际化语言","成功",200,"en","醉生测试修改用户国际化语言场景","","","","","1000",""]