config:
    name: "按照用户编码和用户名称查询用户信息"

testcases:

-
    name: 自动化测试按照用户编码和用户名称查询用户信息字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserListByUserCodeName/tc_getUserListByUserCodeName1.yml
    parameters:
       title1-message1-code1-organizationTerritory1-userMainType1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["用户名称为空","用户名称不能为空",913,"1","",null,"","","","","1000",""]
          - ["用户名称不传","用户名称不能为空",913,"1","","","","","","","1000",""]
          - ["用户类型传以外的参数","填写的用户组织类型不支持",913,"3","","醉生","","","","","1000",""]


-
    name:  自动化测试按照用户编码和用户名称查询用户信息场景校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserListByUserCodeName/tc_getUserListByUserCodeName2.yml
    parameters:
      title1-message1-code1-organizationTerritory1-userMainType1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - [ "能根据用户编码和用户名称查询出新增用户信息","成功",200,"1","","醉生测试用户编码和用户名称查询用户信息","","","","","1000","" ]