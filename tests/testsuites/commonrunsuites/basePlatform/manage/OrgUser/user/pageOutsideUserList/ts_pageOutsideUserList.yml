config:
    name: "获取外部用户分页列表"

testcases:


-
    name: 自动化测试获取外部用户分页列表字段校验-$title1
    testcase: testcases/manage/OrgUser/user/pageOutsideUserList/tc_pageOutsideUserList1.yml
    parameters:
       title1-message1-code1-organizationName1-userName1-currPage1-pageSize1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["接口成功返回","成功",200,"","",1,5,"","","","","1000",""]

-
    name: 自动化测试获取外部用户分页列表场景校验-$title1
    testcase: testcases/manage/OrgUser/user/pageOutsideUserList/tc_pageOutsideUserList2.yml
    parameters:
      title1-message1-code1-organizationName1-userName1-currPage1-pageSize1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - [ "能够查询出新增的外部用户","成功",200,"","醉生新增外部用户测试外部用户分页列表",1,5,"","","","","1000","" ]