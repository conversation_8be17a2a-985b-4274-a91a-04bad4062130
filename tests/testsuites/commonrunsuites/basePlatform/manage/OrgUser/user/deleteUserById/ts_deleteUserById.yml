config:
    name: "[内]01-组织用户-用户管理-删除用户"

testcases:

-
    name: 自动化测试删除用户字段校验-$title1
    testcase: testcases/manage/OrgUser/user/deleteUserById/tc_deleteUserById1.yml
    parameters:
       title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["删除用户ID为空",null,"参数id必填!",913,"","","","","1000",""]
          - ["删除用户ID不传","","参数id必填!",913,"","","","","1000",""]
          - ["删除用户ID不存在","123456485462132","用户不存在",1111021,"","","","","1000",""]

-
    name: 自动化测试删除用户场景-$title1
    testcase: testcases/manage/OrgUser/user/deleteUserById/tc_deleteUserById2.yml
    parameters:
      title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - ["删除用户成功","","成功",200,"","","","","1000",""]