config:
    name: "通过组织id获取用户列表"

testcases:

-
    name: 自动化测试通过组织id获取用户列表字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserListByOrganizationID/tc_getUserListByOrganizationID1.yml
    parameters:
       title1-message1-code1-organizationId1-userMainType1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["所属组织id不传","所属组织id不能为空",913,"","","","","","","","1000",""]
          - ["所属组织id传空","所属组织id不能为空",913,null,"","","","","","","1000",""]

-
    name: 自动化测试通过组织id获取用户列表场景校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserListByOrganizationID/tc_getUserListByOrganizationID2.yml
    parameters:
      title1-message1-code1-organizationId1-userMainType1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - ["能查询出新建的用户","成功",200,"","","醉生测试通过组织ID获取用户列表场景","","","","","1000","" ]