config:
    name: "导入内部用户Excel"
    variables:
      num: ${get_snowflake()}
      data1:
        {
          "customOrgNo": ZUISHENCSDRNBYH$num,
          "name": 醉生自动化测试导入内部用户$num,
          "organizationType": COMPANY,
          "parentOrgNo": ,
          "parentCode": "0",
          "licenseType": ,
          "licenseNo": ,
          "legalRepAccountNo": ,
          "legalRepUserCode":
        }
      res1: ${create_inner_organizations($data1)}
      setup_organizationCode: ${get_value($res1,organizationCode)}
      reason1: "共1条,导入成功0条/失败1条"
      status1: 3

testcases:

####todo 数据库唯一性校验未做自动化(校验已存在的)
-
    name: 自动化测试导入内部用户Excel场景集合（异步）
    testcase: testcases/manage/OrgUser/user/importInsideUserExcel/tc_importInsideUserExcel1.yml
#    parameters:
#       title1-message1-code1-filename1-fileType1-filePath1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#          - ["导入空模板","成功",200,"管理平台-内部用户导入空模板","multipart/form-data;","data/importInsideUser/管理平台-内部用户导入空模板.xlsx","","","","","1000",""]
          #- ["超过5000条","导入数据不能超过5000条",1111248,"管理平台-内部用户导入超过5000条","multipart/form-data;","data/importInsideUser/管理平台-内部用户导入超过5000条.xlsx","","","","","1000","" ]
          #

-
    name: 自动化测试导入内部用户Excel场景集合2-$title1
    testcase: testcases/manage/OrgUser/user/importInsideUserExcel/tc_importInsideUserExcel2.yml
    parameters:
      title1-task_reason1-task_name1-task_status1-filename1-filePath1:
        - [ "内部用户导入所属组织帐号异常校验","共5条,导入成功0条/失败5条","批量导入用户-管理平台-内部用户导入所属组织帐号异常校验",$status1,"管理平台-内部用户导入所属组织帐号异常校验","importInsideUser/organizationNumber/管理平台-内部用户导入所属组织帐号异常校验.xlsx" ]

        - [ "内部用户导入用户姓名异常校验","共13条,导入成功0条/失败13条","批量导入用户-管理平台-内部用户导入用户姓名异常校验",$status1,"管理平台-内部用户导入用户姓名异常校验","importInsideUser/userName/管理平台-内部用户导入用户姓名异常校验.xlsx" ]

        - [ "内部用户导入账号异常校验","共14条,导入成功0条/失败14条","批量导入用户-管理平台-内部用户导入账号异常校验",$status1,"管理平台-内部用户导入账号异常校验","importInsideUser/accountNumber/管理平台-内部用户导入账号异常校验.xlsx" ]

        - [ "Excel中有重复帐号","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复帐号",$status1,"管理平台-内部用户导入有重复帐号","importInsideUser/accountNumber/管理平台-内部用户导入有重复帐号.xlsx"]

        - [ "管理平台-内部用户手机号异常校","共15条,导入成功0条/失败15条","批量导入用户-管理平台-内部用户手机号异常校验",$status1,"管理平台-内部用户手机号异常校验","importInsideUser/userMobile/管理平台-内部用户手机号异常校验.xlsx" ]
        - [ "Excel中有重复用户手机","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复手机号",$status1,"管理平台-内部用户导入有重复手机号","importInsideUser/userMobile/管理平台-内部用户导入有重复手机号.xlsx"]

        - [ "内部用户邮箱异常校验","共30条,导入成功0条/失败30条","批量导入用户-管理平台-内部用户邮箱异常校验",$status1,"管理平台-内部用户邮箱异常校验","importInsideUser/userEmail/管理平台-内部用户邮箱异常校验.xlsx" ]
        - [ "Excel中有重复邮箱","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复邮箱",$status1,"管理平台-内部用户导入有重复邮箱","importInsideUser/userEmail/管理平台-内部用户导入有重复邮箱.xlsx"]

        - [ "内部用户银行卡号异常校验","共13条,导入成功0条/失败13条","批量导入用户-管理平台-内部用户银行卡号异常校验",$status1,"管理平台-内部用户银行卡号异常校验","importInsideUser/bankCardNo/管理平台-内部用户银行卡号异常校验.xlsx" ]
        - [ "Excel中有重复银行卡号",$reason1,"批量导入用户-管理平台-内部用户导入有重复银行卡号",$status1,"管理平台-内部用户导入有重复银行卡号","importInsideUser/bankCardNo/管理平台-内部用户导入有重复银行卡号.xlsx"]

        - [ "Excel中有重复钉钉","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复钉钉",$status1,"管理平台-内部用户导入有重复钉钉","importInsideUser/userDingTalk/管理平台-内部用户导入有重复钉钉.xlsx"]

        - [ "Excel中有重复企业微信","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复企业微信",$status1,"管理平台-内部用户导入有重复企业微信","importInsideUser/userWorkWechat/管理平台-内部用户导入有重复企业微信.xlsx"]

        - [ "Excel中有重复微信","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复微信",$status1,"管理平台-内部用户导入有重复微信","importInsideUser/userWechat/管理平台-内部用户导入有重复微信.xlsx"]

        - [ "Excel中有重复飞书","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复飞书",$status1,"管理平台-内部用户导入有重复飞书","importInsideUser/userFeiShu/管理平台-内部用户导入有重复飞书.xlsx"]

        - [ "Excel中有重复证件号码","共2条,导入成功0条/失败2条","批量导入用户-管理平台-内部用户导入有重复证件号码",$status1,"管理平台-内部用户导入有重复证件号码","importInsideUser/licenseNumber/管理平台-内部用户导入有重复证件号码.xlsx" ]
        - [ "内部用户证件类型身份证异常校验","共15条,导入成功0条/失败15条","批量导入用户-管理平台-内部用户证件类型身份证异常校验",$status1,"管理平台-内部用户证件类型身份证异常校验","importInsideUser/licenseNumber/idCard/管理平台-内部用户证件类型身份证异常校验.xlsx" ]
        - [ "内部用户证件类型港澳通行证异常校验","共15条,导入成功0条/失败15条","批量导入用户-管理平台-内部用户证件类型港澳通行证异常校验",$status1,"管理平台-内部用户证件类型港澳通行证异常校验","importInsideUser/licenseNumber/macao/管理平台-内部用户证件类型港澳通行证异常校验.xlsx"]
        - [ "内部用户导入证件类型台湾通行证异常校验","共14条,导入成功0条/失败14条","批量导入用户-管理平台-内部用户导入证件类型台湾通行证异常校验",$status1,"管理平台-内部用户导入证件类型台湾通行证异常校验","importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证异常校验.xlsx" ]

        - [ "内部用户导入证件类型护照长异常校验","共13条,导入成功0条/失败13条","批量导入用户-管理平台-内部用户导入证件类型护照长异常校验",$status1,"管理平台-内部用户导入证件类型护照长异常校验","importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照长异常校验.xlsx" ]


#       title1-message1-code1-filename1-fileType1-filePath1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#          - ["所属组织账号未填写","所属组织账号 未填写",200,"管理平台-内部用户导入所属组织帐号未填写","multipart/form-data;","data/importInsideUser/organizationNumber/管理平台-内部用户导入所属组织帐号未填写.xlsx","","","","","1000",""]
#          - ["所属组织账号长度大于50字","所属组织账号 输入长度应为1-50字",200,"管理平台-内部用户导入所属组织帐号超长","multipart/form-data;","data/importInsideUser/organizationNumber/管理平台-内部用户导入所属组织帐号超长.xlsx","","","","","1000",""]
#          - ["所属组织账号长度小于1字","所属组织账号 未填写",200,"管理平台-内部用户导入所属组织帐号过短","multipart/form-data;","data/importInsideUser/organizationNumber/管理平台-内部用户导入所属组织帐号过短.xlsx","","","","","1000",""]
#          - ["所属组织帐号不存在","所属组织账号不存在",200,"管理平台-内部用户导入所属组织帐号不存在","multipart/form-data;","data/importInsideUser/organizationNumber/管理平台-内部用户导入所属组织帐号不存在.xlsx","","","","","1000","" ]
#          - ["所属组织不是内部组织","所属组织账号不是内部组织",200,"管理平台-内部用户导入所属组织不是内部组织","multipart/form-data;","data/importInsideUser/organizationNumber/管理平台-内部用户导入所属组织不是内部组织.xlsx","","","","","1000","" ]
#          #
#          - ["用户姓名未填写","用户姓名 未填写",200,"管理平台-内部用户导入用户姓名未填写","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名未填写.xlsx","","","","","1000",""]
#          - ["用户姓名长度大于26字","用户姓名 输入长度应为2-26字",200,"管理平台-内部用户导入用户姓名过长","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名过长.xlsx","","","","","1000",""]
#          - ["用户姓名长度小于2字","用户姓名 输入长度应为2-26字",200,"管理平台-内部用户导入用户姓名过短","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名过短.xlsx","","","","","1000",""]
#          - ["用户姓名包含特殊字符:","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符一","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符一.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符/","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符二","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符二.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符\\","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符三","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符三.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符<","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符四","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符四.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符>","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符五","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符五.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符*","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符六","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符六.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符?","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符七","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符七.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符|","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符八","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符八.xlsx","","","","","1000","" ]
#          - ["用户姓名包含特殊字符\"","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含特殊字符九","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含特殊字符九.xlsx","","","","","1000","" ]
#          - ["用户姓名包含数字","用户姓名格式错误",200,"管理平台-内部用户导入用户姓名包含数字","multipart/form-data;","data/importInsideUser/userName/管理平台-内部用户导入用户姓名包含数字.xlsx","","","","","1000","" ]
#          #
#          - ["用户账号未填写","用户账号 未填写",200,"管理平台-内部用户导入用户账号未填写","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号未填写.xlsx","","","","","1000","" ]
#          - ["用户账号长度大于30字","用户账号 输入长度应为2-30字",200,"管理平台-内部用户导入用户账号超长","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号超长.xlsx","","","","","1000","" ]
#          - ["用户账号长度小于2字","用户账号 输入长度应为2-30字",200,"管理平台-内部用户导入用户账号过短","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号过短.xlsx","","","","","1000","" ]
#          - ["Excel中有重复帐号","账号已存在",200,"管理平台-内部用户导入有重复帐号","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入有重复帐号.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符:","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符一","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符一.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符/","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符二","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符二.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符\\","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符三","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符三.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符<","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符四","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符四.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符>","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符五","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符五.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符*","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符六","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符六.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符?","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符七","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符七.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符|","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符八","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符八.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符\"","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符九","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符九.xlsx","","","","","1000","" ]
##          - ["用户账号包含特殊字符空格","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符空格","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符空格.xlsx","","","","","1000","" ]
#          - ["用户账号包含特殊字符中文","用户账号格式错误",200,"管理平台-内部用户导入用户账号包含特殊字符中文","multipart/form-data;","data/importInsideUser/accountNumber/管理平台-内部用户导入用户账号包含特殊字符中文.xlsx","","","","","1000","" ]
#
#          #
##          - ["证件类型不支持","证件类型未填写",200,"管理平台-内部用户导入用户证件类型不支持","multipart/form-data;","data/importInsideUser/licenseType/管理平台-内部用户导入用户证件类型不支持.xlsx","","","","","1000","" ]
#          #
#
##          - ["手机号码长度10字","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码长度10字","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码长度10字.xlsx","","","","","1000","" ]
##          - ["手机号码长度12字","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码长度12字","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码长度12字.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符:","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符一","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符一.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符/","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符二","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符二.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符\\","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符三","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符三.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符*","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符四","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符四.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符?","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符五","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符五.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符<","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符六","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符六.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符>","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符七","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符七.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符|","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符八","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符八.xlsx","","","","","1000","" ]
##          - ["手机号码含特殊字符\"","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含特殊字符九","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含特殊字符九.xlsx","","","","","1000","" ]
#          - ["手机号码含中文","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含中文","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含中文.xlsx","","","","","1000","" ]
#          - ["手机号码含空格","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含空格","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含空格.xlsx","","","","","1000","" ]
#          - ["手机号码含英文","用户手机格式错误",200,"管理平台-内部用户导入用户手机号码含英文","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入用户手机号码含英文.xlsx","","","","","1000","" ]
#          - ["Excel中有重复用户手机","用户手机已存在",200,"管理平台-内部用户导入有重复手机号","multipart/form-data;","data/importInsideUser/userMobile/管理平台-内部用户导入有重复手机号.xlsx","","","","","1000","" ]
#          #
#
#          - ["用户邮箱过长","用户邮箱 输入长度应为1-50字",200,"管理平台-内部用户导入用户邮箱过长","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱过长.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符:_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符一_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符一_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符:_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符一_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符一_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符/_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符二_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符二_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符/_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符二_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符二_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符\\_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符三_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符三_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符\\_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符三_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符三_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符*_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符四_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符四_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符*_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符四_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符四_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符?_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符五_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符五_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符?_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符五_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符五_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符<_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符六_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符六_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符<_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符六_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符六_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符>_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符七_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符七_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符>_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符七_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符七_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符|_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符八_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符八_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符|_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符八_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符八_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符_1\"","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符九_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符九_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符_2\"","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符九_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符九_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符中文_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符中文_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符中文_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符中文_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符中文_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符中文_2.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符空格_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符空格_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符空格_1.xlsx","","","","","1000","" ]
#          - ["邮箱含特殊字符空格_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱含特殊字符空格_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱含特殊字符空格_2.xlsx","","","","","1000","" ]
#          - ["邮箱格式错误_1","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱格式错误_1","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱格式错误_1.xlsx","","","","","1000","" ]
#          - ["邮箱格式错误_2","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱格式错误_2","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱格式错误_2.xlsx","","","","","1000","" ]
#          - ["邮箱格式错误_3","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱格式错误_3","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱格式错误_3.xlsx","","","","","1000","" ]
#          - ["邮箱格式错误_4","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱格式错误_4","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱格式错误_4.xlsx","","","","","1000","" ]
#          - ["邮箱格式错误_5","用户邮箱格式错误",200,"管理平台-内部用户导入用户邮箱格式错误_5","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入用户邮箱格式错误_5.xlsx","","","","","1000","" ]
#          - ["Excel中有重复邮箱","用户邮箱已存在",200,"管理平台-内部用户导入有重复邮箱","multipart/form-data;","data/importInsideUser/userEmail/管理平台-内部用户导入有重复邮箱.xlsx","","","","","1000","" ]
#          #
#          - ["Excel中有重复证件号码","证件号码格式错误",200,"管理平台-内部用户导入有重复证件号码","multipart/form-data;","data/importInsideUser/licenseNumber/管理平台-内部用户导入有重复证件号码.xlsx","","","","","1000","" ]
#          #
##          - ["银行卡号含特殊字符:","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符一","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符一.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符/","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符二","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符二.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符\\","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符三","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符三.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符*","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符四","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符四.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符?","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符五","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符五.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符<","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符六","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符六.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符>","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符七","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符七.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符|","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符八","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符八.xlsx","","","","","1000","" ]
##          - ["银行卡号含特殊字符\"","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含特殊字符九","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含特殊字符九.xlsx","","","","","1000","" ]
#          - ["银行卡号含特殊字符中文","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含中文","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含中文.xlsx","","","","","1000","" ]
#          - ["银行卡号含特殊字符空格","银行卡号不正确",200,"管理平台-内部用户导入银行卡号含空格","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号含空格.xlsx","","","","","1000","" ]
#          - ["银行卡号长度小于15","银行卡号不正确",200,"管理平台-内部用户导入银行卡号长度小于15","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号长度小于15.xlsx","","","","","1000","" ]
#          - ["银行卡号长度大于19","银行卡号不正确",200,"管理平台-内部用户导入银行卡号长度大于19","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入银行卡号长度大于19.xlsx","","","","","1000","" ]
#          - ["Excel中有重复银行卡号","证件号码格式错误",200,"管理平台-内部用户导入有重复银行卡号","multipart/form-data;","data/importInsideUser/bankCardNo/管理平台-内部用户导入有重复银行卡号.xlsx","","","","","1000","" ]
#
#          - ["Excel中有重复钉钉","用户钉钉已存在",200,"管理平台-内部用户导入有重复钉钉","multipart/form-data;","data/importInsideUser/userDingTalk/管理平台-内部用户导入有重复钉钉.xlsx","","","","","1000","" ]
#
#          - ["Excel中有重复企业微信","用户企微已存在",200,"管理平台-内部用户导入有重复企业微信","multipart/form-data;","data/importInsideUser/userWorkWechat/管理平台-内部用户导入有重复企业微信.xlsx","","","","","1000","" ]
#
#          - ["Excel中有重复微信","用户微信已存在",200,"管理平台-内部用户导入有重复微信","multipart/form-data;","data/importInsideUser/userWechat/管理平台-内部用户导入有重复微信.xlsx","","","","","1000","" ]
#
#          - ["Excel中有重复飞书","用户飞书已存在",200,"管理平台-内部用户导入有重复飞书","multipart/form-data;","data/importInsideUser/userFeiShu/管理平台-内部用户导入有重复飞书.xlsx","","","","","1000","" ]
#
#          - ["证件类型身份证含特殊字符:","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符一","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符一.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符/","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符二","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符二.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符\\","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符三","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符三.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符*","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符四","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符四.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符?","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符五","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符五.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符<","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符六","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符六.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符>","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符七","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符七.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符|","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符八","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符八.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符\"","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符九","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符九.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符中文","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符中文","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符中文.xlsx","","","","","1000","" ]
#          - ["证件类型身份证含特殊字符空格","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证含特殊字符空格","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证含特殊字符空格.xlsx","","","","","1000","" ]
#          - ["证件类型身份证不符合规则","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证不符合规则","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证不符合规则.xlsx","","","","","1000","" ]
#          - ["证件类型身份证长度大于18","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证长度过长","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证长度过长.xlsx","","","","","1000","" ]
#          - ["证件类型身份证长度小于18","证件号码格式错误",200,"管理平台-内部用户导入证件类型身份证长度过短","multipart/form-data;","data/importInsideUser/licenseNumber/idCard/管理平台-内部用户导入证件类型身份证长度过短.xlsx","","","","","1000","" ]
#
##          - ["证件类型港澳通行证含特殊字符:","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符一","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符一.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符/","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符二","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符二.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符\\","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符三","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符三.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符*","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符四","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符四.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符?","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符五","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符五.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符<","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符六","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符六.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符>","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符七","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符七.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符|","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符八","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符八.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符\"","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符九","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符九.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符中文","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符中文","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符中文.xlsx","","","","","1000","" ]
##          - ["证件类型港澳通行证含特殊字符空格","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证含特殊字符空格","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证含特殊字符空格.xlsx","","","","","1000","" ]
#          - ["证件类型港澳通行证不符合规则","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证不符合规则","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证不符合规则.xlsx","","","","","1000","" ]
#          - ["证件类型港澳通行证长度7","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证长度7","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证长度7.xlsx","","","","","1000","" ]
#          - ["证件类型港澳通行证长度9","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证长度9","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证长度9.xlsx","","","","","1000","" ]
#          - ["证件类型港澳通行证长度11","证件号码格式错误",200,"管理平台-内部用户导入证件类型港澳通行证长度11","multipart/form-data;","data/importInsideUser/licenseNumber/macao/管理平台-内部用户导入证件类型港澳通行证长度11.xlsx","","","","","1000","" ]
#
##          - ["证件类型台湾通行证含特殊字符:","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符一","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符一.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符/","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符二","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符二.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符\\","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符三","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符三.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符*","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符四","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符四.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符?","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符五","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符五.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符<","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符六","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符六.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符>","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符七","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符七.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符|","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符八","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符八.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符\"","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符九","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符九.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符中文","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符中文","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符中文.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符英文","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符英文","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符英文.xlsx","","","","","1000","" ]
##          - ["证件类型台湾通行证含特殊字符空格","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证含特殊字符空格","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证含特殊字符空格.xlsx","","","","","1000","" ]
#          - ["证件类型台湾通行证长度7","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证长度7","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证长度7.xlsx","","","","","1000","" ]
#          - ["证件类型台湾通行证长度9","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证长度9","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证长度9.xlsx","","","","","1000","" ]
#          - ["证件类型台湾通行证长度11","证件号码格式错误",200,"管理平台-内部用户导入证件类型台湾通行证长度11","multipart/form-data;","data/importInsideUser/licenseNumber/taiwan/管理平台-内部用户导入证件类型台湾通行证长度11.xlsx","","","","","1000","" ]
#
##          - ["证件类型护照含特殊字符:","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符一","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符一.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符/","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符二","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符二.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符\\","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符三","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符三.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符*","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符四","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符四.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符?","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符五","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符五.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符<","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符六","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符六.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符>","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符七","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符七.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符|","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符八","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符八.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符\"","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符九","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符九.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符中文","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符中文","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符中文.xlsx","","","","","1000","" ]
##          - ["证件类型护照含特殊字符空格","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照含特殊字符空格","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照含特殊字符空格.xlsx","","","","","1000","" ]
#          - ["证件类型护照长度4","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照长度4","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照长度4.xlsx","","","","","1000","" ]
#          - ["证件类型护照长度21","证件号码格式错误",200,"管理平台-内部用户导入证件类型护照长度21","multipart/form-data;","data/importInsideUser/licenseNumber/passport/管理平台-内部用户导入证件类型护照长度21.xlsx","","","","","1000","" ]

-
    name: 删除内部组织
    testcase: testcases/manage/OrgUser/user/saveUser/teardown_delete.yml
    parameters:
      insOrgCode:
          - [ $setup_organizationCode]


-  name: 导入内部用户Excel-证件类型为其他
   testcase: testcases/manage/OrgUser/user/importInsideUserExcel/tc_importInsideUserExcel3.yml
