config:
    name: "获取当前登录用户信息"
    variables:
      sys_role_code: SUB_SYS_ADMIN
      #      # 系统管理员 角色id
      sys_ternary_id: ${get_ternary_distribution_id($sys_role_code)}

testcases:

-
    name: 自动化测试获取当前登录用户信息字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserByLoginCode/tc_getUserByLoginCode1.yml
    parameters:
       title1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["接口成功返回","成功",200,"","","","","1000",""]

#-  注:开启该用例需要邮件支持,创建用户时的邮箱,和getpassword()的邮箱相同
#    name: 自动化测试获取当前登录用户信息场景校验-$title1
#    testcase: testcases/manage/OrgUser/user/getUserByLoginCode/tc_getUserByLoginCode2.yml
#    parameters:
#      title1-message1-code1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#        - ["返回当前登录用户信息","成功",200,"醉生当前登录用户信息场景","","","","","1000",""]