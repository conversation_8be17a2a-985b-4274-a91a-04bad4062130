config:
    name: "通过用户主键Id获取用户对象-字段校验"

testcases:
-
    name: 自动化测试通过用户主键Id获取用户对象字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserById/tc_getUserById1.yml
    parameters:
       title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["参数id不填","","参数id必填!",913,"","","","","1000",""]
         - ["接口成功返回","12341856456","成功",200,"","","","","1000",""]


-
    name: 自动化测试通过用户主键Id获取用户对象场景校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserById/tc_getUserById2.yml
    parameters:
        title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["通过主键ID获取新建的用户","","成功",200,"","","","","1000","" ]