config:
    name: "通过组织id获取用户列表分页"

testcases:

-
  name: 用户列表分页查询
  testcase: testcases/manage/OrgUser/user/getUserByOrganization/tc_getUserByOrganizationTC.yml

-
    name: 自动化测试通过组织id获取用户列表分页字段校验-$title1
    testcase: testcases/manage/OrgUser/user/getUserByOrganization/tc_getUserByOrganization1.yml
    parameters:
       title1-message1-code1-id1-organizationId1-userName1-userStatusList1-allChildOrganizationFlag1-currPage1-pageSize1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["所属组织ID不传","所属组织id不能为空",400,"","","",[],true,1,5,"","","","","1000",""]
          - ["所属组织ID传空","所属组织id不能为空",400,"",null,"",[],true,1,5,"","","","","1000",""]
          - ["每页记录数为null","每页记录数不能为空",400,"","1231456","",[],true,1,null,"","","","","1000",""]
          - ["每页记录数小于1","每页记录数不能小于1",400,"","1231456","",[],true,1,0,"","","","","1000",""]
          - ["每页记录数大于100","每页记录数不能大于100",400,"","1231456","",[],true,1,101,"","","","","1000",""]
          - ["当前页号为null","当前页数不能为空",400,"","1231456","",[],true,null,5,"","","","","1000",""]
          - ["当前页号小于1","当前页号不能小于1",400,"","1231456","",[],true,0,5,"","","","","1000",""]
          - ["当前页号大于10000000","当前页号不能大于10000000",400,"","1231456","",[],true,10000001,5,"","","","","1000",""]

#-
#   name: 自动化测试通过组织id获取用户列表分页场景校验-$title1
#   testcase: testcases/manage/OrgUser/user/getUserByOrganization/tc_getUserByOrganization2.yml
#   parameters:
#     title1-message1-code1-id1-organizationId1-userName1-userStatusList1-allChildOrganizationFlag1-currPage1-pageSize1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#       - ["新增组织ID能够返回新增用户","成功",200,"","","醉生通过组织id获取用户列表分页用户Admin",[],true,1,5,"","","","","1000",""]
#

