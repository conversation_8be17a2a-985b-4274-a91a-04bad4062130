config:
    name: "[内]01-组织用户-组织管理-保存组织测试用例集"
    variables:
        lic_str1: "9100000001658972JX"
        sp: " "
        lic_encrypt: "${encrypt($lic_str1)}"

testcases:

-   name: 自动化测试保存组织场景校验
    testcase: testcases/manage/OrgUser/org/saveOrganization/tc_save_organization_2.yml

-   name: 自动化测试保存组织字段校验-$title1
    testcase: testcases/manage/OrgUser/org/saveOrganization/tc_save_organization_1.yml
    parameters:
        title1-accountNumber1-organizationName1-organizationStatus1-organizationTerritory1-organizationType1-parentOrganizationCode1-licenseNumber1-licenseType1-contactAddress1-dingTalk1-feiShu1-workWechat1-orderNum1-status1-message1:
            - ["账号不传",null,"自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"账号不能为空"]
            - ["账号传空字符串","","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"账号支持输入2-54字,账号不能为空"]
            - ["账号长度小于2字符","a","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"账号支持输入2-54字"]
            - ["账号长度大于54字符","accountNumber_accountNumber_accountNumber_accountNumber","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"账号支持输入2-54字"]
            - ["账号特殊字符校验\\_1","accountNumber\\","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验/_2","accountNumber/","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验:_3","accountNumber:","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验*_4","accountNumber*","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验?_5","accountNumber?","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验|_6","accountNumber|","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验\"_7","accountNumber\"","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验空格_8","accountNumb er","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验中文_9","accountNumber中文","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验<_10","a<<<","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["账号特殊字符校验>_11",">>>b","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111234,"账号不能包含以下特殊符号：\\/:*?\"<>|"]

            - ["组织名称不传","accountNumber${get_snowflake()}",null,"1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称不能为空"]
            - ["组织名称传空字符串","accountNumber${get_snowflake()}","","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称不能为空,组织名称支持输入2-100字"]
            - ["组织名称长度小于2字符","accountNumber${get_snowflake()}","自","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称支持输入2-100字"]
            - ["组织名称长度大于100字符","accountNumber${get_snowflake()}","${generate_random_str(101)}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称支持输入2-100字"]
#            - ["组织名称特殊字符校验\\_1","accountNumber${get_snowflake()}","自动化测试组织名称\\","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111232,"组织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验/_2","accountNumber${get_snowflake()}","自动化测试组织名称/","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验:_3","accountNumber${get_snowflake()}","自动化测试组织名称:","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验*_4","accountNumber${get_snowflake()}","自动化测试组织名称*","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验?_5","accountNumber${get_snowflake()}","自动化测试组织名称?","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验|_6","accountNumber${get_snowflake()}","自动化测试组织名称|","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验\"_7","accountNumber${get_snowflake()}","自动化测试组织名称\"","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"织名称不能包含以下特殊符号：\\/:*?\"<>|"]
##            - ["组织名称特殊字符校验空格_8","accountNumber${get_snowflake()}","自动化测试组织名 称","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111232,"组织名称不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["组织名称特殊字符校验<_9","accountNumber${get_snowflake()}","自<<<","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称支持输入2-50字"]
#            - ["组织名称特殊字符校验>_10","accountNumber${get_snowflake()}",">>>称","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织名称支持输入2-50字"]

            - ["状态不传","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}",null,"1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"状态不能为空"]
            - ["状态传空字符串","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"状态不能为空,填写的状态不支持"]
            - ["状态传1,2,3以外的值","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","4","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"填写的状态不支持"]

            - ["组织类型不传","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1",null,"1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织类型不能为空"]
            - ["组织类型传空字符串","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"组织类型不能为空,填写的组织类型不支持"]
            - ["组织类型传1,2以外的值","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","3","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"填写的组织类型不支持"]

            - ["分类不传","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1",null,"0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"分类不能为空"]
            - ["分类传空字符串","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"分类不能为空,填写的分类不支持"]
            - ["分类传1,2以外的值","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","3","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"填写的分类不支持"]

            - ["上级组织编码不传","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1",null,"$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"上级组织编码不能为空"]
            - ["上级组织编码传空字符串","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"上级组织编码不能为空"]
            - ["上级组织编码长度大于54字符","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","1adbb38642364b0db99743c4dc525b5c1adbb38642364b0db99743a","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"上级组织编码支持输入1-54字"]

            - ["证件类型不传，传证件号","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt",null,"联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111235,"证件号有值时证件类型不能为空"]
            - ["证件类型传空字符串，传证件号","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111235,"证件号有值时证件类型不能为空"]
            - ["证件类型传指定类型之外的值","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","10","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",913,"填写的证件类型不支持"]
            - ["证件号码不加密","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","123456789101112365","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1501003,"请求参数有误,用户信息解密有误"]

#            - ["社会信用代码含特殊字符:_1","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnDYV4zq8DVS3hIIIHNRT6cc=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符/_2","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnBVbny4PzIMYVibVHWe4/3U=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符\\_3","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnND6CPyNN7vwcZ+OaWKmSm8=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符*_4","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnDqC7SedlEQ1ewgHeF7ClFY=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符?_5","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnMuTpE2WxK9ra8F88jQhVUQ=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符<_6","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnAeXUUL+vqieP6ZKtsBGq44=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符>_7","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnJZKhNNaYUzQa1FftMTcZpk=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符|_8","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnAR51UZbDN+9llyXDS8V6Bg=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符\"_9","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","TdeFIojjfKbCWICgcDlKnDlHNxxGCbsiLR3Gp2e0tx0=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符中文_10","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","dli0Ssd9Boy7JfNo/UAc3noXdOu6t8ulir1JFqYi3bU=","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
            - ["社会信用代码含特殊字符空格_11","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","${encrypt($sp$lic_str1$sp)}","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
            - ["社会信用代码长度超过18字符_12","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","${encrypt(123$lic_str1)}","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]

            - ["工商注册号长度小于13字符_13","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","${encrypt(************)}","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
            - ["工商注册号长度等于14字符_14","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","${encrypt(**************)}","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
            - ["工商注册号长度大于15字符_15","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","${encrypt(**************56)}","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符:_16","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","7vHzk6vyTpsQB24vb8bkqA==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符/_17","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","ow72mBeHagBr1pQfz3Zj1g==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符\\_18","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","/o++Qbb7uR7tJv2v2GUXlQ==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符*_19","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","smLb2TjjdKTL4q9v5C2A/g==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符?_20","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","joP/+/epWEt7Exaa0rNC8A==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符<_21","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","5RHZNF6OnSMO32piKe177w==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符>_22","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","j1w/b3Io44tf7N8Hfb3gXg==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符|_23","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","4V8Y4T87emcJfUWG/Sf+3Q==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符\"_24","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","tkCFOOHzNar8IJH9CdEEpQ==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符中文_25","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","RffMY0t+A7ZJTJCS4sFg5g==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号含特殊字符空格_26","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","+i6FZdHRPqz8ATIJMretOA==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]
#            - ["工商注册号不全是数字_27","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","oSBP6kMGGRKSg7r5qCP40Q==","13","联系地址","ding_ding_account","fei_shu_account","wechat_account","100",1111237,"请输入正确的证件号码"]

            - ["联系地址长度大于100字符","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联","ding_ding_account","fei_shu_account","wechat_account","100",913,"联系地址支持输入0-100字"]
            - ["联系地址特殊字符校验\\_1","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址\\","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验/_2","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址/","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验:_3","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址:","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验*_4","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址*","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验?_5","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址?","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验|_6","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址|","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验\"_7","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地址\"","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
            - ["联系地址特殊字符校验空格_8","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","","12","联系地 址","","","","100",1111363,"联系地址不能包含以下特殊符号：\\/:*?\"<>|"]

            - ["企业钉钉长度大于50字符","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account_ding_ding_account_ding_ding_account","fei_shu_account","wechat_account","100",913,"企业钉钉支持输入0-50字"]
            - ["企业飞书长度大于50字符","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account_fei_shu_account_fei_shu_account_fei","wechat_account","100",913,"企业飞书支持输入0-50字"]
            - ["企业微信长度大于50字符","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account_wechat_account_wechat_account_wechat_account","100",913,"企业微信支持输入0-50字"]
            - ["排序传小于1的数","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","0",913,"排序支持输入1-4位数字"]
            - ["排序传大于9999的数","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","10000",913,"排序支持输入1-4位数字"]
            - ["排序传非数字值","accountNumber${get_snowflake()}","自动化测试组织名称${get_snowflake()}","1","1","1","0","$lic_encrypt","12","联系地址","ding_ding_account","fei_shu_account","wechat_account","a",801,"请求格式错误，请重试"]

        #87

-   name: 保存组织-扩展字段校验证书计费项目场景校验(管理平台）
    testcase: testcases/manage/OrgUser/org/saveOrganization/tc_save_organization_3.yml