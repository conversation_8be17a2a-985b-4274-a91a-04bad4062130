config:
    name: "[内]01-组织用户-组织管理-组织信息-导入内部组织Excel用例集"
    variables:
        - baseDir: importOrganizationExcel
        - reason: "共1条,导入成功0条/失败1条"

testcases:

-   name: 自动化测试导入内部组织Excel场景用例
    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_case.yml

-   name: 自动化测试导入内部组织Excel字段校验-$title1
    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_parm_check.yml
    parameters:
        title1-filename1-filePath1-task_reason-task_name-task_status:
            - ["组织名称长度大于50","组织名称长度大于50","$baseDir/organizationName/组织名称长度大于50.xlsx","$reason","批量导入组织-组织名称长度大于50",3]

            - [ "内部组织导入账号校验-异常","内部组织导入账号校验-异常","$baseDir/accountNumber/内部组织导入账号校验-异常.xlsx","共14条,导入成功0条/失败14条","批量导入组织-内部组织导入账号校验-异常",3 ]

            - ["分类传指定类型之外的值","分类传指定类型之外的值","$baseDir/organizationType/分类传指定类型之外的值.xlsx","$reason","批量导入组织-分类传指定类型之外的值",3]

            - [ "内部组织导入上级组织账号校验-异常","内部组织导入上级组织账号校验-异常","$baseDir/parentAccountNumber/内部组织导入上级组织账号校验-异常.xlsx","共13条,导入成功0条/失败13条","批量导入组织-内部组织导入上级组织账号校验-异常",3 ]

            - ["证件类型传指定类型之外的值","证件类型传指定类型之外的值","$baseDir/licenseType/证件类型传指定类型之外的值.xlsx","$reason","批量导入组织-证件类型传指定类型之外的值",3]

            - [ "内部组织导入证件号校验-异常","内部组织导入证件号校验-异常","$baseDir/licenseNumber/内部组织导入证件号校验-异常.xlsx","共28条,导入成功0条/失败28条","批量导入组织-内部组织导入证件号校验-异常",3 ]

            - ["企业微信长度大于50","企业微信长度大于50","$baseDir/workWechat/企业微信长度大于50.xlsx","$reason","批量导入组织-企业微信长度大于50",3]
            - ["企业飞书长度大于50","企业飞书长度大于50","$baseDir/feiShu/企业飞书长度大于50.xlsx","$reason","批量导入组织-企业飞书长度大于50",3]
            - ["企业钉钉长度大于50","企业钉钉长度大于50","$baseDir/dingTalk/企业钉钉长度大于50.xlsx","$reason","批量导入组织-企业钉钉长度大于50",3]

#        title1-filename1-filePath1-organizationName1-accountNumber1-organizationType1-parentAccountNumber1-licenseType1-licenseNumber1-workWechat1-feiShu1-dingTalk1-code1-message1-errorMsg1:
#            - ["组织名称长度小于2","组织名称长度小于2","$baseDir/organizationName/组织名称长度小于2.xlsx","组","account_number","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","组织名称 输入长度应为2-50字"]
#            - ["组织名称长度大于50","组织名称长度大于50","$baseDir/organizationName/组织名称长度大于50.xlsx","组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称","account_number","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","组织名称 输入长度应为2-50字"]
#
#            - ["账号长度小于2","账号长度小于2","$baseDir/accountNumber/账号长度小于2.xlsx","组织名称","a","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 输入长度应为2-54字"]
#            - ["账号长度大于54","账号长度大于54","$baseDir/accountNumber/账号长度大于54.xlsx","组织名称","accountNumber_accountNumber_accountNumber_accountNumber","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 输入长度应为2-54字"]
#            - ["账号包含特殊字符\\_1","账号包含特殊字符1","$baseDir/accountNumber/账号包含特殊字符1.xlsx","组织名称","account_number\\","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符/_2","账号包含特殊字符2","$baseDir/accountNumber/账号包含特殊字符2.xlsx","组织名称","account_number/","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符:_3","账号包含特殊字符3","$baseDir/accountNumber/账号包含特殊字符3.xlsx","组织名称","account_number:","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符*_4","账号包含特殊字符4","$baseDir/accountNumber/账号包含特殊字符4.xlsx","组织名称","account_number*","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符?_5","账号包含特殊字符5","$baseDir/accountNumber/账号包含特殊字符5.xlsx","组织名称","account_number?","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符\"_6","账号包含特殊字符6","$baseDir/accountNumber/账号包含特殊字符6.xlsx","组织名称","account_number\"","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符<_7","账号包含特殊字符7","$baseDir/accountNumber/账号包含特殊字符7.xlsx","组织名称","account_number<","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符>_8","账号包含特殊字符8","$baseDir/accountNumber/账号包含特殊字符8.xlsx","组织名称","account_number>","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符|_9","账号包含特殊字符9","$baseDir/accountNumber/账号包含特殊字符9.xlsx","组织名称","account_number|","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符空格_10","账号包含特殊字符10","$baseDir/accountNumber/账号包含特殊字符10.xlsx","组织名称","account_numbe r","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符中文_11","账号包含特殊字符11","$baseDir/accountNumber/账号包含特殊字符11.xlsx","组织名称","account_number中文","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","账号 格式错误"]
#
#            - ["分类传指定类型之外的值","分类传指定类型之外的值","$baseDir/organizationType/分类传指定类型之外的值.xlsx","组织名称","account_number","3","0","12","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","根节点下不能直接挂部门"]
#
#            - ["上级组织账号长度大于54","上级组织账号长度大于54","$baseDir/parentAccountNumber/上级组织账号长度大于54.xlsx","组织名称","account_number","企业","accountNumber_accountNumber_accountNumber_accountNumber","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 输入长度应为1-54字"]
#            - ["上级组织账号包含特殊字符\\_1","上级组织账号包含特殊字符1","$baseDir/parentAccountNumber/上级组织账号包含特殊字符1.xlsx","组织名称","account_number","企业","parent_account\\","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符/_2","上级组织账号包含特殊字符2","$baseDir/parentAccountNumber/上级组织账号包含特殊字符2.xlsx","组织名称","account_number","企业","parent_account/","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符:_3","上级组织账号包含特殊字符3","$baseDir/parentAccountNumber/上级组织账号包含特殊字符3.xlsx","组织名称","account_number","企业","parent_account:","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符*_4","上级组织账号包含特殊字符4","$baseDir/parentAccountNumber/上级组织账号包含特殊字符4.xlsx","组织名称","account_number","企业","parent_account*","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符?_5","上级组织账号包含特殊字符5","$baseDir/parentAccountNumber/上级组织账号包含特殊字符5.xlsx","组织名称","account_number","企业","parent_account?","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符\"_6","上级组织账号包含特殊字符6","$baseDir/parentAccountNumber/上级组织账号包含特殊字符6.xlsx","组织名称","account_number","企业","parent_account\"","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符<_7","上级组织账号包含特殊字符7","$baseDir/parentAccountNumber/上级组织账号包含特殊字符7.xlsx","组织名称","account_number","企业","parent_account<","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符>_8","上级组织账号包含特殊字符8","$baseDir/parentAccountNumber/上级组织账号包含特殊字符8.xlsx","组织名称","account_number","企业","parent_account>","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符|_9","上级组织账号包含特殊字符9","$baseDir/parentAccountNumber/上级组织账号包含特殊字符9.xlsx","组织名称","account_number","企业","parent_account|","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符空格_10","上级组织账号包含特殊字符10","$baseDir/parentAccountNumber/上级组织账号包含特殊字符10.xlsx","组织名称","account_number","企业","parent_accoun t","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#            - ["上级组织账号包含特殊字符中文_11","上级组织账号包含特殊字符11","$baseDir/parentAccountNumber/上级组织账号包含特殊字符11.xlsx","组织名称","account_number","企业","parent_account中文","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","上级组织账号 不存在"]
#
#            - ["证件类型传指定类型之外的值","证件类型传指定类型之外的值","$baseDir/licenseType/证件类型传指定类型之外的值.xlsx","组织名称","account_number","企业","0","10","*****************3","wei_xin","fei_shu","ding_ding",200,"成功","证件类型不存在"]
#
#            - ["社会信用代码含特殊字符\\_1","社会信用代码含特殊字符1","$baseDir/licenseNumber/社会信用代码含特殊字符1.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************\\","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符/_2","社会信用代码含特殊字符2","$baseDir/licenseNumber/社会信用代码含特殊字符2.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************/","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符:_3","社会信用代码含特殊字符3","$baseDir/licenseNumber/社会信用代码含特殊字符3.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************:","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符*_4","社会信用代码含特殊字符4","$baseDir/licenseNumber/社会信用代码含特殊字符4.xlsx","组织名称","account_number","企业","0","统一社会信用代码","******************","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符?_5","社会信用代码含特殊字符5","$baseDir/licenseNumber/社会信用代码含特殊字符5.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************?","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符\"_6","社会信用代码含特殊字符6","$baseDir/licenseNumber/社会信用代码含特殊字符6.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************\"","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符<_7","社会信用代码含特殊字符7","$baseDir/licenseNumber/社会信用代码含特殊字符7.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************<","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符>_8","社会信用代码含特殊字符8","$baseDir/licenseNumber/社会信用代码含特殊字符8.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************>","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符|_9","社会信用代码含特殊字符9","$baseDir/licenseNumber/社会信用代码含特殊字符9.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************|","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符空格_10","社会信用代码含特殊字符10","$baseDir/licenseNumber/社会信用代码含特殊字符10.xlsx","组织名称","account_number","企业","0","统一社会信用代码","**************** 4","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符中文_11","社会信用代码含特殊字符11","$baseDir/licenseNumber/社会信用代码含特殊字符11.xlsx","组织名称","account_number","企业","0","统一社会信用代码","****************中4","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码长度超过18字符_12","社会信用代码长度超过18字符12","$baseDir/licenseNumber/社会信用代码长度超过18字符12.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************34","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#
#            - ["工商注册号含特殊字符\\_1","工商注册号含特殊字符1","$baseDir/licenseNumber/工商注册号含特殊字符1.xlsx","组织名称","account_number","企业","0","13","**************\\","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符/_2","工商注册号含特殊字符2","$baseDir/licenseNumber/工商注册号含特殊字符2.xlsx","组织名称","account_number","企业","0","13","**************/","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符:_3","工商注册号含特殊字符3","$baseDir/licenseNumber/工商注册号含特殊字符3.xlsx","组织名称","account_number","企业","0","13","**************:","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符*_4","工商注册号含特殊字符4","$baseDir/licenseNumber/工商注册号含特殊字符4.xlsx","组织名称","account_number","企业","0","13","***************","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符?_5","工商注册号含特殊字符5","$baseDir/licenseNumber/工商注册号含特殊字符5.xlsx","组织名称","account_number","企业","0","13","**************?","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符\"_6","工商注册号含特殊字符6","$baseDir/licenseNumber/工商注册号含特殊字符6.xlsx","组织名称","account_number","企业","0","13","**************\"","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符<_7","工商注册号含特殊字符7","$baseDir/licenseNumber/工商注册号含特殊字符7.xlsx","组织名称","account_number","企业","0","13","**************<","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符>_8","工商注册号含特殊字符8","$baseDir/licenseNumber/工商注册号含特殊字符8.xlsx","组织名称","account_number","企业","0","13","**************>","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符|_9","工商注册号含特殊字符9","$baseDir/licenseNumber/工商注册号含特殊字符9.xlsx","组织名称","account_number","企业","0","13","**************|","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符空格_10","工商注册号含特殊字符10","$baseDir/licenseNumber/工商注册号含特殊字符10.xlsx","组织名称","account_number","企业","0","13","************* 4","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符中文_11","工商注册号含特殊字符11","$baseDir/licenseNumber/工商注册号含特殊字符11.xlsx","组织名称","account_number","企业","0","13","**************中","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度小于13字符_12","工商注册号长度小于13字符12","$baseDir/licenseNumber/工商注册号长度小于13字符12.xlsx","组织名称","account_number","企业","0","13","************","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度等于14字符_13","工商注册号长度等于14字符13","$baseDir/licenseNumber/工商注册号长度等于14字符13.xlsx","组织名称","account_number","企业","0","13","**************","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度大于15字符_14","工商注册号长度大于15字符14","$baseDir/licenseNumber/工商注册号长度大于15字符14.xlsx","组织名称","account_number","企业","0","13","****************","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号不全是数字_15","工商注册号不全是数字15","$baseDir/licenseNumber/工商注册号不全是数字15.xlsx","组织名称","account_number","企业","0","13","**************a","wei_xin","fei_shu","ding_ding",200,"成功","请输入正确的证件号码"]
#
#            - ["企业微信长度大于50","企业微信长度大于50","$baseDir/workWechat/企业微信长度大于50.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************3","wei_xin_wei_xin_wei_xin_wei_xin_wei_xin_wei_xin_wei_xin","fei_shu","ding_ding",200,"成功","企业微信 输入长度应为0-50字"]
#            - ["企业飞书长度大于50","企业飞书长度大于50","$baseDir/feiShu/企业飞书长度大于50.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu_fei_shu_fei_shu_fei_shu_fei_shu_fei_shu_fei_shu","ding_ding",200,"成功","企业飞书 输入长度应为0-50字"]
#            - ["企业钉钉长度大于50","企业钉钉长度大于50","$baseDir/dingTalk/企业钉钉长度大于50.xlsx","组织名称","account_number","企业","0","统一社会信用代码","*****************3","wei_xin","fei_shu","ding_ding_ding_ding_ding_ding_ding_ding_ding_ding_ding_ding",200,"成功","企业钉钉 输入长度应为0-50字"]

-   name: 自动化测试导入内部组织Excel字段校验-特殊场景
    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_parm_check2.yml

#-   name: 自动化测试导入内部组织Excel字段校验-组织名称包含特殊字符放开
#    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_parm_check3.yml

-   name: 导入内部组织Excel测试用例-证件类型为其他
    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_type_other.yml

-   name: 自动化测试导入内部组织Excel字段校验-证书计费项目ID字段校验
    testcase: testcases/manage/OrgUser/org/importOrganizationExcel/tc_import_org_parm_check4.yml
