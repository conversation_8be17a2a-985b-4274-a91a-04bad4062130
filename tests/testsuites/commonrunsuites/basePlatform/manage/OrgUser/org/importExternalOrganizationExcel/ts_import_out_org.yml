config:
    name: "[内]01-组织用户-组织管理-组织信息-导入外部组织Excel用例集"

testcases:

-   name: 自动化测试导入外部组织Excel场景用例
    testcase: testcases/manage/OrgUser/org/importExternalOrganizationExcel/tc_import_out_org_case.yml

-   name: 自动化测试导入外部组织Excel字段校验-$title1
    testcase: testcases/manage/OrgUser/org/importExternalOrganizationExcel/tc_import_out_org_parm_check.yml
    parameters:
            title1-task_status-task_reason-task_name-filename1-fileType1-filePath1:
                - [ "导入组织名称异常校验",3,"共3条,导入成功0条/失败3条","外部组织导入组织名称校验-异常","外部组织导入组织名称校验-异常","multipart/form-data;","data/importOutsideOrganization/organizationName/外部组织导入组织名称校验-异常.xlsx" ]
                - [ "导入组织账号异常校验",3,"共14条,导入成功0条/失败14条","外部组织导入账号校验-异常","外部组织导入账号校验-异常","multipart/form-data;","data/importOutsideOrganization/accountNumber/外部组织导入账号校验-异常.xlsx" ]
                - [ "导入组织联系地址异常校验",3,"共11条,导入成功0条/失败11条","外部组织导入联系地址校验-异常","外部组织导入联系地址校验-异常","multipart/form-data;","data/importOutsideOrganization/contactAddress/外部组织导入联系地址校验-异常.xlsx" ]
                - [ "导入组织证件类型异常校验",3,"共2条,导入成功0条/失败2条","外部组织导入证件类型校验-异常","外部组织导入证件类型校验-异常","multipart/form-data;","data/importOutsideOrganization/licenseType/外部组织导入证件类型校验-异常.xlsx" ]
                - [ "导入组织工商注册号异常校验",3,"共15条,导入成功0条/失败15条","外部组织导入工商注册号校验-异常","外部组织导入工商注册号校验-异常","multipart/form-data;","data/importOutsideOrganization/licenseNumber/外部组织导入工商注册号校验-异常.xlsx" ]
                - [ "导入组织社会信用代码异常校验",3,"共12条,导入成功0条/失败12条","外部组织导入社会信用代码校验-异常","外部组织导入社会信用代码校验-异常","multipart/form-data;","data/importOutsideOrganization/licenseNumber/外部组织导入社会信用代码校验-异常.xlsx" ]

#
#        title1-filename1-filePath1-organizationName1-accountNumber1-licenseType1-licenseNumber1-contactAddress1-code1-message1-errorMsg1:
#            - ["组织名称长度小于2","组织名称长度小于2","data/importExternalOrganizationExcel/organizationName/组织名称长度小于2.xlsx","组","account_number","统一社会信用代码","****************43","联系地址",200,"成功","组织名称 输入长度应为2-50字"]
#            - ["组织名称长度大于50","组织名称长度大于50","data/importExternalOrganizationExcel/organizationName/组织名称长度大于50.xlsx","组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称组织名称","account_number","统一社会信用代码","****************43","联系地址",200,"成功","组织名称 输入长度应为2-50字"]
#
#            - ["账号长度小于2","账号长度小于2","data/importExternalOrganizationExcel/accountNumber/账号长度小于2.xlsx","组织名称","a","统一社会信用代码","****************43","联系地址",200,"成功","账号 输入长度应为2-54字"]
#            - ["账号长度大于54","账号长度大于54","data/importExternalOrganizationExcel/accountNumber/账号长度大于54.xlsx","组织名称","accountNumber_accountNumber_accountNumber_accountNumber","统一社会信用代码","****************43","联系地址",200,"成功","账号 输入长度应为2-54字"]
#            - ["账号包含特殊字符\\_1","账号包含特殊字符1","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符1.xlsx","组织名称","account_number\\","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符/_2","账号包含特殊字符2","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符2.xlsx","组织名称","account_number/","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符:_3","账号包含特殊字符3","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符3.xlsx","组织名称","account_number:","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符*_4","账号包含特殊字符4","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符4.xlsx","组织名称","account_number*","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符?_5","账号包含特殊字符5","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符5.xlsx","组织名称","account_number?","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符\"_6","账号包含特殊字符6","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符6.xlsx","组织名称","account_number\"","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符<_7","账号包含特殊字符7","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符7.xlsx","组织名称","account_number<","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符>_8","账号包含特殊字符8","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符8.xlsx","组织名称","account_number>","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符|_9","账号包含特殊字符9","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符9.xlsx","组织名称","account_number|","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符空格_10","账号包含特殊字符10","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符10.xlsx","组织名称","account_numbe r","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#            - ["账号包含特殊字符中文_11","账号包含特殊字符11","data/importExternalOrganizationExcel/accountNumber/账号包含特殊字符11.xlsx","组织名称","account_number中文","统一社会信用代码","****************43","联系地址",200,"成功","账号 格式错误"]
#
#            - ["证件类型传指定类型之外的值","证件类型传指定类型之外的值","data/importExternalOrganizationExcel/licenseType/证件类型传指定类型之外的值.xlsx","组织名称","account_number","10","****************43","联系地址",200,"成功","证件类型不存在"]
#
#            - ["社会信用代码含特殊字符\\_1","社会信用代码含特殊字符1","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符1.xlsx","组织名称","account_number","12","****************4\\","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符/_2","社会信用代码含特殊字符2","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符2.xlsx","组织名称","account_number","12","****************4/","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符:_3","社会信用代码含特殊字符3","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符3.xlsx","组织名称","account_number","12","****************4:","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符*_4","社会信用代码含特殊字符4","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符4.xlsx","组织名称","account_number","12","****************4*","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符?_5","社会信用代码含特殊字符5","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符5.xlsx","组织名称","account_number","12","****************4?","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符\"_6","社会信用代码含特殊字符6","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符6.xlsx","组织名称","account_number","12","****************4\"","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符<_7","社会信用代码含特殊字符7","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符7.xlsx","组织名称","account_number","12","****************4<","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符>_8","社会信用代码含特殊字符8","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符8.xlsx","组织名称","account_number","12","****************4>","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符|_9","社会信用代码含特殊字符9","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符9.xlsx","组织名称","account_number","12","****************4|","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符空格_10","社会信用代码含特殊字符10","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符10.xlsx","组织名称","account_number","12","**************** 4","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码含特殊字符中文_11","社会信用代码含特殊字符11","data/importExternalOrganizationExcel/licenseNumber/社会信用代码含特殊字符11.xlsx","组织名称","account_number","12","****************中4","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["社会信用代码长度超过18字符_12","社会信用代码长度超过18字符12","data/importExternalOrganizationExcel/licenseNumber/社会信用代码长度超过18字符12.xlsx","组织名称","account_number","统一社会信用代码","****************434","联系地址",200,"成功","请输入正确的证件号码"]
#
#            - ["工商注册号含特殊字符\\_1","工商注册号含特殊字符1","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符1.xlsx","组织名称","account_number","13","**************\\","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符/_2","工商注册号含特殊字符2","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符2.xlsx","组织名称","account_number","13","**************/","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符:_3","工商注册号含特殊字符3","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符3.xlsx","组织名称","account_number","13","**************:","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符*_4","工商注册号含特殊字符4","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符4.xlsx","组织名称","account_number","13","***************","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符?_5","工商注册号含特殊字符5","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符5.xlsx","组织名称","account_number","13","**************?","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符\"_6","工商注册号含特殊字符6","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符6.xlsx","组织名称","account_number","13","**************\"","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符<_7","工商注册号含特殊字符7","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符7.xlsx","组织名称","account_number","13","**************<","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符>_8","工商注册号含特殊字符8","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符8.xlsx","组织名称","account_number","13","**************>","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符|_9","工商注册号含特殊字符9","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符9.xlsx","组织名称","account_number","13","**************|","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符空格_10","工商注册号含特殊字符10","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符10.xlsx","组织名称","account_number","13","************* 4","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号含特殊字符中文_11","工商注册号含特殊字符11","data/importExternalOrganizationExcel/licenseNumber/工商注册号含特殊字符11.xlsx","组织名称","account_number","13","**************中","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度小于13字符_12","工商注册号长度小于13字符12","data/importExternalOrganizationExcel/licenseNumber/工商注册号长度小于13字符12.xlsx","组织名称","account_number","13","************","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度等于14字符_13","工商注册号长度等于14字符13","data/importExternalOrganizationExcel/licenseNumber/工商注册号长度等于14字符13.xlsx","组织名称","account_number","13","**************","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号长度大于15字符_14","工商注册号长度大于15字符14","data/importExternalOrganizationExcel/licenseNumber/工商注册号长度大于15字符14.xlsx","组织名称","account_number","13","****************","联系地址",200,"成功","请输入正确的证件号码"]
#            - ["工商注册号不全是数字_16","工商注册号不全是数字16","data/importExternalOrganizationExcel/licenseNumber/工商注册号不全是数字16.xlsx","组织名称","account_number","13","**************a","联系地址",200,"成功","请输入正确的证件号码"]
#
#            - ["联系地址长度大于100字符","联系地址长度大于100字符","data/importExternalOrganizationExcel/contactAddress/联系地址长度大于100字符.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联系地址联",200,"成功","联系地址 输入长度应为0-100字"]
#            - ["联系地址包含特殊字符/_2","联系地址包含特殊字符2","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符2.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址/",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符:_3","联系地址包含特殊字符3","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符3.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址:",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符*_4","联系地址包含特殊字符4","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符4.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址*",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符?_5","联系地址包含特殊字符5","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符5.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址?",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符\"_6","联系地址包含特殊字符6","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符6.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址\"",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符<_7","联系地址包含特殊字符7","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符7.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址<",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符>_8","联系地址包含特殊字符8","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符8.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址>",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符|_9","联系地址包含特殊字符9","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符9.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地址|",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]
#            - ["联系地址包含特殊字符空格_10","联系地址包含特殊字符10","data/importExternalOrganizationExcel/contactAddress/联系地址包含特殊字符10.xlsx","组织名称","account_number","统一社会信用代码","****************43","联系地 址",200,"成功","联系地址联系地址不能包含以下特殊符号：\\/:*?\"<>|"]

-   name: 自动化测试导入外部组织Excel字段校验-特殊场景
    testcase: testcases/manage/OrgUser/org/importExternalOrganizationExcel/tc_import_out_org_parm_check2.yml

#-   name: 自动化测试导入外部组织Excel字段校验-组织名称包含特殊字符放开
#    testcase: testcases/manage/OrgUser/org/importExternalOrganizationExcel/tc_import_out_org_parm_check3.yml