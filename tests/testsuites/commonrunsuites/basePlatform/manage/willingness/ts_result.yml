config:
    name: 查询意愿认证结果
    variables:
      sp: " "
#      sql1: select *from ec_unifiedauth_apply eua where eua.ev_result=1
#      applyId0: ${get_param_by_sql($sql1, id)}
      applyId0: "123"
#      userCode0: ${get_param_by_sql($sql1, user_code)}
      userCode0: "${ENV(sign01.userCode)}"


testcases:
-
    name: 查询意愿认证结果-校验-新增参数
    parameters:
    - name-userCode-applyId-code-message:
#        - ["TC0-正常调用",$userCode0,$applyId0,200,"成功"]
        - ["TC1-applyId传空字符串/不传",$userCode0,"",913,"申请认证id不能为空"]
        - ["TC2-applyId包含特殊字符",$userCode0,"/:*? \" <>|",1111370,"该意愿申请不存在"]
        - ["TC3-applyId包含首尾空格",$userCode0, $sp $applyId0 $sp,1111370,"该意愿申请不存在"]
        - ["TC4-userCode传空字符串/不传","",$applyId0,1111278,"用户账号/用户编码不能全部为空"]
        - ["TC8-userCode包含首尾空格",$sp $userCode0 $sp,$applyId0,1111370,"该意愿申请不存在"]
        - ["TC9-userCode指定用户不存在",$userCode0 123,$applyId0,1111003,"用户已不存在"]



    testcase: testcases/manage/willingness/tc_result.yml


-
    name: 查询意愿认证结果-场景用例

    testcase: testcases/manage/willingness/tc_result2.yml