config:
    name: 查询意愿认证结果
    variables:
      sp: " "


      sql: select * from ec_unifiedauth_apply ea left join ec_user eu on ea.user_code = eu.user_code  where eu.user_territory =1 and eu.deleted =0
      applyId0: ${get_param_by_sql($sql, id)}
      userCode0: ${get_param_by_sql($sql, user_code)}
      redirectUrl0: "${ENV(esign.projectHost)}/main-index-web/home"
      redirectUrl2: "${ENV(esign.projectHost)}/main-index-web/home"
      redirectUrl3000: "https://www.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.com/"
      redirectUrl3001: "https://www.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.com/"
      clientType0: "PC"

testcases:
-
    name: 查询意愿认证结果-校验-新增参数
    parameters:
    - name-userCode-customAccountNo-redirectUrl-clientType-willTypes-excludeWillTypes-code-message:
        - ["TC0-正常调用",null,$userCode0,$redirectUrl0,$clientType0,[],[],200,"成功"]
        - ["TC0-用户账号、编码为空",null,null,$redirectUrl0,$clientType0,[],[],1111278,"用户账号/用户编码不能全部为空"]
        - ["TC1-redirectUrl传空字符串/不传",null,$userCode0,"",$clientType0,[],[],913,"意愿结束后页面跳转地址不能为空"]
        - ["TC2-redirectUrl为3000字符",null,$userCode0,$redirectUrl3000,$clientType0,[],[],200,"成功"]
        - ["TC3-redirectUrl为3001字符",null,$userCode0,$redirectUrl3001,$clientType0,[],[],913,"跳转地址长度必须小于等于3000"]
        - ["TC4-redirectUrl包含首尾空格",null,$userCode0,$sp $redirectUrl0 $sp,$clientType0,[],[],200,"成功"]
        - ["TC5-redirectUrl以http://或https://开头",null,$userCode0,$redirectUrl2,$clientType0,[],[],200,"成功"]
        - ["TC5-redirectUrl以非http://或https://开头",null,$userCode0,"ftp:192.168.0.1",$clientType0,[],[],1112004,"URL格式不正确"]
        - ["TC6-clientType传空字符串/不传",null,$userCode0,$redirectUrl0,"",[],[],1111368,"clientType只能为PC或H5"]
        - ["TC7-clientType为1个字符或1个字符",null,$userCode0,$redirectUrl0,"P",[],[],1111368,"clientType只能为PC或H5"]
        - ["TC8-clientType为3个字符",null,$userCode0,$redirectUrl0,"PC1",[],[],1111368,"clientType只能为PC或H5"]
        - ["TC9-clientType为h5",null,$userCode0,$redirectUrl0,"H5",[],[],200,"成功"]
        - ["TC10-clientType为h5",null,$userCode0,$redirectUrl0,"h5",[],[],200,"成功"]
        - ["TC11-clientType为PC/H5之外的任意2个字符",null,$userCode0,$redirectUrl0,"",[],[],1111368,"clientType只能为PC或H5"]

    testcase: testcases/manage/willingness/tc_getUrl.yml

-
    name: 查询意愿认证结果usercode
    testcase: testcases/manage/willingness/tc_getUrl_2.yml


-
    name: 查询意愿认证结果usercode
    testcase: testcases/manage/willingness/tc_getUrl_3.yml
