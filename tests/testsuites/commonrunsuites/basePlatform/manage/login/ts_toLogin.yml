config:
    name: "登录验证"

testcases:

-
    name: 自动化测试登录验证字段校验-$title1
    testcase: testcases/manage/login/tc_toLogin1.yml
    parameters:
       title1-message1-code1-accountNum1-rememberMe1-password1-verificationCode1-tenantCode2-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
           - ["帐号为空","账号不能为空",913,null,true,"password","DFST","1000","","","","","1000",""]
           - ["帐号不传","账号不能超过60个字符",913,"",true,"password","DFST","1000","","","","","1000",""]
           - ["帐号长度小于1","账号不能超过60个字符",913,"",true,"password","DFST","1000","","","","","1000",""]
           - ["帐号长度大于60","账号不能超过60个字符",913,"abcdab<PERSON><PERSON>b<PERSON><PERSON>b<PERSON><PERSON>b<PERSON>dabcdabcdabcdabcdab<PERSON><PERSON>b<PERSON>dabcdabcdabcdabcdabcd",true,"password","DFST","1000","","","","","1000",""]
           - ["登录密码为空","登录密码不能为空",913,"admin",true,null,"DFST","1000","","","","","1000",""]
           - ["登录密码不传","登录密码不能为空",913,"admin",true,"","DFST","1000","","","","","1000",""]
           - ["是否记住我为空","是否记住我不能为空",913,"admin",null,"ad","DFST","1000","","","","","1000",""]
           - ["验证码为空","验证码不能为空",913,"admin",true,"adbc",null,"1000","","","","","1000",""]
           - ["验证码不传","验证码不能为空",913,"admin",true,"adbc","","1000","","","","","1000",""]
           - ["header租户编码不传","租户编码验证不正确",1111204,"a",true,"password","DFST","","","","","","1000",""]
           - ["header租户编码为空","租户编码验证不正确",1111204,"a",true,"password","DFST",null,"","","","","1000",""]
#           - ["帐号包含特殊符号:","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin:",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号/","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin/",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号\\","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin\\",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号*","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin*",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号?","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin?",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号|","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin|",true,"password","DFST","1000","","","","","1000",""]
#           - ["帐号包含特殊符号\"","账号不能包含中文或以下特殊符号：\\/:*?\"<>|",1111263,"admin\"",true,"password","DFST","1000","","","","","1000",""]
#           - ["填写的账号不存在","填写的账号不存在",1111275,"sydfda",true,"password","DFST","1000","","","","","1000",""]
