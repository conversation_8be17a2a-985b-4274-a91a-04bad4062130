#后管双因子登录接口验证
config:
    name: "双因子账号登录单接口用例"
    variables:
        account01: ${ENV(zidhdl.userCode)}
        account02: ${ENV(accountDj.userCode)}
        password01: ${ENV(login.password)}
        verificationCode01: ${ENV(verificationCode)}
#        target01: "${ENV(esign.projectHost)}/main-index-web/home"
#        referer01: "${ENV(esign.projectHost)}/main-index-web/task"

testcases:
-   name: 登录验证-双因子登录-账密登录报错用例1
    testcase: testcases/manage/login/sso/doubleFactorLoginVerify.yml
    parameters:
        - casename_1-account_1-accountType_1-password-verificationCode_1-except_code_1-except_message_1-except_succes_1:
            - ["账号为空","","1",$password01,$verificationCode01,1412022,"账号格式不正确",False]
            - ["账密都为空","","1","",$verificationCode01,1412022,"账号格式不正确",False]
            - ["密码为空",$account01,"1","",$verificationCode01,1412022,"密码不能为空",False]
            - ["图形验证码为空",$account01,"1",$password01,"",1412022,"图形验证码格式不正确",False]
            - ["图形验证码不填",$account01,"1",$password01,"",1412022,"图形验证码格式不正确",False]
#            - ["密码错误",$account01,"1",123456@bc,$verificationCode01,1412022,"图形验证码已过期",False]
#            - ["账号不存在","zhoull","1",123456@bc,$verificationCode01,1412022,"图形验证码不能为空",False]
#            - ["账号未授权",$account02,"1",123456@bc,$verificationCode01,1412022,"图形验证码不能为空",False]



-
    name: 登录验证-双因子登录-报错用例
    testcase: testcases/manage/login/sso/doubleFactorLogin.yml
    parameters:
        - casename-account-accountType-dynamicCode-except_code-except_message-except_succes:
            - ["账号为空","","3","123456",1412022,"账号不能为空",False]
            - ["动态验证码为空",$account01,"3","",1412022,"动态验证码不能为空",False]
            - ["账号类型为空",$account01,"","123456",1412022,"账号类型不正确",False]
            - ["账号类型为非3和4",$account01,"1","123456",1412022,"账号类型不正确",False]

