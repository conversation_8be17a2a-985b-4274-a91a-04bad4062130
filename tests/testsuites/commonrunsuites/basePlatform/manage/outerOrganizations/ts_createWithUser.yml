
config:
    name: 创建外部组织和用户

testcases:
-
    name: 测试创建外部组织和用户，字段校验
    testcase: testcases/manage/outerOrganizations/createWithUser/tc_createWithUser.yml
    parameters:
        name_4-customOrgNo_4-licenseType_4-licenseNo_4-customAccountNo-email-mobile-name-bankCardNo-licenseNo-except_code_4-except_message_4-expect_data:
            - ["customOrgNo不填","","REGIST_NUMBER","***************","ZDHCSZZHMUSER_1","","***********","自动化创建外部组织和账号","","",913,"组织账号未填写",""]
            - ["","ZDHCSZZHM_1","REGIST_NUMBER","***************","ZDHCSZZHMUSER_1","","***********","自动化创建外部组织和账号","","",913,"组织名称未填写",""]
#            - ["customAccountNo不填","ZDHCSZZHM_1","REGIST_NUMBER","***************","","","***********","自动化创建外部组织和账号","","",200,"成功",""]
#            - ["email和手机号不填","ZDHCSZZHM_1","REGIST_NUMBER","***************","ZDHCSZZHMUSER_1","","","自动化创建外部组织和账号","","",913,"组织账号未填写",""]
#            - ["用户姓名没填","ZDHCSZZHM_1","REGIST_NUMBER","***************","ZDHCSZZHMUSER_1","<EMAIL>","","","","",913,"组织账号未填写",""]


-
    name: 获取外部机构列表分页测试用例--证件号和证件类型都为空
    testcase: testcases/manage/outerOrganizations/createWithUser/tc_createWithUser2.yml

-
    name: 获取外部机构列表分页测试用例--证件号和证件类型都有值
    testcase: testcases/manage/outerOrganizations/createWithUser/tc_createWithUser3.yml

-
    name: 创建内外部形同账号和姓名的用户，查看创建成功后的详情
    testcase: testcases/manage/outerOrganizations/createWithUser/tc_createWithUser_scene5.yml