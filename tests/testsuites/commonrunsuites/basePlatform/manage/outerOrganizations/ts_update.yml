
config:
    name: 修改外部组织用例集
    variables:
        setup_data:
          {
            "customOrgNo": ZDHCSBJWBZZTSZDJY,
            "name": 自动化测试编辑外部组织字段特殊校验,
            "licenseType": ,
            "licenseNo": ,
            "legalRepAccountNo": ,
            "legalRepUserCode":
          }
        setup_res: ${create_outer_organizations($setup_data)}
        setup_organizationCode: ${get_value($setup_res,organizationCode)}


testcases:
-
    name: 修改外部组织场景集
    testcase: testcases/manage/outerOrganizations/update/tc_update_1.yml

-
    name: 测试修改证件号-特殊字符
    testcase: testcases/manage/outerOrganizations/update/tc_update_2.yml
    parameters:
        organizationCode-title1-licenseType1-licenseNo1-code1-message1:
            - [$setup_organizationCode,"证件类型传不存在的","abc",null,1111322,"证件类型仅支持:统一社会信用代码(CREDIT_CODE) 工商注册号(REGIST_NUMBER) 其他(OTHER)"]
            - [$setup_organizationCode,"统一社会信用代码含小写字母","CREDIT_CODE","*****************c",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含:","CREDIT_CODE","*****************:",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含*","CREDIT_CODE","******************",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含?","CREDIT_CODE","*****************?",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含\\","CREDIT_CODE","*****************\\",1111287,"证件号码格式错误"]
#            - [$setup_organizationCode,"统一社会信用代码含<","CREDIT_CODE","*****************<",1111287,"证件号码格式错误"]
#            - [$setup_organizationCode,"统一社会信用代码含>","CREDIT_CODE","*****************>",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含|","CREDIT_CODE","*****************|",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含\"","CREDIT_CODE","*****************\"",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含空格","CREDIT_CODE","***************** ",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码长度大于18","CREDIT_CODE","*****************C9",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码长度小于18","CREDIT_CODE","*****************",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度小于13","REGIST_NUMBER","111112222233",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度14","REGIST_NUMBER","11111222223333",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度16","REGIST_NUMBER","1111122222333334",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含英文","REGIST_NUMBER","11111222223333a",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含:","REGIST_NUMBER","11111222223333:",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含?","REGIST_NUMBER","11111222223333?",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含*","REGIST_NUMBER","11111222223333*",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含\\","REGIST_NUMBER","1111122222333\\",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含/","REGIST_NUMBER","11111222223333/",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含|","REGIST_NUMBER","11111222223333|",1111287,"证件号码格式错误"]
#            - [$setup_organizationCode,"工商注册号含<","REGIST_NUMBER","11111222223333<",1111287,"证件号码格式错误"]
#            - [$setup_organizationCode,"工商注册号含>","REGIST_NUMBER","11111222223333>",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含空格","REGIST_NUMBER","11111222223333 ",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含中文","REGIST_NUMBER","11111222223333中",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号含\"","REGIST_NUMBER","11111222223333\"",1111287,"证件号码格式错误"]
#
#
-
    name: 清理数据，删除外部组织、用户
    testcase: testcases/manage/outerOrganizations/update/teardown_delete.yml
    parameters:
        organizationCode:
          - [ $setup_organizationCode ]