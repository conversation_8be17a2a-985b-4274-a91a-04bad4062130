config:
  name: "应用中心用例集"

testcases:
-
    name: 用户实名认证通过后更新实名信息并存证正常用例
    testcase: testcases/manage/persons/tc_authFlowId_2.yml

-
    name: 上传应用
    testcase: testcases/manage/Integrate/appCenter/tc_upload_app.yml

#-
#    name: 上传应用授权
#    testcase: testcases/manage/Integrate/appCenter/tc_upload_app_lic.yml

-
    name: 安装应用
    testcase: testcases/manage/Integrate/appCenter/tc_install_app.yml

#-
#    name: 发现应用
#    testcase: testcases/manage/Integrate/appCenter/tc_discovery_app_list.yml
#
#-
#    name: 发现应用安装
#    testcase: testcases/manage/Integrate/appCenter/tc_discovery_app_install.yml
#
#-
#    name: 发现应用安装结果
#    testcase: testcases/manage/Integrate/appCenter/tc_discovery_app_install_result.yml

-
    name: 查询和配置应用
    testcase: testcases/manage/Integrate/appCenter/tc_find_app_configs.yml

-
    name: 启动应用
    testcase: testcases/manage/Integrate/appCenter/tc_startup_app.yml

-
    name: 查询应用详情
    testcase: testcases/manage/Integrate/appCenter/tc_app_info.yml


-
    name: 卸载应用
    testcase: testcases/manage/Integrate/appCenter/tc_uninstall_app.yml

-
    name: 删除应用
    testcase: testcases/manage/Integrate/appCenter/tc_delete_app.yml





