config:
  name: "修改内部用户用例集"
  variables:
    tmp1: ${deleteInnerUsers(测试编辑一用户)}
    tmp2: ${deleteInnerOrgs(自动化测试修改一内部用户特殊字段)}
#    setup_data:
#      {
#        "customOrgNo": ZDHCSXJNBYHTSZDY,
#        "name": 自动化测试编辑内部用户特殊字段校验一,
#        "organizationType": COMPANY,
#        "parentOrgNo": ,
#        "parentCode": "0",
#        "licenseType": ,
#        "licenseNo": ,
#        "legalRepAccountNo": ,
#        "legalRepUserCode":
#      }
#    setup_res: ${create_inner_organizations($setup_data)}
#    setup_organizationCode: ${get_value($setup_res,organizationCode)}
    setup_organizationCode: ${ENV(csqs.orgCode)}
    userdata:
      [
        {
          "customAccountNo": "csbjtszd",
          "name": "测试编辑用户特殊字段",
          "mobile": "***********",
          "email": "${random_str(10)}@tsign.cn",
          "licenseType": ,
          "licenseNo": ,
          "bankCardNo": ,
          "mainOrganizationCode": $setup_organizationCode,
          "mainCustomOrgNo": ,
          "otherOrganization": [ ]
        }
      ]
    user_res: ${create_inner_users($userdata)}
    upsetusercode: ${get_value($user_res,userCode)}

testcases:
-
    name: 修改内部用户场景用例集
    testcase: testcases/manage/InnerUsers/update/tc_update_1.yml

-
    name: 修改内部用户场景-校验兼职组织的更新
    testcase: testcases/manage/InnerUsers/update/tc_update_other.yml

-
    name: 修改内部用户场景
    testcase: testcases/manage/InnerUsers/update/tc_update_2.yml
    parameters:
      userCode-title1-name1-mobile1-email1-licenseType1-licenseNo1-bankCardNo1-code1-message1:

        - [ $upsetusercode,"用户姓名传空字符","","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名长度超过26字符","张三qwertyuiopasdfghjklzxcvbn","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名含特殊字符:","张三:","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名含特殊字符/","张三/","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名含特殊字符\\","张三\\","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名含特殊字符*","张三*","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名含特殊字符?","张三?","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        #            - [ $upsetusercode,"用户姓名含特殊字符<","张三<","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名"]
        #            - [ $upsetusercode,"用户姓名含特殊字符>","张三>","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名"]
        - [ $upsetusercode,"用户姓名含特殊字符|","张三|","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]

        - [ $upsetusercode,"用户姓名含特殊字符\"","张三\"","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]
        - [ $upsetusercode,"用户姓名包含数字","张三1","***********","${random_str(10)}@tsign.cn",null,null,null,913,"请输入正确的用户姓名" ]


-
    name: 修改内部用户场景
    testcase: testcases/manage/InnerUsers/update/tc_update_3.yml
    parameters:
      userCode-title1-name1-mobile1-email1-licenseType1-licenseNo1-bankCardNo1-code1-message1:
        - [ $upsetusercode, "手机号码长度10字","须弥","**********","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符:","须弥","**********:","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符/","须弥","**********/","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符\\","须弥","**********\\","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符*","须弥","***********","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符?","须弥","**********?","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        #            - [ $upsetusercode,"手机号码含特殊字符<","须弥","**********<","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号"]
        #
        #            - [ $upsetusercode,"手机号码含特殊字符>","须弥","**********>","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号"]
        - [ $upsetusercode, "手机号码含特殊字符|","须弥","**********|","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符\"","须弥","**********\"","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符中文","须弥","**********中","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符空格","须弥","183012345 6","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "手机号码含特殊字符英文","须弥","**********a","${random_str(10)}@tsign.cn",null,null,null,1111243,"请输入正确的手机号" ]
        - [ $upsetusercode, "邮箱含特殊字符:_1","须弥","13100002222","xumi:@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符:_2","须弥","13100002222","xumi@tsign:.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符/_1","须弥","13100002222","xumi/@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]

        - [ $upsetusercode, "邮箱含特殊字符/_2","须弥","13100002222","xumi@/tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符\\_1","须弥","13100002222","xumi\\@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符\\_2","须弥","13100002222","xumi@t\\sign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符*_1","须弥","13100002222","xumi*@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符*_2","须弥","13100002222","xumi@t*sign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符?_1","须弥","13100002222","xumi?@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符?_2","须弥","13100002222","xumi@ts?ign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        #            - [ $upsetusercode,"邮箱含特殊字符<_1","须弥","13100002222","xumi<@tsign.cn",null,null,null,1111242,"请输入正确的邮箱"]
        #            - [ $upsetusercode,"邮箱含特殊字符<_2","须弥","13100002222","xumi@t<sign.cn",null,null,null,1111242,"请输入正确的邮箱"]
        #            - [ $upsetusercode,"邮箱含特殊字符>_1","须弥","13100002222","xumi>@tsign.cn",null,null,null,1111242,"请输入正确的邮箱"]
        #
        #            - [ $upsetusercode,"邮箱含特殊字符>_2","须弥","13100002222","xumi@t>sign.cn",null,null,null,1111242,"请输入正确的邮箱"]
        - [ $upsetusercode, "邮箱含特殊字符|_1","须弥","13100002222","xumi|@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符|_2","须弥","13100002222","xumi@ts|ign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符_1\"","须弥","13100002222","xumi\"@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符_2\"","须弥","13100002222","xumi@t\"sign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符中文_1","须弥","13100002222","xumi徐@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符中文_2","须弥","13100002222","xumi@ts徐ign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符空格_1","须弥","13100002222","xum <EMAIL>",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱含特殊字符空格_2","须弥","13100002222","xumi@ts ign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱格式错误_1","须弥","13100002222","xumi@tsign",null,null,null,1111242,"请输入正确的邮箱" ]

        - [ $upsetusercode, "邮箱格式错误_2","须弥","13100002222","xumitsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱格式错误_3","须弥","13100002222","xumitsign@.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱格式错误_4","须弥","13100002222","xumi@@tsign.cn",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "邮箱格式错误_5","须弥","13100002222","xumi@.cntsign",null,null,null,1111242,"请输入正确的邮箱" ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符:","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593:",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符/","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593/",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符\\","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593\\",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符*","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","110101200703075935*",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符?","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593?",null,1111237,请输入正确的证件号码 ]

        #            - [ $upsetusercode, "证件类型身份证，身份证含特殊字符<","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593<",null,1111237,请输入正确的证件号码 ]
        #            - [ $upsetusercode, "证件类型身份证，身份证含特殊字符>","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593>",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符|","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593|",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符\"","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593\"",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符中文","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593中",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证含特殊字符空格","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593 ",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，身份证不符合规则","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","*********012345678 ",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，长度小于18字","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","11010120070307593",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型身份证，长度大于18字","须弥","13100002222","${random_str(10)}@tsign.cn","ID_CARD","1101012007030759356",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符:","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567:",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符/","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567/",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符\\","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567\\",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符*","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567*",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符?","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567?",null,1111237,请输入正确的证件号码 ]
        #            - [ $upsetusercode,"证件类型港澳通行证，含特殊字符<","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567<",null,1111237,请输入正确的证件号码]
        #            - [ $upsetusercode,"证件类型港澳通行证，含特殊字符>","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567>",null,1111237,请输入正确的证件号码]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符|","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567|",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符\"","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567\"",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符中文","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567中",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，含特殊字符空格","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","H1234567 ",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型港澳通行证，不符合规则","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","11234567",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型港澳通行证，长度7字符","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","1234567",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，长度9字符","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","*********",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型港澳通行证，长度11字符","须弥","13100002222","${random_str(10)}@tsign.cn","HK_MACAO","*********01",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符:","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567:",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符/","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567/",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符\\","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567\\",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符*","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567*",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符?","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567?",null,1111237,请输入正确的证件号码 ]
        #            - [ $upsetusercode,"证件类型台湾通行证，证件号含特殊字符<","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567<",null,1111237,请输入正确的证件号码]
        #            - [ $upsetusercode,"证件类型台湾通行证，证件号含特殊字符>","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567>",null,1111237,请输入正确的证件号码]

        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符|","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567|",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符\"","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567\"",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符中文","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567中",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含英文","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567 ",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号含特殊字符空格","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","T1234567",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号长度7","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","1234567",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号长度9","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","*********",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型台湾通行证，证件号长度11","须弥","13100002222","${random_str(10)}@tsign.cn","TAIWAN","*********01",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符:","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567:",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符/","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567/",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符\\","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567\\",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型护照，证件号含特殊字符*","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567*",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符?","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567?",null,1111237,请输入正确的证件号码 ]
        #            - [ $upsetusercode,"证件类型护照，证件号含特殊字符<","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567<",null,1111237,请输入正确的证件号码]
        #            - [ $upsetusercode,"证件类型护照，证件号含特殊字符>","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567>",null,1111237,请输入正确的证件号码]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符|","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567|",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符\"","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567\"",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符中文","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234567中",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号含特殊字符空格","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","123456 7",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号长度4","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","1234",null,1111237,请输入正确的证件号码 ]
        - [ $upsetusercode, "证件类型护照，证件号长度21","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0a",null,1111237,请输入正确的证件号码 ]

        - [ $upsetusercode, "证件类型空字符","须弥","13100002222","${random_str(10)}@tsign.cn","","",null,1111312,"证件号码类型只能填写PASSPORT、HK_MACAO、TAIWAN、ID_CARD、OTHER" ]
        - [ $upsetusercode, "银行卡号含特殊字符:","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123:",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符/","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123/",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符\\","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123\\",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符*","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123*",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符?","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123?",1111245,请输入正确的银行卡号 ]
        #            - [ $upsetusercode,"银行卡号含特殊字符<","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123<",1111245,请输入正确的银行卡号]
        #            - [ $upsetusercode,"银行卡号含特殊字符>","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123>",1111245,请输入请输入正确的银行卡号正确的证件号码]
        - [ $upsetusercode, "银行卡号含特殊字符|","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123|",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符\"","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123\"",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符中文","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123中",1111245,请输入正确的银行卡号 ]

        - [ $upsetusercode, "银行卡号含特殊字符空格","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123 ",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符长度小于15","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","0*********0123",1111245,请输入正确的银行卡号 ]
        - [ $upsetusercode, "银行卡号含特殊字符长度大于19","须弥","13100002222","${random_str(10)}@tsign.cn","PASSPORT","*********0*********0","Y*********0*********",1111245,请输入正确的银行卡号 ]
-
    name: 删除用户
    testcase: testcases/manage/InnerUsers/update/teardown_delete.yml
    parameters:
      userCode-organizationCode:
        - [ $upsetusercode,"" ]