config:
    name: "[内]01-组织用户-用户管理-删除用户"

testcases:

-
    name: 自动化测试删除用户字段校验-$title1(V6.0.12.0-beta.4废弃用户删除功能)
    testcase: testcases/manage/portal/user/deleteUserById/tc_deleteUserById1.yml
    parameters:
       title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["删除用户ID为空",null,"没有该按钮权限,请联系管理员！",1802008,"","","","","1000",""]
#          - ["删除用户ID不传","","用户主键不能为空",1501001,"","","","","1000",""]
#          - ["删除用户ID不存在","123456485462132","操作人数据权限不足，请联系管理员检查权限",1502801,"","","","","1000",""]

#-
#    name: 自动化测试删除用户场景-$title1
#    testcase: testcases/manage/portal/user/deleteUserById/tc_deleteUserById2.yml
#    parameters:
#      title1-id1-message1-code1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#        - ["删除用户成功","","成功",200,"","","","","1000",""]