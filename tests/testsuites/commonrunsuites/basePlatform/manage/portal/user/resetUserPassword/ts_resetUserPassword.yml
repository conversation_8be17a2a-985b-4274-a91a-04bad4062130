config:
    name: "根据用户主键ID修改用户密码"
    variables:
      sys_role_code: SUB_SYS_ADMIN
        #      # 系统管理员 角色id
      sys_ternary_id: ${get_ternary_distribution_id($sys_role_code)}
testcases:


-
    name: 自动化测试根据用户主键ID修改用户密码字段校验-$title1
    testcase: testcases/manage/portal/user/resetUserPassword/tc_resetUserPassword1.yml
    parameters:
       title1-message1-code1-id1-password1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["用户ID传空","用户id不能为空",1501001,null,"123456","","","","","1000",""]
          - ["用户ID不传","用户id不能为空",1501001,"","123456","","","","","1000",""]


#注:开启该用例需要等待2000S,并且配置邮箱相关用于通过邮箱获取密码,因为新创建的用户需要30分钟后才能修改密码,并且新建用户邮箱要与getPassword()方法邮箱一致
#-
#    name: 自动化测试根据用户主键ID修改用户密码场景校验-$title1
#    testcase: testcases/manage/portal/user/resetUserPassword/tc_resetUserPassword2.yml
#    parameters:
#      title1-message1-code1-id1-password1-userName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
#        - ["修改密码后能正常登陆","成功",200,"","","醉生测试修改用户密码","","","","","1000","" ]
