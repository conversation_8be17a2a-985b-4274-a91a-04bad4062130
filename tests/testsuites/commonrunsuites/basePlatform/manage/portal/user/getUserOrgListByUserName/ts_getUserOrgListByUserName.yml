config:
    name: "获取某一租户下所有用户和组织简略信息通过userName"

testcases:

-
    name: 自动化测试获取某一租户下所有用户和组织简略信息通过userName字段校验-$title1
    testcase: testcases/manage/portal/user/getUserOrgListByUserName/tc_getUserOrgListByUserName1.yml
    parameters:
       title1-message1-code1-userName1-userTerritory1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
          - ["用户姓名为空","参数userName必填!",913,null,"","","","","","1000",""]
          - ["用户姓名不传","参数userName必填!",913,"","","","","","","1000",""]

-
    name: 自动化测试获取某一租户下所有用户和组织简略信息通过userName场景校验-$title1
    testcase: testcases/manage/portal/user/getUserOrgListByUserName/tc_getUserOrgListByUserName2.yml
    parameters:
      title1-message1-code1-userName1-userTerritory1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - [ "能获取新增的组织和用户信息","成功",200,"醉生测试获取某一租户下所有用户和组织简略信息","1","","","","","1000","" ]