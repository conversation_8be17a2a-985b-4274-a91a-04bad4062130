config:
    name: "导入外部用户Excel"
#    variables:
#      num: ${get_snowflake()}
#      data1:
#        {
#          "customOrgNo": BJFGSA1,
#          "name": 醉生测试自动化导入$num,
#          "licenseType": ,
#          "licenseNo": ,
#          "legalRepAccountNo": ,
#          "legalRepUserCode":
#        }
#      res1: ${create_outer_organizations($data1)}
#      setup_organizationCode: ${ENV(wsignwb01.main.orgCode)}

testcases:
#
#####todo 数据库唯一性校验未做自动化(校验已存在的)
-
    name: 自动化测试导入外部用户Excel场景集合-$title1-异步处理任务都会是成功
    testcase: testcases/manage/portal/opposite/importOutsideUserExcel/tc_importOutsideUserExcel1.yml
    parameters:
       title1-task_status-task_reason-task_name-filename1-fileType1-filePath1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
             - ["导入空模板",2,"共0条,导入成功0条/失败0条","外部用户导入空模板","外部用户导入空模板","multipart/form-data;","data/importOutsideUser/外部用户导入空模板.xlsx","","","","","1000",""]
             - ["导入用户姓名异常校验",3,"共13条,导入成功0条/失败13条","外部用户导入姓名校验-异常","外部用户导入姓名校验-异常","multipart/form-data;","data/importOutsideUser/userName/外部用户导入姓名校验-异常.xlsx","","","","","1000",""]
             - ["导入用户账号异常校验",3,"共14条,导入成功0条/失败14条","外部用户导入账号校验-异常","外部用户导入账号校验-异常","multipart/form-data;","data/importOutsideUser/accountNumber/外部用户导入账号校验-异常.xlsx","","","","","1000",""]
             - ["导入用户银行卡号异常校验",3,"共13条,导入成功0条/失败13条","外部用户导入银行卡号校验-异常","外部用户导入银行卡号校验-异常","multipart/form-data;","data/importOutsideUser/bankCardNo/外部用户导入银行卡号校验-异常.xlsx","","","","","1000",""]
             - ["导入用户身份证号异常校验",3,"共14条,导入成功0条/失败14条","外部用户导入证件类型身份证校验-异常","外部用户导入证件类型身份证校验-异常","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/外部用户导入证件类型身份证校验-异常.xlsx","","","","","1000",""]
             - ["导入用户港澳通行证异常校验",3,"共15条,导入成功0条/失败15条","外部用户导入证件类型港澳通行证校验-异常","外部用户导入证件类型港澳通行证校验-异常","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/外部用户导入证件类型港澳通行证校验-异常.xlsx","","","","","1000",""]
             - ["导入用户护照异常校验",3,"共13条,导入成功0条/失败13条","外部用户导入证件类型护照校验-异常","外部用户导入证件类型护照校验-异常","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/外部用户导入证件类型护照校验-异常.xlsx","","","","","1000",""]
             - ["导入用户台湾通行证异常校验",3,"共15条,导入成功0条/失败15条","外部用户导入证件类型台湾通行证校验-异常","外部用户导入证件类型台湾通行证校验-异常","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/外部用户导入证件类型台湾通行证校验-异常.xlsx","","","","","1000",""]
             - ["导入用户证件类型不支持",3,"共1条,导入成功0条/失败1条","外部用户导入证件类型其他字符","外部用户导入证件类型其他字符","multipart/form-data;","data/importOutsideUser/licenseType/外部用户导入证件类型其他字符.xlsx","","","","","1000",""]
             - ["导入用户所属组织账号异常校验",3,"共3条,导入成功0条/失败3条","外部用户导入所属组织账号校验-异常","外部用户导入所属组织账号校验-异常","multipart/form-data;","data/importOutsideUser/organizationNumber/外部用户导入所属组织账号校验-异常.xlsx","","","","","1000",""]
             - ["导入用户邮箱异常校验",3,"共29条,导入成功0条/失败29条","外部用户导入邮箱校验-异常","外部用户导入邮箱校验-异常","multipart/form-data;","data/importOutsideUser/userEmail/外部用户导入邮箱校验-异常.xlsx","","","","","1000",""]
             - ["导入用户手机号异常校验",3,"共15条,导入成功0条/失败15条","外部用户导入手机号校验-异常","外部用户导入手机号校验-异常","multipart/form-data;","data/importOutsideUser/userMobile/外部用户导入手机号校验-异常.xlsx","","","","","1000",""]



#-
#    name: 自动化测试导入外部用户Excel场景集合-$title2
#    testcase: testcases/manage/portal/opposite/importOutsideUserExcel/tc_importOutsideUserExcel2.yml
#    parameters:
#       title2-message1-code1-filename1-fileType1-filePath1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
##             - ["导入空模板","不能导入空模板",1111339,"管理平台-外部用户导入空模板","multipart/form-data;","data/importOutsideUser/外部用户导入模板-0条.xlsx","","","","","1000",""]
#             #- ["超过5000条","导入数据不能超过5000条",1111248,"管理平台-外部用户导入超过5000条","multipart/form-data;","data/importOutsideUser/管理平台-外部用户导入超过5000条.xlsx","","","","","1000","" ]
#          ##
#             - ["所属组织账号长度大于50字","所属组织账号 输入长度应为1-50字",200,"管理平台-外部用户导入所属组织帐号超长","multipart/form-data;","data/importOutsideUser/organizationNumber/管理平台-外部用户导入所属组织账号超长.xlsx","","","","","1000",""]
#             - ["所属组织帐号不存在","所属组织账号不存在",200,"管理平台-外部用户导入所属组织帐号不存在","multipart/form-data;","data/importOutsideUser/organizationNumber/管理平台-外部用户导入所属组织账号不存在.xlsx","","","","","1000","" ]
#          #
#             - ["Excel中有重复用户手机","用户手机已存在",200,"管理平台-外部用户导入有重复手机号","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入有重复手机号.xlsx","","","","","1000","" ]
#             - ["Excel中有重复邮箱","用户邮箱已存在",200,"管理平台-外部用户导入有重复邮箱","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入有重复邮箱.xlsx","","","","","1000","" ]
#             - ["Excel中有重复帐号","账号已存在",200,"管理平台-外部用户导入有重复帐号","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入有重复账号.xlsx","","","","","1000","" ]
#             - ["Excel中有重复银行卡号","银行卡号已存在",200,"管理平台-外部用户导入有重复银行卡号","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入有重复银行卡号.xlsx","","","","","1000","" ]
#             - ["Excel中有重复证件号码","证件号码已存在",200,"管理平台-外部用户导入有重复证件号码","multipart/form-data;","data/importOutsideUser/licenseNumber/管理平台-外部用户导入有重复证件号码.xlsx","","","","","1000","" ]
#             #
#             - ["用户姓名未填写","用户姓名 未填写",200,"管理平台-外部用户导入用户姓名未填写","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名未填写.xlsx","","","","","1000","" ]
#             - ["用户姓名长度大于26字","用户姓名 输入长度应为2-26字",200,"管理平台-外部用户导入用户姓名过长","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名过长.xlsx","","","","","1000","" ]
#             - ["用户姓名长度小于2字","用户姓名 输入长度应为2-26字",200,"管理平台-外部用户导入用户姓名过短","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名过短.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符:","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符一","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符一.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符/","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符二","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符二.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符\\","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符三","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符三.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符<","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符四","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符四.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符>","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符五","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符五.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符*","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符六","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符六.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符?","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符七","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符七.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符|","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符八","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符八.xlsx","","","","","1000","" ]
#             - ["用户姓名包含特殊字符\"","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含特殊字符九","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含特殊字符九.xlsx","","","","","1000","" ]
#             - ["用户姓名包含数字","用户姓名格式错误",200,"管理平台-外部用户导入用户姓名包含数字","multipart/form-data;","data/importOutsideUser/userName/管理平台-外部用户导入用户姓名包含数字.xlsx","","","","","1000","" ]
##
#             - ["用户账号未填写","用户账号 未填写",200,"管理平台-外部用户导入用户账号未填写","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号未填写.xlsx","","","","","1000","" ]
#             - ["用户账号长度大于30字","用户账号 输入长度应为2-30字",200,"管理平台-外部用户导入用户账号超长","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号超长.xlsx","","","","","1000","" ]
#             - ["用户账号长度小于2字","用户账号 输入长度应为2-30字",200,"管理平台-外部用户导入用户账号过短","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号过短.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符:","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符一","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符一.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符/","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符二","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符二.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符\\","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符三","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符三.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符<","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符四","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符四.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符>","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符五","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符五.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符*","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符六","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符六.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符?","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符七","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符七.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符|","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符八","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符八.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符\"","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符九","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符九.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符空格","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符空格","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符空格.xlsx","","","","","1000","" ]
#             - ["用户账号包含特殊字符中文","用户账号格式错误",200,"管理平台-外部用户导入用户账号包含特殊字符中文","multipart/form-data;","data/importOutsideUser/accountNumber/管理平台-外部用户导入用户账号包含特殊字符中文.xlsx","","","","","1000","" ]
##
#             - [ "银行卡号含特殊字符:","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符一","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符一.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符/","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符二","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符二.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符\\","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符三","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符三.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符*","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符四","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符四.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符?","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符五","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符五.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符<","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符六","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符六.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符>","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符七","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符七.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符|","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符八","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符八.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符\"","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含特殊字符九","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含特殊字符九.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符中文","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含中文","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含中文.xlsx","","","","","1000","" ]
#             - [ "银行卡号含特殊字符空格","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号包含空格","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号包含空格.xlsx","","","","","1000","" ]
#             - [ "银行卡号长度小于15","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号长度小于15","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号长度小于15.xlsx","","","","","1000","" ]
#             - [ "银行卡号长度大于19","银行卡号格式错误",200,"管理平台-外部用户导入银行卡号长度大于19","multipart/form-data;","data/importOutsideUser/bankCardNo/管理平台-外部用户导入银行卡号长度大于19.xlsx","","","","","1000","" ]
# #
#             - ["证件类型身份证含特殊字符:","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符一","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符一.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符/","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符二","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符二.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符\\","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符三","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符三.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符*","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符四","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符四.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符?","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符五","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符五.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符<","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符六","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符六.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符>","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符七","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符七.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符|","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符八","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符八.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符\"","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符九","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符九.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符中文","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符中文","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符中文.xlsx","","","","","1000","" ]
#             - ["证件类型身份证含特殊字符空格","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证含特殊字符空格","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证含特殊字符空格.xlsx","","","","","1000","" ]
#             - ["证件类型身份证不符合规则","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证不符合规则","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证不符合规则.xlsx","","","","","1000","" ]
#             - ["证件类型身份证长度大于18","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证长度过长","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证长度过长.xlsx","","","","","1000","" ]
#             - ["证件类型身份证长度小于18","证件号码格式错误",200,"管理平台-外部用户导入证件类型身份证长度过短","multipart/form-data;","data/importOutsideUser/licenseNumber/idCard/管理平台-外部用户导入证件类型身份证长度过短.xlsx","","","","","1000","" ]
# #
#             - [ "证件类型港澳通行证含特殊字符:","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符一","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符一.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符/","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符二","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符二.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符\\","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符三","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符三.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符*","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符四","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符四.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符?","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符五","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符五.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符<","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符六","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符六.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符>","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符七","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符七.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符|","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符八","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符八.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符\"","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符九","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符九.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符中文","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符中文","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符中文.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证含特殊字符空格","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证含特殊字符空格","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证含特殊字符空格.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证不符合规则","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证不符合规则","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证不符合规则.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证长度7","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证长度7","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证长度7.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证长度9","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证长度9","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证长度9.xlsx","","","","","1000","" ]
#             - [ "证件类型港澳通行证长度11","证件号码格式错误",200,"管理平台-外部用户导入证件类型港澳通行证长度11","multipart/form-data;","data/importOutsideUser/licenseNumber/macao/管理平台-外部用户导入证件类型港澳通行证长度11.xlsx","","","","","1000","" ]
#       #
#             - [ "证件类型台湾通行证含特殊字符:","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符一","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符一.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符/","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符二","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符二.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符\\","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符三","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符三.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符*","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符四","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符四.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符?","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符五","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符五.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符<","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符六","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符六.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符>","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符七","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符七.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符|","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符八","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符八.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符\"","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符九","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符九.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符中文","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符中文","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符中文.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符英文","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符英文","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符英文.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证含特殊字符空格","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证含特殊字符空格","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证含特殊字符空格.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证长度7","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证长度7","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证长度7.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证长度9","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证长度9","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证长度9.xlsx","","","","","1000","" ]
#             - [ "证件类型台湾通行证长度11","证件号码格式错误",200,"管理平台-外部用户导入证件类型台湾通行证长度11","multipart/form-data;","data/importOutsideUser/licenseNumber/taiwan/管理平台-外部用户导入证件类型台湾通行证长度11.xlsx","","","","","1000","" ]
#             #
#             - [ "证件类型护照含特殊字符:","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符一","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符一.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符/","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符二","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符二.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符\\","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符三","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符三.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符*","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符四","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符四.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符?","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符五","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符五.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符<","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符六","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符六.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符>","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符七","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符七.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符|","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符八","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符八.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符\"","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符九","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符九.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符中文","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符中文","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符中文.xlsx","","","","","1000","" ]
#             - [ "证件类型护照含特殊字符空格","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照含特殊字符空格","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照含特殊字符空格.xlsx","","","","","1000","" ]
#             - [ "证件类型护照长度4","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照长度4","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照长度4.xlsx","","","","","1000","" ]
#             - [ "证件类型护照长度21","证件号码格式错误",200,"管理平台-外部用户导入证件类型护照长度21","multipart/form-data;","data/importOutsideUser/licenseNumber/passport/管理平台-外部用户导入证件类型护照长度21.xlsx","","","","","1000","" ]
#            #
##             - [ "用户手机未填写","成功",200,"管理平台-外部用户导入用户手机未填写","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机未填写.xlsx","","","","","1000","" ]
#             - [ "手机号码长度10字","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码长度10字","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码长度10字.xlsx","","","","","1000","" ]
#             - [ "手机号码长度12字","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码长度12字","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码长度12字.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符:","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符一","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符一.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符/","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符二","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符二.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符\\","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符三","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符三.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符*","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符四","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符四.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符?","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符五","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符五.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符<","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符六","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符六.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符>","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符七","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符七.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符|","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符八","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符八.xlsx","","","","","1000","" ]
#             - [ "手机号码含特殊字符\"","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含特殊字符九","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含特殊字符九.xlsx","","","","","1000","" ]
#             - [ "手机号码含中文","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含中文","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含中文.xlsx","","","","","1000","" ]
#             - [ "手机号码含空格","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含空格","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含空格.xlsx","","","","","1000","" ]
#             - [ "手机号码含英文","用户手机格式错误",200,"管理平台-外部用户导入用户手机号码含英文","multipart/form-data;","data/importOutsideUser/userMobile/管理平台-外部用户导入用户手机号码含英文.xlsx","","","","","1000","" ]
#           #
#             - ["证件类型不支持","证件类型未填写",200,"管理平台-外部用户导入用户证件类型不支持","multipart/form-data;","data/importOutsideUser/licenseType/管理平台-外部用户导入用户证件类型不支持.xlsx","","","","","1000","" ]
#           #
##             - [ "用户邮箱未填写","成功",200,"管理平台-外部用户导入用户邮箱未填写","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱未填写.xlsx","","","","","1000","" ]
#             - [ "用户邮箱过短","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱过短","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱过短.xlsx","","","","","1000","" ]
#             - [ "用户邮箱过长","用户邮箱 输入长度应为1-50字",200,"管理平台-外部用户导入用户邮箱过长","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱过长.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符:_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符一_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符一_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符:_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符一_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符一_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符/_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符二_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符二_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符/_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符二_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符二_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符\\_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符三_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符三_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符\\_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符三_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符三_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符*_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符四_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符四_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符*_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符四_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符四_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符?_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符五_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符五_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符?_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符五_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符五_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符<_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符六_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符六_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符<_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符六_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符六_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符>_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符七_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符七_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符>_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符七_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符七_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符|_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符八_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符八_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符|_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符八_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符八_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符_1\"","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符九_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符九_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符_2\"","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符九_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符九_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符中文_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符中文_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符中文_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符中文_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符中文_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符中文_2.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符空格_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符空格_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符空格_1.xlsx","","","","","1000","" ]
#             - [ "邮箱含特殊字符空格_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱含特殊字符空格_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱含特殊字符空格_2.xlsx","","","","","1000","" ]
#             - [ "邮箱格式错误_1","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱格式错误_1","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱格式错误_1.xlsx","","","","","1000","" ]
#             - [ "邮箱格式错误_2","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱格式错误_2","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱格式错误_2.xlsx","","","","","1000","" ]
#             - [ "邮箱格式错误_3","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱格式错误_3","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱格式错误_3.xlsx","","","","","1000","" ]
#             - [ "邮箱格式错误_4","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱格式错误_4","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱格式错误_4.xlsx","","","","","1000","" ]
#             - [ "邮箱格式错误_5","用户邮箱格式错误",200,"管理平台-外部用户导入用户邮箱格式错误_5","multipart/form-data;","data/importOutsideUser/userEmail/管理平台-外部用户导入用户邮箱格式错误_5.xlsx","","","","","1000","" ]