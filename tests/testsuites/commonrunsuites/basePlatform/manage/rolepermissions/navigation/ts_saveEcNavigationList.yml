
config:
    name: "同步生态管理系统的导航配置列表数据用例集"

testcases:
-
    name: 测试创建-同步生态管理系统的导航配置列表数据
    testcase: testcases/manage/rolepermissions/navigation/tc_saveEcNavigationList.yml
    parameters:
        title1-parentId1-navigationName1-navigationCode1-navigationType1-navigationClass1-alloted1-authorization1-displayType1-enabled1-navigationIcon1-navigationUrl1-orderNum1-status1-message1:
            - [ "父级导航id为空","","自动化测试新增导航父级导航id为空","ZDHCS_PARENTID_ISNULL","3","1","","","","1","","",1000,913,"导航父id不存在"]
            - [ "导航名称为空","0","","ZDHCS_NAVIGATIONNAME_ISNULL","3","1","","","","1","","",1000,913,"导航名称必填,导航名称长度必须在1-50" ]
            - [ "导航名称长度超过50","0","自动化测试新增导航导航名称长度超过50字12345678901234567890123456789012345","ZDHCS_NAVIGATIONNAME_ISNULL","3","1","","","","1","","",1000,913,"导航名称长度必须在1-50" ]

            - [ "导航菜单导航图标超过36","0","自动化测试新增导航菜单导航图标超过36","ZDHCS_NAVIGATIOICONL_OVERLENGTH","3","2","","","1","1","1234567890123456789012345678901234567890","11",1000,913,"导航图标长度必须在0-36" ]

 #           - [ "导航菜单导航url为空","0","自动化测试新增导航菜单导航url为空","ZDHCS_NAVIGATIOURL_ISNULL","3","2","","","1","1","","",1000,913,"参数navigationUrl必填!" ]
            - [ "导航菜单导航url长度超过100","0","自动化测试新增导航菜单导航url长度超过100","ZDHCS_NAVIGATIOURL_OVERLENGTH","3","2","","","1","1","","12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",1000,913,"导航url长度必须在0-100" ]

 #           - [ "导航菜单导航显示类型为空","0","自动化测试新增导航菜单显示类型为空","ZDHCS_DISPLAYTYPE_ISNULL","3","2","","","","1","","111",1000,913,"参数displayType必填!" ]
            - [ "导航菜单导航显示类型不为1或2","0","自动化测试新增导航菜单显示类型不为1或2","ZDHCS_DISPLAYTYPE_VALUEERROR","3","2","","","333","1","","111",1000,913,"显示类型只支持1、2、3、4" ]

            - [ "导航编码为空","0","自动化测试新增导航编码为空","","3","1","","","","1","","",1000,913,"导航编码必填,导航编码长度必须在1-54" ]
            - [ "导航编码长度超过54","0","自动化测试新增导航导航编码长度超过54字","ZDHCS_NAVIGATIONCODE_1234567890123456789012345678901234567890","3","1","","","","1","","",1000,913,"导航编码长度必须在1-54" ]

            - [ "导航分类为空","0","自动化测试新增导航导航分类为空","ZDHCS_NAVIGATIONCLASS_ISNULL","3","","","","","1","","",1000,913,"导航分类不能为空,导航分类长度为1" ]
            - [ "导航分类不为1、2、3、4","0","自动化测试新增导航导航分类不为1或2或3","ZDHCS_NAVIGATIONCLASS_OVERLENGTH","3","99","","","","1","","",1000,913,"分类只支持1、2、3、4" ]

            - [ "是否启用为空","0","自动化测试新增导航是否启用为空","ZDHCS_NAVIGATIONCLASS_OVERLENGTH","3","1","","","","","","",1000,913,"是否启用不能为空" ]
            - [ "是否启用不为1或2","0","自动化测试新增导航是否启用不为1或2","ZDHCS_NAVIGATIONCLASS_VALUEERROR","3","1","","","","44","","",1000,913,"是否启用只支持1、2" ]

#            - [ "新建按钮授权标识不能为空","0","自动化测试新建按钮授权标识不能为空","ZDHCS_authorization_ISNULL","3","3","","","","1","","",1000,913,"参数authorization必填!" ]
            - [ "授权标识超过100","0","自动化测试授权标识超过100","ZDHCS_authorization_OVERLENGTH","3","1","","123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789011","","1","","",1000,913,"授权标识长度必须在0-100" ]

            - [ "排序为空","0","自动化测试排序为空","ZDHCS_ORDERNUM_ISNULL","3","1","","","","1","","",NULL,913,"排序不能为空" ]
            - [ "排序数据小于1","0","自动化测试排序数据小于1","ZDHCS_ORDERNUM_LETEERLENGTH","3","1","","","","1","","",0,913,"排序号最小支持1" ]
            - [ "排序输入超过10000000","0","自动化测试输入超过10000000","ZDHCS_ORDERNUM_OVERLENGTH","3","1","","","","1","","",999999999,913,"排序最大支持10000000" ]

