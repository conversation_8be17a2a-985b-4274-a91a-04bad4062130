
config:
    name: "获取用户组列表用例集"

testcases:
-
    name: 测试获取用户组列表
    testcase: testcases/manage/usergroup/list/tc_getUserGroupList.yml

-
    name: 测试通过用户组名字获取用户组列表分页
    testcase: testcases/manage/usergroup/list/tc_getUserGroupByUserGroupName.yml

-
    name: 测试通过用户id获取所在用户组列表
    testcase: testcases/manage/usergroup/list/tc_getUserGroupListByUserId.yml

-
    name: 测试通过用户组id获取用户组对象和组员对象
    testcase: testcases/manage/usergroup/list/tc_getUserGroupUserSByUserGroupId.yml

-
    name: 测试通过用户组编码获取组员列表
    testcase: testcases/manage/usergroup/list/tc_getUserListByUserGroupCode.yml

-
    name: 测试通过用户组UUID获得组员列表
    testcase: testcases/manage/usergroup/list/tc_getUserListByUserGroupId.yml

-
    name: 通过用户组编码获取组员列表（不包括删除用户组）
    testcase: testcases/manage/usergroup/list/tc_getUsersByUserGroupCode.yml
