config:
    name: "修改参数异常场景"
    variables:
      code: ${random_letter(4)}
      data:
        {
          domain: "admin_platform",
          params:
            {
              "orderNum": 1, #必填
              "outFlag": false,
              "parameterClassCode": "$code",
              "parameterClassDesc": "描述",
              "parameterClassName": "测试修改$code",
              "parameterClassStatus": "1",
              "parameterClassType": "2"
            }
        }
      res: ${save_parameter_class($data)}
      parameterId: ${get_parameter_class_by_name(测试修改$code,$code)}


testcases:
-
    name: 修改
    testcase: testcases/manage/appConfig/parameter/class/tc_updateParameterClass.yml
    parameters:
      title-id-orderNum-parameterClassCode-parameterClassDesc-parameterClassName-parameterClassStatus-parameterClassType-status-message:
        - ["id不传",null,1,$code,"描述","测试修改","1","2",913,"参数id必填!"]
        - ["id传空字符","",1,$code,"描述","测试修改","1","2",913,"参数id必填!"]
        - ["id值不存在","131020202",1,$code,"描述","测试修改","1","2",1113202,"参数分类已不存在"]
        - ["排序不传",$parameterId,null,$code,"描述","测试修改","1","2",913,"排序不能为空"]
        - ["排序传负值",$parameterId,-1,$code,"描述","测试修改","1","2",913,"排序支持输入1-6位数字"]
        - ["排序传字符串",$parameterId,test,$code,"描述","测试修改","1","2",801,"请求格式错误，请重试"]
        - ["排序传0",$parameterId,0,$code,"描述","测试修改","1","2",913,"排序支持输入1-6位数字"]
        - ["参数类编码不传",$parameterId,1,null,"描述","测试修改","1","2",913,"参数分类编码不能为空,参数分类编码必须在1-94之间"]
        - ["参数类编码传空字符串",$parameterId,1,"","描述","测试修改","1","2",913,"参数分类编码不能为空,参数分类编码必须在1-94之间"]
#        - ["参数类编码传不存在的值",$parameterId,1,"xxxx","描述","测试修改","1","2",200,"成功"]
        - ["参数类名称不传",$parameterId,1,$code,"描述",null,"1","2",913,"参数分类名称不能为空"]
        - ["参数类名称传空字符",$parameterId,1,$code,"描述","","1","2",913,"参数分类名称不能为空,参数分类名称必须在1-90之间"]
        - ["参数类名称长度90字符",$parameterId,1,$code,"描述","aaaaaaaaaabbbbbbbbbbccccccccccaaaaaaaaaabbbbbbbbbbccccccccccaaaaaaaaaabbbbbbbbbbcccccccccc","1","2",200,"成功"]
        - ["参数类名称长度超90字符",$parameterId,1,$code,"描述","aaaaaaaaaabbbbbbbbbbccccccccccaaaaaaaaaabbbbbbbbbbccccccccccaaaaaaaaaabbbbbbbbbbcccccccccc1","1","2",913,"参数分类名称必须在1-90之间"]
        - ["参数类状态不传",$parameterId,1,$code,"描述","测试修改",null,"2",913,"参数类状态不能为空"]
        - ["参数类状态非0、1、2",$parameterId,1,$code,"描述","测试修改","3","2",913,"填写的状态不支持"]
        - ["参数类状态传空",$parameterId,1,$code,"描述","测试修改","","2",913,"填写的状态不支持,状态不能为空"]
        - ["参数类类型不传",$parameterId,1,$code,"描述","测试修改","1",null,913,"参数类型不能为空"]
        - ["参数类类型非1、2",$parameterId,1,$code,"描述","测试修改","2","3",913,"填写的参数类型不支持"]
        - ["参数类类型传空",$parameterId,1,$code,"描述","测试修改","1","",913,"参数类型不能为空,填写的参数类型不支持"]