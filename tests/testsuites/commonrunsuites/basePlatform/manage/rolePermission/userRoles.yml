config:
    name: 查询角色列表
    variables:
      sp: " "
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(wsignwb01.main.orgNo)}
      worg01Code: ${ENV(wsignwb01.main.orgId)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
        ###用户离职之后又二次创建
#      resetCreateAccountNo: ${ENV(resetCreateAccountNo)}
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}

testcases:
-
    name: 查询角色列表
    parameters:
    - name-customAccountNo-userCode-customDepartmentNo-departmentCode-code0-message0:
#        - ["TC0-正常调用",$userCode1,$customAccountNo1,$departmentCode1,$customDepartmentNo1,$roleCode1,200,"成功"]
        - [ "TC1-customAccountNo和userCode都不传",null,null,"$org01No","",1114401,"用户编码或用户账号不能同时为空"]
        - [ "TC2-customAccountNo和userCode都为空","","","$org01No","",1114401,"用户编码或用户账号不能同时为空"]
        - [ "TC3-customAccountNo不传，userCode不存在","","XXXXXX","$org01No","",1114406,"}对应用户不存在或状态异常"]
        - [ "TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","",1114406,"}对应用户不存在或状态异常" ]
        - [ "TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","",1114407,"}对应用户不存在或状态异常" ]
        - [ "TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","",1114407,"}对应用户不存在或状态异常" ]
        - [ "TC7-customAccountNo正确，userCode错误","$sign01No","$wsign01Code","$org01No","",1114406,"对应用户不存在或状态异常" ]
        - [ "TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","$org01No","",200,"成功" ]
        - [ "TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","",1114406,"}对应用户不存在或状态异常" ]
        - [ "TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","",1114406,"}对应用户不存在或状态异常" ]
#        - [ "TC11-customAccountNo用户离职之后又创建成功","$resetCreateAccountNo","","$org01No","",200,"成功" ] #todo
        - [ "TC12-customDepartmentNo和departmentCode都不传","$sign01No","",null,null,200,"成功" ]
        - [ "TC13-customDepartmentNo和departmentCode都为空","$sign01No","","","",200,"成功" ]
        - [ "TC14-customDepartmentNo为空，departmentCode不存在","$sign01No","","","XXXXXX",1114408,"}对应部门不存在" ]
        - [ "TC15-customDepartmentNo为空，departmentCode相对方","$sign01No","","","$worg01Code",1114413,"departmentCode错误：用户不属于该机构编码{" ]
        - [ "TC16-customDepartmentNo不存在，departmentCode为空","$sign01No","","XXXXXX","",1114409,"}对应部门不存在" ]
        - [ "TC17-customDepartmentNo相对方账号，departmentCode为空","$sign01No","","$worg01No","",1114414,"customDepartmentNo错误：用户不属于该机构账号{" ]
        - [ "TC18-customDepartmentNo正确，departmentCode错误","$sign01No","","$org01No","XXXXXX",1114408,"}对应部门不存在" ]
        - [ "TC19-customDepartmentNo错误，departmentCode正确","$sign01No","","XXXXXX","$org01Code",200,"成功" ]
        - [ "TC20-departmentCode类型是部门","$sign01No","","$departmentNo","",200,"成功" ]
        - [ "TC21-departmentCode企业已删除","$sign01No","","$orgCodeDelete","",1114409,"对应部门不存在" ]

    testcase: testcases/manage/rolePermission/userRoles.yml

-
    name: 查询角色列表校验-roleName-pageSize-pageNo
    parameters:
      - name-roleName-pageSize-pageNo-code0-message0:
        - [ "TC1-roleName为空","",1,10,200,"成功"]
        - [ "TC2-roleName不存在","123",1,10,200,"成功"]
        - [ "TC3-roleName为不支持的特殊字符","!@#\\/:*?\"<>|",1,10,200,"成功"]
        - [ "TC4-pageSize为小数","",1.35,10,1114412,"不能小于1或大于50"]
        - [ "TC5-pageSize为负数","123",-1,10,1114412,"pageSize错误：单页数量{-1}不能小于1或大于50"]
        - [ "TC6-pageSize为字母","",abc,10,1114412,"不能小于1或大于50"]
        - [ "TC7-pageSize为1000","",1000,10,1114412,"不能小于1或大于50"]
        - [ "TC8-pageNo不存在","123",1,"abc",1114411,"不能小于1或大于10000000" ]
        - [ "TC9-pageNo为小数","",1,10.24,1114411,"不能小于1或大于10000000" ]
        - [ "TC10-pageNo为负数","123",1,-10,1114411,"pageNo错误：页码{-10}不能小于1或大于10000000" ]
        - [ "TC12-pageNo为1000","",10,10000001,1114411,"不能小于1或大于10000000" ]
    testcase: testcases/manage/rolePermission/userRoles.yml

-
    name: 角色取消授权
    testcase: testcases/manage/rolePermission/revoke.yml