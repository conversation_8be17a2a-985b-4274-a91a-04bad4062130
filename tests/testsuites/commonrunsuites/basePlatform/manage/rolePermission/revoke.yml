config:
    name: 角色授权
    variables:
      sp: " "
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(wsignwb01.main.orgNo)}
      worg01Code: ${ENV(wsignwb01.main.orgId)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
        ###用户离职之后又二次创建
#      resetCreateAccountNo: ${ENV(resetCreateAccountNo)}
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}
      departmentCode01: ${ENV(sign01.JZ.departCode)}

testcases:
-
    name: 角色授权校验参数
    parameters:
    - name-customAccountNo-userCode-departmentCode-customDepartmentNo-roleCode-code0-message0:
        - ["TC0-正常调用",$customAccountNo1,$userCode1,$departmentCode1,$customDepartmentNo1,$roleCode1,200,"成功"]
        - [ "TC1-customAccountNo和userCode都不传",null,null,"$org01No","","",1114401,"用户编码或用户账号不能同时为空"]
        - [ "TC2-customAccountNo和userCode都为空","","","$org01No","","",1114401,"用户编码或用户账号不能同时为空"]
        - [ "TC3-customAccountNo不传，userCode不存在",null,"XXXXXX","$org01No","","",1114406,"userCode错误：用户编码{XXXXXX}对应用户不存在或状态异常"]
        - [ "TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","","",1114406,"}对应用户不存在或状态异常" ]
        - [ "TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","","",1114407,"customAccountNo错误：用户账号{XXXXXX}对应用户不存在或状态异常" ]
        - [ "TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","","",1114407,"}对应用户不存在或状态异常" ]
        - [ "TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","","$org01No",$roleCode1,200,"成功" ]
        - [ "TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","","",1114406,"}对应用户不存在或状态异常" ]
        - [ "TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","","",1114406,"}对应用户不存在或状态异常" ]
#        - [ "TC11-customAccountNo用户离职之后又创建成功","$resetCreateAccountNo","","","$org01No",$roleCode1,200,"成功" ] todo 后期autodata中需要加此用户，
        - [ "TC12-customDepartmentNo和departmentCode都不传","$sign01No","",null,null,"",1114402,"机构编码或机构账号不能同时为空" ]
        - [ "TC13-customDepartmentNo和departmentCode都为空","$sign01No","","","","",1114402,"机构编码或机构账号不能同时为空" ]
        - [ "TC14-customDepartmentNo为空，departmentCode不存在","$sign01No","","XXXXXX","","",1114408,"departmentCode错误：部门编码{XXXXXX}对应部门不存在" ]
        - [ "TC15-customDepartmentNo为空，departmentCode相对方","$sign01No","","$worg01Code","","",1114413,"departmentCode错误：用户不属于该机构编码" ]
        - [ "TC16-customDepartmentNo不存在，departmentCode为空","$sign01No","","","XXXXXX","",1114409,"customDepartmentNo错误：部门账号{XXXXXX}对应部门不存在" ]
        - [ "TC17-customDepartmentNo相对方账号，departmentCode为空","$sign01No","","","$worg01No","",1114414,"customDepartmentNo错误：用户不属于该机构账号" ]
        - [ "TC18-customDepartmentNo正确，departmentCode错误","$sign01No","","XXXXXX","$org01No","",1114408,"departmentCode错误：部门编码{XXXXXX}对应部门不存在" ]
        - [ "TC19-customDepartmentNo错误，departmentCode正确","$sign01No","","$org01Code","XXXXXX",$roleCode1,200,"成功" ]
        - [ "TC20-departmentCode类型是部门","$sign01No","","$departmentCode01","",$roleCode1,200,"成功" ]
        - [ "TC21-departmentCode企业已删除","$sign01No","","$orgCodeDelete","",$roleCode1,1114408,"不存在" ]

    testcase: testcases/manage/rolePermission/revokeParamCheck.yml


-
    name: 角色授权校验参数-roleCode
    parameters:
      - name-roleCode1-code0-message0:
        - [ "TC1-roleCode为空","",1114403,"roleCode错误：角色编码{}对应的角色不存在"]
        - [ "TC2-roleCode不存在","123",1114403,"roleCode错误：角色编码{123}对应的角色不存在"]
        - [ "TC3-roleCode为不支持的特殊字符","#%",1114403,"roleCode错误：角色编码{#%}对应的角色不存在"]
    testcase: testcases/manage/rolePermission/revokeParamCheck.yml

-
    name: 角色取消授权
    testcase: testcases/manage/rolePermission/revoke.yml