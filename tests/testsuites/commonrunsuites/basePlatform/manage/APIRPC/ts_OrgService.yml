
config:
    name: "RpcOrgService"

testcases:
-
    name: 创建token、验证token
    testcase: testcases/manage/APIRPC/RpcTokenManageService/tc_createToken.yml

#-
#    name: 根据组织编码集合获取所在层级上级关系列表:返回本级及上级组织的信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getAllParentOrgByDeptCodeList.yml
#
#
#-
#    name: 根据组织编码获取部门的上级公司:如organizationCode为部门code，则返回所属公司信息，如organizationCode为公司code，则返回本公司信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getCompanyByDeptCode.yml
#
#
#-
#    name: 根据组织编码获取某一节点和所有下级(多级)列表:根据organizationCode获取该组织及下级组织信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizatioAllGradeListByCode.yml
#
#-
#    name: 通过组织id获取组织对象:根据ID获取组织信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationById.yml
#
#-
#    name: 根据organizationCode获取本组织信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationByOrganizationCode.yml
#
#-
#    name: 获取所有组织列表
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationList.yml
#
#-
#    name: 根据组织编码集合获取组织列表
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationListByCodeList.yml
#
#-
#    name: 根据组织名称模糊查询组织信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationListByOrganizationCodeName.yml
#
#-
#    name: 获取所有组织树
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrganizationTreeAll.yml
#
#-
#    name: 根据organizationCode获取本组织信息
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrgByPublicSearch.yml
#
#-
#    name: 根据组织编码和组织账号列表获取组织信息列表
#    testcase: testcases/manage/APIRPC/RpcOrgService/tc_getOrgListByOrgCodeAndAccountNumberList.yml