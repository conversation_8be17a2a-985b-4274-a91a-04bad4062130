config:
  name: "项目配置-对外接口管理权限开关校验-启用禁用生效有缓存"
  variables:
    numts: ${get_snowflake()}

testcases:
  - name: 校验项目-启用且开启【对外接口管理权限】
    testcase: testcases/manage/proconfig/projectScene1.yml

  - name: 校验项目-禁用且开启【对外接口管理权限】
    testcase: testcases/manage/proconfig/projectScene2.yml

  - name: 校验项目-禁用且关闭【对外接口管理权限】
    testcase: testcases/manage/proconfig/projectScene3.yml

  - name: 校验项目-启用且关闭【对外接口管理权限】
    testcase: testcases/manage/proconfig/projectScene4.yml

  - name: 校验项目-启用但又删除了
    testcase: testcases/manage/proconfig/projectScene5.yml

  - name: 校验项目-接口权限设置-校验接口明细
    testcase: testcases/manage/proconfig/projectOpenApiListScene1.yml