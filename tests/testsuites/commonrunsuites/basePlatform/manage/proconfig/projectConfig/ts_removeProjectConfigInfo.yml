config:
    name: "[内]03-应用配置-项目配置-删除项目配置信息"
    variables:
      num: ${get_snowflake()}
      data1:
        {
          "customerIP": "",
          "deptId": "",
          "domain": "",
          "params": {
            "depositUrlTime": "60",
            "esignAppId": "id$num",
            "esignAppSecret": "",
            "evidenceSwitch": "2",
            "fileUrlType": "1",
            "id": null,
            "internalSignUrl": "1",
            "ipList": [ ],
            "projectDesc": "",
            "projectName": "醉生测试$num",
            "shortLinkTime": "1",
            "downloadLinkTime": "32",
            "openApiAuthStatus": "0",
            "signCallbackUrl": ""
          },
          "platform": "",
          "tenantCode": 1000,
          "userCode": "admin"
        }
      res1: ${save_project_config_info($data1)}
      updId1: ${get_project_config_by_name(醉生测试$num)}

testcases:

-
    name: 自动化测试删除项目配置信息字段校验-$title1
    testcase: testcases/manage/proconfig/projectConfig/tc_removeProjectConfigInfo.yml
    parameters:
       title1-message1-code1-id1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["id为空","id不能为空",913,null,"","","","","1000",""]
         - ["id不传","id不能为空",913,"","","","","","1000",""]

-
    name: 自动化测试删除项目配置信息场景校验-$title1
    testcase: testcases/manage/proconfig/projectConfig/tc_removeProjectConfigInfo2.yml
    parameters:
      title1-message1-code1-id1-projectName1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - ["删除新增的项目配置信息成功","成功",200,"$updId1","醉生测试$num","","","","","1000",""]
