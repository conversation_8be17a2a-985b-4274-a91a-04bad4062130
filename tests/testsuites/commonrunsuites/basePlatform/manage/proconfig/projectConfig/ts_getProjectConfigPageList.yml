config:
    name: "[内]03-应用配置-项目配置-获取项目配置分页列表"
    variables:
      num: ${get_snowflake()}
      data1:
        {
          "customerIP": "",
          "deptId": "",
          "domain": "",
          "params": {
            "depositUrlTime": "60",
            "esignAppId": "id$num",
            "esignAppSecret": "",
            "evidenceSwitch": "2",
            "fileUrlType": "1",
            "id": null,
            "internalSignUrl": "1",
            "ipList": [ ],
            "projectDesc": "",
            "projectName": "醉生测试$num",
            "shortLinkTime": "1",
            "openApiAuthStatus": "0",
            "downloadLinkTime": "32",
            "signCallbackUrl": ""
          },
          "platform": "",
          "tenantCode": 1000,
          "userCode": "admin"
        }
      res1: ${save_project_config_info($data1)}
      updId1: ${get_project_config_by_name(醉生测试$num)}

testcases:

-
    name: 自动化测试获取项目配置分页列表字段校验-$title1
    testcase: testcases/manage/proconfig/projectConfig/tc_getProjectConfigPageList.yml
    parameters:
       title1-message1-code1-currPage1-pageSize1-projectId1-projectName1-projectStatus1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["每页记录数为空","每页记录数不能为空",913,1,null,"","","","","","","","1000",""]
         - ["每页记录数小于1","每页记录数不能小于1",913,1,0,"","","","","","","","1000",""]
         - ["每页记录数大于100","每页记录数不能大于100",913,1,101,"","","","","","","","1000",""]
         - ["当前页数为空","当前页数不能为空",913,null,5,"","","","","","","","1000",""]
         - ["当前页数小于1","当前页号不能小于1",913,0,5,"","","","","","","","1000",""]
         - ["当前页数大于10000000","当前页号不能大于10000000",913,10000001,5,"","","","","","","","1000",""]

-
  name: 自动化测试获取项目配置分页列表场景校验-$title1
  testcase: testcases/manage/proconfig/projectConfig/tc_getProjectConfigPageList2.yml
  parameters:
    title1-message1-code1-currPage1-pageSize1-projectId1-projectName1-projectStatus1-updId2-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
      - ["能查询出新增的项目配置","成功",200,1,10,"","醉生测试$num","","$updId1","","","","","1000",""]


-
  name: 删除新增的项目配置信息
  testcase: testcases/manage/proconfig/projectConfig/tc_removeProjectConfigInfo.yml
  parameters:
    title1-message1-code1-id1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
      - ["接口成功返回","成功",200,"$updId1","","","","","1000",""]