config:
    name: "[内]03-应用配置-项目配置-新增项目配置信息"
    variables:
      num: ${get_snowflake()}

testcases:

-
    name: 自动化测试新增项目配置信息字段校验-$title1
    testcase: testcases/manage/proconfig/projectConfig/tc_saveProjectConfigInfo.yml
    parameters:
       title1-message1-code1-projectName1-projectDesc1-esignAppId1-esignAppSecret1-internalSignUrl1-fileUrlType1-depositUrlTime1-shortLinkTime1-signCallbackUrl1-evidenceSwitch1-id1-ipList1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
         - ["项目名称为空","项目名称不能为空",913,null,"描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["项目名称长度小于1","项目名称的长度必须在1-30之间",913,"","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["项目名称长度大于30","项目名称的长度必须在1-30之间",913,"12345678901234567890123456789011","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["描述长度大于200","描述的长度必须在0-200之间",913,"醉生测试","描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百描述测试大于二百二百","EsignAppId","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["EsignAppId长度大于100","esign appid的长度必须在0-100之间",913,"醉生测试","描述","appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid123451","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["EsignAppSecret长度大于100","esign appsecret的长度必须在0-100之间",913,"醉生测试","描述","EsignAppId","appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid123451","1","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["内部用户签署地址为空","内部用户签署地址不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret",null,"1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["内部用户签署地址不传","内部用户签署内部用户签署地址(1内网2外网)填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["内部用户签署地址传规定以外","内部用户签署内部用户签署地址(1内网2外网)填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","3","1","60","1","backurl","1","",[],"","","","","1000",""]
         - ["文件下载地址为空","文件下载地址不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1",null,"60","1","backurl","1","",[],"","","","","1000",""]
         - ["文件下载地址不传","文件下载地址(1内网2外网)填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","","60","1","backurl","1","",[],"","","","","1000",""]
         - ["文件下载地址传规定以外","文件下载地址(1内网2外网)填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","3","60","1","backurl","1","",[],"","","","","1000",""]
         - ["存证凭证查看地址有效期为空","存证凭证查看地址有效期不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1",null,"1","backurl","1","",[],"","","","","1000",""]
         - ["存证凭证查看地址有效期不传","存证凭证查看地址有效期不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","","1","backurl","1","",[],"","","","","1000",""]
         - ["短连接有效期为空","短链接有效期不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60",null,"backurl","1","",[],"","","","","1000",""]
         - ["短连接有效期不传","有效期范围为1-99天",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","","backurl","1","",[],"","","","","1000",""]
         - ["短连接有效期传规定以外","有效期范围为1-99天",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","100","backurl","1","",[],"","","","","1000",""]
         - ["签署回调地址长度大于200","签署回调地址的长度必须在0-200之间",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","1","appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid123451appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid12345appid123451","1","",[],"","","","","1000",""]
         - ["存证开关为空","存证开关不能为空",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl",null,"",[],"","","","","1000",""]
         - ["存证开关不传","存证开关（1开启，2关闭）填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","","",[],"","","","","1000",""]
         - ["存证开关传规定以外","存证开关（1开启，2关闭）填写的类型不正确",913,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","3","",[],"","","","","1000",""]
         - ["存证凭证查看地址有效期包含非数字","只支持数字(包含一位小数)",1122000,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","ads","1","backurl","1","",[],"","","","","1000",""]
         - ["Esign appId和appSecret配置不正确","Esign appId和appSecret配置不正确，请检查配置，保存失败",1122006,"醉生测试","描述","EsignAppId","EsignAppSecret","1","1","60","1","backurl","1","",[],"","","","","1000",""]



-
    name: 自动化测试新增项目配置信息场景校验-$title1
    testcase: testcases/manage/proconfig/projectConfig/tc_saveProjectConfigInfo2.yml
    parameters:
      title1-message1-code1-projectName1-projectDesc1-esignAppId1-esignAppSecret1-internalSignUrl1-fileUrlType1-depositUrlTime1-shortLinkTime1-signCallbackUrl1-evidenceSwitch1-id1-ipList1-customerIP1-deptId1-domain1-platform1-tenantCode1-userCode1:
        - ["新增项目配置信息成功","成功",200,"醉生测试$num","描述","EsignAppId","","1","1","60","1","backurl","1","",[ ],"","","","","1000","" ]

