
config:
    name: "编辑内部组织用例集"
    variables:
        setup_data1:
              {
                  "customOrgNo": ZDHCSBJNBZZ,
                  "name": 自动化测试编辑内部组织,
                  "organizationType": COMPANY,
                  "parentOrgNo": ,
                  "parentCode": "0",
                  "licenseType": ,
                  "licenseNo": ,
                  "legalRepAccountNo": ,
                  "legalRepUserCode":
              }
        setup_res1: ${create_inner_organizations($setup_data1)}
        setup_organizationCode: ${get_organizationCode($setup_res1)}


testcases:
-
    name: 测试编辑内部组织名称-特殊字符
    testcase: testcases/manage/InnerOrganizations/update/tc_name_1.yml
    parameters:
        organizationCode-title_-name_-customOrgNo_-organizationType_-parentOrgNo_-parentCode_-licenseType_-licenseNo-except_code_-except_message_:
            - [$setup_organizationCode,"组织名称含特殊字符:_1","自动化测试编辑组织名称:_1","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称含特殊字符/_2","自动化测试编辑组织名称/_2","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称含特殊字符\_3","自动化测试编辑组织名称\\_3","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称含特殊字符*_4","自动化测试编辑组织名称*_4","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
##            - ["组织名称含特殊字符<_5","自动化测试编辑组织名称<_5","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
##            - ["组织名称含特殊字符>_6","自动化测试编辑组织名称>_6","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称含特殊字符?_7","自动化测试编辑组织名称?_7","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称含特殊字符|_8","自动化测试编辑组织名称|_8","ZDHCSBJNBZZ","COMPANY","","0","","",200,"成功"]
            - [$setup_organizationCode,"组织名称1字符_9","自","ZDHCSBJNBZZ","COMPANY","","0","","",1111290,"组织名称输入长度应为2-50字"]
            - [$setup_organizationCode,"组织名称51字符_10","自动化测试编辑组织名称0123456789012345678901234567890123456789","ZDHCSBJNBZZ","COMPANY","","0","","",1111290,"组织名称输入长度应为2-50字"]


-
    name: 测试编辑内部组织名称-场景校验
    testcase: testcases/manage/InnerOrganizations/update/tc_name_2.yml


-
    name: 测试编辑内部组织账号-场景校验
    testcase: testcases/manage/InnerOrganizations/update/tc_customOrgNo_2.yml


-
    name: 测试编辑内部组织上级组织编号、上级组织账号-场景校验
    testcase: testcases/manage/InnerOrganizations/update/tc_parentCode_parentOrgNo.yml


-
    name: 测试编辑内部组织上级组织账号-特殊字符校验
    testcase: testcases/manage/InnerOrganizations/update/tc_parentOrgNo.yml
    parameters:
        organizationCode-title_3-parentOrgNo_3-except_code_3-except_message_3:
            - [$setup_organizationCode,"上级组织账号含特殊字符:_1","ZDHCSBJNBZ:Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符/_2","ZDHCSBJNBZ/Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符\_3","ZDHCSBJNBZ\\Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符*_4","ZDHCSBJNBZ*Z",1111286,"上级组织不存在"]
#            - ["上级组织账号含特殊字符<_5","ZDHCSBJNBZ<Z",913,"上级组织不存在"]
#            - ["上级组织账号含特殊字符>_6","ZDHCSBJNBZ>Z",913,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符?_7","ZDHCSBJNBZ?Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符|_8","ZDHCSBJNBZ|Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符\"_9","ZDHCSBJNBZ\"Z",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号含特殊字符中文_10","ZDHCSBJNBZ中文",1111286,"上级组织不存在"]
#            - [$setup_organizationCode,"上级组织账号含特殊字符空格_11","ZDHCSBJNBZ",1111286,"上级组织不存在"]
            - [$setup_organizationCode,"上级组织账号长度超过30字符_12","ZDHCSBJNBZ012345678901234567890",1111286,"上级组织不存在"]


-
    name: 测试编辑内部组织统一社会信用代码-特殊字符校验
    testcase: testcases/manage/InnerOrganizations/update/tc_licenseNo_licenseType_1.yml


-
    name: 测试编辑内部组织法人代表
    testcase: testcases/manage/InnerOrganizations/update/tc_legalRepAccountNo_legalRepUserCode.yml


-
    name: 测试编辑内部组织统一社会信用代码-特殊字符校验
    testcase: testcases/manage/InnerOrganizations/update/tc_licenseNo_licenseType_2.yml
    parameters:
        organizationCode-title_4-licenseType_4-licenseNo_4-except_code_4-except_message_4:
            - [$setup_organizationCode,"统一社会信用代码含特殊字符:_1","CREDIT_CODE","****************:7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符/_2","CREDIT_CODE","****************/7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符\_3","CREDIT_CODE","****************\\7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符*_4","CREDIT_CODE","*****************7",1111287,"证件号码格式错误"]
#            - ["统一社会信用代码含特殊字符<_5","CREDIT_CODE","****************<7",1111287,"证件号码格式错误"]
#            - ["统一社会信用代码含特殊字符>_6","CREDIT_CODE","****************>7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符?_7","CREDIT_CODE","****************?7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符|_8","CREDIT_CODE","****************|7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符\"_9","CREDIT_CODE","****************\"7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符中文_10","CREDIT_CODE","****************中7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码含特殊字符空格_11","CREDIT_CODE","**************** 7",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"统一社会信用代码长度超过18字符_12","CREDIT_CODE","****************K71",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度小于13字符_13","REGIST_NUMBER","123456789012",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度等于14字符_14","REGIST_NUMBER","12345678901234",1111287,"证件号码格式错误"]
            - [$setup_organizationCode,"工商注册号长度大于15字符_15","REGIST_NUMBER","1234567890123456",1111287,"证件号码格式错误"]
            - [$setup_organizationCode, "工商注册号含特殊字符:_16","CREDIT_CODE","12345678901:7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode,"工商注册号含特殊字符/_17","CREDIT_CODE","12345678901/7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符\_18","CREDIT_CODE","12345678901\\7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符*_19","CREDIT_CODE","12345678901*7",1111287,"证件号码格式错误" ]
#            - [ "工商注册号含特殊字符<_20","CREDIT_CODE","12345678901<7",1111287,"证件号码格式错误" ]
#            - [ "工商注册号含特殊字符>_21","CREDIT_CODE","12345678901>7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符?_22","CREDIT_CODE","12345678901?7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符|_23","CREDIT_CODE","12345678901|7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符\"_24","CREDIT_CODE","12345678901\"7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符中文_25","CREDIT_CODE","12345678901中7",1111287,"证件号码格式错误" ]
            - [$setup_organizationCode, "工商注册号含特殊字符空格_26","CREDIT_CODE","12345678901 7",1111287,"证件号码格式错误" ]

-
    name: 创建内部用户特殊字符验证
    testcase: testcases/manage/InnerOrganizations/update/teardown_delete.yml
    parameters:
        organizationCode:
          - [ $setup_organizationCode ]
          -
-
    name: 编辑内部组织-字段证书计费项目certChargingProject校验
    testcase: testcases/manage/InnerOrganizations/update/tc_certChargingProject.yml
