
config:
    name: "创建内部组织用例集"

testcases:
-
    name: 测试组织名称
    testcase: testcases/manage/InnerOrganizations/create/tc_name.yml

-
    name: 测试组织账号
    testcase: testcases/manage/InnerOrganizations/create/tc_customOrgNo.yml

-
    name: 测试部门分类
    testcase: testcases/manage/InnerOrganizations/create/tc_organizationType.yml

-
    name: 测试上级组织编码以及上级组织账号
    testcase: testcases/manage/InnerOrganizations/create/tc_parentCode_parentOrgNo.yml

-
    name: 测试证件类型以及证件号码
    testcase: testcases/manage/InnerOrganizations/create/tc_licenseType_licenseNo.yml


-
    name: 测试创建-内部组织工商注册号-特殊字符校验
    testcase: testcases/manage/InnerOrganizations/create/tc_licenseType_licenseNo_2.yml
    parameters:
        name_4-customOrgNo_4-licenseType_4-licenseNo_4-except_code_4-except_message_4:
            - ["工商注册号长度12字符","ZDHCSZZHM_1","REGIST_NUMBER","123456789012",1111287,"证件号码格式错误"]
            - ["工商注册号长度14字符","ZDHCSZZHM_2","REGIST_NUMBER","12345678901234",1111287,"证件号码格式错误"]
            - ["工商注册号长度16字符","ZDHCSZZHM_3","REGIST_NUMBER","1234567890123456",1111287,"证件号码格式错误"]
#            - ["工商注册号含特殊字符小于号","ZDHCSZZHM_5","REGIST_NUMBER","123456789012<",1111287,"证件号码格式错误"]
#            - ["工商注册号含特殊字符大于号","ZDHCSZZHM_6","REGIST_NUMBER","123456789012>",1111287,"证件号码格式错误"]
            - ["工商注册号含特殊字符问号","ZDHCSZZHM_7","REGIST_NUMBER","123456789012?",1111287,"证件号码格式错误"]
            - ["工商注册号含特殊字符分隔符","ZDHCSZZHM_8","REGIST_NUMBER","123456789012|",1111287,"证件号码格式错误"]
            - ["工商注册号含特殊字符双引号","ZDHCSZZHM_9","REGIST_NUMBER","123456789012\"",1111287,"证件号码格式错误"]
            - ["工商注册号含特殊字符中文","ZDHCSZZHM_10","REGIST_NUMBER","123456789012中",1111287,"证件号码格式错误"]

-
    name: 新建内部组织-当证件号/账号重复时，failureData里返回已有组织信息organizationCode/customOrgNo
    testcase: testcases/manage/InnerOrganizations/create/tc_licenseType_licenseNo_3.yml

-
    name: 创建内部组织-证件类型为其他时
    testcase: testcases/manage/InnerOrganizations/create/other_document_types.yml

-
    name: 新建内部组织-字段证书计费项目certChargingProject校验
    testcase: testcases/manage/InnerOrganizations/create/tc_certChargingProject.yml