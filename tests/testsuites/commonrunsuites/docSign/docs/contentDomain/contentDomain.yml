#config:
#    name: "新增内容域"
#
#testcases:
#  - name: 新增内容域
#    testcase: testcases/docs/contentDomain/test_addContentDomain.yml
#
#  - name: 新增内容域-校验
#    testcase: testcases/docs/contentDomain/test_addContentDomain_check.yml
#
#  - name: 新增内容域-权限
#    testcase: testcases/docs/contentDomain/test_addContentDomain_permissions.yml
#
#  - name: 删除内容域
#    testcase: testcases/docs/contentDomain/test_deleteContentDomain.yml
#
#  - name: 获取内容域列表
#    testcase: testcases/docs/contentDomain/test_getContentDomainList.yml
#
#  - name: 新建内容域_内容域数据源来源内部组织
#    testcase: testcases/docs/contentDomain/test_getDataSource.yml
#
#  - name: 更新内容域
#    testcase: testcases/docs/contentDomain/test_updateContentDomain.yml
#
#  - name: 内容域校验
#    testcase: testcases/docs/contentDomain/test_validContentDomain.yml
#
#
#  - name: 新增内容域属性可修改属性
#    testcase: testcases/docs/contentDomain/test_addContentDomain-allowEdit.yml
#
#  - name: 更新内容域属性可修改属性
#    testcase: testcases/docs/contentDomain/test_updateContentDomain-allowEdit.yml
#
#
