config:
    name: "文件配置"

testcases:
  - name: 文件配置场景：一个正常的操作流程场景
    testcase: testcases/docs/docConfigure/docConfigureList_scene.yml

  - name: 文件配置：新建子级文件类型
    testcase: testcases/docs/docConfigure/test_addChildDocFolder.yml

  - name: 文件配置：新建子级文件类型
    testcase: testcases/docs/docConfigure/test_addChildDocType.yml

  - name: 新建同级文件夹
    testcase: testcases/docs/docConfigure/test_addSameLevelDocFolder.yml

  - name: 删除文件夹
    testcase: testcases/docs/docConfigure/test_deleteDocFolder.yml


  - name: 删除文件类型
    testcase: testcases/docs/docConfigure/test_deleteDocType.yml

  - name: 左侧文件类型树区域搜索
    testcase: testcases/docs/docConfigure/test_docConfigureList.yml

  - name: 左侧文件类型树区域搜索
    testcase: testcases/docs/docConfigure/test_getDocConfigureList.yml

  - name: 无权限文件配置列表搜索
    testcase: testcases/docs/docConfigure/test_getDocConfigureListWithoutPermission.yml

  - name: 查看文件夹
    testcase: testcases/docs/docConfigure/test_queryDocFolder.yml

  - name: 查看文件类型
    testcase: testcases/docs/docConfigure/test_queryDocType.yml
  - name: 移动文件夹
    testcase: testcases/docs/docConfigure/test_removeDocFolder.yml
  - name: 移动文件类型
    testcase: testcases/docs/docConfigure/test_removeDocType.yml
  - name: 编辑文件类型
    testcase: testcases/docs/docConfigure/test_updateDocType.yml

  - name: 编辑文件夹
    testcase: testcases/docs/docConfigure/test_updateFolder.yml