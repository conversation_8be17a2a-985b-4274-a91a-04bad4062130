config:
    name: "文件模板"
    variables:
      userCode0: $${ENV(sign01.userCode)}
      code65: ${generate_random_str(65)}
      code64: ${generate_random_str(64)}
      customAccountNo0: $${ENV(sign01.accountNo)}
      accountNo301: ${generate_random_str(301)}
      accountNo300: ${generate_random_str(300)}
      organizationCode0: $${ENV(sign01.main.orgCode)}
      customOrgNo0: $${ENV(sign01.main.orgNo)}
      templateName1: 测试模版PDF${generate_random_str(3)}
      templateName101: ${generate_random_str(101)}
      templateName100: ${generate_random_str(100)}
      fileKey0: $${ENV(fileKey)}
      pngPageFileKey: $${ENV(pngPageFileKey)}
      docTypeId0: ${get_a_docConfig_type()}
      docTypeName0: "autotest通用文件类型*************"
      docTypeName51: ${generate_random_str(51)}
      description201: ${generate_random_str(201)}
      redirectUrl501: ${generate_random_str(501)}
      redirectUrl500: ${generate_random_str(500)}
      organizationCode1: $${ENV(businessorganizationCode)}
      customOrgNo1: $${ENV(businesscustomOrgNo)}
      wsignwb01userCode: $${ENV(wsignwb01.userCode)}
      wsignwb01orgNo: $${ENV(wsignwb01.main.orgNo)}
      ceswdzxzdhyhwgd1orgName: $${ENV(ceswdzxzdhyhwgd1.orgName)}
      ceswdzxzdhyhwgd1orgNo: $${ENV(ceswdzxzdhyhwgd1.orgNo)}

testcases:
  - name: 创建文件模板
    parameters:
      - name-userCode-customAccountNo-organizationCode-customOrgNo-templateName-docTypeId-docTypeName-description-fileKey-code-message:
          - [ "P1TL-创建人userCode、customAccountNo 为空字符串", "", "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605104, "userCode和customAccountNo二选一必填" ]
          - [ "P3TL-创建人userCode字符长度65超出校验", $code65, "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1600017, "创建人用户编码长度不可超出64字符！" ]
          - [ "P4TL-创建人userCode字符长度64边界值校验", $code64, "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605107, "创建人不存在" ]
          - [ "P5TL-创建人customAccountNo字符长度301超出校验", "", $accountNo301,"","","测试模版123哈",$docTypeId0,"","",$fileKey0,1600017, "创建人用户账号长度不可超出300字符！" ]
          - [ "P6TL-创建人customAccountNo字符长度300边界值校验", "", $accountNo300,"","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605107, "创建人不存在" ]
          - [ "P7TL-创建人userCode、customAccountNo指定相对方用户", $wsignwb01userCode, "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605107, "创建人不存在" ]
          - [ "P8TL-创建人userCode、customAccountNo指定内部用户已注销", "ceszxyh", "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605107, "创建人不存在" ]
          - [ "P9TL-创建人userCode、customAccountNo指定内部用户已离职", "cesfqrlz", "","","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605107, "创建人不存在" ]
          - [ "P10TL-创建人organizationCode、customOrgNo为均填写,为签署主体内部企业,docTypeName", $userCode0, "",$organizationCode0,$customOrgNo0,$templateName1,"",$docTypeName0,"",$fileKey0,200, "成功" ]
          - [ "P11TL-创建人organizationCode字符长度65超出校验", $userCode0, "",$code65,"","测试模版123哈",$docTypeId0,"","",$fileKey0,1600017, "创建人组织编码长度不可超出64字符！" ]
          - [ "P12TL-创建人organizationCode字符长度64边界值校验", $userCode0, "",$code64,"","测试模版123哈",$docTypeId0,"","",$fileKey0,1605108, "创建人组织不存在" ]
          - [ "P13TL-创建人customOrgNo字符长度301超出校验", $userCode0, "","",$accountNo301,"测试模版123哈",$docTypeId0,"","",$fileKey0,1600017, "创建人组织账号长度不可超出300字符！" ]
          - [ "P14TL-创建人customOrgNo字符长度300边界值校验", $userCode0, "","",$accountNo300,"测试模版123哈",$docTypeId0,"","",$fileKey0,1605108, "创建人组织不存在" ]
          - [ "P15TL-创建人organizationCode不存在", $userCode0, "","不存在organizationCode","","测试模版123哈",$docTypeId0,"","",$fileKey0,1605108, "创建人组织不存在" ]
          - [ "P16TL-创建人customOrgNo不存在", $userCode0, "","","不存在customOrgNo","测试模版123哈",$docTypeId0,"","",$fileKey0,1605108, "创建人组织不存在" ]
          - [ "P17TL-创建人userCode存在，organizationCode不匹配", $userCode0, "",$organizationCode1,"","测试模版123哈",$docTypeId0,"","",$fileKey0,1605109, "创建人组织非创建人所属组织" ]
          - [ "P18TL-创建人userCode存在，customOrgNo不匹配", "", $customAccountNo0,"",$customOrgNo1,"测试模版123哈",$docTypeId0,"","",$fileKey0,1605109, "创建人组织非创建人所属组织" ]
          - [ "P19TL-创建人userCode、customAccountNo指定内部用户，organizationCode、customOrgNo：相对方企业", "", $customAccountNo0,"",$wsignwb01orgNo,"测试模版123哈",$docTypeId0,"","",$fileKey0,1605108, "创建人组织不存在" ]
          - [ "P20TL-templateName文件模板名称字符长度101超出校验", $userCode0, "","",$customOrgNo0,$templateName101,$docTypeId0,"","",$fileKey0,1600017, "模板名称最大长度100位" ]
          - [ "P20TL-templateName文件模板名称字符长度100边界值校验", "", $customAccountNo0,$organizationCode0,"",$templateName100,$docTypeId0,"","",$fileKey0,200, "成功" ]
          - [ "P21TL-templateName文件模板名称为空", $userCode0, "","",$customOrgNo0,"",$docTypeId0,"","",$fileKey0,1600017, "模板名称不能为空" ]
          - [ "P22TL-templateName文件模板名称存在特殊字符", "", $customAccountNo0,"",$customOrgNo0,"模板名称包含特殊字符/:*?<>|",$docTypeId0,"","",$fileKey0,1600017, "模板名称包含特殊字符/:*?\"<>|" ]
          - [ "P23TL-templateName文件模板名称重复", $userCode0, "",$organizationCode0,"",$templateName1,$docTypeId0,"","",$fileKey0,1605021, "文档模板名称重复" ]
          - [ "P24TL-docTypeId、docTypeName 为空字符串", $userCode0, "",$organizationCode0,"","测试模版123哈","","","",$fileKey0,1605104, "docTypeId和docTypeName二选一必填" ]
          - [ "P25TL-docTypeId文件类型id不存在", $userCode0, "","",$customOrgNo0,"测试模版123哈","11111","","",$fileKey0,1605004, "文件类型未找到" ]
          - [ "P26TL-docTypeId文件类型id字符长度超出校验", $userCode0, "",$organizationCode0,"","测试模版123哈","docTypeId文件类型id字符长度超出校验docTypeId文件类型i","","",$fileKey0,1600017, "文档类型ID长度不可以超出36字符！" ]
          - [ "P27TL-docTypeName文件类型名称不存在", $userCode0, "","",$customOrgNo0,"测试模版123哈","","AAAAAA","",$fileKey0,1605004, "文件类型未找到" ]
          - [ "P28TL-docTypeName文件类型名称字符长度51超出校验", $userCode0, "","",$customOrgNo0,"测试模版123哈","",$docTypeName51,"",$fileKey0,1600017, "文件类型名称长度不能超过50！" ]
          - [ "P29TL-docTypeName文件类型名称存在特殊字符", "", $customAccountNo0,"",$customOrgNo0,"测试模版123哈","","文件类型名称包含特殊字符/:*?<>|","",$fileKey0,1600017, "文件类型名称包含特殊字符/:*?\"<>|" ]
          - [ "P30TL-description模版说明字符长度201超出校验", "", $customAccountNo0,"",$customOrgNo0,"测试模版123哈",$docTypeId0,"",$description201,$fileKey0,1600017, "模板说明最大长度200位" ]
          - [ "P31TL-fileKey文件为空", $userCode0, "",$organizationCode0,"","测试模版123哈",$docTypeId0,"","","",1605110, "文件fileKey不存在" ]
          - [ "P32TL-fileKey文件支持格式png", $userCode0, "",$organizationCode0,"","测试模版123哈",$docTypeId0,"","",$pngPageFileKey,200, "成功" ]
    testcase: testcases/docs/template/createTemplatecases.yml

  - name: 变更文件模板状态
    parameters:
      - name-templateId-templateName-updateStatus-code-message:
          - [ "P1TL-编辑模版templateId、templateName为空字符串", "", "",1,1605104, "templateId和templateName二选一必填"]
          - [ "P2TL-编辑模版templateId文件模板id不存在", "AAAA哈哈哈", "",1,1605113, "文件模板不存在" ]
          - [ "P3TL-templateName文件模板名称字符长度101超出校验", "",$templateName101,0,1600017, "模板名称最大长度100位"]
          - [ "P4TL-templateName文件模板名称字符长度100边界值校验，状态为草稿变更为已发布", "",$templateName100,0,200, "成功" ]
          - [ "P5TL-templateName文件模板名称存在特殊字符", "","模板名称包含特殊字符/:*?<>|",0,1600017, "模板名称包含特殊字符/:*?\"<>|" ]
          - [ "P6TL-templateId、templateName均填写，状态非0或1", "",$templateName1,5,1600017, "文件模板状态仅支持0或者1" ]
          - [ "P7TL-文件模板状态为草稿变更为停用", "", $templateName1,1,1605106, "文件模板当前状态为草稿，暂不支持停用" ]
          - [ "P8TL-状态为已发布变更为已停用", "",$templateName100,1,200, "成功" ]
          - [ "P9TL-状态为已停用再重复停用", "",$templateName100,1,1605003, "该模板状态已变更无法进行此操作，请刷新页面查看模板最新状态"]
          - [ "P10TL-状态为为草稿变更为已发布", "",$templateName1,0,200, "成功" ]
    testcase: testcases/docs/template/updateStatuscases.yml


  - name: 获取编辑文件模板页面
    parameters:
      - name-templateId-templateName-redirectUrl-code-message:
          - [ "P1TL-编辑模版templateId、templateName为空字符串", "", "","",1605104, "templateId和templateName二选一必填" ]
          - [ "P2TL-编辑模版templateId文件模板id不存在", "AAAA哈哈哈", "","",1605113, "文件模板不存在" ]
          - [ "P3TL-templateName文件模板名称字符长度101超出校验", "",$templateName101,"",1600017, "模板名称最大长度100位" ]
          - [ "P4TL-templateName文件模板名称字符长度100边界值校验", "",$templateName100,"",200, "成功" ]
          - [ "P5TL-templateName文件模板名称存在特殊字符", "","模板名称包含特殊字符/:*?<>|","",1600017, "模板名称包含特殊字符/:*?\"<>|" ]
          - [ "P7TL-redirectUrl文档创建完成重定向地址为超出长度501校验", "AAAA哈哈哈", "",$redirectUrl501,1600017, "重定向地址长度不可以超过500字符！" ]
          - [ "P8TL-redirectUrl文档创建完成重定向地址长度500边界值校验", "", $templateName100,$redirectUrl500,200, "成功" ]
          - [ "P9TL-文件模板状态为已发布进行获取编辑", "",$templateName1,"",1605114, "文件模板已发布，暂不支持编辑" ]
    testcase: testcases/docs/template/getTemplateEditUrlcases.yml

  - name: 复制文件模板
    parameters:
      - name-templateId-templateName-renameTemplate-code-message-data:
          - [ "P1TL-复制模版templateId、templateName为空字符串", "", "","",1605104, "templateId和templateName二选一必填",null ]
          - [ "P2TL-复制模版templateId文件模板id不存在", "AAAA哈哈哈", "","",1605113, "文件模板不存在" ,null]
          - [ "P3TL-templateName文件模板名称字符长度101超出校验", "",$templateName101,"",1600017, "模板名称最大长度100位" ,null]
          - [ "P4TL-templateName文件模板名称字符长度边界值校验，renameTemplate不填写", "",$templateName100,"",1605115, "模板名称最大长度100位" ,null]
          - [ "P5TL-templateName文件模板名称存在特殊字符", "","模板名称包含特殊字符/:*?<>|","",1600017, "模板名称包含特殊字符/:*?\"<>|" ,null]
          - [ "P6TL-templateId、templateName均填写，renameTemplate已存在", "",$templateName1,$templateName1,1605021, "文档模板名称重复" ,null]
    testcase: testcases/docs/template/copycases.yml

  - name: TemplateTC.yml(场景用例)
    parameters:
    testcase: testcases/docs/template/TemplateTC.yml

  - name: 删除文件模板
    parameters:
      - name-templateId-templateName-code-message-data:
          - [ "P1TL-删除模版templateId、templateName为空字符串", "", "",1605104, "templateId和templateName二选一必填",null ]
          - [ "P2TL-删除模版templateId文件模板id不存在", "AAAA哈哈哈", "",1605113, "文件模板不存在",null ]
          - [ "P3TL-templateName文件模板名称字符长度101超出校验", "",$templateName101,1600017, "模板名称最大长度100位",null ]
          - [ "P4TL-templateName文件模板名称字符长度100边界值校验", "",$templateName100,200, "成功",null ]
          - [ "P5TL-文件模板状态为已发布进行删除", "",$templateName1,1605105, "文件模板已发布，暂不支持删除",null ]
          - [ "P6TL-删除fileKey文件为png格式的模板", "","测试模版123哈",200, "成功",null ]
    testcase: testcases/docs/template/deletecases.yml
