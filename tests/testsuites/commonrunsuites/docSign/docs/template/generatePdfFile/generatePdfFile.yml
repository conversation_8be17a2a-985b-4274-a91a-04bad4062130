config:
    name: "模板"

testcases:
  - name: 模板控件校验-多选非必填测试
    testcase: testcases/docs/template/generatePdfFile/test_checkbox_not_required.yml

  - name: 模板控件校验-多选必填测试
    testcase: testcases/docs/template/generatePdfFile/test_checkbox_required.yml

  - name: 模板控件校验-多选特殊测试
    testcase: testcases/docs/template/generatePdfFile/test_checkbox_special.yml

  - name: 模板控件校验-模板id
    testcase: testcases/docs/template/generatePdfFile/test_contentIdAndContentCode.yml

  - name: 模板控件校验-身份证
    testcase: testcases/docs/template/generatePdfFile/test_contentValue_idCard.yml

  - name: 模板控件校验-邮箱
    testcase: testcases/docs/template/generatePdfFile/test_contentValue_mail.yml

  - name: 模板控件校验-手机号
    testcase: testcases/docs/template/generatePdfFile/test_contentValue_phone.yml

  - name: 模板控件校验-文本
    testcase: testcases/docs/template/generatePdfFile/test_contentValue_txt.yml

  - name: 模板控件校验-动态表格
    testcase: testcases/docs/template/generatePdfFile/test_dynamicTable.yml

  - name: 模板控件校验-文件名称
    testcase: testcases/docs/template/generatePdfFile/test_fileName.yml


  - name: 模板控件校验-数字0
    testcase: testcases/docs/template/generatePdfFile/test_number_0.yml


  - name: 模板控件校验-数字1
    testcase: testcases/docs/template/generatePdfFile/test_number_1.yml


  - name: 模板控件校验-数字2
    testcase: testcases/docs/template/generatePdfFile/test_number_2.yml

  - name: 模板控件校验-数字3
    testcase: testcases/docs/template/generatePdfFile/test_number_3.yml

  - name: 模板控件校验-机构代码
    testcase: testcases/docs/template/generatePdfFile/test_organize_code.yml

  - name: 模板控件校验-图片
    testcase: testcases/docs/template/generatePdfFile/test_picture_content_not_pic.yml

  - name: 模板控件校验-图片控件非必填
    testcase: testcases/docs/template/generatePdfFile/test_picture_not_required.yml

  - name: 模板控件校验-图片控件非必填2
    testcase: testcases/docs/template/generatePdfFile/test_picture_not_required_with_two_picture.yml

  - name: 模板控件校验-图片必填
    testcase: testcases/docs/template/generatePdfFile/test_picture_required.yml

  - name: 模板控件校验-单选
    testcase: testcases/docs/template/generatePdfFile/test_singleChoice.yml

  - name: 模板控件校验-模板id
    testcase: testcases/docs/template/generatePdfFile/test_templateId.yml


  - name: 模板控件校验-社会信用代码
    testcase: testcases/docs/template/generatePdfFile/test_united_code.yml


  - name: 模板控件校验-content值
    testcase: testcases/docs/template/generatePdfFile/text_contentValue.yml

  - name: 模板控件校验-content值50
    testcase: testcases/docs/template/generatePdfFile/text_contentValue50.yml

  - name: 模板控件校验-文本必填
    testcase: testcases/docs/template/generatePdfFile/text_required.yml

  - name: 校验文本内容域长度不同，其他属性相同，调用openapi合成pdf时合并填写
    testcase: testcases/docs/template/generatePdfFile/contentValue_txt_mergeFill.yml

  - name: openapi填写模板并转为pdf-填写错误，超出控件长度，message透出内容域名称-60140.beta3
    testcase: testcases/docs/template/generatePdfFile/error_messageTC1.yml

  - name: 线上问题集测化1
    testcase: testcases/docs/template/generatePdfFile/test_contentValue.yml

  - name: 线上问题集测化2
    testcase: testcases/docs/template/generatePdfFile/test_contentValue2.yml

  - name: 线上问题集测化3
    testcase: testcases/docs/template/generatePdfFile/test_contentValue3.yml