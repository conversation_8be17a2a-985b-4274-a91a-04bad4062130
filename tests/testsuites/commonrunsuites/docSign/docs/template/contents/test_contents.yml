config:
    name: "模板"

testcases:
  - name: 模板字段校验
    testcase: testcases/docs/template/contents/test_contents.yml

  - name: 模板相关
    testcase: testcases/docs/template/contents/test_list.yml

  - name: 文档模板相关测试用例--创建分组、控件
    testcase: testcases/docs/template/contents/contentsWithEpaasPdf.yml


#
#  - name: 文档模板内容域新增可修改、默认值字段
#    testcase: testcases/docs/template/contents/content_allowEdit.yml



#  - name: 文档模板内容域新增默认值格式校验
#    testcase: testcases/docs/template/contents/content_defaultContentValue.yml
#
#  - name: 文本类型内容域格式:行间距测试用例-6.0.14-beta.1
#    testcase: testcases/docs/template/contents/content_multipliedLeading.yml