config:
    name: "getDocFlowLists"

testcases:
  - name: 查询openapi发起的流程信息
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists.yml

  - name: 查询openapi发起的流程信息
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists2.yml

  - name: 根据业务编码查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_businessNo.yml

  - name: 根据动态编码查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_dynamicCode.yml

  - name: 通过结束时间查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_finishTime.yml

  - name: 通过flowid查询文档流程
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_flowId.yml

  - name: 根据流程名称查询成功
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_flowName.yml

  - name: 通过流程状态正常查询文档流程
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_flowStatus.yml

  - name: 根据发起人组织查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_initiatorOrganizeCode.yml

  - name: 根据发起人姓名查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_initiatorUserName.yml

  - name: 根据签署人姓名查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_signerUserName.yml

#  - name: 根据签署人姓名查询
#    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_signerUserName.yml

  - name: 查询文档流程场景一个正常的操作流程场景
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_startTime.yml


  - name: 根据流程最近处理日期查询
    testcase: testcases/docs/docFlow/getDocFlowLists/manage_getDocFlowLists/manage_getDocFlowLists_modifiedTime.yml