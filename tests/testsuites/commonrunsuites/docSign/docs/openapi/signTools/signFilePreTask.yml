config:
    name: "签署区配置相关openapi的场景链路（新接口）"

testcases:
  -
    name: SETUP-多通道-关闭
    parameters:
      - keyIdCode1-value1:
          - [ "system_config_tsp","0" ]
    testcase: common/valuesCmc.yml

  - name: openapi:签署区设置任务发起。场景：同一个节点内支持顺序签署-v6.0.6.5
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask-inNodeSequentialSign.yml

  - name: openapi链路用例：【1、签署区设置任务发起->保存签署区设置->openapi查看详情->使用详情参数一步发起】【2、新老数据兼容】
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask-to-createSignsNew.yml

  - name: 获取签署区配置页-参数校验
    testcase: testcases/docs/openapi/signTools/signFilePreTask/test_signFilePreTask.yml

  -
    name: SETUP-多通道-关闭
    parameters:
      - keyIdCode1-value1:
          - [ "system_config_tsp","0" ]
    testcase: common/valuesCmc.yml

  - name: 支持页面设置备注签署区-V6.0.12.0-beta.1
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTaskRemarkSceneTCNew.yml

  -
    name: SETUP-多通道-关闭
    parameters:
      - keyIdCode1-value1:
          - [ "system_config_tsp","0" ]
    testcase: common/valuesCmc.yml

  - name: ofd签署区设置
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask_ofdSignerSealNew.yml

  -
    name: SETUP-多通道-关闭
    parameters:
      - keyIdCode1-value1:
          - [ "system_config_tsp","0" ]
    testcase: common/valuesCmc.yml

  - name: pdf签署区设置
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask_pdfSignerSealNew.yml

  - name: SETUP-多通道-关闭
    parameters:
      - keyIdCode1-value1:
          - [ "system_config_tsp","0" ]
    testcase: common/valuesCmc.yml

  - name: 个人支持骑缝
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask_edgeSignforuserNew.yml

  - name: 签署区设置控制签署区是否必选印章
    testcase: testcases/docs/openapi/signTools/signFilePreTask/signFilePreTask-sealCheckSign.yml