#config:
#    name: "openapi-签署区配置老接口---对接epaas后不支持老的"
#
#testcases:
#  - name: 获取签署区配置页-参数校验
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask.yml
#
#
#  - name: 获取签署区配置页-内部机构
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask_innerOrg.yml
#
#
#  - name: 获取签署区配置页-内部个人
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask_innerUser.yml
#
#
#  - name: 获取签署区配置页-不指定签署方
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask_noSigner.yml
#
#
#
#  - name: 获取签署区配置页-相对方机构
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask_outOrg.yml
#
#
#  - name: 获取签署区配置页-相对方个人
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTask_outUser.yml
#
#
#  - name: o获取签署配置（预盖章签署页面）
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTaskDetail.yml
#
#  - name: 只有公章
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTaskNoLegalSeal.yml
#
#  - name: 只有法人章
#    testcase: testcases/docs/openapi/signTools/filePreTask/test_filePreTaskOnlyLegalSeal.yml