config:
  name: 创建签署配置（预盖章签署页面）参数校验
  variables:
    b0: 30
    # 内部个人机构的印章相关信息
    fileKey1page: $${ENV(1PageFileKey)}
    sealId0: ${ENV(org01.sealId)}
    userSealId0: ${ENV(sign01.sealId)}
    sealTypeCode0: ${ENV(orgSealTypeCode)}
    sealId2: ${ENV(orgSealId2)}
    sealTypeCode2: ${ENV(orgSealTypeCode2)}
    sealTypeCodeDelete: ${ENV(orgSealTypeCodeDelete)}
    userSealIdStop: ${ENV(userSealStop)}
    userSealIdDelete: ${ENV(userSealDelete)}
    userSealIdNotPublic: ${ENV(userSealNoPublic)}
    signatureType0: " PERSON-SEAL "
    signatureType1: " COMMON-SEAL "
    userCode0: ${ENV(sign01.userCode)}
    userCodeOpposit: ${ENV(wsignwb01.userCode)}
    userCodeInit: ${ENV(csqs.userCode)}
    orgCodeInit: ${ENV(csqs.orgCode)}
    orgCode0: ${ENV(sign01.main.orgCode)}
    orgCodeOpposit: ${ENV(wsignwb01.main.orgCode)}
    departmentCode0: ${ENV(sign01.main.orgCode)}
    customAccountNoSigner0: ${ENV(sign01.accountNo)}
    #内部用户的主责是部门
    customAccountNoSigner1: ${ENV(sign03.accountNo)}
    departmentNo1: ${ENV(sign03.main.departNo)}
    orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
    #内部用户的兼职是部门
    customAccountNoSigner2: ${ENV(sign01.accountNo)}
    customAccountNoSigner3: ${ENV(sign03.accountNo)}
    departmentNo2: ${ENV(sign01.JZ.departNo)}
    orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
    customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
    customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
    customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
    customDepartmentNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
    customOrgNoSignerOpposit: ${ENV(wsignwb01.main.orgNo)}
    sp: " "
    customDepartmentNo1: ${ENV(wsignwb01.main.orgNo)}
    fileKey0: "$${ENV(fileKey)}"
    fileKey1: "$${ENV(ofdFileKey)}"
    signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]

testcases:
  - name: 签署区设置页-校验-customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNoSigner-customOrgNoSigner-customDepartmentNoSigner-userCodeSigner-organizationCodeSigner-departmentCodeSigner-signatureType-code-message-data:
          - [ "TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,"","","","","",$signatureType0,200,"成功",null ]
          - [ "TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,"","",$userCode0,"","",$signatureType0,200,"成功",null ]
          - [ "TC3-customAccountNo和userCode都为空,报错","","","","","","",$signatureType0,200,"成功",null ]
          - [ "TC4-customAccountNo和userCode都不传,报错",null,"","",null,"","",$signatureType0,200,"成功",null ]
          - [ "TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',"","","","","",$signatureType0,1623008,"customAccountNo:xxxxx 对应用户不存在","value" ]
          - [ "TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',"","",$userCode0,"","",$signatureType0,200,"成功",null ]
          - [ "TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,"","","XXXXXX","","",$signatureType0,1623007,"userCode:XXXXXX 对应用户不存在","value" ]
          #          - [ "TC8-customAccountNo对应的账号为离职状态，userCode为空,报错",$customAccountNoDimission,"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）",null ]
          #          - [ "TC9-customAccountNo对应的账号为删除状态，userCode为空,报错",$customAccountNoDelete,"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）",null ]
          - [ "TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功",null ]
          - [ "TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",$signatureType1,200,"成功",null ]
          - [ "TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",$signatureType1,1623082,"签署方测试签署一（用户账号：sign01）不支持设置企业签署区","value" ]
          - [ "TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",$signatureType1,1623082,"签署方测试签署一（用户账号：sign01）不支持设置企业签署区","value" ]
          - [ "TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",$signatureType1,1623015,"customOrgNo:xxxxxxxx 对应机构不存在","value" ]
          - [ "TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",$signatureType1,200,"成功",null ]
          - [ "TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",$signatureType1,1623014,"organizeCode:XXXXX 对应机构不存在","value" ]
          #          - [ "TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",$signatureType1,1702446,"机构不存在",null ]
          - [ "TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",$signatureType1,200,"成功",null ]
          - [ "TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,"",$customOrgNoSignerOpposit,"","",$orgCode0,$signatureType0,200,"成功",null ]
          - [ "TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功",null ]
          - [ "TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,"",null,"","",null,$signatureType0,200,"成功",null ]
          - [ "TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,"","XXXXXX","","","",$signatureType0,1623026,"customDepartmentNo:XXXXXX 对应机构不存在","value" ]
          - [ "TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,'',"","","",$departmentCode0,$signatureType0,200,"成功",null ]
          - [ "TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,'',$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",$signatureType1,1623025,"departmentCode:XXXXXXX 对应机构不存在","value" ]
          #          - [ "TC25-customDepartmentNo对应的账号为注销状态，departmentCode为空,报错",$customAccountNoSigner0,"",$customOrgNoDelete,"","","",$signatureType0,1702446,"机构不存在",null ]
          - [ "TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",$signatureType1,200,"成功",null ]
          - [ "TC27-customAccountNo所属主责是企业，校验发起后customDepartmentNo为该企业,成功",$sp$customAccountNoSigner0$sp,"","","","","",$signatureType0,200,"成功",null ]
          - [ "TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner1,"","","","","",$signatureType0,200,"成功",null ]
          - [ "TC29-customAccountNo和customDepartmentNo没有绑定关系,报错",$customAccountNoSigner0,"",$customDepartmentNo1,"","","",$signatureType0,1623035,"签署方测试签署一（用户账号：sign01）的所属组织与用户关系不匹配","value" ]
          - [ "TC30-customAccountNo和customOrgNo没有授权关系,报错",$customAccountNoSigner0,"","","",$orgCodeOpposit,"",$signatureType1,1623038,"无指定机构的授权权限","value" ]
          - [ "TC31-customAccountNo传入的长度超过36字符,报错","${random_str(37)}","","","","","",$signatureType0,1623008,"对应用户不存在","value" ]
          - [ "TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,"","${random_str(65)}","","","",$signatureType0,1623026,"customDepartmentNo","value" ]
          - [ "TC33-customOrgNo传入的长度超过64字符,报错",$customAccountNoSigner0,"${random_str(65)}","","","","",$signatureType0,1623015,"customOrgNo","value" ]
          - [ "TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,"","","","","",$signatureType0,200,"成功",null ]
          - [ "TC35-customAccountNo传入的是相对方个人的账z号，userType限定是内部,报错",$customAccountNoSignerOpposit,"","","","","",$signatureType0,1623008,"customAccountNo:wsignwb01 对应用户不存在","value" ]
          - [ "TC37-customAccountNo兼职部门，指定兼职部门发起流程,成功",$customAccountNoSigner2,"",$departmentNo2,"","","",$signatureType0,200,"成功",null ]
    testcase: testcases/docs/openapi/signTools/signFilePreTask/test_signFilePreTask1.yml

  - name: 签署区设置页-校验-expirationDate-SignerInfos
    parameters:
      - name-expirationDate-signerInfos-code-message-data:
          - [ "TC1-expirationDate，=-1报错",-1,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1600017,"签署配置链接有效期不能小于1天","{}" ]
          - [ "TC2-expirationDate，=0报错",0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1600017,"签署配置链接有效期不能小于1天","{}" ]
          - [ "TC3-expirationDate，=1报错",1,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC4-expirationDate，非指定类型=T报错","T",[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],400,"expirationDate参数错误!","{}" ]
          - [ "TC5-expirationDate，>365",366,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1600017,"签署配置链接有效期不能超过365天","{}" ]
          - [ "TC6-expirationDate，=365",365,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC14-签署方为个人，指定签署印章类型是机构，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623082,"签署方测试签署一（用户编码：sign01）不支持设置企业签署区","{}" ]
          - [ "TC15-签署方为个人，指定签署印章类型是法人，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": " LEGAL-PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623049,"为个人不支持指定法人签署印章类型","{}" ]
          - [ "TC16-签署方为机构，只指定签署印章类型是个人，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623081,"至少设置一个企业签署区","{}" ]
          - [ "TC25-签署方为机构，只指定经办人签名域，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"userType": 1,"signMode": 0,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623081,"至少设置一个企业签署区","{}" ]
          - [ "TC30-签署方为机构，签署域指定法人，机构类型",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" },{ "signatureType": "LEGAL-PERSON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": true,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC31-签署方为机构，签署域指定经办人，法人，机构类型",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "pageNo": "1-99","signType": "KEYWORD-SIGN","signatureType": "PERSON-SEAL","keywordInfo": { "keyword": "甲方","keywordIndex": "1","offsetPosX": "50","offsetPosY": "50" } },{ "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN ","signatureType": "LEGAL-PERSON-SEAL" },{ "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": true,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC32-签署方为机构，签署域只指定法人类型",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "pageNo": "1-99","signType": "KEYWORD-SIGN","signatureType": "PERSON-SEAL","keywordInfo": { "keyword": "甲方","keywordIndex": "1","offsetPosX": "50","offsetPosY": "50" } },{ "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN ","signatureType": "LEGAL-PERSON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": true,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623081,"至少设置一个企业签署区","{}" ]
          - [ "TC33-签署方为机构，签署域指定法人类型，legalSignFlag=false，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" },{ "signatureType": "LEGAL-PERSON-SEAL" },{ "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": false,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623051,"未开启法人签时,signatureType不支持传LEGAL-PERSON-SEAL","{}" ]
          - [ "TC34-签署方为机构，签署域指定法人类型，legalSignFlag不传，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "pageNo": "1-99","signType": "KEYWORD-SIGN","signatureType": "PERSON-SEAL","keywordInfo": { "keyword": "甲方","keywordIndex": "1","offsetPosX": "50","offsetPosY": "50" } },{ "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN ","signatureType": "LEGAL-PERSON-SEAL" },{ "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623051,"未开启法人签时,signatureType不支持传LEGAL-PERSON-SEAL","{}" ]
          - [ "TC35-签署方为个人，指定法人签名域，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "LEGAL-PERSON-SEAL" } ] } ],"userType": 1,"userCode": "$userCode0" } ],1623049,"个人不支持指定法人签署印章类型","{}" ]
          - [ "TC36-签署方为个人，signatureType为非指定值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "T" } ] } ],"userType": 1,"userCode": "$userCode0" } ],1623062,"指定签署印章类型只支持PERSON-SEAL、COMMON-SEAL、LEGAL-PERSON-SEAL类型","{}" ]
          - [ "TC37-签署方为机构，signatureType为非指定值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" },{ "signatureType": "T" } ] } ],"userType": 1,"legalSignFlag": true,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623062,"指定签署印章类型只支持PERSON-SEAL、COMMON-SEAL、LEGAL-PERSON-SEAL类型","{}" ]
          - [ "TC38-签署方为个人，signatureType不传，允许",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "200","isAddSignDate": 0,"posY": "200","sealId": "","pageNo": "1" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": false,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC40-签署方为机构，signatureType为空，允许",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN ","signatureType": "" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC41-签署方为机构，legalSignFlag非指定值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" },{ "signatureType": "LEGAL-PERSON-SEAL" },{ "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": "T","userCode": "$userCode0","organizationCode": "$orgCode0" } ],400,"legalSignFlag参数错误","{}" ]
          - [ "TC42-签署方为机构，自由签带法人",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0" } ],"signNode": 1,"userType": 1,"signMode": 0,"legalSignFlag": true,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC43-签署方多个，signMode不传，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ],1623072,"签署模式不能为空","{}" ]
          - [ "TC44-signMode非指定值=-1，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": -1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623085,"一个签署主体时，签署模式只能是顺序签！","{}" ]
          - [ "TC45-signMode非指定类型，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": "T","userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],400,"signMode参数错误","{}" ]
          - [ "TC46-多个签署方的signMode不传，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"userType": 1,"userCode": "$userCode0" } ],1623072,"签署模式不能为空",'{}' ]
          - [ "TC47-signMode传空，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": "","userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"signMode": "","userType": 1,"userCode": "$userCode0" } ],1623072,"签署模式不能为空",'{}' ]
          - [ "TC61-单个signMode和signNode都不传，默认是顺序签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC48-signNode不传，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": null,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623071,"签署节点不能为空","{}" ]
          - [ "TC49-signNode传空，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": "","signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623071,"签署节点不能为空","{}" ]
          - [ "TC50-signNode其他值非正整数，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": -1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1600017,"签署方节点顺序不能小于1","{}" ]
          - [ "TC51-signNode其他类型的值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": "T","signMode": 0,"userType": 1,"userCode": "$userCode0" } ],400,"signNode参数错误","{}" ]
          - [ "TC52-signNode=99999，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 99999,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC53-signNode范围超过99999，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1000000,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623070,"签署节点:1000000请在1-99999中选择！","{}" ]
          - [ "TC54-signNode顺序签署时传入相同顺序，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],1623074,"顺序签同一节点:1只能有一个签署方！","{}" ]
          - [ "TC55-signNode无序签署时传入不同顺序，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"signMode": 1,"userType": 1,"userCode": "$userCode0" } ],1623084,"非顺序签，请至少保证每个节点存在2个签署方！","{}" ]
          - [ "TC56-signNode或签时传入不同顺序，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"signMode": 2,"userType": 1,"userCode": "$userCode0" } ],1623084,"非顺序签，请至少保证每个节点存在2个签署方！","{}" ]
          - [ "TC57-signNode相同签署模式不同，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0" } ],1623073,"同一节点:1不可出现不同签署模式！","{}" ]
          - [ "TC58-无序签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 2,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"signMode": 1,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC59-或签签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC60-顺序签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 10,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC62-sealInfo指定fileKey为空，报错",$b0,[ { "sealInfos": [ { "fileKey": "","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1600017,"fileKey不能为空","{}" ]
          - [ "TC63-sealInfo多节点fileKey重复，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] },{ "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "100","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623005,"filekey不支持重复","{}" ]
          - [ "TC64-sealInfo多节点fileKey包含ofd和pdf，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] },{ "fileKey": "$fileKey1","signConfigs": [ { "signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623041,"同一签署任务不能同时支持PDF与OFD文件","{}" ]
          - [ "TC65-sealId长度为65字符，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${random_str(65)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623057,"不存在","{}" ]
          - [ "TC66-sealId长度为63字符，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${random_str(63)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623057,"不存在","{}" ]
          - [ "TC67-sealId不属于签署人，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "$sealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623057,"不存在","{}" ]
          - [ "TC68-签署方内部个人，sealId状态为未发布，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${ENV(userSealNoPublic)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623069,"未发布","{}" ]
          - [ "TC70-签署方内部个人，sealId状态为已删除，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "$userSealIdDelete" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623057,"不存在","{}" ]
          - [ "TC71-签署方内部个人，sealId状态为已停用，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "150","posY": "150","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "$userSealIdStop" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "" } ],1623069,"未发布","{}" ]
          - [ "TC72-签署方内部机构，机构签名域的sealId状态为已停用，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealStop)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623069,"未发布","{}" ]
          - [ "TC73-签署方内部机构，机构签名域的sealId状态为已删除，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealDelete)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC74-签署方内部机构，机构签名域的sealId状态为未发布，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealNoPublic)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623069,"未发布","{}" ]
          - [ "TC75-签署方内部机构，机构签名域的sealId不属于签署人，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealNoPermission)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1702634,"非印章","{}" ]
          - [ "TC77-签署方内部机构，经办人签名域的sealId状态为已停用，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "$userSealIdStop","handEnable": False } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623069,"未发布","{}" ]
          - [ "TC78-签署方内部机构，经办人签名域的sealId状态为已删除，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "$userSealIdDelete","handEnable": False } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC79-签署方内部机构，经办人签名域的sealId状态为未发布，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${ENV(userSealNoPublic)}","handEnable": False } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623069,"未发布","{}" ]
          - [ "TC81-签署方内部机构，法人签名域的sealId状态为未发布，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "LEGAL-PERSON-SEAL","sealId": "${ENV(userSealNoPublic)}","handEnable": False } ] } ],"legalSignFlag": true,"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC82-签署方内部机构，法人签名域的sealId状态为已停用，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "LEGAL-PERSON-SEAL","sealId": "$userSealIdStop","handEnable": False } ] } ],"legalSignFlag": true,"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC83-签署方内部机构，法人签名域的sealId状态为已删除，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "LEGAL-PERSON-SEAL","sealId": "$userSealIdDelete","handEnable": False } ] } ],"legalSignFlag": true,"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC84-签署方内部机构，指定印章进行机构，经办人，法人签署,静默签署成功",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "$sealId0" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "LEGAL-PERSON-SEAL","sealId": "${ENV(org01.legal.sealId)}" },{ "posX": "150","posY": "350","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${ENV(sign01.sealId)}","handEnable": False } ] } ],"legalSignFlag": true,"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC85-签署方相对方个人，不允许指定sealId,报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "PERSON-SEAL","sealId": "$sealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit" } ],1623013,"相对方不支持指定印章id","{}" ]
          - [ "TC86-签署方相对方机构，不允许指定sealId,报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "$sealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode": "$orgCodeOpposit" } ],1623013,"相对方不支持指定印章id","{}" ]
          - [ "TC87-签署方内部机构，机构签名域的sealId状态为其他机构",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgCodeNoCertSealId)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623057,"不存在","{}" ]
          - [ "TC88-签署方内部个人，只指定印章Id",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "$userSealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC89-签署方内部机构，只指定印章Id",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "$sealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623087,"未指定签署印章类型",'{}' ]
          - [ "TC199-内部机构签署，指定印章类型sealTypeCode",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": " ${ENV(orgSealTypeCode)} " } ],200,"成功",null ]
          - [ "TC200-相对方机构签署，指定印章类型（已实名的账号）",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode": "$orgCodeOpposit","sealTypeCode": "PUBLIC" } ],200,"成功",null ]
          - [ "TC201-内部机构签署，sealTypeCode指定不存在的值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": "XXXXXX" } ],1623012,"不支持机构印章：XXXXXX类型","{}" ]
          - [ "TC203-内部机构签署，sealTypeCode指定和sealId不匹配的值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "$sealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": "$sealTypeCode2" } ],1623058,"不匹配","{}" ]
          - [ "TC204-内部个人签署，sealTypeCode指定值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "PERSON-SEAL","handEnable": true } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","sealTypeCode": $sealTypeCode0 } ],1623063,"为个人不支持指定印章类型","{}" ]
          - [ "TC205-相对方机构签署，sealTypeCode指定不存在的值，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode": "$orgCodeOpposit","sealTypeCode": "XXXX" } ],1623012,"不支持机构印章","{}" ]
          - [ "TC206-内部机构签署，sealTypeCode指定法人章，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": "LEGAL-PERSON-SEAL" } ],1623012,"不支持机构印章","{}" ]
          - [ "TC207-内部机构签署，sealTypeCode的值超过64位，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": "${random_str(65)}" } ],1623012,"不支持机构印章","{}" ]
          - [ "TC208-相对方个人签署，不支持指定印章类型，报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","sealTypeCode": "$orgCodeOpposit" } ],1623063,"为个人不支持指定印章类型","{}" ]
          - [ "TC209-多签名域指定不同的印章sealId",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "$sealId0" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealId2)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC214-内部机构签署，指定的sealId没有授权给这个经办人，报错",$b0,[ { "sealInfos": [ { "fileKey": " $fileKey0 ","signConfigs": [ { "sealId": " ${ENV(orgSealNoPermission)} ","edgeScope": " 1","posY": "150","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],1623065,"无指定印章ID","{}" ]
          - [ "TC222-签署方，个人相对方专用编码GRXDF不可作为机构,报错",$b0,[ { "sealInfos": [ { "fileKey": " $fileKey0 ","signConfigs": [ { "sealId": " $sealId0 ","posX": "150","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode": " GRXDF ","departmentCode": " " } ],1623036,"organizationCode值需和departmentCode值保持一致","{}" ]
          - [ "TC224-相对方机构签署，指定sealTypeCode多个允许前后带空格",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode": "$orgCodeOpposit","sealTypeCode": " PUBLIC ,CONTRACT, FINANCE,PERSONNEL ,COMMON" } ],200,"成功",null ]
          - [ "TC225-设置多个空的signConfigs,报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "","signatureType": "" },{ "sealId": "","signatureType": "" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"userCode": "$userCode0" } ],1623086,"同一预设签署任务不允许添加两个相同的预设签署信息","{}" ]
          - [ "TC226-设置多个有sealId的signConfigs,报错",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "$sealId0","signatureType": "COMMON-SEAL" },{ "sealId": "$sealId0","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"userType": 1,"signMode": 0,"userCode": "$userCode0","organizationCode": "$orgCode0"  } ],1623086,"同一预设签署任务不允许添加两个相同的预设签署信息","{}" ]

    testcase: testcases/docs/openapi/signTools/signFilePreTask/test_signFilePreTask-signerInfos-err.yml

  - name: 签署区设置页-校验-expirationDate-SignerInfos
    parameters:
      - name-expirationDate-signerInfos-code-message-data:
          - [ "TC58-无序签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 2,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 2,"signMode": 1,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC59-或签签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 2,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC60-顺序签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "150","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "100","posY": "150","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 10,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC84-签署方内部机构，指定印章进行机构，经办人，法人签署,静默签署成功",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "250","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL","sealId": "$sealId0" },{ "posX": "250","posY": "250","pageNo": "1-3","signType": "COMMON-SIGN","signatureType": "LEGAL-PERSON-SEAL","sealId": "${ENV(org01.legal.sealId)}" },{ "posX": "150","posY": "350","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL","sealId": "${ENV(sign01.sealId)}","handEnable": False } ] } ],"legalSignFlag": true,"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC88-签署方内部个人，只指定印章Id",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "$userSealId0" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC89-签署方内部机构，只指定印章Id",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "sealId": "$sealId0","signatureType":"COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC199-内部机构签署，指定印章类型sealTypeCode",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "keywordInfo": { "keyword": "甲方","keywordIndex": "1" },"pageNo": "1-3","signType": "KEYWORD-SIGN","signatureType": "COMMON-SEAL","sealId": "" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","sealTypeCode": " ${ENV(orgSealTypeCode)} " } ],200,"成功",null ]
          - [ "TC209-多签名域指定不同的印章sealId",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "signatureType": "COMMON-SEAL","sealId": "$sealId0" },{ "signatureType": "COMMON-SEAL","sealId": "${ENV(orgSealId2)}" } ] } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC211-相同机构不同经办人签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "edgeScope": "1","posY": "150","pageNo": "","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0","departmentCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posY": "350","pageNo": "1-10","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "${ENV(signjz01.userCode)}","organizationCode": "$orgCode0","departmentCode": "$orgCode0" } ],200,"成功",null ]
          - [ "TC212-个人签和机构经办人签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "130","posY": "150","pageNo": "1-10","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" },{ "posX": "130","posY": "350","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "230","posY": "250","pageNo": "1-10","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
          - [ "TC213-机构签署人，不指定signConfigs自由签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0" } ],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" } ],200,"成功",null ]
#          - [ "TC212-个人签和机构经办人骑缝章签署",$b0,[ { "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "130","posY": "150","pageNo": "1-10","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" },{ "posX": "130","posY": "350","pageNo": "1","signType": "-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0","organizationCode": "$orgCode0" },{ "sealInfos": [ { "fileKey": "$fileKey0","signConfigs": [ { "posX": "230","posY": "250","pageNo": "1-10","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"signNode": 1,"signMode": 1,"userType": 1,"userCode": "$userCode0" } ],200,"成功",null ]
    testcase: testcases/docs/openapi/signTools/signFilePreTask/test_signFilePreTask-signerInfos.yml