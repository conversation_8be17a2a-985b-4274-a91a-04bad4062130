config:
  name: 已签文件导入(open-api)
  variables:
    customAccountNo0: $${ENV(sign01.userCode)}
    departmentName0: $${ENV(sign01.main.orgName)}
    customDepartmentNo0: $${ENV(sign01.main.orgNo)}
    wsignwb01userName: $${ENV(wsignwb01.userName)}
    wsignwb01userCode: $${ENV(wsignwb01.userCode)}
    wsignwb01orgNo: $${ENV(wsignwb01.main.orgNo)}
    ceswdzxzdhyhwgd1orgName: $${ENV(ceswdzxzdhyhwgd1.orgName)}
    ceswdzxzdhyhwgd1orgNo: $${ENV(ceswdzxzdhyhwgd1.orgNo)}
    subject201: ${generate_random_str(201)}
    subject200: ${generate_random_str(200)}
    userName27: ${generate_random_str(191)}
    userName26: ${generate_random_str(190)}
    customAccountNo31: ${generate_random_str(31)}
    customAccountNo30: ${generate_random_str(30)}
    departmentName51: ${generate_random_str(51)}
    departmentName50: ${generate_random_str(50)}
    customDepartmentNo55: ${generate_random_str(55)}
    customDepartmentNo54: ${generate_random_str(54)}


testcases:
- name: importSignedFilecase(公共参数)
  parameters:
    - name-subject-signFlowCreateTime-signFlowEndTime-signType-code-message:
        - [ "P1TL-subject为空字符串", "", "","","",1600017, "流程主题支持的字符长度为1-200" ]
        - [ "P2TL-subject长度为201字符", $subject201, "","","",1600017, "流程主题支持的字符长度为1-200" ]
        - [ "P3TL-subject长度为200字符", $subject200, "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P4TL-subject含不支持的特殊字符", "主题*<>", "","","",1702106, "流程标题存在非法字符！" ]
        - [ "P5TL-subject主题重复", $subject200, "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P6TL-subject包含特殊字符", "测试Test~!@#$%^&()_+{}'.,;[]=-`（）·！@#￥%……&——+、；‘，。、《》：“©™®℗", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P7TL-subject包含首尾和中间空格", " 主题 测试 ", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P8TL-subject包含不支持的特殊字符", "测试\/:*?<>|", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,1702106, "流程标题存在非法字符！" ]
        - [ "P9TL-签署创建时间signFlowCreateTime为空字符串", "主题1", "","2024-07-12 13:00:00",1,1624071, "signFlowCreateTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P10TL-签署创建时间signFlowCreateTime晚于结束时间signFlowEndTime", "主题2", "2024-07-22 12:00:00","2024-07-12 13:00:00",1,1613012, "签署完成时间不能早于发起时间" ]
        - [ "P11TL-签署创建时间signFlowCreateTime非时间格式", "主题3", "123123123","2024-07-12 13:00:00",1,1624071, "signFlowCreateTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P12TL-签署创建时间signFlowCreateTime不存在时间", "主题4", "2024-6-32 00:00:00","2024-07-12 13:00:00",1,1624071, "signFlowCreateTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P13TL-签署创建时间signFlowCreateTime为 yyyy-MM-dd", "主题5", "2024-6-30","2024-07-12 13:00:00",1,1624071, "signFlowCreateTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P14TL-签署创建时间signFlowCreateTime为yyyy年mm月dd日 hh:mm:ss", "主题6", "2021年12月31日 00:00:00","2024-07-12 13:00:00",1,1624071, "signFlowCreateTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P15TL-签署创建时间signFlowCreateTime为 yyyy-MM-dd hh:mm:ss", "主题7", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P16TL-签署创建时间signFlowCreateTime包含首尾空格", "主题8", " 2021-12-31  00:00:00 ","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P17TL-签署创建时间signFlowCreateTime大于当前时间", "主题9", " 2028-12-31  00:00:00 ","2029-07-12 13:00:00",1,1613015, "签署完成时间不能早于当前时间" ]
        - [ "P18TL-签署结束时间signFlowEndTime为空字符串", "主题1", "2024-07-12 13:00:00","",1,1624071, "signFlowEndTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P19TL-签署结束时间signFlowEndTime非时间格式", "主题2", "2024-07-12 13:00:00","123123123",1,1624071, "signFlowEndTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P20TL-签署结束时间signFlowEndTime不存在时间", "主题3", "2024-07-12 13:00:00","2024-6-32 00:00:00",1,1624071, "signFlowEndTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P21TL-签署结束时间signFlowEndTime为 yyyy-MM-dd", "主题4", "2024-07-12 13:00:00","2024-6-30",1,1624071, "signFlowEndTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P22TL-签署结束时间signFlowEndTime为yyyy年mm月dd日 hh:mm:ss", "主题5", "2024-07-12 13:00:00","2021年12月31日 00:00:00",1,1624071, "signFlowEndTime格式填写错误，正确格式为:yyyy-MM-dd HH:mm:ss" ]
        - [ "P23TL-签署结束时间signFlowEndTime为 yyyy-MM-dd hh:mm:ss", "主题6", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P24TL-签署结束时间signFlowEndTime包含首尾空格", "主题7", "2024-07-12 13:00:00"," 2024-07-15  00:00:00 ",1,200, "成功" ]
        - [ "P25TL-signType为空字符串", "主题", "2024-07-12 12:00:00","2024-07-12 13:00:00","",200, "成功" ]
        - [ "P26TL-signType为1", "主题", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P27TL-signType为2", "主题", "2024-07-12 12:00:00","2024-07-12 13:00:00",1,200, "成功" ]
        - [ "P28TL-signType为非1或2字符", "主题", "2024-07-12 12:00:00","2024-07-12 13:00:00",5,1703009, "签署类型有误" ]
  testcase: testcases/docs/signFlow/importSignedFilecase.yml


- name: importSignedFilesignFiles(流程文档信息集合)
  parameters:
  testcase: testcases/docs/signFlow/importSignedFilesignFiles.yml

- name: importSignedFileinitiatorInfo.yml(发起方信息集合)
  parameters:
    - name-userName1-customAccountNo1-departmentName1-customDepartmentNo1-code-message:
        - [ "P1TL-发起人userName、customAccountNo 为空字符串", "", "","","",1624099, "流程发起人名称和编码不能都为空" ]
        - [ "P2TL-发起人userName字符长191超出校验", $userName27, "",$departmentName0,"",1624080, "请输入正确的用户姓名" ]
        - [ "P3TL-发起人userName字符长度190边界值校验", $userName26, "",$departmentName0,"",200, "成功" ]
        - [ "P4TL-发起人customAccountNo字符长度31超出校验", "", $customAccountNo31,$departmentName0,"",1624083, "账号支持输入2-30字" ]
        - [ "P6TL-发起人userName包含首尾空格", " 哈哈 ", "",$departmentName0,"",200, "成功" ]
        - [ "P7TL-发起人userName、customAccountNo为系统中不存在", "哈哈", "haha",$departmentName0,"",1623008, "customAccountNo:haha 对应用户不存在" ]
        - [ "P8TL-发起人userName不存在", "不存在用户b", "",$departmentName0,"",200, "成功" ]
        - [ "P9TL-发起人userName、customAccountNo指定相对方用户", $wsignwb01userName, $wsignwb01userCode,$departmentName0,"",1623008, "customAccountNo:wsignwb01 对应用户不存在" ]
        - [ "P10TL-发起人userName、customAccountNo指定内部用户已注销", "测试注销用户", "ceszxyh",$departmentName0,"",1623008, "customAccountNo:ceszxyh 对应用户不存在" ]
        - [ "P11TL-发起人userName、customAccountNo指定内部用户已注销", "测试离职用户", "cesfqrlz",$departmentName0,"",1623008, "customAccountNo:cesfqrlz 对应用户不存在" ]
        - [ "P12TL-发起人departmentName、customDepartmentNo 为空字符串", "测试签署一", $customAccountNo0,"","",1624070, "流程发起人组织名称和编码不能都为空" ]
        - [ "P13TL-发起人departmentName、customDepartmentNo 为均填写", "测试签署一", $customAccountNo0,$departmentName0,$customDepartmentNo0,200, "成功" ]
        - [ "P14TL-发起人departmentName字符长度51超出校验", "测试签署一", $customAccountNo0,$departmentName51,"",1624081, "组织名称支持输入2-50字" ]
        - [ "P15TL-发起人departmentName字符长度50边界值校验", "测试签署一", $customAccountNo0,$departmentName50,"",200, "成功" ]
        - [ "P16TL-发起人customDepartmentNo 字符长度55超出校验", "测试签署一", $customAccountNo0,"",$customDepartmentNo55,1624082, "账号支持输入2-54字" ]
        - [ "P18TL-发起人departmentName 包含首尾空格", "测试签署一", $customAccountNo0," 所属机构A ","",200, "成功" ]
        - [ "P19TL-发起人departmentName 包含特殊字符", "测试签署一", $customAccountNo0,"esigntest企业\/:*?<>|含特殊符号","",200, "成功" ]
        - [ "P20TL-发起人departmentName、customDepartmentNo 为系统中不存在", "测试签署一", $customAccountNo0,"哈哈","hahhahah",1623026, "customDepartmentNo:hahhahah 对应机构不存在" ]
        - [ "P21TL-发起人userName不存在，departmentName、customDepartmentNo不匹配", "哈哈测试", "",$departmentName0,$customDepartmentNo0,200, "成功" ]
        - [ "P22TL-发起人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo不匹配", "测试签署一", $customAccountNo0,$ceswdzxzdhyhwgd1orgName,$ceswdzxzdhyhwgd1orgNo,200, "成功" ]
        - [ "P23TL-发起人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo传相对方机构", "测试签署一", $customAccountNo0,"esigntest自动化签署中心外部一CI测试","WORG-SIGN-01",1623026, "customDepartmentNo:WORG-SIGN-01 对应机构不存在" ]
        - [ "P24TL-发起人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo为空字符串", "测试签署一", $customAccountNo0,"","",1624070, "流程发起人组织名称和编码不能都为空" ]
        - [ "P25TL-发起人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo为所属企业", "测试签署一", $customAccountNo0,$departmentName0,$customDepartmentNo0,200, "成功" ]
  testcase: testcases/docs/signFlow/importSignedFileinitiatorInfo.yml

- name: importSignedFilesignerInfos.yml(签署方信息集合)
  parameters:
    - name-userType-userName-customAccountNo-departmentName-customDepartmentNo-organizationName-customOrgNo-code-message:
        - [ "P1TL-签署人userName、customAccountNo 为空字符串", 1,"", "","","","","",1624072, "流程签署人名称和编码不能都为空" ]
        - [ "P2TL-签署人userName字符长度191超出校验", 1,$userName27, "",$departmentName0,"","","",1624080, "请输入正确的用户姓名" ]
        - [ "P3TL-签署人userName字符长度190边界值校验", 1,$userName26, "",$departmentName0,"","",$customDepartmentNo0,200, "成功" ]
        - [ "P4TL-签署人customAccountNo字符长度31超出校验",1, "", $customAccountNo31,$departmentName0,"","","",1624083, "账号支持输入2-30字" ]
        - [ "P5TL-签署人customAccountNo字符长度30边界值校验",1, "", $customAccountNo30,$departmentName0,"","","",1623008, "对应用户不存在" ]
        - [ "P6TL-签署人userName包含首尾空格",1, " 哈哈 ", "",$departmentName0,"","",$customDepartmentNo0,200, "成功" ]
        - [ "P7TL-签署人userName、customAccountNo为系统中不存在",1, "哈哈", "haha",$departmentName0,"","",$customDepartmentNo0,1623008, "对应用户不存在" ]
        - [ "P8TL-签署人userName不存在",1, "不存在用户b", "",$departmentName0,"","",$customDepartmentNo0,200, "成功" ]
        - [ "P9TL-签署人userName、customAccountNo指定相对方用户",1, $wsignwb01userName, $wsignwb01userCode,$departmentName0,"","",$customDepartmentNo0,1623008, "对应用户不存在" ]
        - [ "P10TL-签署人userName、customAccountNo指定内部用户已注销",1, "测试注销用户", "ceszxyh",$departmentName0,"","",$customDepartmentNo0,1623008, "对应用户不存在" ]
        - [ "P11TL-签署人userName、customAccountNo指定内部用户已注销",1, "测试离职用户", "cesfqrlz",$departmentName0,"","",$customDepartmentNo0,1623008, "对应用户不存在" ]
        - [ "P12TL-签署人departmentName、customDepartmentNo 为空字符串",1, "测试签署一", $customAccountNo0,"","","",$customDepartmentNo0,200, "成功" ]
        - [ "P13TL-签署人departmentName、customDepartmentNo 为均填写",1, "测试签署一", $customAccountNo0,$departmentName0,$customDepartmentNo0,"",$customDepartmentNo0,200, "成功" ]
        - [ "P14TL-签署人departmentName字符长度51超出校验",1, "测试签署一", $customAccountNo0,$departmentName51,"","",$customDepartmentNo0,1624081, "组织名称支持输入2-50" ]
        - [ "P15TL-签署人departmentName字符长度50边界值校验",1, "测试签署一", $customAccountNo0,$departmentName50,"","",$customDepartmentNo0,200, "成功" ]
        - [ "P16TL-签署人customDepartmentNo 字符长度55超出校验",1, "测试签署一", $customAccountNo0,"",$customDepartmentNo55,"",$customDepartmentNo0,1624082, "账号支持输入2-54字" ]
        - [ "P17TL-签署人customDepartmentNo 字符长度54边界值校验",1, "测试签署一", $customAccountNo0,"",$customDepartmentNo54,"",$customDepartmentNo0,1623015, "对应机构不存在" ]
        - [ "P18TL-签署人departmentName 包含首尾空格",1, "测试签署一", $customAccountNo0," 所属机构A ","","",$customDepartmentNo0,200, "成功" ]
        - [ "P19TL-签署人departmentName 包含特殊字符",1, "测试签署一", $customAccountNo0,"esigntest企业\/:*?<>|含特殊符号","","",$customDepartmentNo0,200, "成功" ]
        - [ "P20TL-签署人departmentName、customDepartmentNo 为系统中不存在",1, "测试签署一", $customAccountNo0,"哈哈","hahhahah","",$customDepartmentNo0,1623015, "对应机构不存在" ]
        - [ "P21TL-签署人userName不存在，departmentName、customDepartmentNo不匹配",1, "哈哈测试", "",$departmentName0,$customDepartmentNo0,"",$customDepartmentNo0,200, "成功" ]
        - [ "P22TL-签署人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo不匹配",1, "测试签署一", $customAccountNo0,$ceswdzxzdhyhwgd1orgName,$ceswdzxzdhyhwgd1orgNo,"",$customDepartmentNo0,200, "成功" ]
        - [ "P23TL-签署人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo传相对方机构",1, "测试签署一", $customAccountNo0,"esigntest自动化签署中心外部一CI测试","WORG-SIGN-01","",$customDepartmentNo0,1623015, "对应机构不存在" ]
        - [ "P24TL-签署人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo为空字符串",1, "测试签署一", $customAccountNo0,"","","",$customDepartmentNo0,200, "成功" ]
        - [ "P25TL-签署人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo为所属企业",1, "测试签署一", $customAccountNo0,$departmentName0,$customDepartmentNo0,"",$customDepartmentNo0,200, "成功" ]
        - [ "P26TL-签署人userType为空或不传","", "测试签署一", $customAccountNo0,"","","",$customDepartmentNo0,1624073, "流程签署方类型错误" ]
        - [ "P27TL-签署人userType为其他值3/0/内/N",3, "测试签署一", $customAccountNo0,"","","",$customDepartmentNo0,1624073, "流程签署方类型错误" ]
        - [ "P28TL-签署人organizationName、customOrgNo为均填写",1, "测试签署一", $customAccountNo0,"","","测试机构",$customDepartmentNo0,200, "成功" ]
        - [ "P29TL-签署人签署人organizationName字符长度51超出校验",1, "测试签署一", $customAccountNo0,"","",$departmentName51,"",1624081, "组织名称支持输入2-50" ]
        - [ "P30TL-签署人organizationName字符长度50边界值校验",1, "测试签署一", $customAccountNo0,"","",$departmentName50,"",200, "成功" ]
        - [ "P31TL-签署人customOrgNo字符长度55超出校验",1, "测试签署一", $customAccountNo0,"","","",$customDepartmentNo55,1624082, "账号支持输入2-54字" ]
        - [ "P32TL-签署人customOrgNo字符长度54边界值校验",1, "测试签署一", $customAccountNo0,"","","",$customDepartmentNo54,1623015, "对应机构不存在" ]
        - [ "P32TL-签署人customOrgNo账号格式错误",1, "测试签署一", $customAccountNo0,"","","","ESIGNTESTZX*",1624084, "账号不能包含以下特殊符号：\\/:*?\"<>|" ]
        - [ "P33TL-签署人organizationName包含首尾空格",1, "测试签署一", $customAccountNo0,"",""," 所属机构A ","",200, "成功" ]
        - [ "P34TL-签署人organizationName包含特殊字符",1, "测试签署一", $customAccountNo0,"","","esigntest企业\/:*?<>|含特殊符号","",200, "成功" ]
        - [ "P35TL-签署人organizationName、customOrgNo为均不存在",1, "测试签署一", $customAccountNo0,"","","所属机构A","ESIGNTESTZXCSZZSESIGNT",1623015, "对应机构不存在" ]
        - [ "P36TL-签署人userName不存在，departmentName、customDepartmentNo不匹配，organizationName、customOrgNo不匹配",1, "内部姓名c", "","",$customDepartmentNo0,"",$customDepartmentNo0,200, "成功" ]
        - [ "P37TL-签署人userName、customAccountNo指定内部用户，departmentName、customDepartmentNo不匹配，organizationName、customOrgNo不匹配",1, "测试签署一", $customAccountNo0,$ceswdzxzdhyhwgd1orgName,$ceswdzxzdhyhwgd1orgNo,$ceswdzxzdhyhwgd1orgName,$ceswdzxzdhyhwgd1orgNo,200, "成功" ]
        - [ "P38TL-指定内部个人",1, "", $customAccountNo0,"","","","",200, "成功" ]
        - [ "P39TL-指定内部结构，机构已删除",1, "", $customAccountNo0,"","","","ESIGNTESTZDHQSZXWBSCICSSC",1623015, "对应机构不存在" ]
        - [ "P40TL-指定内部结构，机构已删除",1, "", "sign04","","","",$customDepartmentNo0,200, "成功" ]
        - [ "P41TL-指定相对方个人",2, "", $wsignwb01userCode,"","","","",200, "成功" ]
        - [ "P42TL-指定相对方个人，用户已注销",2, "", "wsignwb88","","","","",1623008, "对应用户不存在" ]
        - [ "P43TL-指定相对方机构",2, "", $wsignwb01userCode,"","","","GGGGG",1623015, "对应机构不存在" ]
        - [ "P44TL-指定相对方机构，签署人非机构成员",2, "", "wsignwb03","","","",$wsignwb01orgNo,200, "成功" ]
        - [ "P45TL-签署人与发起人相同",1, "", $customAccountNo0,$departmentName0,"",$departmentName0,"",200, "成功" ]
  testcase: testcases/docs/signFlow/importSignedFilesignerInfos.yml


- name: importSignedFileTC1.yml(场景用例)
  parameters:
  testcase: testcases/docs/signFlow/importSignedFileTC1.yml