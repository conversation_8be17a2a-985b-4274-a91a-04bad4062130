config:
    name: 查询采集模板列表
    variables:
      sp: ""
      lastyear:  ${getDateTime(-365,1)}
      nextyear: ${getDateTime(365,1)}
      wrongdata1: ${getDateTime(1,3)}
      wrongdata2: ${getDateTime(1,2)}
      today: ${getDateTime(0,1)}
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(worg01.orgNo)}
      worg01Code: ${ENV(worg01.orgCode)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
      ###用户离职之后又二次创建
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}
      collectionTemplateId0: ${get_collectionTemplateId()}

testcases:

  - name: 查询采集任务列表-校验组织用户信息
    parameters:
      - name-customAccountNo-userCode-customOrgNo-organizationCode-code-message-data:
          - ["TC1-customAccountNo和userCode都不传",null,null,"$org01No","",200,"成功",0]
#          - ["TC2-customAccountNo和userCode都为空","","","$org01No","",1325001,"创建人编码和创建人账号不能同时为空",0]
          - ["TC3-customAccountNo不传，userCode不存在",null,"XXXXXX","$org01No","",1612149,"collectionTaskCreatorUserCode错误：【",0]
          - ["TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","】采集任务创建人不存在",1612149,"",0]
          - ["TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","",1612150,"collectionTaskCreatorCustomAccountNo错误：【",0]
          - ["TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","",1612150,"collectionTaskCreatorCustomAccountNo错误：【",0]
          - ["TC7-customAccountNo正确，userCode错误","$sign01No","$wsign01Code","$org01No","",1612149,"collectionTaskCreatorUserCode错误：【",0]
          - ["TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","$org01No","",200,"成功",30]
          - ["TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","",1612149,"collectionTaskCreatorUserCode错误：【",0]
          - ["TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","",1612149,"collectionTaskCreatorUserCode错误：【",0]
          - ["TC12-customOrgNo和organizationCode都不传","$sign01No","",null,null,200,"成功",0]
          - ["TC14-customOrgNo为空，organizationCode不存在","$sign01No","","","XXXXXX",1612151,"组织不存在",0]
          - ["TC15-customOrgNo为空，organizationCode相对方","$sign01No","","","$worg01Code",1612151,"组织不存在",0]
          - ["TC16-customOrgNo不存在，organizationCode为空","$sign01No","","XXXXXX","",1612152,"组织不存在",0]
          - ["TC17-customOrgNo相对方账号，organizationCode为空","$sign01No","","$worg01No","",1612152,"】采集任务创建人组织不存在",0]
#          - ["TC18-customOrgNo正确，organizationCode错误","$sign01No","","$org01No","XXXXXX",1612151,"】采集任务创建人组织不存在",0]
          - ["TC19-customOrgNo错误，organizationCode正确","$sign01No","","XXXXXX","$org01Code",200,"成功",30]
#          - ["TC20-organizationCode类型是部门","$sign01No","","$departmentNo","",1325056,"}不是企业",0]
          - ["TC21-organizationCode企业已删除","$sign01No","","$orgCodeDelete","",1612152,"组织不存在",0]
    testcase: testcases/docs/collection/taskList.yml

  - name: 查询采集模板列表-校验采集任务名称和id-collectionTaskName-collectionTaskId
    parameters:
      - name-collectionTaskName-collectionTaskId-code-message-data:
          - [ "TC1-采集任务名称和id为空","","",200,"成功",null ]
          - [ "TC1-采集任务名称不存在","","",200,"成功",null ]
          - [ "TC2-采集任务名称正确，模糊查询","测试","",200,"成功",null ]
#          - [ "TC3-采集任务名称为不支持的特殊字符","\/:*?<>|","",200,"失败",null]
#          - [ "TC4-采集任务ID为不支持的特殊字符","","\/:*?<>|",200,"失败",null]
          - [ "TC5-采集任务ID不存在","","testabc",200,"成功",null]
          - [ "TC6-采集任务ID为中文","","测试采集模板id",200,"成功",null]

    testcase: testcases/docs/collection/taskList.yml

  - name: 查询采集模板列表-校验-collectionTemplateId
    parameters:
      - name-collectionTemplateId-code-message:
          - [ "TC5-采集模板ID不存在","testabc",200,"成功"]
          - [ "TC6-采集模板ID为中文","测试采集模板id",200,"成功"]
          - [ "TC6-采集模板ID加前后空格","$sp $collectionTemplateId0 $sp",200,"成功"]
          - [ "TC7-采集模板ID正确",$collectionTemplateId0",200,"成功"]

    testcase: testcases/docs/collection/templateList.yml

  - name: 查询采集模板列表-校验采集采集任务状态-collectionTaskStatus
    parameters:
      - name-collectionTaskStatus-code-message-data:
          - [ "TC1-采集任务状态为空","",200,"成功",null ]
          - [ "TC1-采集任务状态为abc",abc,1600015,"collectionTaskStatus参数错误!",null ]
          - [ "TC2-采集任务状态为负数",-1,1612193,"collectionTaskStatus错误：采集任务状态不支持传【-1】",null ]
          - [ "TC3-采集任务状态为小数",1.24,1600015,"collectionTaskStatus参数错误!",null]
          - [ "TC4-采集任务状态为3",3,1612193,"collectionTaskStatus错误：采集任务状态不支持传【3】",null]
          - [ "TC5-采集任务状态为0",0,200,"成功",null]
          - [ "TC6-采集任务状态为1",1,200,"成功",null]
          - [ "TC6-采集任务状态为2",2,200,"成功",null]

    testcase: testcases/docs/collection/taskList.yml

  - name: 查询采集模板列表-校验采集任务截止时间-collectionTaskStartExpireTime- collectionTaskEndExpireTime
    parameters:
      -  name-collectionTaskStartExpireTime-collectionTaskEndExpireTime-code-message-data:
          - [ "TC1-开始时间结束时间均为空","","",200,"成功",null ]
          - [ "TC2-开始时间结束时间均正确","$lastyear","$today",200,"成功",null ]
          - [ "TC3-开始时间大于结束时间","$nextyear","$lastyear",1612184,"采集任务截止时间结束时间不能早于起始时间",null ]
          - [ "TC4-时间传不支持的特殊字符","\/:*?<>|","\/:*?<>|",1612174,"collectionTaskStartExpireTime错误: 【",null ]
          - [ "TC5-开始时间格式不正确","$wrongdata1","2099-06-14 20:43:11",1612174,"】时间格式校验错误",null ]
          - [ "TC6-结束格式不正确","$today","$wrongdata2",1612175,"】时间格式校验错误",null ]

    testcase: testcases/docs/collection/taskList.yml


  - name: 查询采集模板列表-校验采集任务创建时间-collectionTaskStartCreateTime- collectionTaskEndCreateTime
    parameters:
      -  name-collectionTaskStartCreateTime-collectionTaskEndCreateTime-code-message-data:
          - [ "TC1-开始时间结束时间均为空","","",200,"成功",null ]
          - [ "TC2-开始时间结束时间均正确","$lastyear","$today",200,"成功",null ]
          - [ "TC3-开始时间大于结束时间","$nextyear","$lastyear",1612185,"采集任务创建时间结束时间不能早于起始时间",null ]
          - [ "TC4-时间传不支持的特殊字符","\/:*?<>|","\/:*?<>|",1612178,"collectionTaskStartCreateTime错误: 【",null ]
          - [ "TC5-开始时间格式不正确,自动会去掉小数","$wrongdata1","2099-06-14 20:43:11",1612178,"】时间格式校验错误",null ]
          - [ "TC6-结束格式不正确","$today","$wrongdata2",1612179,"】时间格式校验错误",null ]

    testcase: testcases/docs/collection/taskList.yml

  - name: 查询采集任务列表-采集任务更新时间-collectionTaskStartUpdateTime- collectionTaskEndUpdateTime
    parameters:
      -  name-collectionTaskStartUpdateTime-collectionTaskEndUpdateTime-code-message-data:
          - [ "TC1-开始时间结束时间均为空","","",200,"成功",null ]
          - [ "TC2-开始时间结束时间均正确","$lastyear","$today",200,"成功",null ]
          - [ "TC3-开始时间大于结束时间","$nextyear","$lastyear",1612186,"采集任务更新时间结束时间不能早于起始时间",null ]
          - [ "TC4-时间传不支持的特殊字符","\/:*?<>|","\/:*?<>|",1612182,"collectionTaskStartUpdateTime错误: 【",null ]
          - [ "TC5-开始时间格式不正确","$wrongdata1","2099-06-14 20:43:11",1612182,"】时间格式校验错误",null ]
          - [ "TC6-结束格式不正确","$today","$wrongdata2",1612183,"】时间格式校验错误",null ]

    testcase: testcases/docs/collection/taskList.yml


  - name: 查询采集模板列表-pageSize-pageNo
    parameters:
      - name-pageSize-pageNo-code-message:
        - [ "TC4-pageSize为小数",1.35,10,1600015,"pageSize参数错误!"]
        - [ "TC5-pageSize为负数",-1,10,1612192,"】单页数量不能小于1"]
        - [ "TC6-pageSize为字母",abc,10,1600015,"pageSize参数错误!"]
        - [ "TC7-pageSize为1000",1000,10,1612190,"】单页数量不能超过50"]
        - [ "TC8-pageNo不存在",10,abc,1600015,"pageNo参数错误!"]
        - [ "TC9-pageNo为小数",1,10.24,1600015,"pageNo参数错误!" ]
        - [ "TC10-pageNo为负数",1,-10,1612188,"】分页查询页码不能小于1" ]
        - [ "TC12-pageNo为1000",10,1000,200,"成功" ]
    testcase: testcases/docs/collection/taskList.yml
