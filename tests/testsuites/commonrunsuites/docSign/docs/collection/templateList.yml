config:
    name: 查询采集模板列表
    variables:
      sp: ""
      lastyear:  ${getDateTime(-365,1)}
      nextyear: ${getDateTime(365,1)}
      wrongdata1: ${getDateTime(1,3)}
      wrongdata2: ${getDateTime(1,2)}
      today: ${getDateTime(0,1)}
      collectionTemplateId0: ${get_collectionTemplateId()}


testcases:

  - name: 查询采集模板列表-校验-collectionTemplateName-collectionTemplateId
    parameters:
      - name-collectionTemplateName-collectionTemplateId-code-message:
          - [ "TC1-采集模板名称和id为空","","",200,"成功" ]
          - [ "TC1-采集模板名称不存在","","",200,"成功" ]
          - [ "TC2-采集模板名称正确，模糊查询","测试","",200,"成功" ]
#          - [ "TC3-采集模板名称为不支持的特殊字符","\/:*?<>|","",200,"失败"]
#          - [ "TC4-采集模板ID为不支持的特殊字符","","\/:*?<>|",200,"失败"]
          - [ "TC5-采集模板ID不存在","","testabc",200,"成功"]
          - [ "TC6-采集模板ID为中文","","测试采集模板id",200,"成功"]
          - [ "TC7-采集模板ID正确","","$collectionTemplateId0",200,"成功"]

    testcase: testcases/docs/collection/templateList.yml

  - name: 查询采集模板列表-校验-StartCreateTime- EndCreateTime
    parameters:
      -  name-StartCreateTime-EndCreateTime-code-message-data:
          - [ "TC1-开始时间结束时间均为空","","",200,"成功",null ]
          - [ "TC2-开始时间结束时间均正确","$lastyear","$today",200,"成功",null ]
          - [ "TC3-开始时间大于结束时间","$nextyear","$lastyear",1612160,"采集模板创建时间结束时间不能早于起始时间",null ]
          - [ "TC4-时间传不支持的特殊字符","\/:*?<>|","\/:*?<>|",1612158,"collectionTemplateStartCreateTime错误: 【",null ]
          - [ "TC5-开始时间格式不正确","$wrongdata1","$today",1612158,"时间格式校验错误",null ]
          - [ "TC6-结束格式不正确","$today","$wrongdata2",1612159,"】时间格式校验错误",null ]

    testcase: testcases/docs/collection/templateList.yml


  - name: 查询采集模板列表-pageSize-pageNo
    parameters:
      - name-pageSize-pageNo-code-message:
        - [ "TC4-pageSize为小数",1.35,10,1600015,"pageSize参数错误!"]
        - [ "TC5-pageSize为负数",-1,10,1612192,"】单页数量不能小于1"]
        - [ "TC6-pageSize为字母",abc,10,1600015,"pageSize参数错误!"]
        - [ "TC7-pageSize为1000",1000,10,1612190,"】单页数量不能超过50"]
        - [ "TC8-pageNo不存在",10,abc,1600015,"pageNo参数错误!"]
        - [ "TC9-pageNo为小数",1,10.24,1600015,"pageNo参数错误!" ]
        - [ "TC10-pageNo为负数",1,-10,1612188,"】分页查询页码不能小于1" ]
        - [ "TC12-pageNo为1000",10,1000,200,"成功" ]
    testcase: testcases/docs/collection/templateList.yml