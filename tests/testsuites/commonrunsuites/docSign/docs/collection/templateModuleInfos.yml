config:
    name: 查询采集模板组件
    variables:
      sp: ""
      collectionTemplateId0: ${get_collectionTemplateId()}


testcases:

  - name: 查询采集模板列表-校验-collectionTemplateName-collectionTemplateId
    parameters:
      - name-collectionTemplateId-code-message:
          - [ "TC1-采集模板id为空","",1612144,"collectionTemplateId错误: 采集模板id不能为空" ]
          - [ "TC2-采集模板ID不存在","testabc",1612161,"采集模板不存在"]
          - [ "TC3-采集模板ID为中文","测试采集模板id",1612161,"采集模板不存在"]
          - [ "TC4-采集模板ID正确","$collectionTemplateId0",200,"成功"]
          - [ "TC5-采集模板ID加前后空格", "$sp $collectionTemplateId0  $sp",200,"成功" ]


    testcase: testcases/docs/collection/templateModuleInfos.yml