#查询已采信息列表
config:
    name: 查询已采集信息列表
    variables:
      lastyear: ${getDateTime(-365,1)}
      nextyear: ${getDateTime(365,1)}
      wrongdata1: ${getDateTime(1,3)}
      wrongdata2: ${getDateTime(1,2)}
      today: ${getDateTime(0,1)}
      collectionTemplateId0: ${get_collectionTemplateId()}
      sp: " "



testcases:

  - name: 查询已采集信息列表-校验-collectedInformationId-collectionTaskId
    parameters:
      - name-collectedInformationId-collectionTaskId-code-message-data:
          - [ "TC1-采集模板名称和id为空","","",200,"成功",null ]
          - [ "TC2-采集模板名称正确，模糊查询","测试","",200,"成功",null ]
#          - [ "TC3-采集模板名称为不支持的特殊字符","\/:*?<>|","",200,"失败",null]
#          - [ "TC4-采集模板ID为不支持的特殊字符","","\/:*?<>|",200,"失败",null]
          - [ "TC5-采集模板ID不存在","","testabc",200,"成功",null]
          - [ "TC6-采集模板ID为中文","","测试采集模板id",200,"成功",null]

    testcase: testcases/docs/collection/InformationList.yml

  - name: 查询采集模板列表-pageSize-pageNo
    parameters:
      - name-pageSize-pageNo-code-message:
          - [ "TC4-pageSize为小数",1.35,10,1600015,"pageSize参数错误!" ]
          - [ "TC5-pageSize为负数",-1,10,1612192,"】单页数量不能小于1" ]
          - [ "TC6-pageSize为字母",abc,10,1600015,"pageSize参数错误!" ]
          - [ "TC7-pageSize为1000",1000,10,1612190,"】单页数量不能超过50" ]
          - [ "TC8-pageNo不存在",10,abc,1600015,"pageNo参数错误!" ]
          - [ "TC9-pageNo为小数",1,10.24,1600015,"pageNo参数错误!" ]
          - [ "TC10-pageNo为负数",1,-10,1612188,"】分页查询页码不能小于1" ]
          - [ "TC12-pageNo为1000",10,1000,200,"成功" ]
    testcase: testcases/docs/collection/InformationList.yml



  - name: 查询采集模板列表-校验-collectionTemplateId
    parameters:
      - name-collectionTemplateId-code-message:
          - [ "TC5-采集模板ID不存在","testabc",1612161,"采集模板不存在"]
          - [ "TC6-采集模板ID为中文","测试采集模板id",1612161,"采集模板不存在"]
          - [ "TC6-采集模板ID加前后空格","$sp $collectionTemplateId0 $sp",200,"成功"]
          - [ "TC7-采集模板ID正确","$collectionTemplateId0",200,"成功"]

    testcase: testcases/docs/collection/InformationList.yml

  - name: 查询采集模板列表-校验采集已采信息状态-collectedInformationStatus
    parameters:
      - name-collectedInformationStatus-code-message-data:
          - [ "TC1-已采信息状态为空","",200,"成功",null ]
          - [ "TC1-已采信息状态为abc",abc,1612194,"collectedInformationStatus错误：已采信息状态不支持传【",null ]
          - [ "TC2-已采信息状态为负数",-1,1612194,"collectedInformationStatus错误：已采信息状态不支持传【",null ]
          - [ "TC3-已采信息状态为小数",1.24,1612194,"collectedInformationStatus错误：已采信息状态不支持传【",null ]
          - [ "TC4-已采信息状态为3",3,200,"成功",null]
          - [ "TC5-已采信息状态为0",0,1612194,"collectedInformationStatus错误：已采信息状态不支持传【0】",null]
          - [ "TC6-已采信息状态为1",1,200,"成功",null]
          - [ "TC6-已采信息状态为2",2,200,"成功",null]
          - [ "TC6-已采信息状态为4",4,200,"成功",null]
          - [ "TC6-已采信息状态为多条件","1,2,3,4",200,"成功",null]
          - [ "TC6-已采信息状态为多条件","0,1,2,3,4",1612194,"collectedInformationStatus错误：已采信息状态不支持传【",null]
          - [ "TC6-已采信息状态为多条件","0,1,2,5,4",1612194,"collectedInformationStatus错误：已采信息状态不支持传【",null]

    testcase: testcases/docs/collection/InformationList.yml


  - name: 查询已采集信息列表-校验-moduleId-moduleValue
    parameters:
      - name-moduleValue-moduleId-code-message-data:
          - [ "TC1-moduleValue名称和id为空","","",200,"成功",null ]
          - [ "TC2-moduleValue名称正确，模糊查询","测试","",200,"成功",null ]
          - [ "TC3-moduleValue名称为不支持的特殊字符","\/:*?<>|","",200,"成功",null]
          - [ "TC4-moduleValue为不支持的特殊字符","","\/:*?<>|",200,"成功",null]
          - [ "TC5-moduleValue不存在","","testabc",200,"成功",null]
          - [ "TC6-moduleValue为中文","","测试采集模板id",200,"成功",null]
          - [ "TC7-moduleValue支持逗号分割模糊查询","测试,123","",200,"成功",null]

    testcase: testcases/docs/collection/InformationList.yml