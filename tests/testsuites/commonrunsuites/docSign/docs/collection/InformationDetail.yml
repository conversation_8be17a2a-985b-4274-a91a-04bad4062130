config:
    name: 查询已采信息详情
    variables:
      sp: ""
      collectionTemplateId0: ${get_collectionTemplateId()}
      collectedInformationId0: "1735263921751785472"


testcases:

  - name: 查询已采信息详情-校验-collectedInformationId-collectionTemplateId
    parameters:
      - name-collectedInformationId-collectionTemplateId-code-message-data:
#          - [ "TC1-已采信息id和采集模板id均为空","","",1612144,"collectionTemplateId错误: 采集模板id不能为空",null ]
          - [ "TC1-已采信息id和采集模板id不匹配","$collectedInformationId0","",1612144,"采集模板id不能为空",null ]
          - [ "TC2-已采信息id正确，采集模板id为空","测试","",1612144,"collectionTemplateId错误: 采集模板id不能为空",null ]
          - [ "TC3-已采信息id为空，采集模板id有值","","$collectionTemplateId0",1612162,"collectedInformationId错误，已采信息id不能为空",null]
          - [ "TC4-采集模板ID为不支持的特殊字符","$collectedInformationId0","\/:*?<>|",1612170,"查询采集模板组件失败：采集模板不存在",null]
          - [ "TC5-已采信息id为支持的特殊字符","$collectionTemplateId0","testabc",1612170,"查询采集模板组件失败：采集模板不存在",null]
          - [ "TC6-采集模板ID为中文","$collectionTemplateId0","测试采集模板id",1612170,"查询采集模板组件失败：采集模板不存在",null]

    testcase: testcases/docs/collection/InformationDetail.yml