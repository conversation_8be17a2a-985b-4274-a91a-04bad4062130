config:
    name: 新建采集任务
    variables:
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(worg01.orgNo)}
      worg01Code: ${ENV(worg01.orgCode)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
      ###用户离职之后又二次创建
      resetCreateAccountNo: ${ENV(resetCreateAccountNo)}
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}
      lastyear: ${getDateTime(-365,1)}
      nextyear: ${getDateTime(365,1)}
      wrongdata1: ${getDateTime(1,3)}
      wrongdata2: ${getDateTime(1,2)}
      today: ${getDateTime(3,1)}
      assignUsers0: [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}]
      assignUsersWrongPhone: [{"assignUserName":"测试采集一","contactContent":"1988008","contactType": 0}]
      assignUsersmail: [{"assignUserName":"测试采集一","contactContent":"<EMAIL>","contactType": 1}]
      assignUsersNot:  [{"assignUserName":"\/:*?<>|","contactContent":"<EMAIL>","contactType": 1}]
      assignUserswrong: [{"assignUserName":"测试采集一","contactContent":"***********@","contactType": 1}]
      contactTypeNull: [{"assignUserName":"测试采集一","contactContent":"***********","contactType": ""}]
      contactType2: [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 2}]
      contactContentNot: [{"assignUserName":"测试采集一","contactContent":"\/:*?<>|","contactType": 1}]
      assignUsers3: [{"assignUserName":"测试采集一","contactContent":"***********","contactType": 0}, {"assignUserName":"测试采集二","contactContent":"<EMAIL>","contactType": 3}]
      collectionTemplateId0: ${get_collectionTemplateId()}

testcases:

  - name: 新建采集任务-校验-collectionTaskName-collectionTemplateId
    parameters:
      - name-collectionTemplateName-collectionTemplateId-code-message-data:
          - [ "TC1-采集模板名称和id为空","","",1612144,"collectionTemplateId错误: 采集模板id不能为空",null ]
#          - [ "TC3-采集模板名称为不支持的特殊字符"," \/:*?<>| ","$collectionTemplateId0",1612144,"失败",null]
          - [ "TC5-采集模板ID不存在","","testabc",1612168,"创建采集任务失败：采集模板不存在",null]
          - [ "TC6-采集模板ID为中文","","测试采集模板id",1612168,"创建采集任务失败：采集模板不存在",null]

    testcase: testcases/docs/collection/taskCreate.yml



  - name: 新建采集任务-校验组织用户信息
    parameters:
      - name-customAccountNo-userCode-customOrgNo-organizationCode-code-message-data:
          - ["TC1-customAccountNo和userCode都不传",null,null,"$org01No","",1612147,"collectionTaskCreatorUserCode和collectionTaskCreatorCustomAccountNo二选一必填",0]
          - ["TC2-customAccountNo和userCode都为空","","","$org01No","",1612147,"collectionTaskCreatorUserCode和collectionTaskCreatorCustomAccountNo二选一必填",0]
          - ["TC3-customAccountNo不传，userCode不存在",null,"XXXXXX","$org01No","",1612149,"collectionTaskCreatorUserCode错误：【XXXXXX】采集任务创建人不存在",0]
          - ["TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","",1612149,"】采集任务创建人不存在",0]
          - ["TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","",1612150,"collectionTaskCreatorCustomAccountNo错误：",0]
          - ["TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","",1612150,"】采集任务创建人不存在",0]
          - ["TC7-customAccountNo正确，userCode错误","$sign01No","$wsign01Code","$org01No","",1612149,"】采集任务创建人不存在",0]
          - ["TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","$org01No","",200,"成功",30]
#          - ["TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","",1612191,"}不存在",0]
          - ["TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","",1612149,"】采集任务创建人不存在",0]
#          - ["TC11-customAccountNo用户离职之后又创建成功","$resetCreateAccountNo","","$org01No","",200,"成功",30]
          - ["TC12-customOrgNo和organizationCode都不传","$sign01No","",null,null,1612148,"collectionTaskCreatorOrganizationCode和collectionTaskCreatorCustomOrgNo二选一必填",0]
          - ["TC13-customOrgNo和organizationCode都为空","$sign01No","","","",1612148,"collectionTaskCreatorOrganizationCode和collectionTaskCreatorCustomOrgNo二选一必填",0]
          - ["TC14-customOrgNo为空，organizationCode不存在","$sign01No","","","XXXXXX",1612151,"采集任务创建人组织不存在",0]
          - ["TC15-customOrgNo为空，organizationCode相对方","$sign01No","","","$worg01Code",1612151,"】采集任务创建人组织不存在",0]
          - ["TC16-customOrgNo不存在，organizationCode为空","$sign01No","","XXXXXX","",1612152,"】采集任务创建人组织不存在",0]
          - ["TC17-customOrgNo相对方账号，organizationCode为空","$sign01No","","$worg01No","",1612152,"】采集任务创建人组织不存在",0]
          - ["TC18-customOrgNo正确，organizationCode错误","$sign01No","","$org01No","XXXXXX",1612151,"】采集任务创建人组织不存在",0]
          - ["TC19-customOrgNo错误，organizationCode正确","$sign01No","","XXXXXX","$org01Code",200,"成功",30]
          - ["TC20-organizationCode类型是部门","$sign01No","","$departmentNo","",200,"成功",0]
          - ["TC21-organizationCode企业已删除","$sign01No","","$orgCodeDelete","",1612152,"】采集任务创建人组织不存在",0]
    testcase: testcases/docs/collection/taskCreate.yml

  - name: 新建采集任务-校验采集任务截止时间、是否需要审核、是否自动发起
    parameters:
      -  name-collectionTaskExpireTime-collectionTaskApprove-automaticInitiation-code-message-data:
          - [ "TC1-采集任务截止时间为空报错","",1,1,1612153,"collectionTaskExpireTime错误：采集任务截止时间不能为空",null ]
          - [ "TC2-采集任务截止时间为去年","$lastyear",0,0,1612168,"创建采集任务失败：任务截止时间不能小于当前时间",null ]
#          - [ "TC3-采集任务截止时间格式错误","$wrongdata1",0,0,1612154,"】时间格式校验错误",null ]
          - [ "TC4-采集任务截止时间正确","$nextyear",0,0,200,"成功",null ]
          - [ "TC5-collectionTaskApprov为1","$nextyear",1,0,200,"成功",null ]
          - [ "TC6-collectionTaskApprov为2","$today",2,0,1612195,"collectionTaskApprove错误：是否需要审核不支持传【2】",null ]
          - [ "TC7-collectionTaskApprov为小数2.3","$today",2,0,1612195,"collectionTaskApprove错误：是否需要审核不支持传【2】",null ]
          - [ "TC8-collectionTaskApprov为abc","$today",abc,0,1600015,"collectionTaskApprove参数错误!",null ]
          - [ "TC9-collectionTaskApprov为负数","$today",-1,0,1612195,"collectionTaskApprove错误：是否需要审核不支持传【-1】",null ]
          - [ "TC10-collectionTaskApprov为0","$today",0,0,200,"成功",null ]
          - [ "TC11-automaticInitiation为1","$nextyear",1,0,200,"成功",null ]
          - [ "TC12-automaticInitiation为2","$today",1,2,1612197,"automaticInitiation错误：是否自动发起不支持传【2】",null ]
          - [ "TC13-automaticInitiation为小数2.3","$today",0,2.3,1600015,"automaticInitiation参数错误!",null ]
          - [ "T14-automaticInitiation为abc","$today",0,abc,1600015,"automaticInitiation参数错误!",null ]
          - [ "TC15-automaticInitiation为负数","$today",0,-1,1612197,"automaticInitiation错误：是否自动发起不支持传【-1】",null ]
          - [ "TC16-automaticInitiation为0","$today",0,0,200,"成功",null ]
          - [ "TC17-自动发起和需要审核均为1是报错","$today",1,1,1612168,"创建采集任务失败：自动发起配置项开启时业务模版必填",null ]

    testcase: testcases/docs/collection/taskCreate.yml

  - name: 新建采集任务-任务类型-指定人员名称
    parameters:
      -  name-collectionTaskType-assignUsers-code-message-data:
          - [ "TC1-collectionTaskType不传，默认为公开采集","",[],200,"成功",null ]
          - [ "TC2-collectionTaskType为0，公开采集",0,[],200,"成功",null ]
          - [ "TC3-collectionTaskType为1,assignUsers不传，报错",1,[],1612168,"创建采集任务失败：指定人员不能为空",null ]
          - [ "TC4-collectionTaskType为1，assignUsers正确",1,"$assignUsers0",200,"成功",null ]
          - [ "TC5-collectionTaskType为3,assignUsers为空",3,[],1612146,"collectionTaskType错误: 任务类型不支持传3",null ]
          - [ "TC7-collectionTaskType为小数2.3",2.3,[],1600015,"collectionTaskType参数错误!",null ]
          - [ "TC8-collectionTaskType为abc",abc,[],1600015,"collectionTaskType参数错误!",null ]
          - [ "TC9-collectionTaskType为负数",-1,[],1612146,"collectionTaskType错误: 任务类型不支持传-1",null ]
          - [ "TC4-collectionTaskType为1，邮箱正确",1,"$assignUsersmail",200,"成功",null ]
          - [ "TC4-collectionTaskType为1，手机号错误",1,"$assignUsersWrongPhone",1612200,"contactContent错误：手机号格式不正确",null ]
          - [ "TC4-collectionTaskType为1，姓名不支持的特殊字符",1,"$assignUsersNot",1612166,"assignUserName错误: 【/:*?<>|】不支持的特殊字符",null ]
          - [ "TC4-collectionTaskType为1，contactType为空",1,"$contactTypeNull",200,"成功",null ]
          - [ "TC4-collectionTaskType为1，手机号和类型不匹配",1,"$contactType2",1612199,"contactType错误：联系方式不支持传【2】",null ]
          - [ "TC4-collectionTaskType为1，邮箱错误",1,"$assignUserswrong",1612201,"contactContent错误：邮箱格式不正确",null ]
          - [ "TC4-collectionTaskType为1，手机号不支持的特殊字符",1,"$contactContentNot",1612167,"contactContent错误: 【/:*?<>|】不支持的特殊字符",null ]


    testcase: testcases/docs/collection/taskCreate.yml

  - name: 新建采集任务-任务类型-automaticInitiation-businessTemplateCode
    parameters:
      -  name-automaticInitiation-businessTemplateCode-code-message-data:
          - [ "TC1-automaticInitiation不传，默认为否","","",200,"成功",null ]
          - [ "TC2-automaticInitiation为0",0,"",200,"成功",null ]
          - [ "TC3-automaticInitiation为1,businessTemplateCode为空报错",1,"",1612168,"创建采集任务失败：自动发起配置项开启时业务模版必填",null ]
          - [ "TC7-automaticInitiation为小数2.3",2.3,1,1600015,"automaticInitiation参数错误!",null ]
          - [ "TC8-automaticInitiation为abc",abc,1,1600015,"automaticInitiation参数错误!",null ]
          - [ "TC9-automaticInitiation为负数",-1,1,1612197,"automaticInitiation错误：是否自动发起不支持传【-1】",null ]
          - [ "TC10-automaticInitiation为1，businessTemplateCode不存在",1,"abc",1612198,"businessTemplateCode错误：【业务模板配置不存在】",null ]

    testcase: testcases/docs/collection/taskCreate.yml

  - name: 场景用例
    testcase: testcases/docs/collection/scene.yml