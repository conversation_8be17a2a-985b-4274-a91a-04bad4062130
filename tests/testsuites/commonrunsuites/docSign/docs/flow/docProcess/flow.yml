config:
    name: "流程作废"

testcases:
  - name: 我管理的-作废删除
    testcase: testcases/docs/flow/docProcess/manage_flow_revoke_delete.yml

  - name: 我发起的-作废删除
    testcase: testcases/docs/flow/docProcess/owner_flow_revoke_delete.yml


  - name: 页面作废-发起方、签署方、抄送方有兼职组织，作废时发起方的兼职组织不存在
    testcase: testcases/docs/flow/docProcess/flow_revoke.yml

  - name: 单方签署，有审批，单人审批之后是并行会签
    testcase: testcases/docs/flow/docProcess/parallel_countersignature_flow.yml


  - name: 审批环节签署上传文件和附件
    testcase: testcases/docs/flow/docProcess/submitTaskForAuditAddFile.yml

