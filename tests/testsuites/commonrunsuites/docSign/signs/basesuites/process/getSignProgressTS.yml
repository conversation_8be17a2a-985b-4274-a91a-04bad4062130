config:
    variables:
      - bizNo: ${get_business_no()}
      - orSignFlowId: ${get_sign_scene(5,0)}
#      - projectId: ${ENV(esign.projectId)}
#      - headers: ${get_headers_with_autho($projectId)}
#      - sign: ${sign_wrap($orSignFlowId, False, $headers,1)}
testcases:
-
         name: 获取签署流getSignProgress
         testcase: testcases/signs/process/getSignProgressTC.yml

####这些场景已经归拢到signDetail的校验和各种签署场景中，不需要单独校验
-
         name: 场景用例
         parameters:
           - name-processId-status:
#               - ["P1TL-获取已签署完成流程", "${get_signFlowId_status(2,$bizNo, True, False, 1,1,0)}", 2]
               - ["P1TL-获取已拒签流程", "${ENV(pdf.reject.processId)}", 4]
               - ["P1TL-获取作废中流程", "${ENV(pdf.revocking.processId)}", 6]
               - ["P1TL-获取已作废流程", "${ENV(pdf.revock.processId)}", 5]
               - ["P1TL-获取已过期流程", "${ENV(pdf.overdue.processId)}", 3]
#               - ["P1TL-获取签署中状态的流程", "${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}", 1]
#               - ["P1TL-获取无序签签署中流程", "${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}", 1]
#               - ["P1TL-获取多人顺序签签署中流程", "${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}", 1]
##               - ["P1TL-获取或签签署完成流程", $orSignFlowId, 2]
##               - ["P1TL-获取草稿状态的流程", "${gen_startSignFlow_node(0)}", 0]
         testcase: testcases/signs/process/getSignProgressStatusTC.yml
