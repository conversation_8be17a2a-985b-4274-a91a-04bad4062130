config:
    variables:
      - bizNo: ${get_business_no()}
      - proccessId0: ${get_signFlowId_status(1,$bizNo, true, false, 1,1,0)}
      - encryptedValues0: ${getContenMd5(proccessId0)}
testcases:
-
         name: 场景用例
         parameters:
           - name-processId-processDocId-encryptedValues-status-message:
               - ["TC1-processId为空", "", "",$encryptedValues0,1701049,"非法密文"]
               - ["TC2-processDocId为空", $proccessId0, "",$encryptedValues0,1701049,"非法密文"]
               - ["TC3-encryptedValues为空", $proccessId0, "1111","",1701049,"非法密文"]
               - ["TC4-encryptedValues密文错误", $proccessId0, "1111","111",1701049,"非法密文"]
               - ["TC5-processId不存在", "XXXXXXX", "1111","",1701049,"非法密文"]
         testcase: testcases/signs/process/previewTC.yml
