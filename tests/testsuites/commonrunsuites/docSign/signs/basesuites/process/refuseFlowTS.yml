config:
    variables:
     #流程的签署主体是机构签署的流程
     - data0: ${get_refusing_data( False, True, 0,1)}
     - data1: ${get_refusing_data( False, True, 0,1)}
     - organizationCode0: ${get_data_param($data0,organizeCode)}
     - userCode0: ${get_data_param($data0,userCode)}
     - organizationCode1: ${get_data_param($data1,organizeCode)}
     - userCode1: ${get_data_param($data1,userCode)}
     # 个人签署的流程
     - dataPerson0: ${get_refusing_data(True, False, 0,1)}

testcases:
-   name: 拒签-processId
    parameters:
        - name-processId-status-message:
            - ["TC1-processId不传，报错",NULL,1703001,"流程id不能为空"]
            - ["TC2-processId为空,报错","",1703001,"流程id不能为空"]
            - ["TC3-processId传入不正确的值,报错","${get_randomNo()}",1702007,"流程不存在"]
    testcase: testcases/signs/process/refuseFlow/processIdTC.yml

-   name: 拒签-applyId
    parameters:
        - name-applyId-status-message:
            - ["TC4-applyId不传，报错",NULL,1703001,"意愿id不能为空"]
            - ["TC5-applyId为空,报错","",1703001,"意愿id不能为空"]
            - ["TC6-applyId传入不正确的值,报错","${get_randomNo()}",1702328,"签署人意愿id不一致!"]
    testcase: testcases/signs/process/refuseFlow/applyIdTC.yml

-   name: 拒签-refuseReason
    parameters:
        - name-refuseReason-status-message:
            - ["TC7-refuseReason不传，报错",NULL,1703001,"拒签原因不能为空"]
            - ["TC8-refuseReason为空,报错","",1703001,"拒签原因不能为空"]
            - ["TC9-refuseReason长度超过100字符,报错","${generate_random_str(101)}",1703001,"拒签原因原因不能大于100字"]
    testcase: testcases/signs/process/refuseFlow/refuseReasonTC.yml

-   name: 拒签-userCode and organizeCode
    parameters:
        - name-userCode-organizeCode-status-message:
            - ["TC10-userCode不传，报错",NULL,"${organizationCode0}",1702304,"无权操作"]
            - ["TC11-userCode为空,报错","","${organizationCode0}",1702304,"无权操作"]
            - ["TC12-userCode不存在的值,报错","${generate_random_str(10)}","${organizationCode0}",1702304,"无权操作"]
            - ["TC13-userCode和流程ID不匹配,报错","en","${organizationCode0}",1702304,"无权操作"]
            - ["TC14-userCode正确值organizeCode为空，流程的签署主体是机构签署，报错","${userCode1}","",1702028,"该签署人不存在"]
            - ["TC15-userCode正确值organizeCode为不存在的值，流程的签署主体是机构签署,报错","${userCode1}","${generate_random_str(10)}",1702028,"该签署人不存在"]
    testcase: testcases/signs/process/refuseFlow/userCodeAndOrganizeCodeTC.yml

-   name: 拒签-异常场景
    testcase: testcases/signs/process/refuseFlow/refuseExceptSceneTC.yml

-   name: 拒签-正常流程场景
    testcase: testcases/signs/process/refuseFlow/refuseSuccessSceneTC.yml
