config:
    variables:
      - signFlowIdFinish: "${get_signFlowId_status(2,checkBill, true, false, 1,1,0)}"
      - signFlowIdDraft: "${get_signFlowId_status(0,checkBill, true, false, 1,1,0)}"
      - signFlowIdRevok: ${ENV(pdf.revock.processId)}
      - signFlowId0: "${get_signFlowId_status(1,checkBill, true, false, 1,1,0)}"
      - signFlowIdCharging: "${get_signFlowId_status(10, None, None, None, None, None,0)}"
      - organizeCode0: ${ENV(sign01.main.orgCode)}
      - userCode0: ${ENV(sign01.userCode)}
      - organizeCodeOuter0: ${ENV(worg01.orgCode)}
      - userCodeOuter0: ${ENV(wsignwb01.userCode)}

testcases:
-
         name: 校验用户是否购买包年套餐用例
         parameters:
           - name-processId-organizeCode-userCode-status-message:
               - ["TC1-校验的流程为空，报错", "", $organizeCode0, $userCode0, 1702007, "流程不存在"]
               - ["TC2-校验的流程不传，报错", None, $organizeCode0, $userCode0, 1702007, "流程不存在"]
               - ["TC3-校验的流程不存在，报错", "XXXXX", $organizeCode0, $userCode0, 1702007, "流程不存在"]
               - ["TC4-校验的用户为空，报错", $signFlowId0, $organizeCode0, '', 1703001, "用户code不能为空"]
#               - ["TC5-校验的用户不传，报错", $signFlowId0, $organizeCode0, None, 1701999, "您不是流程的签署人"]
               ###V6.0.12.0修改流程和用户关联校验，不是取userCode，改取登录态里面的用户信息
               - ["TC6-校验的机构为空，报错", $signFlowId0, '', $userCode0, 200, "成功"]
               - ["TC7-校验的机构不传，报错", $signFlowId0, None, $userCode0, 1701999, "您不是流程的签署人"]
               - ["TC8-校验的流程状态-签署完成，报错", $signFlowIdFinish, "", $userCode0, 1702261, "流程状态非签署中"]
#               - ["TC9-校验的流程状态-草稿，报错", $signFlowIdDraft, $organizeCode0, $userCode0, 1702261, "流程状态非签署中"]
#               - ["TC10-校验的流程状态-已作废，报错", $signFlowIdRevok, $organizeCode0, $userCode0, 1702261, "流程状态非签署中"]
               - ["TC11-校验的流程中没有指定的签署人，报错", $signFlowIdCharging, $organizeCode0, $userCode0, 1701999, "您不是流程的签署人"]
               - ["TC12-校验的流程中没有指定的签署机构，报错", $signFlowIdCharging, $organizeCode0, $userCodeOuter0, 1701999, "您不是流程的签署人"]
               - ["TC13-校验的流程不是相对方付费的流程", $signFlowId0, $organizeCode0, $userCode0, 200, "成功"]
#               - ["TC14-校验出流程签署方没有购买套餐", $signFlowIdCharging, $organizeCodeOuter0, $userCodeOuter0, 200, "成功"]

         testcase: testcases/signs/process/checkBillingPackageTC.yml

-
         name: 校验-addAttachmentFileKeys
         testcase: testcases/signs/process/checkTC.yml
