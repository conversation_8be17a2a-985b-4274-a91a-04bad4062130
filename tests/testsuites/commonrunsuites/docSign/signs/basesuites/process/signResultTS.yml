config:
    variables:
      - project_id: ${ENV(esign.projectId)}
      - project_secrect: ${ENV(esign.projectSecret)}
      - bizNo: ${get_business_no()}
      - userCode0: ${ENV(sign01.userCode)}
      - organizeCode0: ${ENV(sign01.main.orgCode)}
      ################################个人签
      - project_id: ${ENV(esign.projectId)}
      - processId1: ${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}
      - headers1:  ${get_headers_with_autho()}
      - sign: ${sign_wrap($processId1, False, $headers1,1)}
      ###############################机构签
      - proccessIdOrg: ${get_signFlowId_status(1,$bizNo, False, True, 0,1,0)}
      - headers2:  ${get_headers_with_autho()}
      - sign2: ${sign_wrap($proccessIdOrg, True, $headers2,1)}
testcases:
-
         name: 单接口校验
         parameters:
           - name-processId-userCode-organizeCode-status-success-message:
               - ["P1TL-个人签organizeCode不传，其他参数正确", $processId1, $userCode0,Null,200, True, "成功"]
               - ["P1TL-个人签organizeCode传入", $processId1, $userCode0,$organizeCode0,1702257, False, "当前流程无待签署人"]
               - ["P1TL-userCode为空", $processId1, "",Null,1703001, False, "用户id不能为空"]
               - ["P1TL-userCode不传", $processId1, Null,Null,1703001, False, "用户id不能为空"]
               - ["P1TL-userCode不存在", $processId1, "errorUserCode",Null,1702304, False, "无权操作"]
               - ["P1TL-processId为空", "", $userCode0,Null,1702007, False, "流程不存在"]
               - ["P1TL-processId不传", Null, $userCode0,Null,1703001, False, "流程id不能为空"]
               - ["P1TL-processId不存在", "Naoidjfo1", $userCode0,Null,1702007, False, "流程不存在"]
               - ["P1TL-机构签organizeCode不传", $proccessIdOrg, $userCode0,Null,1702257, False, "当前流程无待签署人"]
               - ["P1TL-机构签organizeCode为空", $proccessIdOrg, $userCode0,"",1702257, False, "当前流程无待签署人"]
               - ["P1TL-机构签organizeCode不存在", $proccessIdOrg, $userCode0,"adfa2",1702257, False, "当前流程无待签署人"]
               - ["P1TL-机构签参数正确", $proccessIdOrg, $userCode0,$organizeCode0,200, True, "成功"]
         testcase: testcases/signs/process/signResult/signResultTC.yml
-
         name: 场景校验
         parameters:
           - name-processId-userCode-organizeCode-status-success-message-signStatus:
               - ["P1TL签署完成流程结果查询", "${get_sign_scene(5,0)}", $userCode0,Null,200, True, "成功", 2]
               - ["P1TL-签署失败流程结果查询", "${get_signFlowId_status(7,$bizNo, True, False, 0,1,0)}", $userCode0,Null,200, True, "成功",3]
         testcase: testcases/signs/process/signResult/signResultStatusTC.yml
-
         name: 场景校验2
         parameters:
           - name-processId-userCode-organizeCode-status-success-message-data:
               - ["P1TL-签署中流程结果查询", "${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}", $userCode0,Null,200, True, "成功", Null]
               - ["P1TL-草稿状态流程结果查询", "${gen_startSignFlow_node(0)}", $userCode0,Null,1702001, False, "签署人信息不存在", Null]
               - ["P1TL-拒签状态流程结果查询", "${ENV(pdf.reject.processId)}", $userCode0,Null,200, True, "成功", Null]
         testcase: testcases/signs/process/signResult/signResultEmptyTC.yml

-
         name: 相对方场景
         testcase: testcases/signs/process/signResult/signResultOutTC.yml