config:
    variables:
     #签署
      - project_id: ${ENV(esign.projectId)}

testcases:
-   name: 签署-taskUUID
    parameters:
        - name-taskUUID-status-message:
            - ["TC13-taskUUID不传，报错",NULL,1703001,"签署任务uuid不能为空"]
            - ["TC14-taskUUID为空,报错","",1702016,"签署人与签署任务不匹配"]
            - ["TC15-taskUUID传入不正确的值,报错","${get_randomNo()}",1702016,"签署人与签署任务不匹配"]
    testcase: testcases/signs/process/standardFlowSign/innerParam/taskUUIDTC.yml

-   name: 签署-processDocId
    parameters:
        - name-processDocId-status-message:
            - ["TC16-processDocId不传，报错",NULL,1703001,"流程文档id不能为空"]
            - ["TC17-processDocId为空,报错","",1703001,"流程文档id不能为空"]
            - ["TC18-processDocId传入不正确的值,报错","${get_randomNo()}",1702031,"流程文档不存在"]
    testcase: testcases/signs/process/standardFlowSign/innerParam/processDocIdTC.yml

-   name: 签署-detailRequests
    parameters:
        - name-detailRequests-status-message:
            - ["TC19-detailRequests不传，报错",NULL,1703001,"签署位置信息不能为空"]
            - ["TC20-detailRequests为空,报错","",1703012,"detailRequests字段类型不匹配"]
            - ["TC21-detailRequests传入null,报错",[],1703001,"签署位置信息不能为空"]
    testcase: testcases/signs/process/standardFlowSign/innerParam/detailRequestsTC.yml

-   name: 签署-detailRequests-param(内部参数)
    testcase: testcases/signs/process/standardFlowSign/innerParam/detailRequestsParamTC.yml