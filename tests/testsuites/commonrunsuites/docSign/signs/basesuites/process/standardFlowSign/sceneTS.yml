config:
    variables:
        #签署
        - project_id: ${ENV(esign.projectId)}

testcases:
-    name: 签署-内部个人场景
     testcase: testcases/signs/process/standardFlowSign/scene/personInnerSceneTC.yml
-    name: 签署-内部机构场景
     testcase: testcases/signs/process/standardFlowSign/scene/organizeInnerSceneTC.yml
-    name: 签署-相对方个人场景
     testcase: testcases/signs/process/standardFlowSign/scene/personOppositeSceneTC.yml
-    name: 签署-相对方机构场景
     testcase: testcases/signs/process/standardFlowSign/scene/organizeOppositeSceneTC.yml
-    name: 签署-多签署方场景
     testcase: testcases/signs/process/standardFlowSign/scene/fixSceneTC.yml
-    name: 签署-关键字定位非必签场景
     testcase: testcases/signs/process/standardFlowSign/scene/keyWordSignNotMustSign.yml
-    name: 页面签署骑缝场景补充-骑缝签坐标超出页面计算兜底逻辑
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene1TC.yml
-    name: 签署的时候坐标和页码的校验
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene2TC.yml
-    name: ukey意愿
     testcase: testcases/signs/process/standardFlowSign/ukeyWill.yml

-    name: V6.0.14.0-beta.1-PDF一步发起流程支持签署指定宽高-内部个人签署
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene3TC.yml

-    name: V6.0.14.0-beta.1-OFD分步发起流程支持签署指定宽高-内部个人+企业+相对方个人签署
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene4TC.yml

-    name: V6.0.14.0-beta.1-页面发起-设置长方形宽高
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene5TC.yml

-    name: V6.0.14.0-beta6页面发起增加扩展字段管理
     testcase: testcases/signs/process/standardFlowSign/extensionListTC.yml

-    name: V6.0.14.0-beta.8-自建CA-PDF-新建企业绑定法人发起流程，查看签署页的印章信息
     testcase: testcases/signs/process/standardFlowSign/standardFlowScene7TC.yml


