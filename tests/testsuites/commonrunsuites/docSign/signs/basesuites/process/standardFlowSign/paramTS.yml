config:
    variables:
     #签署
      - project_id: ${ENV(esign.projectId)}

testcases:
-   name: 签署-processUUId
    parameters:
        - name-processUUId-status-message:
            - ["TC1-processUUId不传，报错",NULL,1703001,"流程uuid不能为空"]
            - ["TC2-processUUId为空,报错","",1702007,"流程不存在"]
            - ["TC3-processUUId传入不正确的值,报错","${get_randomNo()}",1702007,"流程不存在"]
    testcase: testcases/signs/process/standardFlowSign/param/processIdTC.yml

-   name: 签署-applyId
    parameters:
        - name-applyId-status-message:
            - ["TC4-applyId不传，报错",NULL,1702328,"签署人意愿id不一致!"]
            - ["TC5-applyId为空,报错","",1702328,"签署人意愿id不一致!"]
            - ["TC6-applyId传入不正确的值,报错","${get_randomNo()}",1702328,"签署人意愿id不一致!"]
    testcase: testcases/signs/process/standardFlowSign/param/applyIdTC.yml

-   name: 签署-accountCode
    parameters:
        - name-accountCode-status-message:
            - ["TC7-accountCode不传，报错",NULL,1703001,"用户id不能为空"]
            - ["TC8-accountCode为空,报错","",1703001,"用户id不能为空"]
            - ["TC9-accountCode不存在的值,报错","${generate_random_str(10)}",1702280,"签署者不存在或已注销"]
            - ["TC10-accountCode和流程ID不匹配,报错","en",1702280,"签署者不存在或已注销"]
#            - ["TC11-organizeCode不传，签署主体为机构，报错","${accountCode0}",null,1702280,"签署者不存在或已注销"]
#            - ["TC12-organizeCode为空，签署主体为机构，报错","${accountCode0}","",1702280,"签署者不存在或已注销"]
#            - ["TC13-organizeCode不存在，签署主体为机构，报错","${accountCode0}","${generate_random_str(10)}",1702280,"签署者不存在或已注销"]
    testcase: testcases/signs/process/standardFlowSign/param/accountCodeTC.yml

-   name: 签署-organizeCode
    testcase: testcases/signs/process/standardFlowSign/param/organizeCodeTC.yml

-   name: 签署-signDocInfoRequests
    parameters:
        - name-signDocInfoRequests-status-message:
            - ["TC14-signDocInfoRequests不传，报错",NULL,1703001,"签署信息不能为空"]
            - ["TC15-signDocInfoRequests为空,报错","",1703012,"signDocInfoRequests字段类型不匹配"]
            - ["TC16-signDocInfoRequests传入[],报错",[],1703001,"签署信息不能为空"]
    testcase: testcases/signs/process/standardFlowSign/param/signDocInfoRequestsTC.yml

-   name: 签署-willAuthFlag
    testcase: testcases/signs/process/standardFlowSign/param/willAuthFlagTC.yml




