config:
    name: 关键字解析
    variables:
      - keyPdf: 'pdf-page30'
      - project_id: ${ENV(esign.projectId)}
      - fileKey0: $${ENV(fileKey)}

testcases:
-   name: 关键字解析-fileKey
    parameters:
      - name-fileKey-status-message:
        - ["TC1-输入正确的入参数据，成功", $fileKey0,200,"成功"]
        - ["TC2-fileKey字段不传，其他入参传入正确值,报错",null,1703001,"文件id不能为空"]
        - ["TC3-fileKey为空，其他入参传入正确值,报错","",1703001,"文件id不能为空"]
#        - ["TC4-fileKey不存在，其他入参传入正确值,成功","XXX.pdf",1702472,"文件不存在"]
    testcase: testcases/signs/process/keyword/fileKeyTC.yml

-   name: 关键字解析-keyList
    parameters:
      - name-keyList-status-message:
        - ["TC5-keyList字段不传，其他入参传入正确值,报错", null,1703001,"关键字列表不能为空"]
        - ["TC6-keyList传入不存在的关键字，报错",["不存在"],1702109,"未找到关键字"]
        - ["TC7-keyList传入部分不存在的关键字,报错",["甲方","不存在"],1702109,"在《key30page.pdf》内未找到关键字【不存在】"]
        - ["TC8-keyList传入的关键字长度超过20字符,报错",["${generate_random_str(21)}"],1702059,"关键字长度不可超出20个字符!"]
        - ["TC9-keyList传入特殊字符查询关键字，报错", ["甲方(盖章):","~!@#$","Publisher: O'Reilly"],1702060,"关键字存在非法字符!"]
        - ["TC10-keyList传入带空格的关键字查询关键字，正常返回keyPositions的信息,成功", ["乙 方(签字)"],1702109,"未找到关键字"]
        - ["TC10-keyList传入带空格的关键字查询关键字，正常返回keyPositions的信息,成功", ["乙方(签字)"],200,"成功"]
        - ["TC11-keyList传入带生僻字关键字查询关键字，正常返回keyPositions的信息,成功", ["《蕘垚犇骉镳䶮叕囍囎呝"],200,"成功"]
        - ["TC12-keyList传入带繁体字关键字查询关键字，正常返回keyPositions的信息,成功", ["繁體"],200,"成功"]
        - ["TC13-keyList传入关键字的总个数大于10个,报错", ["甲 方","乙 方","1","甲 方","乙 方","1","甲 方","乙 方","1","甲 方","乙 方","1","甲 方","乙 方","1","甲 方","乙 方","1"],1702108,"一次提交关键字长度不可超过10个！"]
        - ["TC14-keyList传入重复关键字，正常返回keyPositions的信息,成功", ["甲方","甲方"],200,"成功"]
        - ["TC15-keyList传入的关键字一个带空格，一个不带空格查询关键字，正常返回keyPositions的信息,成功", ["甲方","甲 方"],1702109,"未找到关键字"]
    testcase: testcases/signs/process/keyword/keyListTC.yml

-   name: 关键字解析-pageNo
    parameters:
      - name-pageNo-status-message:
        - ["TC16-pageNo不传，正常返回keyPositions的信息，成功", null,200,"成功"]
        - ["TC17-pageNo传空,正常返回keyPositions的信息，成功","",200,"成功"]
        - ["TC18-pageNo传的分隔符非英文逗号,报错","1;2",1701007,"页码格式不合法"]
        - ["TC19-pageNo传的分隔符是中文逗号,报错","1，2",1701007,"页码格式不合法"]
        - ["TC20-pageNo传的页码超过了文档的总页数,报错","9999",1702109,"未找到关键字"]
        - ["TC21-pageNo传的页码=0,报错","0",1702323,"页码不正确"]
        - ["TC22-pageNo传的页码有重合,正常返回keyPositions的信息，成功","1,3,5,2-6",200,"成功"]
        - ["TC23-pageNo传的页码=-1,报错","-1",1701007,"页码格式不合法"]
        - ["TC24-pageNo传的页码=-999,报错","-999",1701007,"页码格式不合法"]
        - ["TC25-pageNo传的页码不重合,正常返回keyPositions的信息，成功","1,2,3,5,6",200,"成功"]
    testcase: testcases/signs/process/keyword/pageNoTC.yml
