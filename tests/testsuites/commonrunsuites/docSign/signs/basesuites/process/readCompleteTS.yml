config:
    variables:
      - bizNo: ${get_business_no()}
      - proccessId: ${get_signFlowId_status(1,$bizNo, true, false, 1,1,0)}
      - actorId0: ${get_proccess_actor_uuid($proccessId,0,0)}

testcases:
-
         name: 场景用例
         parameters:
           - name-processId-actorId-status-success-message:
               - ["TC1-processId和actorId均为空", "", "",1702387, false, "流程参与人uuid不存在！"]
               - ["TC2-processId和actorId均不为空且值正确", $proccessId, "$actorId0",200,true,"成功"]
               - ["TC3-processId为空，actorId值正确", "", "$actorId0",1701041,false,"流程id不可为空"]
               - ["TC4-processId不为空且值正确，actorId值为空", $proccessId, "",1702387,false,"流程参与人uuid不存在！"]
               - ["TC5-processId不为空且值正确，actorId值不正确", $proccessId, "daf121",1702387,false,"流程参与人uuid不存在！"]
         testcase: testcases/signs/process/readCompleteTC.yml
