config:
    variables:
     - project_id: ${ENV(esign.projectId)}
     - param_pdf: [0,0]

testcases:
-   name: 批量签署-批量签署结果反馈列表-batchTaskUuId
    parameters:
        - name-batchTaskUuId-status-message:
            - ["TC1-batchTaskUuId不传，报错",NULL,1703001,"批量签署任务uuid不能为空"]
            - ["TC2-batchTaskUuId为空,报错","",1703001,"批量签署任务uuid不能为空"]
            - ["TC3-batchTaskUuId传入不正确的值,报错","${get_randomNo()}",1702421,"批量签署任务不存在"]
#            - ["TC4-batchTaskUuId传入正确的pdf的值，成功","${gen_simple_batch_sign($param_pdf)}",200,"成功"]
    testcase: testcases/signs/process/batchSign/result/batchTaskUuIdTC.yml
