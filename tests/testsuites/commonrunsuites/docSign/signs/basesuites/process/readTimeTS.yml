config:
    variables:
      - bizNo: ${get_business_no()}
      - proccessId: ${ENV(pdf.signing.countersign.processId)}
      - uuid: ${get_proccess_actor_uuid($proccessId,0,0)}
testcases:
-
         name: 场景用例
         parameters:
           - name-processId-actorId-status-success-message:
               - ["P1TL-processId和actorId均为空", "", "",1702387, false, "流程参与人uuid不存在！"]
               - ["P1TL-processId和actorId均不为空且值正确", $proccessId, $uuid,200,true,"成功"]
               - ["P1TL-processId为空，actorId值正确", "", $uuid,1701041,false,"流程id不可为空"]
               - ["P1TL-processId不为空且值正确，actorId值为空", $proccessId, "",1702387,false,"流程参与人uuid不存在！"]
               - ["P1TL-processId不为空且值正确，actorId值不正确", $proccessId, "daf121",1702387,false,"流程参与人uuid不存在！"]
         testcase: testcases/signs/process/readTimeTC.yml
