config:
    variables:
      - bizNo: ${get_business_no()}
testcases:
-
         name: 获取签署发起详情detail, processId测试
         testcase: testcases/signs/process/detail/detailProcessIdTC.yml
-
         name: 获取签署发起详情detail, organizeCode测试
         testcase: testcases/signs/process/detail/detailOrgCodeTC.yml
-
         name: 获取签署发起详情detail, requestSource测试
         testcase: testcases/signs/process/detail/detailRequestSourceTC.yml
-
         name: 获取签署发起详情detail, approvalProcessId测试
         testcase: testcases/signs/process/detail/detailApproveTC.yml
-
         name: 场景用例
         parameters:
           - name-processId-status-msg:
               - ["P1TL-获取签署完成的流程详情", "${get_signFlowId_status(2,$bizNo, true, false, 1,1,0)}",200,'成功']
               - ["P1TL-获取拒签流程的详情", "${ENV(pdf.reject.processId)}",200,'成功']
               - ["P1TL-获取作废中流程详情", "${ENV(pdf.revocking.processId)}",200,'成功']
               - ["P1TL-获取已作废的流程详情", "${ENV(pdf.revock.processId)}",200,'成功']
               - ["P1TL-获取过期的流程详情", "${ENV(pdf.overdue.processId)}",200,'成功']
               - ["P1TL-签署中获取的流程详情", "${get_signFlowId_status(1,$bizNo, true, false, 1,1,0)}",200,'成功']
               - ["P1TL-签署中流程带静默签署流程", "${get_signFlowId_status(1,$bizNo, true, false, 1,1,0)}",200,'成功']
               - ["P1TL-签署中无序签流程", "${get_signFlowId_status(1,$bizNo, true, false, 1,1,0)}",200,'成功']
               - ["P1TL-签署中流程顺序签流程", "${get_signFlowId_status(1,$bizNo, true, false, 0,1,0)}",200,'成功']
               - ["P1TL-获取草稿流程详情", "${gen_startSignFlow_node(0)}",1702115,'当来源为open-api且流程状态为草稿时 不可获取详情！']
         testcase: testcases/signs/process/detail/detailStatusTC.yml
