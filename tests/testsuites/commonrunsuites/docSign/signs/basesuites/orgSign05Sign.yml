config:
  #由于(ORG-SIGN-05)可能适配了很多场景，比如只有法人章，只有企业章，没有证书等，去执行签署的场景
  #所以必须要顺序签署，因而但凡调用这个企业的都需要集合在这个脚本中，确保是顺序执行
    name: esigntest同级企业CI(ORG-SIGN-05)的签署场景

testcases:
-
  name: 批量签署-PDF流程-只有法人章的企业签署
  testcase: testcases/signs/portal/task/batchSignTC14.yml

-
  name: 批量签署-经办人没有印章企业只有法人章的企业签署使用默认签署和非默认印章签署
  testcase: testcases/signs/portal/task/batchSignTC15.yml

-
  name: 批量签署-经办人没有印章企业有企业和法人章的企业签署使用默认签署和非默认印章签署
  testcase: testcases/signs/portal/task/batchSignTC16.yml

-
  name: PDF-企业无RSA证书但是有法人信息和印章，查看签署页的印章信息和签署
  testcase: testcases/signs/process/standardFlowSign/standardFlowScene6TC.yml

-
  name: 只有sm2印章签署
  testcase: testcases/signs/signFlow/quickSign/quickSignWithbase64OnlySM2.yml