config:
    name: 签署手写板任务
    variables:
      - equip02: "uuidid30bf8617-d193-4974-95f2-dc37ade9c8e0"
      - equip01: "uuidid3b026e89-e8df-4dc7-b9f6-61a6d0e16943"

testcases:
-
  name: SETUP-手写板任务-开启
  parameters:
    - keyIdCode1-value1:
        - [ "equip_task_switch","1" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-一步or分步发起验证手写板任务
  testcase: testcases/signs/equipSign/equipListSceneTC.yml

-
  name: openapi-业务模板发起验证手写板任务
  testcase: testcases/signs/equipSign/equipListSceneTC2.yml

-
  name: openapi-含手写板的转交
  testcase: testcases/signs/equipSign/equipListSceneTC3.yml

-
  name: openapi-一步or分步发起验证手写板任务-OFD
  testcase: testcases/signs/equipSign/equipListSceneTC4-OFD.yml

-
  name: openapi-增加equipAutoOpen
  testcase: testcases/signs/equipSign/equipListSceneTC6-equipAutoOpen.yml

-
  name: openapi-填写可以推送手写板
  testcase: testcases/signs/equipSign/equipListSceneTC7.yml

-
  name: openapi-指纹章
  testcase: testcases/signs/equipSign/equipListSceneTC8.yml

-
  name: SETUP-手写板任务-关闭
  parameters:
    - keyIdCode1-value1:
        - [ "equip_task_switch","0" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-手写板开关关闭后发起手写板任务失败
  testcase: testcases/signs/equipSign/equipListSceneTC5.yml