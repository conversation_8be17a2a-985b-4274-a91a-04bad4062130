config:
    name: 获取手绘印章二维码返回正确的uuid
    variables:
        handUrl: ${get_draw_seal_url()}
        specialCharacters: ${get_not_support_str()}
        bizNo: ${get_business_no()}

testcases:
-
    name: 获取手绘印章二维码返回正确的uuid
    parameters:
      - name-uuid-processId-handDrawName-url-code-message:
          - [ "TC20-校验正常调用接口之后，出参有正确的uuid值","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","测试文敏",$handUrl,200,"成功" ]
    testcase: testcases/signs/handPaintedSeal/base64QrCode.yml


