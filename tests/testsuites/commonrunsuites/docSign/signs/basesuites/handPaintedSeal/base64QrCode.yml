config:
    name: 获取手绘印章二维码
    variables:
        handUrl: ${get_draw_seal_url()}
        specialCharacters: ${get_not_support_str()}
        bizNo: ${get_business_no()}

testcases:
-
    name: 获取手绘印章二维码
    parameters:
        - name-uuid-processId-handDrawName-url-code-message:
#            ----------------------------------------------单接口校验-------------------------------------------------------
            - [ "TC1-正确的参数调用接口能够获取手绘印章","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","测试文敏",$handUrl,200,"成功" ]
            - [ "TC2-handDrawName包含不支持的特殊字符，能正常调用成功","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}",$specialCharacters,$handUrl,200,"成功" ]
            - [ "TC3-handDrawName包含支持的特殊字符，能正常调用成功","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","迪丽热巴·新疆人名",$handUrl,200,"成功" ]
            - [ "TC4-非必填项handDrawName字段不传，能正常调用成功","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","",$handUrl,200,"成功" ]
            - [ "TC21-非必填项handDrawName字段为空，能正常调用成功",null,"${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","",$handUrl,200,"成功" ]

            # 用例与接口表现不一致，期望报数据过长，实际不会对数据长度校验，直接报sql错误
#            - [ "TC6-非必填项handDrawName字段长度校验超过长度，能正常报错","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","过长的数据过长的数据过长的数据过长的数据过长的数据过长的数据过长的数据过长的数据过长的数据",$handUrl,1701005,"" ]

            - [ "TC7-processId不传，能正常报错","","","测试文敏",$handUrl,1702007,"流程不存在" ]
            - [ "TC22-processId为空，能正常报错",null,"","测试文敏",$handUrl,1702007,"流程不存在" ]
            - [ "TC8-processId为不存在的流程，能正常报错","uuid","123456789","测试文敏",$handUrl,1702007,"流程不存在" ]

#            # 9 23 10 用例与接口表现不一致  期望报错，实际成功返回。接口逻辑上不会去校验url
            - [ "TC9-url不传，能正常报错","","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","测试文敏","",200,"成功" ]
            - [ "TC23-url为空，能正常报错",null,"${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","测试文敏","",200,"成功" ]
            - [ "TC10-url为错误值，能正常报错","uuid","${get_signFlowId_status(1,$bizNo,True,False,0,1,0)}","测试文敏","www.baidu.com",200,"成功" ]
            - [ "TC11-processId长度超过64字符，能正常报错","","0f1e2b2b5dd12f4225c89f7d21a81d9d0f1e2b2b5dd12f4225c89f7d21a81d9d","测试文敏",$handUrl,1702007,"流程不存在" ]


#            ----------------------------------------------场景校验-------------------------------------------------------
            # 以下用例与接口表现不一致，期望报错，实际不会校验流程状态以及业务类型配置
            - [ "TC12-processId的状态是草稿状态，能正常报错","","${get_signFlowId_status(0,$bizNo,True,False,0,1,0)}","霍梦阳",$handUrl,200,"成功" ]
            - [ "TC13-processId的状态是签署完成状态，能正常报错","","${get_signFlowId_status(2,$bizNo, True, False, 1,1,0)}","霍梦阳",$handUrl,200,"成功" ]
            - [ "TC14-processId的状态是拒签状态，能正常报错","",'${ENV(pdf.reject.processId)}',"霍梦阳","${handUrl}",200,"成功" ]
            - [ "TC15-processId的状态是过期状态，能正常报错","","${ENV(pdf.overdue.processId)}","霍梦阳",$handUrl,200,"成功" ]
            - [ "TC17-processId的状态是作废类型的签署中状态的流程，能正常调用成功","","${ENV(pdf.revock.processId)}","测试文敏",$handUrl,200,"成功" ]
            - [ "TC18-processId是签署中但是只允许机构签署，能正常报错","","${get_signFlowId_status(1,$bizNo,False,True,0,1,0)}","霍梦阳",$handUrl,200,"成功" ]
            - [ "TC19-processId配置的业务类型不允许手绘签署，能正常报错","","${get_signFlowId_status(1,$bizNo,False,True,0,1,0)}","霍梦阳",$handUrl,200,"成功" ]

    testcase: testcases/signs/handPaintedSeal/base64QrCode.yml


