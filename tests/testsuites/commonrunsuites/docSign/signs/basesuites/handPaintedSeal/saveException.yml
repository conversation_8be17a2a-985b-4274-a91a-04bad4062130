config:
    name: 保存手绘印章图片正常场景
    variables:
        huoBase64: ${get_constant(base64)}
        MengBase64: ${get_constant(base64_2)}
        handDrawName1: "霍梦阳"
        handUrl: ${get_draw_seal_url()}
        uuid0: ${get_hand_seal_uuid($processId1, $handDrawName1, $handUrl)}
        bizNo: ${get_business_no()}
        specialCharacters: ${get_not_support_str()}
        handUrl1: ${get_draw_seal_url()}
        processId1: ${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}
        uuid1: ${get_hand_seal_uuid($processId1, $handDrawName1, $handUrl1)}
testcases:
-
    name: 保存手绘印章图片异常场景
    parameters:
        - name-processId-uuid-handDrawName-fileBase64-code-success-message:
            - [ "TC1-uuid为空，能正常报错","$processId1","","霍梦阳",$huoBase64,1703001,false,"手绘章唯一标识不能为空" ]
            - [ "TC2-uuid不传，能正常报错","$processId1","","霍梦阳",$huoBase64,1703001,false,"手绘章唯一标识不能为空" ]
            - [ "TC3-uuid传入不存在的手绘记录id，能正常报错","$processId1","123456","霍梦阳",$huoBase64,1702037,false,"手绘印章记录不存在" ]
            - [ "TC4-fileBase64为空，能正常报错","$processId1",$uuid0,"霍梦阳","",1703001,false,"文件base64不能为空" ]
            - [ "TC5-fileBase64不传，能正常报错","$processId1",$uuid0,"霍梦阳","",1703001,false,"文件base64不能为空" ]
#            接口有bug，数组下标越界
#            - [ "TC6-fileBase64传入错误的信息，接口调用正常，但是手绘图片不存在","1f4a88ec139cdd9d2bd897eac3df3030",$uuid0,"霍梦阳","12345678910",200,true,"成功" ]
    testcase: testcases/signs/handPaintedSeal/saveException.yml


