config:
    name: 获取手绘印章get异常场景

testcases:
-
    name: 获取手绘印章get异常场景
    parameters:
        - name-uuid-processId-handDrawName-url-code-success-message:
            - [ "TC1-uuid为空，能正常报错","","","","",1702037,false,"手绘印章记录不存在" ]
            - [ "TC2-uuid不传，能正常报错","","","","",1702037,false,"手绘印章记录不存在" ]
            - [ "TC3-uuid传入不存在的手绘记录id，能正常报错","123456","","","",1702037,false,"手绘印章记录不存在" ]
    testcase: testcases/signs/handPaintedSeal/getException.yml


