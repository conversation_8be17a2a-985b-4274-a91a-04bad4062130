config:
    name: 获取手绘印章配置异常场景
    variables:
        handUrl: ${get_draw_seal_url()}
        specialCharacters: ${get_not_support_str()}

testcases:
-
    name: 获取手绘印章配置异常场景
    parameters:
        - name-uuid-code-message:
              - [ "TC1-uuid为空，能正常报错","",1703001,"手绘章唯一标识不能为空" ]
              - [ "TC2-uuid不传，能正常报错","",1703001,"手绘章唯一标识不能为空" ]
              - [ "TC3-uuid传入不存在的手绘记录id，能正常报错","1234567",1702037,"手绘印章记录不存在" ]
    testcase: testcases/signs/handPaintedSeal/configException.yml


