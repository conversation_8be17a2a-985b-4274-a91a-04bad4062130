config:
    name: 获取手绘印章get
    variables:
        huoBase64: ${get_constant(base64)}
        MengBase64: ${get_constant(base64_2)}
        bizNo: ${get_business_no()}
        handDrawName1: "自动化case创建手绘"
        handUrl1: ${get_draw_seal_url()}
        processId1: ${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}
        uuid1: ${get_hand_seal_uuid($processId1, $handDrawName1, $handUrl1)}
        fileKey1: ${get_hand_seal_file_key($uuid1, $huoBase64)}
testcases:
-
    name: 获取手绘印章get正常场景
    parameters:
        - name-uuid-processId-handDrawName-url-fileKey-code-success-message:
            - [ "TC1-输入正确的入参数据，能正常调用接口","$uuid1","$processId1","$handDrawName1","$handUrl1","$fileKey1",200,true,"成功" ]
            - [ "TC2-processId和handDrawName和url传入错误的值，接口能够正常调用","$uuid1","123456","123456","123456","$fileKey1",200,true,"成功" ]
            - [ "TC3-校验正常调用接口之后，出参有正确的uuid值","$uuid1","$processId1","$handDrawName1","$handUrl1","$fileKey1",200,true,"成功" ]
            - [ "TC4-processId和handDrawName和url都是非必填项，不传，接口能够正常调用","$uuid1","","","","$fileKey1",200,true,"成功" ]
    testcase: testcases/signs/handPaintedSeal/get.yml


