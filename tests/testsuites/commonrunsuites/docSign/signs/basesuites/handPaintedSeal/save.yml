config:
    name: 保存手绘印章图片正常场景
    variables:
        huoBase64: ${get_constant(base64)}
        MengBase64: ${get_constant(base64_2)}
        bizNo: ${get_business_no()}

testcases:
-
    name: 保存手绘印章图片正常场景
    parameters:
        - name-processId-handDrawName-fileBase64-code-success-message:
            - [ "TC1-输入正确的入参数据，能正常调用接口","${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}","霍梦阳",$huoBase64,200,true,"成功" ]
            - [ "TC2-校验正常调用接口之后，出参有正确的fileKey","${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}","霍梦阳",$MengBase64,200,true,"成功" ]
    testcase: testcases/signs/handPaintedSeal/save.yml


