config:
    name: 获取手绘配置正常返回
    variables:
        bizNo: ${get_business_no()}
        handUrl: ${get_draw_seal_url()}
        specialCharacters: ${get_not_support_str()}
        handUrl1: ${get_draw_seal_url()}
        handDrawName1: "自动化case创建手绘"
        processId1: ${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}
        uuid1: ${get_hand_seal_uuid($processId1, $handDrawName1, $handUrl1)}

testcases:
-
    name: 获取手绘配置正常返回
    parameters:
        - name-uuid-aiHandEnable-handDrawName-code-message-success:
              - [ "TC4-uuid传入正确的手绘记录，能正常调用接口","$uuid1",1,"$handDrawName1",200,"成功",true ]
              - [ "TC5-校验正常调用接口之后，出参有正确的aiHandEnable值和handDrawName信息","$uuid1",1,"$handDrawName1",200,"成功",true ]
    testcase: testcases/signs/handPaintedSeal/config.yml