config:
    variables:
      - bizNo: ${get_business_no()}
      - processId01: ${get_signFlowId_status(1,$bizNo, True, False, 0, 1,0)}
      - userCode01: ${ENV(sign01.userCode)}
      - redirectUrl01: ${get_willingRedirectUrl()}
testcases:
-
      name: 单接口校验
      parameters:
          - name-processId-userCode-redirectUrl-status-message-success:
            - [ "P0TL-正确的参数调用接口能够获取url", $processId01, $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为空，能正常报错", "", $userCode01,$redirectUrl01,1702027,"流程id或用户id不能为空",false]
            - [ "P1TL-processId不传，能正常报错", Null, $userCode01,$redirectUrl01,1702007,"流程不存在",false]
            - [ "P1TL-processId不存在，能正常报错", "ww1", $userCode01,$redirectUrl01,1702007,"流程不存在",false]
            - [ "P1TL-processId和userCode不匹配，能正常报错", $processId01, "${ENV(wsignwb01.userCode)}",$redirectUrl01,1701029,"用户无预览权限",false]
            - [ "P1TL-userCode不传，能正常报错", $processId01, "",$redirectUrl01,1702027,"流程id或用户id不能为空",false]
            - [ "P1TL-userCode为空，能正常报错", $processId01, Null,$redirectUrl01,1701029,"用户无预览权限",false]
            - [ "P1TL-userCode不存在，能正常报错", $processId01, "aa",$redirectUrl01,1701029,"用户无预览权限",false]
            - [ "P1TL-userCode传入流程的机构信息，能正常报错", $processId01, "${ENV(csqs.orgCode)}",$redirectUrl01,1701029,"用户无预览权限",false]
            - [ "P1TL-redirectUrl不传，能正常报错", $processId01, $userCode01,"",1702403,"重定向地址不能为空！",false]
            - [ "P1TL-redirectUrl为空，能正常报错", $processId01, $userCode01," ",1702403,"重定向地址不能为空！",false]
            - [ "P1TL-redirectUrl传入非正常的地址，目前没有校验，允许调用成功", $processId01, $userCode01,"123",200,"成功",true]
      testcase: testcases/signs/auth/action/actionTC.yml
-
      name: 场景接口
      parameters:
          - name-processId-userCode-redirectUrl-status-message-success:
            - [ "P1TL-校验正常调用接口之后，出参有正确的uuid值", "${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}", $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为拒签状态，获取url成功", "${ENV(pdf.reject.processId)}", $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为作废状态，获取url成功", "${ENV(pdf.revock.processId)}", $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为过期状态，获取url成功", "${ENV(pdf.overdue.processId)}", $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为作废中状态，获取url成功", "${ENV(pdf.revocking.processId)}", $userCode01,$redirectUrl01,200,"成功",true]
            - [ "P1TL-processId为草稿状态，获取url成功", "${gen_startSignFlow_node(0)}", $userCode01,$redirectUrl01,200,"成功",true]
      testcase: testcases/signs/auth/action/actionTC.yml

-
      name: 相对方用户
      testcase: testcases/signs/auth/action/actionTCOut.yml

-
      name: 相对方用户saas免登
      testcase: testcases/signs/auth/realNameForSaaSNoLoginTC.yml