config:
    variables:
      - bizNo: ${get_business_no()}
      - redirectUrl0: ${get_willingRedirectUrl()}
      - projectId: ${ENV(esign.projectId)}
      - organizeCode0:  ${ENV(sign01.main.orgCode)}
testcases:
-
      name: 单接口校验
      variables:
        - signFlowId0: ${get_signFlow_with_unrealName_user()}
      parameters:
          - name-processId-forceRealName-redirectUrl-type-organizeCode-status-message-success:
#            - [ "P0TL-正确的参数调用接口-个人", $signFlowId0, True, $redirectUrl0,  1,Null ,200,"成功",true]
            - [ "P1TL-type不传，能正常报错", $signFlowId0, True, $redirectUrl0,  Null, $organizeCode0,1703001,"用户类型不能为空",False]
            - [ "P1TL-type为非指定值0，能正常报错", $signFlowId0, True, $redirectUrl0,  -1, $organizeCode0, 1703001,"用户类型不正确",False]
            - [ "P1TL-type传入非int类型的值，能正常报错", $signFlowId0, True, $redirectUrl0,  a, $organizeCode0, 1703012,"type字段类型不匹配",False]
            - [ "P1TL-processId不传，能正常报错", Null, True, $redirectUrl0,  1, $organizeCode0, 1702007,"流程不存在",False]
            - [ "P1TL-processId为空，能正常报错", "", True, $redirectUrl0,  1, $organizeCode0,1702007,"流程不存在",False]
            - [ "P1TL-processId不存在的流程，能正常报错", "afa", True, $redirectUrl0,  1, $organizeCode0, 1702007,"流程不存在",False]
            - [ "P1TL-processId长度超过64字符，能正常报错", "afasdfadfaf23r3d1ea31df1321ds121", True, $redirectUrl0,  1, $organizeCode0, 1702007,"流程不存在",False]
#            - [ "TC9-redirectUrl不传，能正常报错", $signFlowId0, True, Null,  1, $organizeCode0, 1707003,"获取个人实名授权地址失败: 参数redirectUrl必填!",False]
            - [ "TC9-redirectUrl不传，能正常报错", $signFlowId0, True, Null,  1, $organizeCode0, 1703001,"redirectUrl不能为空!",False]
            - [ "TC10-redirectUrl传入为空，能正常报错", $signFlowId0, True, "",  1, $organizeCode0, 1703001,"redirectUrl不能为空!",False]
#            - [ "TC11-redirectUrl传入错误的值，能正常报错", $signFlowId0, True, "sss",  1, $organizeCode0, 1450003,"参数格式错误：redirectUrl",False]
            - [ "TC12-redirectUrl传入3000长度，能正常报错", $signFlowId0, True, "${generate_random_str(3001)}",  1,Null, 1703001,"redirectUrl的长度不能超过3000!",False]
            - [ "TC13-organizeCode传入不存在的值，成功返回", $signFlowId0, True, $redirectUrl0,  1,qqq ,1701037,"机构不存在",False]
            - [ "TC14-forceRealName传入类型不正确，能正常报错", $signFlowId0, sss, $redirectUrl0,  1,$organizeCode0 ,1703012,"forceRealName字段类型不匹配",False]
            - [ "TC15-校验正常调用接口之后，出参有正确的url值-机构", $signFlowId0, True, $redirectUrl0,  2,$organizeCode0 ,1702001,"签署人信息不存在",False]
            - [ "TC16-获取机构实名，非强制实名", $signFlowId0, False, $redirectUrl0,  2,$organizeCode0 ,1702001,"签署人信息不存在",False]
      testcase: testcases/signs/auth/realNameTC.yml

-
      name: 外部用户
      variables:
        - signFlowId: ${get_sign_scene(7,0)}
        - userCode: ${ENV(wsignwb01.userCode)}
        - headers0: ${get_headers_with_outer_person($userCode)}
      parameters:
          - name-processId-forceRealName-redirectUrl-type-organizeCode-status-message-success-headers:
            - [ "TC1-正确的参数调用接口-外部个人", $signFlowId, True, $redirectUrl0,  1,Null ,200,"成功",true, $headers0]
      testcase: testcases/signs/auth/realNameTC.yml

#-
#      name: 单接口tenantCode校验
#      variables:
#        - signFlowId0: ${get_signFlow_with_unrealName_user()}
#      parameters:
#          - name-processId-forceRealName-redirectUrl-type-organizeCode-tenantCode-status-message-success:
##            - [ "P0TL-tenantCode为正整数", $signFlowId0, True, $redirectUrl0,  1,$organizeCode0 ,1,200,"成功",true]
#            - [ "P0TL-tenantCode为负数", $signFlowId0, True, $redirectUrl0,  1,$organizeCode0 ,-1,200,"成功",true]
#            - [ "P0TL-tenantCode为字母", $signFlowId0, True, $redirectUrl0,  1,$organizeCode0 ,"a",1703012,"tenantCode字段类型不匹配",False]
#      testcase: testcases/signs/auth/realNameTC.yml