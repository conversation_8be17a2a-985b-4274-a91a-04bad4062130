config:
    variables:
      - bizNo: ${get_business_no()}
      - processId22: ${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}
      - applyId2: ${get_applyId($processId22, False)}
      - processId01: ${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}
      - applyId01: ${get_applyId($processId01, False)}
testcases:
-
      name: 单接口校验
      parameters:
          - name-processId-applyId-status-message-success:
            - [ "P0TL-正确的参数调用接口能够获取意愿的结果", $processId22, $applyId2,200,"成功",true]
            - [ "P1TL-processId不传，能正常报错", Null, $applyId2,1702007,"流程不存在",false]
            - [ "P1TL-processId为空，能正常报错", "", $applyId2,1702007,"流程不存在",false]
            - [ "P1TL-processId不存在的流程，能正常报错", "asdas", $applyId2,1702007,"流程不存在",false]
            - [ "P1TL-processId长度超过64字符，能正常报错", "asdasadsadsacdffqewfddszvreafearfzdvcdzvsf", $applyId2,1702007,"流程不存在",false]
            - [ "P1TL-applyId为空，能正常报错", $processId22, "", 1707006,"获取意愿结果失败",false]
            - [ "P1TL-applyId不传，能正常报错", $processId22, Null, 1707006,"获取意愿结果失败",false]
            - [ "P1TL-applyId不存在的流程，能正常报错", $processId22, "qqq", 1707006,"获取意愿结果失败",false]
            - [ "P1TL-applyId长度超过64字符，能正常报错", $processId22, "dsafafadsfa243r4aeq2edsf2sdf34fsdadfaffasd13x", 1707006,"获取意愿结果失败: 请求方法失败",false]
            - [ "P1TL-applyId和processId不相关，返回0", $processId22, $applyId01, 200,"成功",true]
            - [ "P1TL-校验正常调用接口之后，出参有正确的result值", $processId22, $applyId2,200,"成功",true]
      testcase: testcases/signs/auth/willing/resultTC.yml

-
      name: 场景校验
      testcase: testcases/signs/auth/willing/resultSignTC.yml
-
      name: 场景校验2
      testcase: testcases/signs/auth/willing/resultRefuseTC.yml

#-
#      name: 外部个人-场景校验
#      testcase: testcases/signs/auth/willing/resultOutTC.yml


