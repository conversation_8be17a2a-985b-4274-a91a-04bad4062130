config:
    variables:
        - psnName: ${get_constant(zhixuanUserNameForRealName)}
        - psnIdNo: ${get_constant(zhixuanIdNoForRealName)}
        - mobile: ${get_constant(zhixuanMobileForRealName)}
        - userCode: ${get_constant(zhixuanUserCodeForRealName)}
        - processId: ${gen_inner_realname_processId($userCode,None)}
        - notifyUrl1: ${gen_realName_notifyUrl($userCode,,$processId)}
testcases:
-
    name: 发起运营商3要素核身认证-实名-$CaseName
    testcase: testcases/signs/auth/psnAuthPub/telecom3Factors_verifyCodeSuccess.yml
    parameters:
        - CaseName-name-idNo-mobileNo-grade-authcode-notifyUrl:
#            - ["正常场景-简版三要素-正确验证码","测试芷萱自动化","350781196403076805","19999999901","STANDARD","123456",$notifyUrl1]
            - ["正常场景-简版三要素-正确验证码",$psnName,$psnIdNo,$mobile,"STANDARD","123456", $notifyUrl1]

