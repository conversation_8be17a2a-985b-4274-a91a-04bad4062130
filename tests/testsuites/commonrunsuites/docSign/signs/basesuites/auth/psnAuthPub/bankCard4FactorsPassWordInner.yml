config:
    variables:
        - psnName: ${get_constant(zhixuanUserNameForRealName)}
        - psnIdNo: $${get_constant(zhixuanIdNoForRealNameHZ)}
        - mobile: ${get_constant(zhixuanMobileForRealName)}
        - userCode: ${get_constant(zhixuanUserCodeForRealName)}
        - processId: ${gen_inner_realname_processId($userCode,None)}
        - notifyUrl1: ${gen_realName_notifyUrl($userCode,,$processId)}
        - bankNo: ${get_constant(zhixuanBankNo)}

testcases:
-
    name: 发起银行卡4要素核身认证-证件类型是护照-$CaseName
    testcase: testcases/signs/auth/psnAuthPub/bankCard4Factors_verifyCodeSuccess.yml
    parameters:
        - CaseName-certType-name-idNo-mobileNo-bankCardNo-grade-authcode-notifyUrl:
            - ["正常场景-护照-正确验证码","INDIVIDUAL_PASSPORT",$psnName,$psnIdNo,$mobile,$bankNo,"STANDARD","123456",$notifyUrl1]
