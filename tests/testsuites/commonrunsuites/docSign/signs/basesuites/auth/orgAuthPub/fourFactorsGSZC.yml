config:
    name: 发起企业核身认证4要素校验
    variables:
        - organizationName: ${get_constant(organizationNameForRealName)}
        - organizationNo: ${get_constant(organizationNoForRealNameGSZC)}
        - legalName: ${get_constant(zhixuanUserNameForRealName)}
        - legalIdNo: ${get_constant(zhixuanIdNoForRealName)}
        - business_no: ${get_business_no()}
        - userCode: ${get_constant(zhixuanUserCodeForRealName)}
        - innerOrganizationCode: ${getInnerOrganizationCode($userCode)}
        - processId: ${gen_inner_realname_processId($userCode,$innerOrganizationCode)}
#        - processId: "613bce240a6c40e61c10adeba80bf9f6"
        - notifyUrl1: ${gen_realName_notifyUrl($userCode,$innerOrganizationCode,$processId)}
#备注：1.核身没有人工审核 2.四要素发起，没有法定代表人认证 3.除发起企业核身认证4要素校验单接口，其他异常场景和实名一致，不重复写
#116
testcases:
-
    name: 发起企业核身认证4要素校验-随机金额打款认证-工商注册号-$CaseName
    testcase: testcases/signs/auth/orgAuthPub/orgInnerGSZC.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-cardNo-subbranch-amount-bank-notifyUrl:
             - ["企业信息都正确(常规企业)-工商注册号",$organizationName,$organizationNo,$legalName,$legalIdNo,"INDIVIDUAL_CH_IDCARD","**************","平安银行杭州高新支行","0.01","平安银行",$notifyUrl1]