config:
    name: 发起企业核身认证4要素校验
    variables:
        - organizationName: ${get_constant(organizationNameForRealName)}
        - organizationNo: ${get_constant(organizationNoForRealNameTYSH)}
        - legalName: ${get_constant(zhixuanUserNameForRealName)}
        - legalIdNo: ${get_constant(zhixuanIdNoForRealNameHZ)}
        - business_no: ${get_business_no()}
        - userCode: ${get_constant(zhixuanUserCodeForRealName)}
        - innerOrganizationCode: ${getInnerOrganizationCode($userCode)}
        - processId: ${gen_inner_realname_processId($userCode,$innerOrganizationCode)}
        - notifyUrl1: ${gen_realName_notifyUrl($userCode,$innerOrganizationCode,$processId)}
#备注：1.核身没有人工审核 2.四要素发起，没有法定代表人认证 3.除发起企业核身认证4要素校验单接口，其他异常场景和实名一致，不重复写
#116
testcases:
-
    name: 发起企业核身认证4要素校验-企业支付宝一键实名认证-统一社会信用代码-$CaseName
    testcase: testcases/signs/auth/orgAuthPub/fourFactors_alipayOneclickConfirm.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-device-token-authCode-notifyUrl:
#            - ["企业信息都正确(常规企业)-企业支付宝一键实名认证-统一社会信用代码",$organizationName,$organizationNo,$legalName,$legalIdNo,"INDIVIDUAL_CH_IDCARD","PC","xingchenAccessTokenAO009","xingchenAuthcodeZbf009",$notifyUrl1]
            - ["企业信息都正确(常规企业)-企业支付宝一键实名认证-统一社会信用代码-法人是护照",$organizationName,$organizationNo,$legalName,$legalIdNo,"INDIVIDUAL_PASSPORT","PC","xingchenAccessTokenAO009","xingchenAuthcodeZbf009",$notifyUrl1]
