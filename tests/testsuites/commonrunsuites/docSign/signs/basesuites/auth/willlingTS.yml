config:
    variables:
      - bizNo: ${get_business_no()}
      - org001: ${ENV(sign01.main.orgCode)}
      - redirectUrl0: "${ENV(esign.projectHost)}/sign-manage-web/sign-page?mcok"
      - processId0: ${get_signFlowId_status(1,$bizNo, False, True, 0,1,0)}
testcases:
-
      name: 单接口校验
      parameters:
          - name-processId-clientType-redirectUrl-organizeCode-status-message-success:
            - [ "P0TL-正确的参数调用接口意愿申请", "$processId0", 0, "$redirectUrl0",  "$org001",200,"成功",true]
            - [ "P0TL-正确的参数调用接口意愿申请-个人意愿", "${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}", 0, "$redirectUrl0",  "",200,"成功",true]
            - [ "P1TL-processId不传，能正常报错", Null, 0, "$redirectUrl0",  "$org001",1702007,"流程不存在",false]
            - [ "P1TL-processId为空，能正常报错", "", 0, "$redirectUrl0",  "$org001",1702007,"流程不存在",false]
            - [ "P1TL-processId不存在的流程，能正常报错", "ssss", 0, "$redirectUrl0",  "$org001",1702007,"流程不存在",false]
            - [ "P1TL-processId长度超过64字符，能正常报错", "add92fd5d23f084ef754ef7c2c5856aaqqwewqeqsfaefef212", 0, "$redirectUrl0",  "$org001",1702007,"流程不存在",false]
            - [ "P1TL-organizeCode传入空/不传，能正常报错", "$processId0", 0, "$redirectUrl0",  Null,1701999,"您不是流程的签署人",false]
            - [ "P1TL-organizeCode传入不存在的值，能正常报错", "$processId0", 0, "$redirectUrl0",  "adfaewfsd",1701999,"您不是流程的签署人",false]
            - [ "P1TL-clientType非定义的值如-1，当做0处理", "$processId0", -1, "$redirectUrl0",  "$org001",200,"成功",true]
            - [ "P1TL-clientType非定义的非数字，能正常报错", "$processId0", "a", "$redirectUrl0",  "$org001",1703012,"clientType字段类型不匹配",false]
            - [ "P1TL-redirectUrl不传，能正常报错", "$processId0", 0, Null,  "$org001",1702388,"获取短链地址失败",false]
            - [ "P1TL-redirectUrl传入为空，能正常报错", "$processId0", 0, "",  "$org001",1702388,"获取短链地址失败",false]
            - [ "P1TL-redirectUrl传入错误的值，能正常报错", "$processId0", 0, "asdwev",  "$org001",1702388,"获取短链地址失败",false]
            - [ "P1TL-校验正常调用接口之后，出参有正确的applyId值(机构签)", "$processId0", 0, "$redirectUrl0",  "$org001",200,"成功",true]
            - [ "P1TL-校验正常调用接口之后，出参有正确的applyId值（个人签）", "${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}", 0, "$redirectUrl0",  Null,200,"成功",true]
            - [ "P1TL-processId为已经签署完成的流程信息，能正常报错", "${get_signFlow_signing(False, True, False)}", 0, "$redirectUrl0",  "$org001",1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "P1TL-processId为已经拒签完成的流程信息，能正常报错", "${ENV(pdf.reject.processId)}", 0, "$redirectUrl0",  "$org001",1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "P1TL-processId为已经作废完成的流程信息，能正常报错", "${ENV(pdf.revock.processId)}", 0, "$redirectUrl0",  "$org001",1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "P1TL-processId为已经作废中的流程信息，能正常报错", "${ENV(pdf.revocking.processId)}", 0, "$redirectUrl0",  "$org001",1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "P1TL-processId为已经过期的流程信息，能正常报错", "${ENV(pdf.overdue.processId)}", 0, "$redirectUrl0",  "$org001",1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
      testcase: testcases/signs/auth/willingTC.yml


-
      name: 外部用户-willing接口
      variables:
        - userCode1: ${ENV(wsignwb01.userCode)}
        - organizationCode1: ${ENV(worg01.orgCode)}
      parameters:
          - name-processId-clientType-redirectUrl-organizeCode-status-message-success:
            - [ "TC1-processId为已经签署完成的流程信息，能正常报错-外部", "${get_sign_scene(6,0)}", 0, "$redirectUrl0", "$organizationCode1" ,1701999,"您不是流程的签署人",false]
            - [ "TC2-processId为已经拒签完成的流程信息，能正常报错-外部", "${ENV(pdf.reject.processId)}", 0, "$redirectUrl0",  Null,1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "TC3-processId为已经作废完成的流程信息，能正常报错-外部", "${ENV(pdf.revock.processId)}", 0, "$redirectUrl0",  Null,1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "TC4-processId为已经作废中的流程信息，能正常报错-外部", "${ENV(pdf.revocking.processId)}", 0, "$redirectUrl0",  Null,1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
            - [ "TC5-processId为已经过期的流程信息，能正常报错-外部", "${ENV(pdf.overdue.processId)}", 0, "$redirectUrl0",  Null,1702376,"当前流程文件已发生签署，请刷新页面获取最新信息！",false]
      testcase: testcases/signs/auth/willingTC.yml
