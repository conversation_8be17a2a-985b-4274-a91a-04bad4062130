config:
    variables:
      - bizNo: ${get_business_no()}
      - processId01: ${get_signFlowId_status(1,$bizNo, True, False, 0,1,0)}
      - orgProcessId: ${get_signFlowId_status(1,$bizNo, False, True, 0,1,0)}
      - orgCode: ${ENV(sign01.main.orgCode)}
      - userCodeNeedApprove: ${ENV(wsignwb02.approve.userCode)}
      - flowIdApprove: ${get_sign_scene(4,0)}
testcases:
-
      name: 个人签-processId-单接口校验
      parameters:
          - name-processId-status-message-success:
            - [ "P0TL-正确的参数调用接口能够获取结果", $processId01,200,"成功",true]
            - [ "P0TL-processId为空", "", 1702007,"流程不存在",false]
            - [ "P0TL-processId不传", Null, 1702027,"流程id或用户id不能为空",false]
            - [ "P0TL-processId长度超64", "adlfj0932r09joidjoiajf009ewdszmlafj0f02fjdsamfo2e091fds", 1702007,"流程不存在",false]
      testcase: testcases/signs/preloading/preloadingTC.yml
-
      name: 个人签-userCode-接口校验
      parameters:
          - name-processId-userCode-status-message-success:
            - [ "P0TL-userCode为空", $processId01, "",913,"查询参数错误，请检查",false]
            - [ "P0TL-userCode不传", $processId01, Null,1702027,"流程id或用户id不能为空",false]
            - [ "P0TL-userCode长度超64", $processId01, "adjfo2093jfdj02d09fu32skdfnewjf0fdsf202fs",1709134,"流程或用户不存在",false]
      testcase: testcases/signs/preloading/preloadingTC.yml

-
      name: 个人签-preview-接口校验
      parameters:
          - name-processId-preview-status-message-success:
            - [ "P0TL-preview-为字母", $processId01, "a",1703012,"preview字段类型不匹配",false]
            - [ "P0TL-preview-为null", $processId01, Null,200,"成功",true]
            - [ "P0TL-preview-为-1", $processId01, -1,200,"成功",true]
            - [ "P0TL-preview-不存在", $processId01, 5,200,"成功",true]
      testcase: testcases/signs/preloading/preloadingTC.yml

-
      name: 个人签-s-接口校验
      parameters:
          - name-processId-s-status-message-success:
            - [ "P0TL-s-为空", $processId01, "",200,"成功",true]
            - [ "P0TL-s-为null", $processId01, Null,200,"成功",true]
            - [ "P0TL-s-不存在", $processId01, "adf1w",1702544,"解密字符串",false]
      testcase: testcases/signs/preloading/preloadingTC.yml

-
      name: 个人签-tenantCode-接口校验
      parameters:
          - name-processId-tenantCode-status-message-success:
            - [ "P0TL-tenantCode-为字母", $processId01, "a",1703012,"tenantCode字段类型不匹配",false]
            - [ "P0TL-tenantCode-为null", $processId01, Null,200,"成功",true]
            - [ "P0TL-tenantCode-为-1", $processId01, -1,200,"成功",true]
            - [ "P0TL-tenantCode-不存在", $processId01, 5,200,"成功",true]
      testcase: testcases/signs/preloading/preloadingTC.yml

-
      name: 机构签-organizeCode-接口校验
      parameters:
          - name-processId-organizeCode-status-message-success:
            - [ "P0TL-organizeCode-为空", $orgProcessId, "",1702674,"非当前流程参与人",false]
            - [ "P0TL-organizeCode-不传", $orgProcessId, Null,1702674,"非当前流程参与人",false]
            - [ "P0TL-organizeCode-长度超64", $orgProcessId, "adjfo2093jfdj02d09fu32skdfnewjf0fdsf202fs",1702674,"非当前流程参与人",false]
            - [ "P0TL-organizeCode-正确", $orgProcessId, $orgCode,200,"成功",true]
      testcase: testcases/signs/preloading/preloadingTC.yml

-
      name: 场景用例
      parameters:
          - name-processId-status-message-success:
            - [ "P0TL-纯内部签署中", "${get_signFlowId_status(1,$bizNo, True, False, 1,1,0)}", 200,"成功",true]
            - [ "P0TL-纯内部签署完成", "${get_signFlowId_status(2,$bizNo, True, False, 1,1,0)}", 200,"成功",true]
            - [ "P0TL-纯内部拒签", "${ENV(pdf.reject.processId)}", 200,"成功",true]
            - [ "P0TL-纯内部签署失败", "${get_signFlowId_status(7,$bizNo, True, False, 1,1,0)}", 200,"成功",true]
            - [ "P0TL-获取作废协议签署中预加载", "${ENV(pdf.revock.signing.processId)}", 200,"成功",true]
      testcase: testcases/signs/preloading/preloadingTC.yml
-
      name: 场景用例2
      parameters:
          - name-processId-userCode-organizeCode-status-message-success-headers:
            #修改oppositeOrganizationCode为organizationCodeOuterSigner，id一样的，去掉一个
            - [ "P0TL-用印审批中预加载", "$flowIdApprove", $userCodeNeedApprove, "${ENV(worg01.orgCode)}",200,"成功",true,"${get_headers_with_outer_person($userCodeNeedApprove)}" ]
      testcase: testcases/signs/preloading/preloadingTC.yml
-
      name: 获取相对方个人+机构签署preloading
      testcase: testcases/signs/preloading/preloadingTC2.yml
