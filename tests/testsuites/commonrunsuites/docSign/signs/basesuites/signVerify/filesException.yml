config:
    name: 文档验签异常用例集
    variables:
        bizNo: ${get_business_no()}
        accountNumber: ${ENV(sign01.accountNo)}
        passwordEncrypt: ${ENV(passwordEncrypt)}
        token: ${getPortalToken($accountNumber,$passwordEncrypt)}
        # 已完成的静默签流程id
        processId: ${get_signFlowId_status(8,$bizNo,True,False,0,1,0)}
        # 获取签署后的文件filekey
        fileKeyInfo: ${get_process_filekey($processId, $token)}
        fileKey1: ${get_file_key_by_info($fileKeyInfo,1)}
        cryptoFileKey1: ${get_file_key_by_info($fileKeyInfo,2)}
        # 获取指定文件的base64
        file_loacl_path: ${get_constant(file_local_path)}
        fileBase641: ${get_file_base64_add_suffix($file_loacl_path)}

testcases:
-
    name: 文档验签异常用例集
    parameters:
        - name-fileKey-cryptoFileKey-fileFormat-fileBase64-filePassword-projectKey-code-success-message-data:
            - ["TC4-fileFormat传ofd，文档fileId为pdf",$fileKey1,$cryptoFileKey1,"ofd",null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改"]
            - ["TC5-fileFormat传错误的类型（PDF/OFD/ABC），文档fileId为pdf",$fileKey1,$cryptoFileKey1,"ABC",null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改"]
            - ["TC6-fileBase64不传，且fileId也不传",null,null,"ABC",null,null,null,1705016,False,"fileKey与fileBase64不能同时为空",null]
            - ["TC8-fileId和fileBase64都传，Id错误base64正确","123456",$cryptoFileKey1,"pdf",$fileBase641,null,null,1702158,False,"文件fileKey不可为空！",null]
            - [ "TC10-只传fileId，对应的值不正确","123456789",$cryptoFileKey1,null,null,null,null,1702158,False,"文件fileKey不可为空！",null ]
            - [ "TC11-只传fileId，超出位数","123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789123456789",$cryptoFileKey1,null,null,null,null,1703001,False,"文档id过长",null ]
            - [ "TC12-只传fileId，fileId值为空","","",null,null,null,null,1705016,False,"fileKey与fileBase64不能同时为空",null ]
            - [ "TC14-只传fileBase64，fileBase64值为空","","",null,null,null,null,1705016,False,"fileKey与fileBase64不能同时为空",null ]
            - [ "TC34-只传fileBase64，对应的值不正确","","",null,null,"null",null,1705016,False,"fileKey与fileBase64不能同时为空",null ]



    testcase: testcases/signs/signVerify/filesException.yml