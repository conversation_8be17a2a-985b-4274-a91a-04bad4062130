config:
    name: 文档验签用例集
    variables:
        bizNo: ${get_business_no()}
        accountNumber: ${ENV(sign01.accountNo)}
        passwordEncrypt: ${ENV(passwordEncrypt)}
        token: ${getPortalToken($accountNumber,$passwordEncrypt)}
        # 已完成的静默签流程id
        processId: ${get_signFlowId_status(8,$bizNo,True,False,0,1,0)}
        # 获取签署后的文件filekey
        fileKeyInfo: ${get_process_filekey($processId, $token)}
        fileKey1: ${get_file_key_by_info($fileKeyInfo,1)}
        cryptoFileKey1: ${get_file_key_by_info($fileKeyInfo,2)}
        # 获取签署后的文件名称
        file_name: ${download_by_file_key($fileKey1, $cryptoFileKey1, $token)}
        # 构建文件的相对路径
        file_path: ${get_file_path_by_name($file_name)}
        # 获取指定文件的base64
        fileBase641: ${get_file_base64($file_path)}
        # 获取签署后的文件filekey
        updatedFileKeyInfo: ${get_filekey_by_filenamev1(被篡改的文档.pdf, $token)}
        updatedFileKey1: ${get_file_key_by_info($updatedFileKeyInfo,1)}
        updatedCryptoFileKey1: ${get_file_key_by_info($updatedFileKeyInfo,2)}
testcases:
-
    name: 文档验签用例集
    parameters:
        - name-fileKey-cryptoFileKey-fileFormat-fileBase64-filePassword-projectKey-code-success-message-validateResult-filName:
            - ["TC1-fileFormat数据不传，对应文档类型为PDF",$fileKey1,$cryptoFileKey1,null,null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC2-fileFormat传空或者null，对应文档类型为PDF",$fileKey1,$cryptoFileKey1,null,null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC3-fileFormat传pdf，文档fileId为pdf",$fileKey1,$cryptoFileKey1,"pdf",null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC7-fileId和fileBase64都传，Id正确base64错误",$fileKey1,$cryptoFileKey1,"pdf","null",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC9-只传fileId，且值正确",$fileKey1,$cryptoFileKey1,null,null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC13-只传fileBase64，且值正确",null,null,null,$fileBase641,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",$file_name]
            - ["TC15-不需要签署密码的文件，不传filePassword",$fileKey1,$cryptoFileKey1,"pdf",null,null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null]
            - ["TC16-不需要签署密码的文件，传任意filePassword",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC21-验签单文档-单个章-个人模板章-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC22-验签单文档-单个章-个人手绘章-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC23-验签单文档-单个章-带签署日期-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC24-验签单文档-单个章-机构章-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC25-验签单文档-单个章-法人章-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC26-验签单文档-单个章-机构骑缝章多页-成功",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC27-验签单文档-没有章",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC28-验签单文档-多个章（多页签同一个章）",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC29-验签单文档-多个章（非多页签）集合-都成功的",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC30-验签单文档-文档包含1000个签名的情况",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC31-验签单文档-签名位置重叠验证",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC32-验签单文档-作废签署的验签",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC33-验签单文档-大文件的100M以上验签200个章",$fileKey1,$cryptoFileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
            - ["TC34-验签单文档-被篡改后的文档验签",$updatedFileKey1,$updatedCryptoFileKey1,"pdf","",null,null,200,True,"成功","签名无效：自签名以来，签名已被修改",null]


            # 以下用例，本期签署均不支持
#            - ["TC17-需要密码的文件，不传filePassword",$fileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
#            - ["TC18-需要密码的文件，传入错误的filePassword",$fileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
#            - ["TC19-需要密码的文件，传入正确的filePassword",$fileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]
#            - ["TC20-传的filePassword超出最大位数",$fileKey1,"pdf","123456",null,null,200,True,"成功","签名有效：自签名以来，内容未被修改",null ]


    testcase: testcases/signs/signVerify/files.yml