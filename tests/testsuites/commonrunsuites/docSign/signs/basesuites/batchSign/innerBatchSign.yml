config:
    name: 业务平台批量签署

testcases:
-
  name: SETUP-多通道-关闭
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","0" ]
  testcase: common/valuesCmc.yml

-
  name: 批量签署-PDF流程-校验流程签署的状态
  testcase: testcases/signs/portal/task/batchSignTC1.yml
-
  name: 批量签署-PDF流程-批量签署不支持强制阅读到底流程
  testcase: testcases/signs/portal/task/batchSignTC2.yml
-
  name: 批量签署-PDF流程-批量签署不支持只有自由签署区
  testcase: testcases/signs/portal/task/batchSignTC3.yml
-
  name: 批量签署-PDF流程-批量签署不支持强制手绘流程
  testcase: testcases/signs/portal/task/batchSignTC4.yml
-
  name: 批量签署-PDF流程-批量签署勾选的流程非当前登录用户
  testcase: testcases/signs/portal/task/batchSignTC5.yml

-
  name: SETUP-多通道-关闭
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","0" ]
  testcase: common/valuesCmc.yml

-
  name: 批量签署-PDF流程-批量签署不支持强制阅读时间
  testcase: testcases/signs/portal/task/batchSignTC6.yml
-
  name: 批量签署-PDF流程-指定企业签署区签署成功
  testcase: testcases/signs/portal/task/batchSignTC7.yml
-
  name: 批量签署-PDF流程-批量签署不支持未实名企业和已删除企业
  testcase: testcases/signs/portal/task/batchSignTC8.yml
-
  name: 批量签署-PDF流程-批量签署不支持指定多个印章类型（除了法人章）
  testcase: testcases/signs/portal/task/batchSignTC9.yml
-
  name: 批量签署-PDF流程-只指定法人签署区签署
  testcase: testcases/signs/portal/task/batchSignTC11-legal.yml

-
  name: SETUP-多通道-关闭
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","0" ]
  testcase: common/valuesCmc.yml

-
  name: 批量签署-PDF流程-用非默认印章签署(下一步签署)
  testcase: testcases/signs/portal/task/batchSignTC12.yml
-
  name: 批量签署-PDF流程-默认签署不支持仅UKey签署和备注签署
  testcase: testcases/signs/portal/task/batchSignTC13.yml
-
  name: 批量签署不支持新建用户（无RSA证书）
  testcase: testcases/signs/portal/task/batchSignTC17.yml
-
  name: 批量签署-签署方无证书无印章（sign04）
  testcase: testcases/signs/portal/task/batchSignTC18.yml
