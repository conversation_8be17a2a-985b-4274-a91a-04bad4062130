config:
  name: 回调
  variables:
    - processId0: "${ENV(pdf.signed.processId)}"
    - userCode0: "${ENV(sign01.userCode)}"
    - organizeCode0: "${ENV(sign01.main.orgCode)}"
    - fileKey0: "$${ENV(fileKey)}"

testcases:
  - name: 公有云用印审批回调
    parameters:
      - name-approvalFlowId-approverName-flowId-refusedReason-sealId-sealName-sealUrl-sealHeight-result-status-msg:
          - [ "TC1-回调通知","","审批回调流程","","审批拒绝原因","","sealName","",100,1,200,"成功" ]
          - [ "TC2-审批流程不存在","${generate_random_str(101)}","审批回调流程","","审批拒绝原因","","sealName","",100,1,200,"成功" ]
          - [ "TC3-拒绝原因过长","","审批回调流程","","${generate_random_str(101)}","","sealName","",100,2,200,"成功" ]
          - [ "TC4-flowId不存在","","审批回调流程","${generate_random_str(65)}","审批拒绝原因","","sealName","",100,1,200,"成功" ]
          - [ "TC5-印章名称过长","","审批回调流程","","审批拒绝原因","","${generate_random_str(101)}","",100,1,200,"成功" ]
          - [ "TC6-审批拒绝","","审批回调流程","","审批拒绝原因","","sealName","",100,2,200,"成功" ]
          - [ "TC7-印章宽度不是数字","","审批回调流程","","审批拒绝原因","","sealName","","中文",1,1703012,"sealHeight字段类型不匹配" ]
          - [ "TC8-印章宽度小数","","审批回调流程","","审批拒绝原因","","sealName","","50.45",1,1703012,"sealHeight字段类型不匹配" ]
    testcase: testcases/signs/callback/approvalTC.yml

  - name: 授权回调
    parameters:
      - name-agentAccountId-agentFlowId-processId-applyId-authFlowId-email-mobile-organizeCode-userCode-type-status-msg:
          - [ "TC1-授权回调-接口定义不规范，post请求竟然需要param参数，保留一个报错，其他注释掉",$userCode0,$processId0,$processId0,null,"","<EMAIL>","***********",$organizeCode0,$userCode0,"PERSON",1703023,"参数processId必填!" ]
#          - [ "TC2-processId不存在",$userCode0,$processId0,"xxxxxx",null,"","<EMAIL>","***********",$organizeCode0,$userCode0,"PERSON",1701005,"Required String parameter 'processId' is not present" ]
#          - [ "TC3-邮箱格式错误",$userCode0,$processId0,$processId0,null,"","xxxxtsign.cn","***********",$organizeCode0,$userCode0,"PERSON",1701005,"Required String parameter 'processId' is not present" ]
#          - [ "TC4-手机格式错误",$userCode0,$processId0,$processId0,null,"","<EMAIL>","***************",$organizeCode0,$userCode0,"PERSON",1701005,"Required String parameter 'processId' is not present" ]
#          - [ "TC5-机构类型回调",$userCode0,$processId0,null,null,"","<EMAIL>","***********",$organizeCode0,$userCode0,"ORGANIZE",1701005,"Required String parameter 'processId' is not present" ]
    testcase: testcases/signs/callback/authV3TC.yml

  - name: 大文件切割回调
    parameters:
      - name-processDocId-fileKey-status-msg:
          - [ "TC1-fileKey为空","1111XXXX","",200,"成功" ]
          - [ "TC2-processDocId为空",null,"$fileKey0",200,"成功" ]
          - [ "TC3-processDocId不存在","1111XXXXX","$fileKey0",200,"成功" ]
          - [ "TC4-fileKey不存在",$processId0,"xxxxxxx",200,"成功" ]
    testcase: testcases/signs/callback/splitLargeFileTC.yml