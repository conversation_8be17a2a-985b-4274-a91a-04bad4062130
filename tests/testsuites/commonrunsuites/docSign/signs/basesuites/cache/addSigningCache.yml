config:
    name: 添加提交签署时缓存用例集
    variables:
        cacheValuec: ${build_process_cache()}
        # processIdc和processActorIdc无实际意义，接口不会校验，直接写死
        processIdc: ${get_constant(cacheProcessId)}
        processActorIdc: ${get_constant(cacheProcessActorId)}
testcases:
-
    # 接口不做校验，仅仅是做一次redis存储，获取的时候也是原样返回字符串数据，造真正的流程数据无实际意义，故这里采用mock的流程数据
    name: 添加提交签署时缓存用例集
    parameters:
        - name-cacheValue-processId-processActorId-code-success-message:
            - ["TC1-cacheValue为空或null","",$processIdc,$processActorIdc,1703001,false,"缓存值不可为空"]
            - ["TC2-processActorId为空或者null",$cacheValuec,$processIdc,"",1702001,false,"签署人信息不存在"]
            - ["TC3-processId为空或者null",$cacheValuec,"",$processActorIdc,1702001,false,"签署人信息不存在"]
#            - ["TC4-cacheValue&processActorId&processId不为空且值正确",$cacheValuec,$processIdc,$processActorIdc,200,true,"成功"]
#            - ["TC5-内部个人提交签署添加签署的缓存",$cacheValuec,$processIdc,$processActorIdc,200,true,"成功"]
#            - ["TC6-内部机构提交签署添加签署的缓存",$cacheValuec,$processIdc,$processActorIdc,200,true,"成功" ]
#            - ["TC7-相对方个人提交签署添加签署的缓存",$cacheValuec,$processIdc,$processActorIdc,200,true,"成功" ]
#            - ["TC8-相对方机构提交签署添加签署的缓存",$cacheValuec,$processIdc,$processActorIdc,200,true,"成功" ]
            - ["TC8-相对方机构提交签署添加签署的缓存",$cacheValuec,$processIdc,$processActorIdc,1624088,false,"流程签署人不存在" ]
    testcase: testcases/signs/cache/addSigningCache.yml


