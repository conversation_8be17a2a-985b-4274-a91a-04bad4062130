config:
    name: 校验AI手绘用例集
    variables:
      bizNo: ${get_business_no()}
      singleWord1: ${get_constant(singleWord1)}
      singleWord2: ${get_constant(singleWord2)}
      filePath: ${get_constant(word_file_path1)}
      base64: ${get_file_base64_add_suffix($filePath)}

testcases:
-
    name: 校验AI手绘用例集
    parameters:
        - name-wordIndex-singleWord-wordBase64-code-success-message-isMatch:
            - ["TC1-入参输入正确",0,$singleWord1,$base64,200,True,"成功",True]
            - ["TC2-singleWord和wordBase64不匹配",0,$singleWord2,$base64,200,True,"成功",False]
    testcase: testcases/signs/AIFreehand/check.yml
