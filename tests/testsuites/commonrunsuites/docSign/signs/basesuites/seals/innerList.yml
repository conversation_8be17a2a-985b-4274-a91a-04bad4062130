config:
  name: 获取签署页可用印章列表用例集-内部
  variables:
    - accountNumber: ${ENV(sign01.accountNo)}
    - passwordEncrypt: ${ENV(passwordEncrypt)}
    - token1: ${get_headers_with_accountCode($accountNumber)}
testcases:
  - name: 获取签署页可用印章列表接口用例集-内部
    parameters:
      - name-processId-organizeCode-code-success-message-token:
          - [ "TC1-processId未传或为空","","",1702007,false,"流程不存在","$token1"]
          - [ "TC2-签署主体为机构，organizeCode未传或为空","${get_sign_flow_signing_v3(False, False, False, 2, 0)}","",1702028,false,"该签署人不存在","$token1" ]
          - [ "TC3-签署主体为个人，organizeCode未传或为空，processId值正确","${get_sign_flow_signing_v3(falFalsese, False, False, 1, 0)}","",200,true,"成功","$token1" ]
          - [ "TC4-签署主体为机构，organizeCode及processId值正确","${get_sign_flow_signing_v3(False, False, False, 2, 0)}","${ENV(sign01.main.orgCode)}",200,true,"成功","$token1" ]
          - [ "TC5-签署主体为个人，用户有多个个人印章","${get_sign_flow_signing_v3(falFalsese, False, False, 1, 0)}","",200,true,"成功","$token1" ]
          - [ "TC6-签署主体为机构，用户有多个个人印章、企业授权章及法人章","${get_sign_flow_signing_v3(False, False, False, 2, 0)}","${ENV(sign01.main.orgCode)}",200,true,"成功","$token1" ]
          - [ "TC10-内部个人单节点单文档","${get_sign_flow_signing_v3(False, False, False, 1, 0)}","",200,true,"成功","$token1" ]
          - [ "TC11-内部个人多文档指定印章类型模板","${get_sign_flow_signing_v3(True, False, False, 1, 0)}","",200,true,"成功","$token1" ]
    testcase: testcases/signs/seals/list.yml


