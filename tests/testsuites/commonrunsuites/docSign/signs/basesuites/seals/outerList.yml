config:
  name: 获取签署页可用印章列表用例集-外部
  variables:
    - outerUserCode: ${ENV(wsignwb01.userCode)}
    - outerOrgCode: ${ENV(worg01.orgCode)}
    - token1: ${get_headers_with_outer_person($outerUserCode)}

testcases:
  - name: 获取签署页可用印章列表接口用例集-外部
    parameters:
      - name-processId-organizeCode-code-success-message-token:
          - [ "TC7-签署主体为相对方机构，用印类型为机构印章","${get_sign_flow_signing_v3(False, False, True, 2, 0)}","$outerOrgCode",200,true,"成功","$token1" ]
          - [ "TC8-签署主体为相对方机构，用印类型为法人章","${get_sign_flow_signing_v3(False, False, True, 2, 0)}","$outerOrgCode",200,true,"成功","$token1" ]
          - [ "TC9-签署主体为相对方机构，用印类型为经办人章","${get_sign_flow_signing_v3(False, False, True, 2, 0)}","$outerOrgCode",200,true,"成功","$token1" ]
          - [ "TC12-相对方机构单节点法人签署+机构签署","${get_sign_flow_signing_v3(False, False, True, 2, 0)}","$outerOrgCode",200,true,"成功","$token1" ]
    testcase: testcases/signs/seals/list.yml
