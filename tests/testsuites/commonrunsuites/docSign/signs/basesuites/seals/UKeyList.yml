config:
  name: 获取签署页可用印章列表用例集-UKey印章
  variables:
    - accountNumber: ${ENV(sign01.accountNo)}
    - accountNumber2: ${ENV(sign03.accountNo)}
    - passwordEncrypt: ${ENV(passwordEncrypt)}
    - token1: ${getPortalToken($accountNumber, $passwordEncrypt)}
    - token2: ${getPortalToken($accountNumber2, $passwordEncrypt)}
    - flowId01: ${get_sign_flow_signing_v3(False, False, False, 1, 0)} #内部个人签署
    - flowId04: ${get_sign_scene(10,0)} #仅UKey签署

testcases:
  - name: 获取签署页可用印章列表接口用例集-内部
    parameters:
      - name-processId-organizeCode-processIds-redirectUrl-signSns-code-success-message-token:
          - [ "TC1-processId未传或为空","","",[],"",[],1702007,false,"流程不存在","$token1"]
          - [ "TC2-签署主体为个人，organizeCode未传或为空，processId值正确","$flowId01","",[],"",[],200,true,"成功","$token1" ]
          - [ "TC3-登录人非当前流程参与人","$flowId01","",[],"",[],1702028,false,"该签署人不存在","$token2" ]
          - [ "TC4-签署主体为机构，organizeCode及processId值正确","$flowId04","${ENV(sign01.main.orgCode)}",[],"",[],200,true,"成功","$token1" ]
          - [ "TC5-仅UKey签署流程获取印章",$flowId04,"",[$flowId01,$flowId04],"",[],200,true,"成功","$token1" ]
          - [ "TC6-redirectUrl=1000长度",$flowId01,"",[],"${generate_random_str(1000)}",[],200,true,"成功","$token1" ]
          - [ "TC7-signSns=1000长度",$flowId01,"",[],"",["${generate_random_str(1000)}"],200,true,"成功","$token1" ]
    testcase: testcases/signs/seals/UKeyList.yml


