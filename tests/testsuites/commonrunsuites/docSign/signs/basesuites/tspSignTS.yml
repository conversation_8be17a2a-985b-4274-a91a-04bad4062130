config:
    name: 签署多通道
    variables:
      - tspId01: "LOCAL_DEFAULT_TSP"
      - tspId02: "ESIGN_WITNESS_TSP"

testcases:
-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-一步or分步发起验证多通道签署
  testcase: testcases/signs/TSP/TSPSceneTC.yml

-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-一步or分步发起OFD流程验证多通道签署
  testcase: testcases/signs/TSP/TSPSceneTC2-OFD.yml

-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-业务模板发起多通道流程验证
  testcase: testcases/signs/TSP/TSPSceneTC3.yml

-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: webapi-页面发起多通道流程
  testcase: testcases/signs/TSP/TSPSceneTC4.yml

-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: webapi-页面发起多通道流程带有流程审批
  testcase: testcases/signs/TSP/TSPSceneTC5.yml

-
  name: SETUP-多通道-开启（要求安装了通道应用）
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","1" ]
  testcase: common/valuesCmc.yml

-
  name: 多通道非正向签署流程-不同状态的数据查询
  testcase: testcases/signs/TSP/TSPSceneTC6.yml

-
  name: SETUP-多通道-关闭
  parameters:
    - keyIdCode1-value1:
        - [ "system_config_tsp","0" ]
  testcase: common/valuesCmc.yml

-
  name: openapi-通道关闭下传入tspId忽略处理
  testcase: testcases/signs/TSP/TSPSceneTC7.yml