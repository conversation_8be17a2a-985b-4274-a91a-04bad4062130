config:
    name: 获取数据签签署区信息
    variables:
      sp: " "
      bisNo: ${get_business_no()}

testcases:
-
    name: 获取数据签签署区信息-校验-新增参数
    parameters:
    - name-businessNo-signTaskId-code-message:
#        - ["TC0-正常调用",123,"9b1a1598cb438acbb1c06a0954cf0aec",200,"成功"]
        - ["TC1-businessNo为空字符串/不传","","",1703017,"业务编码和签署任务id不能同时为空"]
        - ["TC2-businessNo包含首尾空格",$sp 123 $sp,"",200,"成功"]
        - ["TC3-businessNo包含不支持的特殊字符","/:*? \" <>|","",1703001,"第三方签署业务id不能包含特殊字符"]
        - ["TC4-businessNo长度为51字符","文本长度为51字符文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文","",1703001,"第三方签署业务id不可超出50字符！"]
        - ["TC5-signTaskId不存在","","signTaskId不存在",1702469,"签署任务不存在"]
        - ["TC6-signTaskId为空字符串/不传","","",1703017,"业务编码和签署任务id不能同时为空"]
#        - ["TC7-signTaskId包含首尾空格","",$sp 9b1a1598cb438acbb1c06a0954cf0aec $sp,200,"成功"]
        - ["TC8-signTaskId包含不支持的特殊字符","","/:*? \" <>|",1703001,"签署任务id不能包含特殊字符"]
        - ["TC9-signTaskId为36字符","","文本长度为51字符文本长度超过50文本长度超过50文本长度超过50文本长度超过501",1703001,"签署任务id不可超出36字符！"]
#        - ["TC10-signTaskId和businessNo都填，且对应","biz-859687","9b1a1598cb438acbb1c06a0954cf0aec  ",200,"成功"]
#        - ["TC11-signTaskId和businessNo都填，不为同一条任务，以signTaskId为准返回对应信息",123,"9b1a1598cb438acbb1c06a0954cf0aec",200,"成功"]


    testcase: testcases/signs/signTask/getHtmlSignature.yml


-
         name: 获取数据签签署区信息-场景用例
         testcase: testcases/signs/signTask/getHtmlSignature1.yml