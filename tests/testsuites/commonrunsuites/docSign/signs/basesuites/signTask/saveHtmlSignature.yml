config:
    name: 分步发起添加文档add接口-校验-signFiles
    variables:
      sp: " "
        # 4种签署方
      bisNo: ${get_business_no()}
      userCode0: ${ENV(sign01.userCode)}
      oppositeUserCode0: ${ENV(wsignwb01.userCode)}
      orgCode0: ${ENV(sign01.main.orgCode)}
      oppositeOrgCode0: ${ENV(worg01.orgCode)}
        #用户无个人证书
      userCode1: ${ENV(userCodeNoCert)}
#     离职
      leaveUserCode: getUserInfoByDB(2,'user_code', 1)
#      已删除用户
      deleteUserCode: getUserInfoByDB(6,'user_code', 1)
      deleteOrg: getOrgInfoByDB(6,'organization_id', 1, 1)
#      已注销用户
      cancelUserCode: getUserInfoByDB(4,'user_code', 1)
      cancelOrg: getOrgInfoByDB(2,'organization_id', 1, 1)

#      sealId0: ${ENV(sign01.sealId)}
#      sealId2: ${ENV(org01.legal.sealId)}
      filePath: ${get_constant(word_file_path1)}
      base64: ${get_file_base64($filePath)}
      sealId1: ${ENV(orgSealId2)}
      data500: "文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能能文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能能"


#      userSealIdStop: ${ENV(userSealStop)}
#      userSealIdDelete: ${ENV(userSealDelete)}
#      userSealIdNotPublic: ${ENV(userSealNoPublic)}

testcases:
-
    name: 保存签署区-校验-入参
    parameters:
    - name-businessNo-datas-userCode-organizationCode-code-message:
        - ["TC0-正常调用",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0,$orgCode0,200,"成功"]
#        - ["TC1-data为空或不传",$bisNo,"",$userCode0,$orgCode0,1703001,"文本内容不能为空"]
        - ["TC2-data传文本格式内容，其它参数正常",123,[{"signData": "文本内容"},{"signData": "文本内容"}],$userCode0,$orgCode0,200,"成功"]
        - ["TC3-data传参为json字符串格式，其它参数正常，其它参数正常",$bisNo,[ {"signData": "222"}], $userCode0, $orgCode0,200,"成功"]
#        base64长度有问题,校验长度有问题
        - ["TC4-data传参为base64格式，其它参数正常",$bisNo,[{"signData":$base64},{"signData": "222"}], $userCode0, $orgCode0,200,"成功"]
        - ["TC5-data入参文本首尾包含空格，其它参数正常，其它参数正常",$bisNo,[{"signData": $sp "文本内容" $sp},{"signData": "222"}],$userCode0,$orgCode0,200,"成功"]
#        datas长度已经放开，类型为text
#        - ["TC6-data内容长度超过500，",$bisNo,[{"signData": $data500},{"signData": "222"}],$userCode0,$orgCode0,1703001,"文本内容长度不能超过500"]
        - ["TC7-businessNo业务编号重复，其它参数正常",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0, $orgCode0,200,"成功"]
        - ["TC8-businessNo业务编号为空字符串/不传","",[{"signData": "文本内容"},{"signData": "222"}],$userCode0, $orgCode0,1703001,"第三方签署业务id不能为空"]
        - ["TC9-businessNo业务编号包含首尾空格",$sp $bisNo $sp,[{"signData": "文本内容"},{"signData": "222"}], $userCode0, $orgCode0,200,"成功"]
        - ["TC10-businessNo业务编号包含不支持的特殊字符","/:*?\"<>|",[{"signData": "文本内容"},{"signData": "222"}],$userCode0, $orgCode0,1703001,"第三方签署业务id不能包含特殊字符"]
        - ["TC11-businessNo业务编号长度为51字符","文本长度为51字符文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文",[{"signData": "文本内容"},{"signData": "222"}],$userCode0, $orgCode0,1703001,"第三方签署业务id不可超出50字符！"]
        - ["TC37-签署人userCode为空/不传",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],"",$orgCode0,1703001,"用户编码不能为空! "]
#        65字符长度有问题
        - ["TC38-签署人userCode长度为65字符",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],"iVBORw0KGgoAAAANSUhEUgAAAVkAAAGACAYAAAAHwxE8AAAAAXNSR0IArs4c6QAA",$orgCode0,1111003,"用户已不存在"]
        - ["TC39-指定内部个人userCode包含首尾空格",$bisNo,[{"signData": "文本内容"},{"signData": "222"}], $sp$userCode0$sp, $orgCode0,200,"成功"]
        - ["TC40-指定内部个人",$bisNo,[{"signData": "文本内容"},{"signData": "222"}], $userCode0, $orgCode0,200,"成功"]
        - ["TC41-指定内部个人，用户已离职",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$leaveUserCode,$orgCode0,1111003,"用户已不存在"]
        - ["TC42-指定内部个人，用户已注销",$bisNo,[{"signData": "文本内容"},{"signData": "222"}], $cancelUserCode, $orgCode0,1111003,"用户已不存在"]
        - ["TC43-指定内部个人，用户不存在",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0 123, $orgCode0,1111003,"用户已不存在"]
        - ["TC44-usercode包含特殊字符",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],"/:*?\"<>|",$orgCode0,1703001,"用户编码不能包含特殊字符"]
        - ["TC45-指定内部机构",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0,$orgCode0,200,"成功"]
        - ["TC46-机构包含特殊字符",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0,"/:*?\"<>|",1703001,"企业编码不能包含特殊字符"]
        - ["TC47-签署人organizationCode长度为65字符",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0,"iVBORw0KGgoAAAANSUhEUgAAAVkAAAGACAYAAAAHwxE8AAAAAXNSR0IArs4c6QAA",1702046,"未知的组织信息！"]
        - ["TC48-指定内部机构organizationCode包含首尾空格",$bisNo,[{"signData": "文本内容"},{"signData": "222"}], $userCode0, $sp $orgCode0 $sp,200,"成功"]
        - ["TC49-指定内部结构，机构已删除",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$userCode0,$deleteOrg,1702046,"未知的组织信息！"]
#        - ["TC50-指定内部结构，用印员已删除",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,1703001,"印章id不存在"]
#        - ["TC52-指定内部结构，签署人非用印员，其它参数正常",$bisNo,[{"signData": "文本内容"},{"signData": "222"}], $userCode0, $orgCode0,17020477,"印章不存在或者无使用权限"]
        - ["TC53-指定相对方个人",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$oppositeUserCode0,$oppositeOrgCode0,1702077,"账号类型与传入的不一致！"]
        - ["TC54-指定相对方机构",$bisNo,[{"signData": "文本内容"},{"signData": "222"}],$oppositeUserCode0,$oppositeOrgCode0,1702077,"账号类型与传入的不一致！"]



#        - ["TC1-data为空或不传",$bisNo,"",$userCode0,$orgCode0,"",$sealId0,"","",1,1703001,"文本内容不能为空"]
#        - ["TC2-data传文本格式内容，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC3-data传参为json字符串格式，其它参数正常，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC4-data传参为base64格式，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC5-data入参文本首尾包含空格，其它参数正常，其它参数正常",$bisNo,$sp"签署文本内容"$sp, $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC6-businessNo业务编号重复，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC7-businessNo业务编号为空字符串/不传",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC8-businessNo业务编号包含首尾空格",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC9-businessNo业务编号包含不支持的特殊字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#        - ["TC10-businessNo业务编号长度为51字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]

    testcase: testcases/signs/signTask/saveHtmlSignature.yml


#config:
#    name: 分步发起添加文档add接口-校验-signFiles
#    variables:
#      sp: " "
#        # 4种签署方
#      bisNo: ${get_business_no()}
#      userCode0: ${ENV(sign01.userCode)}
#      oppositeUserCode0: ${ENV(wsignwb01.userCode)}
#      orgCode0: ${ENV(sign01.main.orgCode)}
#      oppositeOrgCode0: ${ENV(worg01.orgCode)}
#        #用户无个人证书
#      userCode1: ${ENV(userCodeNoCert)}
##     离职
#      leaveUserCode: getUserInfoByDB(2,'user_code', 1)
##      已删除用户
#      deleteUserCode: getUserInfoByDB(6,'user_code', 1)
#      deleteOrg: getOrgInfoByDB(6,'organization_id', 1, 1)
##      已注销用户
#      cancelUserCode: getUserInfoByDB(4,'user_code', 1)
#      cancelOrg: getOrgInfoByDB(2,'organization_id', 1, 1)
#
#      sealId0: ${ENV(sign01.sealId)}
#      sealId2: ${ENV(org01.legal.sealId)}
#      filePath: ${get_constant(word_file_path1)}
#      base64: ${get_file_base64($filePath)}
#      sealId1: ${ENV(orgSealId2)}
#      data500: "文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能能文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能为文本内容不能能"
#
#
#      userSealIdStop: ${ENV(userSealStop)}
#      userSealIdDelete: ${ENV(userSealDelete)}
#      userSealIdNotPublic: ${ENV(userSealNoPublic)}
#
#testcases:
#-
#    name: 保存签署区-校验-入参
#    parameters:
#    - name-businessNo-datas-userCode-organizationCode-signatureType-sealId-posX-posY-pageNo-code-message:
#        - ["TC0-正常调用",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,"","",1,200,"成功"]
#        - ["TC1-data为空或不传",$bisNo,"",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"文本内容不能为空"]
#        - ["TC2-data传文本格式内容，其它参数正常",123,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC3-data传参为json字符串格式，其它参数正常，其它参数正常",$bisNo,{"dade":4566}, $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
##        base64长度有问题,校验长度有问题
#        - ["TC4-data传参为base64格式，其它参数正常",$bisNo,$base64, $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC5-data入参文本首尾包含空格，其它参数正常，其它参数正常",$bisNo,$sp "签署文本内容" $sp,$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC6-data内容长度超过500，",$bisNo,$data500,$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"文本内容长度不能超过500"]
#        - ["TC7-businessNo业务编号重复，其它参数正常",$bisNo,"签署文本内容",$userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC8-businessNo业务编号为空字符串/不传","","签署文本内容",$userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"第三方签署业务id不能为空"]
#        - ["TC9-businessNo业务编号包含首尾空格",$sp $bisNo $sp,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC10-businessNo业务编号包含不支持的特殊字符","/:*?\"<>|","签署文本内容",$userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"第三方签署业务id不能包含特殊字符"]
#        - ["TC11-businessNo业务编号长度为51字符","文本长度为51字符文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文本长度超过50文","签署文本内容",$userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"第三方签署业务id不可超出50字符！"]
#        - ["TC12-sealId为空或不传",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL","",0,0,1,1703001,"印章id不能为空"]
#        - ["TC13-sealId不存在，其它参数正常",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC14-sealId已吊销，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$userSealIdStop,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC15-sealId为未发布状态",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$userSealIdNotPublic,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC16-sealId与签署人类型不匹配",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC17-ssealId为未授权印章",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC18-sealId入参包含首尾空格，其它参数正常",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sp$sealId0$sp,0,0,1,200,"成功"]
#        - ["TC19-sealId与signatureType不匹配",$bisNo,"签署文本内容", $userCode0, $orgCode0,"COMMON-SEAL",$sealId0,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC20--signatureType为空，默认签署人为内部个人，其它入参正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"",$sealId0,0,0,1,200,"成功"]
#        - ["TC21--signatureType传PERSON-SEAL，签署人为内部个人，其它入参正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC22-signatureType传COMMON-SEAL:签署主体为内部机构其它入参正确",$bisNo,"签署文本内容", $userCode0, $orgCode0,"COMMON-SEAL",$sealId1,0,0,1,200,"成功"]
#        - ["TC23-signatureType传LEGAL-PERSON-SEAL签署主体为机构其它入参正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"LEGAL-PERSON-SEAL",$sealId2,0,0,1,200,"成功"]
#        - ["TC24-signType类型不正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"SEAL",$sealId0,0,0,1,1702369,"印章类型不存在"]
#        - ["TC25-signatureType传LEGAL-PERSON-SEAL:签署主体为个人其它入参正确",$bisNo,"签署文本内容", $userCode0, $orgCode0,"LEGAL-PERSON-SEAL",$sealId0,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC26-signatureType传COMMON-SEAL:签署主体为个人其它入参正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"COMMON-SEAL",$sealId0,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC27-signatureType传PERSON-SEAL:签署主体为机构其它入参正确",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId2,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC28-signatureType传不支持的字符类型",$bisNo,"签署文本内容", $userCode0, $orgCode0,"/:*?\"<>|",$sealId0,0,0,1,1703001,"signatureType包含不支持的特殊字符"]
#        - ["TC29-signatureType包含首位空格",$bisNo,"签署文本内容",$userCode0,$orgCode0,$sp PERSON-SEAL $sp,$sealId0,0,0,1,200,"成功"]
#        - ["TC30-PosX输入传入不支持的特殊字符",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,"/:*?\"<>|",0,1,1703012,"包含不支持的特殊字符"]
#        - ["TC31-PosX设置为非数字字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,"abc",0,1,1703012,"posX字段类型不匹配"]
#        - ["TC32-PosX设置为空",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,"",0,1,200,"成功"]
#        - ["TC33-PosX设置为大于99999整数",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,999999,0,1,1703001,"最大限制"]
#        - ["TC34-PosX设置为0",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
##        x坐标不能小于0 code重复
#        - ["TC35-PosX设置为负数",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,-1000,0,1,1703001,"x坐标不能小于0"]
#        - ["TC36-PosX设置为小数",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0.48,0,1,200,"成功"]
#        - ["TC37-PosX设置为99999",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,99999,0,1,200,"成功"]
#        - ["TC38-PosY输入传入不支持的特殊字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,"/:*?\"<>|",1,1703012,"失败"]
#        - ["TC39-PosY设置为非数字字符",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,"abc",1,1703012,"posY字段类型不匹配"]
#        - ["TC40-PosY设置为大于99999整数",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,999999,1,1703001,"超过最大限制"]
#        - ["TC41-PosY设置为空",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,"",1,200,"成功" ]
#        - ["TC42-PosY设置为0",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC43-PosY设置为负数",$bisNo,"签署文本内容", $userCode0, $orgCode0,"COMMON-SEAL",$sealId1,0,-100,1,1703001,"y坐标不能小于0"]
#        - ["TC36-PosY设置为小数",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,1.49,1,200,"成功" ]
#        - ["TC35-PosY设置为99999",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0,0,99999,1,200,"成功"]
#        - ["TC37-签署人userCode为空/不传",$bisNo,"签署文本内容","",$orgCode0,"PERSON-SEAL","",0,0,1,1703001,"用户编码不能为空!"]
##        65字符长度有问题
#        - ["TC38-签署人userCode长度为65字符",$bisNo,"签署文本内容","iVBORw0KGgoAAAANSUhEUgAAAVkAAAGACAYAAAAHwxE8AAAAAXNSR0IArs4c6QAA",$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,1703001,"超过最大限制"]
#        - ["TC39-指定内部个人userCode包含首尾空格",$bisNo,"签署文本内容", $sp$userCode0$sp, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC40-指定内部个人",$bisNo,"签署文本内容", $userCode0, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC41-指定内部个人，用户已离职",$bisNo,"签署文本内容",$leaveUserCode,$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,1111003,"用户已不存在"]
#        - ["TC42-指定内部个人，用户已注销",$bisNo,"签署文本内容", $cancelUserCode, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1111003,"用户已不存在"]
#        - ["TC43-指定内部个人，用户不存在",$bisNo,"签署文本内容",$userCode0 123, $orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1111003,"用户已不存在"]
#        - ["TC44-usercode包含特殊字符",$bisNo,"签署文本内容","/:*?\"<>|",$orgCode0,"PERSON-SEAL",$sealId0,0,0,1,1703001,"用户编码不能包含特殊字符"]
#        - ["TC45-指定内部机构",$bisNo,"签署文本内容",$userCode0,$orgCode0,"COMMON-SEAL",$sealId1,0,0,1,200,"成功"]
#        - ["TC46-机构包含特殊字符",$bisNo,"签署文本内容",$userCode0,"/:*?\"<>|",$sealId1,"PERSON-SEAL",0,0,1,1703001,"企业编码不能包含特殊字符"]
#        - ["TC47-签署人organizationCode长度为65字符",$bisNo,"签署文本内容",$userCode0,"iVBORw0KGgoAAAANSUhEUgAAAVkAAAGACAYAAAAHwxE8AAAAAXNSR0IArs4c6QAA","PERSON-SEAL",$sealId0,0,0,1,1703001,"长度超过限制"]
#        - ["TC48-指定内部机构organizationCode包含首尾空格",$bisNo,"签署文本内容", $userCode0, $sp $orgCode0 $sp,"PERSON-SEAL",$sealId0,0,0,1,200,"成功"]
#        - ["TC49-指定内部结构，机构已删除",$bisNo,"签署文本内容",$userCode0,$deleteOrg,"PERSON-SEAL","",0,0,1,1703001,"印章id不能为空"]
##        - ["TC50-指定内部结构，用印员已删除",$bisNo,"签署文本内容",$userCode0,$orgCode0,"PERSON-SEAL",$sealId0 11,0,0,1,1703001,"印章id不存在"]
#        - ["TC52-指定内部结构，签署人非用印员，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"COMMON-SEAL",$sealId1,0,0,1,17020477,"印章不存在或者无使用权限"]
#        - ["TC53-指定相对方个人",$bisNo,"签署文本内容",$oppositeUserCode0,$oppositeOrgCode0,"PERSON-SEAL",$sealId0,0,0,1,1702077,"账号类型与传入的不一致！"]
#        - ["TC54-指定相对方机构",$bisNo,"签署文本内容",$oppositeUserCode0,$oppositeOrgCode0,"COMMON-SEAL",$sealId1,0,0,1,1702077,"账号类型与传入的不一致！"]
#
#
#
##        - ["TC1-data为空或不传",$bisNo,"",$userCode0,$orgCode0,"",$sealId0,"","",1,1703001,"文本内容不能为空"]
##        - ["TC2-data传文本格式内容，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC3-data传参为json字符串格式，其它参数正常，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC4-data传参为base64格式，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC5-data入参文本首尾包含空格，其它参数正常，其它参数正常",$bisNo,$sp"签署文本内容"$sp, $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC6-businessNo业务编号重复，其它参数正常",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC7-businessNo业务编号为空字符串/不传",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC8-businessNo业务编号包含首尾空格",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC9-businessNo业务编号包含不支持的特殊字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
##        - ["TC10-businessNo业务编号长度为51字符",$bisNo,"签署文本内容", $userCode0, $orgCode0,"",1,"","",1,1703001,"文本内容不能为空"]
#
#    testcase: testcases/signs/signTask/saveHtmlSignature.yml