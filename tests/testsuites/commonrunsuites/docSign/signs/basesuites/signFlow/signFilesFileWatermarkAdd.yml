config:
    name: 分步发起添加文档add接口-校验-signFiles
    variables:
      fileKey0: $${ENV(fileKey)}
      imgKey0: $${ENV(pngPageFileKey)}
      sp: " "

testcases:
-
    name: 分步发起-校验-signFiles新增参数
    parameters:
      - name-fileKey-waterMarkType-opacity-pagePosition-watermarkPage-angle-watermarkContent-watermarkSize-watermarkSpace-imgKey-watermarkPosition-watermarkPosX-watermarkPosY-code-message:
          - ["TC0-签署文档fileKey为空字符串/不传","",1,20,1,1,5,'测试水印',18,50,None,1,0,0,1703001,"签署文档fileKey为必填字段"]
          - ["TC1-watermarkType为1，配置相关文字水印信息",$fileKey0,1,20,1,1,5,'测试水印',18,50,None,1,0,0,200,"服务器成功返回"]
          - ["TC2-watermarkType设置为不支持的特殊字符",$fileKey0,'\/:*?"<>|',20,1,1,5,'测试水印',18,50,None,1,0,0,1703012,"waterMarkType字段类型不匹配"]
          - ["TC3-watermarkType设置为0",$fileKey0,0,20,1,1,5,'测试水印',18,50,None,1,0,0,1703001,"水印外观只能为1或2"]
          - ["TC4-watermarkType设置为大于2的数字",$fileKey0,5,20,1,1,5,'测试水印',18,50,None,1,0,0,1703001,"水印外观只能为1或2"]
          - ["TC5-watermarkType设置为负数",$fileKey0,-200,20,1,1,5,'测试水印',18,50,None,1,0,0,1703001,"水印外观只能为1或2"]
          - ["TC6-watermarkType设置为小数",$fileKey0,5.25,20,1,1,5,'测试水印',18,50,None,1,0,0,1703012,"waterMarkType字段类型不匹配"]
          - ["TC5-watermarkType设置为1",$fileKey0,1,20,1,1,5,'测试水印',18,50,None,1,0,0,200,"成功"]
          - ["TC6-watermarkType设置为2",$fileKey0,2,20,1,1,5,'测试水印',18,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC7-watermarkContent设置为不支持的特殊字符",$fileKey0,1,20,1,1,5,'\/:*?"<>|',18,50,None,1,0,0,1702448,"水印内容格式不正确"]
          - ["TC8-watermarkContent设置为大于20字符",$fileKey0,1,20,1,1,5,'测试水印超过二十个字符测试水印超过五十个字符测试水印超过二十个字符测试水印超过五十个字符测试水印超过二十个字符测试水印超过五',18,50,None,1,0,0,1702449,"水印内容最多仅支持50字符"]
          - ["TC10-watermarkSize设置为不支持的特殊字符",$fileKey0,1,20,1,1,5,'测试水印','\/:*?"<>|',50,None,1,0,0,1703012,"watermarkSize字段类型不匹配"]
          - ["TC11-watermarkSize设置小于10的整数",$fileKey0,1,20,1,1,5,'测试水印',5,50,None,1,0,0,1702455,"水印文字大小为10-18之间的整数"]
          - ["TC12-watermarkSize设置大于18的整数",$fileKey0,1,20,1,1,5,'测试水印',28,50,None,1,0,0,1702455,"水印文字大小为10-18之间的整数"]
          - ["TC13-watermarkSize设置为负数",$fileKey0,1,20,1,1,5,'测试水印',-18,50,None,1,0,0,1702455,"水印文字大小为10-18之间的整数"]
          - ["TC14-watermarkSize设置为小数",$fileKey0,1,20,1,1,5,'测试水印',19.25,50,None,1,0,0,1703012,"watermarkSize字段类型不匹配"]
          - ["TC15-watermarkSize设置为10-18之间整数",$fileKey0,1,20,1,1,5,'测试水印',16,50,None,1,0,0,200,"成功"]
          - ["TC16-水印图片imgKey设置为不支持的特殊字符",$fileKey0,2,20,1,1,5,'测试水印',18,50,'\/:*?"<>|',1,0,0,1702450,"图片imageKey格式不正确"]
          - ["TC19-水印图片imgKey包含首尾空格",$fileKey0,2,20,1,1,5,'测试水印',18,50,$sp$imgKey0$sp,1,0,0,200,"成功"]
          - ["TC20-水印图片imgKey正常场景",$fileKey0,2,20,1,1,5,'测试水印',18,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC21-opacity设置为不支持的特殊字符",$fileKey0,2,'\/:*?"<>|',1,1,5,'测试水印',18,50,$imgKey0,1,0,0,1703012,"opacity字段类型不匹配"]
          - ["TC22-opacity设置为0",$fileKey0,2,0,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC23-opacity设置为大于100的数字",$fileKey0,2,1000,1,1,5,'测试水印',5,50,None,1,0,0,1703001,"水印透明度范围0~100"]
          - ["TC24-opacity设置为负数",$fileKey0,2,-20,1,1,5,'测试水印',28,50,None,1,0,0,1703001,"水印透明度范围0~100"]
          - ["TC25-opacity设置为小数",$fileKey0,2,20.25,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,1703012,"opacity字段类型不匹配"]
          - ["TC26-opacity设置为50",$fileKey0,2,50,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC27-opacity设置为100",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC28-watermarkPage输入传入不支持的特殊字符",$fileKey0,1,100,1,'\/:*?"<>|',5,'测试水印',16,50,$imgKey0,1,0,0,1703012,"watermarkPage字段类型不匹配"]
          - ["TC29-watermarkPage设置为非数字字符",$fileKey0,1,100,1,abc,5,'测试水印',16,50,$imgKey0,1,0,0,1703012,"watermarkPage字段类型不匹配"]
          - ["TC30-watermarkPage设置为大于3整数",$fileKey0,1,100,1,5,5,'测试水印',16,50,$imgKey0,1,0,0,1703001,"水印页数范围1~3"]
          - ["TC31-watermarkPage设置为0",$fileKey0,1,100,1,0,5,'测试水印',16,50,$imgKey0,1,0,0,1703001,"水印页数范围1~3"]
          - ["TC32-watermarkPage设置为负数",$fileKey0,1,100,1,-10,5,'测试水印',16,50,$imgKey0,1,0,0,1703001,"水印页数范围1~3"]
          - ["TC33-watermarkPage设置为小数",$fileKey0,1,100,1,5.25,5,'测试水印',16,50,$imgKey0,1,0,0,1703012,"watermarkPage字段类型不匹配"]
          - ["TC34-watermarkPage设置为1",$fileKey0,1,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC35-watermarkPage设置为2",$fileKey0,1,100,1,2,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC36-watermarkPage设置为3",$fileKey0,1,100,1,3,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC37-watermarkPosition输入传入不支持的特殊字符",$fileKey0,1,100,1,1,5,'测试水印',16,50,$imgKey0,'\/:*?"<>|',0,0,1703012,"watermarkPosition字段类型不匹配"]
          - ["TC38-watermarkPosition设置为非数字字符",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,abc,0,0,1703012,"watermarkPosition字段类型不匹配"]
          - ["TC39-watermarkPosition设置为大于4整数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,8,0,0,1702457,"水印位置支持1-4之间整数"]
          - ["TC40-watermarkPosition设置为0",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,0,0,0,1702457,"水印位置支持1-4之间整数"]
          - ["TC41-watermarkPosition设置为负数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,-10,0,0,1702457,"水印位置支持1-4之间整数"]
          - ["TC42-watermarkPosition设置为小数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1.25,100.35,0,1703012,"watermarkPosition字段类型不匹配"]
          - ["TC43-watermarkPosition设置为1",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC44-watermarkPosition设置为2",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,2,0,0,200,"成功"]
          - ["TC45-watermarkPosition设置为3",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,3,0,0,200,"成功"]
          - ["TC46-watermarkPosition设置为4",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,4,0,0,200,"成功"]
          - ["TC47-watermarkPosX输入传入不支持的特殊字符",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,'\/:*?"<>|',0,1703012,"watermarkPosX字段类型不匹配"]
          - ["TC48-watermarkPosX设置为非数字字符",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,abc,0,1703012,"watermarkPosX字段类型不匹配"]
          - ["TC49-watermarkPosX设置为大于99999整数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,999999,0,1702459,"偏移量支持0-99999之间整数"]
          - ["TC50-watermarkPosX设置为0",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC51-watermarkPosX设置为负数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,-100,0,1702459,"偏移量支持0-99999之间整数"]
          - ["TC52-watermarkPosX设置为小数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,100.25,0,1703012,"watermarkPosX字段类型不匹配"]
          - ["TC53-watermarkPosX设置为99999",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,99999,0,200,"成功"]
          - ["TC54-watermarkPosY输入传入不支持的特殊字符",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,'\/:*?"<>|',1703012,"watermarkPosY字段类型不匹配"]
          - ["TC55-watermarkPosY设置为非数字字符",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,abc,1703012,"watermarkPosY字段类型不匹配"]
          - ["TC56-watermarkPosY设置为大于99999整数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,999999,1702459,"偏移量支持0-99999之间整数"]
          - ["TC57-watermarkPosY设置为0",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC58-watermarkPosY设置为负数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,-100,1702459,"偏移量支持0-99999之间整数"]
          - ["TC59-watermarkPosY设置为小数",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,100.25,1703012,"watermarkPosY字段类型不匹配"]
          - ["TC60-watermarkPosY设置为0",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC61-watermarkPosY设置为99999",$fileKey0,2,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,99999,200,"成功"]
          - ["TC62-pagePosition设置为不支持的特殊字符",$fileKey0,2,20,'\/:*?"<>|',1,5,'测试水印',18,50,None,1,0,0,1703012,"pagePosition字段类型不匹配"]
          - ["TC63-pagePosition设置为0",$fileKey0,1,20,0,1,5,'测试水印',18,50,None,1,0,0,1703001,"页面位置只能为1或2"]
          - ["TC64-pagePosition设置为大于2的数字",$fileKey0,1,20,5,1,5,'测试水印',18,50,None,1,0,0,1703001,"页面位置只能为1或2"]
          - ["TC65-pagePosition设置为负数",$fileKey0,1,20,-11,1,5,'测试水印',18,50,None,1,0,0,1703001,"页面位置只能为1或2"]
          - ["TC66-pagePosition设置为小数",$fileKey0,1,20,1.25,1,5,'测试水印',18,50,None,1,0,0,1703012,"pagePosition字段类型不匹配"]
          - ["TC67-pagePosition设置为1",$fileKey0,1,20,1,1,5,'测试水印',18,50,None,1,0,0,200,"成功"]
          - ["TC68-pagePosition设置为2",$fileKey0,1,20,2,1,5,'测试水印',18,50,None,1,0,0,200,"成功"]
          - ["TC69-angle设置为不支持的特殊字符",$fileKey0,1,100,1,1,'\/:*?"<>|','测试水印',18,50,$imgKey0,1,0,0,1703012,"angle字段类型不匹配" ]
          - ["TC70-angle设置为0",$fileKey0,1,0,1,1,5,'测试水印',18,50,None,1,100,100,200,"成功"]
          - ["TC71-angle设置为大于360的数字",$fileKey0,1,100,1,1,369,'测试水印',15,50,$imgKey0,1,0,0,1703001,"旋转角度范围0~359"]
          - ["TC72-angle设置为负数",$fileKey0,1,20,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC73-angle设置为小数",$fileKey0,1,20,1,1,5.5,'测试水印',18,50,$imgKey0,1,100,100,1703012,"angle字段类型不匹配"]
          - ["TC74-angle设置为50",$fileKey0,1,50,1,1,50,'测试水印',18,50,$imgKey0,1,0,0,200,"成功"]
          - ["TC75-angle设置为359",$fileKey0,1,100,1,1,5,'测试水印',16,50,$imgKey0,1,0,0,200,"成功"]


    testcase: testcases/signs/signFlow/signFilesWatermarkAdd.yml

-
    name: 分步发起添加流程文档接口-添加文档水印-校验-出参新增外网下载地址-6.0.14.0-beta1
    testcase: testcases/signs/signFlow/signFilesWatermarkAddTC1.yml