config:
    name: 添加签署方-流程由业务模板openapi发起
    variables:
      sp: " "

testcases:
-
         name: 校验-创建流程和添加文件添加签署方异常用例--已通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例1--已通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases1.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例2--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases2.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例3--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases3.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例4--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases4.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例5--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases5.yml

#-
#         name: 校验-创建流程和添加文件添加签署方异常用例6--多通道类型
#         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases6.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例7--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases7.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例8--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases8.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例9--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases9.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例10--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases10.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例11--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases11.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例12--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases12.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例13--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases13.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例14--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases14.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例15-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases15.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例16-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases16.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例17-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases17.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例18-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases18.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例19-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases19.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例20--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases20.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例21-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases21.yml


-
         name: 校验-创建流程和添加文件添加签署方异常用例22-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases22.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例23--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases23.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例24-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases24.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例25--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases25.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例26
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases26.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例28--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases28.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例30
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases30.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例31
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases31.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例32--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases32.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例33--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases33.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例34-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases34.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例35
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases35.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例36-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases36.yml
-
         name: 校验-创建流程和添加文件添加签署方异常用例37-通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases37.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例38--通过
         testcase: testcases/signs/signFlow/signAdd2/signCreateSingersAddCases38.yml

-
        name: SETUP-开通相对方付费-开启
        parameters:
          - keyIdCode1-value1:
              - [ "signer_pay","1" ]
        testcase: common/valuesCmc.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例--tongg
         testcase: testcases/signs/signFlow/signAdd2/signersAddchargingTypeCases.yml

-
         name: 校验-创建流程和添加文件添加签署方异常用例--通过
         testcase: testcases/signs/signFlow/signAdd2/signersAddOfXinchuangTC.yml