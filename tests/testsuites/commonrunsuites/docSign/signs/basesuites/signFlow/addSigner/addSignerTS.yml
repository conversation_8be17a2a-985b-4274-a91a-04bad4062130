config:
    name: 添加签署方-1
    variables:
      sp: " "

testcases:
-
         name: 添加签署方signFlowId异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases.yml

-
         name: 添加签署方keyfile异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases1.yml

-
         name: 添加签署方机构sealId异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases2.yml


-
         name: 添加签署方个人sealId异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases3.yml


-
         name: 添加签署方个人sealType异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases4.yml

-
         name: 添加签署方机构sealType异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases5.yml

-
         name: 添加签署方机构签署人异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases6.yml

-
         name: 添加签署方机构签署人为相对方机构异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases7.yml

-
         name: 添加签署方为相对方个人异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases8.yml

-
         name: 添加签署方为内部个人异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases9.yml