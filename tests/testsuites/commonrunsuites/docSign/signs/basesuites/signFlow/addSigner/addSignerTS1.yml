config:
    name: 添加签署方异常用例cases-10
    variables:
      sp: " "

testcases:
-
         name: 添加签署方为内部个人静默签ukeySign异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases10.yml

-
         name: 添加签署方为内部个人ofd文档异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases11.yml

-
         name: 添加签署方为内部个人ukey不传
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases12.yml

-
         name: 添加签署方为内部个人静默签个人异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases13.yml

-
         name: 添加签署方为相对方个人静默签相对方个人异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases14.yml

-
         name: 添加签署方为相对方机构静默签相对方机构异常用例
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases15.yml

-
         name: 添加签署方为相对方机构内部机构异常用例
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases16.yml

-
         name: 添加签署方为相对方机构内部机构异常用例isHandEnable
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases17.yml

-
         name: 添加签署方为相对方机构内部机构异常用例posX和PosY异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases18.yml

-
         name: 添加签署方为相对方机构内部机构异常用例pageNo异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases19.yml