config:
    name: 无节点-校验签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    variables:
      signatureType0: " PERSON-SEAL "
      signatureType1: " COMMON-SEAL "
      userCode0: ${ENV(sign01.userCode)}
      userCodeOpposit: ${ENV(wsignwb01.userCode)}
      userCodeInit: ${ENV(csqs.userCode)}
      orgCodeInit: ${ENV(csqs.orgCode)}
      orgCode0: ${ENV(sign01.main.orgCode)}
      orgCodeOpposit: ${ENV(worg01.orgCode)}
      departmentCode0: ${ENV(sign01.main.orgCode)}
      customAccountNoSigner0: ${ENV(sign01.accountNo)}
      #内部用户的主责是部门
      customAccountNoSigner1: ${ENV(sign03.accountNo)}
      departmentNo1: ${ENV(sign03.main.departNo)}
      orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
      customAccountNoSigner2: ${ENV(sign01.accountNo)}
      customAccountNoSigner3: ${ENV(sign03.accountNo)}
      departmentNo2: ${ENV(sign01.JZ.departNo)}
      orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
      customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
      customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
      customDepartmentNoSignerOpposit: ${ENV(worg01.orgNo)}
      customOrgNoSignerOpposit: ${ENV(worg01.orgNo)}
      sp: " "
      customAccountNoDimission: ${getUserInfoByDB(2,account_number, 1)}
      customAccountNoDelete: ${getUserInfoByDB(6,account_number, 1)}
      customOrgNoDelete: ${getOrgInfoByDB(6,account_number, 1, 1)}
      customDepartmentNo1: ${ENV(worg01.orgNo)}
      fileKey0: "$${ENV(fileKey)}"
      signFlow00: ${getCreateByBizTemplateId()}

testcases:
-
    name: 无节点-校验签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNo-customOrgNo-customDepartmentNo-userCode-organizationCode-departmentCode-signatureType-code-message:
          - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,"","","","","",$signatureType0,200,"成功"]
          - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,"","",$userCode0,"","",$signatureType0,200,"成功"]
          - ["TC3-customAccountNo和userCode都为空,报错","","","","","","",$signatureType0,1701051,"签署人编码和签署人账号不能同时为空"]
          - ["TC4-customAccountNo和userCode都不传,报错",null,"","",null,"","",$signatureType0,1701051,"签署人编码和签署人账号不能同时为空"]
          - ["TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',"","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',"","",$userCode0,"","",$signatureType0,200,"成功"]
          - ["TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,"","","XXXXXX","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC8-customAccountNo对应的账号为离职状态，userCode为空,报错",$customAccountNoDimission,"","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC9-customAccountNo对应的账号为删除状态，userCode为空,报错",$customAccountNoDelete,"","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功"]
          - ["TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",$signatureType1,200,"成功"]
          - ["TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",$signatureType1,1702518,"在签署文件中至少设置一个{个人}签署区"]
          - ["TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",$signatureType1,1702518,"在签署文件中至少设置一个{个人}签署区"]
          - ["TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",$signatureType1,1701053,"未查询到签署人组织信息"]
          - ["TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",$signatureType1,200,"成功"]
          - ["TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",$signatureType1,1701053,"未查询到签署人组织信息"]
          - ["TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",$signatureType1,1701053,"未查询到签署人组织信息"]
          - ["TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",$signatureType1,200,"成功"]
          - ["TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,"",$customOrgNoSignerOpposit,"","",$orgCode0,$signatureType0,200,"成功"]
          - ["TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功"]
          - ["TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,"",null,"","",null,$signatureType0,200,"成功"]
          - ["TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,"","XXXXXX","","","",$signatureType0,1701053,"未查询到签署人组织信息"]
          - ["TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,'',"","","",$departmentCode0,$signatureType0,200,"成功"]
          - ["TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,'',$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",$signatureType1,1701053,"未查询到签署人组织信息"]
          - ["TC25-customDepartmentNo对应的账号为注销状态，departmentCode为空,报错",$customAccountNoSigner0,"",$customOrgNoDelete,"","","",$signatureType0,1701053,"未查询到签署人组织信息"]
          - ["TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",$signatureType1,200,"成功"]
          - ["TC27-customAccountNo所属主责是企业，校验发起后customDepartmentNo为该企业,成功",$sp$customAccountNoSigner0$sp,"","","","","",$signatureType0,200,"成功"]
          - ["TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner1,"","","","","",$signatureType0,200,"成功"]
          - ["TC29-customAccountNo和customDepartmentNo没有绑定关系,报错",$customAccountNoSigner0,"",$customDepartmentNo1,"","","",$signatureType0,1702393,"未知的部门信息！"]
          - ["TC30-customAccountNo和customOrgNo没有授权关系,报错",$customAccountNoSigner0,"","","",$orgCodeOpposit,"",$signatureType1,1702443,"当前用户不是当前企业章用印人"]
          - ["TC31-customAccountNo传入的长度超过36字符,报错","${generate_random_str(37)}","","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,"","${generate_random_str(65)}","","","",$signatureType0,1701053,"未查询到签署人组织信息"]
          - ["TC33-customOrgNo传入的长度超过64字符,报错",$customAccountNoSigner0,"${generate_random_str(65)}","","","","",$signatureType0,1701053,"未查询到签署人组织信息"]
          - ["TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,"","","","","",$signatureType0,200,"成功"]
          - ["TC35-customAccountNo传入的是相对方个人的账号，userType限定是内部,报错",$customAccountNoSignerOpposit,"","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC36-customAccountNo传入不支持的特殊字符,报错","${get_not_support_str()}","","","","","",$signatureType0,1701052,"未查询到签署人账号信息"]
          - ["TC37-customAccountNo兼职部门，指定兼职部门发起流程,成功",$customAccountNoSigner2,"",$departmentNo2,"","","",$signatureType0,200,"成功"]

    testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases27.yml

-
    name: 无节点-校验多个签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-signerInfos-code-message:
          - ["TC1-通过customAccountNo指定第一个为A，通过userCode指定第二个签署人A,报错",[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCode0"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"signMode": 0,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],1603125,"添加的签署方存在重复数据"]
          - ["TC2-通过customAccountNo指定第一个为A，通过userCode指定第二个签署人B,成功",[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"signMode": 0,"userType": 1,"userCode": "$userCodeInit"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"signMode": 0,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],200,"成功"]
          - ["TC3-通过customAccountNo和customDepartmentNo和organizationCode指定第一个签署方A，通过userCode和departmentCode和customOrgNo指定第二个签署方B,成功",[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode":$orgCodeOpposit,"customDepartmentNo":$customOrgNoSignerOpposit},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"signMode": 0,"userType": 1,"customAccountNo": "$customAccountNoSigner0","departmentCode":$departmentCode0,"customOrgNo":$customOrgNoSigner0}],200,"成功"]
          - ["TC4-指定四个不同签署主体校验出参信息,成功",[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit","organizationCode":$orgCodeOpposit,"customDepartmentNo":$customOrgNoSignerOpposit},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"signMode": 0,"userType": 2,"userCode": "$userCodeOpposit"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 3,"signMode": 0,"userType": 1,"customAccountNo": "$customAccountNoSigner0","departmentCode":$departmentCode0,"customOrgNo":$customOrgNoSigner0},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 4,"signMode": 0,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],200,"成功"]
    testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases28.yml

-
    name: 无节点-校验allowMove-签署区可移动校验
    parameters:
      - name-signFlowId-allowMove-signFieldType-customOrgNoSigner-customAccountNoSigner-userTypeSigner-signatureType-code-message-data0:
          - [ "TC1-个人签署区可移动allowMove=1",'${gen_startSignFlow_node(3)}',1,0,"",$userCode0,1,"PERSON-SEAL",200,"成功","" ]
          - [ "TC2-个人签署区可移动allowMove=0",'${gen_startSignFlow_node(3)}',0,0,"",$userCode0,1,"PERSON-SEAL",200,"成功","" ]
          - [ "TC3-企业签署区可移动allowMove=1",'${gen_startSignFlow_node(3)}',1,0,$customOrgNoSigner0,$userCode0,1,"COMMON-SEAL",200,"成功","" ]
          - [ "TC4-企业签署区可移动allowMove=1",'${gen_startSignFlow_node(3)}',0,0,$customOrgNoSigner0,$userCode0,1,"COMMON-SEAL",200,"成功","" ]
          - [ "TC5-内部备注签署区可移动allowMove=1",'${gen_startSignFlow_node(3)}',1,1,"",$userCode0,1,"PERSON-SEAL",200,"成功","" ]
          - [ "TC6-外部备注签署区可移动allowMove=1",'${gen_startSignFlow_node(3)}',1,1,"",$userCodeOpposit,2,"PERSON-SEAL",200,"成功","" ]
          - [ "TC7-allowMove传入未定义的值allowMove=2",'${gen_startSignFlow_node(3)}',2,0,"",$userCode0,1,"PERSON-SEAL",200,"成功","" ]
          - [ "TC8-allowMove传入未定义的值allowMove=是",'$signFlow00','是',0,"",$userCode0,1,"PERSON-SEAL",1600015,"allowMove参数错误!","" ]
          - [ "TC9-allowMove传入未定义的值allowMove=-1",'${gen_startSignFlow_node(3)}',-1,0,"",$userCode0,1,"PERSON-SEAL",200,"成功","" ]
          - [ "TC10-allowMove传入未定义的值allowMove=0.09",'$signFlow00',0.09,0,"",$userCode0,1,"PERSON-SEAL",1600015,"allowMove参数错误!","" ]

    testcase: testcases/signs/signFlow/signAdd2/signCreateSignersAddCases29.yml