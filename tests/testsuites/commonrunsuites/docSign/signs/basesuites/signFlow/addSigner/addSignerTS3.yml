config:
    name: 添加签署方-30
    variables:
      sp: " "

testcases:
-
         name: 校验-freeMode自由签
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases30.yml

-
         name: 校验-allowAddAttachment
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases31.yml

-
         name: 校验-6076-组织用户体系改造
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases32.yml

-
         name: 校验-支持法人章作为主体印章
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases33.yml

-
         name: 校验-UKeyOnly指定仅云印章
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases34.yml

-
         name: 校验-优化自由签的只读逻辑-指定文件签署不指定的只读
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases35.yml

-
         name: 校验-自由签支持普通和骑缝-V6.0.10.0-beta.2
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases36.yml

-
         name: 校验-batchSignDefault校验-V6.0.10.0-beta.2
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases37.yml