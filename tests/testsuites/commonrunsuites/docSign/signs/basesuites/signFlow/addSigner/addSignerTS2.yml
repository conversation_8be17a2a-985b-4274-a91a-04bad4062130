config:
    name: 添加签署方异常用例cases-20
    variables:
      sp: " "
      userCode0: ${ENV(sign01.userCode)}
#      userCodeOpposit: ${ENV(wsignwb01.userCode)}
      orgCode0: ${ENV(sign01.main.orgCode)}
#      orgCodeOpposit: ${ENV(worg01.orgCode)}
#      departmentCode0: ${ENV(sign01.main.orgCode)}
      customAccountNoSigner0: ${ENV(sign01.accountNo)}
      #内部用户的主责是部门
      customAccountNoSigner1: ${getUserInfoByDB(7,account_number, 1)}
      #customAccountNoSigner1的所属部门的部门信息
      orgId1: ${getUserInfoByDB(7,organization_id, 1)}
      departmentNo1: ${getOrgInfoByDB(10,$orgId1, 1, 1)}
      customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
      customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
      customDepartmentNoSignerOpposit: ${ENV(worg01.orgNo)}
      customOrgNoSignerOpposit: ${ENV(worg01.orgNo)}
      customAccountNoDimission: ${getUserInfoByDB(2,account_number, 1)}
      orgNoDimission: ${getUserInfoByDB(9,$customAccountNoDimission, 1)}
      customAccountNoDelete: ${getUserInfoByDB(6,account_number, 1)}
      orgNoDelete: ${getUserInfoByDB(9,$customAccountNoDelete, 1)}
      customOrgNoDelete: ${getOrgInfoByDB(6,account_number, 1, 1)}
      customDepartmentNo1: ${getOrgInfoByDB(1,account_number, 1, 2)}


testcases:
-
         name: 无节点添加签署人新增参数校验
         parameters:
          - name-customAccountNoSigner-customOrgNoSigner-customDepartmentNoSigner-userCode-organizationCode-departmentCode-userType-signatureType-code-message:
            - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,"","","",$orgCode0,"",1," COMMON-SEAL ",200,"成功"]
            - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号",$customAccountNoSignerOpposit,"","",$userCode0,$orgCode0,"",1," COMMON-SEAL ",200,"成功"]
            - ["TC3-customAccountNo和userCode都为空","","","","",$orgCode0,"",1," PERSON-SEAL ",1701051,"签署人编码和签署人账号不能同时为空"]
            - ["TC4-customAccountNo不存在的值，userCode为空","dsfdsf","","","",$orgCode0,"",1," PERSON-SEAL ",1701052,"未查询到签署人账号信息"]
            - ["TC5-customAccountNo不存在的值，userCode为正确的值","dsfdsf","","",$userCode0,$orgCode0,"",1," COMMON-SEAL ",200,"成功"]
            - ["TC6-customAccountNo为正常值，userCode不存在的值",$customAccountNoSigner0,"","","xxxxx",$orgCode0,"",1," PERSON-SEAL ",1701052,"未查询到签署人账号信息"]
            - ["TC7-customAccountNo对应的账号为离职状态，userCode为空",$customAccountNoDimission,$orgNoDimission,"","","","",1," PERSON-SEAL ",1701052,"未查询到签署人账号信息"]
            - ["TC8-customAccountNo对应的账号为注销状态，userCode为空",$customAccountNoDelete,$customOrgNoSigner0,"","","","",1," PERSON-SEAL ",1701052,"未查询到签署人账号信息"]
            - ["TC9-customDepartmentNo为正常值，departmentCode为空",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",1," COMMON-SEAL ",200,"成功"]
            - ["TC10-customDepartmentNo为正常值A账号，departmentCode为正常值B账号",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,1," COMMON-SEAL ",200,"成功"]
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases27.yml

-
         name: 添加签署方为相对方机构内部机构异常用例with异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases20.yml

-
         name: 添加签署方为相对方机构内部机构异常用例keyword异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases21.yml

-
         name: 添加签署方为相对方机构内部机构异常用例keyword签offsetPosX和OffesetPosY异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases22.yml

-
         name: 添加签署方为相对方机构内部机构异常用例keyword签keyIndex
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases23.yml


-
         name: 添加签署方为相对方机构内部机构异常用例骑缝签异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases24.yml

-
         name: 添加签署方为相对方机构内部机构异常用例addSignTime异常
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases25.yml

-
         name: 补充用例
         testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddCases26.yml