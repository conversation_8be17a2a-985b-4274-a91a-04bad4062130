config:
    name: 创建流程用例集


testcases:
-
         name: subject用例case
         testcase: testcases/signs/signFlow/create/signFlowCreate.yml


-
         name: bussinessNo的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateBizNo.yml

-
         name: remark的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateRemark.yml

-
         name: BusinessTypeCode的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateBusinessTypeCode.yml

-
         name: ReadComplete的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateReadComplete.yml


-
         name: redirectUrl的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateRedirectUrl.yml

-
         name: signNotifyUrl的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignNotifyUrl.yml
-
         name: 发起人userCode的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignInitUserCode.yml

-
         name: 发起人organizationCode的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignInitOrganiza.yml

-
         name: 发起人userType的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateInitUserType.yml

-
         name: 抄送人userType的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateCCUserType.yml

-
         name: 抄送人userCode的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignCCUserCode.yml

-
         name: 抄送人organizationCode的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignCCOrganiza.yml

-
         name: 抄送人departMent的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateDepartment.yml

-
         name: 抄送人SignValidate的用例case
         testcase: testcases/signs/signFlow/create/signFlowCreateSignValidate.yml
-
         name: 分步发起-flowExtensions字段用例
         testcase: testcases/signs/signFlow/create/createFlowExtensions.yml