config:
    name: 一步发起-校验-subject-businessNo
#    variables:
#      desensitive: "true"

testcases:
-
    name: 一步发起-校验-subject-businessNo
    parameters:
      - name-subject-businessNo-code-message:
          - ["TC1-流程主题传入空字符串，报错","","No-1",1703001,"流程主题支持的字符长度为1-200"]
          - ["TC2-流程主题不传，报错",NULL,"",1703001,"流程主题支持的字符长度为1-200"]
          - ["TC3-流程主题输入不支持的特殊字符，报错","${get_not_support_str()}","",1702106,"流程标题存在非法字符！"]
          - ["TC4-流程主题长度超过200，报错","${generate_random_str(201)}","",1703001,"流程主题支持的字符长度为1-200"]
          - ["TC5-流程主题长度200，报错","${generate_random_str(200)}","",200,"成功"]
          - ["TC6-流程主题输入特殊字符","${get_support_str(10)}","",200,"成功"]
          - ["setup-流程主题允许重复"," 《流程重复的主题》 ","",200,"成功"]
          - ["TC7-流程主题重复"," 《流程重复的主题》 ","",200,"成功"]
          - ["TC8-业务编码长度超过50，成功"," 《流程重复的主题》 ","${generate_random_str(191)}",200,"成功"]
          - ["TC9-业务编码长度50"," ${generate_random_str(5)} ","${generate_random_str(50)}",200,"成功"]
          - ["setup-业务编码允许重复"," ${generate_random_str(5)} ","业务编码重复",200,"成功"]
          - ["TC10-业务编码重复"," ${generate_random_str(5)} ","业务编码重复",200,"成功"]
          - ["TC11-业务编码允许为空"," ${generate_random_str(5)} ","",200,"成功"]
          - ["TC12-业务编码允许不传"," ${generate_random_str(5)} ",NULL,200,"成功"]
          - ["TC13-业务编码传入不支持的特殊字符，允许","业务编码支持特殊字符 "," ${get_not_support_str()} ",200,"成功"]
          - ["TC14-业务编码传入支持的特殊字符","${generate_random_str(5)}","${get_support_str(10)}",200,"成功"]
          - ["TC8-业务编码长度超过192，报错"," 《流程重复的主题》 ","${generate_random_str(192)}",1703001,"流程编号不可超过191个字符！"]

    testcase: testcases/signs/signFlow/createAndStart/subjectBusinessNoTC.yml
-
    name: 新增businessNo扩展至191用例
    testcase: testcases/signs/signFlow/businessNo100.yml