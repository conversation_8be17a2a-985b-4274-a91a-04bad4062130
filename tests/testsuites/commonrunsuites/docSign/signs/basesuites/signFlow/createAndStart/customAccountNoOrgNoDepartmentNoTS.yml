config:
    name: 一步发起-校验签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    variables:
      signatureType0: " PERSON-SEAL "
      signatureType1: " COMMON-SEAL "
      userCode0: ${ENV(sign01.userCode)}
      userCodeOpposit: ${ENV(wsignwb01.userCode)}
      userCodeInit: ${ENV(csqs.userCode)}
      orgCodeInit: ${ENV(csqs.orgCode)}
      orgCode0: ${ENV(sign01.main.orgCode)}
      orgCodeOpposit: ${ENV(worg01.orgCode)}
      departmentCode0: ${ENV(sign01.main.orgCode)}
      customAccountNoSigner0: ${ENV(sign01.accountNo)}
      #内部用户的主责是部门
      customAccountNoSigner1: ${ENV(sign03.accountNo)}
      departmentNo1: ${ENV(sign03.main.departNo)}
      orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
      customAccountNoSigner2: ${ENV(sign01.accountNo)}
      customAccountNoSigner3: ${ENV(sign03.accountNo)}
      departmentNo2: ${ENV(sign01.JZ.departNo)}
      orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
      customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner3: ${get_constant(userCodeToDepartmentOrgNo)}
      customDeptNoSigner3: ${get_constant(userCodeToDepartmentDepNo)}
      customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
      customDepartmentNoSignerOpposit: ${ENV(worg01.orgNo)}
      customOrgNoSignerOpposit: ${ENV(worg01.orgNo)}
      sp: " "
      customAccountNoDimission: ${getUserInfoByDB(2,account_number, 1)}
      orgNoDimission: ${getUserInfoByDB(9,$customAccountNoDimission, 1)}
      customAccountNoDelete: ${getUserInfoByDB(6,account_number, 1)}
      orgNoDelete: ${getUserInfoByDB(9,$customAccountNoDelete, 1)}
      customOrgNoDelete: ${getOrgInfoByDB(6,account_number, 1, 1)}
      customDepartmentNo1: ${ENV(worg01.orgNo)}
      fileKey0: "$${ENV(fileKey)}"
      signerInfos0: [ { "sealInfos": [ { "fileKey": $fileKey0 } ],"signNode": 1,"userType": 1,"userCode": "$userCode0" } ]

testcases:
-
    name: 一步发起-校验签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNoSigner-customOrgNoSigner-customDepartmentNoSigner-userCodeSigner-organizationCodeSigner-departmentCodeSigner-signatureType-code-message:
          - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,"","","","","",$signatureType0,200,"成功"]
          - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,"","",$userCode0,"","",$signatureType0,200,"成功"]
          - ["TC3-customAccountNo和userCode都为空,报错","","","","","","",$signatureType0,1702179,"签署人用户编码不可为空！"]
          - ["TC4-customAccountNo和userCode都不传,报错",null,"","",null,"","",$signatureType0,1702179,"签署人用户编码不可为空！"]
          - ["TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',"","",$userCode0,"","",$signatureType0,200,"成功"]
          - ["TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,"","","XXXXXX","","",$signatureType0,1702045,"未知的账号信息！"]
          - ["TC8-customAccountNo对应的账号为离职状态，userCode为空,报错",$customAccountNoDimission,"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC9-customAccountNo对应的账号为删除状态，userCode为空,报错",$customAccountNoDelete,"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功"]
          - ["TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",$signatureType1,200,"成功"]
          - ["TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",$signatureType1,1702518,"至少设置一个{个人}签署区"]
          - ["TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",$signatureType1,1702518,"至少设置一个{个人}签署区"]
          - ["TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",$signatureType1,1702446,"机构不存在"]
          - ["TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",$signatureType1,200,"成功"]
          - ["TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",$signatureType1,1702046,"未知的组织信息！"]
          - ["TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",$signatureType1,1702446,"机构不存在"]
          - ["TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",$signatureType1,200,"成功"]
          - ["TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,"",$customOrgNoSignerOpposit,"","",$orgCode0,$signatureType0,200,"成功"]
          - ["TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",$signatureType1,200,"成功"]
          - ["TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,"",null,"","",null,$signatureType0,200,"成功"]
          - ["TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,"","XXXXXX","","","",$signatureType0,1702434,"用户所属组织不存在（客户系统的唯一标识）: XXXXXX"]
          - ["TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,'',"","","",$departmentCode0,$signatureType0,200,"成功"]
          - ["TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,'',$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",$signatureType1,1702444,"当前部门与当前用户信息不匹配"]
          - ["TC25-customDepartmentNo对应的账号为注销状态，departmentCode为空,报错",$customAccountNoSigner0,"",$customOrgNoDelete,"","","",$signatureType0,1702434,"用户所属组织不存在（客户系统的唯一标识）"]
          - ["TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",$signatureType1,200,"成功"]
          - ["TC27-customAccountNo所属主责是企业，校验发起后customDepartmentNo为该企业,成功",$sp$customAccountNoSigner0$sp,"","","","","",$signatureType0,200,"成功"]
          - ["TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner1,"","","","","",$signatureType0,200,"成功"]
          - ["TC29-customAccountNo和customDepartmentNo没有绑定关系,报错",$customAccountNoSigner0,"",$customDepartmentNo1,"","","",$signatureType0,1702393,"未知的部门信息！"]
          - ["TC30-customAccountNo和customOrgNo没有授权关系,报错",$customAccountNoSigner0,"","","",$orgCodeOpposit,"",$signatureType1,1702443,"当前用户不是当前企业章用印人"]
          - ["TC31-customAccountNo传入的长度超过36字符,报错","${generate_random_str(37)}","","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,"","${generate_random_str(65)}","","","",$signatureType0,1702434,""]
          - ["TC33-customOrgNo传入的长度超过64字符,报错",$customAccountNoSigner0,"${generate_random_str(65)}","","","","",$signatureType0,1702446,"机构不存在"]
          - ["TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,"","","","","",$signatureType0,200,"成功"]
          - ["TC35-customAccountNo传入的是相对方个人的账号，userType限定是内部,报错",$customAccountNoSignerOpposit,"","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）: wsignwb01"]
          - ["TC36-customAccountNo传入不支持的特殊字符,报错","${get_not_support_str()}","","","","","",$signatureType0,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC37-customAccountNo兼职部门，指定兼职部门发起流程,成功",$customAccountNoSigner2,"",$departmentNo2,"","","",$signatureType0,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/signerCustomAccountNoOrgNoDepartmentNoTC.yml

-
    name: 一步发起-校验多个签署人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-CCInfos-signerInfos-code-message:
          - ["TC1-通过customAccountNo指定第一个为A，通过userCode指定第二个签署人A,报错",[],[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"userType": 1,"userCode": "$userCodeSigner"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],1702076,"同一流程签署方不可重复！"]
          - ["TC2-通过customAccountNo指定第一个为A，通过userCode指定第二个签署人B,成功",[],[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"userType": 1,"userCode": "$userCodeInit"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],200,"成功"]
          - ["TC3-通过customAccountNo和customDepartmentNo和organizationCode指定第一个签署方A，通过userCode和departmentCode和customOrgNo指定第二个签署方B,成功",[],[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"userType": 2,"userCode": "$userCodeOpposit","organizationCode":$orgCodeOpposit,"customDepartmentNo":$customOrgNoSignerOpposit},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"userType": 1,"customAccountNo": "$customAccountNoSigner0","departmentCode":$departmentCode0,"customOrgNo":$customOrgNoSigner0}],200,"成功"]
          - ["TC4-指定四个不同签署主体校验出参信息,成功",[],[{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 1,"userType": 2,"userCode": "$userCodeOpposit","organizationCode":$orgCodeOpposit,"customDepartmentNo":$customOrgNoSignerOpposit},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 2,"userType": 2,"userCode": "$userCodeOpposit"},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 3,"userType": 1,"customAccountNo": "$customAccountNoSigner0","departmentCode":$departmentCode0,"customOrgNo":$customOrgNoSigner0},{"sealInfos":[{"fileKey": $fileKey0}],"signNode": 4,"userType": 1,"customAccountNo": "$customAccountNoSigner0"}],200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/customAccountNoOrgNoDepartmentNoJsonTC.yml

-
    name: 一步发起-校验多个抄送人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-CCInfos-signerInfos-code-message:
          - ["TC1-通过customAccountNo指定第一个为A，通过userCode指定第二个抄送人A,报错",[{"userType": 1,"userCode": "$userCode0","customOrgNo":$customOrgNoSigner0},{"userType": 1,"customAccountNo": "$customAccountNoSigner0","customOrgNo":$customOrgNoSigner0}],$signerInfos0,1702140,"同一流程抄送方不可重复！"]
          - ["TC2-通过customAccountNo和customDepartmentNo和organizationCode指定第一个抄送方A，通过userCode和departmentCode和customOrgNo指定第二个抄送方B,成功",[{"userType": 2,"userCode": "$userCodeOpposit","organizationCode":$orgCodeOpposit,"customDepartmentNo":$customOrgNoSignerOpposit},{"userType": 1,"customAccountNo": "$customAccountNoSigner0","departmentCode":$departmentCode0,"customOrgNo":$customOrgNoSigner0}],$signerInfos0,200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/customAccountNoOrgNoDepartmentNoJsonTC.yml

-
    name: 一步发起-校验抄送人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNoCC-customOrgNoCC-customDepartmentNoCC-userCodeCC-organizationCodeCC-departmentCodeCC-userTypeCC-code-message:
          - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,$customOrgNoSigner0,"",$userCode0,"","",1,200,"成功"]
          - ["TC3-customAccountNo和userCode都为空,报错","",$customOrgNoSigner0,"","","","",1,1702168,"抄送人用户编码不可为空！"]
          - ["TC4-customAccountNo和userCode都不传,报错",null,$customOrgNoSigner0,"",null,"","",1,1702168,"抄送人用户编码不可为空！"]
          - ["TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',$customOrgNoSigner0,"",$userCode0,"","",1,200,"成功"]
          - ["TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","XXXXXX","","",1,1702045,"未知的账号信息！"]
          - ["TC8-customAccountNo对应的账号为离职状态，userCode为空,报错",$customAccountNoDimission,$orgNoDimission,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC9-customAccountNo对应的账号为删除状态，userCode为空,报错",$customAccountNoDelete,$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",1,200,"成功"]
          - ["TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",1,200,"成功"]
          - ["TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",1,200,"成功"]
          - ["TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",1,200,"成功"]
          - ["TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",1,1702046,"未知的组织信息！"]
          - ["TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",1,200,"成功"]
          - ["TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,1,200,"成功"]
          - ["TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,null,"","",null,1,200,"成功"]
          - ["TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,$customOrgNoSigner0,"XXXXXX","","","",1,1702434,"用户所属组织不存在（客户系统的唯一标识）: XXXXXX"]
          - ["TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","",$departmentCode0,1,200,"成功"]
          - ["TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",1,1702078,"当前用户信息和组织信息不匹配！"]
          - ["TC25-customDepartmentNo对应的账号为注销状态，departmentCode为空,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoDelete,"","","",1,1702434,"用户所属组织不存在（客户系统的唯一标识）"]
          - ["TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",1,200,"成功"]
          - ["TC27-customAccountNo所属主责是企业，校验发起后customDepartmentNo为该企业,成功",$sp$customAccountNoSigner0$sp,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功"]
          - ["TC29-customAccountNo和customDepartmentNo没有绑定关系,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNo1,"","","",1,1702078,"当前用户信息和组织信息不匹配！"]
          - ["TC30-customAccountNo和customOrgNo没有绑定关系,报错",$customAccountNoSigner0,$customOrgNoSignerOpposit,$customDepartmentNoSigner0,"","","",1,1702078,"当前用户信息和组织信息不匹配！"]
          - ["TC31-customAccountNo传入的长度超过36字符,报错","${generate_random_str(37)}",$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,$customOrgNoSigner0,"${generate_random_str(65)}","","","",1,1703001,"抄送人所在组织长度不可超出64字符！"]
          - ["TC33-customOrgNo传入的长度超过64字符,报错",$customAccountNoSigner0,"${generate_random_str(65)}","","","","",1,1703001,"抄送人所在组织长度不可超出64字符！"]
          - ["TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功"]
          - ["TC35-customAccountNo传入的是相对方个人的账号，userType限定是内部,报错",$customAccountNoSignerOpposit,$customOrgNoSignerOpposit,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）: wsignwb01"]
          - ["TC36-customAccountNo传入不支持的特殊字符,报错","${get_not_support_str()}",$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC37-customAccountNo所属部门，customOrgNo和customDepartmentNo不匹配,报错",$customAccountNoSigner1,$customOrgNoSigner0,$departmentNo1,"","","",1,1702078,"当前用户信息和组织信息不匹配！"]
          - ["TC38-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,"","","","",1,200,"成功"]
          - ["TC39-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，对应的code都不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功"]
          - ["TC40-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo不传,报错",$customAccountNoSigner0,$customOrgNoSigner3,"","","","",1,200,"成功"]
          - ["TC41-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，成功",$customAccountNoSigner0,$customOrgNoSigner3,"${ENV(sign01.JZ.departNo)}","","","",1,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/CCCustomAccountNoOrgNoDepartmentNoTC.yml

-
    name: 一步发起-校验发起人新增的customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNoInitiator-customOrgNoInitiator-customDepartmentNoInitiator-userCodeInitiator-organizationCodeInitiator-departmentCodeInitiator-userTypeInitiator-code-message:
          - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,$customOrgNoSigner0,"",$userCode0,"","",1,200,"成功"]
          - ["TC3-customAccountNo和userCode都为空,报错","",$customOrgNoSigner0,"","","","",1,1702160,"流程发起人编码不可为空！"]
          - ["TC4-customAccountNo和userCode都不传,报错",null,$customOrgNoSigner0,"",null,"","",1,1702160,"流程发起人编码不可为空！"]
          - ["TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',$customOrgNoSigner0,"",$userCode0,"","",1,200,"成功"]
          - ["TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","XXXXXX","","",1,1702045,"未知的账号信息！"]
          - ["TC8-customAccountNo对应的账号为离职状态，userCode空,报错",$customAccountNoDimission,$orgNoDimission,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC9-customAccountNo对应的账号为删除状态，userCode空,报错",$customAccountNoDelete,$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",1,200,"成功"]
          - ["TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",1,200,"成功"]
          - ["TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",1,200,"成功"]
          - ["TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",1,200,"成功"]
          - ["TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",1,1702046,"未知的组织信息！"]
          - ["TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",1,200,"成功"]
          - ["TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,1,200,"成功"]
          - ["TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,null,"","",null,1,200,"成功"]
          - ["TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,$customOrgNoSigner0,"XXXXXX","","","",1,1702434,"用户所属组织不存在（客户系统的唯一标识）: XXXXXX"]
          - ["TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","",$departmentCode0,1,200,"成功"]
          - ["TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",1,1702046,"未知的组织信息！"]
#          - ["TC25-customDepartmentNo对应的账号为注销状态，departmentCode为空,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoDelete,"","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",1,200,"成功"]
          - ["TC27-customAccountNo所属主责是企业，校验发起后customDepartmentNo为该企业,成功",$sp$customAccountNoSigner0$sp,$customOrgNoSigner0,"","","","",1,200,"成功"]
          - ["TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功"]
#          - ["TC29-customAccountNo和customDepartmentNo没有绑定关系,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNo1,"","","",1,1702391,"流程发起人组织编码和机构编码不匹配！"]
          - ["TC30-customAccountNo和customOrgNo没有绑定关系,报错",$customAccountNoSigner0,$customOrgNoSignerOpposit,$customDepartmentNoSigner0,"","","",1,1702391,"流程发起人组织编码和机构编码不匹配！"]
          - ["TC31-customAccountNo传入的长度超过36字符,报错","${generate_random_str(37)}",$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,$customOrgNoSigner0,"${generate_random_str(65)}","","","",1,1702434,""]
          - ["TC33-customOrgNo传入的长度超过64字符,报错",$customAccountNoSigner0,"${generate_random_str(65)}","","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）"]
          - ["TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,$customOrgNoSigner3,"","","","",1,200,"成功"]
          - ["TC35-customAccountNo传入的是相对方个人的账号，userType限定是内部,报错",$customAccountNoSignerOpposit,$customOrgNoSignerOpposit,"","","","",1,1702432,"用户账号不存在"]
          - ["TC36-customAccountNo传入不支持的特殊字符,报错","${get_not_support_str()}",$customOrgNoSigner0,"","","","",1,1702432,"用户账号不存在（客户系统的唯一标识）"]
          - ["TC37-customAccountNo所属部门，customOrgNo和customDepartmentNo不匹配,报错",$customAccountNoSigner1,$customOrgNoSigner0,$departmentNo1,"","","",1,1702078,"当前用户信息和组织信息不匹配！"]
          - ["TC38-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,"","","","",1,200,"成功"]
          - ["TC39-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，对应的code都不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功"]
          - ["TC40-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo不传,报错",$customAccountNoSigner0,$customOrgNoSigner3,"","","","",1,200,"成功"]
          - ["TC41-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，成功",$customAccountNoSigner0,$customOrgNoSigner3,"${ENV(sign01.JZ.departNo)}","","","",1,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/initiatorCustomAccountNoOrgNoDepartmentNoTC.yml