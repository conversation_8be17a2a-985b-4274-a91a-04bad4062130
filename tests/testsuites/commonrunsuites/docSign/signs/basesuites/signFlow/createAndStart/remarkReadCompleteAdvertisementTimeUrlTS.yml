config:
  name: 测试一步发起-remark-readComplete-advertisement-signFlowExpireTime-redirectUrl-signNotifyUrl
#    variables:
#      desensitive: "true"

testcases:
  - name: 测试一步发起-remark-readComplete-advertisement-signFlowExpireTime-redirectUrl-signNotifyUrl
    parameters:
      - name-remark-readComplete-advertisement-signFlowExpireTime-redirectUrl-signNotifyUrl-code-message:
          - ["TC1-流程备注允许为空，强制阅读,签署需知关闭，签署有效期,跳转/通知地址为空","",false,false,"","","",200,"成功"]
          - ["TC2-流程备注允许，强制阅读，签署需知，签署有效期，跳转地址，通知地址都不传",NULL,NULL,NULL,NULL,NULL,NULL,200,"成功"]
          - ["TC3-流程备注允许首尾空格"," remark:备注 允许有空格 ",false,false,"","","",200,"成功"]
          - ["TC4-流程备注允许有特殊字符","${get_support_str(10)}",false,false,"","","",200,"成功"]
          - ["TC5-流程备注允许200字符","${generate_random_str(200)}",false,false,"","","",200,"成功"]
          - ["TC6-流程备注超度超过200，报错","${generate_random_str(201)}",false,false,"","","",1703001,"流程备注不可超过200个字符！"]
          - ["TC7-强制阅读为空，默认关闭","",NULL,false,"","","",200,"成功"]
          - ["TC8-强制阅读传入true,开启","",true,false,"","","",200,"成功"]
          - ["TC9-强制阅读其他值","","T",false,"","","",1703012,"readComplete字段类型不匹配"]
          - ["TC10-签署需知为空，默认关闭","",false,NULL,"","","",200,"成功"]
          - ["TC11-签署需知传入true,开启","",false,true,"","","",200,"成功"]
          - ["TC12-签署需知其他值","",false,"T","","","",1703012,"advertisement字段类型不匹配"]
          - ["TC13-签署有效期不传","",false,false,NULL,"","",200,"成功"]
          - ["TC14-签署有效期长度超过33","",false,false,"${generate_random_str(33)}","","",1703001,"签署有效期长度不可超出32位！"]
          - ["TC15-签署有效期为正确的值","",false,false,"2100-1-1 00:00:00","","",200,"成功"]
          - ["TC16-签署有效期为过去时间","",false,false,"2000-1-1 00:00:00","","",1702072,"签署截止日期不可早于当前日期！"]
          - ["TC17-签署有效期为无效时间","",false,false,"2022-2-30 00:00:00","","",1702061,"时间格式异常！"]
          - ["TC18-签署有效期年月日格式","",false,false,"2122年3月30日 00:00:00","","",1702061,"时间格式异常！"]
          - ["TC19-签署有效期分隔符非指定格式","",false,false,"2122/3/30 00:00:00","","",1702061,"时间格式异常！"]
          - ["TC20-签署有效期允许首尾空格","",false,false," 2100-1-1 00:00:00 ","","",200,"成功"]
          - ["TC21-跳转地址允许500以内字符","",false,false,"","${generate_random_str(500)}","",200,"成功"]
          - ["TC22-跳转地址长度超过500，报错","",false,false,"","${generate_random_str(501)}","",1703001,"自定义跳转地址长度不可超出500字符"]
          - ["TC23-跳转地址输入正确的值","",false,false,""," http://datafactory.smlk8s.esign.cn/simpleTools/notice/ ","",200,"成功"]
          - ["TC24-回调地址允许1000以内字符","",false,false,"","","${generate_random_str(1000)}",200,"成功"]
          - ["TC25-回调地址长度超过1000，报错","",false,false,"","","${generate_random_str(1001)}",1703001,"签署地址回调不超过1000个字符！"]
          - ["TC26-回调地址输入正确的值","",false,false,"","","http://192.168.22.90:777/test/testCallBack",200,"成功"]
          - ["TC27-流程备注不支持的特殊字符，允许","${get_not_support_str()}",false,false,"","${get_support_str(5)} ","${get_support_str(5)} ",200,"成功"]
          - ["TC28-跳转地址不支持的特殊字符，允许","${get_support_str(5)} ",false,false,"","${get_not_support_str()}","",200,"成功"]
          - ["TC29-回调地址不支持的特殊字符，允许","${get_support_str(5)} ",false,false,"","","${get_not_support_str()}",200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/remarkReadCompleteAdvertisementTimeUrlTC.yml