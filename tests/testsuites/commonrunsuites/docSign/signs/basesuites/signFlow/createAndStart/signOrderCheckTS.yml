config:
    name: 一步发起-->一个签署节点内包含多个签署人1、改动点增加signOrder支持节点内支持顺序签
    variables:
            #filekey
            fileKey01: "$${ENV(fileKey)}"
            #第二个 filekey
            fileKey02: "$${ENV(1PageFileKey)}"
            fileKey03: "$${ENV(3PageFileKey)}"
            # 多份文档
           # signFiles0: [ { "fileKey": "$fileKey01" },{ "fileKey": "$fileKey02" },{ "fileKey": "$fileKey03" } ]
            # 相对方账号
            outUserNo: "${ENV(wsignwb01.accountNo)}"
            # 内部账号
            innerUserNo: "${ENV(csqs.accountNo)}"
            innerUserNo2: "${ENV(sign01.accountNo)}"
            # 内部企业
            innerOrgNo: "${ENV(sign01.main.orgNo)}"
            innerOrgNo2: "${ENV(csqs.orgNo)}"
            # 相对方企业
            outOrgNo: "${ENV(wsignwb01.main.orgNo)}"
            #节点内签署模式 0 顺序签 1 无序签 2 或签
            signMode0: 0
            signMode1: 1
            signMode2: 2
            # 节点1顺序签节点内包含两个个签署人:[{签署人1:内部企业O1经办人P1签署F1},{签署人2:内部企业O1经办人P2签署F2}]
            signerInfo0: [
              { "signMode":$signMode0,"signNode": 1,"signOrder": 1,"autoSign": 1,"sealInfos": [ { "fileKey":"$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode":$signMode0, "signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
                    ]
            signerInfo1: [
              { "signMode":$signMode0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode":$signMode0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo2: [
              { "signMode":$signMode0,"signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode":$signMode0,"signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo3: [
              {"signMode":1,"signNode": 1,"signOrder": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              {"signMode":2,"signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo4: [
              { "signMode": 1,"signNode": 1,"signOrder": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 1,"signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo5: [
              { "signMode": 1,"signNode": 1,"signOrder": "","autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 1,"signNode": 1,"signOrder": "","autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo6: [
              { "signMode": 1,"signNode": 1,"signOrder": 3,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 1,"signNode": 1,"signOrder": 3,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo7: [
              { "signMode": 2,"signNode": 1,"signOrder": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 0,"signNode": 1,"signOrder": 3,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo8: [
              { "signMode": 2,"signNode": 1,"signOrder": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 2,"signNode": 1,"signOrder": 3,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo9: [
              { "signMode": 2,"signNode": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 2,"signNode": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            signerInfo10: [
              { "signMode": 2,"signNode": 1,"signOrder": 3,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 2,"signNode": 1,"signOrder": 3,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]
            #多节点，节点一两个签署人或签，节点二两个签署人顺序签，节点三两个签署人无序签
            signerInfo11: [
              { "signMode": 0,"signNode": 1,"signOrder": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
              { "signMode": 0,"signNode": 1,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
              { "signMode": 1,"signNode": 2,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
              { "signMode": 1,"signNode": 2,"signOrder": 2,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
              { "signMode": 2,"signNode": 3,"signOrder": 2,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
              { "signMode": 2,"signNode": 3,"signOrder": 2,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "300","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo","customOrgNo": "$innerOrgNo" },
            ]

testcases:
-
    name: PDF-一步发起静默签--TL-顺序签节点内两个签署人，signMode=0，验证signOrder
    parameters:
      - name-signerInfos-code-message:
          - ["TL-顺序签节点内多个签署人，signMode=0，signOrder传值",$signerInfo0,200,"成功"]
          - ["TL-顺序签节点内多个签署人，signMode=0，signOrder不传",$signerInfo1,1702584,"节点1内顺序签，签署顺序1不能重复"]
          - ["TL-顺序签节点内多个签署人，signMode=0，signOrder值重复",$signerInfo2,1702584,"不能重复"]

    testcase: testcases/signs/signFlow/createAndStart/signOrderCheck.yml

-
    name: PDF-一步发起静默签--TL-无序签节点内两个签署人，signMode=1，验证signOrder
    parameters:
      - name-signerInfos-code-message:
          - ["TL-同一节点不可出现不同签署模式",$signerInfo3,1702142,"同一节点不可出现不同签署模式！"]
          - ["TL-无序签节点内多个签署人，signMode=1,signOrder传值",$signerInfo4,200,"成功"]
          - ["TL-无序签节点内多个签署人，signMode=1，signOrder不传",$signerInfo5,200,"成功"]
          - ["TL-无序签节点内多个签署人，signMode=1，signOrder值重复",$signerInfo6,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/signOrderCheck.yml

-
    name: PDF-一步发起静默签--TL-或签节点内两个签署人，signMode=2，验证signOrder
    parameters:
      - name-signerInfos-code-message:
          - ["TL-同一节点不可出现不同签署模式",$signerInfo7,1702142,"同一节点不可出现不同签署模式！"]
          - ["TL-或签节点内多个签署人，signMode=2,signOrder传值",$signerInfo8,200,"成功"]
          - ["TL-或签节点内多个签署人，signMode=2，signOrder不传",$signerInfo9,200,"成功"]
          - ["TL-或签节点内多个签署人，signMode=2，signOrder值重复",$signerInfo10,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/signOrderCheck.yml

#-
#    name: TL-多节点，节点一两个签署人或签，节点二两个签署人顺序签，节点三两个签署人无序签
#    parameters:
#      - name-signerInfos-code-message:
#          - ["TL-多节点，节点一两个签署人或签，节点二两个签署人顺序签，节点三两个签署人无序签",$signerInfo7,200,"成功"]
#
#    testcase: testcases/signs/signFlow/createAndStart/signOrderCheck.yml