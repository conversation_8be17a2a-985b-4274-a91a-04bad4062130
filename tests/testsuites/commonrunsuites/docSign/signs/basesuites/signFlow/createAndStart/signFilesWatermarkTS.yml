config:
    name: 一步发起-校验-attachment-signFiles
    variables:
      fileKey0: $${ENV(fileKey)}

testcases:
-
    name: 一步发起-校验-signFiles新增参数
    parameters:
      - name-fileKey-qrFile-qrPosX-qrPosY-qrView-qrWidth-watermarkPage-watermarkPosition-code-message:
          - ["TC1-qrFile输入传入不支持的特殊字符",$fileKey0,'\/:*?"<>|',0,0,0,0,0,0,1703012,"qrFile字段类型不匹配"]
          - ["TC2-qrFile字段为-1",$fileKey0,'-1',0,0,0,0,0,0,1703001,"qrFile错误,只支持0否，1是;默认为0"]
          - ["TC3-qrFile字段为0",$fileKey0,0,0,0,1,96,1,1,200,"成功"]
          - ["TC4-qrFile字段为1",$fileKey0,1,0,0,1,96,3,1,200,"成功"]
          - ["TC5-qrFileInfos字段qrWidth_qrView均为空",$fileKey0,0,0,0,"","",1,1,200,"成功"]
          - ["TC6-qrWidth设置为非数字字符",$fileKey0,0,0,0,0,'\/:*?"<>|',0,0,1703012,"qrWidth字段类型不匹配"]
          - ["TC7-qrWidth设置为小于96整数",$fileKey0,1,0,0,1,9,3,1,1703001,"二维码宽度, 支持10～149（PX）之间整数"]
          - ["TC8-qrWidth设置为大于149整数",$fileKey0,1,0,0,1,1000,3,1,1703001,"二维码宽度, 支持10～149（PX）之间整数"]
          - ["TC9-qrWidth设置为负数",$fileKey0,1,0,0,1,-100,3,1,1703001,"二维码宽度, 支持10～149（PX）之间整数"]
          - ["TC10-qrWidth设置为小数",$fileKey0,1,0,0,1,100.5,3,1,1703012,"qrWidth字段类型不匹配"]
          - ["TC11-qrView输入传入不支持的特殊字符",$fileKey0,1,0,0,'\/:*?"<>|',90,3,1,1703012,"qrView字段类型不匹配"]
          - ["TC12-qrView输入传入2",$fileKey0,1,0,0,2,100,3,1,200,"成功"]
          - ["TC13-qrView输入传入0",$fileKey0,1,0,0,0,100,3,1,200,"成功"]
          - ["TC14-watermarkPage输入传入不支持的特殊字符",$fileKey0,1,0,0,0,96,'\/:*?"<>|',1,1703012,"watermarkPage字段类型不匹配"]
          - ["TC15-watermarkPage设置为非数字字符",$fileKey0,1,0,0,2,96,'test',1,1703012,"watermarkPage字段类型不匹配"]
          - ["TC16-watermarkPage设置为大于3整数",$fileKey0,1,0,0,0,96,4,1,1703001,"水印页数：1-首页 2-末页 3-所有页"]
          - ["TC17-watermarkPage设置为0",$fileKey0,1,0,0,0,96,0,1,1703001,"水印页数：1-首页 2-末页 3-所有页"]
          - ["TC18-watermarkPage设置为负数",$fileKey0,1,0,0,0,96,-100,1,1703001,"水印页数：1-首页 2-末页 3-所有页"]
          - ["TC19-watermarkPage设置为小数",$fileKey0,1,0,0,0,96,0.25,1,1703012,"watermarkPage字段类型不匹配	"]
          - ["TC20-watermarkPage设置为1",$fileKey0,1,0,0,0,96,1,1,200,"成功"]
          - ["TC21-watermarkPage设置为2",$fileKey0,1,0,0,0,96,2,1,200,"成功"]
          - ["TC22-watermarkPage设置为3",$fileKey0,1,0,0,0,96,3,1,200,"成功"]
          - ["TC23-watermarkPosition输入传入不支持的特殊字符",$fileKey0,1,0,0,0,96,1,'\/:*?"<>|',1703012,"watermarkPosition字段类型不匹配"]
          - ["TC24-watermarkPosition设置为非数字字符",$fileKey0,1,0,0,0,96,1,test,1703012,"watermarkPosition字段类型不匹配"]
          - ["TC25-watermarkPosition设置为大于4整数",$fileKey0,1,0,0,0,96,1,18,1703001,"水印位置仅为1，2，3，4"]
          - ["TC26-watermarkPosition设置为0",$fileKey0,1,0,0,0,96,1,0,1703001,"水印位置仅为1，2，3，4"]
          - ["TC27-watermarkPosition设置为负数",$fileKey0,1,0,0,0,96,1,-18,1703001,"水印位置仅为1，2，3，4"]
          - ["TC28-watermarkPosition设置为小数",$fileKey0,1,0,0,0,96,1,1.25,200,"成功"]
          - ["TC29-watermarkPosition设置为1",$fileKey0,1,0,0,0,96,1,1,200,"成功"]
          - ["TC30-watermarkPosition设置为2",$fileKey0,1,0,0,0,96,1,2,200,"成功"]
          - ["TC29-watermarkPosition设置为3",$fileKey0,1,0,0,0,96,1,3,200,"成功"]
          - ["TC30-watermarkPosition设置为4",$fileKey0,1,0,0,0,96,1,4,200,"成功"]
          - ["TC31-qrPosX输入传入不支持的特殊字符",$fileKey0,1,'\/:*?"<>|',0,0,96,1,1,1703012,"qrPosX字段类型不匹配"]
          - ["TC32-qrPosX设置为非数字字符",$fileKey0,1,test,0,0,96,1,test,1703012,"qrPosX字段类型不匹配"]
          - ["TC33-qrPosX设置为大于99999整数",$fileKey0,1,999999,0,0,96,1,1,1703001,"偏移量：支持0-99999之间整数"]
          - ["TC34-qrPosX设置为0",$fileKey0,1,0,0,0,96,1,1,200,"成功"]
          - ["TC35-qrPosX设置为负数",$fileKey0,1,-18,0,0,96,1,1,1703001,"偏移量：支持0-99999之间整数"]
          - ["TC36-qrPosX设置为小数",$fileKey0,1,0.28,0,0,96,1,1.1,1703012,"qrPosX字段类型不匹配	"]
          - ["TC37-qrPosX设置为99999",$fileKey0,1,99999,0,0,96,1,1,200,"成功"]
          - ["TC32-qrPosY设置为非数字字符",$fileKey0,1,0,test,0,96,1,test,1703012,"qrPosY字段类型不匹配"]
          - ["TC33-qrPosY设置为大于99999整数",$fileKey0,1,0,999999,0,96,1,1,1703001,"偏移量：支持0-99999之间整数"]
          - ["TC34-qrPosY设置为0",$fileKey0,1,0,0,0,96,1,1,200,"成功"]
          - ["TC35-qrPosY设置为负数",$fileKey0,1,0,-18,0,96,1,1,1703001,"偏移量：支持0-99999之间整数"]
          - ["TC36-qrPosY设置为小数",$fileKey0,1,0,0.28,0,96,1,1.1,1703012,"qrPosY字段类型不匹配	"]
          - ["TC37-qrPosY设置为99999",$fileKey0,1,0,99999,0,96,1,1,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/signFilesTC.yml

-
    name: 一步发起-校验-signFiles新增参数校验
    parameters:
        - name-fileKey-qrFile-qrPosX-qrPosY-qrView-qrWidth-watermarkPage-watermarkPosition-qrEffectiveTime-code-message:
            - ["TC1-qrPosY设置为小数",$fileKey0,1,0,20,2,96,1,1,1,1703012,"qrPosY字段类型不匹配"]
            - ["TC2-qrEffectiveTime输入传入不支持的特殊字符",$fileKey0,1,0,20,2,96,1,1,'\/:*?"<>|',1703012,"qrEffectiveTime字段类型不匹配"]
            - ["TC3-qrEffectiveTime输入传入0",$fileKey0,1,0,20,2,96,1,1,0,200,"成功"]
            - ["TC4-qrEffectiveTime输入传入-1",$fileKey0,1,0,20,2,96,1,1,-1,1703001,"二维码有效时间，可传入1-36500区间任意整数，单位为天，默认为30天"]
            - ["TC5-qrEffectiveTime输入传入小数",$fileKey0,1,0,20,2,96,1,1,2.5,1703012,"qrEffectiveTime字段类型不匹配"]
            - ["TC6-qrEffectiveTime输入传入36501",$fileKey0,1,0,20,2,96,1,1,365001,1703001,"二维码有效时间，可传入1-36500区间任意整数，单位为天，默认为30天"]
                #          - [ "TC4-qrEffectiveTime输入传入36501",1,1,36501,1703001,"二维码查看权限，0-全部 1-流程参与人 ，默认为1" ]
#          - [ "TC2-qrEffectiveTime输入传入不支持的特殊字符",1,1,'\/:*?"<>|',90,3,1,1703012,"qrEffectiveTime字段类型不匹配" ]
#          - [ "TC3-qrEffectiveTime输入传入0",1,1,0,1703001,"二维码查看权限，0-全部 1-流程参与人 ，默认为1" ]
#          - [ "TC4-qrEffectiveTime输入传入36501",1,1,36501,1703001,"二维码查看权限，0-全部 1-流程参与人 ，默认为1" ]
    testcase: testcases/signs/signFlow/createAndStart/signFilesTC.yml