config:
    name: 一步发起-govSignFlag场景

testcases:
-
    name: 一步发起-校验-govSignFlag
    parameters:
      - name-organizationCodeSigner-userCodeSigner-userTypeSigner-signatureType-autoSign-sealId-govSignFlag-code-message:
         - ["TC1-内部机构签署，govSignFlag传其他值","${ENV(csqs.orgCode)}","${ENV(csqs.userCode)}","1","COMMON-SEAL","false","","1212df",1703012,"govSignFlag字段类型不匹配"]
         - ["TC2-相对方机构签署，govSignFlag传其他值","${ENV(worg01.orgCode)}","${ENV(wsignwb01.userCode)}","2","COMMON-SEAL","false","","1212df",1703012,"govSignFlag字段类型不匹配"]
#         - ["TC3-个人相对方用户签署，使用政府签署标识","GRXDF","${ENV(oppositePersonUserCodeOuter5)}","2","COMMON-SEAL","false","","",1702406,"个人相对方不可作为组织编码！"]
#         - ["TC4-相对方机构使用政府签署，签署机构的统一社会信用代码/工商号是空的","${ENV(oppositeOrganizationIdOuter1)}","${ENV(oppositePersonUserCodeOuter1)}","2","COMMON-SEAL","false","","true",1702442,"请完善当前企业证件信息"]
#         - ["TC5-相对方机构使用政府签署，签署机构的身份证信息是空的","${ENV(oppositeOrganizationIdOuter2)}","${ENV(oppositePersonUserCodeOuter2)}","2","COMMON-SEAL","false","","true",1702440,"请完善当前签署人证件信息"]
#         - ["TC6-相对方机构使用政府签署，公有云不存在的组织","${ENV(oppositeOrganizationIdOuter3)}","${ENV(oppositePersonUserCodeOuter3)}","2","COMMON-SEAL","false","","true",1702441,"外部组织不存在或经办人非该组织的真实法人，请核对信息后，重新发起签署！"]
#         - ["TC7-相对方机构使用政府签署，经办人和天印本地法人一致，但和公有云不一致","${ENV(oppositeOrganizationIdOuter4)}","${ENV(oppositePersonUserCodeOuter4)}","2","COMMON-SEAL","false","","true",1702441,"外部组织不存在或经办人非该组织的真实法人，请核对信息后，重新发起签署！"]
#         - ["TC8-相对方机构使用政府印章静默签，提示","${ENV(worg01.orgCode)}","${ENV(wsignwb01.userCode)}","2","COMMON-SEAL","true","","true",1702183,"外部用户不可发起静默签！"]
#         - ["TC9-相对方机构不使用政府印章静默签，提示","${ENV(worg01.orgCode)}","${ENV(wsignwb01.userCode)}","2","COMMON-SEAL","true","","",1702183,"外部用户不可发起静默签！"]
#         - ["TC10-相对方机构使用政府印章签署，签署机构指定政府印章，发起时提示","${ENV(worg01.orgCode)}","${ENV(wsignwb01.userCode)}","2","COMMON-SEAL","false","7bd2f9d4-6e0c-449a-ad62-3d9607ef3fab","",1702218,"系统暂不支持外部用户/机构指定印章id！"]
#         - ["TC11-相对方机构不使用政府印章签署，签署机构指定政府印章，发起时提示","${ENV(worg01.orgCode)}","${ENV(wsignwb01.userCode)}","2","COMMON-SEAL","false","7bd2f9d4-6e0c-449a-ad62-3d9607ef3fab","",1702218,"系统暂不支持外部用户/机构指定印章id！"]
    testcase: testcases/signs/signFlow/createAndStart/govSignFlagAbnormalTC.yml

-
    name: 一步发起正常场景
    testcase: testcases/signs/signFlow/createAndStart/govSignFlagTC.yml

-
    name: 一步发起拒签场景
    testcase: testcases/signs/signFlow/createAndStart/govSignFlagRefuseSuccessTC.yml