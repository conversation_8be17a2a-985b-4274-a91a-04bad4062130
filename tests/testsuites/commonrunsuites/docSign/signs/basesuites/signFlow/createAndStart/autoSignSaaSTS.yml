config:
    name: 相对方企业静默签-->新增proxyCustomOrgNo和proxyOrganizationCode自动签代签署企业账号和编码参数
    variables:
        #filekey
        fileKey01: "$${ENV(fileKey)}"
        #第二个 filekey
        fileKey02: "$${ENV(1PageFileKey)}"
        fileKey03: "$${ENV(3PageFileKey)}"
        fileKey04: "$${ENV(ofdFileKey)}"
        # 单份PDF文档
        signFiles0: [{ "fileKey": "$fileKey01" }]
        # OFD单份文档
        signFiles1: [{ "fileKey": "$fileKey04" }]
        #多份文档
        signFiles2: [ { "fileKey": "$fileKey01" },{ "fileKey": "$fileKey02" },{ "fileKey": "$fileKey03" } ]
        # 相对方账号
        outUserNo: "${ENV(wsignwb01.accountNo)}"
        # 内部账号
        innerUserNo: "${ENV(csqs.accountNo)}"
        innerUserNo2: "${ENV(sign01.accountNo)}"
        proxyCustomOrgNo1: "${generate_random_str(301)}"
        proxyOrganizationCode1: "${generate_random_str(65)}"
        guomisealId: "${ENV(sign01.cloud.guomi.SealId)}"
        # 内部企业
        innerOrgNo: "${ENV(sign01.main.orgNo)}"
        innerOrgCode: "${ENV(sign01.main.orgCode)}"
        innerOrgNo2: "${ENV(csqs.orgNo)}"
        # 相对方企业
        outOrgNo: "${ENV(wsignwb01.main.orgNo)}"
        #内部企业sign01印章
        innersealId: "${ENV(org01.sealId)}"
        #外部企业公章
        outOrgsealId: "${ENV(wsignwb01.orgsealId)}"
        #外部企业公章
        outOrgsealIdht: "${ENV(wsignwb01.orgsealIdht)}"

        # 节点1相对方企业静默签署proxyCustomOrgNo和proxyOrganizationCode为空
        signerInfo0: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode":"", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]

        # 节点1内部企业静默签署proxyCustomOrgNo和proxyOrganizationCode为空
        signerInfo1: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode": "", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId":"$innersealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" }
        ]
        # 节点1相对方企业静默签署proxyCustomOrgNo超出长度校验
        signerInfo2: [
            { "proxyCustomOrgNo": "$proxyCustomOrgNo1","proxyOrganizationCode": "", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署proxyCustomOrgNo超出长度校验
        signerInfo3: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode": "$proxyOrganizationCode1", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署proxyOrganizationCode为不存在
        signerInfo4: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode": "123", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署proxyCustomOrgNo为相对方企业
        signerInfo5: [
            { "proxyCustomOrgNo": "$outOrgNo","proxyOrganizationCode": "", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，userType=相对方时，自动签代签署企业账号和ID不是同一个企业,PFD发起成功，静默签成功
        signerInfo6: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，userType=相对方时，自动签代签署企业账号和ID不是同一个企业,OFD发起成功，静默签成功
        signerInfo61: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey04","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，userType=相对方时，相对方企业未授权，发起成功，静默签失败
        signerInfo7: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId":"$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "wsignwb03","customOrgNo": "WORG-SIGN-03" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，autoSign开启，userType=相对方时，企业编码和企业账号均为空
        signerInfo8: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "","customOrgNo": "" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，userType=相对方时，代签署企业未实名未授权，发起成功，静默签成功
        signerInfo9: [
            { "proxyCustomOrgNo": "ORG-DOCS","proxyOrganizationCode": "", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，userType=相对方时，相对方企业未给代签署企业授权，已实名，发起成功，静默签失败
        signerInfo10: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "wsign03","customOrgNo": "WCI" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，autoSign开启，userType=相对方时，sealId为空
        signerInfo11: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1相对方企业静默签署:autoSign开启，autoSign开启，userType=相对方时，sealId错误
        signerInfo12: [
            { "proxyCustomOrgNo": "","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$innersealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
        ]
        # 节点1静默签署:一个相对方企业静默签，多个印章签署合同章骑缝签
        signerInfo62: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ,{ "posX": "200","posY": "200","pageNo": "1-3","sealId": "$outOrgsealIdht","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo" }
           ]
        # 节点1静默签署无序:混合内部个人+内部企业法人章骑缝签+相对方企业指定印章类型为公章和合同章第二页盖章均静默签，无序签
        signerInfo63: [
            { "signMode": 1,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey02","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"legalSignFlag": 0,"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "" },
            { "signMode": 1,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-3","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 1,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey03","signConfigs": [ { "posX": "200","posY": "200","pageNo": "2","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 0,"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo","sealTypeCode": "PUBLIC,CONTRACT", },
        ]
        # 节点1静默签署顺序:内相对方企业静默签相对方个人手动签，顺序签
        signerInfo64: [
            { "proxyCustomOrgNo": "$innerOrgNo2","proxyOrganizationCode": "$innerOrgCode", "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "2","sealId": "$outOrgsealId","signType": "COMMON-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 0,"userType": 2,"customAccountNo": "$outUserNo","customOrgNo": "$outOrgNo","sealTypeCode": "PUBLIC,CONTRACT", },
            { "signMode": 0,"signNode": 2,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "100","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] } ],"userType": 2,"customAccountNo": "$outUserNo"}
        ]
testcases:
-
    name: 一步发起-相对方企业静默签署参数校验
    parameters:
      - name-chargingType-signerInfos-code-message-data:
          - ["TC0-autoSign开启，userType=相对方时，自动签代签署企业账号和ID为空",1,$signerInfo0,1709111,"代理签署方企业编码和账号不能都为空",null]
          - ["TC1-autoSign开启，userType=相对方时，自动签代签署企业账号长度301超出校验",1,$signerInfo2,1600017,"代理签署人组织账号长度不可超出300字符！",null]
          - ["TC2-autoSign开启，userType=相对方时，自动签代签署企业ID长度65超出校验",1,$signerInfo3,1600017,"代理签署人组织编码长度不可超出64字符！",null]
          - ["TC3-autoSign开启，userType=相对方时，自动签代签署企业ID不存在",1,$signerInfo4,1709112,"代理签署方企业信息不存在",null]
          - ["TC4-autoSign开启，userType=相对方时，自动签代签署企业账号为相对方企业",1,$signerInfo5,1709112,"代理签署方企业信息不存在",null]
          - ["TC5-autoSign开启，userType=相对方时，自动签代签署企业账号和ID均填写，chargingType为2",2,$signerInfo6,1709110,"相对方企业自动签署不支持相对方计费",null]
          - ["TC6-autoSign开启，userType=相对方时，企业编码和企业账号均为空",1,$signerInfo8,1702179,"签署人用户编码不可为空！",null]
          - ["TC7-autoSign开启，autoSign开启，userType=相对方时，sealId为空",1,$signerInfo11,1702124,"静默签印章id不可为空！",null]
    testcase: testcases/signs/signFlow/createAndStart/autoSignSaaS.yml

-
    name: 一步发起相对方企业静默签成功
    parameters:
      - name-signFiles-signerInfos-code-message:
          - ["TC0-autoSign开启，userType=内部时，自动签代签署企业账号和ID为空",$signFiles0,$signerInfo1,200,"成功"]
          - ["TC1-autoSign开启，userType=相对方时，自动签代签署企业账号和ID不是同一个企业，PDF文档",$signFiles0,$signerInfo6,200,"成功"]
          - ["TC2-autoSign开启，userType=相对方时，自动签代签署企业账号，OFD文档",$signFiles1,$signerInfo61,200,"成功"]
          - ["TC3-autoSign开启，userType=相对方时，相对方企业未授权",$signFiles0,$signerInfo7,200,"成功"]
          - ["TC4-autoSign开启，userType=相对方时，代签署企业未实名未授权",$signFiles0,$signerInfo9,200,"成功"]
          - ["TC5-autoSign开启，userType=相对方时，相对方企业未给代签署企业授权，已实名",$signFiles0,$signerInfo10,200,"成功"]
          - ["TC6-autoSign开启，userType=相对方时，sealId错误",$signFiles0,$signerInfo12,200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/autoSignSaaSTC.yml

-
    name: 分布发起开启流程后-添加相对方企业签署方静默签署参数校验
    parameters:
      - name-signerInfos-code-message:
          - ["TC0-autoSign开启，userType=相对方时，自动签代签署企业账号和ID为空",$signerInfo0,1709111,"代理签署方企业编码和账号不能都为空"]
          - ["TC1-autoSign开启，userType=相对方时，自动签代签署企业账号长度301超出校验",$signerInfo2,1703001,"代理签署人组织账号长度不可超出300字符！"]
          - ["TC2-autoSign开启，userType=相对方时，自动签代签署企业ID长度65超出校验",$signerInfo3,1703001,"代理签署人组织编码长度不可超出64字符！"]
          - ["TC3-autoSign开启，userType=相对方时，自动签代签署企业ID不存在",$signerInfo4,1701053,"未查询到签署人组织信息"]
          - ["TC4-autoSign开启，userType=相对方时，自动签代签署企业账号为相对方企业",$signerInfo5,1709112,"代理签署方企业信息不存在"]
          - ["TC5-autoSign开启，userType=相对方时，企业编码和企业账号均为空",$signerInfo8,1701051,"签署人编码和签署人账号不能同时为空"]
          - ["TC6-autoSign开启，autoSign开启，userType=相对方时，sealId为空",$signerInfo11,1702124,"静默签印章id不可为空！"]
    testcase: testcases/signs/signFlow/create/autoSignCreateTC.yml

-
    name: 分布发起开启流程后-添加相对方企业签署方静默签署成功
    parameters:
        - name-signerInfos-code-message:
          - ["TC0-autoSign开启，userType=内部时，自动签代签署企业账号和ID为空",$signerInfo1,200,"成功"]
          - ["TC1-autoSign开启，userType=相对方时，自动签代签署企业账号和ID不是同一个企业，PDF文档",$signerInfo6,200,"成功"]
          - ["TC3-autoSign开启，userType=相对方时，相对方企业未授权",$signerInfo7,200,"成功"]
          - ["TC4-autoSign开启，userType=相对方时，代签署企业未实名未授权",$signerInfo9,200,"成功"]
          - ["TC5-autoSign开启，userType=相对方时，相对方企业未给代签署企业授权，已实名",$signerInfo10,200,"成功"]
          - ["TC6-autoSign开启，userType=相对方时，sealId错误",$signerInfo12,200,"成功"]
    testcase: testcases/signs/signFlow/create/autoSignCreateTC.yml

-
    name: 一步发起相对方企业静默签场景用例
    parameters:
      - name-signFiles-signerInfos-code-message:
          - ["一个相对方企业静默签，多个印章签署",$signFiles0,$signerInfo62,200,"成功"]
          - ["混合内部个人+内部企业+相对方企业均静默签，无序签",$signFiles2,$signerInfo63,200,"成功"]
          - ["相对方企业静默签相对方个人手动签，顺序签",$signFiles0,$signerInfo64,200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/autoSignSaaSTC.yml



