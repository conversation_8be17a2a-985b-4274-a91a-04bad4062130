config:
    name: 一步发起-校验-businessTypeCode-SignerInfos
    variables:
        b0: ${ENV(businessTypeCode)}
        fileKey0: $${ENV(fileKey)}
        # 4种签署方
        userCode0: ${ENV(sign01.userCode)}
        oppositeUserCode0: ${ENV(wsignwb01.userCode)}
        orgCode0: ${ENV(sign01.main.orgCode)}

        oppositeOrgCode0: ${ENV(worg01.orgCode)}
        #用户无个人证书
        userCode1: ${ENV(userCodeNoCert)}
        orgCode1: ${get_inneruser_userCode(sign05,mainOrganizationCode)}
        # 内部个人机构的印章相关信息
        sealId0: ${ENV(org01.sealId)}
        sealTypeCode0: ${ENV(orgSealTypeCode)}
        sealId2: ${ENV(orgSealId2)}
        sealTypeCode2: ${ENV(orgSealTypeCode2)}
#        sealTypeCodeDelete: ${getSealTypeCodeDelete()}
        userSealIdStop: ${ENV(userSealStop)}
        userSealIdDelete: ${ENV(userSealDelete)}
        userSealIdNotPublic: ${ENV(userSealNoPublic)}

#        签署人是内部个人
        signerInfos0: [{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":1,"userCode":"$userCode0"}]
#        签署人是相对方个人
        signerInfos1: [{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":2,"userCode":"$oppositeUserCode0"}]
#        签署人是内部机构
        signerInfos2: [{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":1,"userCode":"$userCode0", "organizationCode": "$orgCode0"}]
#        签署人是相对方机构
        signerInfos3: [{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":2,"userCode":"$oppositeUserCode0", "organizationCode": "$oppositeOrgCode0"}]
#        只支持个人，不支持机构盖章,不支持设置有效期,不支持手绘
        businessTypeCodeOnlyPerson0: $b0
#        只支持机构盖章，不支持个人盖章
        businessTypeCodeOnlyOrganize0: $b0
#        只支持内部，不支持相对方用印
        businessTypeCodeOnlyInner0: $b0
#        只支持相对方，不支持内部用印
        businessTypeCodeOnlyOuter0: $b0
        fileKey1page: $${ENV(1PageFileKey)}

testcases:
-
    name: 一步发起-校验-businessTypeCode-SignerInfos
    parameters:
      - name-businessTypeCode-signerInfos-code-message-data0:
          - ["TC1-业务类型传入空字符串，报错","",$signerInfos0,1702165,"流程业务类型不可为空！",10]
          - ["TC2-业务类型不传，报错",NULL,$signerInfos0,1703001,"请输入业务类型编码！",10]
#          - ["TC3-业务类型停用状态，报错","$b0",$signerInfos0,1702062,"当前业务类型已禁用！",10]
          - ["TC4-业务类型长度超过36，报错","${generate_random_str(37)}",$signerInfos0,1702166,"流程业务类型编码长度不可超出36字符！",10]
          - ["TC5-业务类型不存在，报错","XXXXX",$signerInfos0,1702341,"未知的业务类型！",10]
#          - ["TC6-业务类型范围：只允许个人，签署方添加机构，报错"," $businessTypeCodeOnlyPerson0 ",$signerInfos3,1702091,"当前流程不可添加机构章！",10]
#          - ["TC7-业务类型范围：只允许机构，签署方添加个人，报错","$businessTypeCodeOnlyOrganize0",$signerInfos0,1702090,"当前流程不可添加个人章！",10]
#          - ["TC8-业务类型范围：只允许内部，签署方添加相对方，报错"," $businessTypeCodeOnlyInner0 ",$signerInfos1,1702089,"当前流程不可添加相对方！",10]
#          - ["TC9-业务类型范围：只允许相对方，签署方添加内部，报错","${gen_businessTypeId_Scene2($businessTypeCodeOnlyOuterData0)}",$signerInfos2,1702088,"当前流程不可添加内部用户！",10]
          - ["TC10-业务类型范围：只允许个人，签署方添加内部个人","$businessTypeCodeOnlyPerson0 " ,$signerInfos0,200,"成功",null]
          - ["TC11-业务类型范围：只允许个人，签署方添加相对方个人"," $businessTypeCodeOnlyOuter0 " ,$signerInfos1,200,"成功",null]
          - ["TC12-业务类型范围：只允许机构，签署方添加内部机构","$businessTypeCodeOnlyOrganize0 ",$signerInfos2,200,"成功",null]
          - ["TC13-业务类型范围：只允许机构，签署方添加相对方机构"," $businessTypeCodeOnlyOrganize0 ",$signerInfos3,200,"成功",null]
          - ["TC14-签署方为个人，指定签署印章类型是机构，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
          - ["TC15-签署方为个人，指定签署印章类型是法人，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":" LEGAL-PERSON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
          - ["TC16-签署方为机构，只指定签署印章类型是个人，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0","organizationCode": "$orgCode0"}],1702518,"至少设置一个{企业或法人}签署区",10]
#          - ["TC17-业务类型范围：不允许设置手绘，入参开启强制手绘，报错","$businessTypeCodeOnlyPerson0",[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"handEnable": true,"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0"}],1702137,"当前业务类型不支持手绘！",10]
          - ["TC18-业务类型范围：不允许设置签署截止日期，入参设置了，数据库存储时时间不会落库","$businessTypeCodeOnlyPerson0",$signerInfos0,200,"成功",null]
          - ["TC19-签署方为相对方个人，静默签署，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"userType":2,"userCode":"$oppositeUserCode0","autoSign":true}],1702183,"外部用户不可发起静默签！",10]
          - ["TC20-签署方为相对方机构，静默签署，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","pageNo":"1","signType":"COMMON-SIGN","signatureType":" COMMON-SEAL"}]}],"signNode":1,"userType":2,"userCode":"$oppositeUserCode0","organizationCode":"$oppositeOrgCode0","autoSign":true}],1709111,"代理签署方企业编码和账号不能都为空",10]
          - ["TC21-静默签不指定位置，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":1,"userCode":"$userCode0","autoSign":true}],1702532,"发起失败：静默签必须指定签署区",10]
          - ["TC22-签署方为个人，指定机构签名域，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","isAddSignDate":0,"posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":2,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
          - ["TC23-签署方为个人，指定骑缝签署，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","isAddSignDate":0,"posY":"200","sealId":"","pageNo":"1","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"userType":1,"userCode":"$userCode0"}],1702055,"骑缝签 请至少保证印章落在2个页面以上！",10]
          - ["TC24-签署方为个人，单页签署并添加签署日期，静默签署成功",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","sealId":"${ENV(sign01.sealId)}","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC25-签署方为机构，只指定经办人签名域，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","sealId":"$sealId0","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702518,"至少设置一个{企业或法人}签署区",10]
          - ["TC26-签署方为机构，多页签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","sealId":"$sealId0","pageNo":"1-10","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC27-签署方为机构，骑缝签署不允许添加签署日期，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","sealId":"$sealId0","pageNo":"1-10","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702222,"骑缝签不可添加签署时间！",10]
          - ["TC28-签署方为机构，骑缝签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","posY":"200","sealId":"$sealId0","pageNo":"1-10","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC29-签署方为机构，关键字签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"sealId":"$sealId0","pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"100","offsetPosY":"100"},"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC30-签署方为机构，签署域指定法人，机构类型",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"100","offsetPosY":"100"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC31-签署方为机构，签署域指定经办人，法人，机构类型",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"},{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC32-签署方为机构，签署域只指定法人类型",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
#          - ["TC33-签署方为机构，签署域指定法人类型，legalSignFlag=false，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"},{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":false,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
#          - ["TC34-签署方为机构，签署域指定法人类型，legalSignFlag不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"},{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC35-签署方为个人，指定法人签名域，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","isAddSignDate":0,"posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]}],"signNode":2,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
          - ["TC36-签署方为个人，signatureType为非指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","isAddSignDate":0,"posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"T"}]}],"signNode":2,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
          - ["TC37-签署方为机构，signatureType为非指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"T"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702518,"至少设置一个{企业或法人}签署区",10]
          - ["TC38-签署方为个人，signatureType不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"200","isAddSignDate":0,"posY":"200","sealId":"","pageNo":"1"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":false,"userCode":"$userCode0"}],1702518,"在签署文件中至少设置一个{个人}签署区",10]
          - ["TC40-签署方为机构，signatureType为空，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":""}]}],"signNode":1,"userType":1,"autoSign":false,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702518,"在签署文件中至少设置一个{企业或法人}签署区",10]
          - ["TC41-签署方为机构，legalSignFlag非指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX":"50","offsetPosY":"50"}},{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN ","signatureType":"LEGAL-PERSON-SEAL"},{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":"T","userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"legalSignFlag字段类型不匹配",10]
          - ["TC42-签署方为机构，自由签带法人",$b0,[{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"userType":1,"autoSign":false,"legalSignFlag":true,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC43-签署方多个，signMode不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"userType":1,"userCode":"$userCode0"}],1702584,"节点1内顺序签，签署顺序1不能重复",10]
          - ["TC44-signMode非指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":-1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702189,"未知的签署模式！",10]
          - ["TC45-signMode非指定类型，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":"T","userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"signMode字段类型不匹配",10]
          - ["TC46-signMode不传，默认是顺序签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"signMode":0,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC47-signMode传空，默认是顺序签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":"","userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"signMode":"","userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC48-signNode不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":null,"signMode":0,"userType":1,"userCode":"$userCode0"}],1702248,"添加签署方，签署节点不可为空！",10]
          - ["TC49-signNode传空，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":"","signMode":0,"userType":1,"userCode":"$userCode0"}],1702248,"添加签署方，签署节点不可为空！",10]
          - ["TC50-signNode其他值非正整数，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":-1,"signMode":0,"userType":1,"userCode":"$userCode0"}],1702201,"签署方签署节点请在1-99999中选择！",10]
          - ["TC51-signNode其他类型的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":"T","signMode":0,"userType":1,"userCode":"$userCode0"}],1703012,"signNode字段类型不匹配",10]
          - ["TC52-signNode=99999，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":99999,"signMode":0,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC53-signNode范围超过99999，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1000000,"signMode":0,"userType":1,"userCode":"$userCode0"}],1702201,"签署方签署节点请在1-99999中选择！",10]
          - ["TC54-signNode顺序签署时传入相同顺序，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0"}],1702584,"节点1内顺序签，签署顺序1不能重复",10]
          - ["TC55-signNode无序签署时传入不同顺序，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"signMode":1,"userType":1,"userCode":"$userCode0"}],1702145,"非顺序签，请至少保证每个节点存在2个签署方！",10]
          - ["TC56-signNode或签时传入不同顺序，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"signMode":2,"userType":1,"userCode":"$userCode0"}],1702145,"非顺序签，请至少保证每个节点存在2个签署方！",10]
          - ["TC57-signNode相同签署模式不同，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0"}],1702142,"同一节点不可出现不同签署模式！",10]
          - ["TC58-无序签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":2,"signMode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"signMode":1,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC59-或签签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC60-顺序签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":10,"signMode":0,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC61-或签不允许静默签署，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"150","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"autoSign":true,"userType":1,"userCode":"$userCode0"}],1702185,"静默签不可指定或签！",10]
          - ["TC62-sealInfo指定fileKey为空，报错",$b0,[{"sealInfos":[{"fileKey":"","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702249,"指定签署区，文件必须为当前流程签署文件！",10]
          - ["TC63-sealInfo指定fileKey不存于signFiles，报错",$b0,[{"sealInfos":[{"fileKey":"${fileKey1page}","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702249,"指定签署区，文件必须为当前流程签署文件！",10]
          - ["TC64-sealInfo多节点fileKey重复，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]},{"fileKey":"$fileKey0","signConfigs":[{"posX":"100","posY":"100","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":2,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702191,"同一份文件指定签署区时请不要分开设置！",10]
          - ["TC65-sealId长度为65字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${generate_random_str(65)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702494,"sealId错误：印章",10]
          - ["TC66-sealId长度为63字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${generate_random_str(63)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702494,"sealId错误：印章",10]
          - ["TC67-sealId不属于签署人，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$sealId0"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702494,"sealId错误：印章",10]
          - ["TC68-签署方内部个人，sealId状态为未发布，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${ENV(userSealNoPublic)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702349,"当前印章未发布！",10]
          - ["TC69-签署方内部个人，指定手绘，指定sealId，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${ENV(sign01.sealId)}","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702200,"指定手绘签时，不可指定印章id！",10]
          - ["TC70-签署方内部个人，sealId状态为已删除，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$userSealIdDelete"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702494,"sealId错误：印章",10]
          - ["TC71-签署方内部个人，sealId状态为已停用，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$userSealIdStop"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702349,"当前印章未发布！",10]
          - ["TC72-签署方内部机构，机构签名域的sealId状态为已停用，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgSealStop)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702494,"sealId错误：印章{1743105265179590658}不存在",10]
          - ["TC73-签署方内部机构，机构签名域的sealId状态为已删除，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgSealDelete)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702633,"不存在",10]
          - ["TC74-签署方内部机构，机构签名域的sealId状态为未发布，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgSealNoPublic)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702494,"不存在",10]
          - ["TC75-签署方内部机构，机构签名域的sealId不属于签署人，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgSealNoPermission)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702634,"签署方测试签署一-esigntest自动化签署中心CI测试（sign01）非印章1743105313636384770用印人",10]
          - ["TC76-签署方内部机构，机构签名域的sealId指定，强制手绘，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"$sealId0","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702148,"签署区类型非个人时，不可使用手绘！",10]
          - ["TC77-签署方内部机构，经办人签名域的sealId状态为已停用，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$userSealIdStop","handEnable": False}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702349,"当前印章未发布！",10]
          - ["TC78-签署方内部机构，经办人签名域的sealId状态为已删除，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$userSealIdDelete","handEnable": False}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702494,"sealId错误：印章",10]
          - ["TC79-签署方内部机构，经办人签名域的sealId状态为未发布，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${ENV(userSealNoPublic)}","handEnable": False}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702349,"当前印章未发布！",10]
          - ["TC80-签署方内部机构，经办人签名域的sealId指定，强制手绘，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${ENV(sign01.sealId)}","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702200,"指定手绘签时，不可指定印章id！",10]
          - ["TC81-签署方内部机构，法人签名域的sealId状态为未发布，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL","sealId":"${ENV(userSealNoPublic)}","handEnable": False}]}],"legalSignFlag":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702633,"不存在",10]
          - ["TC82-签署方内部机构，法人签名域的sealId状态为已停用，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL","sealId":"$userSealIdStop","handEnable": False}]}],"legalSignFlag":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702633,"不存在",10]
          - ["TC83-签署方内部机构，法人签名域的sealId状态为已删除，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL","sealId":"$userSealIdDelete","handEnable": False}]}],"legalSignFlag":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702633,"不存在",10]
          - ["TC84-签署方内部机构，指定印章进行机构，经办人，法人签署,静默签署成功",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"$sealId0"},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL","sealId":"${ENV(org01.legal.sealId)}"},{"posX":"150","posY":"350","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"${ENV(sign01.sealId)}","handEnable": False}]}],"legalSignFlag":true,"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC85-签署方相对方个人，允许指定sealId,成功",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL","sealId":"$sealId0"}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0"}],200,"成功",null]
          - ["TC86-签署方相对方机构，允许指定sealId,成功",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"$sealId0"}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0","organizationCode":"$oppositeOrgCode0"}],200,"成功",null]
          - ["TC87-签署方内部机构，机构签名域的sealId状态为其他机构",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgCodeNoCertSealId)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702633,"不存在",10]
#          - ["TC88-签署方内部个人，自由签指定印章===自由签逻辑修改废弃",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"sealId":"${ENV(orgCodeNoCertSealId)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0"}],1702518,"至少设置一个{个人}签署区",10]
#          - ["TC89-签署方内部机构，自由签指定印章===自由签逻辑修改废弃",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"sealId":"${ENV(orgCodeNoCertSealId)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702518,"至少设置一个{企业或法人}签署区",10]
          - ["TC90-handEnable=true，强制手绘签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC91-handEnable其他非指定的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","handEnable": "T"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1703012,"handEnable字段类型不匹配",10]
          - ["TC92-handEnable为空，默认fasle",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","handEnable": ""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
#          - ["TC94-签署方经办人：handEnable开启，但业务类型关闭，报错","$businessTypeCodeOnlyOrganize0",[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"$userSealIdDelete","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702200,"指定手绘签时，不可指定印章id！",10]
          - ["TC95-signType为空",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC96-signType不传",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC97-signType非指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"T","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702172,"未知的签署方式！",10]
          - ["TC98-signType长度超过191字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"${generate_random_str(191)}","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702172,"未知的签署方式！",10]
          - ["TC99-签署方个人，自由签",$b0,[{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC100-签署方机构，自由签",$b0,[{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC101-多页签署，posX不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702053,"印章坐标 x y 轴均不可为空！",10]
          - ["TC102-多页签署，posY不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702053,"印章坐标 x y 轴均不可为空！",10]
          - ["TC103-骑缝签署，posY不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702199,"骑缝签时 y 轴不可为空！",10]
          - ["TC104-关键字签署，posX和posY不传，允许",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC105-多页签署，posX=0，posY=0，允许",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"0","posY":"0","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC106-posX负数如-100，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"-100","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702207,"指定坐标X轴不可低于0！",10]
          - ["TC107-posY负数如-100，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"-100","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702208,"指定坐标Y轴不可低于0！",10]
          - ["TC108-posX超过页面大小如100，允许",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"1000","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC109-posX非数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"T","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702205,"指定坐标X轴需为数字！",10]
          - ["TC110-posY非数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"T","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702206,"指定坐标Y轴需为数字！",10]
          - ["TC111-posX=99999.999，允许",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"9999.999","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC112-posY=99999.999，允许",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"9999.999","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC113-posX设置6位数100000，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"100000","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702231,"签署区指定坐标最大值请勿超出99999！",10]
          - ["TC114-posY设置6位数100000，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"100000","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702231,"签署区指定坐标最大值请勿超出99999！",10]
          - ["TC115-多页签署，pageNo不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC116-多页签署，pageNo为空，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo": null,"signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC117-多页签署，pageNo为非正整数-1，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"-1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702323,"页码不正确",10]
          - ["TC118-多页签署，pageNo为非有效数字0，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"0","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702211,"指定页码需为正整数！",10]
          - ["TC119-多页签署，pageNo为非有效页码99999，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"99999","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702251,"指定单页签时，页码不可超出最大页码！",10]
          - ["TC120-多页签署，pageNo为非数字如T，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"T","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702211,"指定页码需为正整数！",10]
          - ["TC120-多页签署，pageNo为1-1",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC121-骑缝签署，pageNo为1-1，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-1","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702055,"骑缝签 请至少保证印章落在2个页面以上！",10]
          - ["TC122-多页签署，pageNo允许长度128字符",$b0, [{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"${generate_random_str(128)}","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}] ,1702211,"指定页码需为正整数！",10]
          - ["TC123-多页签署，pageNo允许长度129字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"${generate_random_str(129)}","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702212,"指定页码长度不可超出128字符！",10]
          - ["TC124-多页签署，pageNo包含多种格式如：1-3,4,6-7",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3,4,6-7","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC125-多页签署，pageNo包含重复页码,如：1-3,2,5",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3,2,5","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702068,"页码设置不可重叠！",10]
          - ["TC126-多页签署，pageNo包含重复页码,如：1-2,2-5",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-2,2-5","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702068,"页码设置不可重叠！",10]
          - ["TC127-多页签署，pageNo起始页大于结束页，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"3-1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702067,"页码起始值不可大于截止值！",10]
          - ["TC128-单页签署，pageNo单页，非首页",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC129-width不传",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":null}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC130-width为空",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC131-width超过页面宽度如10000",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":"10000"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC132-width非数字",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":"T"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702254,"指定宽度必须为整数！",10]
          - ["TC133-width小于0",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":"-1"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702213,"指定签署区宽度不可低于0！",10]
          - ["TC134-width=0",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":"","width":"0"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702213,"指定签署区宽度不可低于0！",10]
          - ["TC135-关键字签署，keyword为空，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702244,"若为关键字签署，则关键字不可为空！",10]
          - ["TC136-关键字签署，keyword不传，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":null,"keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702244,"若为关键字签署，则关键字不可为空！",10]
          - ["TC137-关键字签署，keyword不存在，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"不存在的关键字XXXX","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702109,"未找到关键字",10]
          - ["TC138-关键字签署，keyword长度超过30字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"${generate_random_str(31)}","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702245,"若为关键字签署，则单个关键字长度不可超出30字符！",10]
          - ["TC139-关键字签署，keyword不支持特殊字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"${get_not_support_str()}","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702127,"关键字存在特殊字符！",10]
          - ["TC140-关键字签署，keyword支持中文，数字，英文定位20字符",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"测试：TEST（1）","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC141-关键字签署，keyword支持部分特殊字符定位",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"+{}'.,;[]=-`（）·！@","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC142-关键字签署，keyword支持繁体字定位",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"測試繁體","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC143-关键字签署，keyword支持生僻字定位",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"蕘垚犇骉镳䶮叕","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC144-关键字签署，keyword支持带空格字符定位，解析时自动去掉空格",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲 方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC145-关键字签署，offsetPosX传入非数字类型，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "T","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702209,"关键字X轴偏移量需为数字！",10]
          - ["TC146-关键字签署，offsetPosY传入非数字类型，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "","offsetPosY":"T"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702210,"关键字y轴偏移量需为数字！",10]
          - ["TC147-关键字签署，offsetPosX传入超过5位字符的数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "100000","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702237,"签署偏移量请在-99999 - 99999 中选择！",10]
          - ["TC148-关键字签署，offsetPosY传入超过5位字符的数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "","offsetPosY":"100000"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702237,"签署偏移量请在-99999 - 99999 中选择！",10]
          - ["TC149-关键字签署，offsetPosX传入超过5位字符的负字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "-100000","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702237,"签署偏移量请在-99999 - 99999 中选择！",10]
          - ["TC150-关键字签署，offsetPosY传入超过5位字符的负字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "","offsetPosY":"-100000"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702237,"签署偏移量请在-99999 - 99999 中选择！",10]
          - ["TC151-关键字签署，offsetPosX和offsetPosY=0.000",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1","offsetPosX": "0","offsetPosY":"0"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC152-关键字签署，keywordIndex非数字字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"T","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702317,"指定第几个关键字需为整数",10]
          - ["TC153-关键字签署，keywordIndex小于-1,报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"-2","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702153,"关键字指定出现的关键字不可低于-1 ！",10]
          - ["TC154-关键字签署，keywordIndex=-1,查询最后一个",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"-1","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC155-关键字签署，keywordIndex超过查到的个数，定位最后一个",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"9999","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC156-关键字签署，keywordIndex=0，查询全部",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"0","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC157-关键字签署，keywordIndex输入超过10位字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"10000000000","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702193,"指定第几个关键字长度不可超出10个字符！",10]
          - ["TC158-关键字签署，keywordIndex输入10位字符，查询最后一个",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"999999999","offsetPosX": "","offsetPosY":""},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC159-关键字签署，解析关键字个数10个，查询正确",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"乙方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"甲","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"测","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"试","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"Test","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"()","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"+","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"《","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC160-关键字签署，解析关键字个数大于10个，查询正确",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"乙方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"甲","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"方","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"测","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"试","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"Test","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"()","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"+","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"《","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"},{"keywordInfo":{"keyword":"----","keywordIndex":"1"},"pageNo":"1-20","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC161-骑缝签署，edgeScope不传，默认是全部",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":null,"posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC162-骑缝签署，edgeScope为空，默认是全部",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"","posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC163-骑缝签署，edgeScope=0，代表全部",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"0","posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC164-骑缝签署，edgeScope=1，仅奇数页",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"1","posX":"250","posY":"250","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC165-骑缝签署，edgeScope=2，仅偶数页",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"2","posX":"250","posY":"250","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC166-骑缝签署，edgeScope非指定的数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"-1","posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702040,"未知的骑缝范围！",10]
          - ["TC167-骑缝签署，edgeScope非数字的字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"T","posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"edgeScope字段类型不匹配",10]
          - ["TC168-骑缝签署，没有Y坐标，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702199,"骑缝签时 y 轴不可为空！",10]
          - ["TC169-骑缝签署，Y坐标小于0，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"-1","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702208,"指定坐标Y轴不可低于0！",10]
          - ["TC170-骑缝签署，个人不支持骑缝，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"1","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",10]
          - ["TC171-骑缝签署，不可同时设置页码和奇偶页，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"1","posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702066,"骑缝签 不可同时设置页码和奇偶页！",10]
          - ["TC172-骑缝签署，机构的经办人签署骑缝，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"posX":"250","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",10]
          - ["TC173-骑缝签署，机构的法人签署骑缝，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"posX":"250","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]}],"legalSignFlag":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",10]
          - ["TC174-骑缝签署，允许pageNo不传",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"","posX":"250","posY":"250","pageNo":null,"signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC175-骑缝签署，允许pageNo为空",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"","posX":"250","posY":"250","pageNo":"","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC176-添加签署日期，骑缝章不支持，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702222,"骑缝签不可添加签署时间！",10]
          - ["TC177-添加签署日期，addSignDate非指定的字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":"T","sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"addSignDate字段类型不匹配",10]
          - ["TC178-添加签署日期，addSignDate非指定的数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":"-1","sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"addSignDate字段类型不匹配",10]
          - ["TC179-添加签署日期，sealSignDateFormat非指定的字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":"T"},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1703012,"sealSignDateFormat字段类型不匹配",10]
          - ["TC180-添加签署日期，sealSignDateFormat非指定的数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":-1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702225,"若指定添加签署时间，请在1yyyy-MM-dd 2yyyy年MM月dd日 3yyyy/MM/dd选择时间格式！",10]
          - ["TC181-添加签署日期，posX非指定的字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"T","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702205,"指定坐标X轴需为数字！",10]
          - ["TC182-添加签署日期，posY非指定的字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"T","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702206,"指定坐标Y轴需为数字！",10]
          - ["TC183-添加签署日期，posX负数，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"-1","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702157,"时间坐标不可低于0！",10]
          - ["TC184-添加签署日期，posY负数，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"-1","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702157,"时间坐标不可低于0！",10]
          - ["TC185-添加签署日期，posX超过5位字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100000","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702236,"签署时间指定坐标最大值请勿超出99999！",10]
          - ["TC186-添加签署日期，posY超过5位字符，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100000","sealSignDateFormat":1},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702236,"签署时间指定坐标最大值请勿超出99999！",10]
          - ["TC187-添加签署日期，fontSize负数，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"-18","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702227,"签署时间字体大小请在6-72中选择！",10]
          - ["TC188-添加签署日期，fontSize=0，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"0","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702227,"签署时间字体大小请在6-72中选择！",10]
          - ["TC189-添加签署日期，fontSize小于6，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"5","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702227,"签署时间字体大小请在6-72中选择！",10]
          - ["TC189-添加签署日期，fontSize超过最大字体，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"73","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702227,"签署时间字体大小请在6-72中选择！",10]
          - ["TC190-添加签署日期，fontSize非数字，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"T","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702226,"签署时间字体大小请使用不低于0的整数！",10]
          - ["TC191-添加签署日期，fontSize非整数，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18.89","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702226,"签署时间字体大小请使用不低于0的整数！",10]
          - ["TC192-添加签署日期，fontSize最小字体=6",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"6","posX":"100","posY":"100","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC193-添加签署日期，fontSize最大字体=72",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"72","posX":"100","posY":"100","sealSignDateFormat":3},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC194-添加签署日期，不传具体坐标，默认签署日期定位",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC195-添加签署日期，只传posX，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100.45","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC196-添加签署日期，只传posY，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posY":"100.98","sealSignDateFormat":2},"posX":"250","posY":"250","pageNo":"1-99","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC197-添加签署日期，内部个人手绘添加签署日期",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":2},"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC198-添加签署日期，内部机构关键字添加签署日期",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":2},"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC199-内部机构签署，指定印章类型sealTypeCode",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","sealTypeCode":" ${ENV(orgSealTypeCode)} "}],200,"成功",null]
          - ["TC200-相对方机构签署，指定印章类型（已实名的账号）",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0","organizationCode":"$oppositeOrgCode0","sealTypeCode":"${get_constant(organizationCodeOuterSignerSealTypeCode)}"}],200,"成功",null]
          - ["TC201-内部机构签署，sealTypeCode指定不存在的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","sealTypeCode":"XXXXXX"}],1702230,"无指定印章类型权限！",10]
#          - ["TC202-内部机构签署，sealTypeCode指定已删除的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","sealTypeCode":"$sealTypeCodeDelete"}],1702230,"无指定印章类型权限！",10]
          - ["TC203-内部机构签署，sealTypeCode指定和sealId不匹配的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":"$sealId0"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","sealTypeCode":"$sealTypeCode2"}],1702233,"指定的印章类型和印章id不匹配！",10]
          - ["TC204-内部个人签署，sealTypeCode指定值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","handEnable": true}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","sealTypeCode":$sealTypeCode0}],1702252,"签署主体为个人时，不可指定印章类型！",10]
          - ["TC205-相对方机构签署，sealTypeCode指定不存在的值，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0","organizationCode":"$oppositeOrgCode0","sealTypeCode":"XXXX"}],1702230,"无指定印章类型权限！",10]
          - ["TC207-内部机构签署，sealTypeCode的值超过64位，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","sealTypeCode":"${generate_random_str(65)}"}],1702230,"无指定印章类型权限！",10]
          - ["TC208-相对方个人签署，不支持指定印章类型，报错",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","sealId":""}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0","sealTypeCode":"$oppositeOrgCode0"}],1702252,"签署主体为个人时，不可指定印章类型！",10]
          - ["TC209-多签名域指定不同的印章sealId",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"keywordInfo":{"keyword":"甲方","keywordIndex":"1"},"pageNo":"1-3","signType":"KEYWORD-SIGN","signatureType":"COMMON-SEAL","sealId":"$sealId0"},{"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL","sealId":"${ENV(orgSealId2)}"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC210-指定奇数页骑缝和偶数页骑缝",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"1","posY":"150","pageNo":"","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"edgeScope":"2","posY":"350","pageNo":"","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"edgeScope":"0","posY":"550","pageNo":"","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC211-相同机构不同经办人签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"edgeScope":"1","posY":"150","pageNo":"","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0","departmentCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posY":"350","pageNo":"1-10","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"${ENV(signjz01.userCode)}","organizationCode":"$orgCode0","departmentCode":"$orgCode0"}],200,"成功",null]
          - ["TC212-个人签和机构经办人签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"130","posY":"150","pageNo":"1-10","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"},{"posX":"130","posY":"350","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"},{"sealInfos":[{"fileKey":"$fileKey0","signConfigs":[{"posX":"230","posY":"250","pageNo":"1-10","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"$userCode0"}],200,"成功",null]
          - ["TC213-机构签署人，不指定signConfigs自由签署",$b0,[{"sealInfos":[{"fileKey":"$fileKey0"}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],200,"成功",null]
          - ["TC214-内部机构签署，指定的sealId没有授权给这个经办人，报错",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" ${ENV(orgSealNoPermission)} ","edgeScope":" 1","posY":"150","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":"$orgCode0"}],1702634,"用印人",10]
          - ["TC215-静默签，内部个人指定手绘，报错",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"handEnable":true ,"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],1702125,"静默签不支持手绘签！",10]
          - ["TC216-静默签，内部个人不指定sealId，签署默认印章",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":"" ,"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC217-静默签，内部个人sealId不传，签署默认印章",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":null ,"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":""}],200,"成功",null]
          - ["TC218-静默签，内部个人无可用证书， 发起成功签署才会报错",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" ${ENV(userCodeNoCertSealId)} " ,"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(userCodeNoCert)}","organizationCode":""}],200,"成功",null]
          - ["TC219-静默签，内部机构无可用证书， 发起成功签署才会报错",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" ${ENV(orgCodeNoCertSealId)} " ,"posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(userCodeNoCert)}","organizationCode":" ${ENV(orgCodeNoCert)} ","departmentCode":" ${ENV(orgCodeNoCert)} "}],200,"成功",null]
          - ["TC220-静默签，内部机构进行经办人，法人，机构印章签署",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" $sealId0 ","posX":"150","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"sealId":"","posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"},{"sealId":" ${ENV(org01.legal.sealId)} ","posX":"350","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]}],"legalSignFlag":true,"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":" $orgCode0 ","departmentCode":" $orgCode0 "}],200,"成功",null]
          - ["TC221-静默签，内部机构进行签署并添加签署日期",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"18","posX":"100","posY":"100","sealSignDateFormat":2},"sealId":" $sealId0 ","posX":"150","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"},{"addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"200","posY":"100","sealSignDateFormat":1},"sealId":"","posX":"250","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"},{"addSignDate":true,"sealSignDatePositionInfo":{"posX":"100","posY":"300","sealSignDateFormat":3},"sealId":" ${ENV(org01.legal.sealId)} ","posX":"350","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]}],"legalSignFlag":true,"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode0","organizationCode":" $orgCode0 ","departmentCode":" $orgCode0 "}],200,"成功",null]
          - ["TC222-签署方，个人相对方专用编码GRXDF不可作为机构,报错",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" $sealId0 ","posX":"150","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":2,"userCode":"$oppositeUserCode0","organizationCode":" GRXDF ","departmentCode":" "}],1702406,"个人相对方不可作为组织编码！",10]
          - ["TC223-经办人签署企业印章+法定代表人印章，要求经办人个人没有证书且经办人和法人不一致，企业有企业证书，法定代表人有个人证书",$b0,[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" $sealId0 ","posX":"150","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"},{"sealId":" ${ENV(org01.legal.sealId)} ","posX":"350","posY":"250","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]}],"legalSignFlag":true,"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"$userCode1","organizationCode":" $orgCode0 ","departmentCode":" $orgCode1 "}],200,"成功",null]

    testcase: testcases/signs/signFlow/createAndStart/businessTypeCodeSignerInfosTC.yml