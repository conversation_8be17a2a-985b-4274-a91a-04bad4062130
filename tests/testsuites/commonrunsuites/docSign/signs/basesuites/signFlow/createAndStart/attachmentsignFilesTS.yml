config:
    name: 一步发起-校验-attachment-signFiles
    variables:
      fileKey0: $${ENV(fileKey)}
      fileKey1: $${ENV(2PageFileKey)}
      fileKey3: $${ENV(1PageFileKey)}
      fileKey2: $${ENV(3PageFileKey)}
      fileKey2page: $${ENV(2PageFileKey)}
      fileKey3page: $${ENV(3PageFileKey)}
      attachments0: []
      signFiles0: [{"fileKey":" $fileKey0 ","fileOrder":1}]
      signerInfos0: [{"signNode":1,"sealInfos":[{"fileKey":"$fileKey0"}],"userType":1,"userCode":"${ENV(sign01.userCode)}"}]
      fileKeyPng: $${ENV(pngPageFileKey)}
      fileKeyJpg: $${ENV(jpegFileKey)}
      fileKeyDocx: $${ENV(docxPageFileKey)}
      fileKeyOfd: $${ENV(ofdFileKey)}
      fileKeyZip: $${ENV(zipFileKey)}
      fileKeyDoc: $${ENV(docPageFileKey)}
      fileKeyXlsx: $${ENV(xlsxFileKey)}

testcases:
-
    name: 一步发起-校验-attachment-signFiles
    parameters:
      - name-attachments-signFiles-signerInfos-code-message:
          - ["TC1-附件fileKey为空字符串，报错",[{"fileKey":" ","fileOrder":1}],$signFiles0,$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC2-附件fileKey为不存在的字符串，报错",[{"fileKey":"XXXXX","fileOrder":1}],$signFiles0,$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC3-附件fileKey长度超过64，报错",[{"fileKey":"${generate_random_str(65)}","fileOrder":1}],$signFiles0,$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC4-附件fileKey重复，报错",[{"fileKey":"$fileKey0","fileOrder":1},{"fileKey":"$fileKey0","fileOrder":2}],$signFiles0,$signerInfos0,1702187,"流程签署附件不可重复！"]
          - ["TC5-附件fileKey和签署文件相同",$signFiles0,$signFiles0,$signerInfos0,200,"成功"]
          - ["TC6-附件fileKey支持zip",[{"fileKey":" ${fileKeyZip} ","fileOrder":1}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC7-附件fileKey支持ofd",[{"fileKey":" ${fileKeyOfd} ","fileOrder":1}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC8-附件fileKey为不支持类型，报错",[{"fileKey":" ${fileKeyXlsx} ","fileOrder":1}],$signFiles0,$signerInfos0,1702151,"系统不支持当前文件格式！"]
          - ["TC11-签署文档fileKey为空字符串，报错",$attachments0,[{"fileKey":" ","fileOrder":1}],$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC12-签署文档fileKey为不存在的字符串，报错",$attachments0,[{"fileKey":"XXXXX","fileOrder":1}],$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC13-签署文档fileKey长度超过64，报错",$signFiles0,[{"fileKey":"${generate_random_str(65)}","fileOrder":1}],$signerInfos0,1702047,"未知的文件信息！"]
          - ["TC14-签署文档fileKey重复，报错",$signFiles0,[{"fileKey":"$fileKey0","fileOrder":1},{"fileKey":"$fileKey0","fileOrder":2}],$signerInfos0,1702117,"流程签署文件不可重复！"]
          - ["TC15-签署文档fileKey和签署文件相同",$signFiles0,$signFiles0,$signerInfos0,200,"成功"]
          - ["TC16-签署文档fileKey不支持zip",$signFiles0,[{"fileKey":" ${fileKeyZip} ","fileOrder":1}],$signerInfos0,1702151,"系统不支持当前文件格式！"]

          - ["TC20-附件fileOrder长度为6字符,报错",[{"fileKey":" $fileKey0 ","fileOrder":100000}],$signFiles0,$signerInfos0,1702159,"文件排序长度不可超出5字符！"]
          - ["TC21-附件fileOrder小于0，报错",[{"fileKey":" $fileKey0 ","fileOrder":-1}],$signFiles0,$signerInfos0,1702188,"文件顺序请用整数！"]
          - ["TC22-附件fileOrder非正整数，报错",[{"fileKey":" $fileKey0 ","fileOrder":1.23}],$signFiles0,$signerInfos0,1702188,"文件顺序请用整数！"]
          - ["TC23-签署文档fileOrder长度为6字符,报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":11111111}],$signerInfos0,1702159,"文件排序长度不可超出5字符！"]
          - ["TC24-签署文档fileOrder小于0，报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":-1}],$signerInfos0,1702188,"文件顺序请用整数！"]
          - ["TC25-签署文档fileOrder非正整数，报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1.23}],$signerInfos0,1702188,"文件顺序请用整数！"]
          - ["TC26-附件fileOrder允许重复",[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey":" ${fileKeyOfd} ","fileOrder":2}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC27-附件fileOrder允许不连续",[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey":" ${fileKeyOfd} ","fileOrder":10}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC28-签署文档fileOrder允许重复",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey":" $fileKey1 ","fileOrder":2}],$signerInfos0,200,"成功"]
          - ["TC29-签署文档fileOrder允许不连续",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey": "$fileKey1","fileOrder":10}],$signerInfos0,200,"成功"]
          - ["TC31-3份签署文档1个签署方，1份指定签署部分2份不指定的属于自由签",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],200,"成功"]
          - ["TC32-3份签署文档1个签署方，1份指定签署部分2份指定自由签署",$signFiles0,[{"fileKey":" $fileKey0 "},{"fileKey":" $fileKey1 "},{"fileKey":" $fileKey2 "}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey1 "},{"fileKey":" $fileKey2 "}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],200,"成功"]
          - ["TC33-3份签署文档多个内部机构签署，1份指定签署1份自由签1份只读",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"sealInfos":[{"fileKey":" $fileKey1 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":2,"signMode":0,"userType":1,"userCode":"${ENV(csqs.userCode)}","organizationCode":"${ENV(csqs.orgCode)}"}],200,"成功"]
          - ["TC34-3份签署文档多个签署方，1个签署方对A文档指定签署，1个签署方对B文档指定签署C自由签署,报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"sealInfos":[{"fileKey":" $fileKey1 ","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"},{"fileKey":" $fileKey2 "}]}],"signNode":2,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}"}],1702176,"若指定签署区，则印章类型不可为空！"]
          - ["TC35-单签署文档多个签署方，1个签署方自由签1个签署方指定签，报错",$signFiles0,[{"fileKey":" $fileKey0 "}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":1,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"sealInfos":[{"fileKey":" $fileKey0 "}],"signNode":1,"signMode":1,"userType":1,"userCode":"${ENV(sign01.userCode)}"}],1702527,"指定签署区"]
          - ["TC36-3份签署文档多个签署方,1个签署方指定3份，1个签署方自由签署3份文档，报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey1 ","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey2 ","signConfigs":[{"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"signNode":2,"signMode":0,"userType":1,"userCode":"${ENV(csqs.userCode)}","organizationCode":"${ENV(csqs.orgCode)}"}],1702519,"在签署文件中没有签署区"]
          - ["TC36-3份签署文档多个签署方,1个签署方指定freeMode=0的3份，1个签署方自由签署3份文档，报错",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"freeMode": 0,"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey1 ","signConfigs":[{"freeMode": 0,"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey2 ","signConfigs":[{"freeMode": 0,"posX":"150","posY":"150","pageNo":"1-3","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"signNode":2,"signMode":0,"userType":1,"userCode":"${ENV(csqs.userCode)}","organizationCode":"${ENV(csqs.orgCode)}"}],1702519,"在签署文件中没有签署区"]
          - ["TC36-3份签署文档2个签署方,1个签署方指定freeMode=1的3份，1个签署方自由签署3份文档，成功",$signFiles0,[{"fileKey":" $fileKey0 ","fileOrder":1},{"fileKey":" $fileKey1 ","fileOrder":1},{"fileKey":" $fileKey2 ","fileOrder":1}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"freeMode": 1,"posY":"150","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey1 ","signConfigs":[{"freeMode": 1,"signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey2 ","signConfigs":[{"freeMode": 1,"signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"},{"signNode":2,"signMode":0,"userType":1,"userCode":"${ENV(csqs.userCode)}","organizationCode":"${ENV(csqs.orgCode)}"}],200,"成功"]
          - ["TC37-文档：1页文档奇数签署，报错",[],[{"fileKey":" $fileKey3 "}],[{"sealInfos":[{"fileKey":" $fileKey3 ","signConfigs":[{"edgeScope":" 1","posY":"150","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702055,"骑缝签 请至少保证印章落在2个页面以上！"]
          - ["TC38-文档：2页文档奇数签署，报错",$signFiles0,[{"fileKey":" $fileKey2page "}],[{"sealInfos":[{"fileKey":" $fileKey2page ","signConfigs":[{"edgeScope":" 1","posY":"150","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702055,"骑缝签 请至少保证印章落在2个页面以上！"]
          - ["TC39-文档：3页文档偶数签署，报错",$signFiles0,[{"fileKey":" $fileKey3page "}],[{"sealInfos":[{"fileKey":" $fileKey3page ","signConfigs":[{"edgeScope":" 2","posY":"150","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702055,"骑缝签 请至少保证印章落在2个页面以上！"]
#          - ["TC40-多签署文档机构签署方，部分文档未指定机构签名域",$signFiles0,[{"fileKey":" $fileKey0 "},{"fileKey":" $fileKey1 "}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"edgeScope":" 1","posY":"150","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":" $fileKey1 ","signConfigs":[{"posNo":"1-3","posX":"150","posY":"150","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702518,"至少设置一个{企业或法人}签署区"]
          - ["TC41-静默签，多签署文档，部分指定，报错",$signFiles0,[{"fileKey":" $fileKey0 "},{"fileKey":" $fileKey1 "}],[{"sealInfos":[{"fileKey":" $fileKey0 ","signConfigs":[{"sealId":" ${ENV(org01.sealId)} ","posX":"150","posY":"250","pageNo":"1-3","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"legalSignFlag":true,"autoSign":true,"signNode":1,"signMode":0,"userType":1,"userCode":"${ENV(sign01.userCode)}","organizationCode":" ${ENV(sign01.main.orgCode)} ","departmentCode":" ${ENV(sign01.main.orgCode)} "}],1702532,"发起失败：静默签必须指定签署区"]
          - ["TC42-附件fileKey支持doc",[{"fileKey":" ${fileKeyDoc} ","fileOrder":1}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC43-附件fileKey支持docx",[{"fileKey":" ${fileKeyDocx} ","fileOrder":2}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC44-附件fileKey支持jpg",[{"fileKey":" ${fileKeyJpg} ","fileOrder":10}],$signFiles0,$signerInfos0,200,"成功"]
          - ["TC45-附件fileKey支持png",[{"fileKey":" ${fileKeyPng} ","fileOrder":1}],$signFiles0,$signerInfos0,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/attachmentsignFilesTC.yml