config:
    name: 一步发起-主要验证多文档签署和只读-企业的3个签署区展示 （非常重要！！！ 一定要注意detail的校验）

testcases:
  一步发起-验证自由签的数据:
       testcase: testcases/signs/signFlow/createAndStart/freeSignTC01.yml

  一步发起-支持法人章作为主体章签署:
     testcase: testcases/signs/signFlow/createAndStart/legalPersonSealTC.yml

  一步发起-优化自由签逻辑-手动结束的流程的只读逻辑:
     testcase: testcases/signs/signFlow/createAndStart/finishModeSignSceneTC.yml

  一步发起-自由签支持普通和骑缝-V6.0.10.0-beta.2:
    testcase: testcases/signs/signFlow/createAndStart/freeEdgeSignSceneTC.yml

  一步发起-batchSignDefault校验-V6.0.10.0-beta.2:
    testcase: testcases/signs/signFlow/createAndStart/batchSignDefaultTC.yml

  批量转发场景-V6.0.11.0-beta.1:
    testcase: testcases/signs/portal/bulkTransfer/bulkTransferTC1.yml

  签署pdf支持38540印章结构体-V6.0.11.0-beta.2:
    testcase: testcases/signs/signFlow/createAndStart/pdfWith38540SealTC.yml

  手动开启进入签署区设置页，允许设置备注签署区-V6.0.12.0-beta.1:
    testcase: testcases/signs/signFlow/createAndStart/startModeRemarkSignSceneTC.yml

  一步发起-个人经办人法人章支持骑缝签署(静默)-V6.0.12.0-beta.4:
    testcase: testcases/signs/signFlow/createAndStart/personSealEdgeSignSceneTC.yml

  一步发起-个人经办人法人章支持骑缝签署-V6.0.12.0-beta.4:
    testcase: testcases/signs/signFlow/createAndStart/edgeSignforUserTC.yml

  一步发起-多节点流程排序-V6.0.12.0-beta.5:
    testcase: testcases/signs/signFlow/createAndStart/createSignFlowNodeSceneTC.yml