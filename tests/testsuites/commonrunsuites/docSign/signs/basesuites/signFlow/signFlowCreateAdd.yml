config:
  name: 创建流程和添加流程文件

testcases:
  - name: 创建流程和添加流程文件用例case
    testcase: testcases/signs/signFlow/flowStart/signCreateAdd.yml

  - name: 创建流程和删除流程文件用例case
    testcase: testcases/signs/signFlow/flowStart/signCreateFileDelete.yml

  - name: 创建流程和添加签署方用例case
    testcase: testcases/signs/signFlow/flowStart/signCreateSignersAdd.yml

  - name: 创建流程和添加签署方删除签署方用例case
    testcase: testcases/signs/signFlow/flowStart/signCreateSignersDelete.yml

  - name: 创建流程和添加签署方删除签署方个人用例case
    testcase: testcases/signs/signFlow/flowStart/signCreateSignersDelete1.yml

  - name: 创建流程和添加文件开启流程
    testcase: testcases/signs/signFlow/flowStart/signFlowStart.yml

  - name: 创建流程和添加签署方开启流程
    testcase: testcases/signs/signFlow/flowStart/signFlowStart1.yml

  - name: 创建流程和添加签署方开启流程结束流程
    testcase: testcases/signs/signFlow/flowStart/signFlowFinish.yml

  - name: 创建流程和添加签署方静默签开启流程结束流程
    testcase: testcases/signs/signFlow/flowStart/signFlowFinish1.yml

  - name: 机构支持经办人备注签用例
    testcase: testcases/signs/signFlow/signAdd/signCreateSignersAddRemarkSignConfig.yml

