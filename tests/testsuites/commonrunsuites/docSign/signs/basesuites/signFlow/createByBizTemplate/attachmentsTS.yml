config:
    name: createByBizTemplate-使用业务模板发起签署-校验抄送人信息
    variables:
      fileKey0: $${ENV(fileKey)}
      fileKey1: $${ENV(1PageFileKey)}
      fileKeyPng: $${ENV(pngPageFileKey)}
      fileKeyJpg: $${ENV(fileKeyJpg)}
      fileKeyDocx: $${ENV(docxPageFileKey)}
      fileKeyOfd: $${ENV(ofdFileKey)}
      fileKeyZip: $${ENV(zipFileKey)}
      fileKeyDoc: $${ENV(docPageFileKey)}
      fileKeyXlsx: $${ENV(xlsxFileKey)}
#      businessTypeCodeCBBT: "${getBusinessTypeId(自动化-openapi模板发起-指定双方无内容域,1,0)}"
      businessTypeCodeCBBT: "${getBusinessTypeId2(2)}"

testcases:
-
    name: createByBizTemplate校验attachments入参字段
    parameters:
      - name-attachments0-code-message-data:
          - ["TC1-附件fileKey为空字符串，报错",[{"fileKey":" ","fileOrder":1}],1612212,"发起失败：缺少参数：文件标识",""]
          - ["TC2-附件fileKey为不存在的字符串，报错",[{"fileKey":"XXXXX","fileOrder":1}],1612212,"发起失败：文件获取失败:XXXXX",""]
          - ["TC3-附件fileKey长度超过64，报错",[{"fileKey":"${generate_random_str(65)}","fileOrder":1}],1612212,"发起失败：文件获取失败:",""]
          - ["TC4-附件fileKey重复，报错",[{"fileKey":"$fileKey0","fileOrder":1},{"fileKey":"$fileKey0","fileOrder":2}],1612212,"发起失败：附件的fileKey重复",""]
          - ["TC5-附件fileKey支持zip",[{"fileKey":" ${fileKeyZip} ","fileOrder":1}],200,"成功",null]
          - ["TC6-附件fileKey支持ofd",[{"fileKey":" ${fileKeyOfd} ","fileOrder":1}],200,"成功",null]
          - ["TC7-附件fileKey为不支持类型，报错",[{"fileKey":" ${fileKeyXlsx} ","fileOrder":1}],1612212,"发起失败：上传的附件格式不支持",""]
          - ["TC8-附件fileOrder长度为6字符,报错",[{"fileKey":" $fileKey0 ","fileOrder":100000}],1600017,"附件顺序的值只支持0-5位整数",""]
          - ["TC9-附件fileOrder小于0，报错",[{"fileKey":" $fileKey0 ","fileOrder":-1}],1600017,"附件顺序的值只支持0-5位整数",""]
          - ["TC10-附件fileOrder非正整数，报错",[{"fileKey":" $fileKey0 ","fileOrder":1.23}],1600015,"fileOrder参数错误!",""]
          - ["TC11-附件fileOrder允许重复",[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey":" $fileKey1 ","fileOrder":2}],200,"成功",null]
          - ["TC12-附件fileOrder允许不连续",[{"fileKey":" $fileKey0 ","fileOrder":2},{"fileKey":" $fileKey1 ","fileOrder":10}],200,"成功",null]
          - ["TC13-附件fileKey支持doc",[{"fileKey":" ${fileKeyDoc} ","fileOrder":1}],200,"成功",null]
          - ["TC14-附件fileKey支持docx",[{"fileKey":" ${fileKeyDocx} ","fileOrder":2}],200,"成功",null]
          - ["TC15-附件fileKey支持jpg",[{"fileKey":" ${fileKeyJpg} ","fileOrder":10}],200,"成功",null]
          - ["TC16-附件fileKey支持png",[{"fileKey":" ${fileKeyPng} ","fileOrder":1}],200,"成功",null]
          - ["TC17-附件空数组能成功",[],200,"成功",""]
          - ["TC18-附件空对象，报错",[{}],1612212,"发起失败：缺少参数：文件标识",""]

    testcase: testcases/signs/signFlow/createByBizTemplate/attachmentsTC.yml