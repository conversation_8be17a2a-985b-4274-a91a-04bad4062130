config:
    name: createByBizTemplate-使用业务模板发起签署-校验指定签署方信息-不能用通用的businessTypeCode
    variables:
      userCode0: ${ENV(sign01.userCode)}

testcases:
-
    name: createByBizTemplate-使用业务模板发起签署-校验指定签署方的Code信息
    testcase: testcases/signs/signFlow/createByBizTemplate/signerInfosCodeTC.yml

-
    name: createByBizTemplate-使用业务模板发起签署-校验签署方指定印章类型
    testcase: testcases/signs/signFlow/createByBizTemplate/signerInfosSealTypeCodeTC.yml