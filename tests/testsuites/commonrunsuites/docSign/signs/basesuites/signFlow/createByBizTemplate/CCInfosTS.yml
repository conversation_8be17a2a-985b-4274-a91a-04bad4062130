config:
    name: createByBizTemplate-使用业务模板发起签署-校验抄送人信息
    variables:
      userCode0: ${ENV(sign01.userCode)}
      userCodeOpposit: ${ENV(wsignwb01.userCode)}
      userCodeInit: ${ENV(csqs.userCode)}
      orgCodeInit: ${ENV(csqs.orgCode)}
      orgCode0: ${ENV(sign01.main.orgCode)}
      orgCodeOpposit: ${ENV(worg01.orgCode)}
      departmentCode0: ${ENV(sign01.main.orgCode)}
      customAccountNoSigner0: ${ENV(sign01.accountNo)}
      #内部用户的主责是部门
      customAccountNoSigner1: ${ENV(sign03.accountNo)}
      departmentNo1: ${ENV(sign03.main.departNo)}
      orgParentNoToDepartment1: ${ENV(sign03.main.departNo.orgNo)}
      #内部用户的兼职是部门
      customAccountNoSigner2: ${ENV(sign01.accountNo)}
      customAccountNoSigner3: ${ENV(sign03.accountNo)}
      departmentNo2: ${ENV(sign01.JZ.departNo)}
      orgParentNoToDepartment2: ${ENV(sign01.JZ.depart.orgNo)}
      customDepartmentNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner0: ${ENV(sign01.main.orgNo)}
      customOrgNoSigner3: ${get_constant(userCodeToDepartmentOrgNo)}
      customDeptNoSigner3: ${get_constant(userCodeToDepartmentDepNo)}
      customAccountNoSignerOpposit: ${ENV(wsignwb01.accountNo)}
      customDepartmentNoSignerOpposit: ${ENV(worg01.orgNo)}
      customOrgNoSignerOpposit: ${ENV(worg01.orgNo)}
      sp: " "
      customAccountNoDimission: ${getUserInfoByDB(2,account_number, 1)}
      orgNoDimission: ${getUserInfoByDB(9,$customAccountNoDimission, 1)}
      customAccountNoDelete: ${getUserInfoByDB(6,account_number, 1)}
      orgNoDelete: ${getUserInfoByDB(9,$customAccountNoDelete, 1)}
      customOrgNoDelete: ${getOrgInfoByDB(6,account_number, 1, 1)}
      customDepartmentNo1: ${ENV(worg01.orgNo)}
#      businessTypeCodeCBBT: "${getBusinessTypeId(自动化-openapi模板发起-指定双方无内容域,1,0)}"
      businessTypeCodeCBBT: "${getBusinessTypeId2(2)}"

testcases:
-
    name: createByBizTemplate-使用业务模板抄送签署-校验抄送方的Code信息
    testcase: testcases/signs/signFlow/createByBizTemplate/CCInfosCodeTC.yml

-
    name: createByBizTemplate校验抄送人信息-customAccountNo,customOrgNo,customDepartmentNo入参字段
    parameters:
      - name-customAccountNo0-customOrgNo0-customDepartmentNo0-userCode00-organizationCode00-departmentCode00-userType0-code-message-data:
          - ["TC1-customAccountNo为正常值，userCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoSigner0,"","","",1,200,"成功",""]
          - ["TC2-customAccountNo为正常值A账号，userCode为正常值B账号,发起B数据成功",$customAccountNoSignerOpposit,$customOrgNoSigner0,$customOrgNoSigner0,$userCode0,"","",1,200,"成功",""]
          - ["TC3-customAccountNo和userCode都为空,报错","",$customOrgNoSigner0,"","","","",1,1612212,"发起失败：抄送方用户编码不能为空",{}]
          - ["TC4-customAccountNo和userCode都不传,报错",null,$customOrgNoSigner0,"",null,"","",1,1612212,"发起失败：抄送方用户编码不能为空",{}]
          - ["TC5-customAccountNo不存在的值，userCode为空,报错",'xxxxx',$customOrgNoSigner0,"","","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
          - ["TC6-customAccountNo不存在的值，userCode为正确的值,发起成功",'xxxxx',$customOrgNoSigner0,$customOrgNoSigner0,$userCode0,"","",1,200,"成功",""]
          - ["TC7-customAccountNo为正常值，userCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","XXXXXX","","",1,1612212,"发起失败：用户已不存在",{}]
          - ["TC8-customAccountNo对应的账号为离职状态，userCode空,报错",$customAccountNoDimission,$customOrgNoSigner0,$customOrgNoSigner0,"","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
          - ["TC9-customAccountNo对应的账号为删除状态，userCode空,报错",$customAccountNoDelete,$customOrgNoSigner0,$customOrgNoSigner0,"","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
#          - ["TC10-customOrgNo为正常值，organizationCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功",""]
#          - ["TC11-customOrgNo为正常值A账号，organizationCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,"",1,200,"成功",""]
#          - ["TC12-customOrgNo和organizationCode都为空,报错",$customAccountNoSigner0,"","","","","",1,200,"成功",""]
#          - ["TC13-customOrgNo和organizationCode都不传,报错",$customAccountNoSigner0,null,"","",null,"",1,200,"成功",""]
#          - ["TC14-customOrgNo不存在的值，organizationCode为空,报错",$customAccountNoSigner0,'xxxxxxxx',"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）",""]
#          - ["TC15-customOrgNo不存在的值，organizationCode为正确的值,发起成功",$customAccountNoSigner0,'xxxxx',"","",$orgCode0,"",1,200,"成功",""]
#          - ["TC16-customOrgNo为正常值，organizationCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,"","","XXXXX","",1,1702046,"未知的组织信息！",""]
#          - ["TC17-customOrgNo对应的账号为注销状态，organizationCode为空,报错",$customAccountNoSigner0,$customOrgNoDelete,"","","","",1,1702433,"用户所属企业不存在（客户系统的唯一标识）",""]
          - ["TC18-customDepartmentNo为正常值，departmentCode为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"","","",1,200,"成功",""]
          - ["TC19-customDepartmentNo为正常值A账号，departmentCode为正常值B账号,发起B数据成功",$customAccountNoSigner0,$customOrgNoSigner0,$customOrgNoSignerOpposit,"","",$orgCode0,1,200,"成功",""]
          - ["TC20-customDepartmentNo和departmentCode都为空,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","","",1,200,"成功",""]
          - ["TC21-customDepartmentNo和departmentCode都不传,发起成功",$customAccountNoSigner0,$customOrgNoSigner0,null,"","",null,1,200,"成功",""]
          - ["TC22-customDepartmentNo不存在的值，departmentCode为空,报错",$customAccountNoSigner0,$customOrgNoSigner0,"XXXXXX","","","",1,1612212,"发起失败：抄送方组织信息不存在",{}]
          - ["TC23-customDepartmentNo不存在的值，departmentCode为正确的值,成功",$customAccountNoSigner0,$customOrgNoSigner0,"","","",$departmentCode0,1,200,"成功",""]
          - ["TC24-customDepartmentNo为正常值，departmentCode不存在的值,报错",$customAccountNoSigner0,$customOrgNoSigner0,$customDepartmentNoSigner0,"",$orgCode0,"XXXXXXX",1,1612212,"发起失败：抄送方组织信息不存在",{}]
          - ["TC26-customAccountNo和customDepartmentNo和customOrgNo为正常值，对应的code都为空字符串,成功",$sp$customAccountNoSigner0$sp,$sp$customOrgNoSigner0$sp,$sp$customDepartmentNoSigner0$sp,"","","",1,200,"成功",""]
          - ["TC28-customAccountNo所属主责是部门，校验发起后customDepartmentNo为该部门,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功",""]
          - ["TC31-customAccountNo传入的长度超过36字符,报错","${generate_random_str(37)}",$customOrgNoSigner0,$customOrgNoSigner0,"","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
          - ["TC32-customDepartmentNo传入的长度超过64字符,报错",$customAccountNoSigner0,$customOrgNoSigner0,"${generate_random_str(65)}","","","",1,1612212,"发起失败：抄送方组织信息不存在",{}]
          - ["TC34-customAccountNo带点号的数据等支持的特殊字符,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customOrgNoSigner3,"","","",1,200,"成功",""]
          - ["TC35-customAccountNo传入的是相对方个人的账号，userType限定是内部,报错",$customAccountNoSignerOpposit,$customOrgNoSignerOpposit,$customOrgNoSignerOpposit,"","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
          - ["TC36-customAccountNo传入不支持的特殊字符,报错","${get_not_support_str()}",$customOrgNoSigner0,"","","","",1,1612212,"发起失败：抄送方用户信息不存在",{}]
          - ["TC38-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,"","","","",1,200,"成功",""]
          - ["TC39-customAccountNo所属部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，对应的code都不传,成功",$customAccountNoSigner3,$customOrgNoSigner3,$customDeptNoSigner3,"","","",1,200,"成功",""]
          - ["TC40-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo不传,报错",$customAccountNoSigner0,$customOrgNoSigner3,"","","","",1,200,"成功",""]
          - ["TC41-customAccountNo兼职部门，customOrgNo为该部门所属企业，customDepartmentNo为该部门，成功",$customAccountNoSigner0,$customOrgNoSigner3,"${ENV(sign01.JZ.departNo)}","","","",1,200,"成功",""]

    testcase: testcases/signs/signFlow/createByBizTemplate/CCInfosNoTC.yml