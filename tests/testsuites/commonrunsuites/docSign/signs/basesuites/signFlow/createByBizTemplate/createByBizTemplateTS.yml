config:
    name: createByBizTemplate-使用业务模板发起签署-综合场景
    variables:
      userCode0: ${ENV(sign01.userCode)}

testcases:
-
    name: createByBizTemplate-使用业务模板发起签署-校验综合场景-验证〇
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence1TC.yml

-
    name: createByBizTemplate-使用业务模板抄送签署-校验businessTypeCode
    testcase: testcases/signs/signFlow/createByBizTemplate/businessTypeCodeTC.yml

-
    name: createByBizTemplate-V6.0.12.0-beta.5-支持发起人填写
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence2TC.yml

-
    name: createByBizTemplate-V6.0.12.0-beta.5-支持其他填写方设置
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence3TC.yml

-
    name: createByBizTemplate-V6.0.13.0-beta.1-支持签署网络
    testcase: testcases/signs/signFlow/createByBizTemplate/signerInfosestpNodeTC.yml
    
-
    name: createByBizTemplate-V6.0.13.0-beta.1-限制模板印章颜色
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSealSenceTC.yml

-
    name: createByBizTemplate-V6.0.13.0-beta.6-静默签署成功之后-作废
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence4TC.yml

-
    name: V6.0.14.0-beta.3-允许添加签署文件-业务模板开启追加文件且设置签署方
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence5TC.yml

-
    name: V6.0.14.0-beta.3-允许添加签署文件-业务模板关闭追加文件且设置签署方
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence6TC.yml

-
    name: V6.0.14.0-beta.3-允许添加签署文件-业务模板开启追加文件且设置签署方静默签署
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence7TC.yml

-
    name: V6.0.14.0-beta.3-允许添加签署文件-OFD流程且设置签署方
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence8TC.yml

-
    name: V6.0.14.0-beta.3-允许添加签署文件-内置通用业务模板发起
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateSence9TC.yml


-
    name: V6.0.14.0-beta.3-允许添加签署方1
    testcase: testcases/signs/signFlow/createByBizTemplate/createByMessageWithSignersAdd1.yml

-
    name: V6.0.14.0-beta.3-允许添加签署方2
    testcase: testcases/signs/signFlow/createByBizTemplate/createByMessageWithSignersAdd2.yml

-
    name: V6.0.14.0-beta.3-允许添加签署方3
    testcase: testcases/signs/signFlow/createByBizTemplate/createByMessageWithSignersAdd3.yml

-
    name: V6.0.14.0-beta.6-使用业务模板发起-flowExtensions字段
    testcase: testcases/signs/signFlow/createByBizTemplate/createByBizTemplateFlowExtensionsTC.yml



