config:
  name: 作废流程用例
  variables:
    - customAccountNo0: ${ENV(csqs.accountNo)}
    - userCode0: ${ENV(csqs.userCode)}
    - orgNo0: ${ENV(csqs.orgNo)}
    - orgCode0: ${ENV(csqs.orgCode)}

    - customAccountNo1: ${ENV(sign01.accountNo)}
    - userCode1: ${ENV(sign01.userCode)}
    - orgNo1: ${ENV(sign01.main.orgNo)}
    - orgCode1: ${ENV(sign01.main.orgCode)}

    - customAccountNo2: ${ENV(wsignwb01.accountNo)}
    - userCode2: ${ENV(wsignwb01.userCode)}
    - orgNo2: ${ENV(wsignwb01.main.orgNo)}
    - orgCode2: ${ENV(worg01.orgCode)}

    - userCode3: ${ENV(userCode.dimission)}
    - userCode4: ${ENV(userCode.delete)}
    - userCode5: ${ENV(userCode.opposite.delete)}

    - orgCode3: ${ENV(orgCode.delete)}
    - orgCode4: ${ENV(orgCode.opposite.delete)}

    - signFlowId0: "${get_signFlow_signing(False, True, False)}"


testcases:
  - name: 作废校验发起方参数
    parameters:
      - name-signFlowId-initiatorInfo-code-message-data:
          - [ "TC1-customAccountNo和userCode都不传，报错",$signFlowId0,{ "customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userType": 1 },1702160,"流程发起人编码不可为空！","" ]
          - [ "TC2-customAccountNo和userCode都为空，报错",$signFlowId0,{ "customAccountNo": "","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "","userType": 1 },1702160,"流程发起人编码不可为空！","" ]
          - [ "TC3-userCode不存在，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 },1702602,"发起方(XXXXX)不存在","" ]
          - [ "TC4-userCode已离职，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode3","userType": 1 },1702602,"不存在","" ]
          - [ "TC5-userCode已删除，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode4","userType": 1 },1702602,"不存在","" ]
          - [ "TC6-userCode相对方账号，报错",$signFlowId0,{ "departmentCode": " $orgCode0 ","organizationCode": "$orgCode0","userCode": "$userCode5","userType": 1 },1702602,"不存在","" ]
          - [ "TC7-userCode为空customAccountNo不存在，报错",$signFlowId0,{ "customAccountNo": "XXXX","departmentCode": "$orgCode0 ","organizationCode": " $orgCode0 ","userCode": "","userType": 1 },1702616,"发起方用户账号不存在（客户系统的唯一标识）: XXXX","" ]
          - [ "TC8-userCode不存在customAccountNo正确，报错",$signFlowId0,{ "customAccountNo": "$customAccountNo0","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 },1702602,"发起方(XXXXX)不存在","" ]
          - [ "TC9-userCode正确customAccountNo不存在，作废成功","${get_signFlow_signing(False, True, False)}",{ "customAccountNo": "","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },200,"成功","" ]
          - [ "TC10-customOrgNo和organizationCode都不传，报错",$signFlowId0,{ "departmentCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702162,"流程发起人组织编码不可为空！","" ]
          - [ "TC11-customOrgNo和organizationCode都为空，报错",$signFlowId0,{ "customOrgNo": "","departmentCode": "","userCode": "$userCode0","userType": 1 },1702162,"流程发起人组织编码不可为空！","" ]
          - [ "TC12-organizationCode不存在，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "xxxx","userCode": "$userCode0","userType": 1 },1702604,"所属企业不存在","" ]
          - [ "TC13-organizationCode已删除，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode3","userCode": "$userCode0","userType": 1 },1702604,"所属企业不存在","" ]
          - [ "TC14-organizationCode和用户不匹配，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode1","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC15-organizationCode是相对方，报错",$signFlowId0,{ "departmentCode": "$orgCode0","organizationCode": "$orgCode2","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC16-organizationCode为空customOrgNo不存在，报错",$signFlowId0,{ "customOrgNo": "XXXX","departmentCode": "$orgCode0","organizationCode": "","userCode": "$userCode0","userType": 1 },1702606,"发起方用户所属企业不存在","" ]
          - [ "TC17-organizationCode不存在customOrgNo正确，报错",$signFlowId0,{ "customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "XXXX","userCode": "$userCode0","userType": 1 },1702604,"所属企业不存在","" ]
          - [ "TC18-organizationCode正确customOrgNo不存在，报错","${get_signFlow_signing(False, True, False)}",{ "customOrgNo": "xxxx","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },200,"成功","" ]
          - [ "TC19-departmentCode和customDepartmentNo不传，作废成功","${get_signFlow_signing(False, True, False)}",{ "customAccountNo": "","customDepartmentNo": "","customOrgNo": "$orgNo0","departmentCode": "","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },200,"成功","" ]
          - [ "TC20-departmentCode不存在，报错",$signFlowId0,{ "departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC21-departmentCode已删除，报错",$signFlowId0,{ "departmentCode": "$orgCode3","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC22-departmentCode和用户不匹配，报错",$signFlowId0,{ "departmentCode": "$orgCode1","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC23-departmentCode是相对方，报错",$signFlowId0,{ "departmentCode": "$orgCode2","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC24-departmentCode为空customDepartmentNo不存在，报错",$signFlowId0,{ "customDepartmentNo": "XXX","departmentCode": "","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702620,"发起方用户所属组织不存在（客户系统的唯一标识）: XXX","" ]
          - [ "TC25-departmentCode不存在customDepartmentNo正确，报错",$signFlowId0,{ "customDepartmentNo": "$orgNo0","departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },1702615,"组织关系不匹配","" ]
          - [ "TC26-userType=2，报错",$signFlowId0,{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": 2 },1600017,"发起人用户类型不正确","" ]
          - [ "TC27-userType=Y，报错",$signFlowId0,{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": "Y" },1600015,"userType参数错误!","" ]
          - [ "TC28-userType=-1，报错",$signFlowId0,{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": -1 },1600017,"发起人用户类型不正确","" ]
          - [ "TC29-不允许相对方发起，报错",$signFlowId0,{ "organizationCode": "$orgCode2","userCode": "$userCode2","userType": 2 },1600017,"发起人用户类型不正确","" ]
          - [ "TC30-允许作废的发起方非原流程发起方",$signFlowId0,{ "organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 },200,"","" ]
          - [ "TC31-签署中流程，作废成功","${get_signFlow_signing(False, False, False)}",{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 },200,"成功","" ]

    testcase: testcases/signs/signFlow/revoke/revokeInitiatorInfoTC.yml