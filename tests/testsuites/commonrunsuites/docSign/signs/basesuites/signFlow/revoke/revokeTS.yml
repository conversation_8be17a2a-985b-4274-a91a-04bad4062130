config:
  name: 作废流程用例
  variables:
    - customAccountNo0: ${ENV(csqs.accountNo)}

testcases:
  作废流程用例:
    testcase: testcases/signs/signFlow/revoke/revokeTC.yml

  作废流程场景用例:
    testcase: testcases/signs/signFlow/revoke/revokeSceneTC.yml

  作废流程场景用例2:
    testcase: testcases/signs/signFlow/revoke/revokeSceneTC2.yml

  作废流程用例4:
    testcase: testcases/signs/signFlow/revoke/revokeTC4.yml

  作废流程用例5:
    testcase: testcases/signs/signFlow/revoke/revokeTC5.yml

  作废流程用例6:
    testcase: testcases/docs/signFlow/revokeRevokeSignatureArea.yml

  支持部分作废-60150-beta3:
    testcase: testcases/signs/signFlow/revoke/revokeTC6.yml

  不支持部分作废-60150-beta3:
    testcase: testcases/signs/signFlow/revoke/revokeTC7.yml

