config:
  name: 作废流程用例
  variables:
    - customAccountNo1: ${ENV(csqs.accountNo)}
    - userCode1: ${ENV(csqs.userCode)}
    - orgNo1: ${ENV(csqs.orgNo)}
    - orgCode1: ${ENV(csqs.orgCode)}

    - customAccountNo0: ${ENV(sign01.accountNo)}
    - userCode0: ${ENV(sign01.userCode)}
    - orgNo0: ${ENV(sign01.main.orgNo)}
    - orgCode0: ${ENV(sign01.main.orgCode)}

#    - customAccountNo0: ${ENV(csqs.accountNo)}
#    - userCode0: ${ENV(csqs.userCode)}
#    - orgNo0: ${ENV(csqs.orgNo)}
#    - orgCode0: ${ENV(csqs.orgCode)}
#
#    - customAccountNo1: ${ENV(sign01.accountNo)}
#    - userCode1: ${ENV(sign01.userCode)}
#    - orgNo1: ${ENV(sign01.main.orgNo)}
#    - orgCode1: ${ENV(sign01.main.orgCode)}

    - customAccountNo2: ${ENV(wsignwb01.accountNo)}
    - userCode2: ${ENV(wsignwb01.userCode)}
    - orgNo2: ${ENV(wsignwb01.main.orgNo)}
    - orgCode2: ${ENV(worg01.orgCode)}

    - userCode3: ${ENV(userCode.dimission)}
    - userCode4: ${ENV(userCode.delete)}
    - userCode5: ${ENV(userCode.opposite.delete)}

    - orgCode3: ${ENV(orgCode.delete)}
    - orgCode4: ${ENV(orgCode.opposite.delete)}

    - signFlowId0: "${get_signFlow_signing(False, True, False)}"
    - signFlowId1: "${get_signFlow_signing(False, True, False)}"


testcases:
- name: 作废校验签署方参数
  parameters:
    - name-signFlowId-signerInfos-code-message-data:
        - [ "TC1-customAccountNo和userCode都不传，报错",$signFlowId0,[{ "customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userType": 1 }],1702613,"签署方用户编码和用户账号不能都为空",{} ]
        - [ "TC2-customAccountNo和userCode都为空，报错",$signFlowId0,[{ "customAccountNo": "","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "","userType": 1 }],1702613,"签署方用户编码和用户账号不能都为空",{} ]
        - [ "TC3-userCode不存在，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 }],1702624,"签署方经办人(XXXXX)不存在",{} ]
        - [ "TC4-userCode已离职，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode3","userType": 1 }],1702624,"不存在",{} ]
        - [ "TC5-userCode已删除，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode4","userType": 1 }],1702624,"不存在",{} ]
        - [ "TC6-userCode相对方账号已删除，报错",$signFlowId0,[{ "departmentCode": "  ","organizationCode": "","userCode": "$userCode5","userType": 2 }],1702603,"不存在",{} ]
        - [ "TC7-userCode为空customAccountNo不存在，报错",$signFlowId0,[{ "customAccountNo": "XXXX","departmentCode": "$orgCode0 ","organizationCode": " $orgCode0 ","userCode": "","userType": 1 }],1702605,"签署方用户账号不存在（客户系统的唯一标识）: XXXX",{} ]
        - [ "TC8-userCode不存在customAccountNo正确，报错",$signFlowId0,[{ "customAccountNo": "$customAccountNo0","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 }],1702624,"签署方经办人(XXXXX)不存在",{} ]
        - [ "TC9-userCode正确customAccountNo不存在userCode非原流程签署人，报错",$signFlowId0,[{ "customAccountNo": "","customDepartmentNo": "","customOrgNo": "","departmentCode": "$orgCode1","organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 }],1702599,"不在原流程中不能作废",{} ]
#        - [ "todo TC10-customOrgNo和organizationCode都不传，报错",$signFlowId0,[{ "userCode": "$userCode1","userType": 1 }],1702599,"不在原流程中不能作废",{} ]
#        - [ "TC11-customOrgNo和organizationCode都为空，报错",$signFlowId0,[{ "customOrgNo": "","organizationCode": "","userCode": "$userCode1","userType": 1 }],1702599,"不在原流程中不能作废",{} ]
        - [ "TC12-organizationCode不存在，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "xxxx","userCode": "$userCode0","userType": 1 }],1702603,"签署主体(xxxx)不存在",{} ]
        - [ "TC13-organizationCode已删除，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode3","userCode": "$userCode0","userType": 1 }],1702603,"不存在",{} ]
        - [ "TC14-organizationCode和用户不匹配，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode1","userCode": "$userCode0","userType": 1 }],1702599,"不在原流程中不能作废",{} ]
        - [ "TC15-organizationCode是相对方，报错",$signFlowId0,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode2","userCode": "$userCode0","userType": 1 }],1702609,"不是企业章用印人",{} ]
        - [ "TC16-organizationCode为空customOrgNo不存在，报错",$signFlowId0,[{ "customOrgNo": "XXXX","departmentCode": "$orgCode0","organizationCode": "","userCode": "$userCode0","userType": 1 }],1702603,"签署主体(XXXX)不存在",{} ]
        - [ "TC17-organizationCode不存在customOrgNo正确，报错",$signFlowId0,[{ "customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "XXXX","userCode": "$userCode0","userType": 1 }],1702603,"签署主体(XXXX)不存在",{} ]
        - [ "TC18-organizationCode正确customOrgNo不存在，报错",$signFlowId0,[{ "customOrgNo": "xxxx","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702599,"不在原流程中不能作废",{} ]
        - [ "TC19-departmentCode和customDepartmentNo不传，作废成功","${get_signFlow_signing(False, True, False)}",[{"customDepartmentNo": "","departmentCode": "","organizationCode": "","userCode": "$userCode0","userType": 1 }],200,"成功","" ]
        - [ "TC20-departmentCode不存在，报错",$signFlowId0,[{ "departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702608,"组织关系不匹配",{} ]
        - [ "TC21-departmentCode已删除，报错",$signFlowId0,[{ "departmentCode": "$orgCode3","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702608,"组织关系不匹配",{} ]
        - [ "TC22-departmentCode和用户不匹配，报错",$signFlowId0,[{ "departmentCode": "$orgCode1","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702608,"组织关系不匹配",{} ]
        - [ "TC23-departmentCode是相对方，报错",$signFlowId0,[{ "departmentCode": "$orgCode2","organizationCode": "","userCode": "$userCode0","userType": 1 }],1702393,"未知的部门信息！",{} ]
        - [ "TC24-departmentCode为空customDepartmentNo不存在，报错",$signFlowId0,[{ "customDepartmentNo": "XXX","departmentCode": "","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702607,"签署方用户所属组织不存在（客户系统的唯一标识）: XXX",{} ]
        - [ "TC25-departmentCode不存在customDepartmentNo正确，报错",$signFlowId0,[{ "customDepartmentNo": "$orgNo0","departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702608,"组织关系不匹配",{} ]
        - [ "TC26-userType=2但是userCode是内部，报错",$signFlowId0,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": 2 }],1702623,"用户类型不匹配",{} ]
        - [ "TC27-userType=Y，报错",$signFlowId0,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": "Y" }],1600015,"userType参数错误!",{} ]
        - [ "TC28-userType=-1，报错",$signFlowId0,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": -1 }],1702623,"用户类型不匹配",{} ]
        - [ "TC29-不允许添加非原流程的签署方，报错",$signFlowId0,[{ "organizationCode": "$orgCode2","userCode": "$userCode2","userType": 2 }],1702599,"不在原流程中不能作废",{} ]
        - [ "TC30-指定空数组，会自动带入原流程的签署方，发起作废成功","${get_signFlow_signing(False, True, False)}",[],200,"成功", ""]
        - [ "TC31-指定空对象",$signFlowId0,[{}],1702613,"签署方用户编码和用户账号不能都为空",{} ]
        - [ "TC32-指定原流程的签署方，作废成功",$signFlowId0,[{ "departmentCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],200,"成功", ""]
  testcase: testcases/signs/signFlow/revoke/revokeSignerInfoTC.yml
#
- name: 作废校验抄送方参数
  parameters:
    - name-signFlowId-CCInfos-code-message-data:
        - [ "TC1-customAccountNo和userCode都不传，报错",$signFlowId1,[{ "customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userType": 1 }],1702612,"抄送方用户编码和用户账号不能都为空",{} ]
        - [ "TC2-customAccountNo和userCode都为空，报错",$signFlowId1,[{ "customAccountNo": "","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "","userType": 1 }],1702612,"抄送方用户编码和用户账号不能都为空",{} ]
        - [ "TC3-userCode不存在，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 }],1702610,"抄送方(XXXXX)不存在",{} ]
        - [ "TC4-userCode已离职，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode3","userType": 1 }],1702610,"不存在",{} ]
        - [ "TC5-userCode已删除，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode4","userType": 1 }],1702610,"不存在",{} ]
        - [ "TC6-userCode相对方账号，报错",$signFlowId1,[{ "departmentCode": " $orgCode0 ","organizationCode": "$orgCode0","userCode": "$userCode5","userType": 1 }],1702610,"不存在",{} ]
        - [ "TC7-userCode为空customAccountNo不存在，报错",$signFlowId1,[{ "customAccountNo": "XXXX","departmentCode": "$orgCode0 ","organizationCode": " $orgCode0 ","userCode": "","userType": 1 }],1702617,"抄送方用户账号不存在（客户系统的唯一标识）: XXXX",{} ]
        - [ "TC8-userCode不存在customAccountNo正确，报错",$signFlowId1,[{ "customAccountNo": "$customAccountNo0","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "XXXXX","userType": 1 }],1702610,"抄送方(XXXXX)不存在",{} ]
        - [ "TC9-userCode正确customAccountNo不存在，作废成功","${get_signFlow_signing(False, True, False)}",[{ "customAccountNo": "","customDepartmentNo": "$orgNo0","customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],200,"成功",""]
#        - [ "TC10-customOrgNo和organizationCode都不传，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702408,"抄送人组织编码不可为空!",{} ]
#        - [ "TC11-customOrgNo和organizationCode都为空，报错",$signFlowId1,[{ "customOrgNo": "","departmentCode": "","userCode": "$userCode0","userType": 1 }],1702408,"抄送人组织编码不可为空!",{} ]
        - [ "TC12-organizationCode不存在，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "xxxx","userCode": "$userCode0","userType": 1 }],1702611,"抄送方",{} ]
        - [ "TC13-organizationCode已删除，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode3","userCode": "$userCode0","userType": 1 }],1702611,"所属企业不存在",{} ]
        - [ "TC14-organizationCode和用户不匹配，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode1","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC15-organizationCode是相对方，报错",$signFlowId1,[{ "departmentCode": "$orgCode0","organizationCode": "$orgCode2","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC16-organizationCode为空customOrgNo不存在，报错",$signFlowId1,[{ "customOrgNo": "XXXX","departmentCode": "$orgCode0","organizationCode": "","userCode": "$userCode0","userType": 1 }],1702618,"抄送方用户所属企业不存在（客户系统的唯一标识）: XXXX",{} ]
        - [ "TC17-organizationCode不存在customOrgNo正确，报错",$signFlowId1,[{ "customOrgNo": "$orgNo0","departmentCode": "$orgCode0","organizationCode": "XXXX","userCode": "$userCode0","userType": 1 }],1702611,"所属企业不存在",{} ]
        - [ "TC18-organizationCode正确customOrgNo不存在，报错","${get_signFlow_signing(False, True, False)}",[{ "customOrgNo": "xxxx","departmentCode": "$orgCode0","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],200,"成功",""]
        - [ "TC19-departmentCode和customDepartmentNo不传，作废成功","${get_signFlow_signing(False, True, False)}",[{"customDepartmentNo": "","customOrgNo": "$orgNo0","departmentCode": "","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],200,"成功",""]
        - [ "TC20-departmentCode不存在，报错",$signFlowId1,[{ "departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC21-departmentCode已删除，报错",$signFlowId1,[{ "departmentCode": "$orgCode3","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC22-departmentCode和用户不匹配，报错",$signFlowId1,[{ "departmentCode": "$orgCode1","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC23-departmentCode是相对方，报错",$signFlowId1,[{ "departmentCode": "$orgCode2","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC24-departmentCode为空customDepartmentNo不存在，报错",$signFlowId1,[{ "customDepartmentNo": "XXX","departmentCode": "","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702619,"抄送方用户所属组织不存在（客户系统的唯一标识）",{} ]
        - [ "TC25-departmentCode不存在customDepartmentNo正确，报错",$signFlowId1,[{ "customDepartmentNo": "$orgNo0","departmentCode": "XXXX","organizationCode": "$orgCode0","userCode": "$userCode0","userType": 1 }],1702614,"组织关系不匹配",{} ]
        - [ "TC26-userType=2，报错",$signFlowId1,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": 2 }],1702077,"账号类型与传入的不一致！",{} ]
        - [ "TC27-userType=Y，报错",$signFlowId1,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": "Y" }],1702167,"用户类型请在 1内部 2相对方内选择！",{} ]
        - [ "TC28-userType=-1，报错",$signFlowId1,[{ "organizationCode": "$orgCode0","userCode": "$userCode0","userType": -1 }],1702167,"用户类型请在 1内部 2相对方内选择！",{} ]
        - [ "TC29-允许添加新的抄送方-相对方，成功","${get_signFlow_signing(False, True, False)}",[{ "organizationCode": "$orgCode2","userCode": "$userCode2","userType": 2 }],200,"成功",""]
  testcase: testcases/signs/signFlow/revoke/revokeCCInfoTC.yml

- name: 作废校验-添加多个签署方-多个抄送方
  parameters:
    - name-signFlowId-signerInfos-CCInfos-code-message-data:
        - [ "TC1-作废的签署方比原流程多，报错",$signFlowId0,[{"userCode": "$userCode0","userType": 1 },{"organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 }],[],1702600,"签署主体与原流程不匹配",{} ]
        - [ "TC2-作废的签署方和原流程一致但是重复，报错",$signFlowId0,[{"userCode": "$userCode0","userType": 1 },{"userCode": "$userCode0","userType": 1 }],[],1702600,"签署主体与原流程不匹配",{} ]
        - [ "TC3-作废的抄送方重复，报错",$signFlowId0,[],[{"organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 },{"organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 }],1702140,"同一流程抄送方不可重复！", {}]
        - [ "TC4-作废的抄送方添加多个，成功",$signFlowId0,[],[{"organizationCode": "$orgCode1","userCode": "$userCode1","userType": 1 },{"organizationCode": "$orgCode2","userCode": "$userCode2","userType": 2 }],200,"成功",""]
  testcase: testcases/signs/signFlow/revoke/revokeTC3.yml