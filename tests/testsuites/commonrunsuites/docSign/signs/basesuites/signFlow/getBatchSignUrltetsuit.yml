config:
    name: "获取批量签署页面"
    variables:
      userCode0: $${ENV(sign01.userCode)}
      userCode04: $${ENV(sign04.userCode)}
      code65: ${generate_random_str(65)}
      code64: ${generate_random_str(64)}
      customAccountNo0: $${ENV(sign01.accountNo)}
      accountNo301: ${generate_random_str(301)}
      accountNo300: ${generate_random_str(300)}
      redirectUrl501: ${generate_random_str(501)}
      wsignwb01userCode: $${ENV(wsignwb01.userCode)}
testcases:
  - name: 获取批量签署页面
    parameters:
      - name-userType-userCode-customAccountNo-redirectUrl-code-message-data:
          - [ "P1TL-发起获取批量签署地址接口签署人类型为2相对方，内部签署人", 2, $userCode0,"","",1709113, "暂不支持相对方签署",null ]
          - [ "P2TL-发起获取批量签署地址接口签署人类型为非1或2", 5, $userCode0,"","",1703001, "签署人账号类型只能是1或2",null ]
          - [ "P3TL-发起获取批量签署地址接口userType为1，userCode和customAccountNo均为空", 1, "","","",1709114, "签署人编码和签署人账号不能都为空",null ]
          - [ "P4TL-发起获取批量签署地址接口userType为1，userCode超出边界值65校验",1, $code65, "","",1703001, "签署人用户编码长度不可超出64字符！",null]
          - [ "P5TL-发起获取批量签署地址接口userType为1，userCode字符长度64边界值校验",1, $code64, "","",1702001, "签署人信息不存在",null ]
          - [ "P6TL-发起获取批量签署地址接口userType为1，customAccountNo超出边界值301校验",1, "", $accountNo301,"",1703001, "签署人用户账号长度不可超出300字符！",null ]
          - [ "P7TL-发起获取批量签署地址接口userType为1，customAccountNo字符长度300边界值校验",1, "", $accountNo300,"",1702001, "签署人信息不存在",null ]
          - [ "P8TL-发起获取批量签署地址接口userType为1，签署人userCode、customAccountNo指定相对方用户",1, $wsignwb01userCode, "","",1709113, "暂不支持相对方签署",null ]
          - [ "P9TL-发起获取批量签署地址接口userType为1，签署人userCode、customAccountNo指定内部用户已注销",1, "ceszxyh", "","",1702001,"签署人信息不存在",null  ]
          - [ "P10TL-发起获取批量签署地址接口userType为1，签署人userCode、customAccountNo指定内部用户已离职",1, "cesfqrlz", "","",1702001, "签署人信息不存在",null  ]
          - [ "P11TL-redirectUrl文档创建完成重定向地址为超出长度501校验",1, "", $customAccountNo0,$redirectUrl501,1703001, "批量签署页重定向地址长度不能超过500字符",null ]
    testcase: testcases/signs/signFlow/getBatchSignUrlcases.yml

  - name: 获取批量签署页面-appRedirectInfo
    parameters:
      - name-appRedirectInfo_batch-code-message-data:
          - [ "TC2-appRedirectInfo为[{}]", [{}] ,1703001, "不能为空！",null ]
          - [ "TC3-appRedirectInfo为appName为空",[{"appName": " ","appSchema": " esign://demo/realBack " }] ,1703001, "APP设备标识名称不能为空！",null ]
          - [ "TC4-appRedirectInfo为appName为null",[{"appName": null,"appSchema": " esign://demo/realBack " }] ,1703001, "APP设备标识名称不能为空！",null ]
          - [ "TC6-appRedirectInfo为appName长度超过",[{"appName": " ${generate_random_str(129)} ","appSchema": " esign://demo/realBack " }] ,1703001, "APP设备标识名称长度不可超出128字符！",null ]
          - [ "TC7-appRedirectInfo为appSchema为空",[{"appName": "test","appSchema": " " }],1703001, "APP跳转协议不能为空！",null ]
          - [ "TC8-appRedirectInfo为appSchema为null",[{"appName": "test","appSchema": null }],1703001, "APP跳转协议不能为空！",null ]
          - [ "TC10-appRedirectInfo为appSchema长度超过",[{"appName": "test","appSchema": " ${generate_random_str(129)} " }],1703001, "APP跳转协议长度不可超出128字符！",null ]
    testcase: testcases/signs/signFlow/getBatchSignUrlcases.yml

  - name: 获取批量签署页面-appRedirectInfo-成功case调用
    parameters:
      - name-appRedirectInfo_batch-code-message-data:
          - [ "TC1-appRedirectInfo为[]", [] ,200, "成功",null ]
          - [ "TC5-appRedirectInfo为appName中文字符串",[{"appName": " 测试数据 ","appSchema": " esign://demo/realBack " }],200, "成功",null ]
          - [ "TC9-appRedirectInfo为appSchema中文字符串",[{"appName": "test","appSchema": " 测试数据 " }],200, "成功",null ]
          - [ "TC11-appRedirectInfo为appSchema有特殊字符",[{"appName": "test","appSchema": " 【/:*?<>|】不支持的特殊字符 " }],200, "成功",null ]
          - [ "TC12-appRedirectInfo多个重复对象",[{"appName": "test","appSchema": "esign://demo" },{"appName": "test","appSchema": "esign://demo" },{"appName": "test","appSchema": "esign://demo" }],200, "成功",null ]
          - [ "TC13-appRedirectInfo多个不同对象",[{"appName": "test111","appSchema": "esign://demo/${generate_random_str(100)}" },{"appName": "test222","appSchema": "esign://demo/${generate_random_str(100)}" },{"appName": "test333","appSchema": "esign://demo/${generate_random_str(100)}" }],200, "成功",null ]
          - [ "TC14-appRedirectInfo超过10个对象",[{"appName": "1","appSchema": "1" },{"appName": "2","appSchema": "2" },{"appName": "3","appSchema": "3" },{"appName": "1","appSchema": "1" },{"appName": "2","appSchema": "2" },{"appName": "3","appSchema": "3" },{"appName": "1","appSchema": "1" },{"appName": "2","appSchema": "2" },{"appName": "3","appSchema": "3" },{"appName": "1","appSchema": "1" },{"appName": "2","appSchema": "2" },{"appName": "3","appSchema": "3" }],200, "成功",null ]

    testcase: testcases/signs/signFlow/getBatchSignUrlcasesSuccess.yml

  - name: 获取批量签署页面场景用例
    testcase: testcases/signs/signFlow/getBatchSignUrlTC.yml

