config:
    name: 内部机构静默签
    variables:
        #filekey
        fileKey30Page: "$${ENV(fileKey)}"
        #第二个 filekey
        fileKey30Page1: $${ENV(3PageFileKey)}
        # 单文件 签署文件
        signFiles0: [{"fileKey":"$fileKey30Page"}]
        # 签署附件
        signFiles1: [{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}]
        # 骑缝签 页码为0
        signerPage0: [{"pageNo":"0"}]
        # 骑缝签 页码范围超出范围
        signerMore: [{"pageNo":"99"}]
        # 印章不匹配
        signerOrgAndSealDiff: [{"sealId":"${ENV(org01.legal.sealId)}"}]
        signerOrgAndSealDiff2: [{"sealId":"${ENV(sign01.sealId)}"}]
        # 关键字长度超出30字符(31)
        signerKeylen20: [{"addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"测试 TEST12345678901234 弋弌弍弎弐弑 測試"}]}]
        # 未发布印章
        signerNotPublicSeal: [{"sealId":"${ENV(orgSealNoPublic)}"}]
        # 取默认章
        signerNotDef: [{"pageNo":"1","organizeCode":"${ENV(sign01.JZ.orgCode)}",signerConfig:[{"childSignatureType":"COMMON-SEAL","childSealId":""}]}]


testcases:
-
    name: 内部机构静默签 - 报错用例
    parameters:
      - name-signFiles-attachments-signerInfos-code-message:
          - ["TC1-机构静默签 自由签 报错",$signFiles0,$signFiles0,[{"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702123,"静默签必须指定签署位置！"]
          - ["TC2-机构静默签 多文档 部分未指定位置 报错",$signFiles1,$signFiles0, [{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":"$fileKey30Page1"}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}","organizationCode":"${ENV(sign01.main.orgCode)}"}],1702532,"发起失败：静默签必须指定签署区"]
          - ["TC3-机构静默签单文档  骑缝页码为0  报错",$signFiles0,$signFiles0,"${autoAdapter(3,$signFiles0,$signerPage0)}",1702211,"指定页码需为正整数！"]
          - ["TC4-机构静默签多文档  骑缝页码为0  报错",$signFiles1,$signFiles0,"${autoAdapter(3,$signFiles1,$signerPage0)}",1702211,"指定页码需为正整数！"]
          - ["TC5-机构静默签单文档  指定页码超出页码范围  报错",$signFiles0,$signFiles0,"${autoAdapter(3,$signFiles0,$signerMore)}",1702251,"指定单页签时，页码不可超出最大页码！"]
          - ["TC6-机构静默签单文档  印章是法人章 报错",$signFiles0,$signFiles0,"${autoAdapter(4,$signFiles0,$signerOrgAndSealDiff)}",1702204,"机构签署区不可落法人章！"]
          - ["TC62-机构静默签单文档  印章和人员不匹配 报错",$signFiles0,$signFiles0,"${autoAdapter(4,$signFiles0,$signerOrgAndSealDiff2)}",1702494,"sealId错误"]
          - ["TC7-机构静默签多文档  没有默认章 报错",$signFiles1,$signFiles0,"${autoAdapter(4,$signFiles1,$signerNotDef)}",1702443,"当前用户不是当前企业章用印人：sign01"]
          - ["TC8-机构静默签单文档  关键字超出20字符 报错",$signFiles0,$signFiles0,"${autoAdapter(4,$signFiles0,$signerKeylen20)}",1702245,"若为关键字签署，则单个关键字长度不可超出30字符！"]
          - ["TC9-机构静默签多文档  关键字超出20字符 报错",$signFiles1,$signFiles0,"${autoAdapter(4,$signFiles1,$signerKeylen20)}",1702245,"若为关键字签署，则单个关键字长度不可超出30字符！"]
          - ["TC8-机构静默签单文档  指定印章未发布 报错",$signFiles0,$signFiles0,"${autoAdapter(4,$signFiles0,$signerNotPublicSeal)}",1702494,"sealId错误：印章{1743105261169836034}不存在"]
          - ["TC9-机构静默签多文档  指定印章未发布 报错",$signFiles1,$signFiles0,"${autoAdapter(4,$signFiles1,$signerNotPublicSeal)}",1702494,"sealId错误：印章{1743105261169836034}不存在"]

    testcase: testcases/signs/signFlow/autoSignFlow/internalOrganizeFailAutoSignTC.yml

