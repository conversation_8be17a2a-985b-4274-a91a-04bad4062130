config:
    name: 内部个人静默签
    variables:
        #filekey
        fileKey30Page: "$${ENV(fileKey)}"
        # 单文件 签署文件
        signFiles0: [{"fileKey":"$fileKey30Page"}]
        # 签署文件
        signFiles1: [{"fileKey":"$fileKey30Page"},{"fileKey":"$${ENV(3PageFileKey)}"}]
        # 没有默认章的用户编码
        sign02UserCode: "${ENV(userCodeNoSeal)}"
        userCodeSigner0: "${ENV(sign01.userCode)}"

testcases:
-
    name: 内部个人静默签 - 报错用例
    parameters:
      - name-signFiles-attachments-signerInfos-code-message:
          - ["TC1-个人静默签 自由签 报错",$signFiles0,$signFiles0,[{"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],1702123,"静默签必须指定签署位置！"]
#          - ["TC2-个人静默签 未指定印章 没有默认章 报错",$signFiles0,$signFiles0, [{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$sign02UserCode"}],1709109,"签署人:sign02 无默认印章"]
          - ["TC3-个人静默签 多文档 部分未指定位置 报错",$signFiles1,$signFiles0, [{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],1702532,"发起失败：静默签必须指定签署区"]
          - ["TC4-个人静默签单文档 骑缝  报错",$signFiles0,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1-20","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],200,"成功"]
          - ["TC5-个人静默签多文档 骑缝  报错",$signFiles1,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1-20","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2}}]},{"fileKey":"${ENV(3PageFileKey)}","signConfigs":[{"posX":"300","posY":"300","sealId":"","pageNo":"1-20","signType":"EDGE-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],200,"成功"]
          - ["TC6-个人静默签单文档 指定未发布印章  报错",$signFiles0,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"${ENV(userSealNoPublic)}","pageNo":"1-20","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],1702349,"当前印章未发布！"]
          - ["TC7-个人静默签多文档 指定未发布印章  报错",$signFiles1,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"${ENV(userSealNoPublic)}","pageNo":"1-20","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2}}]},{"fileKey":"${ENV(3PageFileKey)}","signConfigs":[{"posX":"300","posY":"300","sealId":"${ENV(userSealNoPublic)}","pageNo":"1-20","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],1702349,"当前印章未发布！"]
          - ["TC8-个人静默签单文档 指定吊销印章  报错",$signFiles0,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"${ENV(userSealDelete)}","pageNo":"1-20","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":false,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCodeSigner0"}],1702494,"不存在"]
          - ["TC9-个人静默签单文档 超出长度为30的关键字 报错",$signFiles0,$signFiles0,[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"","pageNo":"1-30","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试 TEST12345678901234 弋弌弍弎弐弑 測試","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],1702245,"若为关键字签署，则单个关键字长度不可超出30字符！"]
    testcase: testcases/signs/signFlow/autoSignFlow/internalPersonFailAutoSignTC.yml

