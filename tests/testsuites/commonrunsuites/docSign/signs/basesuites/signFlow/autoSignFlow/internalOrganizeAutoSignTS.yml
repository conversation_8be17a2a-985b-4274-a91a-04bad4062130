config:
    name: 内部机构静默签
    variables:
        fileKey0: $${ENV(fileKey)}
        # 单文件 签署文件
        signFiles0: [{"fileKey":$fileKey0}]
        # 多文件 签署文件
        signFiles1: [{"fileKey":$fileKey0},{"fileKey": "$${ENV(3PageFileKey)}" }]
        # 多文件 签署文件 骑缝 偶数页 至少4页
        signFiles2: [{"fileKey":$fileKey0},{"fileKey": "$${ENV(3000PageFileKey)}"}]
        # 多文件 签署文件 关键字 相同的文件便于关键字解析
#        signFileskey: [{"fileKey":$fileKey0},{"fileKey":"${getFilekeyByType2(10)}"}]
        # 签署文件
        attachments0: [{"fileKey":$fileKey0},{"fileKey": "$${ENV(3PageFileKey)}" }]
        # 签署人编码
        signerUserCode0: "${ENV(sign01.userCode)}"
        # 指定2个位置
        signer: [{"lenConfig":2}]
        # 指定2个位置
        signer10: [{"lenConfig":10}]
        # 指定页码
        signerPage: [{"pageNo":"1,2,3-10"}]
        # 指定页码
        signerPageNoTime: [{"pageNo":"1,2,3-10","addSignDate":False}]
        # 骑缝签全部页
        signerEdgeAll: [{"pageNo":"","edgeScope":"0","addSignDate":False,"signType":"EDGE-SIGN"}]
        # 骑缝签全部页
        signerEdgeOdd: [{"pageNo":"","edgeScope":"1","addSignDate":False,"signType":"EDGE-SIGN"}]
        # 骑缝签全部页
        signerEdgeEven: [{"pageNo":"","edgeScope":"2","addSignDate":False,"signType":"EDGE-SIGN"}]
        # 关键字所有页
        signerKeyAll: [{"addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9"}]}]
        # 关键字指定页码范围
        signerKeyPage: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9"}]}]
        # 关键字指定页码范围 取第一个
        signerKeyPageFirst: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9","keywordIndex":1}]}]
        # 关键字指定页码范围 取最后一个
        signerKeyPageLast: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9","keywordIndex":-1}]}]
        # 关键字指定页码范围 偏移超出文档
        signerKeyPageOffsetOutOf: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9","keywordIndex":1,"offsetPosX":"20000"}]}]
        # 关键字长度20
        signerKeylen20: [{"addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"测试关键字极限长度 20 字符： 测试："}]}]
        # 10个关键字
        signerKey10: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"测"},{"keyword":"试"},{"keyword":"关"},{"keyword":"键"},{"keyword":"字"},{"keyword":"极"},{"keyword":"限"},{"keyword":"长"},{"keyword":"度"}]}]
        # 特殊字符
        signerKeyTS: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"~"},{"keyword":"！"},{"keyword":"@"},{"keyword":"#"},{"keyword":"$"},{"keyword":"%"},{"keyword":"&"},{"keyword":"["},{"keyword":"{"}]}]
        # 生僻 繁体字符
        signerKeySP: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"巑"},{"keyword":"測"},{"keyword":"試"},{"keyword":"巗"},{"keyword":"巘"},{"keyword":"巙"},{"keyword":"巚"},{"keyword":"奨"},{"keyword":"奰"}]}]
        # 关键字指定页码范围 取最后一个
        signerKeyPageOutOfIndex: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"9","keywordIndex":9999}]}]
        # 不同的机构章 1518498555586715650
        signerSealMore: [{"sealId":"${ENV(orgSealId2)}"},{"sealId":"${ENV(org01.sealId)}"}]
        # 关键字指定页码范围
        signerOffsetDiff: [{"pageNo":"1-2","addSignDate":False,"signType":"KEYWORD-SIGN","keyArry":[{"keyword":"测","offsetPosX":100,"offsetPosY":100},{"keyword":"试"}]}]
        # 不同印章id
        signerMoreSealId: [{"pageNo":"1",signerConfig:[{"childSealId":"${ENV(org01.sealId)}"},{"childSealId":"${ENV(orgSealId2)}"}]}]
        # 含法人签
        signerLegal: [{"legalSignFlag":True,"pageNo":"1",signerConfig:[{"childSealId":"${ENV(org01.sealId)}"},{"childSealId":"${ENV(org01.legal.sealId)}","childSignatureType":"LEGAL-PERSON-SEAL"}]}]
        # 含个人签
        signerPerson: [{"legalSignFlag":True,"pageNo":"1",signerConfig:[{"childSealId":"${ENV(org01.sealId)}"},{"childSealId":"${ENV(sign01.sealId)}","childSignatureType":"PERSON-SEAL"}]}]
        # 含个人签 + 法人签
        signerAllSealInfo: [{"legalSignFlag":True,"pageNo":"1",signerConfig:[{"childSealId":"${ENV(org01.sealId)}"},{"childSealId":"${ENV(org01.legal.sealId)}","childSignatureType":"LEGAL-PERSON-SEAL"},{"childSealId":"${ENV(sign01.sealId)}","childSignatureType":"PERSON-SEAL"}]}]
        # 指定页码
        signerWidth: ["signerConfig":[{"width":"10"},{"width":"200"}]]
        # 含法人签 取默认章
        signerLegalDef: [{"legalSignFlag":True,"pageNo":"1",signerConfig:[{"childSignatureType":"COMMON-SEAL","childSealId":""},{"childSealId":"","childSignatureType":"LEGAL-PERSON-SEAL"}]}]
        # 骑缝签 页码 0-20
        signerEdgePageNo: [{"pageNo":"1-20","addSignDate":False,"signType":"EDGE-SIGN"}]




testcases:
-
    name: 内部机构静默签
    parameters:
      - name-signFiles-attachments-signerInfos-code-message:
          - ["TC1-内部机构单文档 指定2个位置 含签署时间 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signer)}",200,"成功"]
          - ["TC2-内部机构多文档 指定2个位置 含签署时间 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signer)}",200,"成功"]
          - ["TC3-内部机构单文档 指定10个位置 含签署时间 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signer10)}",200,"成功"]
          - ["TC4-内部机构多文档 指定10个位置 含签署时间 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signer10)}",200,"成功"]
          - ["TC5-内部机构单文档 页面格式为1,2,3-10 含签署时间 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerPage)}",200,"成功"]
          - ["TC6-内部机构多文档 页码格式为1,2,3-10 含签署时间 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerPage)}",200,"成功"]
          - ["TC7-内部机构单文档 页面格式为1,2,3-10 不含签署时间 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerPageNoTime)}",200,"成功"]
          - ["TC8-内部机构多文档 页码格式为1,2,3-10 不含签署时间 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerPageNoTime)}",200,"成功"]
          - ["TC9-内部机构单文档 骑缝 全部页 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerEdgeAll)}",200,"成功"]
          - ["TC10-内部机构多文档 骑缝 全部页 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerEdgeAll)}",200,"成功"]
          - ["TC11-内部机构单文档 骑缝 奇数页 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerEdgeOdd)}",200,"成功"]
          - ["TC12-内部机构多文档 骑缝 奇数页 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerEdgeOdd)}",200,"成功"]
          - ["TC13-内部机构单文档 骑缝 偶数页 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerEdgeEven)}",200,"成功"]
          - ["TC14-内部机构多文档 骑缝 偶数页 成功",$signFiles2,$attachments0,"${autoAdapter(3,$signFiles2,$signerEdgeEven)}",200,"成功"]
          - ["TC15-内部机构单文档 关键字 所有页 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyAll)}",200,"成功"]
          - ["TC16-内部机构多文档 关键字 所有页 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyAll)}",200,"成功"]
          - ["TC17-内部机构单文档 关键字 指定页码范围 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyPage)}",200,"成功"]
          - ["TC18-内部机构多文档 关键字 指定页码范围 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyPage)}",200,"成功"]
          - ["TC19-内部机构单文档 关键字 指定页码范围第一个关键字 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyPageFirst)}",200,"成功"]
          - ["TC20-内部机构多文档 关键字 指定页码范围第一个关键字 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyPageFirst)}",200,"成功"]
          - ["TC21-内部机构单文档 关键字 指定页码范围最后一个关键字 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyPageLast)}",200,"成功"]
          - ["TC22-内部机构多文档 关键字 指定页码范围最后一个关键字 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyPageLast)}",200,"成功"]
          - ["TC23-内部机构单文档 关键字 指定页码范围偏移超出文档 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyPageOffsetOutOf)}",200,"成功"]
          - ["TC24-内部机构多文档 关键字 指定页码范围偏移超出文档 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyPageOffsetOutOf)}",200,"成功"]
          - ["TC25-内部机构单文档 关键字 关键字长度为20 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeylen20)}",200,"成功"]
#          - ["TC26-内部机构多文档 关键字 关键字长度为20 成功",$signFileskey,$attachments0,"${autoAdapter(3,$signFileskey,$signerKeylen20)}",200,"成功"]
          - ["TC27-内部机构单文档 关键字 10个关键字 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKey10)}",200,"成功"]
#          - ["TC28-内部机构多文档 关键字 10个关键字 成功",$signFileskey,$attachments0,"${autoAdapter(3,$signFileskey,$signerKey10)}",200,"成功"]
          - ["TC29-内部机构单文档 关键字 特殊字符 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyTS)}",200,"成功"]
#          - ["TC30-内部机构多文档 关键字 特殊字符 成功",$signFileskey,$attachments0,"${autoAdapter(3,$signFileskey,$signerKeyTS)}",200,"成功"]
          - ["TC29-内部机构单文档 关键字 生僻字 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeySP)}",200,"成功"]
#          - ["TC30-内部机构多文档 关键字 生僻字 成功",$signFileskey,$attachments0,"${autoAdapter(3,$signFileskey,$signerKeySP)}",200,"成功"]
          - ["TC31-内部机构单文档 关键字 指定页码范围指定超出范围取最后一个 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerKeyPageOutOfIndex)}",200,"成功"]
          - ["TC32-内部机构多文档 关键字 指定页码范围指定超出范围取最后一个 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerKeyPageOutOfIndex)}",200,"成功"]
          - ["TC33-内部机构单文档 2个关键字 偏移量不同 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerOffsetDiff)}",200,"成功"]
#          - ["TC34-内部机构多文档 2个关键字 偏移量不同 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerOffsetDiff)}",200,"成功"]
          - ["TC35-内部机构单文档 指定不同的印章id 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerMoreSealId)}",200,"成功"]
          - ["TC36-内部机构单文档 含法人章 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerLegal)}",200,"成功"]
          - ["TC37-内部机构多文档 含法人章 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerLegal)}",200,"成功"]
          - ["TC38-内部机构单文档 含个人章 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerPerson)}",200,"成功"]
          - ["TC39-内部机构单文档 含个人章+法人章 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerAllSealInfo)}",200,"成功"]
          - ["TC40-内部机构单文档 指定签署区宽度 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerWidth)}",200,"成功"]
          - ["TC42-内部机构单文档 含个人章+法人章默认章 成功",$signFiles1,$attachments0,"${autoAdapter(4,$signFiles1,$signerLegalDef)}",200,"成功"]
          - ["TC43-内部机构单文档 骑缝页码范围0-20 成功",$signFiles0,$attachments0,"${autoAdapter(3,$signFiles0,$signerEdgePageNo)}",200,"成功"]
          - ["TC44-内部机构多文档 骑缝页码范围0-20 成功",$signFiles1,$attachments0,"${autoAdapter(3,$signFiles1,$signerEdgePageNo)}",200,"成功"]


    testcase: testcases/signs/signFlow/autoSignFlow/internalOrganizeAutoSignTC.yml



-
   name: 内部机构静默签-判断印章有无授权项目和自动签署
   testcase: testcases/signs/signFlow/autoSignFlow/internalOrganizeAutoSign.yml

