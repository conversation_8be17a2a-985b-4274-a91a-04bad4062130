config:
    name: 内部个人静默签
    variables:
        #filekey
        fileKey30Page: $${ENV(fileKey)}
        #第二个 filekey
        fileKey30Page1: "$${ENV(3PageFileKey)}"
        # 10个关键字
        keyword10: ["测试","windows","1","2","4","5","6","7","8","9"]
        # 特殊字符关键字 {"\\", "/", "*", "?", "\"", "<", ">", "|", ":"
        keywordT10: ["~","!","@","#","$","%","^","&","(",")","_","+","{","}","'",".",",",";","[","]","=","-","`","（","）","·","！","@","#","￥","%","……","&","——","+","、","；","‘","，","。","、","《","》","：","“","©","™","®","℗"]
        # 生僻 繁体字
        keywordS10: ["測","試","蕘","垚","犇","骉","镳","䶮","叕","囍","囎","呝"]
        # 第二个印章
        sealId0: ${ENV(sign01.cloud.guomi.SealId)}
        userCode001: ${ENV(sign01.userCode)}
        orgCode001: ${ENV(sign01.main.orgCode)}
        userCode001_sealId: ${ENV(sign01.sealId)}
        orgCode001_sealId: ${ENV(org01.sealId)}
        # 多个签署人 第一个节点会签 第二个节点或签  userCode-organizeCode-signMode-autoSign-sealId
        signer: [[["$userCode001","","1",true,"$userCode001_sealId"],["$userCode001","$orgCode001","1",true,"$orgCode001_sealId"]],
                 [["${ENV(signjz01.userCode)}","","2",false,""],["${ENV(signjz01.userCode)}","$orgCode001","2",false,""]]]


testcases:
-
    name: 内部个人静默签
    parameters:
      - name-signFiles-attachments-signerInfos-code-message:
          - ["TC1-个人静默签单文档 单页 含签署日期 yyyy-MM-dd 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC2-个人静默签多文档 单页 含签署日期 yyyy年MM月dd日 成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC3-个人静默签单文档 多页 1,2-3 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1,2-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC4-个人静默签多文档 多页 1,2-3 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1,2-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"1,2-3","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC5-个人静默签单文档 所有页 关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC6-个人静默签多文档 所以页 关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC7-个人静默签单文档 指定页码 2-5 关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC8-个人静默签多文档 指定页码 2-5 关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC9-个人静默签单文档 指定页码 2-5 指定最后一个关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC10-个人静默签多文档 指定页码 2-5 指定最后一个关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC11-个人静默签单文档 所有页码 10个关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keyword10)}" }],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC12-个人静默签多文档 所有页码 10个关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keyword10)}" },{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC13-个人静默签单文档 长度为20的关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC14-个人静默签多文档 长度为20的关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC15-个人静默签单文档 所有页码 特殊字符关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keywordT10)}" }],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC16-个人静默签多文档 所有页码 特殊字符关键字 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keywordT10)}" },{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC17-个人静默签单文档 指定页码 2-5 指定最后一个关键字 偏移超出文档范围 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10000","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC18-个人静默签多文档 指定页码 2-5 指定最后一个关键字 偏移超出文档范围 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10000","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10000","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC19-个人静默签单文档 指定页码 2-5 关键字含空格 含签署日期  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC20-个人静默签多文档 指定页码 2-5 关键字含空格 含签署日期  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC21-个人静默签单文档 生僻 繁体 关键字  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keywordS10)}" }],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC22-个人静默签多文档 生僻 繁体 关键字  成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs": "${autoAdapter(2,$keywordS10)}" },{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC23-个人静默签单文档 相同关键字重复关键字 偏移量不同 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}},{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"40","offsetPosY":"10"}} ]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC24-个人静默签多文档 相同关键字重复关键字 偏移量不同 成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}},{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"2-5","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"-1","offsetPosX":"40","offsetPosY":"10"}} ]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],1702109,"在《key30page.pdf》内未找到关键字【测  试】"]
          - ["TC25-个人静默签单文档 超出个数的指定位置的关键字 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC26-个人静默签多文档 超出个数的指定位置的关键字 成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"300","posY":"300","sealId":"$userCode001_sealId","pageNo":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC27-个人静默签单文档 指定位置超出文档范围 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"0","posY":"20000","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC28-个人静默签多文档 指定位置超出文档范围 成功",[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"0","posY":"20000","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}}]},{"fileKey":"$fileKey30Page1","signConfigs":[{"posX":"0","posY":"30000","sealId":"$userCode001_sealId","pageNo":"","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"20","posX":"450","posY":"450","sealSignDateFormat":2},"keywordInfo":{"keyword":"标准","keywordIndex":"-1","offsetPosX":"10","offsetPosY":"10"}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC29-个人静默签单文档 指定不同的印章id 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"100","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}},{"posX":"200","posY":"400","sealId":"$sealId0","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}} ]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC30-个人静默签单文档 2个签署 签署域大小不同 成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"width":"50","posX":"100","posY":"200","sealId":"$userCode001_sealId","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}},{"width":"20","posX":"200","posY":"400","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}} ]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]
          - ["TC32-个人静默签单文档 指定页码范围超出最大页码  成功",[{"fileKey":"$fileKey30Page"}],[{"fileKey":"$fileKey30Page"},{"fileKey":"$fileKey30Page1"}],[{"sealInfos":[{"fileKey":"$fileKey30Page","signConfigs":[{"posX":"200","posY":"200","sealId":"$userCode001_sealId","pageNo":"1-999","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":1}}]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"$userCode001"}],200,"成功"]

    testcase: testcases/signs/signFlow/autoSignFlow/internalPersonAutoSignTC.yml

