config:
    name: 静默签-->多节点-->复杂用例
    variables:
        #filekey
        fileKey30Page: "$${ENV(fileKey)}"
        #第二个 filekey :必须也是关键字文件
#        fileKey30Page1: $${ENV(3PageFileKey)}
#        fileKey30Page1: "${get_filekey_by_filename(key30page.pdf)}"
        # 外部账号
        outUserCode: "${ENV(wsignwb01.userCode)}"
        # 内部账号
        innerUserCode: "${ENV(csqs.userCode)}"
        # 内部机构
        innerOrgCode: "${ENV(csqs.orgCode)}"
        # 单文档
        signFiles0: [{"fileKey":"$fileKey30Page"}]
        # 多份文档
        signFiles1: [{"fileKey":"$fileKey30Page"},{"fileKey": "$${get_filekey_by_filename(key30page.pdf)}"}]
        #内部机构无序签静默签--同一经办人不同机构或签
        signerConfig: [{"userCode":""}]
        #抄送人
        CCInfo: []
        # 节点1无序个人(关键字 最后一个关键字)静默签+机构(骑缝全部页)静默签 ----> 节点2个人静默签(默认章) ----> 机构静默签+个人+法人 (默认章)
        signerInfo1: [
            {"signNode":"3","signMode":"1","organizeCode":"","signConfigs": [{"childSealId":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keyArry":[{"keyword":"测试","keywordIndex":"-1"}]}]},
            {"signNode":"3","signMode":"1","signConfigs": [{"signType":"EDGE-SIGN","addSignDate":False,"pageNo":"","edgeScope":"0"}]},
            {"signNode":"1","signMode":"0","organizeCode":"","userCode":"$innerUserCode","signConfigs":[{"childSealId":"","signatureType":"PERSON-SEAL"}]},
            {"signNode":"2","signMode":"0","legalSignFlag":True,"organizeCode":"$innerOrgCode","userCode":"$innerUserCode","signConfigs": [{"childSealId":"","signatureType":"PERSON-SEAL"},{"childSealId":"","signatureType":"COMMON-SEAL"},{"childSealId":"","signatureType":"LEGAL-PERSON-SEAL"}]}
        ]
        # 节点1无序个人+关键字 静默 ---> 节点2 机构 骑缝+奇数+偶数 静默---> 节点3 个人+机构手动签无序 ---> 节点4外部个人+机构或签
        signerInfo2: [
            {"autoSign":True,"signNode":"1","signMode":"0","organizeCode":"","signConfigs":[{"childSealId":"${ENV(sign01.sealId)}","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keyArry":[{"keyword":"测试","keywordIndex":"99999"}]}]},
            {"autoSign":True,"signNode":"2","signMode":"0","signConfigs":[{"addSignDate":False,"signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","pageNo":"","edgeScope":"1"},{"addSignDate":False,"signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","pageNo":"","edgeScope":"2"}]},
            {"autoSign":False,"signNode":"3","signMode":"1","userCode":"$innerUserCode","organizeCode":"","signConfigs":[{"pageNo":"1-30","childSealId":"","signatureType":"PERSON-SEAL"}]},
            {"autoSign":False,"signNode":"3","signMode":"1","userCode":"$innerUserCode","organizeCode":"$innerOrgCode","signConfigs":[{"pageNo":"1-30","childSealId":""}]},
            {"autoSign":False,"userType":2,"signNode":"4","signMode":"2","userCode":$outUserCode,"organizeCode":"","signConfigs":[{"pageNo":"1-30","childSealId":"","signatureType":"PERSON-SEAL"}]},
            {"autoSign":False,"userType":2,"signNode":"4","signMode":"2","userCode":$outUserCode,"organizeCode":"${ENV(worg01.orgCode)}","signConfigs":[{"pageNo":"1-30","childSealId":""}]}
        ]
        # 节点1外部个人+机构手动或签 ---> 节点2个人+机构静默签 ---> 节点3 内部个人+机构无序签
        signerInfo3: [
            {"autoSign":False,"signNode":"3","signMode":"1","userCode":"$innerUserCode","organizeCode":"","signConfigs":[{"childSealId":"","signType":"KEYWORD-SIGN","signatureType":"PERSON-SEAL","keyArry":[{"keyword":"测试","keywordIndex":"99999"}]}]},
            {"autoSign":False,"signNode":"3","signMode":"1","userCode":"$innerUserCode","organizeCode":"$innerOrgCode","signConfigs":[{"childSealId":"","addSignDate":False,"signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","pageNo":"","edgeScope":"1"},{"childSealId":"","addSignDate":False,"signType":"EDGE-SIGN","signatureType":"COMMON-SEAL","pageNo":"","edgeScope":"2"}]},
            {"autoSign":True,"signNode":"2","signMode":"1","organizeCode":"","signConfigs":[{"pageNo":"1-30","childSealId":"","signatureType":"PERSON-SEAL"}]},
            {"autoSign":True,"signNode":"2","signMode":"1","signConfigs":[{"pageNo":"1-30","childSealId":""}]},
            {"autoSign":False,"userType":2,"signNode":"1","signMode":"2","userCode":"$outUserCode","organizeCode":"","signConfigs":[{"pageNo":"1-30","childSealId":"","signatureType":"PERSON-SEAL"}]},
            {"autoSign":False,"userType":2,"signNode":"1","signMode":"2","userCode":"$outUserCode","organizeCode":"${ENV(worg01.orgCode)}","signConfigs":[{"pageNo":"1-30","childSealId":""}]}
        ]


testcases:
-
    name: 静默签-->多节点-->复杂用例
    parameters:
      - name-signFiles-attachments-signerInfos-CCInfos-executeArry-code-message:
          - ["TC1-多节点单文档 无序+个人+机构 成功",$signFiles0,[],"${autoAdapter(5,$signFiles0,$signerInfo1)}",$CCInfo,[0],200,"成功"]
          - ["TC2-多节点多文档 无序+个人+机构 成功",$signFiles1,[],"${autoAdapter(5,$signFiles1,$signerInfo1)}",$CCInfo,[0],200,"成功"]
#          - ["TC3-多节点单文档 个人静默+机构静默+内部个人-机构手动无序签+外部个人-机构手动或签 成功",$signFiles0,[],"${autoAdapter(5,$signFiles0,$signerInfo2)}",$CCInfo,[0,3,4,5],200,"成功"]
#          - ["TC4-多节点多文档 个人静默+机构静默+内部个人-机构手动无序签+外部个人-机构手动或签 成功",$signFiles1,[],"${autoAdapter(5,$signFiles1,$signerInfo2)}",$CCInfo,[0,3,4,5],200,"成功"]
#          - ["TC5-多节点单文档 外部-个人或签+内部-个人静默签+内部+个人无序签 成功",$signFiles0,[],"${autoAdapter(5,$signFiles0,$signerInfo3)}",$CCInfo,[5,0,3,4],200,"成功"]
#          - ["TC6-多节点多文档 外部-个人或签+内部-个人静默签+内部+个人无序签 成功",$signFiles1,[],"${autoAdapter(5,$signFiles1,$signerInfo3)}",$CCInfo,[5,0,3,4],200,"成功"]
    testcase: testcases/signs/signFlow/autoSignFlow/multiNodeAutoSignTC.yml

