config:
    name: 获取签署流程列表用例集


testcases:
-
         name: 获取签署流程列表BusinessNo、Subject、SignFlowId用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListBusinessNoSubjectSignFlowId.yml

-
         name: 获取签署流程列表Initiator用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListInitiator.yml
-
         name: 获取签署流程列表PageNo、PageSize用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListPageNoSize.yml

-
         name: 获取签署流程列表signFlowListProjectId用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListProjectId.yml

-
         name: 获取签署流程列表signFlowListStatus用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListStatus.yml

-
         name: 获取签署流程列表Time用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListTime.yml

-
         name: 获取签署流程列表signFlowListInitiator用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListInitiator.yml

-
         name: 获取签署流程列表signFlowListDynamicCode用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListDynamicCode.yml

-
         name: 获取签署流程列表signFlowListTC用例
         testcase: testcases/signs/signFlow/signFlowList/signFlowListTC.yml
