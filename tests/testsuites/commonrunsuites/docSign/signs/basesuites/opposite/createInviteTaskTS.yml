config:
    name: 企业相对方成员列表查看

testcases:
-
         name: 邀请注册openapi-不开启强制实名场景校验-指定邀请人信息全部传入-企业用户不存在-企业、个人账号生成规则为证件号、手机号
         testcase: testcases/signs/opposite/inviteRegister/createInviteTaskTC1.yml

-
         name: 邀请注册openapi-不开启强制实名场景校验-仅传入企业名称-企业存在、用户不存在-企业规则为1，用户为0
         testcase: testcases/signs/opposite/inviteRegister/createInviteTaskTC2.yml

-
         name: 邀请注册openapi-不开启强制实名场景校验-仅传入企业证件类型、证件号-企业存在、用户存在、用户不在所属企业-企业规则为1，用户为1
         testcase: testcases/signs/opposite/inviteRegister/createInviteTaskTC3.yml

-
         name: 邀请注册openapi-新增参数businessNo，并在回调信息中增加参数businessNo
         testcase: testcases/signs/opposite/inviteRegister/createInviteTaskTC4.yml