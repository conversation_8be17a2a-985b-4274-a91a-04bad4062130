config:
    name: 个人相对方列表查询
    variables:
        - outerUserCode: ${ENV(wsignwb01.userCode)}
        - outerUserName: ${getOuterUsersName($outerUserCode)}
        - accountNo: ${getOuterUsersAccountNo($outerUserCode)}

testcases:
-
    name: 个人相对方列表查询
    parameters:
      - name-pageSize-pageNo-userName-account-status-message-success:
          - ["默认情况", 10, 1, "", "", 200, "成功", true]
          - ["切换到下一页", 10, 2, "", "", 200, "成功", true]
          - ["同时按姓名和账号查询 ", 10, 1, "${outerUserName}", "${accountNo}", 200, "成功", true]
          - ["pageSize为字母", a, 1, "", "", 1703012, "pageSize字段类型不匹配", False]
          - ["pageSize传入-1", -1, 1, "", "", 913, "查询参数错误，请检查", False]
          - ["pageSize传入0", 0, 1, "", "", 913, "查询参数错误，请检查", False]
          - ["pageSize为空", "", 1, "", "", 1703001, "页大小不能为空", False]
          - ["pageSize为null", Null, 1, "", "", 1703001, "页大小不能为空", False]
          - ["pageNo为字母", 10, a, "", "", 1703012, "pageNo字段类型不匹配", False]
          - ["pageNo传入-1", 10, -1, "", "", 913, "查询参数错误，请检查", False]
          - ["pageNo传入0", 10, 0, "", "", 913, "查询参数错误，请检查", False]
          - ["pageNo不传", 10, Null, "", "", 1703001, "页码不能为空", False]
          - ["pageNo为空", 10, "", "", "", 1703001, "页码不能为空", False]
          - ["已删除的个人相对方", 10, 1, "${get_oppositePerson(username)}", "", 200, "成功", true]
    testcase: testcases/signs/opposite/person/page/pageTC.yml
-
    name: 按姓名搜索
    testcase: testcases/signs/opposite/person/page/pageUserNameTC.yml
-
    name: 按账号搜索
    testcase: testcases/signs/opposite/person/page/pageAccountTC.yml

-
    name: 按手机号邮箱证件号搜索
    testcase: testcases/signs/opposite/person/page/pageUserEmailPhoneLicenseNoTC.yml
