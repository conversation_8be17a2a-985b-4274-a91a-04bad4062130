testcases:
-
    name: 个人相对方详情
    variables:
        - userCode0: ${ENV(wsignwb01.userCode)}
    parameters:
        - name-userCode-status-message-success:
          - [ "参数正确",$userCode0,200,"成功",true ]
          - [ "userCode不存在",adfa31da,1702045,"未知的账号信息！",false ]
          - [ "userCode传的空的","",1702045,"未知的账号信息！",false ]
          - [ "userCode传null",null,1702045,"未知的账号信息！",false ]
    testcase: testcases/signs/opposite/person/infoTC.yml
-
    name: 异常场景补充
    testcase: testcases/signs/opposite/person/infoTCAbnormal.yml