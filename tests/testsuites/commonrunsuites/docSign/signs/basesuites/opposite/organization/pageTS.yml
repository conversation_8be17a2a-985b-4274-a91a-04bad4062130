config:
    name: 企业相对方列表查询
    variables:
        - outerOrgCode: ${ENV(worg01.orgCode)}
        - organizationNo: ${getOuterOrganizationNo($outerOrgCode)}

testcases:
-
    name: 企业相对方列表查询
    parameters:
      - name-pageSize-pageNo-organizationName-organizationAccount-status-message-success:
          - ["默认情况", 10, 1, "", "",200,"成功", true]
          - ["切换到下一页", 10, 2, "", "",200,"成功", true]
          - ["按企业名称查询", 10, 1, "${ENV(sign01.main.orgName)}", "",200,"成功", true]
          - ["按企业账号查询", 10, 1, "", "${organizationNo}",200,"成功", true]
          - ["同时按企业名称和账号查询", 10, 1, "${ENV(sign01.main.orgName)}", "${organizationNo}",200,"成功", true]
          - ["不存在的organizationAccount", 10, 1, "", "errorOrgAccount",200,"成功", true]
          - ["不存在的organizationName", 10, 1, "errorOrgName", "",200,"成功", true]
          - ["不存在的organizationAccount+存在的organizationName", 10, 1, "${ENV(sign01.main.orgName)}", "errorOrgAccount",200,"成功", true]
          - ["存在的organizationAccount+不存在的organizationName", 10, 1, "errorOrgName", "${organizationNo}",200,"成功", true]
          - ["pageSize为-1", -1, 1, "", "",1701005,"fromIndex", False]
          - ["pageSize为0", -1, 1, "", "",1701005,"fromIndex", False]
          - ["pageSize为字母", a, 1, "", "",1703012,"pageSize字段类型不匹配", False]
          - ["pageSize为null", Null, 1, "", "",1701005,None, False]
          - ["pageSize为空", Null, 1, "", "",1701005,Null, False]
          - ["pageNo为字母", 10, a, "", "",1703012,"pageNo字段类型不匹配", False]
          - ["pageNo为0", 10, 0, "", "",1701005,"fromIndex = -10", False]
          - ["pageNo为-1", 10, -1, "", "",1701005,"fromIndex = -20", False]
          - ["pageNo为空", 10, "", "", "",1701005,Null, False]
          - ["pageNo为Null", 10, Null, "", "",1701005,"页码大小不能为空", False]
          - [ "已删除的企业相对方", 10, 1, "${get_oppositePerson(organizationname)}", "", 200, "成功", true ]
    testcase: testcases/signs/opposite/organization/page/pageTC.yml
-
    name: 按企业名称查询
    testcase: testcases/signs/opposite/organization/page/pageUserNameTC.yml
-
    name: 按企业账号查询
    testcase: testcases/signs/opposite/organization/page/pageAccountTC.yml