config:
    name: 企业相对方成员列表查看
    variables:
      - outerOrgCode: ${ENV(worg01.orgCode)}
      - outOrgId: ${getOuterOrganizationId($outerOrgCode)}
      - userName0: ${getUserName()}

testcases:
-
    name: 企业相对方成员列表查看
    variables:
        - orgId: ${outOrgId}
    parameters:
        - name-pageSize-pageNo-organizationId-userName-account-status-message-success:
          - ["默认情况", 10, 1, $orgId, "", "",200, "成功", true]
          - ["切换到下一页", 10, 2, $orgId, "", "",200, "成功", true]
          - ["输入正常的成员姓名查询", 10, 1, $orgId, $userName0, "",200, "成功", true]
#          - ["一个成员归属于A,B两个企业", 10, 1, $orgId2, $userName0, "",200, "成功", true]
          - ["输入正常的成员账号查询", 10, 1, $orgId, "", $userName0,200, "成功", true]
          - ["同时按成员姓名和成员账号查询", 10, 1, $orgId, $userName0, "${ENV(wsignwb01.accountNo)}",200, "成功", true]
          - ["organizationId不传", 10, 1, Null, "", "",200, "成功", true]
          - ["organizationId为空", 10, 1, "", "", "", 200, "成功", true]
          - ["organizationId不存在", 10, 1, "asdasdsadaqw1qw", "", "", 200, "成功", True]
          - ["pageSize为-1", -1, 1, $orgId, "", "", 913, "查询参数错误，请检查", False]
          - ["pageSize为0", 0, 1, $orgId, "", "", 913, "查询参数错误，请检查", False]
          - ["pageSize为不传", Null, 1, $orgId, "", "", 1703001, "页大小不能为空", False]
          - ["pageSize为字母", "a", 1, $orgId, "", "", 1703012, "pageSize字段类型不匹配", False]
#          - ["pageNo为-1", 10, -1, $orgId, "", "", 1701005, "fromIndex = -20", False]
#          - ["pageNo为0", 10, 0, $orgId, "", "", 1701005, "fromIndex = -10", False]
          - ["pageNo为-1", 10, -1, $orgId, "", "", 913, "查询参数错误，请检查", False]
          - ["pageNo为0", 10, 0, $orgId, "", "", 913, "查询参数错误，请检查", False]
          - ["pageNo不传", 10, Null, $orgId, "", "", 1703001, "页码不能为空", False]
          - ["pageNo为字母", 10, "a", $orgId, "", "", 1703012, "pageNo字段类型不匹配", False]
    testcase: testcases/signs/opposite/organization/members/membersTC.yml
-
    name: 按成员姓名查看
    testcase: testcases/signs/opposite/organization/members/membersUsernameTC.yml
-
    name: 按成员账号查看
    testcase: testcases/signs/opposite/organization/members/membersAccountTC.yml

-
    name: 异常场景补充
    testcase: testcases/signs/opposite/organization/members/membersAbnormal.yml