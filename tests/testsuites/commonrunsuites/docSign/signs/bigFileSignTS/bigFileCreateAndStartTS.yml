config:
    name: 一步发起-大文件签署校验
#    variables:
      # 指定2个位置
#      signer: [{ "lenConfig": 2 }]
#      fileKey0: ${ENV(fileKey)}
#      fileKey1: ${ENV(1PageFileKey)}
#      fileKey3: ${ENV(fileKey99MB)}
#      fileKey4: ${ENV(fileKey332MB)}
#      fileKey5: ${ENV(fileKey665MB)}
#      signerInfos0: [{"signNode":1,"sealInfos":[{"fileKey":"$fileKey0"}],"userType":1,"userCode":"${ENV(sign01.userCode)}"}]
#      # 大文件 签署文件
#      signFilesBig0: [{ "fileKey": "$fileKey3" }]
#      signFilesBig1: [{ "fileKey": "$fileKey5" }]

testcases:
#-
#    name: 一步发起-校验-attachment-signFiles
#    parameters:
#      - name-attachments-signFiles-signerInfos-code-message:
#          - ["TC1-签署文档单份大于100M，报错",$signFilesBig0,[{"fileKey":" $fileKey4 ","fileOrder":1}],[{"signNode":1,"sealInfos":[{"fileKey":"$fileKey4"}],"userType":1,"userCode":"${ENV(sign01.userCode)}"}],200,"成功"]
#          - ["TC2-附件单份大于500M，报错",$signFilesBig1,$signFilesBig0,$signerInfos0,1702112,"流程附件文件不可超出500MB!"]
#          - ["TC3-附件单份小于500M，但是总的大于1G，报错",[{ "fileKey": " $fileKey4 " },{ "fileKey": " $fileKey3" }],$signFilesBig1,$signerInfos0,1702111,"流程文件总计不可超出1024MB!" ]
#    testcase: testcases/signs/signFlow/createAndStart/attachmentsignFilesTC.yml

#-
#    name: 内部机构静默签
#    parameters:
#      - name-signFiles-attachments-signerInfos-code-message:
#          - ["TC4-内部机构单文档 文件大小100 成功",$signFilesBig0,[],"${autoAdapter(3,$signFilesBig0,$signer)}",200,"成功"]
#    testcase: testcases/signs/signFlow/autoSignFlow/internalOrganizeAutoSignTC.yml
#
#-
#    name: 内部个人静默签
#    parameters:
#      - name-signFiles-attachments-signerInfos-code-message:
#          - ["TC5-个人静默签单文档 100M 成功",$signFilesBig0,[{"fileKey":"$fileKey0"},{"fileKey":"$fileKey1"}],[{"sealInfos":[{"fileKey":"$fileKey3","signConfigs":[{"width":"50","posX":"100","posY":"200","sealId":"${ENV(sign01.sealId)}","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}},{"width":"20","posX":"200","posY":"400","sealId":"","pageNo":"1","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL","addSignDate":true,"sealSignDatePositionInfo":{"fontSize":"30","posX":"400","posY":"400","sealSignDateFormat":2},"keywordInfo":{"keyword":"测试","keywordIndex":"9999","offsetPosX":"10","offsetPosY":"10"}} ]}],"signNode":2,"userType":1,"autoSign":true,"userCode":"${ENV(sign01.userCode)}"}],200,"成功"]
#    testcase: testcases/signs/signFlow/autoSignFlow/internalPersonAutoSignTC.yml

#-
#      name: 大文件预览-332MB
#      parameters:
#          - name-processId-status-message-success:
#            - [ "TC6-获取大文件签署预加载", "${get_largeFile_process()}", 200,"成功",true]
#      testcase: testcases/signs/preloading/preloadingTC.yml
-
    name: 一步发起-校验-subject-businessNo
    parameters:
      - name-subject-businessNo-code-message:

          - ["TC12-业务编码允许不传"," ${generate_random_str(5)} ",NULL,200,"成功"]

    testcase: testcases/signs/signFlow/createAndStart/subjectBusinessNoTC.yml