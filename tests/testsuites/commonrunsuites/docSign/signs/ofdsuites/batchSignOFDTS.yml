config:
  variables:
    - project_id: ${ENV(esign.projectId)}
    - param_ofd: [ 1,0 ]
    - param_mix: [ 2,0 ]

testcases:
  - name: 批量签署-批量签署结果反馈列表-batchTaskUuId
    parameters:
      - name-batchTaskUuId-status-message:
          - [ "TC5-batchTaskUuId传入正确的ofd的值，成功","${gen_simple_batch_sign($param_ofd)}",200,"成功" ]
          - [ "TC6-batchTaskUuId传入的值包含pdf和ofd流程，成功","${gen_simple_batch_sign($param_mix)}",200,"成功" ]
    testcase: testcases/signs/process/batchSign/result/batchTaskUuIdTC.yml

  - name: 批量签署TC10
    testcase: testcases/signs/process/batchSign/ofd/batchSignTC10.yml

  - name: 批量签署OFD-info
    testcase: testcases/signs/process/batchSign/ofd/batchSignOFDTC.yml

  - name: 批量签署OFD-info-portal
    testcase: testcases/signs/portal/task/batchSignTC10-OFD.yml