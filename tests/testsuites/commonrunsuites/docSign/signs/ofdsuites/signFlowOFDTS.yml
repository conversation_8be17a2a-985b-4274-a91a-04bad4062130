config:
  name: 一步发起-校验-attachment-signFiles
  variables:
    fileKey0: $${ENV(fileKey)}
    attachments0: [ ]
    userCode0: ${ENV(sign01.userCode)}
    signFiles0: [ { "fileKey": " $fileKey0 ","fileOrder": 1 } ]
    signerInfos0: [ { "signNode": 1,"sealInfos": [ { "fileKey": "$fileKey0" } ],"userType": 1,"userCode": "$userCode0" } ]
    fileKeyOfd: $${ENV(ofdFileKey)}

testcases:
  - name: 一步发起-校验-attachment-signFiles
    parameters:
      - name-attachments-signFiles-signerInfos-code-message:
          - [ "TC17-签署文档fileKey支持ofd",$signFiles0,[ { "fileKey": " ${fileKeyOfd} ","fileOrder": 1 } ],[ { "signNode": 1,"sealInfos": [ { "fileKey": "${fileKeyOfd}" } ],"userType": 1,"userCode": "$userCode0" } ],200,"成功" ]
          - [ "TC30-签署文档不允许同时有pdf和ofd，报错",$signFiles0,[ { "fileKey": " $fileKey0 ","fileOrder": 2 },{ "fileKey": " ${fileKeyOfd} ","fileOrder": 10 } ],$signerInfos0,1702119,"同一流程不能同时存在PDF文件与OFD文件！" ]
    testcase: testcases/signs/signFlow/createAndStart/attachmentsignFilesTC.yml