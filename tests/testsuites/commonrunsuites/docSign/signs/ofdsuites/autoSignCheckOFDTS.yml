config:
    name: 静默签-->多节点-->验证文件必签用例，改动点:1、企业用章主体章只需要加载在任意一份文件上就可以;2、静默签署不再必须签署所有签署文件
    variables:
        #filekey
        fileKey01: "$${ENV(ofdFileKey)}"
        #第二个 filekey
        fileKey02: "$${ENV(1PageOFDFileKey)}"
        fileKey03: "$${ENV(3PageOFDFileKey)}"
        # 多份文档
        signFiles0: [ { "fileKey": "$fileKey01" },{ "fileKey": "$fileKey02" },{ "fileKey": "$fileKey03" } ]
        # 相对方账号
        outUserNo: "${ENV(wsignwb01.accountNo)}"
        # 内部账号
        innerUserNo: "${ENV(csqs.accountNo)}"
        innerUserNo2: "${ENV(sign01.accountNo)}"
        # 内部企业
        innerOrgNo: "${ENV(sign01.main.orgNo)}"
        innerOrgNo2: "${ENV(csqs.orgNo)}"
        # 相对方企业
        outOrgNo: "${ENV(wsignwb01.main.orgNo)}"

        # 节点1静默签署无序:内部企业O1经办人P1签署F1
        signerInfo0: [
            { "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" }
        ]

        # 节点1静默签署无序:内部企业O1经办人P1签署F1+内部企业O1经办人P2签署F2+个人P1签署F3
        signerInfo1: [
            {"signMode":1,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey01","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]}],"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo2","customOrgNo":"$innerOrgNo"},
            {"signMode":1,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey02","signConfigs":[{"posX":"300","posY":"300","pageNo":"1","signType":"COMMON-SIGN","signatureType":"COMMON-SEAL"}]}],"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo","customOrgNo":"$innerOrgNo"},
            {"signMode":1,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey03","signConfigs":[{"posX":"100","posY":"100","pageNo":"1-2","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo2"}
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章
        signerInfo2: [{"signMode":0,"signNode":1,"autoSign":1,"sealInfos":[
            {"fileKey":"$fileKey01","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},
            {"fileKey":"$fileKey02","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]},
            {"fileKey":"$fileKey03","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"legalSignFlag":1,"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo2","customOrgNo":"$innerOrgNo"}
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章+内部企业O1经办人P2-F1经办人用章，需要报错的
        signerInfo3: [
            {"signMode":1,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey01","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"EDGE-SIGN","signatureType":"COMMON-SEAL"}]},{"fileKey":"$fileKey02","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"COMMON-SIGN","signatureType":"LEGAL-PERSON-SEAL"}]},{"fileKey":"$fileKey03","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"legalSignFlag":1,"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo2","customOrgNo":"$innerOrgNo"},
            {"signMode":1,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey01","signConfigs":[{"posX":"200","posY":"200","pageNo":"1-5","signType":"COMMON-SIGN","signatureType":"PERSON-SEAL"}]}],"userType":1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo":"$innerUserNo","customOrgNo":"$innerOrgNo"}
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署F1,F2,F3,需要报错
        signerInfo4: [
            {"signMode":0,"signNode":1,"autoSign":1,"sealInfos":[{"fileKey":"$fileKey01"},{"fileKey":"$fileKey02"},{"fileKey":"$fileKey03"}],"userType":2,"tspId": " ESIGN_WITNESS_TSP","customAccountNo":"$outUserNo"}
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署F1,F2,F3,需要报错
        signerInfo5: [
            { "signMode": 1,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 1,"signNode": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01" },{ "fileKey": "$fileKey02" },{ "fileKey": "$fileKey03" } ],"userType": 2,"tspId": " ESIGN_WITNESS_TSP","customAccountNo": "$outUserNo" }
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署不指定任何文件,发起成功
        signerInfo6: [
            { "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 0,"signNode": 2,"autoSign": 0,"sealInfos": [],"userType": 2,"tspId": " ESIGN_WITNESS_TSP","customAccountNo": "$outUserNo" }
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署F2,F3,发起成功
        signerInfo7: [
            { "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 0,"signNode": 2,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey02","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] },{ "fileKey": "$fileKey03" } ],"userType": 2,"tspId": " ESIGN_WITNESS_TSP","customAccountNo": "$outUserNo" }
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署F1,F2,F3,需要报错
        signerInfo51: [
            { "signMode": 1,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 1,"signNode": 1,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey01" },{ "fileKey": "$fileKey02" },{ "fileKey": "$fileKey03" } ],"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2" }
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署不指定任何文件,发起成功
        signerInfo61: [
            { "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 0,"signNode": 2,"autoSign": 0,"sealInfos": [],"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo" }
        ]
        # 节点1静默签署无序:内部企业O1经办人P1-F1企业用章-F2法人用章-F3经办人用章，节点2：相对方手动签署-自由签署F2,F3,发起成功
        signerInfo71: [
            { "signMode": 0,"signNode": 1,"autoSign": 1,"sealInfos": [ { "fileKey": "$fileKey01","signConfigs": [ { "posX": "200","posY": "200","pageNo": "1-5","signType": "EDGE-SIGN","signatureType": "COMMON-SEAL" } ] } ],"legalSignFlag": 1,"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo2","customOrgNo": "$innerOrgNo" },
            { "signMode": 0,"signNode": 2,"autoSign": 0,"sealInfos": [ { "fileKey": "$fileKey02","signConfigs": [ { "posX": "100","posY": "300","pageNo": "1","signType": "COMMON-SIGN","signatureType": "PERSON-SEAL" } ] },{ "fileKey": "$fileKey03" } ],"userType": 1,"tspId": " LOCAL_DEFAULT_TSP","customAccountNo": "$innerUserNo"}
        ]
testcases:
-
    name: OFD-一步发起静默签-->验证企业用章改动
    parameters:
      - name-signFiles-signerInfos-code-message:
          - ["TC0-三份签署文件一个签署方，只签署其中一份文件，报错",$signFiles0,$signerInfo0,1702532,"发起失败：静默签必须指定签署区"]
          - ["TC1-一个签署方静默签署企业用章只需要加载在任意一份文件中,成功",$signFiles0,$signerInfo2,200,"成功"]
          - ["TC2-节点1静默签署无序:内部企业O1经办人P1签署F1+内部企业O1经办人P2签署F2+个人P1签署F3,成功",$signFiles0,$signerInfo1,200,"成功"]
          - ["TC3-多个企业签署方，必须多个主体用章,校验报错",$signFiles0,$signerInfo3,1702518,"至少设置一个{企业或法人}签署区"]
          - ["TC4-相对方静默签署,校验报错",$signFiles0,$signerInfo4,1702183,"外部用户不可发起静默签！"]
          - ["TC5-签署方静默签署的文件，另一个签署方对其自由签署,校验报错",$signFiles0,$signerInfo5,1702527,"指定签署区"]
          - ["TC6-签署方静默签署的文件，另一个签署方不指定任何文件签署,发起成功",$signFiles0,$signerInfo6,200,"成功"]
          - ["TC7-签署方静默签署的文件，另一个签署方指定其他文件签署,发起成功",$signFiles0,$signerInfo7,200,"成功"]
    testcase: testcases/signs/signFlow/createAndStart/autoSignTC.yml

-
    name: OFD-分步发起静默签-->验证企业用章改动(未开启流程的时候添加签署方)
    parameters:
      - name-signerInfos-code-message:
          - ["TC0-三份签署文件一个签署方，只签署其中一份文件，无节点应该成功",$signerInfo0,200,"成功"]
          - ["TC1-一个签署方静默签署企业用章只需要加载在任意一份文件中,成功",$signerInfo2,200,"成功"]
          - ["TC2-节点1静默签署无序:内部企业O1经办人P1签署F1+内部企业O1经办人P2签署F2+个人P1签署F3,成功",$signerInfo1,200,"成功"]
          - ["TC3-多个企业签署方，必须多个主体用章,校验报错",$signerInfo3,1702518,"至少设置一个{企业或法人}签署区"]
          - ["TC4-相对方静默签署,校验报错",$signerInfo4,1702183,"外部用户不可发起静默签！"]
          - ["TC5-签署方静默签署的文件，另一个签署方对其自由签署,校验报错",$signerInfo51,1702527,"指定签署区"]
          - ["TC6-签署方静默签署的文件，另一个签署方不指定任何文件签署,报错",$signerInfo61,17026537,"未指定签署文件"]
          - ["TC7-签署方静默签署的文件，另一个签署方指定其他文件签署,发起成功",$signerInfo71,200,"成功"]
    testcase: testcases/signs/signFlow/create/autoSignCreateOFDTC.yml


