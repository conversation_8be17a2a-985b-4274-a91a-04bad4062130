config:
  name: 业务调用保存协作



testcases:
  -
    name: 业务调用保存协作 saveCooperation_businessClass
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_businessClass.yml

  -
    name: 业务调用保存协作 saveCooperation_callbackParam
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_callbackParam.yml

  -
    name: 业务调用保存协作 saveCooperation_callbackUrl
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_callbackUrl.yml

  -
    name: 业务调用保存协作 saveCooperation_isSubmit
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_isSubmit.yml

  -
    name: 业务调用保存协作 saveCooperation_todoTaskId
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_todoTaskId.yml

  -
    name: 业务调用保存协作 saveCooperation_workflowInstanceId
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_workflowInstanceId.yml





