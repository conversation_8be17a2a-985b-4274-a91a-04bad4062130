config:
  name: 业务调用提交协作




testcases:
  -
    name: 业务调用保存协作 submitCooperation_businessClass
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_businessClass.yml

  -
    name: 业务调用保存协作 submitCooperation_callbackParam
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_callbackParam.yml

  -
    name: 业务调用保存协作 submitCooperation_callbackUrl
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_callbackUrl.yml

  -
    name: 业务调用保存协作 submitCooperation_isSubmit
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_isSubmit.yml

  -
    name: 业务调用保存协作 submitCooperation_todoTaskId
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_todoTaskId.yml

  -
    name: 业务调用保存协作 submitCooperation_workflowInstanceId
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_workflowInstanceId.yml