config:
  name: 获取用户信息
  variables:
    userCode: 用户编码
    accountNumber: ${ENV(userCode)}
    password: ${ENV(password_ceshy)}
    authorization0: ${getPortalToken($accountNumber, $password)}

testcases:
-
    name: 根据用户Code获取用户信息userCode参数校验
    testcase: testcases/seals/manage/orguser/user/getUserByCode/getUserAllOrgList_userCode.yml

-
    name: 根据机构id集合和用户名称匹配机构以及以下所有的用户信息8wkT7F
    testcase: testcases/seals/manage/orguser/user/getUserByOrgIdAndUserName/getUserByOrgIdAndUserName-organizationTerritory.yml

-
    name: 根据机构id集合和用户名称匹配机构以及以下所有的用户信息HLEhI0
    testcase: testcases/seals/manage/orguser/user/getUserByOrgIdAndUserName/getUserByOrgIdAndUserName-orgIdList.yml

-
    name: 根据机构id集合和用户名称匹配机构以及以下所有的用户信息Uhnhin
    testcase: testcases/seals/manage/orguser/user/getUserByOrgIdAndUserName/getUserByOrgIdAndUserName-userName.yml

-
    name: 获取当前用户的国际化语言
    testcase: testcases/seals/manage/orguser/user/getUserLanguage/getUserLanguage_case.yml

-
    name: 获取用户列表allChildOrganizationFlag参数
    testcase: testcases/seals/manage/orguser/user/getUserListByOrganizationID/getUserListByOrganizationID_allChildOrganizationFlag.yml

-
    name: 通过组织id获取用户列表organizationId参数
    testcase: testcases/seals/manage/orguser/user/getUserListByOrganizationID/getUserListByOrganizationID_organizationId.yml

-
    name: 获取用户列表userName参数校验
    testcase: testcases/seals/manage/orguser/user/getUserListByOrganizationID/getUserListByOrganizationID_userName.yml

