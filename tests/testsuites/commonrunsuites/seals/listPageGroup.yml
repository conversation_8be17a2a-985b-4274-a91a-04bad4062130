config:
  name: 查询企业的分组卡片
  variables:
    organizationCode1: ${ENV(sign01.main.orgCode)}
testcases:
  - name: 查询企业的分组卡片，校验分页配置 #todo
    parameters:
      - name-currPage-pageSize-defaultSeal-status0-message0-data0:
          - [ "currPage验证，currPage=1",1,10,false,200,"成功","" ]
          - [ "currPage验证，currPage=-1",-1,10,false,1300000,"当前页号不能小于1","1" ]
          - [ "currPage验证，currPage=0.05",0.05,10,false,1300000,"当前页号不能小于1","1" ]
          - [ "currPage验证，currPage=true",true,10,false,1322222,"不支持传入特殊字符","1" ]
    testcase: testcases/seals/seals/smc/seals/listPageGroup.yml

  - name: 查询企业的分组卡片，校验其他配置 #todo
    parameters:
      - name-remoteSealId-sealGroupName-sealName-sealPatterns-sealStatus-sealTypeCode-showChildOrganizeSeal-ukeySn-status0-message0-data0:
          - [ "remoteSealId印章ID，为空","","","",[ ],"","",true,"",200,"成功","" ]
    testcase: testcases/seals/seals/smc/seals/listPageGroup.yml