config:
  name: 验证企业证书（验证企业是否有有效的云证书，是否需要继续申请？-6.0.11.0-beta.1）
testcases:
  - name: 验证企业证书-已有有效的云证书
    parameters:
    - name-organizationCode-certAlgorithm-status-message:
        - ["TC-企业存在正常状态的RSA云证书","${ENV(sign01.main.orgCode)}","1",200,"成功"]
        - ["TC-企业存在正常状态的SM2云证书","${ENV(sign01.main.orgCode)}","2",200,"成功"]

    testcase: testcases/seals/certs/enterprise/saveEnterpriseCert/checkEnterpriseCertcase.yml
