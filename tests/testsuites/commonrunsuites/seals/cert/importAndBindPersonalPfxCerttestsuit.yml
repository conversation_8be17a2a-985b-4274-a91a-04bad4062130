config:
  name: 导入并绑定个人pfx离线证书-导入证书到系统中
  variables:
      bindPfxUserCode1: ${ENV(sign01.userCode)}
      NotRealNameUserCode: ${ENV(sign07.userCode)}

testcases:
  - name: (个人证书-我的证书)导入并绑定个人pfx离线证书-导入证书到系统中-测试集
    parameters:
    - bindCaseName-pfxFileName-pfxCertFilePassword-pfxCertName-userCode-certAlgorithmEnum-bindResultCode-bindResultMessage:
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为RSA，输入正确密码-导入到系统中成功","测试rsa重复导入pfx证书autotest-personal.pfx","12345678","测试rsa重复导入pfx证书autotest-personal",$bindPfxUserCode1,"1",200,"成功"]
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为SM2，输入正确密码确认-导入到系统中成功","测试sm2重复导入pfx证书autotest-personal.pfx","12345678","测试sm2重复导入pfx证书autotest-personal",$bindPfxUserCode1,"2",200,"成功" ]
    testcase: testcases/seals/certs/personal/importPersonalCerFile/importAndbindPersonalPfxCertcase.yml

  - name: (个人证书-我的证书）导入并绑定个人pfx离线证书-导入证书到系统中导入失败-异常测试集
    parameters:
    - bindCaseName-pfxFileName-pfxCertFilePassword-pfxCertName-userCode-bindResultCode-bindResultMessage:
#      - [ "TC-导入pfx离线证书弹窗-未实名个人-导入失败","测试rsa重复导入pfx证书autotest-personal.pfx","12345678","测试rsa重复导入pfx证书autotest-personal",$NotRealNameUserCode,1316033,"个人未实名"]
      - [ "TC-导入的pfx证书文件已过期-可以导入展示文件信息但证书不能导入到系统中","过期证书.pfx","123456","徐根英_过期+2.5.4.13=徐根英_证书过期",$bindPfxUserCode1,1316027,"证书已过期" ]
    testcase: testcases/seals/certs/personal/importPersonalCerFile/importAndbindPersonalPfxCert-unnomalCase.yml

  - name: (个人证书-我管理的）导入并绑定个人pfx离线证书-导入证书到系统中-测试集
    parameters:
    - bindCaseName-pfxFileName-pfxCertFilePassword-pfxCertName-userCode-certAlgorithmEnum-bindResultCode-bindResultMessage:
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为RSA，输入正确密码-导入到系统中成功","测试rsa重复导入pfx证书autotest-personal.pfx","12345678","测试rsa重复导入pfx证书autotest-personal",$bindPfxUserCode1,"1",200,"成功"]
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为SM2，输入正确密码确认-导入到系统中成功","测试sm2重复导入pfx证书autotest-personal.pfx","12345678","测试sm2重复导入pfx证书autotest-personal",$bindPfxUserCode1,"2",200,"成功" ]
    testcase: testcases/seals/certs/personal/ownerManager/importPersonalCerFile/importAndbindPersonalPfxCertcase.yml

  - name: (个人证书-我管理的）导入并绑定个人pfx离线证书-导入证书到系统中导入失败-异常测试集
    parameters:
    - bindCaseName-pfxFileName-pfxCertFilePassword-pfxCertName-userCode-bindResultCode-bindResultMessage:
      - [ "TC-导入pfx离线证书弹窗-未实名个人-导入失败","测试rsa重复导入pfx证书autotest-personal.pfx","12345678","测试rsa重复导入pfx证书autotest-personal",$NotRealNameUserCode,1316033,"个人未实名"]
      - [ "TC-导入的pfx证书文件已过期-可以导入展示文件信息但证书不能导入到系统中","过期证书.pfx","123456","徐根英_过期+2.5.4.13=徐根英_证书过期",$bindPfxUserCode1,1316027,"证书已过期" ]
    testcase: testcases/seals/certs/personal/ownerManager/importPersonalCerFile/importAndbindPersonalPfxCert-unnomalCase.yml