config:
  name: 导入个人pfx离线证书
  variables:
    wrongPasswordMessage: "证书信息解析失败"
    notFpxFileMessage: "证书信息解析失败"
testcases:
  - name: 导入个人pfx离线证书-正常用例集
    parameters:
    - name-pfxFileName-pfxCertFilePassword-pfxStartTime-pfxEndTime-sigAlgName-code-message:
      - [ "TC-导入离线证书-pfx文件无密码，不输入密码-导入成功","no-password.pfx","","2024-04-10 15:01:36","2025-04-10 15:01:36","RSA",200,"成功"]
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为RSA，输入正确密码-导入成功","测试rsa导入pfx证书autotest.pfx","12345678","2024-04-17 00:00:00","2028-04-17 00:00:00","RSA",200,"成功"]
      - [ "TC-导入离线证书-pfx文件有密码，算法类型为SM2，输入正确密码确认-导入成功","测试sm2重复导入pfx证书autotest.pfx","12345678","2024-04-17 00:00:00","2028-04-17 00:00:00","SM2",200,"成功" ]
      - [ "TC-导入的pfx证书文件已过期-可以导入展示文件信息但证书不能导入到系统中","过期证书.pfx","123456","2012-04-09 08:00:05","2012-05-08 18:00:05","RSA",200,"成功" ]
    testcase: testcases/seals/certs/personal/importPersonalCerFile/importPersonalPfxCertFilecase.yml


  - name: 导入个人pfx离线证书-异常用例集
    parameters:
    - name-pfxFileName-pfxCertFilePassword-code-message:
      - [ "TC-导入离线证书弹窗-pfx文件无密码，输入空格确认-导入失败","no-password.pfx","    ",1316006,$wrongPasswordMessage]
      - [ "TC-导入离线证书弹窗-pfx文件无密码，输入随机数字密码确认-导入失败","no-password.pfx","993382819101",1316006,$wrongPasswordMessage ]
      - [ "TC-导入离线证书弹窗-pfx文件有密码，不输入密码确认-导入失败","测试rsa导入pfx证书autotest.pfx","",1316006,$wrongPasswordMessage ]
      - [ "TC-导入离线证书弹窗-pfx文件有密码，输入随机非正确密码确认-导入失败","测试rsa导入pfx证书autotest.pfx","993382819101",1316006,$wrongPasswordMessage ]
      - [ "TC-异常-导入的为.pfx后缀文件非pfx证书-导入失败","非pfx文件.pfx","",1316006,$notFpxFileMessage ]
      - [ "TC-异常-导入的为其他格式文件例如.docx-导入失败","测试文档.docx","",1316005,"证书格式支持.cer .crt .pfx" ]
    testcase: testcases/seals/certs/personal/importPersonalCerFile/importPersonalPfxCertFile-unnomalCase.yml
