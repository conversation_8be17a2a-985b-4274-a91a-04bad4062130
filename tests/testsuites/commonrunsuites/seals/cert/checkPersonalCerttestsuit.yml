config:
  name: 验证个人证书（验证企业是否有有效的云证书，是否需要继续申请？-6.0.11.0-beta.1）
testcases:
  - name: 验证个人证书-已有有效的云证书
    parameters:
    - name-userCode-certAlgorithm-status-message:
        - ["TC-新建RSA证书：用户存在正常状态的RSA云证书","${ENV(sign01.userCode)}","1",200,"成功"]
        - ["TC-新建SM2证书：用户存在正常状态的SM2云证书","${ENV(sign01.userCode)}","2",200,"成功"]
    testcase: testcases/seals/certs/personal/savePersonalCert/checkPersonalCertcase.yml


  - name: 验证个人证书-无有效的云证书
    parameters:
    - name-userCode-certAlgorithm-status-message:
        - ["TC-新建RSA证书：个人无正常状态的RSA云证书，且无正常状态SM2云证书","${ENV(userCodeNoCert)}","1",200,"成功"]
        - ["TC-新建SM2证书：个人无正常状态的RSA云证书，且无正常状态SM2云证书","${ENV(userCodeNoCert)}","2",200,"成功"]

    testcase: testcases/seals/certs/personal/savePersonalCert/checkPersonalCert-nocert-case.yml
