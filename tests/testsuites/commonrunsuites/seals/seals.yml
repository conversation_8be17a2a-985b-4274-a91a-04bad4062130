config:
  name: 纯电子印控
testcases:
    -
        name: 自定义印章调整图片返回png图片（所有属性）-自定义角度
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealAngle.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）-印章颜色
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealColour.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）-印章清晰度
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealDefinition.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）-印章高度
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealHeight.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）-印章不透明度
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealOpacity.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）- 缩放
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealScale.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）- 印章图片地址
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealThumbnailUrl.yml
    -
        name: 自定义印章调整图片返回png图片（所有属性）- 印章宽度
        testcase: /testcases/seals/seals/enterprise/electronic/allCustomSealAdjustPng/sealWidth.yml

    -
        name: 自定义印章调整图片返回png图片（颜色、清晰度）-印章颜色
        testcase: /testcases/seals/seals/enterprise/electronic/customSealAdjustPng/sealColour.yml
    -
        name: 自定义印章调整图片返回png图片（颜色、清晰度）-印章清晰度
        testcase: /testcases/seals/seals/enterprise/electronic/customSealAdjustPng/sealDefinition.yml
    -
        name: 自定义印章调整图片返回png图片（颜色、清晰度）-印章图片地址
        testcase: /testcases/seals/seals/enterprise/electronic/customSealAdjustPng/sealThumbnailUrl.yml


    -
        name: 预览使用模板电子印章-sealBottomword
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealBottomword.yml
    -
        name: 预览使用模板电子印章-sealCenterImg
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealCenterImg.yml
    -
        name: 预览使用模板电子印章-sealColour
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealColour.yml
    -
        name: 预览使用模板电子印章-sealHorizontalOneText
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealHorizontalOneText.yml
    -
        name: 预览使用模板电子印章-sealHorizontalText
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealHorizontalText.yml
    -
        name: 预览使用模板电子印章-sealHorizontalTwoText
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealHorizontalTwoText.yml
    -
        name: 预览使用模板电子印章-sealShape
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealShape.yml
    -
        name: 预览使用模板电子印章-sealSurroundword
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/sealSurroundword.yml

    -
        name: 吊销印章
        testcase: /testcases/seals/seals/enterprise/electronic/revoke/sealId.yml

    -
        name: 预览使用模板电子印章-innerTopSurroundText
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/innerTopSurroundText.yml
    -
        name: 预览使用模板电子印章-oldStyle
        testcase: /testcases/seals/seals/enterprise/electronic/previewElectronicSeal/oldStyle.yml
    -
        name: 催办
        testcase: /testcases/seals/seals/enterprise/electronic/urgeTask/urgeTask_processInstanceId.yml

    -
        name: 获取手绘印章二维码
        testcase: /testcases/seals/seals/personal/base64QrCode/qrCodeUrl.yml
    -
        name: 删除个人印章
        testcase: /testcases/seals/seals/personal/deletePersonalSeal/id.yml
    -
        name: 下载文件-fileKey
        testcase: /testcases/seals/seals/personal/downloadFile/fileKey.yml
    -
        name: 下载文件-fileName
        testcase: /testcases/seals/seals/personal/downloadFile/fileName.yml
    -
        name: 获取手绘印章
        testcase: /testcases/seals/seals/personal/getHandPaintedSeal/pictureId.yml
    -
        name: 查看个人印章
        testcase: /testcases/seals/seals/personal/getPersonalSeal/id.yml
    -
        name: 我管的-个人印章分页列表-currPage
        testcase: /testcases/seals/seals/personal/ownerManager/pagePersonalSealList/currPage.yml
    -
        name: 我管的-个人印章分页列表-pageSize
        testcase: /testcases/seals/seals/personal/ownerManager/pagePersonalSealList/pageSize.yml
    -
        name:  我管的-个人印章分页列表-sealName
        testcase: /testcases/seals/seals/personal/ownerManager/pagePersonalSealList/sealName.yml
    -
        name: 我管的-个人印章分页列表-sealStatus
        testcase: /testcases/seals/seals/personal/ownerManager/pagePersonalSealList/sealStatus.yml
    -
        name: 个人印章分页列表-currPage
        testcase: /testcases/seals/seals/personal/pagePersonalSealList/currPage.yml
    -
        name: 个人印章分页列表-pageSize
        testcase: /testcases/seals/seals/personal/pagePersonalSealList/pageSize.yml
    -
        name: 个人印章分页列表-sealName
        testcase: /testcases/seals/seals/personal/pagePersonalSealList/sealName.yml
    -
        name: 个人印章分页列表-sealStatus
        testcase: /testcases/seals/seals/personal/pagePersonalSealList/sealStatus.yml
    -
        name: 个人印章预览oldStyle
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/oldStyle.yml
    -
        name: 个人印章预览sealBottomword
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/sealBottomword.yml
    -
        name: 个人印章预览sealColour
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/sealColour.yml
    -
        name: 个人印章预览sealOpacity
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/sealOpacity.yml
    -
        name: 个人印章预览sealShape
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/sealShape.yml
    -
        name: 个人印章预览sealUserName
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/sealUserName.yml
    -
        name: 个人印章预览stampRule
        testcase: /testcases/seals/seals/personal/previewPersonalSeal/stampRule.yml
    -
        name: 发布个人印章-id
        testcase: /testcases/seals/seals/personal/publishPersonalSeal/id.yml
    -
        name: 保存手绘印章-pictureId
        testcase: /testcases/seals/seals/personal/saveHandPaintedSeal/pictureId.yml
    -
        name: 保存手绘印章-qrCode
        testcase: /testcases/seals/seals/personal/saveHandPaintedSeal/qrCode.yml
    -
        name: 保存个人印章-customSealType
        testcase: /testcases/seals/seals/personal/savePersonalSeal/customSealType.yml
    -
        name: 保存个人印章-handPaintedSealId
        testcase: /testcases/seals/seals/personal/savePersonalSeal/handPaintedSealId.yml
    -
        name: 保存个人印章-id
        testcase: /testcases/seals/seals/personal/savePersonalSeal/id.yml
    -
        name: 保存个人印章-managerOrgCode
        testcase: /testcases/seals/seals/personal/savePersonalSeal/managerOrgCode.yml
    -
        name: 保存个人印章-managerOrgName
        testcase: /testcases/seals/seals/personal/savePersonalSeal/managerOrgName.yml
    -
        name: 保存个人印章-ownerCode
        testcase: /testcases/seals/seals/personal/savePersonalSeal/ownerCode.yml
    -
        name: 保存个人印章-ownerName
        testcase: /testcases/seals/seals/personal/savePersonalSeal/ownerName.yml
    -
        name: 保存个人印章-ownerOrganizationCode
        testcase: /testcases/seals/seals/personal/savePersonalSeal/ownerOrganizationCode.yml
    -
        name: 保存个人印章-sealBodyStructure
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealBodyStructure.yml
    -
        name: 保存个人印章sealColour
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealColour.yml
    -
        name: 保存个人印章sealDesc
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealDesc.yml
    -
        name: 保存个人印章sealHeight
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealHeight.yml
    -
        name: 保存个人印章sealName
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealName.yml
    -
        name: 保存个人印章sealSource
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealSource.yml
    -
        name: 保存个人印章sealStatus
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealStatus.yml
    -
        name: 保存个人印章sealUserName
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealUserName.yml
    -
        name: 保存个人印章sealWidth
        testcase: /testcases/seals/seals/personal/savePersonalSeal/sealWidth.yml
    -
        name: 设置个人默认印章
        testcase: /testcases/seals/seals/personal/setDefault/id.yml
    -
        name: 停用个人印章
        testcase: /testcases/seals/seals/personal/stopPersonalSeal/id.yml
    -
        name: 校验印章名称是否重复，印章保存、编辑时印章名称校验（true 重复 false未重复）-印章id
        testcase: /testcases/seals/seals/personal/validateSealName/id.yml
    -
        name: 校验印章名称是否重复，印章保存、编辑时印章名称校验（true 重复 false未重复）-印章名称
        testcase: /testcases/seals/seals/personal/validateSealName/sealName.yml
    -
        name:
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_innerTopSurroundText.yml
    -
        name: 预览使用模板电子印章oldStyle
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_oldStyle.yml

    -
        name: 预览使用模板电子印章sealBottomword
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealBottomword.yml
    -
        name: 预览使用模板电子印章sealCenterImg
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealCenterImg.yml

    -
        name: 预览使用模板电子印章sealColour
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealColour.yml
    -
        name: 预览使用模板电子印章sealHeight
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealHeight.yml
    -
        name: 预览使用模板电子印章sealHorizontalOneText
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealHorizontalOneText.yml
    -
        name: 预览使用模板电子印章sealHorizontalText
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealHorizontalText.yml
    -
        name: 预览使用模板电子印章sealHorizontalTwoText
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealHorizontalTwoText.yml
    -
        name: 预览使用模板电子印章 sealOpacity
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealOpacity.yml
    -
        name: 预览使用模板电子印章 印章形态
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealShape.yml
    -
        name: 预览使用模板电子印章-印章环绕文
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealSurroundword.yml
    -
        name: 预览使用模板电子印章-印章宽度
        testcase: /testcases/seals/seals/sealmodel/previewElectronicSeal_sealWidth.yml
    -
        name: 企业证件号回显
        testcase: /testcases/seals/seals/smc/certs/enterprise/licenseEnterpriseEcho/licenseEnterpriseEcho.yml
    -
        name: 个人证件号回显
        testcase: /testcases/seals/seals/smc/certs/personal/licensePersonalEcho/licensePersonalEcho.yml
    -
        name: 企业法人章根据印章体结构获取已发布个人章分页列表currPage
        testcase: /testcases/seals/seals/smc/seals/personal/pageLegalPersonalSealList/pageLegalPersonalSealList-currPage.yml
    -
        name: 企业法人章根据印章体结构获取已发布个人章分页列表pageSize
        testcase: /testcases/seals/seals/smc/seals/personal/pageLegalPersonalSealList/pageLegalPersonalSealList-pageSize.yml
    -
        name: 企业法人章根据印章体结构获取已发布个人章分页列表sealBodyStructure
        testcase: /testcases/seals/seals/smc/seals/personal/pageLegalPersonalSealList/pageLegalPersonalSealList-sealBodyStructure.yml
    -
        name: 企业法人章根据印章体结构获取已发布个人章分页列表sealName
        testcase: /testcases/seals/seals/smc/seals/personal/pageLegalPersonalSealList/pageLegalPersonalSealList-sealName.yml
    -
        name: 企业法人章根据印章体结构获取已发布个人章分页列表sealTypeOrgCode
        testcase: /testcases/seals/seals/smc/seals/personal/pageLegalPersonalSealList/pageLegalPersonalSealList-sealTypeOrgCode.yml

    -
        name: 查询用户的生效个人印章列表pageNo
        testcase: /testcases/seals/sealcontrols/userseals/list_pageNo.yml
    -
        name: 查询用户的生效个人印章列表pageSize
        testcase: /testcases/seals/sealcontrols/userseals/list_pageSize.yml
    -
        name: 查询用户的生效个人印章列表sealName
        testcase: /testcases/seals/sealcontrols/userseals/list_sealName.yml
    -
        name: 查询用户的生效个人印章列表sealPattern
        testcase: /testcases/seals/sealcontrols/userseals/list_sealPattern.yml
    -
        name: 查询用户的生效个人印章列表userCode
        testcase: /testcases/seals/sealcontrols/userseals/list_userCode.yml
    -
        name: 查询用户的生效个人印章列表customAccountNo
        testcase: /testcases/seals/sealcontrols/userseals/list_customAccountNo.yml