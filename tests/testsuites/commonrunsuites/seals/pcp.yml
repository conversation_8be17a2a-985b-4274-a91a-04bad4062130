config:
  name: pcp
testcases:
-
    name: 上传文件接口mdlrJg
    testcase: /testcases/seals/pcp/business/file/businessUploadFile/businessUploadFile_fileCode.yml

-
    name: 上传文件接口Vz2cwu
    testcase: /testcases/seals/pcp/business/file/businessUploadFile/businessUploadFile_fileName.yml

-
    name: 获得业务范围控制参数校验Ugqalzc
    testcase: /testcases/seals/pcp/business/scopecontrol/businessScopeParameter/businessScopeParameter_all.yml

-
    name: 获取所有汉字拼音首字母字符串chineseString校验gTZXNV
    testcase: /testcases/seals/pcp/thirdparty/pinyin/getAllChineseInitialPinyinStr/getAllChineseInitialPinyinStr_chineseString.yml

-
    name: 获取所有汉字拼音首字母字符串isLowerCase校验47m9v1
    testcase: /testcases/seals/pcp/thirdparty/pinyin/getAllChineseInitialPinyinStr/getAllChineseInitialPinyinStr_isLowerCase.yml

-
    name: 获取首个汉字全拼，其余汉字取拼音首字母字符串chineseString校验W0mU2T
    testcase: /testcases/seals/pcp/thirdparty/pinyin/getOneAndOtherChinesePinyinStr/getOneAndOtherChinesePinyinStr_chineseString.yml

-
    name: 获取首个汉字全拼，其余汉字取拼音首字母字符串isLowerCase校验cikSLX
    testcase: /testcases/seals/pcp/thirdparty/pinyin/getOneAndOtherChinesePinyinStr/getOneAndOtherChinesePinyinStr_isLowerCase.yml