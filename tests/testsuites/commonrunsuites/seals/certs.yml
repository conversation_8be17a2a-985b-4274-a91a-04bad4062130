config:
  name: 纯电子印控
testcases:
    -
        name: 是否存在过期的证书
        testcase: /testcases/seals/certs/certCommon/expire.yml
    -
        name: 延期企业云证书
        testcase: /testcases/seals/certs/enterprise/delayEnterpriseCert/delayEnterpriseCert.yml
    -
        name: 延期企业云证书-新增
        testcase: /testcases/seals/certs/enterprise/delayEnterpriseCert/delayEnterpriseCert_supply.yml
    -
        name: 企业证书下载req文件
        testcase: /testcases/seals/certs/enterprise/downloadEnterpriseReqFile/downloadEnterpriseReqFile.yml
    -
        name: 查询企业印章列表 - certAlgorithm 参数校验
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_certAlgorithm.yaml
    -
        name: 查询企业印章列表 - certName 参数校验
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_certName.yaml
    -
        name: 查询企业印章列表 - certName&organizationName&certAlgorithm&certStatus 多条件组合查询
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_certName_organizationName_certAlgorithm_certStatus.yaml
    -
        name: 查询企业印章列表 - certStatus
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_certStatus.yaml
    -
        name: 查询企业印章列表 - certType 参数校验
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_certType.yaml
    -
        name: 查询企业印章列表 - currPage
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_currPage.yaml
    -
        name: 查询企业印章列表 - domain
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_domain.yaml
    -
        name: 查询企业印章列表 - expire
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_expire.yaml
    -
        name: 查询企业印章列表 - organizationName
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_organizationName.yaml
    -
        name: 查询企业印章列表 - pageSize
        testcase: /testcases/seals/certs/enterprise/getEnterpriseCertList/getEnterpriseCertList_pageSize.yaml
    -
        name: 企业云证书导入离线证书
        testcase: /testcases/seals/certs/enterprise/importEnterpriseCerFile/importEnterpriseCerFile.yml
    -
        name: 企业证书吊销 - id
        testcase: /testcases/seals/certs/enterprise/revokeEnterpriseCert/revokeEnterpriseCert_id.yml
    -
        name: 保存证书_applyMethod
        testcase: /testcases/seals/certs/enterprise/saveEnterpriseCert/saveEnterpriseCert_applyMethod.yml
    -
        name: 保存证书_certAlgorithm
        testcase: /testcases/seals/certs/enterprise/saveEnterpriseCert/saveEnterpriseCert_certAlgorithm.yml
    -
        name: 保存证书_certName
        testcase: /testcases/seals/certs/enterprise/saveEnterpriseCert/saveEnterpriseCert_certName.yml
    -
        name: 保存证书_license
        testcase: /testcases/seals/certs/enterprise/saveEnterpriseCert/saveEnterpriseCert_license.yml
    -
        name: 企业证书查看接口
        testcase: /testcases/seals/certs/enterprise/selectEnterpriseCert/selectEnterpriseCert_id.yml
    -
        name: 延期个人云证书
        testcase: /testcases/seals/certs/personal/delayPersonalCert/delayPersonalCert.yml
    -
        name: 个人证书下载req文件接口_id
        testcase: /testcases/seals/certs/personal/downloadPersonalReqFile/downloadPersonalReqFile_id.yml
    -
        name: 个人证书列表_certType
        testcase: /testcases/seals/certs/personal/getPersonalCertList/certType.yml
    -
        name: 个人证书列表_certName
        testcase: /testcases/seals/certs/personal/getPersonalCertList/getPersonalCertList_certName.yml
    -
        name: 个人证书列表_certStatus
        testcase: /testcases/seals/certs/personal/getPersonalCertList/getPersonalCertList_certStatus.yml
    -
        name: 个人证书列表_expire
        testcase: /testcases/seals/certs/personal/getPersonalCertList/getPersonalCertList_expire.yml
    -
        name: 个人证书列表_userName
        testcase: /testcases/seals/certs/personal/getPersonalCertList/getPersonalCertList_userName.yml
    -
        name: 个人云证书导入离线证书
        testcase: /testcases/seals/certs/personal/importPersonalCerFile/importPersonalCerFile.yml
    -
        name: 延期个人云证书_我管理的
        testcase: /testcases/seals/certs/personal/ownerManager/delayPersonalCert/delayPersonalCert.yml
    -
        name: 延期个人云证书_我管理的_supply
        testcase: /testcases/seals/certs/personal/ownerManager/delayPersonalCert/delayPersonalCert_supply.yml
    -
        name: 个人云证书导入离线证书_我管理的_id
        testcase: /testcases/seals/certs/personal/ownerManager/downloadPersonalReqFile/downloadPersonalReqFile_id.yml
    -
        name: 个人证书列表_我管理的_certType
        testcase: /testcases/seals/certs/personal/ownerManager/getPersonalCertList/certType.yml
    -
        name: 个人证书列表_我管理的_certName
        testcase: /testcases/seals/certs/personal/ownerManager/getPersonalCertList/getPersonalCertList_certName.yml
    -
        name: 个人证书列表_我管理的_certStatus
        testcase: /testcases/seals/certs/personal/ownerManager/getPersonalCertList/getPersonalCertList_certStatus.yml
    -
        name: 个人证书列表_我管理的_expire
        testcase: /testcases/seals/certs/personal/ownerManager/getPersonalCertList/getPersonalCertList_expire.yml
    -
        name: 个人证书列表_我管理的_userName
        testcase: /testcases/seals/certs/personal/ownerManager/getPersonalCertList/getPersonalCertList_userName.yml
    -
        name: 个人云证书导入离线证书_我管理的
        testcase: /testcases/seals/certs/personal/ownerManager/importPersonalCerFile/importPersonalCerFile.yml
    -
        name: 个人证书吊销接口_我管理的
        testcase: /testcases/seals/certs/personal/ownerManager/revokePersonalCert/revokePersonalCert_id.yml
    -
        name: 保存个人证书_我管理的_applyMethod
        testcase: /testcases/seals/certs/personal/ownerManager/savePersonalCert/savePersonalCert_applyMethod.yml
    -
        name: 保存个人证书_我管理的__certAlgorithm
        testcase: /testcases/seals/certs/personal/ownerManager/savePersonalCert/savePersonalCert_certAlgorithm.yml
    -
        name: 保存个人证书_我管理的_certName
        testcase: /testcases/seals/certs/personal/ownerManager/savePersonalCert/savePersonalCert_certName.yml
    -
        name: 保存个人证书_我管理的_certType
        testcase: /testcases/seals/certs/personal/ownerManager/savePersonalCert/savePersonalCert_certType.yml
    -
        name: 保存个人证书_我管理的_license
        testcase: /testcases/seals/certs/personal/ownerManager/savePersonalCert/savePersonalCert_license.yml
    -
        name: 企业证书查看接口_我管理的_id
        testcase: /testcases/seals/certs/personal/ownerManager/selectPersonalCert/selectPersonalCert_id.yml
    -
        name: 个人证书吊销接口_id
        testcase: /testcases/seals/certs/personal/revokePersonalCert/revokePersonalCert_id.yml
    -
        name: 保存个人证书_applyMethod
        testcase: /testcases/seals/certs/personal/savePersonalCert/savePersonalCert_applyMethod.yml
    -
        name: 保存个人证书__certAlgorithm
        testcase: /testcases/seals/certs/personal/savePersonalCert/savePersonalCert_certAlgorithm.yml
    -
        name: 保存个人证书_certName
        testcase: /testcases/seals/certs/personal/savePersonalCert/savePersonalCert_certName.yml
    -
        name: 保存个人证书_certType
        testcase: /testcases/seals/certs/personal/savePersonalCert/savePersonalCert_certType.yml
    -
        name: 企业证书查看接口_id
        testcase: /testcases/seals/certs/personal/selectPersonalCert/selectPersonalCert_id.yml