config:
    name: 删除个人印章
    variables:
        sealId101: "abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a"
        #印章状态(1草稿 g发布 h停用 5吊销)
        userCode1: ${ENV(sign01.userCode)}
        sealId_Revoke: ${getUserSealsByStatus($userCode1, 5)}
        sealId_stop: ${getUserSealsByStatus($userCode1, h)}
        sealId_draf: ${getUserSealsByStatus($userCode1, 1)}
        sealId3: ${getUserSealsByStatus($userCode1, g)}
#
#        
testcases:
-
    name: 删除个人印章
    parameters:
      - name-sealId-code-message-data:
          - ["sealId为空","",1300000,"印章id不能为空",10]
          - ["删除草稿态印章",$sealId_draf,200,"成功","sealId"]
          - ["删除停用态印章",$sealId_stop,200,"成功","sealId"]
          - ["删除发布态印章",$sealId3,1313019,"不能删除",10]
          - ["sealId长度为101",$sealId101,1300000,"印章id不超过36个字",10]
          - ["删除吊销态印章",$sealId_Revoke,200,"成功","sealId"]
          - ["印章id不存在","abc",1313018,"该个人印章[abc]不存在",10]
    testcase: testcases/seals/v1/sealcontrols/userseals/delete.yml
