config:
    name: 授权组织
    variables:
         organizationCode: "58bec88e506142b0bf05342c68518827"


testcases:
-
    name: 电子印章授权组织
#    parameters:
#      - name-organizationCode-customAccountNo-userCode-customOrgNo:
#          - [ "MYTL-只填组织编码-组织账号，授权指定组织","$organizationCode","","","" ]
#          - [ "MYTL-填写多个组织编码-组织账号，授权指定多个组织（组织A,组织B,组织C）","","$organizationCode","","" ]
    testcase: testcases/seals/v1/sealcontrols/sealsigners/sealsignersAuthorizationScope.yml

-
  name: 电子印章授权重复组织编码
  testcase: testcases/seals/v1/sealcontrols/sealsigners/sealsignersAuthorizationScope_duplicateOrgnationCode.yml

-
  name: 电子印章授权多个组织编码
  testcase: testcases/seals/v1/sealcontrols/sealsigners/sealsignersAuthorizationScope_multipleOrgCode.yml

-
  name: 物理印章授权组织
  testcase: testcases/seals/v1/sealcontrols/physicsSealsigners/physicsSealsignersAuthorizationScope.yml

-
  name: 查询印章类型
  testcase: testcases/seals/sealcontrols/organizationSealTypes/list.yml

