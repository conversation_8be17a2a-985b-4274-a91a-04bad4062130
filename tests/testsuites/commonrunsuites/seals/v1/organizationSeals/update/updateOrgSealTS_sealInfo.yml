config:
  name: 更新企业印章校验-校验国际标准的印章更新明细
  variables:
    sign01Code: ${ENV(sign01.userCode)}
    org01Code: ${ENV(sign01.main.orgCode)}
    org02Code: ${ENV(csqs.orgCode)}
    sealIdUpdate0: ${createOrganizationSeals($org01Code, $sign01Code, 3)}
    sealIdUpdate1: ${createOrganizationSeals($org01Code, $sign01Code, 3)}
    sealIdUpdate2: ${createOrganizationSeals($org01Code, $sign01Code, 3, 3)}
    org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
    #        sealSignercertId: [{"sealSignercertId": $org01CertId}]
    #        sealIdUpdate1: ${createOrganizationSeals($org01Code, $sign01Code, 3, $org01CertId, $sealSignercertId)}
    org01CertId_rsa: ${getOrganizationCertsByStatus($org01Code)}
    org02CertId: ${getOrganizationCertsByStatus($org02Code,1,2)}
    sealOpacity: 99
    jpgFile: tests/data/111.jpg
    pngFile: tests/data/111.png
    bmpFile: tests/data/111.bmp
    xlsFile: tests/data/111.xls
    #        pdfFile: tests/data/30M-探索式测试实践之路.pdf
    base64file: ${get_file_base64($jpgFile)}
    base64file0: ${get_file_base64($pngFile)}
    base64file1: ${get_file_base64($bmpFile)}


testcases:
  - name: 更新企业印章-校验sealTemplateStyle,sealSource 非必填数据的校验
    parameters:
      - name-sealId-sealSource-sealTemplateStyle-base64img-code0-message0-data0:
          - [ "TC1-sealSource 非定义值0",$sealIdUpdate0,0,"","",1325015,"sealSource错误：印章来源{0}非法",0 ]
          - [ "TC2-sealSource 非定义值-1",$sealIdUpdate0,-1,"","",1325015,"sealSource错误：印章来源{",0 ]
          - [ "TC4-sealSource 非定义值True",$sealIdUpdate0,"True","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC5-sealSource 非定义值是",$sealIdUpdate0,"是","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC6-sealSource 非定义值3",$sealIdUpdate0,3,"","",1325015,"sealSource错误：印章来源{",0 ]
          - [ "TC7-sealSource 1-自定义印章",$sealIdUpdate0,1,"","",1325028,"自定义印章，印章fileKey和印章base64不能都为空",0 ]
          - [ "TC9-sealTemplateStyle 非定义值0",$sealIdUpdate0,2,0,"",1325017,"sealTemplateStyle错误：印章模版样式{0}非法",0 ]
          - [ "TC10-sealTemplateStyle 非定义值-1",$sealIdUpdate0,2,-1,"",1325017,"sealTemplateStyle错误：印章模版样式{-1}非法",0 ]
          #          - ["TC11-sealTemplateStyle 非定义值0.01",$sealIdUpdate0,2,0.01,"",1325017,"sealTemplateStyle错误：印章模版样式{",0]
          - [ "TC12-sealTemplateStyle 非定义值True",$sealIdUpdate0,2,"True","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC13-sealTemplateStyle 非定义值是",$sealIdUpdate0,2,"是","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC14-sealTemplateStyle 非定义值7",$sealIdUpdate0,2,7,"",1325017,"sealTemplateStyle错误：印章模版样式{",0 ]
    #          - ["TC20-sealTemplateStyle 6-动态编码章",$sealIdUpdate0,2,6,"",200,"成功",30]
    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common.yml

  - name: 更新企业印章-校验sealTemplateStyle,sealSource 非必填数据的校验-正向case
    parameters:
      - name-sealId-sealSource-sealTemplateStyle-base64img-code0-message0-data0:
          - [ "TC3-sealSource 非定义值0.01",$sealIdUpdate0,0.01,"","",1325015,"sealSource错误：印章来源{",0 ]
          - [ "TC8-sealSource 2-模板印章",$sealIdUpdate1,2,"","",200,"成功",30 ]
          - [ "TC15-sealTemplateStyle 1-椭圆形章带五角星",$sealIdUpdate0,2,1,"",200,"成功",30 ]
          - [ "TC16-sealTemplateStyle 2-椭圆形章不带五角星",$sealIdUpdate0,2,2,"",200,"成功",30 ]
          - [ "TC17-sealTemplateStyle 3-圆形章带五角星",$sealIdUpdate0,2,3,"",200,"成功",30 ]
          - [ "TC18-sealTemplateStyle 4-圆形章不带五角星",$sealIdUpdate0,2,4,"",200,"成功",30 ]
          - [ "TC19-sealTemplateStyle 5-双椭圆章不带五角星",$sealIdUpdate0,2,5,"",200,"成功",30 ]
    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common_wait.yml

  - name: 更新企业印章-校验sealMakerCertId,signerCertInfos 非必填数据的校验
    parameters:
      - name-sealId-sealPattern-sealMakerCertId-signerCertInfos-code0-message0-data0:
          - [ "TC1-sealPattern 非定义值0",$sealIdUpdate0,0,"",null,1325008,"sealPattern错误：印章形态{0}非法",0 ]
          - [ "TC2-sealPattern 非定义值-1",$sealIdUpdate0,-1,"",null,1325008,"sealPattern错误：印章形态",0 ]
          #          - ["TC3-sealPattern 非定义值0.01",$sealIdUpdate0,0.01,"",null,1325008,"sealPattern错误：印章形态",0]
          - [ "TC4-sealPattern 非定义值True",$sealIdUpdate0,"True","",null,1325008,"sealPattern错误：印章形态",0 ]
          - [ "TC5-sealPattern 非定义值是",$sealIdUpdate0,"是","",null,1325008,"sealPattern错误：印章形态",0 ]
          - [ "TC6-sealPattern 非定义值4",$sealIdUpdate0,4,"",null,1325008,"sealPattern错误：印章形态",0 ]
          - [ "TC7-sealPattern 1-云国际标准印章",$sealIdUpdate0,1,"",null,200,"成功",30 ]
          - [ "TC8-sealPattern 2-物理印章",$sealIdUpdate0,2,"",null,1325077,"企业印章不存在",30 ]
          - [ "TC9-sealPattern 3-云中国标准印章",$sealIdUpdate0,3,"",null,1325077,"企业印章不存在",30 ]
          - [ "TC10-中国标准印章 sealMakerCertId为空",$sealIdUpdate2,3,"",[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC11-中国标准印章 sealMakerCertIdnull",$sealIdUpdate2,3,null,[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC12-中国标准印章 sealMakerCertId不存在",$sealIdUpdate2,3,"XXXX",[ { "sealSignercertId": $org01CertId } ],1325009,"sealMakerCertId错误：制章者证书{XXXX}不可用",0 ]
          - [ "TC13-国际标准印章 sealMakerCertId不存在",$sealIdUpdate0,1,"XXXX",[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC14-中国标准印章 sealMakerCertIdrsa证书",$sealIdUpdate2,3,$org01CertId_rsa,[ { "sealSignercertId": $org01CertId } ],1325009,"sealMakerCertId错误：制章者证书{",0 ]
          - [ "TC15-中国标准印章 sealMakerCertId非当前用户的所属企业的sm2证书",$sealIdUpdate2,3,$org02CertId,[ { "sealSignercertId": $org01CertId } ],1325009,"sealMakerCertId错误：制章者证书{",0 ]
          - [ "TC16-中国标准印章 signerCertInfos为空数组",$sealIdUpdate2,3,$org01CertId,[ ],200,"成功",30 ]
          - [ "TC17-中国标准印章 signerCertInfos为空对象",$sealIdUpdate2,3,$org01CertId,[ { } ],1325079,"签章者证书列表中不能包含空的证书id",0 ]
          - [ "TC18-中国标准印章 signerCertInfos为证书为空",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": "" } ],1325079,"签章者证书列表中不能包含空的证书id",0 ]
          - [ "TC19-中国标准印章 signerCertInfos为证书为null",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": null } ],1325079,"签章者证书列表中不能包含空的证书id",0 ]
          - [ "TC20-中国标准印章 signerCertInfos为null",$sealIdUpdate2,3,$org01CertId,null,200,"成功",30 ]
          - [ "TC21-中国标准印章 signerCertInfos为不存在",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": "XXXX" } ],1325011,"sealSignercertId错误：签章者证书{XXXX}不可用",0 ]
          - [ "TC22-中国标准印章 signerCertInfos为RSA证书",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org01CertId_rsa } ],1325011,"sealSignercertId错误：签章者证书{",0 ]
          - [ "TC23-中国标准印章 signerCertInfos为其他企业的sm2证书",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org02CertId } ],1325011,"sealSignercertId错误：签章者证书{",0 ]
          - [ "TC24-中国标准印章 signerCertInfos为企业sm2证书",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC25-中国标准印章 signerCertInfos重复对象",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org01CertId },{ "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC26-中国标准印章 signerCertInfos多个对象部分正确",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org01CertId },{ "sealSignercertId": $org02CertId } ],1325011,"sealSignercertId错误：签章者证书{",0 ]
    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common.yml

  - name: 更新企业印章-校验sealMakerCertId,signerCertInfos 非必填数据的校验
    parameters:
      - name-sealId-sealPattern-sealMakerCertId-signerCertInfos-code0-message0-data0:
          - [ "TC7-sealPattern 1-云国际标准印章",$sealIdUpdate0,1,"",null,200,"成功",30 ]
          - [ "TC10-中国标准印章 sealMakerCertId为空",$sealIdUpdate2,3,"",[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC11-中国标准印章 sealMakerCertIdnull",$sealIdUpdate2,3,null,[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC13-国际标准印章 sealMakerCertId不存在",$sealIdUpdate0,1,"XXXX",[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
          - [ "TC16-中国标准印章 signerCertInfos为空数组",$sealIdUpdate2,3,$org01CertId,[ ],200,"成功",30 ]
          - [ "TC20-中国标准印章 signerCertInfos为null",$sealIdUpdate2,3,$org01CertId,null,200,"成功",30 ]
          - [ "TC24-中国标准印章 signerCertInfos为企业sm2证书",$sealIdUpdate2,3,$org01CertId,[ { "sealSignercertId": $org01CertId } ],200,"成功",30 ]
    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common_wait.yml

  - name: 更新企业印章-校验sealWidth、sealHeight、sealFileKey、base64img、sealOldStyle、defaultSeal非必填数据的校验
    parameters:
      - name-sealId-sealSource-sealWidth-sealHeight-sealFileKey-base64img-sealOldStyle-defaultSeal-code0-message0-data0:
          - [ "TC1-sealWidth 0",$sealIdUpdate0,2,0,40,"","","","",1325026,"sealWidth错误：印章宽度{0}不能超出区间[10-100]",0 ]
          - [ "TC2-sealWidth 9",$sealIdUpdate0,2,9,40,"","","","",1325026,"sealWidth错误：印章宽度{9}不能超出区间[10-100]",0 ]
          - [ "TC3-sealWidth 0.01",$sealIdUpdate0,2,0.01,40,"","","","",1325026,"sealWidth错误：印章宽度{0}不能超出区间[10-100]",0 ]
          - [ "TC4-sealWidth -1",$sealIdUpdate0,2,-1,40,"","","","",1325026,"sealWidth错误：印章宽度{-1}不能超出区间[10-100]",0 ]
          - [ "TC5-sealWidth True",$sealIdUpdate0,2,True,40,"","","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC6-sealWidth 是",$sealIdUpdate0,2,是,40,"","","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC7-sealWidth 为空,默认30",$sealIdUpdate0,2,"",40,"","","","",200,"成功",30 ]
          - [ "TC8-sealWidth 限定101",$sealIdUpdate0,2,101,40,"","","","",1325026,"sealWidth错误：印章宽度{101}不能超出区间[10-100]",0 ]
          - [ "TC9-sealWidth 限定10",$sealIdUpdate0,2,10,40,"","","","",200,"成功",30 ]
          - [ "TC10-sealWidth 限定100",$sealIdUpdate0,2,100,40,"","","","",200,"成功",30 ]
          - [ "TC11-sealWidth 限定50",$sealIdUpdate0,2,50,40,"","","","",200,"成功",30 ]

          - [ "TC12-sealHeight 0",$sealIdUpdate0,2,40,0,"","","","",1325027,"sealHeight错误：印章高度{0}不能超出区间[10-100]",0 ]
          - [ "TC13-sealHeight 9",$sealIdUpdate0,2,40,9,"","","","",1325027,"sealHeight错误：印章高度{9}不能超出区间[10-100]",0 ]
          - [ "TC14-sealHeight 0.01",$sealIdUpdate0,2,40,0.01,"","","","",1325027,"sealHeight错误：印章高度{0}不能超出区间[10-100]",0 ]
          - [ "TC15-sealHeight -1",$sealIdUpdate0,2,40,-1,"","","","",1325027,"sealHeight错误：印章高度{-1}不能超出区间[10-100]",0 ]
          - [ "TC16-sealHeight True",$sealIdUpdate0,2,40,True,"","","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC17-sealHeight 是",$sealIdUpdate0,2,40,是,"","","","",1322222,"不支持传入特殊字符",0 ]
          - [ "TC18-sealHeight 为空,默认30",$sealIdUpdate0,2,40,"","","","","",200,"成功",30 ]
          - [ "TC19-sealHeight 限定101",$sealIdUpdate0,2,40,101,"","","","",1325027,"sealHeight错误：印章高度{101}不能超出区间[10-100]",0 ]
          - [ "TC20-sealHeight 限定10",$sealIdUpdate0,2,40,10,"","","","",200,"成功",30 ]
          - [ "TC21-sealHeight 限定100",$sealIdUpdate0,2,40,100,"","","","",200,"成功",30 ]
          - [ "TC22-sealHeight 限定50",$sealIdUpdate0,2,40,50,"","","","",200,"成功",30 ]

          - [ "TC23-base64img 错误数据",$sealIdUpdate0,1,40,40,"","XXXXX","","",1325075,"base64img错误：自定义印章获取宽高失败",0 ]
          - [ "TC24-base64img 正确的jpg",$sealIdUpdate0,1,40,40,"",$base64file,"","",200,"成功",30 ]
          - [ "TC25-base64img 正确的png",$sealIdUpdate0,1,40,40,"",$base64file0,"","",200,"成功",30 ]
          - [ "TC26-base64img 正确的bmp",$sealIdUpdate0,1,40,40,"",$base64file1,"","",200,"成功",30 ]
          - [ "TC27-base64img 不支持的格式xls",$sealIdUpdate0,1,40,40,"","${get_file_base64($xlsFile)}","","",1325075,"base64img错误：自定义印章获取宽高失败",0 ]
          #          - [ "TC28-base64img 不支持的大小10MB以上",$sealIdUpdate0,1,40,40,"","${get_file_base64($pdfFile)}","","",1325075,"base64img错误：自定义印章获取宽高失败",0 ]
          - [ "TC29-sealFileKey 不存在,默认30",$sealIdUpdate0,1,40,40,"XXXX","","","",1325033,"sealFileKey错误：印章fileKey{XXXX}不存在",0 ]
          - [ "TC30-sealFileKey 不符合格式-pdf",$sealIdUpdate0,1,40,40,"${ENV(fileKey)}","","","",1325075,"sealFileKey错误：自定义印章获取宽高失败",0 ]

          - [ "TC31-sealOldStyle 0-图片不做旧",$sealIdUpdate0,2,40,40,"","",0,"",200,"成功",30 ]
          - [ "TC32-sealOldStyle 1-图片做旧",$sealIdUpdate0,2,40,40,"","",1,"",200,"成功",30 ]
          - [ "TC33-sealOldStyle 0.01",$sealIdUpdate0,2,40,40,"","",0.01,"",1322222,"不支持传入特殊字符",0 ]
          - [ "TC34-sealOldStyle -1",$sealIdUpdate0,2,40,40,"","",-1,"",200,"成功",30 ]
          - [ "TC35-sealOldStyle True",$sealIdUpdate0,2,40,40,"","",True,"",200,"成功",30 ]
          - [ "TC36-sealOldStyle 是",$sealIdUpdate0,2,40,40,"","",是,"",1322222,"不支持传入特殊字符",30 ]
          - [ "TC37-sealOldStyle 为空,默认0",$sealIdUpdate0,2,40,40,"","","","",200,"成功",30 ]

#          - [ "TC38-defaultSeal 1-设置默认印章",$sealIdUpdate0,2,40,40,"","","",1,200,"成功",30 ]
#          - [ "TC39-defaultSeal 0.01",$sealIdUpdate0,2,40,40,"","","",0.01,1322222,"不支持传入特殊字符",0 ]
#          - [ "TC40-defaultSeal -1",$sealIdUpdate0,2,40,40,"","","","",200,"成功",30 ]
#          - [ "TC41-defaultSeal True",$sealIdUpdate0,2,40,40,"","","",True,200,"成功",30 ]
#          - [ "TC42-defaultSeal 是",$sealIdUpdate0,2,40,40,"","","",是,1322222,"不支持传入特殊字符",30 ]
#          - [ "TC43-defaultSeal 为空,默认0",$sealIdUpdate0,2,40,40,"","","","",200,"成功",30 ]
    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common_wait.yml

#-
#    name: 更新企业印章-校验sealSignercertId非必填数据的校验
#    parameters:
#      - name-sealId-sealSignercertId-code0-message0-data0:
#          - ["TC1-signerCertInfos为空数组",$sealIdUpdate2,[],200,"成功",30]
#          - ["TC1-signerCertInfos为空对象",$sealIdUpdate2,[{}],1325079,"签章者证书列表中不能包含空的证书id",0]
#          - ["TC1-signerCertInfos为证书为空",$sealIdUpdate2,[{"sealSignercertId":""}],1325079,"签章者证书列表中不能包含空的证书id",0]
#          - ["TC1-signerCertInfos为证书为null",$sealIdUpdate2,[{"sealSignercertId":null}],1325079,"签章者证书列表中不能包含空的证书id",0]
#          - ["TC1-signerCertInfos为null",$sealIdUpdate2,null,200,"成功",30]
#          - ["TC1-signerCertInfos为不存在",$sealIdUpdate2,[{"sealSignercertId":"XXXX"}],1325011,"sealSignercertId错误：签章者证书{XXXX}不可用",0]
#          - ["TC1-signerCertInfos为RSA证书",$sealIdUpdate2,[{"sealSignercertId":$org01CertId_rsa}],1325011,"sealSignercertId错误：签章者证书{",0]
#          - ["TC1-signerCertInfos为其他企业的sm2证书",$sealIdUpdate2,[{"sealSignercertId":$org02CertId}],1325011,"sealSignercertId错误：签章者证书{",0]
#          - ["TC1-signerCertInfos为企业sm2证书",$sealIdUpdate2,[{"sealSignercertId":$org01CertId}],200,"成功",30]
##          - ["TC1-signerCertInfos重复对象",$sealIdUpdate2,[],200,"成功",30]
##          - ["TC1-signerCertInfos为空数组",$sealIdUpdate2,[],200,"成功",30]
#    testcase: testcases/seals/v1/organizationSeals/updateOrgSealTC_sealInfo_common.yml