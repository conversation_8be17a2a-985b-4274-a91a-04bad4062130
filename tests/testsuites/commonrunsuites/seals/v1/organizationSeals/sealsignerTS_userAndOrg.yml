config:
    name: 授权用印人
    variables:
      sealTypeCode0: "COMMON-SEAL"
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(worg01.orgNo)}
      worg01Code: ${ENV(worg01.orgCode)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
      ###用户离职之后又二次创建
      resetCreateAccountNo: ${ENV(resetCreateAccountNo)}
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}
#      sign01_sealId: ${ENV(sign01.sealId)}
#      org01_sealId: ${ENV(org01.sealId)}
#      legal_org01_sealId: ${ENV(org01.legal.sealId)}
#      wsignwb01_sealId: $${ENV(wsignwb01.sealId)}
#      sign05_sealId: ${ENV(userCodeNoCertSealId)}
#      sealId_diaoxiao: ${getUserSealsByStatus($sign01Code, 5)}
#      sealId_stop: ${getUserSealsByStatus($sign01Code, h)}
#      sealId_draf: ${getUserSealsByStatus($sign01Code, 1)}
      sealId: ${createOrganizationSeals($org01Code, $sign01Code)}

testcases:
-
    name: 授权用印人企业印章-authorizationScope和sealsignerType校验
    parameters:
      - name-authorizationScope-sealsignerType-code0-message0-data0:
          - ["TC1-authorizationScope 非指定值0",0,1,1313037,"authorizationScope错误：授权范围不支持传入[0]",0]
          - ["TC2-authorizationScope 非指定值0.05",0.05,1,1313037,"authorizationScope错误：授权范围不支持传入",0]
          - ["TC3-authorizationScope 非指定值-1",-1,1,1313037,"authorizationScope错误：授权范围不支持传入",0]
          - ["TC4-authorizationScope 非指定值TRUE",True,1,1313037,"authorizationScope错误：授权范围不支持传入",0]
          - ["TC5-authorizationScope 非指定值是","是",1,1313037,"authorizationScope错误：授权范围不支持传入",0]
          - ["TC6-authorizationScope 非指定值4",4,1,1313037,"authorizationScope错误：授权范围不支持传入",0]
          - ["TC7-authorizationScope 2-所有人",2,1,200,"成功",1]
          - ["TC8-authorizationScope 3-指定人",3,1,200,"成功",1]
          - ["TC9-sealsignerType 非指定值0",3,0,1313036,"sealsignerType错误：授权用印人类型不支持传入[0]",0]
          - ["TC10-sealsignerType 非指定值0.05",3,0.05,1313036,"sealsignerType错误：授权用印人类型不支持传入",0]
          - ["TC11-sealsignerType 非指定值-1",3,-1,1313036,"sealsignerType错误：授权用印人类型不支持传入",0]
          - ["TC12-sealsignerType 非指定值TRUE",3,True,1313036,"sealsignerType错误：授权用印人类型不支持传入",0]
          - ["TC13-sealsignerType 非指定值是",3,"是",1313036,"sealsignerType错误：授权用印人类型不支持传入",0]
          - ["TC14-sealsignerType 非指定值4",3,4,1313036,"sealsignerType错误：授权用印人类型不支持传入",0]
          - ["TC15-sealsignerType 1-电子印章用印人",3,1,200,"成功",1]
          - ["TC16-sealsignerType 2-物理印章用印人",3,2,1313017,"该企业印章[",0]
          - ["TC17-sealsignerType 3-应急用印人",3,3,1313017,"]不存在",0]
    testcase: testcases/seals/v1/organizationSeals/sealsignersTC_userAndOrg.yml

-
    name: 授权用印人企业印章-校验组织用户信息
    parameters:
      - name-customAccountNo-userCode-customOrgNo-organizationCode-code0-message0-data0:
#          - ["TC1-customAccountNo和userCode都不传",null,null,"$org01No","",1313032,"userCode和customAccountNo二选一必填",0]
          - ["TC2-customAccountNo和userCode都为空","","","$org01No","",200,"成功",1]
          - ["TC3-customAccountNo不传，userCode不存在",null,"XXXXXX","$org01No","",1111003,"用户已不存在",0]
          - ["TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","",1313039,"信息不匹配",0]
          - ["TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","",1313034,"customAccountNo:[XXXXXX]有误，用户信息不存在",0]
          - ["TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","",1313034,"用户信息不存在",0]
          - ["TC7-customAccountNo正确，userCode错误","$sign01No","$wsign01Code","$org01No","",1313039,"信息不匹配",0]
          - ["TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","$org01No","",200,"成功",1]
          - ["TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","",1111003,"用户已不存在",0]
          - ["TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","",1111003,"用户已不存在",0]
#          - ["TC11-customAccountNo用户离职之后又创建成功","$resetCreateAccountNo","","$org01No","",1313039,"信息不匹配",0]
          - ["TC12-customOrgNo和organizationCode都不传","$sign01No","",null,null,1313033,"organizationCode和customOrgNo二选一必填",0]
          - ["TC13-customOrgNo和organizationCode都为空","$sign01No","","","",1313033,"organizationCode和customOrgNo二选一必填",0]
          - ["TC14-customOrgNo为空，organizationCode不存在","$sign01No","","","XXXXXX",1313041,"organizationCode:[XXXXXX]有误，机构或部门信息不存在",0]
          - ["TC15-customOrgNo为空，organizationCode相对方","$sign01No","","","$worg01Code",1313039,"信息不匹配",0]
          - ["TC16-customOrgNo不存在，organizationCode为空","$sign01No","","XXXXXX","",1313038,"customOrgNo:[XXXXXX]有误，机构或部门信息不存在",0]
          - ["TC17-customOrgNo相对方账号，organizationCode为空","$sign01No","","$worg01No","",1313039,"信息不匹配",0]
          - ["TC18-customOrgNo正确，organizationCode错误","$sign01No","","$org01No","XXXXXX",1313041,"organizationCode:[XXXXXX]有误，机构或部门信息不存在",0]
          - ["TC19-customOrgNo错误，organizationCode正确","$sign01No","","XXXXXX","$org01Code",200,"成功",1]
          - ["TC20-organizationCode类型是部门","$sign01No","","$departmentNo","",200,"成功",1]
          - ["TC21-organizationCode企业已删除","$sign01No","","","$orgCodeDelete",1313041,"]有误，机构或部门信息不存在",0]
          - ["TC22-匹配的相对方账号","$wsign01No","","","$worg01Code",1313034,"用户信息不存在",0]
    testcase: testcases/seals/v1/organizationSeals/sealsignersTC_userAndOrg.yml