config:
    name: 创建企业印章校验
    variables:
      sealTypeCode0: "COMMON-SEAL"
      org01Code: ${ENV(sign01.main.orgCode)}
      org02Code: ${ENV(csqs.orgCode)}
      org01CertId_rsa: ${getOrganizationCertsByStatus($org01Code)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org02CertId: ${getOrganizationCertsByStatus($org02Code,1,2)}
      jpgFile: tests/data/111.jpg
      pngFile: tests/data/111.png
      bmpFile: tests/data/111.bmp
      xlsFile: tests/data/111.xls
#      pdfFile: tests/data/30M-探索式测试实践之路.pdf
      base64file: ${get_file_base64($jpgFile)}
      base64file0: ${get_file_base64($pngFile)}
      base64file1: ${get_file_base64($bmpFile)}

testcases:
-
    name: 创建企业印章-校验sealTemplateStyle,sealSource 非必填数据的校验
    parameters:
      - name-sealSource-sealTemplateStyle-base64img-sealFileKey-code0-message0-data0:
          - ["TC1-sealSource 非定义值0",0,"","","",1325015,"sealSource错误：印章来源{0}非法",0]
          - ["TC2-sealSource 非定义值-1",-1,"","","",1325015,"sealSource错误：印章来源{",0]
          - ["TC3-sealSource 非定义值0.01",0.01,"","","",1325015,"sealSource错误：印章来源{",0]
          - ["TC4-sealSource 非定义值True","True","","","",1322222,"不支持传入特殊字符",0]
          - ["TC5-sealSource 非定义值是","是","","","",1322222,"不支持传入特殊字符",0]
          - ["TC6-sealSource 非定义值3",3,"","","",1325015,"sealSource错误：印章来源{",0]
          - ["TC7-sealSource 1-自定义印章",1,"","","",1325028,"自定义印章，印章fileKey和印章base64不能都为空",0]
          - ["TC8-sealSource 2-模板印章",2,"","","",200,"成功",30]
          - ["TC9-sealTemplateStyle 非定义值0",2,0,"","",1325017,"sealTemplateStyle错误：印章模版样式{0}非法",0]
          - ["TC10-sealTemplateStyle 非定义值-1",2,-1,"","",1325017,"sealTemplateStyle错误：印章模版样式{-1}非法",0]
          - ["TC11-sealTemplateStyle 非定义值0.01",2,0.01,"","",1325017,"sealTemplateStyle错误：印章模版样式{",0]
          - ["TC12-sealTemplateStyle 非定义值True",2,"True","","",1322222,"不支持传入特殊字符",0]
          - ["TC13-sealTemplateStyle 非定义值是",2,"是","","",1322222,"不支持传入特殊字符",0]
          - ["TC14-sealTemplateStyle 非定义值7",2,7,"","",1325017,"sealTemplateStyle错误：印章模版样式{",0]
          - ["TC15-sealTemplateStyle 1-椭圆形章带五角星",2,1,"","",200,"成功",30]
          - ["TC16-sealTemplateStyle 2-椭圆形章不带五角星",2,2,"","",200,"成功",30]
          - ["TC17-sealTemplateStyle 3-圆形章带五角星",2,3,"","",200,"成功",30]
          - ["TC18-sealTemplateStyle 4-圆形章不带五角星",2,4,"","",200,"成功",30]
          - ["TC19-sealTemplateStyle 5-双椭圆章不带五角星",2,5,"","",200,"成功",30]
          - ["TC20-sealTemplateStyle 6-动态编码章",2,6,"","",1325024,"动态编码章横向文一不能为空",0]
          - ["TC21-base64img 错误数据",1,"","XXXXX","",1325075,"base64img错误：自定义印章获取宽高失败",0]
          - ["TC22-base64img 正确的jpg",1,"",$base64file,"",200,"成功",30]
          - ["TC23-base64img 正确的png",1,"",$base64file0,"",200,"成功",30]
          - ["TC24-base64img 正确的bmp",1,"",$base64file1,"",200,"成功",30]
#          - ["TC25-base64img 不支持的格式xls",1,"","${get_file_base64($xlsFile)}","",1325075,"自定义印章，印章fileKey和印章base64不能都为空",0]
#          - ["TC26-base64img 不支持的大小10MB以上",1,"","${get_file_base64($pdfFile)}","",1325075,"base64太大了不建议放在html中",0]
          - ["TC27-sealFileKey 不存在",1,"","","XXXX",1325033,"sealFileKey错误：印章fileKey{XXXX}不存在",0]
          - ["TC28-sealFileKey 不符合格式-pdf",1,"","","${ENV(fileKey)}",1325075,"sealFileKey错误：自定义印章获取宽高失败",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验sealPattern,sealMakerCertId,signerCertInfos 非必填数据的校验
    parameters:
      - name-sealPattern-sealMakerCertId-signerCertInfos-code0-message0-data0:
          - ["TC1-sealPattern 非定义值0",0,"",null,1325008,"sealPattern错误：印章形态{0}非法",0]
          - ["TC2-sealPattern 非定义值-1",-1,"",null,1325008,"sealPattern错误：印章形态",0]
          - ["TC3-sealPattern 非定义值0.01",0.01,"",null,1325008,"sealPattern错误：印章形态",0]
          - ["TC4-sealPattern 非定义值True","True","",null,1325008,"sealPattern错误：印章形态",0]
          - ["TC5-sealPattern 非定义值是","是","",null,1325008,"sealPattern错误：印章形态",0]
          - ["TC6-sealPattern 非定义值4",4,"",null,1325008,"sealPattern错误：印章形态",0]
          - ["TC7-sealPattern 1-云国际标准印章",1,"",null,200,"成功",30]
          - ["TC8-sealPattern 2-物理印章",2,"",null,200,"成功",30]
          - ["TC9-sealPattern 3-云中国标准印章",3,"",null,200,"成功",30]
          - ["TC10-中国标准印章 sealMakerCertId为空",3,"",[{"sealSignercertId":$org01CertId}],200,"成功",30]
          - ["TC11-中国标准印章 sealMakerCertIdnull",3,null,[{"sealSignercertId":$org01CertId}],200,"成功",30]
          - ["TC12-中国标准印章 sealMakerCertId不存在",3,"XXXX",[{"sealSignercertId":$org01CertId}],1325009,"sealMakerCertId错误：制章者证书{XXXX}不可用",0]
          - ["TC13-国际标准印章 sealMakerCertId不存在",1,"XXXX",[{"sealSignercertId":$org01CertId}],200,"成功",30]
          - ["TC14-中国标准印章 sealMakerCertIdrsa证书",3,$org01CertId_rsa,[{"sealSignercertId":$org01CertId}],1325009,"sealMakerCertId错误：制章者证书{",0]
          - ["TC15-中国标准印章 sealMakerCertId非当前用户的所属企业的sm2证书",3,$org02CertId,[{"sealSignercertId":$org01CertId}],1325009,"sealMakerCertId错误：制章者证书{",0]
          - ["TC16-中国标准印章 signerCertInfos为空数组",3,$org01CertId,[],200,"成功",30]
          - ["TC17-中国标准印章 signerCertInfos为空对象",3,$org01CertId,[{}],1325079,"签章者证书列表中不能包含空的证书id",0]
          - ["TC18-中国标准印章 signerCertInfos为证书为空",3,$org01CertId,[{"sealSignercertId":""}],1325079,"签章者证书列表中不能包含空的证书id",0]
          - ["TC19-中国标准印章 signerCertInfos为证书为null",3,$org01CertId,[{"sealSignercertId":null}],1325079,"签章者证书列表中不能包含空的证书id",0]
          - ["TC20-中国标准印章 signerCertInfos为null",3,$org01CertId,null,200,"成功",30]
          - ["TC21-中国标准印章 signerCertInfos为不存在",3,$org01CertId,[{"sealSignercertId":"XXXX"}],1325011,"sealSignercertId错误：签章者证书{XXXX}不可用",0]
          - ["TC22-中国标准印章 signerCertInfos为RSA证书",3,$org01CertId,[{"sealSignercertId":$org01CertId_rsa}],1325011,"sealSignercertId错误：签章者证书{",0]
          - ["TC23-中国标准印章 signerCertInfos为其他企业的sm2证书",3,$org01CertId,[{"sealSignercertId":$org02CertId}],1325011,"sealSignercertId错误：签章者证书{",0]
          - ["TC24-中国标准印章 signerCertInfos为企业sm2证书",3,$org01CertId,[{"sealSignercertId":$org01CertId}],200,"成功",30]
          - ["TC25-中国标准印章 signerCertInfos重复对象",3,$org01CertId,[{"sealSignercertId":$org01CertId},{"sealSignercertId":$org01CertId}],200,"成功",30]
          - ["TC26-中国标准印章 signerCertInfos多个对象部分正确",3,$org01CertId,[{"sealSignercertId":$org01CertId},{"sealSignercertId":$org02CertId}],1325011,"sealSignercertId错误：签章者证书{",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验sealTypeCode,sealGroupName,sealGroupDesc，sealRelease，sealNumLimit，sealFileKey，base64img，sealColour，sealSignercertId 的单字段校验
    parameters:
      - name-sealTypeCode-sealGroupName-sealGroupDesc-sealColour-code0-message0-data0:
          - ["sealTypeCode不传","","分组名称","不传","",1325059,"sealTypeCode错误：印章类型不能为空",0]
          - ["sealTypeCode传null",null,"分组名称","不传","",1325059,"sealTypeCode错误：印章类型不能为空",0]
          - ["sealTypeCode不存在","dhjsd","分组名称","不传","",1325004,"sealTypeCode错误：印章类型{dhjsd}不存在",0]
          - ["sealTypeCode正常值","COMMON-SEAL","分组名称","正常场景","",200,"成功",30]
          - ["sealGroupName不传","COMMON-SEAL","","正常场景","",1325074,"sealGroupName错误：企业印章分组名称不能为空",0]
          - ["sealGroupName为null","COMMON-SEAL",null,"正常场景","",1325074,"sealGroupName错误：企业印章分组名称不能为空",30]
          - ["sealGroupName超过25","COMMON-SEAL","哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的哈哈很多的","正常场景","",1325006,"不能超过25字",0]
          - ["sealGroupDesc不传","COMMON-SEAL","哈哈","","",200,"成功",30]
          - ["sealGroupDesc传null","COMMON-SEAL","哈哈",null,"",200,"成功",30]
          - ["sealColour传1","COMMON-SEAL","哈哈",null,1,200,"成功",30]
          - ["sealColour传2","COMMON-SEAL","哈哈",null,2,200,"成功",30]
          - ["sealColour传3","COMMON-SEAL","哈哈",null,3,200,"成功",30]
          - ["sealColour传4","COMMON-SEAL","哈哈",null,1,200,"成功",30]
          - ["sealColour传-1","COMMON-SEAL","哈哈",null,-1,1325018,"sealColour错误：印章颜色{-1}非法",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml
