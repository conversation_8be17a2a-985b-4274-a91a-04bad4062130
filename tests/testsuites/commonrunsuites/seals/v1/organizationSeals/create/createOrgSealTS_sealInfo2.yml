config:
    name: 创建企业印章校验
    variables:
      sealTypeCode0: "COMMON-SEAL"
      org01Code: ${ENV(sign01.main.orgCode)}
      org02Code: ${ENV(csqs.orgCode)}
      org01CertId_rsa: ${getOrganizationCertsByStatus($org01Code)}
      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
      org02CertId: ${getOrganizationCertsByStatus($org02Code,1,2)}


testcases:
-
    name: 创建企业印章-sealName,sealOldStyle(印章图片做旧 0否，1是),defaultSeal,sealHeight,sealWidth,sealOpacity 非必填数据的校验
    parameters:
      - name-sealName-sealOldStyle-defaultSeal-sealHeight-sealWidth-sealOpacity-code0-message0-data0:
          - ["TC1-sealName 为空","",0,0,40,40,100,200,"成功",30]
          - ["TC2-sealName 为null",null,0,0,40,40,100,200,"成功",30]
          - ["TC3-sealName 长度超过101","${generate_random_str(101)}",0,0,40,40,100,1325007,"不能超过100字",0]
          - ["TC4-sealName 为特殊字符","!@#\\/:*?\"<>|",0,0,40,40,100,1338016,"印章名称不支持以下特殊符号：\\/:*?\"<>|",30]
          - ["TC5-sealName 为英文全角","ｑｕａｎｊｉａｏ",0,0,40,40,100,200,"成功",30]
          - ["TC6-sealName 为英文大小写","Test",0,0,40,40,100,200,"成功",30]
          - ["TC7-sealName 为前后空格"," banjiao  ",0,0,40,40,100,200,"成功",30]
          - ["TC8-sealName 为繁体字"," 發纔䶮  ",0,0,40,40,100,200,"成功",30]
          - ["TC9-sealName 为新疆名","达吾提·阿西木 ",0,0,40,40,100,200,"成功",30]
          - ["TC10-sealName 为英文名 ","Rosalette Hazal Royston",0,0,40,40,100,1338016,"印章名称不支持以下特殊符号：\\/:*?\"<>|",30]
          - ["TC11-sealName 为一个字 ","T",0,0,40,40,100,200,"成功",30]
          - ["TC12-sealName 长度=30 ","${generate_random_str(30)}",0,0,40,40,100,200,"成功",30]

          - ["TC21-sealOldStyle 0-图片不做旧 ","${generate_random_str(6)}",0,0,40,40,100,200,"成功",30]
          - ["TC22-sealOldStyle 1-图片做旧 ","${generate_random_str(6)}",1,0,40,40,100,200,"成功",30]
          - ["TC23-sealOldStyle 0.01 ","1322222",0.01,0,40,40,100,1322222,"不支持传入特殊字符",0]
          - ["TC24-sealOldStyle -1 ","${generate_random_str(6)}",-1,0,40,40,100,200,"成功",30]
          - ["TC25-sealOldStyle True","${generate_random_str(6)}",True,0,40,40,100,200,"成功",30]
          - ["TC26-sealOldStyle 是 ","1322222","是",0,40,40,100,1322222,"不支持传入特殊字符",0]
          - ["TC27-sealOldStyle 为空,默认0 ","${generate_random_str(6)}","",0,40,40,100,200,"成功",30]
          - ["TC28-defaultSeal 1-设置默认印章 ","${generate_random_str(6)}",0,1,40,40,100,1325031,"同一机构同一印章类型同一印章形态只能有一个默认印章",30]

          - ["TC31-defaultSeal 0.01 ","测试",0,0.01,40,40,100,1322222,"不支持传入特殊字符",0]
          - ["TC32-defaultSeal -1 ","${generate_random_str(6)}",0,-1,40,40,100,1325031,"同一机构同一印章类型同一印章形态只能有一个默认印章",30]
          - ["TC33-defaultSeal True","${generate_random_str(6)}",0,True,40,40,100,1325031,"同一机构同一印章类型同一印章形态只能有一个默认印章",30]
          - ["TC34-defaultSeal 是 ","1322222",0,"是",40,40,100,1322222,"不支持传入特殊字符",0]
          - ["TC35-defaultSeal 为空,默认0 ","${generate_random_str(6)}",0,"",40,40,100,200,"成功",30]

          - ["TC41-sealHeight 0 ","1325027",0,0,0,40,100,1325027,"sealHeight错误：印章高度{0}不能超出区间[10-100]",0]
          - ["TC42-sealHeight 9 ","测试",0,0,9,40,100,1325027,"不能超出区间[10-100]",0]
          - ["TC43-sealHeight 0.01 ","测试",0,0,0.01,40,100,1325027,"不能超出区间[10-100]",0]
          - ["TC44-sealHeight -1 ","测试",1,0,-1,40,100,1325027,"不能超出区间[10-100]",0]
          - ["TC45-sealHeight True","测试",0,0,True,40,100,1322222,"不支持传入特殊字符",0]
          - ["TC46-sealHeight 是 ","测试",0,0,"是",40,100,1322222,"不支持传入特殊字符",0]
          - ["TC47-sealHeight 为空,默认30 ","${generate_random_str(6)}",0,0,"",40,100,200,"成功",30]
          - ["TC48-sealHeight 限定101 ","测试",0,0,101,40,100,1325027,"不能超出区间[10-100]",0]
          - ["TC49-sealHeight 限定10 ","${generate_random_str(6)}",0,0,10,40,100,200,"成功",30]

          - ["TC51-sealWidth 0 ","测试",0,0,40,0,100,1325026,"sealWidth错误：印章宽度{0}不能超出区间[10-100]",0]
          - ["TC52-sealWidth 9 ","测试",0,0,40,9,100,1325026,"不能超出区间[10-100]",0]
          - ["TC53-sealWidth 0.01 ","测试",0,0,30,0.01,100,1325026,"不能超出区间[10-100]",0]
          - ["TC54-sealWidth -1 ","测试",1,0,11,-1,100,1325026,"不能超出区间[10-100]",0]
          - ["TC55-sealWidth True","测试",0,0,22,True,100,1322222,"不支持传入特殊字符",0]
          - ["TC56-sealWidth 是 ","测试",0,0,22,"是",100,1322222,"不支持传入特殊字符",0]
          - ["TC57-sealWidth 为空,默认30 ","${generate_random_str(6)}",0,0,25,"",100,200,"成功",30]
          - ["TC58-sealWidth 限定101 ","测试",0,0,56,999,100,1325026,"不能超出区间[10-100]",0]
          - ["TC59-sealWidth 限定10 ","${generate_random_str(6)}",0,0,36,10,100,200,"成功",30]

          - ["TC61-sealOpacity 0 ","测试",0,0,40,30,0,1325016,"sealOpacity错误：印章不透明度{0}超出区间[20-100]",0]
          - ["TC62-sealOpacity 19 ","测试",0,0,30,30,19,1325016,"超出区间[20-100]",0]
          - ["TC63-sealOpacity 0.01 ","测试",0,0,30,30,0.01,1325016,"超出区间[20-100]",0]
          - ["TC64-sealOpacity -1 ","测试",1,0,11,41,-1,1325016,"超出区间[20-100]",0]
          - ["TC65-sealOpacity True","测试",0,0,22,33,True,1322222,"不支持传入特殊字符",0]
          - ["TC66-sealOpacity 是 ","测试",0,0,22,33,"是",1322222,"不支持传入特殊字符",0]
          - ["TC67-sealOpacity 为空,默认100 ","${generate_random_str(6)}",0,0,25,45,"",200,"成功",30]
          - ["TC68-sealOpacity 不透明度101 ","${generate_random_str(6)}",0,0,56,56,101,1325016,"超出区间[20-100]",0]
          - ["TC69-sealOpacity 不透明度20","${generate_random_str(6)}",0,0,36,10,20,200,"成功",30]
          - ["TC70-sealOpacity 不透明度100","${generate_random_str(6)}",0,0,36,10,100,200,"成功",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

