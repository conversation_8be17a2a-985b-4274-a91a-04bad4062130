config:
    name: 创建企业印章校验
    variables:
      sealTypeCode0: "COMMON-SEAL"
      sign01Code: ${ENV(sign01.userCode)}
      sign01No: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      wsign01Code: ${ENV(wsignwb01.userCode)}
      wsign01No: ${ENV(wsignwb01.accountNo)}
      worg01No: ${ENV(worg01.orgNo)}
      worg01Code: ${ENV(worg01.orgCode)}
      userCodeDelete: ${ENV(userCode.delete)}
      userCodeDimission: ${ENV(userCode.dimission)}
      ###用户离职之后又二次创建
      resetCreateAccountNo: ${ENV(resetCreateAccountNo)}
      orgCodeDelete: ${ENV(orgCode.delete)}
      departmentNo: ${ENV(sign01.JZ.departNo)}
      sign01_sealId: ${ENV(sign01.sealId)}
      org01_sealId: ${ENV(org01.sealId)}
      legal_org01_sealId: ${ENV(org01.legal.sealId)}
      wsignwb01_sealId: $${ENV(wsignwb01.sealId)}
      sign05_sealId: ${ENV(userCodeNoCertSealId)}
      sealId_diaoxiao: ${getUserSealsByStatus($sign01Code, 5)}
      sealId_stop: ${getUserSealsByStatus($sign01Code, h)}
      sealId_draf: ${getUserSealsByStatus($sign01Code, 1)}

testcases:
-
    name: 创建企业印章-校验组织用户信息
    parameters:
      - name-customAccountNo-userCode-customOrgNo-organizationCode-code0-message0-data0:
          - ["TC1-customAccountNo和userCode都不传",null,null,"$org01No","",1325001,"用户编码和用户账号不能同时为空",0]
          - ["TC2-customAccountNo和userCode都为空","","","$org01No","",1325001,"用户编码和用户账号不能同时为空",0]
          - ["TC3-customAccountNo不传，userCode不存在",null,"XXXXXX","$org01No","",1325002,"userCode错误",0]
          - ["TC4-customAccountNo不传，userCode为相对方",null,"$wsign01Code","$org01No","",1325057,"}不是内部用户",0]
          - ["TC5-customAccountNo不存在，userCode为空","XXXXXX",null,"$org01No","",1325054,"customAccountNo错误：创建人账号{XXXXXX}不存在",0]
          - ["TC6-customAccountNo相对方账号，userCode为空","$wsign01No",null,"$org01No","",1325054,"customAccountNo错误：创建人账号{wsignwb01}不存在",0]
          - ["TC7-customAccountNo正确，userCode错误","$sign01No","$wsign01Code","$org01No","",1325057,"}不是内部用户",0]
          - ["TC8-customAccountNo错误，userCode正确","$wsign01No","$sign01Code","$org01No","",200,"成功",30]
          - ["TC9-userCode用户已离职",null,"$userCodeDimission","$org01No","",1325002,"}不存在",0]
          - ["TC10-userCode用户已删除",null,"$userCodeDelete","$org01No","",1325002,"}不存在",0]
#          - ["TC11-customAccountNo用户离职之后又创建成功","$resetCreateAccountNo","","$org01No","",200,"成功",30]
          - ["TC12-customOrgNo和organizationCode都不传","$sign01No","",null,null,1325000,"企业编码和企业账号不能同时为空",0]
          - ["TC13-customOrgNo和organizationCode都为空","$sign01No","","","",1325000,"企业编码和企业账号不能同时为空",0]
          - ["TC14-customOrgNo为空，organizationCode不存在","$sign01No","","","XXXXXX",1325003,"organizationCode错误：企业编码{XXXXXX}不存在",0]
          - ["TC15-customOrgNo为空，organizationCode相对方","$sign01No","","","$worg01Code",1325055,"}不是内部企业",0]
          - ["TC16-customOrgNo不存在，organizationCode为空","$sign01No","","XXXXXX","",1325053,"customOrgNo错误：印章所属企业账号{XXXXXX}不存在",0]
          - ["TC17-customOrgNo相对方账号，organizationCode为空","$sign01No","","$worg01No","",1325055,"}不是内部企业",0]
          - ["TC18-customOrgNo正确，organizationCode错误","$sign01No","","$org01No","XXXXXX",1325003,"organizationCode错误：企业编码{XXXXXX}不存在",0]
          - ["TC19-customOrgNo错误，organizationCode正确","$sign01No","","XXXXXX","$org01Code",200,"成功",30]
          - ["TC20-organizationCode类型是部门","$sign01No","","$departmentNo","",1325056,"}不是企业",0]
          - ["TC21-organizationCode企业已删除","$sign01No","","$orgCodeDelete","",1325053,"}不存在",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_userAndOrg.yml

-
    name: 创建企业印章-校验组织用户和法人印章关系,sealTypeCode的信息
    parameters:
      - name-userCode-sealTypeCode-organizationCode-legalSealId-code0-message0-data0:
          - ["TC1-创建法人章，legalSealId为空","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","",1325014,"印章类型为法人章，法人章id不能为空",0]
          - ["TC2-创建法人章，legalSealId为NULL","$sign01Code","LEGAL-PERSON-SEAL","$org01Code",null,1325014,"印章类型为法人章，法人章id不能为空",0]
          - ["TC3-创建法人章，legalSealId为不存在","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","XXXX",1325030,"legalSealId错误：法人章id{XXXX}不可用，请检查印章是否存在且状态为发布",0]
          - ["TC4-创建法人章，legalSealId为企业印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$org01_sealId",1325030,"}不可用，请检查印章是否存在且状态为发布",0]
          - ["TC5-创建法人章，legalSealId为法人印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$legal_org01_sealId",1325030,"}不可用，请检查印章是否存在且状态为发布",0]
          - ["TC6-创建法人章，legalSealId为相对方个人印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$wsignwb01_sealId",1325030,"}不可用，请检查印章是否存在且状态为发布",0]
          - ["TC7-创建法人章，legalSealId为其他内部个人印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$sign05_sealId",1325052,"}不是所属印章企业的法人章",0]
          - ["TC8-创建法人章，legalSealId为对应法人的内部个人印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$sign01_sealId",200,"",30]
          - ["TC9-创建法人章，legalSealId为停用的印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$sealId_stop",1325030,"不可用，请检查印章是否存在且状态为发布",0]
          - ["TC10-创建法人章，legalSealId为吊销的印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$sealId_diaoxiao",1325030,"不可用，请检查印章是否存在且状态为发布",0]
          - ["TC11-创建法人章，legalSealId为草稿的印章","$sign01Code","LEGAL-PERSON-SEAL","$org01Code","$sealId_draf",1325030,"不可用，请检查印章是否存在且状态为发布",0]
          - ["TC12-sealTypeCode 为空","$sign01Code","","$org01Code","",1325059,"sealTypeCode错误：印章类型不能为空",0]
          - ["TC13-sealTypeCode 为null","$sign01Code",null,"$org01Code","",1325059,"sealTypeCode错误：印章类型不能为空",0]
          - ["TC14-sealTypeCode 为不存在","$sign01Code","XXXX","$org01Code","",1325004,"sealTypeCode错误：印章类型{XXXX}不存在",0]
          - ["TC15-sealTypeCode 为带有流程（临时用例--需要加方法参数）","$sign01Code","WF-SEAL","$org01Code","",1325005,"存在审批流，请在页面操作，或联系管理员处理",0]
          - ["TC16-sealTypeCode 为已删除的（临时用例--需要加方法参数）","$sign01Code","WMSC","$org01Code","",1325004,"}不存在",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_userAndOrg.yml