config:
    name: 创建企业印章校验-校验印章上玄文，横向文，下玄文
    variables:
      sealTypeCode0: "COMMON-SEAL"
#      org01Code: ${ENV(sign01.main.orgCode)}
#      org02Code: ${ENV(csqs.orgCode)}
#      org01CertId_rsa: ${getOrganizationCertsByStatus($org01Code)}
#      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
#      org02CertId: ${getOrganizationCertsByStatus($org02Code,1,2)}

#############模板印章内容################
testcases:
-
    name: 创建企业印章-校验校验sealUpperText （上玄文2-50字）非必填数据的校验
    parameters:
      - name-sealUpperText-sealTemplateStyle-code0-message0-data0:
          - ["TC1-sealUpperText 为空","",1,200,"成功",30]
          - ["TC2-sealUpperText 为null",null,1,200,"成功",30]
          - ["TC3-sealUpperText 为特殊字符","!@#\\/:*?\"<>|",1,200,"成功",30]
          - ["TC4-sealUpperText 为英文全角","ｑｕａｎｊｉａｏ",1,200,"成功",30]
          - ["TC5-sealUpperText 为英文大小写","ban(TOP)jiao ",1,200,"成功",30]
          - ["TC6-sealUpperText 为繁体字"," 發纔䶮  ",1,200,"成功",30]
          - ["TC7-sealUpperText 为新疆名","达吾提·阿西木 ",1,200,"成功",30]
          - ["TC8-sealUpperText 为英文名"," Rosalette Hazal Royston ",1,200,"成功",30]
          - ["TC9-sealUpperText 为一个字","T",1,1325019,"sealUpperText错误：印章上弦文{T}不能超出区间[2,50]字",0]
          - ["TC10-sealUpperText 为50字","${generate_random_str(50)}",1,200,"成功",30]
          - ["TC11-sealUpperText 为51字","${generate_random_str(51)}",1,1325019,"不能超出区间",0]
          - ["TC12-sealBottomText 2-椭圆形章不带五角星","${generate_random_str(10)}",2,200,"成功",30]
          - ["TC13-sealBottomText 3-圆形章带五角星","${generate_random_str(10)}",3,200,"成功",30]
          - ["TC14-sealBottomText 4-圆形章不带五角星","${generate_random_str(10)}",4,200,"成功",30]
          - ["TC15-sealBottomText 5-双椭圆","${generate_random_str(20)}",5,200,"成功",30]
          - ["TC16-sealBottomText 6-动态串码章","${generate_random_str(20)}",6,1325024,"动态编码章横向文一不能为空",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验校验 sealHorizontalTextfirst 横向文一（不超过20字） 非必填数据的校验
    parameters:
      - name-sealHorizontalTextfirst-sealTemplateStyle-code0-message0-data0:
          - ["TC1-sealHorizontalTextfirst 为空","",6,1325024,"动态编码章横向文一不能为空",0]
          - ["TC2-sealHorizontalTextfirst 为null",null,6,1325024,"动态编码章横向文一不能为空",0]
          - ["TC3-sealHorizontalTextfirst 为特殊字符","!@#\\/:*?\"<>|",6,200,"成功",30]
          - ["TC4-sealHorizontalTextfirst 为英文全角","ｑｕａｎｊｉａｏ",6,200,"成功",30]
          - ["TC5-sealHorizontalTextfirst 为英文大小写","ban(TOP)jiao ",6,200,"成功",30]
          - ["TC6-sealHorizontalTextfirst 为繁体字"," 發纔䶮  ",6,200,"成功",30]
          - ["TC7-sealHorizontalTextfirst 为新疆名","达吾提·阿西木 ",6,200,"成功",30]
          - ["TC8-sealHorizontalTextfirst 为英文名"," lette Hazal Roy ",6,200,"成功",30]
          - ["TC9-sealHorizontalTextfirst 为一个字","T",6,200,"成功",30]
          - ["TC10-sealHorizontalTextfirst 为20字","${generate_random_str(20)}",6,200,"成功",30]
          - ["TC11-sealHorizontalTextfirst 为21字","${generate_random_str(21)}",6,1325023,"不能超出20字",0]
          - ["TC12-sealHorizontalTextfirst 2-椭圆形章不带五角星","${generate_random_str(10)}",2,200,"成功",30]
          - ["TC13-sealHorizontalTextfirst 3-圆形章带五角星","${generate_random_str(10)}",3,200,"成功",30]
          - ["TC14-sealHorizontalTextfirst 4-圆形章不带五角星","${generate_random_str(10)}",4,200,"成功",30]
          - ["TC15-sealHorizontalTextfirst 5-双椭圆","${generate_random_str(10)}",5,200,"成功",30]
          - ["TC16-sealHorizontalTextfirst 1-椭圆形章带五角星","${generate_random_str(10)}",1,200,"成功",30]
          - ["TC16-sealHorizontalTextfirst 1-动态编码章","${generate_random_str(10)}",6,200,"成功",30]
          - ["TC16-sealHorizontalTextfirst 1-动态编码章-横向文一为空","",6,1325024,"动态编码章横向文一不能为空",0]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验校验 sealHorizontalTextsecond 横向文二（不超过20字） 非必填数据的校验
    parameters:
      - name-sealHorizontalTextsecond-sealTemplateStyle-sealHorizontalTextfirst-code0-message0-data0:
          - ["TC1-sealHorizontalTextsecond 为空","",6,"Textfirst",200,"成功",30]
          - ["TC2-sealHorizontalTextsecond 为null",null,6,"Textfirst",200,"成功",30]
          - ["TC3-sealHorizontalTextsecond 为特殊字符","!@#\\/:*?\"<>|",6,"Textfirst",200,"成功",30]
          - ["TC4-sealHorizontalTextsecond 为英文全角","ｑｕａｎｊｉａｏ",6,"Textfirst",200,"成功",30]
          - ["TC5-sealHorizontalTextsecond 为英文大小写","ban(TOP)jiao ",6,"Textfirst",200,"成功",30]
          - ["TC6-sealHorizontalTextsecond 为繁体字"," 發纔䶮  ",6,"Textfirst",200,"成功",30]
          - ["TC7-sealHorizontalTextsecond 为新疆名","达吾提·阿西木 ",6,"Textfirst",200,"成功",30]
          - ["TC8-sealHorizontalTextsecond 为英文名"," lette Hazal Roy ",6,"Textfirst",200,"成功",30]
          - ["TC9-sealHorizontalTextsecond 为一个字","T",6,"Textfirst",200,"成功",30]
          - ["TC10-sealHorizontalTextsecond 为20字","${generate_random_str(20)}",6,"Textfirst",200,"成功",30]
          - ["TC11-sealHorizontalTextsecond 为21字","${generate_random_str(21)}",6,"Textfirst",1325025,"不能超出20字",0]
          - ["TC12-sealHorizontalTextsecond 2-椭圆形章不带五角星","${generate_random_str(30)}",2,"",200,"成功",30]
          - ["TC13-sealHorizontalTextsecond 3-圆形章带五角星","${generate_random_str(30)}",3,"",200,"成功",30]
          - ["TC14-sealHorizontalTextsecond 4-圆形章不带五角星","${generate_random_str(30)}",4,"",200,"成功",30]
          - ["TC15-sealHorizontalTextsecond 5-双椭圆","${generate_random_str(30)}",5,"",200,"成功",30]
          - ["TC16-sealHorizontalTextsecond 1-椭圆形章带五角星","${generate_random_str(30)}",1,"Textfirst",200,"成功",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验校验 sealHorizontalText 横向文（不超过15字） 非必填数据的校验
    parameters:
      - name-sealHorizontalText-sealTemplateStyle-code0-message0-data0:
          - ["TC1-sealHorizontalText 为空","",1,200,"成功",30]
          - ["TC2-sealHorizontalText 为null",null,1,200,"成功",30]
          - ["TC3-sealHorizontalText 为特殊字符","!@#\\/:*?\"<>|",1,200,"成功",30]
          - ["TC4-sealHorizontalText 为英文全角","ｑｕａｎｊｉａｏ",1,200,"成功",30]
          - ["TC5-sealHorizontalText 为英文大小写","ban(TOP)jiao ",1,200,"成功",30]
          - ["TC6-sealHorizontalText 为繁体字"," 發纔䶮  ",1,200,"成功",30]
          - ["TC7-sealHorizontalText 为新疆名","达吾提·阿西木 ",1,200,"成功",30]
          - ["TC8-sealHorizontalText 为英文名"," lette Hazal Roy ",1,200,"成功",30]
          - ["TC9-sealHorizontalText 为一个字","T",1,200,"成功",30]
          - ["TC10-sealHorizontalText 为15字","${generate_random_str(15)}",1,200,"成功",30]
          - ["TC11-sealHorizontalText 为16字","${generate_random_str(16)}",1,1325022,"不能超出15字",0]
          - ["TC12-sealHorizontalText 2-椭圆形章不带五角星","${generate_random_str(10)}",2,200,"成功",30]
          - ["TC13-sealHorizontalText 3-圆形章带五角星","${generate_random_str(10)}",3,200,"成功",30]
          - ["TC14-sealHorizontalText 4-圆形章不带五角星","${generate_random_str(10)}",4,200,"成功",30]
          - ["TC15-sealHorizontalText 5-双椭圆","${generate_random_str(20)}",5,200,"成功",30]
          - ["TC16-sealHorizontalText 6-动态串码章","",6,1325024,"动态编码章横向文一不能为空",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验校验 sealBottomText 下弦文（15字） 非必填数据的校验
    parameters:
      - name-sealBottomText-sealTemplateStyle-code0-message0-data0:
          - ["TC1-sealBottomText 为空","",1,200,"成功",30]
          - ["TC2-sealBottomText 为null",null,1,200,"成功",30]
          - ["TC3-sealBottomText 为特殊字符","!@#\\/:*?\"<>|",1,200,"成功",30]
          - ["TC4-sealBottomText 为英文全角","ｑｕａｎｊｉａｏ",1,200,"成功",30]
          - ["TC5-sealBottomText 为英文大小写","ban(TOP)jiao ",1,200,"成功",30]
          - ["TC6-sealBottomText 为繁体字"," 發纔䶮  ",1,200,"成功",30]
          - ["TC7-sealBottomText 为新疆名","达吾提·阿西木 ",1,200,"成功",30]
          - ["TC8-sealBottomText 为英文名"," lette Hazal Roy ",1,200,"成功",30]
          - ["TC9-sealBottomText 为一个字","T",1,200,"成功",30]
          - ["TC10-sealBottomText 为15字","${generate_random_str(15)}",1,200,"成功",30]
          - ["TC11-sealBottomText 为16字","${generate_random_str(16)}",1,1325021,"不能超出15字",0]
          - ["TC12-sealBottomText 2-椭圆形章不带五角星","${generate_random_str(10)}",2,200,"成功",30]
          - ["TC13-sealBottomText 3-圆形章带五角星","${generate_random_str(10)}",3,200,"成功",30]
          - ["TC14-sealBottomText 4-圆形章不带五角星","${generate_random_str(10)}",4,200,"成功",30]
          - ["TC15-sealBottomText 5-双椭圆","${generate_random_str(10)}",5,200,"成功",30]
          - ["TC16-sealBottomText 6-动态串码章","${generate_random_str(20)}",6,1325024,"动态编码章横向文一不能为空",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

-
    name: 创建企业印章-校验校验ensealUpperText 英文上弦文（50字） 非必填数据的校验5-双椭圆章不带五角星生效
    parameters:
      - name-ensealUpperText-sealTemplateStyle-code0-message0-data0:
          - ["TC1-ensealUpperText 为空","",5,200,"成功",30]
          - ["TC2-ensealUpperText 为null",null,5,200,"成功",30]
          - ["TC3-ensealUpperText 为特殊字符","!@#\\/:*?\"<>|",5,200,"成功",30]
          - ["TC4-ensealUpperText 为英文全角","ｑｕａｎｊｉａｏ",5,1325080,"ensealUpperText错误：只支持英文或英文状态下的符号",0]
          - ["TC5-ensealUpperText 为英文大小写","ban(TOP)jiao ",5,200,"成功",30]
          - ["TC6-ensealUpperText 为繁体字"," 發纔䶮  ",5,1325080,"ensealUpperText错误：只支持英文或英文状态下的符号",0]
          - ["TC7-ensealUpperText 为新疆名","达吾提·阿西木 ",5,1325080,"ensealUpperText错误：只支持英文或英文状态下的符号",0]
          - ["TC8-ensealUpperText 为英文名"," Rosalette Hazal Royston ",5,200,"成功",30]
          - ["TC9-ensealUpperText 为一个字","T",5,200,"成功",30]
          - ["TC10-ensealUpperText 为50字","${generate_random_str(50)}",5,200,"成功",30]
          - ["TC11-ensealUpperText 为51字","${generate_random_str(51)}",5,1325020,"不能超出50字",0]
          - ["TC12-ensealUpperText 2-椭圆形章不带五角星","${generate_random_str(51)}",2,200,"成功",30]
          - ["TC13-ensealUpperText 3-圆形章带五角星","圆形章带五角星",3,200,"成功",30]
          - ["TC14-ensealUpperText 4-圆形章不带五角星","圆形章不带五角星",4,200,"成功",30]
          - ["TC15-ensealUpperText 1-椭圆形章带五角星","椭圆形章带五角星",5,1325080,"ensealUpperText错误：只支持英文或英文状态下的符号",30]
          - ["TC16-ensealUpperText 6-动态串码章","动态串码章",6,1325024,"动态编码章横向文一不能为空",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml
