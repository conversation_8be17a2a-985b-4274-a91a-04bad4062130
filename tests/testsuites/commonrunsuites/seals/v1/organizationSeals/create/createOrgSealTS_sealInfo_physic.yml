config:
    name: 创建企业印章校验-校验物理章
    variables:
      sealTypeCode0: "COMMON-SEAL"
#      org01Code: ${ENV(sign01.main.orgCode)}
#      org02Code: ${ENV(csqs.orgCode)}
#      org01CertId_rsa: ${getOrganizationCertsByStatus($org01Code)}
#      org01CertId: ${getOrganizationCertsByStatus($org01Code,1,2)}
#      org02CertId: ${getOrganizationCertsByStatus($org02Code,1,2)}

testcases:
-
    name: 创建企业印章-校验物理印章
    parameters:
      - name-sealPattern-sealSource-sealTemplateStyle-sealMaterial-sealShape-code0-message0-data0:
          - ["TC1-sealMaterial 为空,默认1-橡胶章",2,2,1,"",1,200,"成功",30]
          - ["TC2-sealMaterial 0",2,2,1,0,1,1325012,"sealMaterial错误：印章材质{0}非法",0]
          - ["TC3-sealMaterial -1",2,2,1,-1,1,1325012,"sealMaterial错误：印章材质",0]
          - ["TC4-sealMaterial 0.01",2,2,1,0.01,1,1325012,"sealMaterial错误：印章材质",0]
          - ["TC5-sealMaterial True",2,2,1,True,1,1322222,"不支持传入特殊字符",0]
          - ["TC6-sealMaterial 是",2,2,1,"是",1,1322222,"不支持传入特殊字符",0]
          - ["TC7-sealMaterial 6",2,2,1,6,1,1325012,"sealMaterial错误：印章材质{6}非法",0]
          - ["TC8-sealMaterial 非物理章不起效",1,2,1,6,1,200,"成功",30]
          - ["TC9-sealMaterial 1-橡胶章",2,2,1,1,1,200,"成功",30]
          - ["TC10-sealMaterial 2-牛角章",2,2,1,2,1,200,"成功",30]
          - ["TC11-sealMaterial 3-铜章",2,2,1,3,1,200,"成功",30]
          - ["TC12-sealMaterial 4-回墨章",2,2,1,4,1,200,"成功",30]
          - ["TC13-sealMaterial 5-光敏章",2,2,1,5,1,200,"成功",30]

          - ["TC14-sealShape 为空,默认1-椭圆形",2,2,1,1,"",200,"成功",30]
          - ["TC15-sealShape 0",2,2,1,1,0,1325013,"sealShape错误：印章形状{0}非法",0]
          - ["TC16-sealShape -1",2,2,1,1,-1,1325013,"sealShape错误：印章形状{-1}非法",0]
          - ["TC17-sealShape 0.01",2,2,1,1,0.01,1325013,"sealShape错误：印章形状{0}非法",0]
          - ["TC18-sealShape True",2,2,1,1,True,1322222,"不支持传入特殊字符",0]
          - ["TC19-sealShape 是",2,2,1,1,"是",1322222,"不支持传入特殊字符",0]
          - ["TC20-sealShape 5",2,2,1,5,5,1325013,"sealShape错误：印章形状{5}非法",0]
          - ["TC21-sealShape 非物理章不起效",1,2,1,5,1,200,"成功",30]
          - ["TC22-sealShape 1-椭圆形",2,2,1,5,1,200,"成功",30]
          - ["TC23-sealShape 2-圆形",2,2,1,4,2,200,"成功",30]
          - ["TC24-sealShape 3-长方形",2,2,1,3,3,200,"成功",30]
          - ["TC25-sealShape 4-正方形",2,2,1,2,4,200,"成功",30]
    testcase: testcases/seals/v1/organizationSeals/createOrgSealTC_sealInfo_common.yml

