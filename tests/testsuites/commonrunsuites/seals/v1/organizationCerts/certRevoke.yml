config:
    name: 吊销企业证书
    variables:
        certId101_revoke: "abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a"
        organizationCode1: ${ENV(sign01.main.orgCode)}
        certIdRSA_revoke: ${select_enterprise_certId($organizationCode1,1,1,0)} #生效中的rsa证书
        certIdSM2_revoke: ${select_enterprise_certId($organizationCode1,1,2,0)} #生效中的sm2证书
        certIdDel_revoke: ${select_enterprise_certId($organizationCode1,1,1,1)} #已删除的rsa证书
#        certIdOver_revoke: ${select_enterprise_certId($organizationCode1,3,1,0)} #已过期的rsa证书
        certIdOverSM2_revoke: ${select_enterprise_certId($organizationCode1,3,2,0)} #已过期的SM2证书
        certIdRevo_revoke: ${select_enterprise_certId($organizationCode1,4,1,0)} #已吊销的rsa证书
  
testcases:
-
    name: 吊销企业证书
    parameters:
      - name-certId-code-message-data:
          - ["certId为空","",1316045,"企业证书id不能为空",null]
          - ["正常吊销商密签章者证书",$certIdRSA_revoke,200,"成功",""]
          - ["正常吊销国密签章者证书",$certIdSM2_revoke,200,"成功",""]
          - ["吊销企业证书报错","1722857737082507266",1316002,"该证书不存在!",null]
          - ["certID为不支持的特殊字符","!@#\\/:*?\"<>|",1316002,"该证书不存在!",null]
          - ["证书已吊销",$certIdRevo_revoke,1316036,"证书吊销失败，状态不正确",null]
          - ["certId长度为101",$certId101_revoke,1316002,"该证书不存在!",null]
#          - ["证书已过期",$certIdOver,1316002,"该证书不存在!",null]
          - ["证书已删除",$certIdDel_revoke,1316002,"该证书不存在!",null]
    testcase: testcases/seals/v1/sealcontrols/organizationCerts/certRevoke.yml

