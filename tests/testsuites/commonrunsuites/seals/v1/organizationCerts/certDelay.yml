config:
    name: 延期企业证书
    variables:
        certId101: "abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a"
        organizationCode1: ${ENV(sign01.main.orgCode)}
        certIdRSA: ${select_enterprise_certId($organizationCode1,1,1,0)} #生效中的rsa证书
        certIdSM2: ${select_enterprise_certId($organizationCode1,1,2,0)} #生效中的sm2证书
#        certIdDel: ${select_enterprise_certId($organizationCode1,1,1,1)} #已删除的rsa证书
        certIdOver: ${select_enterprise_certId($organizationCode1,3,1,0)} #已过期的rsa证书
        certIdOverSM2: ${select_enterprise_certId($organizationCode1,3,2,0)} #已过期的SM2证书
        certIdRevo: ${select_enterprise_certId($organizationCode1,4,1,0)} #已吊销的rsa证书
        organizationCode: ${ENV(csqs.orgCode)}
        organizationName: ${ENV(csqs.orgName)}
        licenseNumber: ${ENV(licenseNumber)}
        certName: ${generate_random_str(5)}
#        cert_expired: ${createOrganizationCert($organizationCode, $organizationName, $licenseNumber, $certName)}

testcases:
-
    name: 延期企业证书
    parameters:
      - name-certId-code-message-data:
          - ["certId为空","",1316045,"企业证书id不能为空",null]
          - ["延期未过期国密签章者证书",$certIdRSA,1316020,"只有过期证书才能延期操作",null]
          - ["延期未过期签章者证书",$certIdSM2,1316020,"只有过期证书才能延期操作",null]
#          - ["certID为不支持的特殊字符","!@#\\/:*?\"<>|",1316002,"该证书不存在!",null]
          - ["延期已吊销证书",$certIdRevo,1316020,"只有过期证书才能延期操作",null]
          - ["certId长度为101",$certId101,1316002,"该证书不存在!",null]
#          - ["延期已过期证书",$certIdOver,200,"成功",""]
#          - ["延期已删除证书",$certIdDel,1316002,"该证书不存在!",null]
    testcase: testcases/seals/v1/sealcontrols/organizationCerts/certDelay.yml

