config:
    name: 创建企业云证书
    variables:
        organizationCode1: ${ENV(sign01.main.orgCode)}
        customOrgNo1: ${ENV(sign01.main.orgNo)}
        CIorgNo: ${ENV(csqs.orgNo)}
        code101: "abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文abc中文a"
        sp: " "
      

testcases:
-
    name: 创建企业证书
    parameters:
      - name-organizationCode-customOrgNo-algorithm-certName-code-message-data:
          - ["organizationCode和customOrgNo均为空","","",1,"",1325000,"企业编码和企业账号不能同时为空",10]
          - ["organizationCode为空，customOrgNo为正常值","",$customOrgNo1,1,"",200,"成功","certId"]
          - ["organizationCode为正常值customOrgNo为空",$organizationCode1,"",1,"",200,"成功","certId"]
          - ["organizationCode和customOrgNo均有值且匹配",$organizationCode1,$customOrgNo1,1,"",200,"成功","certId"]
          - ["organizationCode和customOrgNo均有值但不匹配以organizationCode为主",$organizationCode1,$CIorgNo,1,"",200,"成功","certId"]
          - ["organizationCode为不存在的值",$organizationCode1 abc,"",1,"",1325003,"不存在",10]
          - ["customOrgNo为不存在的值","",$customOrgNo1 123,1,"",1325053,"customOrgNo错误",10]
          - ["organizationCode加前后空格",$sp $organizationCode1 $sp,"",1,"",200,"成功","certId"]
          - ["customOrgNo加前后空格","",$sp $customOrgNo1 $sp,1,"",200,"成功","customOrgNo"]
          - ["customOrgNo传入不支持的特殊字符","","!@#\\/:*?\"<>|",1,"",1325053,"customOrgNo错误",10]
          - ["organizationCode传入不支持的特殊字符","!@#\\/:*?\"<>|","",1,"",1325003,"organizationCode错误",10]
          - ["algorithm传入不支持的特殊字符","","","!@#\\/:*?\"<>|","",1322222,"不支持传入特殊字符",10]
          - ["algorithm传正常值1",$organizationCode1,"",1,"",200,"成功","certId"]
          - ["algorithm传正常值0","","",0,"",1325000,"企业编码和企业账号不能同时为空",10]
          - ["algorithm传入-1",$organizationCode1,"",-1,"",1316037,"algorithm错误",10]
          - ["algorithm传入小数2.1",$organizationCode1,"",2.1,"",200,"成功","certId"]
          - ["algorithm传入abc",$organizationCode1,"","abc","",1322222,"不支持传入特殊字符",10]
          - ["certName传入不支持的特殊字符",$organizationCode1,"",1,"!@#\\/:*?\"<>|",200,"成功","certId"]
          - ["certName传入101字符",$organizationCode1,"",1,$code101,1316038,"certName错误",10]
          - ["certName不传为空",$organizationCode1,"",1,"",200,"成功","certId"]
          - ["certName传入正常字符",$organizationCode1,"",1,"自动化测试创建证书",200,"成功","certId"]
          - ["正常用例创建rsa证书",$organizationCode1,$customOrgNo1,1,"",200,"成功","certId"]
          - ["正常用例创建SM2证书",$organizationCode1,$customOrgNo1,2,"自动化创建sm2",200,"成功","certId"]
    testcase: testcases/seals/v1/sealcontrols/organizationCerts/create.yml