config:
  name: manage
testcases:
-
    name: 根据文件编码获取文件配置fileCode校验2J2ye5
    testcase: testcases/seals/manage/appconfig/file/getFileConfigByCode/getFileConfigByCode_fileCode.yml

-
    name: 根据参数编码获取某一节点一级参数项parameterClassCode校验deGVTy
    testcase: testcases/seals/manage/appconfig/parameter/getFirstParameterItemByItemCode/getFirstParameterItemByItemCode_parameterClassCode.yml

-
    name: 根据参数编码获取某一节点一级参数项parameterItemCode校验j6RrF4
    testcase: testcases/seals/manage/appconfig/parameter/getFirstParameterItemByItemCode/getFirstParameterItemByItemCode_parameterItemCode.yml

-
    name: 获取机构实名链接0BSGRX
    testcase: testcases/seals/manage/org/realName/realName_orgCode.yml

-
    name: 获取机构实名链接snnbBQ
    testcase: testcases/seals/manage/org/realName/realName_redirectUrl.yml

-
    name: 获取当前登录用户所属一级公司3u5FeA
    testcase: testcases/seals/manage/orguser/org/getLoginUserCompanyCode/getLoginUserCompanyCode.yml

-
    name: 组织列表：名称、code模糊查询organizationName校验jDkYWw
    testcase: testcases/seals/manage/orguser/org/getOrganizationListByOrgCodeName/getOrganizationListByOrgCodeName_organizationName.yml

-
    name: 组织列表：名称、code模糊查询organizationTerritory校验TowiXW
    testcase: testcases/seals/manage/orguser/org/getOrganizationListByOrgCodeName/getOrganizationListByOrgCodeName_organizationTerritory.yml

-
    name: 获取某一节点下列表8qQfAz
    testcase: testcases/seals/manage/orguser/org/getOrganizationListByPId/organizationTerritory.yml

-
    name: 获取某一节点下列表krdFv6
    testcase: testcases/seals/manage/orguser/org/getOrganizationListByPId/outFlag.yml

-
    name: 获取某一节点下列表iCOrla
    testcase: testcases/seals/manage/orguser/org/getOrganizationListByPId/parentOrganizationId.yml

-
    name: 通过用户组编码获取组员列表mWm3Xz
    testcase: testcases/seals/manage/orguser/usergroup/getUserListByUserGroupCode/userGroupCode.yml

-
    name: 企业列表(所有启用的即存续的组织不包括删除的)名称模糊查询(数据权限内)XIXGl7
    testcase: testcases/seals/manage/permissions/data/searchCompanyByName/organizationName.yml

-
    name: 模糊搜索用户（在当前用户的数据范围内）QuObeB
    testcase: testcases/seals/manage/permissions/data/searchUserByName/includePartTime.yml

-
    name: 获取当前登录用户的关的所有权限校验5c2RjM
    testcase: testcases/seals/manage/rolepermissions/navigation/v2/getUserNavigationByUserCode/getUserNavigationByUserCode_all.yml

-
    name: 根据用户编码获取关联的角色(包括兼职组织能力)校验TrAaFz
    testcase: testcases/seals/manage/rolepermissions/role/getRoleListByUserCode/getRoleListByUserCode_all.yml

-
    name: 根据角色编码获取关联的用户(包括所属组织、所在公司)roleCode校验GDO1mf
    testcase: testcases/seals/manage/rolepermissions/role/getUserOrgListByRoleCode/getUserOrgListByRoleCode_roleCode.yml

-
    name: 获取用户实名链接xq3w90
    testcase: testcases/seals/manage/user/realName/realName_redirectUrl.yml

-
    name: 获取机构实名链接s5QjT5
    testcase: testcases/seals/manage/user/realName/realName_userCode.yml

-
    name: 业务调用保存协作businessClass字段校验YCIpOd
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_businessClass.yml

-
    name: 业务调用保存协作callbackParam字段校验PzDMnx
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_callbackParam.yml

-
    name: 业务调用保存协作callbackUrl字段校验rmiBXb
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_callbackUrl.yml

-
    name: 业务调用保存协作isSubmit字段校验1o2Gyp
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_isSubmit.yml

-
    name: 业务调用保存协作todoTaskId字段校验cVHrMn
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_todoTaskId.yml

-
    name: 业务调用保存协作workflowInstanceId字段校验71vtLd
    testcase: testcases/seals/manage/workflow/cooperation/saveCooperation/saveCooperation_workflowInstanceId.yml

-
    name: 业务调用发起协作接口businessClass字段校验mg6Vv5
    testcase: testcases/seals/manage/workflow/cooperation/startCooperation/startCooperation_businessClass.yml

-
    name: 业务调用发起协作接口userCodeList字段校验Ydey3u
    testcase: testcases/seals/manage/workflow/cooperation/startCooperation/startCooperation_userCodeList.yml

-
    name: 业务调用发起协作接口workflowInstanceId字段校验yB7onc
    testcase: testcases/seals/manage/workflow/cooperation/startCooperation/startCooperation_workflowInstanceId.yml

-
    name: 业务调用提交协作businessClass字段校验r8JvMw
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_businessClass.yml

-
    name: 业务调用提交协作callbackParam字段校验vmONMa
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_callbackParam.yml

-
    name: 业务调用提交协作callbackUrl字段校验wzJUJL
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_callbackUrl.yml

-
    name: 业务调用提交协作isSubmit字段校验tNuIDv
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_isSubmit.yml

-
    name: 业务调用提交协作todoTaskId字段校验UXuetf
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_todoTaskId.yml

-
    name: 业务调用提交协作workflowInstanceId字段校验o5Znnx
    testcase: testcases/seals/manage/workflow/cooperation/submitCooperation/submitCooperation_workflowInstanceId.yml
    