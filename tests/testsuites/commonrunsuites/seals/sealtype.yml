config:
  name: 纯电子印控
testcases:
    -
        name: 校验印章类型编码是否重复
        testcase: /testcases/seals/seals/sealtype/checkSealTypeCodeDitto/checkSealTypeCodeDitto_sealTypeCode.yml
    -
        name: 校验印章类型编码是否重复
        testcase: /testcases/seals/seals/sealtype/checkSealTypeContainFlow/sealType.yml
    -
        name: 校验印章类型名称是否重复-印章类型名
        testcase: /testcases/seals/seals/sealtype/checkSealTypeName/checkSealTypeName_sealTypeName.yml
    -
        name: 删除印章类型
        testcase: /testcases/seals/seals/sealtype/deleteSealType/id.yml
    -
        name: 查看指定流程图图片
        testcase: /testcases/seals/seals/sealtype/getFlowInfoDetail/modelKey.yml
    -
        name: 印章类型列表接口
        testcase: /testcases/seals/seals/sealtype/getSmcSealTypeList/isShowPersonalSeal.yml
    -
        name: 保存印章类型
        testcase: /testcases/seals/seals/sealtype/saveSealType/saveSealType_sealTypeName.yml
    -
        name: 编辑印章类型
        testcase: /testcases/seals/seals/sealtype/updateSealType/updateSealType_sealTypeName.yml