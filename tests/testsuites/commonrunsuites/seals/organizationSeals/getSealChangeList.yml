config:
  name: 分页查询换章申请列表

testcases:
  -
    name: 分页查询换章申请列表 currPage
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_currPage.yml

  -
    name: 分页查询换章申请列表 pageSize
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_pageSize.yml

  -
    name: 分页查询换章申请列表 endTime
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_endTime.yml

  -
    name: 分页查询换章申请列表 startTime
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_startTime.yml

  -
    name: 分页查询换章申请列表 sealChangeStatus
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_sealChangeStatus.yml

  -
    name: 分页查询换章申请列表 sealChangeUserName
    testcase: testcases/seals/seals/changeseal/getSealChangeList/getSealChangeList_sealChangeUserName.yml


