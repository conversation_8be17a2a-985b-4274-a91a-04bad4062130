- config:
    name: "印章管理员新建多个印章：印章物理+电子云国际标准+电子云中国标准+电子ukey国际标准，带审批流程，不勾选自动发布印章"
    base_url: ${ENV(esign.projectHost)}
    variables:
      param_sealTypeName: ${generate_random_str(8)}
      param_sealTypeCode: ${generate_random_str(8)}
      param_organizationId: ${ENV(organizationId)}
      param_organizationName: ${ENV(csqs.orgName)}
      param_organizationCode: ${ENV(csqs.orgCode)}
      param_sealCode: ${generate_random_str(10)}
      param_sealName: ${generate_random_str(10)}
      groupName: "公章"  #${ENV(groupName)}
      sealGroupId00: ${getGroupId($groupName)}
      defaultsealId00: ${ENV(orgguomidefaultsealid)}
      sealPatterns: ["3"]
      nextAssigneeList: null

- test:
    name: 执行前数据准备 - 新增印章类型配置
    api: api/esignSeals/seals/sealtype/saveSealType.yml
    variables:
        id:
        sealTypeName: $param_sealTypeName
        sealTypeCode: $param_sealTypeCode
        sealTypeStatus:
    extract:
        status: json.status
        success : json.success
        ex_sealTypeId: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - len_gt: [ $ex_sealTypeId, 1]

- test:
    name: 获取一个fileKey
    api: api/esignSeals/seals/enterprise/electronic/previewElectronicSeal.yml
    variables:
      "sealBodyStructure": "1"
      "sealBottomword": "测试22"
      "sealCenterImg": "2"
      "sealColour": "1"
      "sealHeight": 42
      "sealWidth": 42
      "sealHorizontalOneText": ""
      "sealHorizontalText": "测试22"
      "sealHorizontalTwoText": ""
      "sealOpacity": 0.6
      "sealShape": "1"
      "sealSource": "2"
      "sealSurroundword": "$param_organizationName"
      "innerTopSurroundText": "123"
      "oldStyle":
    extract:
      status: json.status
      success : json.success
      cryptoFileKey: json.data.cryptoFileKey
      fileKey : json.data.fileKey
    validate:
       - eq: [ "status_code", 200]
       - eq: [ $status, 200 ]
       - eq: [ $success, true ]

- test:
    name: 获取制章者国密证书
    api: api/esignSeals/certs/enterprise/getSm2CertListV2.yml
    variables:
        currPage: 1
        legelPersonal: false
        organizationCode: $param_organizationCode
        pageSize: 10
    extract:
        status: json.status
        success : json.success
        makeSealCertId: json.data.list.0.id
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [content.message, "成功"]

- test:
    name: 获取签章者国密证书
    api: api/esignSeals/certs/enterprise/getSm2CertList.yml
    variables:
        currPage: 1
        legelPersonal: false
        organizationCode: $param_organizationCode
        pageSize: 10
    extract:
        status: json.status
        success : json.success
        signSealCertIds: json.data.list.0.id
        makeSealCertName: json.data.list.0.certName
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [content.message, "成功"]

- test:
    name: 获取制章者国密证书
    api: api/esignSeals/certs/enterprise/getSm2CertListV2.yml
    variables:
        currPage: 1
        legelPersonal: false
        organizationCode: ${ENV(sign01_main_orgCode)}
        pageSize: 10
    extract:
        status: json.status
        success : json.success
        makeSealCertId: json.data.list.0.id
        signSealCertNameList: json.data.list.0.certName
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [content.message, "成功"]


- test:
    name: 正常创建企业国密电子印章
    api: api/esignSeals/seals/enterprise/electronic/createSealBywebapi.yml
    variables:
      autoPushSeal: 1
      draftOrProduction: 1
      sealGroupConfig: {
            "organizationCode":"$param_organizationCode",
            "organizationName":"$param_organizationName",
            "sealTypeCode":"COMMON-SEAL",
            "sealTypeName":"公章",
            "sealGroupId": "$sealGroupId00",
            "sealGroupName":"",
            "sealGroupDesc":"",
            "sealTypeModelKey":
      }
      sealConfigList:  {
        "sealMedium":"1",
        "sealBodyStructure":"2",
        "personalSealId":null,
        "personalSealName":null,
        "reuseSealId":"",
        "makeSealCertId": "$makeSealCertId",
        "signSealCertIds": ["$signSealCertIds"],
        "makeSealCertName": "$makeSealCertName",
        "signSealCertNameList": ["$signSealCertNameList"],
        "sealCategory":"1",
        "organizationCode":"$param_organizationCode",
        "organizationName":"$param_organizationName",
        "sealStatus":"1",
        "sealName":"esigntest自动化CI测试国密印章最后吊销",
        "canDelete":true,
        "canView":true,
        "sealUrl":"",
        "sealSurroundword":"$param_organizationName",
        "sealBottomword":"",
        "sealHorizontalOneText":"",
        "sealHorizontalText":"",
        "sealHorizontalTwoText":"",
        "sealCenterImg":"2",
        "sealHeight":42,
        "sealWidth":42,
        "sealSize":42,
        "innerTopSurroundText":"",
        "borderWidth":1.2,
        "sealUserName":"",
        "sealShape":"1",
        "sealColour":"1",
        "sealOpacity":1,
        "stampRule":"0",
        "oldStyle":"",
        "fileKey":"",
        "cryptoFileKey":"$cryptoFileKey",
        "innerWidth":33,
        "innerHeight":18,
        "innerBorderWidth":0.5,
        "sealSource":"2",
        "sealThumbnailUrl":"$fileKey",
        "sealWidthPixels":"158.740157",
        "sealHeightPixels":"158.740157",
        "oId":"",
        "sealAdminList":[
        {
          "userCode":"",
          "userName":"",
          "organizationCode":"$param_organizationCode",
          "organizationName":"$param_organizationName"
        }
        ],
        "sealUserList":[

        ]
      }
    extract:
        status: json.status
        sealGroupId0 : json.data.sealGroupId
    validate:
        - eq: [$status, 200]
        - eq: [$success, true]
        - eq: [$sealGroupId0,$sealGroupId00]

- test:
    name: 制作印章操作日志查看
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId: $sealGroupId00
        bizSealId:
        currPage: 1
        pageSize: 10
        operateTypes: [1]
    extract:
        status: json.status
        success : json.success
        ex_sealTypeId: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]


- test:
    name: TC4-查询印章分组中已发布的的印章
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId00
      remoteSealId: ""
      sealStatus: "g"
      sealName: "esigntest自动化CI测试国密印章最后吊销"
    extract:
      sealId001: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

- test:
    name: 制作印章操作日志查看-设置封面
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId: $sealGroupId00
        bizSealId:
        currPage: 1
        pageSize: 10
        operateTypes: [11]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"设为封面"]

- test:
    name: 制作印章操作日志查看-制作印章
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [1]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"制作印章"]

- test:
    name: 制作印章操作日志查看-分配印章管理员
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [14]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"分配印章管理员"]

- test:
    name: 制作印章操作日志查看-发布印章
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [4]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"发布印章"]

- test:
    name: 将中国标准设置为默认
    api: api/esignSeals/seals/enterprise/electronic/setDefault.yml
    variables:
        sealId: $sealId001
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [$message,"成功"]

- test:
    name: 制作印章操作日志查看-设置成默认
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [10]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"设为默认"]


- test:
    name: 查询原有默认的sealid
    api: api/esignSeals/seals/smc/seals/enterprise/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupId00
      remoteSealId: $defaultsealId00
      sealStatus: "g"
      sealName: ""
    extract:
      sealId002: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]


- test:
    name: 将原有的默认印章重新设置成默认印章
    api: api/esignSeals/seals/enterprise/electronic/setDefault.yml
    variables:
        sealId: $sealId002
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [$message,"成功"]
    teardown_hooks: ["${sleep(5)}"]

- test:
    name: 制作印章操作日志查看-设置成默认
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId002
        currPage: 1
        pageSize: 10
        operateTypes:
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"设为默认"]

- test:
    name: TC4-用印人查询
    api: api/esignDocs/user/getUserListByUserCodeName.yml
    variables:
      userName: "测试印控"
    extract:
      id002: content.data.userList.0.id
      organizationCode002: content.data.userList.0.organizationCode
      companyName002: content.data.userList.0.companyName
      userCode002: content.data.userList.0.userCode
      userName002: content.data.userList.0.userName
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "成功" ]


- test:
    name: TC5-分配管理员
    api: api/esignSeals/seals/smc/seals/enterprise/addSealAdmin.yml
    variables:
      authorityUserList: [ {
        id: "$sealId001",
        userCode: "${ENV(userCode)}",
        userName: "测试印控",
        userDeptCode: "${ENV(csqs.orgCode)}",
        userDeptName: "${ENV(csqs.orgName)}"
      }
      ]
      sealId: $sealId001
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]


- test:
    name: 授权用印人-全员使用
    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
    variables:
        sealUseRange: 1
        sealUserList:
        sealId: $sealId001
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [$message,"成功"]

- test:
    name: 制作印章操作日志查看-授权用印人
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [12]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"授权用印人"]

- test:
    name: 授权用印人-授权用印人
    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
    variables:
        sealUseRange: 2
        sealUserList: [
      {
        "userCode":"${ENV(sign01.userCode)}",
        "userName":"${ENV(sign01.userName)}",
        "userDeptCode":"${ENV(sign01.main.orgCode)}",
        "userDeptName":"${ENV(sign01.main.orgName)}",
        "id":""
      }
    ]
        sealId: $sealId001
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [$message,"成功"]

- test:
    name: 制作印章操作日志查看-授权用印人
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [12]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"授权用印人"]

- test:
    name: 停用已发布的电子印章--停用
    api: api/esignSeals/seals/enterprise/electronic/stopEnterprise.yml
    variables:
        sealId: $sealId001
    extract:
        status: json.status
        success : json.success
        message: json.message
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - contains: [$message,"成功"]

- test:
    name: 制作印章操作日志查看-停用记录
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [6]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"停用印章"]

- test:
    name: 吊销印章
    api: api/esignSeals/seals/enterprise/electronic/revoke.yml
    variables:
        sealId: $sealId001
    extract:
        status: json.status
        success : json.success
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]

- test:
    name: 制作印章操作日志查看-吊销记录
    api: api/esignSeals/seals/enterprise/electronic/sealOperateLogList.yml
    variables:
        sealGroupId:
        bizSealId: $sealId001
        currPage: 1
        pageSize: 10
        operateTypes: [7]
    extract:
        status: json.status
        success : json.success
        operateType: json.data.list.0.operateType
    validate:
        - eq: [ $status, 200]
        - eq: [ $success, true]
        - eq: [$operateType,"吊销印章"]