config:
  name: 查询用印宝列表
  variables:
    - accountNumber: ${ENV(csqs.accountNo)}
    - passwordEncrypt: ${ENV(passwordEncrypt)}
    - currPage1: 1
    - deviceCode1: ""
    - deviceMac1: ""
    - deviceName1: ""
    - manufacturerId1: ""
    - pageSize1: 20
    - sealBinded1: true
testcases:
  - name: 查询用印宝列表
    parameters:
      - name-currPage-pageSize-deviceCode-deviceMac-deviceName-manufacturerId-sealBinded-success0-status0-message0:
         - [ "TC1-查询用印宝列表",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC2-查询用印宝列表-page为空","",$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,false,1300000,"当前页数不能为空"]
         - [ "TC3-查询用印宝列表-page为-1","-1",$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,false,1300000,"当前页号不能小于1"]
         - [ "TC4-查询用印宝列表-page为null",null,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,false,1300000,"当前页数不能为空"]
         - [ "TC5-查询用印宝列表-deviceCode为空",$currPage1,$pageSize1,null,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC6-查询用印宝列表-deviceCode不存在",$currPage1,$pageSize1,"$deviceCode1",$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC7-查询用印宝列表-deviceMac为空",$currPage1,$pageSize1,$deviceCode1,null,$deviceName1,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC8-查询用印宝列表-deviceMac不存在",$currPage1,$pageSize1,$deviceCode1,"$deviceMac1",$deviceName1,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC9-查询用印宝列表-deviceName为空",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,null,$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC10-查询用印宝列表-deviceName不存在",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,"$deviceName1",$manufacturerId1,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC11-查询用印宝列表-manufacturerId为空",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,null,$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC12-查询用印宝列表-manufacturerId不存在",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,"$manufacturerId1",$sealBinded1,true,200,"服务器成功返回"]
         - [ "TC13-查询用印宝列表-pageSize为空",$currPage1,"",$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,false,1300000,"每页记录数不能为空"]
         - [ "TC14-查询用印宝列表-pageSize为-1",$currPage1,"-1",$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,$sealBinded1,false,1300000,"每页记录数不能小于1"]
         - [ "TC15-查询用印宝列表-sealBinded为false",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,false,true,200,"服务器成功返回"]
         - [ "TC16-查询用印宝列表-sealBinded错误",$currPage1,$pageSize1,$deviceCode1,$deviceMac1,$deviceName1,$manufacturerId1,"$sealBinded1",true,200,"服务器成功返回"]
    testcase: testcases/seals/seals/smc/certs/enterprise/getDevicePageList.yml