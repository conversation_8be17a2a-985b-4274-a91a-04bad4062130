config:
  name: 创建印章分组单接口校验
  variables:
    - accountNumber: ${ENV(sign01.accountNo)}
    - passwordEncrypt: ${ENV(passwordEncrypt)}
    #   正常创建印章分组
    - sealGroupConfig1:
        organizationCode: ${ENV(sign01.main.orgCode)}
        sealGroupName: ${generate_random_str(8)}
        organizationName: ""
        sealGroupDesc: ""
        sealTypeCode: "COMMON-SEAL"
        sealTypeModelKey: ""
        sealTypeName: "公章"
        sealNumLimit: "0"
        sealGroupId: ""
    #  验证分组名称为空("")
    - sealGroupConfig2:
        organizationCode: ${ENV(sign01.main.orgCode)}
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: ""
        sealTypeName: "公章"
        sealNumLimit: "0"
    #  验证分组名称为空(null)
    - sealGroupConfig2_1:
        organizationCode: ${ENV(sign01.main.orgCode)}
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: null
        sealTypeName: "公章"
        sealNumLimit: "0"
    #    验证分组名称已存在
    - sealGroupConfig3:
        organizationCode: ${ENV(sign01.main.orgCode)}
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: "夏目"
        sealTypeName: "公章"
        sealNumLimit: "0"
    #    验证organizationCode不存在
    - sealGroupConfig4:
        organizationCode: "001010test"
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: ${generate_random_str(8)}
        sealTypeName: "公章"
        sealNumLimit: "0"
    #    验证organizationCode为空（""）
    - sealGroupConfig5:
        organizationCode: ""
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: ${generate_random_str(8)}
        sealTypeName: "公章"
        sealNumLimit: "0"
    #    验证organizationCode为空（null）
    - sealGroupConfig5_1:
        organizationCode: null
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: ${generate_random_str(8)}
        sealTypeName: "公章"
        sealNumLimit: "0"
    #    校验sealGroupName长度校验
    - sealGroupConfig6:
        organizationCode: ${ENV(sign01.main.orgCode)}
        sealTypeCode: "COMMON-SEAL"
        sealGroupName: ${generate_random_str(100)}
        sealTypeName: "公章"
        sealNumLimit: "0"
testcases:
  - name: 创建印章分组单接口校验
    parameters:
      - name-sealGroupConfig-success0-status0-message0:
          - [ "TC1-创建印章分组",$sealGroupConfig1,true,200,"服务器成功返回"]
          - [ "TC2-校验sealGroupName为空字符串",$sealGroupConfig2,false,1923005,"创建印章分组名称不能为空"]
          - [ "TC2_1-校验sealGroupName为空null",$sealGroupConfig2_1,false,1923005,"创建印章分组名称不能为空"]
          - [ "TC3-校验sealGroupName已存在",$sealGroupConfig3,false,1923004,"印章组已存在"]
          - [ "TC4-校验organizationCode不存在",$sealGroupConfig4,false,1312103,"企业不存在或企业当前为非存续状态"]
          - [ "TC5-校验organizationCode为空字符串",$sealGroupConfig5,false,1312103,"企业不存在或企业当前为非存续状态"]
          - [ "TC5_1-校验organizationCode为空null",$sealGroupConfig5_1,false,1312103,"企业不存在或企业当前为非存续状态"]
          - [ "TC6-校验sealGroupName长度校验",$sealGroupConfig6,false,1923006,"印章分组名称不允许超过30字符"]
    testcase: testcases/seals/seals/smc/seals/createGroup.yml
