config:
  name: 6080场景用例

testcases:
  -
    name: 印章模型调整-场景用例1
    testcase: testcases/seals/scene6080/6070Scence2.yml

  - name: 印章模型调整-场景用例2
    testcase: testcases/seals/scene6080/6075Scene.yml

  -
    name: 印章模型调整-场景用例3
    testcase: testcases/seals/scene6080/6081Scene.yml

  - name: 印章模型调整-场景用例4
    testcase: testcases/seals/scene6080/orgSealOperateLogTC.yml

#  - name: 印章模型调整-场景用例5
#    testcase: testcases/seals/scene6080/orgSealOperateLogTC.yml


  - name: 印章模型调整-场景用例6
    testcase: testcases/seals/scene6080/operateLogTC2.yml

  - name: 印章模型调整-场景用例7
    testcase: testcases/seals/scene6080/noneGroupScene.yml

  - name: 印章模型调整-场景用例8
    testcase: testcases/seals/scene6080/createPyhsicSealSceneTC.yml

  - name: 印章模型调整-场景用例9
    testcase: testcases/seals/scene6080/createOrgSealTC_scene.yml

  - name: 印章模型调整-场景用例10
    testcase: testcases/seals/scene6080/createOrgSealsTC_control3.yml

  - name: 印章模型调整-场景用例11
    testcase: testcases/seals/scene6080/createOrgSealsTC_control2.yml

  - name: 印章模型调整-场景用例12
    testcase: testcases/seals/scene6080/createOrgSealsTC_control1.yml

  - name: 印章模型调整-场景用例13
    testcase: testcases/seals/scene6080/createGroupWithWorkFlowSceneTC.yml

  - name: 印章模型调整-场景用例14
    testcase: testcases/seals/scene6080/createGroupSceneTC4.yml

  - name: 印章模型调整-场景用例15
    testcase: testcases/seals/scene6080/createGroupSceneTC3.yml

  - name: 印章模型调整-场景用例16
    testcase: testcases/seals/scene6080/createGroupSceneTC2.yml
  - name: 印章模型调整-场景用例17
    testcase: testcases/seals/scene6080/createGroupSceneTC.yml

  - name: 印章模型调整-场景用例18
    testcase: testcases/seals/scene6080/6070Scence2.yml

  - name: 印章模型调整-场景用例19
    testcase: testcases/seals/scene6080/6075Scene.yml

  - name: 印章模型调整-场景用例20
    testcase: testcases/seals/scene6080/6081Scene.yml

  - name: 印章模型调整-场景用例21
    testcase: testcases/seals/scene6080/createGroupLegalSceneTC.yml