name: '同步上传并分割文件pdf文件,文件大小100M内'
variables:
    fileName: '${ENV(fileNamePdf)}'
    fileType: 'multipart/form-data'
    requestID: '11234567890123456789012345678902'
    filePwd:

request:
    url: ${ENV(esign.gatewayHost)}/file/v1/pdf/uploadAndSpiltV2
    method: POST
    headers:
        X-timevale-project-id: ${ENV(esign.projectId)}
    files:
        file: [$fileName,'${getFileForUpload($fileName)}',$fileType]
    params:
        filePwd: $filePwd
        requestID: $requestID
