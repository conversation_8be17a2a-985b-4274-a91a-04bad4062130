name: '文件系统，生成上传链接'
variables:
    requestID: '11234567890123456789012345678902'
    expire:      #上传链接有效时间 不传默认5分钟 单位为秒
    type: 0     #获取上传链接类型 枚举值 0为uploadAndSpilt为同步上传(小于100M) 1为upload为异步上传
    uploadUrlJson:
        expire: $expire
        requestID: $requestID
        type: $type

request:
    url: ${ENV(esign.gatewayHost)}/file/v1/generateUploadUrl
    method: POST
    headers:
        Content-Type: 'application/json'
        x-timevale-project-id: ${ENV(esign.projectId)}
        x-timevale-signature: ${getSignature($uploadUrlJson)}
    json: $uploadUrlJson