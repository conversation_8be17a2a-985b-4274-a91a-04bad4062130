name: 预警管理-搜索内部组织机构用户列表
variables:
    notifyObject: 手机号、邮箱或者是姓名
    accountNumber: ${ENV(csqs.accountNo)}
    password: ${ENV(passwordEncrypt)}
request:
    url: ${ENV(esign.projectHost)}/evidence/alert/getUserList
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            notifyObject: $notifyObject
        domain: "evidence_system"