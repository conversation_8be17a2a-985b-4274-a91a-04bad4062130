name: 证据管理-分页获取存证失败记录
variables:
    currPage: 当前页数
    pageSize: 每页记录数
    accountNumber: ${ENV(csqs.accountNo)}
    password: ${ENV(passwordEncrypt)}
request:
    url: ${ENV(esign.projectHost)}/evidence/manage/getEvidenceFailedRecord
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            currPage: $currPage
            pageSize: $pageSize
        domain: "evidence_system"