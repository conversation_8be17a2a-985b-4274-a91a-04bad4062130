# 更新签署流程接口定义
name: 更新签署流程

variables:
  - signFlowId: ""
  - businessNo: ""
  - signFlowExpireTime: null
  - contractExpireTime: null
  - docConfidentialConfig:
  - json: {
    "signFlowId": $signFlowId,
    "businessNo": $businessNo,
    "signFlowExpireTime": $signFlowExpireTime,
    "contractExpireTime": $contractExpireTime,
    "docConfidentialConfig": $docConfidentialConfig
  }

request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/signFlow/update
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json
