name: 获取请求日志列表
variables:
  token0: ${getManageToken()}
  params: {"interfaceUrl":"","operationParameter":"","projectId":"","requestMode":"","requestStatus":"","responseParameter":"","createTime":["2024-08-01 00:00:00","2124-10-30 23:59:59"],"beginDate":"2024-08-01 00:00:00","endDate":"2124-10-30 23:59:59","currPage":1,"pageSize":10}

request:
    url: ${ENV(esign.projectHost)}/manage/systools/requestLog/getRequestLogPageList
    method: post
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json:
        domain: ${ENV(manage.domain)}
        params: $params