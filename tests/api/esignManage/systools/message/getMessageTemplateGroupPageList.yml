name: 消息模板分页列表
variables:
    currPage1: 1
    pageSize1: 10
    templateGroupName1: ""
    templateGroupId1: ""
    templateGroupType1: ""
    token0:  ${getManageToken()}
request:
    url: ${ENV(esign.projectHost)}/manage/message/messageManage/getMessageTemplateGroupPageList
    method: post
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json:
        domain: "admin_platform"
        params:
            templateGroupName: $templateGroupName1
            templateGroupId: $templateGroupId1
            templateGroupType: $templateGroupType1
            currPage: $currPage1
            pageSize: $pageSize1
