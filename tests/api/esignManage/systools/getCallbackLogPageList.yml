name: 获取回调日志
variables:
  pageSizeCallBack: 10
  currPageCallBack: 1
  statusCallBack: "1"  #1-发送成功，2-发送失败
  callBackType: "1"  #1-签署回调；2-配置签署区回调；3-物理用印回调
  callBackUrl:
  projectIdCallBack:
  CallBackData:
    domain: admin_platform
    params:
      status: $statusCallBack
      callBackType: $callBackType
      callBackUrl: $callBackUrl
      projectId: $projectIdCallBack
      pageSize: $pageSizeCallBack
      currPage: $currPageCallBack

request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $CallBackData
  method: post
  url: ${ENV(esign.projectHost)}/manage/systools/ecCallBackLog/getCallbackLogPageList