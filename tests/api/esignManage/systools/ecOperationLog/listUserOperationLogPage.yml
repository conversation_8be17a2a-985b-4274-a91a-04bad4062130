name: 审计日志
variables:
  token0: ${getManageToken()}
  data: {"params":{"userCodeOrName":"","organizationName":"","operationType":[],"bizCode":"0","platformType":"","createTime":["2024-08-01 00:00:00","2124-10-30 23:59:59"],"beginDate":"2024-08-01 00:00:00","endDate":"2124-10-30 23:59:59","currPage":1,"pageSize":10},"domain":"admin_platform"}
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: $data
    method: post
    params: {}
    url: ${ENV(esign.projectHost)}/manage/systools/ecOperationLog/listUserOperationLogPage
