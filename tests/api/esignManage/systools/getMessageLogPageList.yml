name: 获取消息日志
variables:
  pageSizeMsg: 10
  currPageMsg: 1
  resultStatusMsg: "1"  #1-发送成功，2-发送失败
  templateGroupNameMsg: #"邀请签署通知"。。。
  templateGroupIdMsg:  #"signflow_invite"。。。
  receiveTypeMsg: "3"  #3-用户编码；2-用户手机号；1-用户邮箱
  receiverMsg: "${ENV()}"
  channelCodeMsg:  #1-站内信，2-邮件，3-短信
  createTimeMsg:
  beginDateMsg: "2024-04-08 00:00:00"
  endDateMsg:
  msgData:
    domain: admin_platform
    params:
      resultStatus: $resultStatusMsg
      templateGroupName: $templateGroupNameMsg
      templateGroupId: $templateGroupIdMsg
      receiveType: $receiveTypeMsg
      receiver: $receiverMsg
      channelCode: $channelCodeMsg
      createTime: $createTimeMsg
      pageSize: $pageSizeMsg
      currPage: $currPageMsg
      beginDate: $beginDateMsg
      endDate: $endDateMsg

request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $msgData
  method: post
  url: ${ENV(esign.projectHost)}/manage/systools/ecMessageLog/getMessageLogPageList