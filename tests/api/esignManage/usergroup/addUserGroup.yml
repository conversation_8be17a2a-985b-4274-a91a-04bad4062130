
name: 新建用户组

request:
    url: ${ENV(esign.projectHost)}/manage/orguser/usergroup/saveUserGroup
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            ecUserIDList: $ecUserIDList
            userGroupCode: $userGroupCode
            userGroupDesc: $userGroupDesc
            userGroupName: $userGroupName
            userGroupType: $userGroupType
