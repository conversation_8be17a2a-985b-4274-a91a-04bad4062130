
name: 修改用户组

request:
    url: ${ENV(esign.projectHost)}/manage/orguser/usergroup/updateUserGroup
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            id: $id
            ecUserIDList: $ecUserIDList
            userGroupCode: $userGroupCode
            userGroupDesc: $userGroupDesc
            userGroupName: $userGroupName
            userGroupType: $userGroupType

