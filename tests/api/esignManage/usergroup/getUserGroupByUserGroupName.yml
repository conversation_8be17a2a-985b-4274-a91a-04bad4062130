
name: 通过用户组名字获取用户组列表分页

request:
    url: ${ENV(esign.projectHost)}/manage/orguser/usergroup/getUserGroupByUserGroupName
    method: post
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            currPage: $currPage
            pageSize: $pageSize
            userGroupName: $userGroupName
