name: "获取某一节点下列表"
variables:
  token_getOrganizationListByPId: ${getManageToken()}
  data: {"params":{"parentOrganizationId":"0","outFlag":false,"organizationTerritory":"1"},"domain":"admin_platform"}

request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: $token_getOrganizationListByPId
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/manage/orguser/org/getOrganizationListByPId