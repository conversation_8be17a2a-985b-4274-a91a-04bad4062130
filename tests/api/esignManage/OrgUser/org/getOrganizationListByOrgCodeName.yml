name: 组织信息-按organizationName
variables:
  domain: admin_platform
  data:
    customerIP:
    deptId:
    domain: $domain
    params:
      organizationName: $apiOrganizationName
      organizationTerritory: $apiOrganizationTerritory
      isCancel:
    platform:
    tenantCode: 1000
    userCode:

request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/manage/orguser/org/getOrganizationListByOrgCodeName
