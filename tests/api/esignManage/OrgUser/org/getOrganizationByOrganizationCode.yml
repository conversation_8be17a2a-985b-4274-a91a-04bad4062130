name: 根据组织Code获取组织
variables:
  params:
  data:
    customerIP:
    deptId:
    domain: "admin_platform"
    params: $params
    platform:
    tenantCode: 1000
    userCode:
request:
    url: ${ENV(esign.projectHost)}/manage/orguser/org/getOrganizationByOrganizationCode
    method: post
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json: $data