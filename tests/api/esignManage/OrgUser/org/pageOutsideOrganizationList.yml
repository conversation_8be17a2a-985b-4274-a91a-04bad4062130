name: 获取外部机构列表分页
variables:
  token0: ${getManageToken()}
  data: {"params":{"organizationName":"","accountNumber":"","pageSize":10,"currPage":1},"domain":"admin_platform"}

request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
        language: zh-CN
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/org/pageOutsideOrganizationList
