name: 保存组织
variables:
  domain: admin_platform
  data:
    customerIP:
    deptId:
    domain: $domain
    params:
      accountNumber: $accountNumber
      contactAddress:
      dingTalk:
      feiShu:
      licenseNumber: $licenseNumber
      licenseType: $licenseType
      orderNum: 100
      organizationName: $organizationName
      organizationStatus: $organizationStatus
      organizationTerritory: $organizationTerritory
      organizationType: $organizationType
      parentOrganizationCode: $parentOrganizationCode
      parentOrganizationId: $parentOrganizationId
      workWechat:
    platform:
    tenantCode: 1000
    userCode:

request:
  url: ${ENV(esign.projectHost)}/manage/orguser/org/saveOrganization
  method: post
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $data