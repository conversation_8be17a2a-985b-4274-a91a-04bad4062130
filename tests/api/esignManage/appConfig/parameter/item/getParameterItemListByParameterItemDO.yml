name: 通过参数项名字、分类等获取参数项列表
variables:
  token0: ${getManageToken()}
  params: {"outFlag":false,"parameterClassCode":"WJLX","parameterItemParentId":"0"}

request:
  url: ${ENV(esign.projectHost)}/manage/appconfig/parameter/item/getParameterItemListByParameterItemDO
  method: POST
  headers:
    Content-Type: "application/json;charset=UTF-8"
    token: $token0
  json:
    domain: "admin_platform"
    params: $params