name: 项目管理-查询项目列表
variables:
  token0: ${getManageToken()}
  data: {"params":{
    "pageSize":10,"currPage":1,
    "projectName":"$projectName_list",
    "projectId":"$projectId_list",
    "projectStatus":"$projectStatus_list"
  },"domain":"admin_platform"}
  projectName_list: ""
  projectId_list: ""
  projectStatus_list: ""
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/getProjectConfigPageList
    method: POST
    json: $data