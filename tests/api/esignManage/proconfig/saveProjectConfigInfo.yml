name: 项目管理-新建项目
variables:
  token0: ${getManageToken()}
  data: {
        "params": {
            "projectName": "$projectName_save",
            "projectId": "$projectId_save",
            "projectSecret": "$projectSecret_save",
            "projectDesc": "$projectDesc_save",
            "esignAppId": "$esignAppId_save",
            "esignAppSecret": "$esignAppSecret_save",
            "internalSignUrl": "$internalSignUrl_save",
            "idempotentSwitch": "0",
            "fileUrlType": "1",
            "downloadLinkTime": 60,
            "depositUrlTime": 10,
            "shortLinkTime": 30,
            "signCallbackUrl": "",
            "ipList": [

            ],
            "evidenceSwitch": "1",
            "evidenceSwitchF": true,
            "openApiAuthStatus": "$openApiAuthStatus_save"
        },
        "domain": "admin_platform"
    }
  projectName_save: ""
  projectId_save: ""
  projectSecret_save: ""
  projectDesc_save: ""
  esignAppId_save: ""
  esignAppSecret_save: ""
  internalSignUrl_save: "1"
  openApiAuthStatus_save: "0"
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/saveProjectConfigInfo
    method: POST
    json: $data