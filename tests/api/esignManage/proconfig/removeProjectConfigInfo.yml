name: 项目管理-启用或禁用项目
variables:
  token0: ${getManageToken()}
  id:
  id_remove: $id
  data: {
        "params":
            {
                "id": $id_remove
            },
        "domain": "admin_platform"
    }
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/removeProjectConfigInfo
    method: POST
    json: $data