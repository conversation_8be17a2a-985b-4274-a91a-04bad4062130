name: 项目管理-启用或禁用项目
variables:
  token0: ${getManageToken()}
  projectStatus: "2"
  id:
  data: {
        "params":
            {
                "id": $id, "projectStatus": $projectStatus
            },
        "domain": "admin_platform"
    }
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/openProjectConfigInfo
    method: POST
    json: $data