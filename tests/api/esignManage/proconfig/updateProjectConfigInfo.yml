name: 更新项目配置信息
variables:
  data:
    {
        "params": {
            "projectName": "$projectName_update",
            "projectId": "$projectId_update",
            "projectSecret": "$projectSecret_update",
            "projectDesc": "python测试项目更新",
            "esignAppId": "$esignAppId_update",
            "esignAppSecret": "$esignAppSecret_update",
            "internalSignUrl": "$internalSignUrl_update",
            "fileUrlType": "$fileUrlType_update",
            "downloadLinkTime": $downloadLinkTime_update,
            "depositUrlTime": $depositUrlTime_update,
            "shortLinkTime": $shortLinkTime_update,
            "signCallbackUrl": $signCallbackUrl_update,
            "ipList": [],
            "evidenceSwitch": "$evidenceSwitch_update",
            "evidenceSwitchF": $evidenceSwitchF_update,
            "openApiAuthStatus": "$openApiAuthStatus_update",
            "id": "$id_update",
            "esignAppIdCopy": "$esignAppId_update",
            "idempotentSwitch": "$idempotentSwitch"
        },
        "domain": "admin_platform"
    }
  id_update: ""
  projectName_update: ""
  projectId_update: ""
  projectSecret_update: ""
  esignAppId_update: ""
  esignAppSecret_update: ""
  internalSignUrl_update: "1"
  fileUrlType_update: "1"
  downloadLinkTime_update: 60
  depositUrlTime_update: 10
  shortLinkTime_update: 30
  signCallbackUrl_update: ""
  evidenceSwitch_update: "1"
  evidenceSwitchF_update: true
  openApiAuthStatus_update: "0"
  token0: ${getManageToken()}
  idempotentSwitch: "0"
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/updateProjectConfigInfo
