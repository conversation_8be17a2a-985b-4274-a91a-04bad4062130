
name: 接口管理-查询接口列表
variables:
  token0: ${getManageToken()}
  apiName: ""
  apiUrl: ""
  apiType: ""
  requestMethod: ""
  functionType: ""
  currPage: 1
  pageSize: 10

request:
  url: ${ENV(esign.projectHost)}/manage/apiconfig/apiConfig/getApiConfigPageList
  # url：${ENV(esign.projectHost)}/manage/apiconfig/apiConfig/getApiConfigPageList
  method: POST
  headers:
    Content-Type: application/json;charset=UTF-8
    token: $token0
    language: zh-CN

  json:
     params : {
       currPage: $currPage,
       pageSize: $pageSize,
       apiName: $apiName,
       apiUrl: $apiUrl,
       apiType: $apiType,
       requestMethod: $requestMethod,
       functionType: $functionType
  }
     domain: "admin_platform"