name: 项目管理-资源类和业务类接口
variables:
  token0: ${getManageToken()}
  data: {"params":{"projectId":"projectId_queryOpenApiListByType"},"domain":"admin_platform"}
  projectId_queryOpenApiListByType: ""
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/proconfig/projectConfig/queryOpenApiListByType
    method: POST
    json: $data