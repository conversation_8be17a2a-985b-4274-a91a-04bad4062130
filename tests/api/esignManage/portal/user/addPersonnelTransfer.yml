name: 新增人员调动
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId: "1764924302865543170"
request:
        headers: ${gen_token_header_permissions($accountNumber,$password,$navId)}
        json:
            domain: "unified_portal_service"
            params:
                personnelTransferDate: $personnelTransferDate
                personnelTransferDesc: $personnelTransferDesc
                toOrganizationId: $toOrganizationId
                userId: $userId
        method: post
        url: ${ENV(esign.projectHost)}/portal/orguser/personneltransfer/addPersonnelTransfer
