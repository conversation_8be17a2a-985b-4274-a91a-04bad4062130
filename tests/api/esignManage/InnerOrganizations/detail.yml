name: 查询内部组织详情
variables:
  organizationCode:
  customOrgNo:
  name:
  nameOrgDetail: $name
  orgDataDetail:
    organizationCode: $organizationCode
    customOrgNo: $customOrgNo
    name: $nameOrgDetail

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerOrganizations/detail
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($orgDataDetail)}
  json: $orgDataDetail
