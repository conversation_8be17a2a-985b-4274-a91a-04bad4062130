name: 新建内部组织
variables:
  certChargingProject:
  legalRepUserCode:
  legalRepAccountNo:
  licenseNo:
  licenseType:
  parentCode: "0"
  parentOrgNo:
  organizationType:
#  customOrgNo:
#  name:

  data:
    name: $name
    customOrgNo: $customOrgNo
    organizationType: $organizationType
    parentOrgNo: $parentOrgNo
    parentCode: $parentCode
    licenseType: $licenseType
    licenseNo: $licenseNo
    legalRepAccountNo: $legalRepAccountNo
    legalRepUserCode: $legalRepUserCode
    certChargingProject: $certChargingProject

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerOrganizations/create
  method: POST
  headers:
      Content-Type: 'application/json'
      x-timevale-project-id: ${ENV(esign.projectId)}
      x-timevale-signature: ${getSignature($data)}
  json: $data
