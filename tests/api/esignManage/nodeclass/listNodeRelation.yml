
name: 分页获取环节关联的处理人类型及按钮
variables:
  token0: ${getManageToken()}
  currPage: 1
  pageSize: 10
  nodeClassName: ""

request:
    url: ${ENV(esign.projectHost)}/manage/workflow/nodeclass/listNodeRelation
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: $token0
    json:
        domain: ${ENV(manage.domain)}
        params:
            currPage: $currPage
            nodeClassName: $nodeClassName
            pageSize: $pageSize
