name: 流程干预分页列表
variables:
  token0: ${getManageToken()}
  data: {"params":{"currPage":1,"pageSize":10,"workflowInstanceTitle":"","startUserName":"","startDeptName":"","workflowInstanceId":""},"domain":"admin_platform"}

request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage-flowable/flowable/manage/getAllWorkflowInstance
