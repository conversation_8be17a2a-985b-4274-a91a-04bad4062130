name: 流程绘制分页列表
variables:
  token0: ${getManageToken()}
  data: {"params":{"displayAllChild":"1","flowTypeCode":"0","modelStatus":"","organizationName":"","workflowName":"","currPage":1,"pageSize":10},"domain":"admin_platform"}

request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage-flowable/flowable/draw/flowModel/getFlowModelPageList
