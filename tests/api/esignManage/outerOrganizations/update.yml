name: 修改外部组织


variables:
  data: {
    organizationCode: $organizationCode,
    customOrgNo: $customOrgNo,
    name: $name,
    licenseType: $licenseType,
    licenseNo: $licenseNo,
    legalRepAccountNo: $legalRepAccountNo,
    legalRepUserCode: $legalRepUserCode
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/outerOrganizations/update
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($data)}
  json: $data