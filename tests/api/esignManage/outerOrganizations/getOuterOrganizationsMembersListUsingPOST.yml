name: 根据用户第三方信息查询个人相对方列表
base_url: ${ENV(manage_url)}
variables:
  organizationCode: 
  pageNo: 1
  pageSize: 10
  customOrgNo:
  name:
  json:
    customOrgNo: $customOrgNo   #组织账号（客户系统的组织唯一标识）
    organizationCode: $organizationCode   #组织编码（本系统的组织唯一标识）
    pageNo: $pageNo   #分页查询页码(分页查询页码不能小于1 不能大于10000000)
    pageSize: $pageSize   #分页查询单页数量(分页查询单页数量不能小于1 不能大于50)
    name:  $name
request:
  headers: ${gen_headers_signature($json)}
  url: ${ENV(manage_url)}/v1/userMembers/list
  method: post
  json: $json
