name: 管理平台-后台任务-下载失败原因
variables:
    token0: ${getManageToken()}
    userCode: "admin"
    businessId:
    cryptoFileKey:
    fileKey:
    fileName:
    type:
#type=1 文档中心批量模板发起
#type=2 证据中心单个证据报告下载
#type=3 文档中心历史导入文件任务
#type=4 文档中心导出明细任务
#type=5 文文档中心批量下载任务
#type=6 签署中心批量签署任务
#type=7 证据中心:证据报告批量下载任务
#type=8 文档中心：批量催办
#type=9  用量统计
#type=11 门户：批量转交
#type=13 批量导入组织和用户
request:
    url: ${ENV(esign.projectHost)}/manage/backend/download
    method: POST
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json:
        params:
            businessId: $businessId
            cryptoFileKey: $cryptoFileKey
            fileKey: $fileKey
            fileName: $fileName
            type: $type
            userCode: $userCode
        domain: "admin_platform"