name: 管理平台-后台任务-获取新的后台任务列表
variables:
    currPage: 1
    pageSize: 5
request:
    url: ${ENV(esign.projectHost)}/manage/backend/getNewBackendTaskListByTime
    method: POST
    headers:
      Content-Type: "application/json;charset=UTF-8"
      token: ${getManageToken()}
    json:
        params:
            currPage: $currPage
            pageSize: $pageSize
        domain: "admin_platform"
        userCode: ""