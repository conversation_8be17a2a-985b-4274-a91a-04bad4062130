name: 用户组权限列表
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            currPage: $currPage
            pageSize: $pageSize
            permissionDataType: $permissionDataType
            roleName: $roleName
            userGroupName: $userGroupName
    method: post
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/permission/getUserGroupPermissionList
