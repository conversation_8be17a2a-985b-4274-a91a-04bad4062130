name: 取消角色授权
variables:
  userCode: ${ENV(sign01.userCode)}
  customAccountNo:  ${ENV(sign01.accountNo)}
  roleCode: "INTERNAL_USERS"
  departmentCode:  ${ENV(sign01.main.orgCode)}
  customDepartmentNo:  ${ENV(sign01.main.orgNo)}
  data: {
    userCode: $userCode,
    customAccountNo: $customAccountNo,
    roleCode: $roleCode,
    departmentCode: $departmentCode,
    customDepartmentNo: $customDepartmentNo
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/rolePermission/revoke
  method: POST
  headers: ${gen_headers_signature($data)}
  json: $data
