name: 查询用户角色列表
variables:
  userCode:  ${ENV(sign01.userCode)}
  customAccountNo:  ${ENV(sign01.accountNo)}
  roleName: ""
  departmentCode:  ${ENV(sign01.main.orgCode)}
  customDepartmentNo:  ${ENV(sign01.main.orgNo)}
  pageSize: 10
  pageNo: 1
  data: {
    userCode: $userCode,
    customAccountNo: $customAccountNo,
    roleName: $roleName,
    departmentCode: $departmentCode,
    customDepartmentNo: $customDepartmentNo,
    pageSize: $pageSize,
    pageNo: $pageNo
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/rolePermission/userRoles
  method: POST
  headers: ${gen_headers_signature($data)}
  json: $data
