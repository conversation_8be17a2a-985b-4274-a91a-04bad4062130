
name: 修改用户角色

request:
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/role/updateRole
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            id: $id
            permissionType: $permissionType
            roleClass: $roleClass
            roleCode: $roleCode
            roleDesc: $roleDesc
            roleName: $roleName
