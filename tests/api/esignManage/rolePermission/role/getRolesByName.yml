name: 根据角色名称模糊查询角色列表并分页显示
variables:
  token0: ${getManageToken()}
  currPage: 1
  pageSize: 10
  permissionDataType: "1"
  roleClass: ""
  roleName: ""

request:
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/role/getRoleListByRoleName
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: $token0
    json:
        domain: ${ENV(manage.domain)}
        params:
            currPage: $currPage
            pageSize: $pageSize
            permissionDataType: $permissionDataType
            roleClass: $roleClass
            roleName: $roleName
