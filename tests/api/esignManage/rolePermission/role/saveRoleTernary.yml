name: "三元角色保存"
variables:
  userList_saveRoleTernary: [ ]
  id_saveRoleTernary: "1523545065551695872"
  roleCode_saveRoleTernary: "AUDIT_ADMIN"
  roleName_saveRoleTernary: "安全审计员"
  data:
    domain: "admin_platform"
    params:
      userList: $userList_saveRoleTernary
      ecRoleVO:
        id: $id_saveRoleTernary
        roleCode: $roleCode_saveRoleTernary
        roleName: $roleName_saveRoleTernary

request:
  url: ${ENV(esign.projectHost)}/manage/rolepermissions/role/saveRoleTernary
  method: POST
  headers:
    Content-Type: "application/json;charset=UTF-8"
    token: ${getManageToken()}
  json: $data
