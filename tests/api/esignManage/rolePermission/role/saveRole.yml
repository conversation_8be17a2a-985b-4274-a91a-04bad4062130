
name: 添加用户角色

request:
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/role/saveRole
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            permissionDataType: $permissionDataType
            permissionType: $permissionType
            roleCode: $roleCode
            roleDesc: $roleDesc
            roleName: $roleName
