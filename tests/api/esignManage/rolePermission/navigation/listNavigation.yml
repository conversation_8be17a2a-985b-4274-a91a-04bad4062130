
name: 根据导航类型获取导航列表，1系统导航 3业务导航
variables:
    navigationType: "3"
    token0:  ${getManageToken()}
request:
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/navigation/listNavigations
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: $token0
    json:
        domain: ${ENV(manage.domain)}
        params:
            navigationType: $navigationType

