name: 删除菜单（内部调用接口）
variables:
  token0: ${getManageToken()}
  navIds_delete: []
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    url: ${ENV(esign.projectHost)}/manage/rolepermissions/navigation/inner/deleteNavigation
    method: POST
    json: {
          "params": {
              "navIds": $navIds_delete,
              "apiEncrypt": "j7aBCNL1q6ftIM9/CHRXRJk2iXt2UmCLE+EA0eZQF4w="
          },
          "domain": "admin_platform"
      }