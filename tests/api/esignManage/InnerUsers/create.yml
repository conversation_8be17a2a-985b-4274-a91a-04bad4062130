
name: 新建内部用户
variables:
  data: [{
          customAccountNo: $customAccountNo,
          name: $name,
          mobile: $mobile,
          email: $email,
          licenseType: ID_CARD,
          licenseNo: $licenseNo,
          bankCardNo: $bankCardNo,
          mainOrganizationCode: $mainOrganizationCode,
          mainCustomOrgNo: $mainCustomOrgNo,
          otherOrganization: $otherOrganization
        }]
request:
    url: ${ENV(esign.gatewayHost)}/manage/v1/innerUsers/create
    method: POST
    headers:
      Content-Type: 'application/json'
      x-timevale-project-id: ${ENV(esign.projectId)}
      x-timevale-signature: ${getSignature($data)}
    json: $data
