name: 查询内部用户
variables:
  userCodeDetail:
  customAccountNoMIDetail:
  nameManageDetail:
  mobile:
  licenseNo:
  detailData:
    userCode: $userCodeDetail
    customAccountNo: $customAccountNoMIDetail
    name: $nameManageDetail
    mobile: $mobile
    licenseNo: $licenseNo

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerUsers/detail
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($detailData)}
  json: $detailData