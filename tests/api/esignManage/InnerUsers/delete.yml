name: 删除内部用户
variables:
  userCode_innerUsersDelete: ""
  customAccountNo_innerUsersDelete: ""
  data: {
    userCode: $userCode_innerUsersDelete,
    customAccountNo: $customAccountNo_innerUsersDelete
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerUsers/delete
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($data)}
  json: $data