name: 快速发起API-发起校验是否配置了流程模板
variables:
    flowTypeCode: 流程定义key不能为空(模板批量发起：[GZLC_PLQS],物理用印：[GZLC-YZ-YYSQ])
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/esign-docs/portal/quickStart/valid
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            flowTypeCode: $flowTypeCode
        domain: "evidence_system"