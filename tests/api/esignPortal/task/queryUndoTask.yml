name: 待签署任务查询
variables:
  authorization0: ${getPortalToken()}
  businessId: ""
  currPage: 1
  endTime: ""
  pageSize: 10
  startTime: ""
  startUserName: ""
  timeType: 1
  workflowCategory: ""
  workflowConfigName: ""
  flowExtensions: {}
  json:
    {
      domain: unified_portal_service,
      params:
        {
          businessId: $businessId,
          currPage: $currPage,
          endTime: $endTime,
          pageSize: $pageSize,
          startTime: $startTime,
          startUserName: $startUserName,
          timeType: $timeType,
          workflowCategory: $workflowCategory,
          workflowConfigName: $workflowConfigName,
          flowExtensions: $flowExtensions
        }
    }


request:
  url: ${ENV(esign.projectHost)}/portal/task/queryUndoTask
  method: POST
  headers:
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json: $json



