name: 我的待办-我发起的
variables:
    currPage: 当前页数
    workflowStatus: 流程状态
    workflowConfigName: 流程名称
    workflowConfigCode: 流程编码
    pageSize: 每页记录数
    timeType: 接收时间类型,1-接收时间,2-处理时间,3-发起时间,4-结束时间
    startTime: 开始时间
    endTime: 结束时间
    workflowCategory: 流程分类
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/task/queryInitiatedTask
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            currPage: $currPage
            workflowStatus: $workflowStatus
            workflowConfigName: $workflowConfigName
            workflowConfigCode: $workflowConfigCode
            pageSize: $pageSize
            timeType: $timeType
            startTime: $startTime
            endTime: $endTime
            workflowCategory: $workflowCategory
        domain: "unified_portal_service"