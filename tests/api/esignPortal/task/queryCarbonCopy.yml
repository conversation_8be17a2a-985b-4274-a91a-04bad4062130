name: 我的待办-查询抄送我的
variables:
    currPage: 当前页数
    workflowStatus: 流程状态
    pageSize: 每页记录数
    timeType: 接收时间类型,1-接收时间,2-处理时间,3-发起时间,4-结束时间
    startUserName: 发起人
    workflowConfigName: 流程名称
    workflowConfigCode: 流程编码
    startTime: 开始时间
    endTime: 结束时间
    workflowCategory: 流程分类
    status: 空为全部查询；
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/task/queryCarbonCopy
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            currPage: $currPage
            workflowStatus: $workflowStatus
            pageSize: $pageSize
            timeType: $timeType
            startUserName: $startUserName
            workflowConfigName: $workflowConfigName
            workflowConfigCode: $workflowConfigCode
            startTime: $startTime
            endTime: $endTime
            workflowCategory: $workflowCategory
            status: $status
        domain: "unified_portal_service"