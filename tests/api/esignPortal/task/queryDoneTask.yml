name: 我的待办-查询我的已办
variables:
    currPage: 当前页数
    startUserName: 发起人
    workflowConfigName: 流程名称
    businessId: 流程编码
    pageSize: 每页记录数
    timeType: 接收时间类型,1-接收时间,2-处理时间,3-发起时间,4-结束时间
    startTime: 开始时间
    endTime: 结束时间
    workflowCategory: 流程分类
    accountNumber: ${ENV(sign01.userCode)}
    password: ${ENV(password01)}
    flowExtensions: {}
request:
    url: ${ENV(esign.projectHost)}/portal/task/queryDoneTask
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            currPage: $currPage
            startUserName: $startUserName
            workflowConfigName: $workflowConfigName
            businessId: $workflowConfigCode
            pageSize: $pageSize
            timeType: $timeType
            startTime: $startTime
            endTime: $endTime
            workflowCategory: $workflowCategory
            flowExtensions: $flowExtensions
        domain: "unified_portal_service"