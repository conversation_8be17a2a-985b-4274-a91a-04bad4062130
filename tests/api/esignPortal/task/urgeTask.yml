name: 我的待办-催办
variables:
    businessId: 业务id
    id: $elem.value
    workflowCategory: 流程分类
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/task/urgeTask
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            businessId: $businessId
            id: $id
            workflowCategory: $workflowCategory
        domain: "unified_portal_service"