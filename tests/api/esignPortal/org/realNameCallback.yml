name: 机构信息-机构实名成功后回调
variables:
    redirectUrl: 前端实名成功之后跳转地址
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
    #todo 暂时写死，为了把取config配置替换掉
    requestJson: "{\"flowId\":\"*************\",\"success\":true,\"code\":\"djasfd-asupodafs\"}"

request:
    url: ${ENV(esign.projectHost)}/portal/org/realNameCallback
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    params:
        applyId: $applyId
        tenantCode: $tenantCode
        requestId: $requestId
        orgCode: $orgCode
        userCode: $userCode
    json:
        requestJson: $requestJson