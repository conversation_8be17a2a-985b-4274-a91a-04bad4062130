name: 机构信息-获取实名链接
variables:
    redirectUrl: 前端实名成功之后跳转地址
    orgCode: 去实名的机构
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/org/realName
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            redirectUrl: $redirectUrl
            orgCode: $orgCode
        domain: "unified_portal_service"