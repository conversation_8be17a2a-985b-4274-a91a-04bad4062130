name: 我的印章-是否有权限制作印章
variables:
    organizationCode: 组织编码
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/seal/isCanMakeSeal
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            organizationCode: $organizationCode
        domain: "unified_portal_service"