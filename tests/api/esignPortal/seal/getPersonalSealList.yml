name: 我的印章-查询个人印章
variables:
    currPage: 当前页数
    pageSize: 每页记录数
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/seals/portal/seal/getPersonalSealList
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            currPage: $currPage
            pageSize: $pageSize
        domain: "unified_portal_service"