name: 保存用户
variables:
  authorization_saveUser: ${getPortalToken()}
  data:
    {
      "customerIP": "",
      "deptId": "",
      "domain": "admin_platform",
      "params": $subParamsSaveUser,
      "platform": "",
      "tenantCode": "1000",
      "userCode": ""
    }
request:
  headers:
    authorization: $authorization_saveUser
    X-timevale-project-id: ${ENV(esign.projectId)}
    navid: "1764924302865543170"
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/portal/orguser/user/saveUser
