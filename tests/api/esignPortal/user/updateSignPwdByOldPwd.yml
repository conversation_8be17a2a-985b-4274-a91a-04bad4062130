name: 用户信息-修改签署密码
variables:
  acount: ${ENV(ceswdzxzdhyhwgd1.account)}
  newPwd: ${ENV(ceswdzxzdhyhwgd1.password)}
  oldPwd: ${ENV(ceswdzxzdhyhwgd1.password)}
  authorization0: ${getPortalToken($acount,$oldPwd)}

request:
  url: ${ENV(esign.projectHost)}/portal/user/updateSignPwdByOldPwd
  method: POST
  headers:
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    domain: unified_portal_service
    params:
      newPwd: $newPwd
      oldPwd: $oldPwd