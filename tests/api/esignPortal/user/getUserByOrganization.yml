name: 通过组织id获取用户列表分页
variables:
    authorization0: ${getPortalToken()}
    userName_get0: ""
    accountNumber_get0: ""
    params_get0:
      {
          "organizationId": "0",
          "currPage": 1,
          "pageSize": 10,
          "userName": $userName_get0,
          "accountNumber": $accountNumber_get0,
          "userMobile": "",
          "userEmail": "",
          "searchType": "0",
          "allChildOrganizationFlag": true,
          "userStatusList": [
              "1"
          ]
      }
    data_getUserByOrganization:
      {
          "params": $params_get0,
          "domain": "admin_platform"
      }
request:
    url: ${ENV(esign.projectHost)}/portal/orguser/user/getUserByOrganization
    method: POST
    headers:
        content-type: 'application/json'
        authorization: $authorization0
        x-timevale-project-id: ${ENV(esign.projectId)}
        navid: "1764924302865543170"
    json: $data_getUserByOrganization