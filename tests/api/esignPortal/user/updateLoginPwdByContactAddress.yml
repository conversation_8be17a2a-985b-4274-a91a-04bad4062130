name: 用户信息-根据手机/邮箱更新登录密码
variables:
    address: 手机号/邮箱地址
    addressType: 枚举 4是手机 3为邮箱
    newPwd: 新密码
    verificationCode: 验证码
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/user/updateLoginPwdByContactAddress
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            address: $address
            addressType: $addressType
            newPwd: $newPwd
            verificationCode: $verificationCode
        domain: "evidence_system"