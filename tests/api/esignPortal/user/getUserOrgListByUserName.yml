name: 获取某一租户下所有用户和组织简略信息通过userName
variables:
    authorization0: ${getPortalToken()}
    organizationCode_get2: ""
    userName_get2: ""
    params_get2:
      {
          "organizationCode": $organizationCode_get2,
          "userMainType": 1,
          "userName": $userName_get2
      }
    data_getUserOrgListByUserName:
      {
          "params": $params_get2,
          "domain": "admin_platform"
      }
request:
    url: ${ENV(esign.projectHost)}/portal/orguser/user/getUserOrgListByUserName
    method: POST
    headers:
        content-type: 'application/json'
        authorization: $authorization0
        x-timevale-project-id: ${ENV(esign.projectId)}
        navid: "1764924302865543170"
    json: $data_getUserOrgListByUserName