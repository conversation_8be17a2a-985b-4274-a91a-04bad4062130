name: 业务平台-直接离职
variables:
    authorization0: ${getPortalToken()}
    userDimissionName: ""
    userDimissionAccountNo: ""
    leaveTime: ""
    userDimissionStartDate: ""
    userDimissionEndDate: ""
    params_get5:
      {
        "userDimissionName": $userDimissionName,
        "currPage": 1,
        "pageSize": 10,
        "userDimissionAccountNo": $userDimissionAccountNo,
        "leaveTime": $leaveTime,
        "userDimissionStartDate": "$userDimissionStartDate",
        "userDimissionEndDate": "$userDimissionEndDate"
      }
    data_dimissionRecord:
      {
          "params": $params_get5,
          "domain": "admin_platform"
      }
request:
    url: ${ENV(esign.projectHost)}/portal/orguser/user/dimissionRecord
    method: POST
    headers:
        content-type: 'application/json'
        authorization: $authorization0
        x-timevale-project-id: ${ENV(esign.projectId)}
        navid: "1764924302865543170"
    json: $data_dimissionRecord