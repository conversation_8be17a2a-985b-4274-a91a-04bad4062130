name: 按organizationName查询机构列表
variables:
    authorization0: ${getPortalToken()}
    organizationName_get1: ""
    organizationTerritory_get1: 1
    params_get1:
      {
          "organizationName": $organizationName_get1,
          "organizationTerritory": $organizationTerritory_get1
      }
    data_getOrganizationListByOrgCodeName:
      {
          "params": $params_get1,
          "domain": "admin_platform"
      }
request:
    url: ${ENV(esign.projectHost)}/portal/orguser/org/getOrganizationListByOrgCodeName
    method: POST
    headers:
        content-type: 'application/json'
        authorization: $authorization0
        x-timevale-project-id: ${ENV(esign.projectId)}
        navid: "1764924302865543170"
    json: $data_getOrganizationListByOrgCodeName