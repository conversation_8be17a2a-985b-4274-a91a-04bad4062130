name: 业务平台-直接离职
variables:
    authorization0: ${getPortalToken()}
    userDimissionId: ""
    handoverStatus: "2"
    needToResign: "2"
    params_get4:
      {
        "userDimissionId": $userDimissionId,
        "handoverStatus": $handoverStatus,
        "needToResign": $needToResign
      }
    data_dimission:
      {
          "params": $params_get4,
          "domain": "admin_platform"
      }
request:
    url: ${ENV(esign.projectHost)}/portal/orguser/dimission/addUserDimission
    method: POST
    headers:
        content-type: 'application/json'
        authorization: $authorization0
        x-timevale-project-id: ${ENV(esign.projectId)}
        navid: "1764924302865543170"
    json: $data_dimission