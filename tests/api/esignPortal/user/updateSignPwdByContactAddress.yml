name: 用户信息-根据手机或者邮箱更新签署密码
variables:
    address: 手机号/邮箱
    addressType: 地址类型 比如4表示手机、3表示邮箱
    newPwd: 新密码
    verificationCode: 验证码
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/user/updateSignPwdByContactAddress
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            address: $address
            addressType: $addressType
            newPwd: $newPwd
            verificationCode: $verificationCode
        domain: "evidence_system"