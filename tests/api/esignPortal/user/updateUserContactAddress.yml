name: 用户信息-新增/更新用户手机或者邮箱地址
variables:
    address: 手机号/邮箱地址
    addressType: 枚举 4是手机 3为邮箱
    verificationCode: 验证码
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/user/updateUserContactAddress
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            address: $address
            addressType: $addressType
            verificationCode: $verificationCode
        domain: "unified_portal_service"