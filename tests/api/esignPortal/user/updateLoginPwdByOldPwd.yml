name: 用户信息-根据旧密码更新登录密码
variables:
    newPwd: 新密码
    oldPwd: 旧密码
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
request:
    url: ${ENV(esign.projectHost)}/portal/user/updateLoginPwdByOldPwd
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            newPwd: $newPwd
            oldPwd: $oldPwd
        domain: "evidence_system"