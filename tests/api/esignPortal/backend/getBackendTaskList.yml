name: 后台任务-获取后台任务分页列表
variables:
    currPage: 当前页数
    pageSize: 每页记录数
    accountNumber: ${ENV(ceswdzxzdhyhwgd1.account)}
    password: ${ENV(password01)}
    authorization0: ${getPortalToken($accountNumber, $password)}
request:
    url: ${ENV(esign.projectHost)}/esign-docs/portal/backend/getBackendTaskList
    method: POST
    headers:
        Content-Type: "application/json"
        authorization: $authorization0
        X-timevale-project-id": ${ENV(esign.projectId)}

    json:
        params:
            currPage: $currPage
            pageSize: $pageSize
        domain: "evidence_system"
        userCode: ${ENV(ceswdzxzdhyhwgd1.account)}