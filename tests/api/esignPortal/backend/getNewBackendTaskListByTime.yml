name: 后台任务-获取新的后台任务列表
variables:
    timestamp: 时间戳
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
    domain: "evidence_system"
request:
    url: ${ENV(esign.projectHost)}/portal/backend/getNewBackendTaskListByTime
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            timestamp: $timestamp
        domain: $domain
        userCode: $accountNumber