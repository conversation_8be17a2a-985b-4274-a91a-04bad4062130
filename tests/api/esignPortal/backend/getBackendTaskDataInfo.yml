name: 后台任务相关-获取任务详情信息
variables:
    batchTemplateInitiationIds: #批量发起任务ID
    accountNumber: ${ENV(userCodeNoSeal)}
    password: ${ENV(password01)}
    userCode: ${ENV(userCodeNoSeal)}

request:
    url: ${ENV(esign.projectHost)}/esign-docs/portal/backend/getBackendTaskDataInfo
    method: POST
    headers: ${gen_token_header_permissions($accountNumber,$password)}

    json:
        params:
            batchTemplateInitiationIds: $batchTemplateInitiationIds
        domain: "evidence_system"
        userCode: $userCode