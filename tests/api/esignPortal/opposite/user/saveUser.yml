name: 保存相对方用户
variables:
    userName:
    accountNumber:
    userEmailEn: ""
    userMobileEn: ""
    userType: "2"
    userTerritory: "2"
    userStatus: "3"
    dimissionTime: ""
    organizationId:
    ecUserParttimeIDList: [ ]
    licenseType: "19"
    licenseNumber: ""
    bankCardNo: ""
    useLanguage: "zh-CN"
    organizationCode:
request:
  url: ${ENV(esign.projectHost)}/portal/opposite/user/saveUser
  method: POST
  headers: ${get_headers_with_autho()}
  json:
    {
      domain: "admin_platform",
      params:
        {
          userName: $userName,
          accountNumber: $accountNumber,
          userType: $userType,
          userTerritory: $userTerritory,
          userStatus: $userStatus,
          userEmail: $userEmailEn,
          userMobile: $userMobileEn,
          dimissionTime: $dimissionTime,
          organizationId: $organizationId,
          ecUserParttimeIDList: $ecUserParttimeIDList,
          licenseType: $licenseType,
          licenseNumber: $licenseNumber,
          bankCardNo: $bankCardNo,
          useLanguage: $useLanguage,
          organizationCode: $organizationCode
        }
    }