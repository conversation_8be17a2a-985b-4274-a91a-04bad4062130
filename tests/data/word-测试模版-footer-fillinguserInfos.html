<div class="froala-esign export-box"><div class="fr-box fr-basic" id="editor" style="width: 794px;"><div class="fr-wrapper"><div class="fr-view fr-element"><div data-v-0109b820="" id="editor-padding-bottom" style="width: 794px; background-color: rgb(255, 255, 255); margin: 0px auto; min-height: 58.39pt;"><div data-v-0109b820="" class="padding-bottom-container" style="position: relative; overflow-y: visible; max-height: 503px; margin: 0px 57.8267px 0px 54.0533px; padding: 0px 0px 48pt; min-height: 9.75pt;"><div data-v-0109b820="" id="editor-page-footer" class="fr-box fr-ltr fr-basic" role="application" style="width: auto; font-size: 9pt; display: block;"><div class="fr-wrapper" dir="ltr"><div class="fr-element fr-view" dir="ltr" contenteditable="true" aria-disabled="false" spellcheck="true" style="min-height: 9.75pt; border-color: transparent; padding-bottom: 0px;"><p style="margin-top:0pt; margin-bottom:0pt; line-height:10pt;"><span style="height:0pt; display:block; position:absolute; z-index:-65537;"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAeCAYAAAAo5+5WAAAACXBIWXMAABtfAAAbXwFfNGm4AAAAQklEQVR4nGNgGAWjYBSMICALxGlAvByIv1HT4ONAfBKI/0MxVQEHrQxmGDV41OBRgwfAYFskg62pZeh/HHgUDAIAAKCnJBENhsbWAAAAAElFTkSuQmCC" width="12" height="16" alt="" style="margin-top:1.87pt; margin-left:252.45pt; -aw-left-pos:293.45pt; -aw-rel-hpos:page; -aw-rel-vpos:page; -aw-top-pos:781.25pt; -aw-wrap-type:none; position:absolute;"></span><span style="font-size:10pt; -aw-import:ignore;"> </span></p></div><span class="fr-placeholder" style="font-size: 14px; line-height: 22.4px; margin-top: 0px; padding-top: 0px; padding-left: 0px; margin-left: 0px; padding-right: 0px; margin-right: 0px; text-align: left;"></span></div><div class="fr-second-toolbar"></div></div><span data-v-0109b820="" class="icon bottom" style="display: none;">页脚</span></div></div><style data-epaas-magic-engine-id="headerFooterStyleClear">#header, #footer {
          padding: 0 !important;
          height: 100% !important;
          position: fixed;
        }
        #editor-padding-top, #editor-padding-bottom {
          transform: scale(1.0) !important;
          transform-origin: top left;
        }
        img{
          display: inline-block;
          float: none;
          vertical-align: bottom;
          margin-left: 5px;
          margin-right: 5px;
          max-width: calc(100% - (2 * 5px));
        }
        .fr-line-breaker, .fr-second-toolbar, .fr-image-resizer {display: none;}
        .fr-wrapper .fr-view p { margin: 0; word-break: break-all; }
        </style></div></div></div></div>