<!DOCTYPE html>
<!-- saved from url=(0050)https://www.cnblogs.com/yoyoketang/p/********.html -->
<html lang="zh-cn"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="origin-when-cross-origin">
    <meta name="description" content="前言 校验接口返回结果，我们习惯校验实际结果和期望结果相等，如果只是部分相等可以用contains包含校验 校验包含 先看下httprunner/builtin/comparators.py 关于 c">
    <meta property="og:description" content="前言 校验接口返回结果，我们习惯校验实际结果和期望结果相等，如果只是部分相等可以用contains包含校验 校验包含 先看下httprunner/builtin/comparators.py 关于 c">
    <meta http-equiv="Cache-Control" content="no-transform">
    <meta http-equiv="Cache-Control" content="no-siteapp">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>httprunner 3.x学习17 - 断言字符串包含 - 上海-悠悠 - 博客园</title>
    <link id="favicon" rel="shortcut icon" href="https://common.cnblogs.com/favicon.svg" type="image/svg+xml">
    
    <style>:not(.cnblogs_code):not(.cnblogs_Highlighter)>pre:not([class*="language-"]):not([highlighted]):not(.hljs):not([class*="brush:"]) {
        background: rgb(245, 245, 245);
        padding: 5px;
        border: 1px solid rgb(204, 204, 204);
        border-radius: 3px;
        border-color: transparent;
        color: rgb(68, 68, 68);
        font-family: "Courier New", sans-serif;
    }:not(.cnblogs_code):not(.cnblogs_Highlighter)>pre:not([highlighted]):not([class*="brush:"]) code:not(.hljs) {
            background: rgb(245, 245, 245) !important;
            border-color: transparent !important;
        }</style><link rel="stylesheet" href="./test_files/blog-common.min.css">
    <link id="MainCss" rel="stylesheet" href="./test_files/bundle-coffee.min.css">
    <link id="highlighter-theme-cnblogs" type="text/css" rel="stylesheet" href="./test_files/cnblogs.css">
    <link type="text/css" rel="stylesheet" href="./test_files/custom.css">
    <link id="mobile-style" media="only screen and (max-width: 767px)" type="text/css" rel="stylesheet" href="./test_files/bundle-coffee-mobile.min.css">
    
    
    
    <link type="application/rss+xml" rel="alternate" href="https://www.cnblogs.com/yoyoketang/rss">
    <link type="application/rsd+xml" rel="EditURI" href="https://www.cnblogs.com/yoyoketang/rsd.xml">
    <link type="application/wlwmanifest+xml" rel="wlwmanifest" href="https://www.cnblogs.com/yoyoketang/wlwmanifest.xml">
    <script type="text/javascript" async="" src="./test_files/analytics.js"></script><script>
        var currentBlogId = 319138;
        var currentBlogApp = 'yoyoketang';
        var cb_enable_mathjax = false;
        var isLogined = false;
        var isBlogOwner = false;
        var skinName = 'coffee';
        var visitorUserId = '';
        var hasCustomScript = false;
        try {
            if (hasCustomScript && document.referrer && document.referrer.indexOf('baidu.com') >= 0) {
                Object.defineProperty(document, 'referrer', { value: '' });
                Object.defineProperty(Document.prototype, 'referrer', { get: function(){ return ''; } });
            }
        } catch(error) { }
        window.codeHighlightEngine = 1;
        window.enableCodeLineNumber = false;
        window.codeHighlightTheme = 'cnblogs';
    </script>
        <script>
            var currentPostDateAdded = '2021-06-24 19:50';
        </script>
    <script src="./test_files/jquery-2.2.0.min.js"></script>
    <script src="./test_files/blog-common.min.js"></script><style>.medium-zoom-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  transition: opacity 300ms;
  will-change: opacity;
}

.medium-zoom--opened .medium-zoom-overlay {
  cursor: pointer;
  cursor: zoom-out;
  opacity: 1;
}

.medium-zoom-image {
  cursor: pointer;
  cursor: zoom-in;
  /*
    The `transition` is marked as "!important" for the animation to happen
    even though it's overriden by another inline `transition` style attribute.

    This is problematic with frameworks that generate inline styles on their
    images (e.g. Gatsby).

    See https://github.com/francoischalifour/medium-zoom/issues/110
   */
  transition: transform 300ms cubic-bezier(0.2, 0, 0.2, 1) !important;
}

.medium-zoom-image--hidden {
  visibility: hidden;
}

.medium-zoom-image--opened {
  position: relative;
  cursor: pointer;
  cursor: zoom-out;
  will-change: transform;
}
</style><script id="hljs-script" async="" data-loaded="true" src="./test_files/highlight.min.js"></script>
    
    
    
<meta http-equiv="origin-trial" content="AzoawhTRDevLR66Y6MROu167EDncFPBvcKOaQispTo9ouEt5LvcBjnRFqiAByRT+2cDHG1Yj4dXwpLeIhc98/gIAAACFeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjYxMjk5MTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="><meta http-equiv="origin-trial" content="A6+nc62kbJgC46ypOwRsNW6RkDn2x7tgRh0wp7jb3DtFF7oEhu1hhm4rdZHZ6zXvnKZLlYcBlQUImC4d3kKihAcAAACLeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjYxMjk5MTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="><meta http-equiv="origin-trial" content="A/9La288e7MDEU2ifusFnMg1C2Ij6uoa/Z/ylwJIXSsWfK37oESIPbxbt4IU86OGqDEPnNVruUiMjfKo65H/CQwAAACLeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXRhZ3NlcnZpY2VzLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjYxMjk5MTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="><script src="./test_files/pubads_impl_2022052301.js" async=""></script><style>.jj-flash-note__popper[data-v-44225974]{position:absolute;border:none;outline:0;text-align:center;width:28px;height:28px;background:#f7f8fa;border:1px solid #e5e6eb;border-radius:2px;padding:4px}.jj-flash-note__popper .icon[data-v-44225974]{pointer-events:none}.jj-flash-note__frame[data-v-dd761a64]{max-width:50%;position:fixed;right:0;bottom:0;top:0;background-color:#fff;z-index:9999}.slide-left-enter-active[data-v-dd761a64],.slide-left-leave-active[data-v-dd761a64]{transition:transform .15s linear}.slide-left-enter-from[data-v-dd761a64],.slide-left-leave-to[data-v-dd761a64]{transform:translateX(100%)}.jj-flash-note__app[data-v-ed443a7e]{z-index:9999;position:fixed;left:0;top:0}.jj-flash-note__app .mask[data-v-ed443a7e]{position:fixed;left:0;right:0;top:0;bottom:0;z-index:9000;background-color:rgba(0,0,0,.4);opacity:1}.jj-flash-note__app .fade-enter-active[data-v-ed443a7e],.jj-flash-note__app .fade-leave-active[data-v-ed443a7e]{transition:opacity .15s ease}.jj-flash-note__app .fade-enter-from[data-v-ed443a7e],.jj-flash-note__app .fade-leave-to[data-v-ed443a7e]{opacity:0}[data-v-41285de6]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-41285de6]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.juejin-search[data-v-41285de6]{display:flex;width:682px;height:46px;border-radius:2px;flex-direction:row;align-items:center;justify-content:center;position:relative}.juejin-search .search-anim[data-v-41285de6]{position:absolute;left:8px;width:28px;height:28px;object-fit:contain;animation-play-state:paused}.juejin-search .search-anim.slide-right-enter-active[data-v-41285de6],.juejin-search .search-anim.slide-right-leave-active[data-v-41285de6]{transition:width .3s linear}.juejin-search .search-anim.slide-right-enter-from[data-v-41285de6],.juejin-search .search-anim.slide-right-leave-to[data-v-41285de6]{width:0}.juejin-search .juejin-search-logo[data-v-41285de6]{right:16px;position:absolute;width:23px;height:18px;object-fit:contain}.juejin-search .juejin-search-logo path[data-v-41285de6]{transition:all .3s linear}.juejin-search #juejin-search-input-global[data-v-41285de6]{height:100%;width:100%}.juejin-search #juejin-search-input-global .input[data-v-41285de6]{padding:0 39px 0 33px;width:100%;height:100%;outline:0;border:none;border-radius:2px;color:var(--jjext-color-font-1);font-size:18px;line-height:22px;font-weight:500;caret-color:transparent;box-sizing:border-box;background-color:var(--jjext-color-layer-4-plugin)}.juejin-search #juejin-search-input-global .input.active[data-v-41285de6]{border:2px solid var(--jjext-color-font-brand-4)}.juejin-search #juejin-search-input-global .input.animation-stopped[data-v-41285de6]{caret-color:#1e80ff;padding-left:16px}.juejin-search #juejin-search-input-global .input[data-v-41285de6]::placeholder{font-weight:400;color:#86909c}.calculator[data-v-4faf9c0e]{display:flex;align-items:center;height:36px;padding:0 16px;cursor:pointer}.calculator .result[data-v-4faf9c0e]{font-size:14px;text-align:start;font-weight:500;line-height:22px;color:#1d2129;margin:0 12px;text-overflow:ellipsis;flex:1 0 auto;overflow:hidden;white-space:nowrap;max-width:494px}.calculator .hint[data-v-4faf9c0e]{font-size:14px;line-height:22px;color:#8a919f}[data-v-d8e306de]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-d8e306de]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.search-action[data-v-d8e306de]{display:flex;align-items:center;box-sizing:border-box;user-select:none;cursor:pointer;height:36px;border-left:4px solid transparent;border-top:4px solid transparent;border-bottom:4px solid transparent;transition:all .15s linear;padding:0 16px 0 12px}.search-action.active[data-v-d8e306de]{border-left-color:var(--jjext-color-font-brand-1);background-color:#f4f5f5}.search-action .search-content[data-v-d8e306de]{display:flex;align-items:center;flex:1 0 auto;margin-right:16px}.search-action .search-content .search-content__logo[data-v-d8e306de]{width:28px;height:28px}.search-action .search-content .search-content__engine[data-v-d8e306de],.search-action .search-content .search-content__keyword[data-v-d8e306de]{font-size:14px;font-weight:500;line-height:22px}.search-action .search-content .search-content__keyword[data-v-d8e306de]{color:var(--jjext-color-font-1);margin:0 4px 0 12px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;max-width:396px}.search-action .search-content .search-content__engine[data-v-d8e306de]{color:var(--jjext-color-font-brand-1)}.search-action .hint[data-v-d8e306de]{font-size:14px;line-height:22px;color:var(--jjext-color-font-brand-1)}em[data-v-b1604592]{font-style:normal;color:#f53f3f}[data-v-434372b6]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-434372b6]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.search-suggest[data-v-434372b6]{background:var(--jjext-color-layer-4);padding:0 4px 8px 4px}.search-suggest .calculator.active[data-v-434372b6],.search-suggest .search-action.active[data-v-434372b6]{background:var(--jjext-color-layer-2-1)}.search-suggest .calculator[data-v-434372b6]{transition:background-color .15s linear}.search-suggest .list[data-v-434372b6]{display:flex;border-top:1px solid var(--jjext-color-layer-gray-1-2);flex-direction:column;padding-top:4px}.search-suggest .list .item[data-v-434372b6]{display:flex;flex-direction:row;align-items:center;height:36px;cursor:pointer}.search-suggest .list .item .content[data-v-434372b6]{color:var(--jjext-color-font-1);font-size:14px}.search-suggest .list .item.active[data-v-434372b6]{background:var(--jjext-color-layer-2-1)}.search-suggest .list .tool-item[data-v-434372b6]{position:relative;padding:0 9px 0 4px}.search-suggest .list .tool-item .tool-icon[data-v-434372b6]{width:24px;height:24px;background-size:100% 100%;background-position:0 0;background-repeat:no-repeat}.search-suggest .list .tool-item .content[data-v-434372b6]{margin-left:8px}.search-suggest .list .tool-item .icon-tool-arrow[data-v-434372b6]{opacity:0;transition:all .15s linear;position:absolute;stroke:var(--jjext-color-font-brand-1);top:50%;transform:translateY(-50%);right:9px}.search-suggest .list .tool-item.active .icon-tool-arrow[data-v-434372b6]{opacity:1}.search-suggest .list .suggest-item[data-v-434372b6]{padding:0 7px;transition:background-color .15s linear}.search-suggest .list .suggest-item .icon-search[data-v-434372b6]{stroke:var(--jjext-color-font-4)}.search-suggest .list .suggest-item .content[data-v-434372b6]{margin-left:12px}.search-suggest .list .suggest-item[data-v-434372b6] .highlight-keyword{color:var(--jjext-color-font-3)}.search-suggest .setting-hint[data-v-434372b6]{display:flex;align-items:center;justify-content:flex-end;margin:8px 16px 0 16px}.search-suggest .setting-hint .text[data-v-434372b6]{color:#8a919f;line-height:22px;cursor:pointer;user-select:none}.search-suggest .setting-hint .text[data-v-434372b6]:hover:not(.disabled){color:#1e80ff;transition:all .15s linear}.search-suggest .setting-hint .text.disabled[data-v-434372b6]{cursor:initial}:root{--jjext-color-input-bg:#f4f5f5;--jjext-color-input-error-bg:#ffece8;--jjext-color-input-placeholder:#86909c;--jjext-color-input-text:#4e5969;--jjext-color-input-icon:#f53f3f}:root .dark{--jjext-color-input-bg:rgba(255, 255, 255, 0.12);--jjext-color-input-error-bg:rgba(255, 81, 50, 0.15);--jjext-color-input-placeholder:#e3e3e3;--jjext-color-input-text:#e3e3e3;--jjext-color-input-icon:#ff6247}[data-v-341e7439]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-341e7439]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.input-option[data-v-341e7439]{display:flex;flex-direction:column}.input-option span.error[data-v-341e7439]{margin-left:6.6666666667rem;font-size:1rem;line-height:20px;display:inline-block;height:20px;color:var(--jjext-color-tips)}.input-wrapper[data-v-341e7439]{display:flex;flex-direction:row;align-items:center;width:100%}.input-wrapper label[data-v-341e7439]{width:4em;font-size:1.1666666667rem;line-height:1.8333333333rem;color:var(--jjext-color-thirdly);margin-right:1rem}.input-wrapper .input[data-v-341e7439]{flex:1 0 auto;position:relative}.input-wrapper .input.error .input-inner[data-v-341e7439]{background-color:var(--jjext-color-input-error-bg)}.input-wrapper .input.error .btn-clear[data-v-341e7439]{color:var(--jjext-color-input-icon)}.input-wrapper .input .input-inner[data-v-341e7439]{background:var(--jjext-color-input-bg);border-radius:2px;color:var(--jjext-color-input-text);font-size:1.0833333333rem;line-height:1.8333333333rem;height:2.3333333333rem;padding:0 8px;outline:0;border:none;width:100%}.input-wrapper .input .input-inner[data-v-341e7439]::placeholder{color:var(--jjext-color-input-placeholder)}.input-wrapper .btn-clear[data-v-341e7439]{position:absolute;top:50%;right:0;transform:translateY(-50%);background:0 0;border:none;outline:0;color:var(--jjext-color-fourthly)}.input-wrapper .btn-clear[data-v-341e7439]::before{font-size:10px;line-height:10px}[data-v-5a92de1e]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-5a92de1e]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}[data-v-5a92de1e]{box-sizing:border-box}.color-tool[data-v-5a92de1e]{padding:0 16px!important}.color-tool .row[data-v-5a92de1e]{display:flex;align-items:center}.color-tool .color-picker[data-v-5a92de1e]{cursor:pointer;outline:0;border:none;padding:0;margin:0;border-radius:2px;background-color:transparent;width:92px;height:40px}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper{padding:3px;border:1px solid transparent;border-radius:4px;transition:all .15s linear}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch-wrapper:hover{border:1px solid #bedaff}.color-tool .color-picker[data-v-5a92de1e]::-webkit-color-swatch{border-radius:2px;border:none}.color-tool .input[data-v-5a92de1e]{transform:translateY(10px);flex:1 1 auto;margin:0 12px}.color-tool .input[data-v-5a92de1e] input.input-inner{height:40px;padding-left:16px;font-size:14px;color:var(--jjext-color-primary);box-sizing:border-box;background:var(--jjext-color-main-bg)}.color-tool .input[data-v-5a92de1e] label{display:none}.color-tool .input[data-v-5a92de1e] span.error{margin-left:16px}.color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear{right:8px}.color-tool .input[data-v-5a92de1e] .input-wrapper .btn-clear::before{font-size:14px;color:#c9cdd4}.color-tool button[data-v-5a92de1e]{outline:0;border:none;background-color:unset;width:93px;height:40px;font-size:14px}.color-tool .btn-convert[data-v-5a92de1e]{background:var(--jjext-color-brand);border-radius:2px;color:#fff;transition:all .15s linear}.color-tool .btn-convert[data-v-5a92de1e]:hover{background:#5399ff}.color-tool .btn-convert[data-v-5a92de1e]:active{background:#0060dd}.color-tool .btn-copy[data-v-5a92de1e]{background:rgba(30,128,255,.05);border:1px solid rgba(30,128,255,.3);border-radius:2px;color:var(--jjext-color-brand);transition:all .15s linear}.color-tool .btn-copy[data-v-5a92de1e]:hover{background:rgba(30,128,255,.1);border-color:rgba(30,128,255,.45)}.color-tool .btn-copy[data-v-5a92de1e]:active{background:rgba(30,128,255,.2);border-color:rgba(30,128,255,.6)}.color-tool .display[data-v-5a92de1e]{flex:1;text-align:start;background-color:var(--jjext-color-main-bg);height:40px;margin:0 12px;border-radius:2px;line-height:40px;padding-left:16px;font-size:14px;color:var(--jjext-color-primary)}.color-tool .label[data-v-5a92de1e]{width:92px;font-size:16px;font-weight:500;color:var(--jjext-color-primary);text-align:end}.color-tool .row[data-v-5a92de1e]:not(:first-of-type){margin-top:16px}[data-v-71426c7e]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-71426c7e]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.quick-tool-drawer[data-v-71426c7e]{z-index:750;position:fixed;right:0;top:0;bottom:0;width:60%;background:var(--jjext-color-thirdly-bg)}.quick-tool-drawer.dark .header .title[data-v-71426c7e]{color:#e3e3e3}.quick-tool-drawer .quick-tool-drawer__header__[data-v-71426c7e]{position:relative;height:64px;padding:0 16px;display:flex;flex-direction:row;align-items:center}.quick-tool-drawer .quick-tool-drawer__header__ .icon[data-v-71426c7e]{width:40px;height:40px}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__title__[data-v-71426c7e]{margin:0 0 0 9px;padding:0;font-size:16px;font-weight:500;line-height:22px;color:var(--jjext-color-brand)}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-71426c7e]{cursor:pointer;position:absolute;right:16px;top:50%;font-size:18px;transform:translateY(-50%)}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__[data-v-71426c7e]::after{display:block;content:" ";position:absolute;padding:10px;width:100%;height:100%;top:-50%;left:-50%}.quick-tool-drawer .quick-tool-drawer__header__ .quick-tool-drawer__btn-close__ svg[data-v-71426c7e]{fill:var(--jjext-color-thirdly)}.quick-tool-drawer .quick-tool-drawer__tool__[data-v-71426c7e]{width:100%;height:100%;box-sizing:border-box}[data-v-19f1e2c8]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-19f1e2c8]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.mask[data-v-19f1e2c8]{position:fixed;left:0;right:0;top:0;bottom:0;z-index:600;background-color:var(--jjext-color-mask)}.slide-left-enter-active,.slide-left-leave-active{transition:transform .3s linear}.slide-left-enter-from,.slide-left-leave-to{transform:translateX(100%)}[data-v-6414b4fe]:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark[data-v-6414b4fe]{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}.search-app[data-v-6414b4fe]{z-index:9999;padding-top:160px;position:fixed;left:0;right:0;top:0;bottom:0;display:flex;align-items:flex-start;justify-content:center}.search-app.extension[data-v-6414b4fe]{z-index:500}@media (max-height:720px){.search-app.tool-active[data-v-6414b4fe]{padding-top:80px}}@media (max-height:640px){.search-app.tool-active[data-v-6414b4fe]{padding-top:30px}}.search-app .search-app__wrapper__[data-v-6414b4fe]{overflow:hidden;border-radius:4px;border:1px solid var(--jjext-color-font-brand-1);background:var(--jjext-color-layer-4);box-shadow:0 0 0 4px rgba(30,128,255,.2),0 0 20px rgba(0,0,0,.15);backdrop-filter:blur(15px)}.search-app .search-app__wrapper__ .search-result[data-v-6414b4fe]{margin-top:8px}.search-app .search-app__wrapper__ .search-result .tool[data-v-6414b4fe]{padding:0 8px}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest{padding:0 0 8px 0}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest .list{border-top:none;padding-left:8px;padding-right:8px}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest .list .suggest-item{padding:0 13px}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest .list .suggest-item .content{margin-left:17px}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest .list .tool-item{padding:0 9px 0 10px}.search-app .search-app__wrapper__[data-v-6414b4fe] .search-suggest .list .tool-item .content{margin-left:12px}.search-app .juejin-search[data-v-6414b4fe]{margin:8px}:root{--jjext-color-brand:#1E80FF;--jjext-color-brand-light:#E8F3FF;--jjext-color-nav-title:#86909C;--jjext-color-nav-popup-bg:#FFFFFF;--jjext-color-primary:#1D2129;--jjext-color-secondary-app:#4E5969;--jjext-color-thirdly:#86909C;--jjext-color-hover:#1e80ff;--jjext-color-hover-thirdly:#86909c;--jjext-color-dropdown-text:#1E80FF;--jjext-color-divider:#E5E6EB;--jjext-color-main-bg:#f4f5f5;--jjext-color-secondary-bg:#FFFFFF;--jjext-color-thirdly-bg:#f4f5f5;--jjext-color-hover-bg:#E8F3FF;--jjext-color-comment-bg:rgba(244, 245, 245, 0.5);--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(232, 243, 255, 0) 0%,
    rgba(232, 243, 255, 0.8) 25.09%,
    #e8f3ff 50.16%,
    rgba(232, 243, 255, 0.8) 75.47%,
    rgba(232, 243, 255, 0) 100%
  );--jjext-color-font-2:#1171EE;--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#ffffff;--jjext-color-nav-bg:rgba(255, 255, 255, 0.13);--jjext-color-nav-selected-border:rgba(229, 230, 235, 0.3);--jjext-color-tips:#F53F3F;--jjext-color-fourthly:#C9CDD4;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#e5e6eb;--jjext-color-icon-search:#ffffff;--jjext-color-navbar-icon:#1e80ff;--jjext-color-layout-dropdown-bg:rgba(232, 243, 255, 0.8);--jjext-color-layout-title:#4E5969;--jjext-color-layout-title-active:#1E80FF;--jjext-color-layout-icon-outline:rgba(30, 128, 255, 0.5);--jjext-color-layout-icon-fill:#ffffff;--jjext-color-layer-gray-1-2:rgba(228, 230, 235, 0.5);--jjext-color-layer-2-1:#F7F8FA;--jjext-color-layer-4:#ffffff;--jjext-color-font-brand-1:#1e80ff;--jjext-color-font-brand-4:#abcdff;--jjext-color-font-1:#252933;--jjext-color-font-3:#8A919F;--jjext-color-font-4:#C2C8D1;--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.05)}:root .dark{--jjext-color-brand:#1352a3;--jjext-color-nav-title:#e3e3e3;--jjext-color-nav-popup-bg:#1352A3;--jjext-color-primary:#e3e3e3;--jjext-color-secondary-app:#a9a9a9;--jjext-color-thirdly:#7d7d7f;--jjext-color-hover:#eeeeee;--jjext-color-hover-thirdly:#878789;--jjext-color-dropdown-text:#878789;--jjext-color-divider:#4a4a4a;--jjext-color-main-bg:#121212;--jjext-color-secondary-bg:#272727;--jjext-color-thirdly-bg:#3a3a3a;--jjext-color-hover-bg:#3a3a3a;--jjext-color-comment-bg:#313131;--jjext-hover-bg:linear-gradient(
    90deg,
    rgba(58, 58, 58, 0) 0%,
    rgba(58, 58, 58, 0.8) 25.09%,
    #3a3a3a 50.16%,
    rgba(58, 58, 58, 0.8) 75.47%,
    rgba(58, 58, 58, 0) 100%
  );--jjext-color-font-2:rgba(238, 238, 238, 0.8);--jjext-color-mask:rgba(0, 0, 0, 0.4);--jjext-color-quick-nav-text:#e3e3e3;--jjext-color-nav-bg:rgb(30, 30, 30);--jjext-color-nav-selected-border:#4a4a4a;--jjext-color-tips:#bc3030;--jjext-color-fourthly:#878789;--jjext-color-shadow:rgba(0, 0, 0, 0.16);--jjext-color-grey-triangle:#3a3a3a;--jjext-color-icon-search:#e3e3e3;--jjext-color-navbar-icon:#e3e3e3;--jjext-color-layout-dropdown-bg:rgba(125, 125, 127, 0.8);--jjext-color-layout-title:#eeeeee;--jjext-color-layout-title-active:#eeeeee;--jjext-color-layout-icon-outline:#131313;--jjext-color-layout-icon-fill:#e3e3e3;--jjext-color-layer-gray-1-2:rgba(255, 255, 255, 0.1);--jjext-color-layer-2-1:rgba(255, 255, 255, 0.08);--jjext-color-layer-4:#2f2f2f;--jjext-color-font-brand-1:#4495FF;--jjext-color-font-brand-4:rgba(19, 113, 236, 0.2);--jjext-color-font-1:rgba(255, 255, 255, 0.9);--jjext-color-font-3:rgba(255, 255, 255, 0.55);--jjext-color-font-4:rgba(255, 255, 255, 0.45);--jjext-color-layer-4-plugin:rgba(30, 128, 255, 0.1)}</style><link rel="preload" href="./test_files/f.txt" as="script"><script type="text/javascript" src="./test_files/f.txt"></script></head>
<body class="skin-coffee has-navbar has-bannerbar hljs-engine">
    <a name="top"></a>
        <a href="https://developer.aliyun.com/trainingcamp/88b6444d8728476090ac3aa1338179f2?utm_content=g_1000340106" onclick="countCreativeClicks(&#39;C0-阿里云-数据库训练营&#39;)" target="_blank" rel="nofollow">
            <div class="bannerbar forpc" style="background-size: contain; background-image: url(https://img2022.cnblogs.com/blog/35695/202205/35695-20220510220927574-745312066.png);">
                <img src="./test_files/35695-20220507181331526-983239102.png" onload="countCreativeImpressions(&#39;C0-阿里云-数据库训练营&#39;);">
            </div>
        </a>
        <div id="bannerbar" class="bannerbar-mobile formobile">
            <a href="https://developer.aliyun.com/trainingcamp/88b6444d8728476090ac3aa1338179f2?utm_content=g_1000340106" onclick="countCreativeClicks(&#39;M2-阿里云-数据库训练营&#39;)" rel="nofollow">
                <img src="./test_files/35695-20220510214350477-1439286895.png" alt="" onload="countCreativeImpressions(&#39;M2-阿里云-数据库训练营&#39;)">
            </a>
        </div>
    <div id="top_nav" class="navbar forpc">
        <nav id="nav_main" class="navbar-main">
            <ul id="nav_left" class="navbar-list navbar-left">
                <li class="navbar-branding">
                    <a href="https://www.cnblogs.com/" title="开发者的网上家园" role="banner">
                        <img src="./test_files/logo.svg" alt="博客园Logo">
                    </a>
                </li>
                <li><a href="https://www.cnblogs.com/" onclick="countClicks(&#39;skin-navbar-sitehome&#39;)">首页</a></li>
                <li><a href="https://news.cnblogs.com/" onclick="countClicks(&#39;nav&#39;, &#39;skin-navbar-news&#39;)">新闻</a></li>
                <li><a href="https://q.cnblogs.com/" onclick="countClicks(&#39;nav&#39;, &#39;skin-navbar-q&#39;)">博问</a></li>
                <li><a id="nav_brandzone" href="https://brands.cnblogs.com/huawei" onclick="countClicks(&#39;nav&#39;, &#39;skin-navbar-brands&#39;)">专区</a></li>
                <li><a href="https://ing.cnblogs.com/" onclick="countClicks(&#39;nav&#39;, &#39;skin-navbar-ing&#39;)">闪存</a></li>
                <li><a href="https://edu.cnblogs.com/" onclick="countClicks(&#39;nav&#39;, &#39;skin-navbar-edu&#39;)">班级</a></li>
            </ul>
            <ul id="nav_right" class="navbar-list navbar-right">
                <li>
                    <form id="zzk_search" class="navbar-search" action="https://zzk.cnblogs.com/s" method="get" role="search">
                        <input name="w" id="zzk_search_input" placeholder="代码改变世界" type="search" tabindex="3">
                        <button type="submit" id="zzk_search_button">
                            <img src="./test_files/search.svg" alt="搜索">
                        </button>
                    </form>
                </li>
                <li id="navbar_login_status" class="navbar-list">
                    <a class="navbar-user-info navbar-blog" href="https://i.cnblogs.com/EditPosts.aspx?opt=1" alt="写随笔" title="写随笔" style="display: none;">
                        <img id="new_post_icon" class="navbar-icon" src="./test_files/newpost.svg" alt="写随笔">
                    </a>
                    <a id="navblog-myblog-icon" class="navbar-user-info navbar-blog" href="https://passport.cnblogs.com/GetBlogApplyStatus.aspx" alt="我的博客" title="我的博客" style="display: none;">
                        <img id="myblog_icon" class="navbar-icon" src="./test_files/myblog.svg" alt="我的博客">
                    </a>
                    <a class="navbar-user-info navbar-message navbar-icon-wrapper" href="https://msg.cnblogs.com/" alt="短消息" title="短消息" style="display: none;">
                        <img id="msg_icon" class="navbar-icon" src="./test_files/message.svg" alt="短消息">
                        <span id="msg_count" style="display: none"></span>
                    </a>
                    <a id="navbar_lite_mode_indicator" data-current-page="blog" style="display: none" href="javascript:void(0)" alt="简洁模式" title="简洁模式启用，您在访问他人博客时会使用简洁款皮肤展示">
                        <img class="navbar-icon" src="./test_files/lite-mode-on.svg" alt="简洁模式">
                    </a>
                    <div id="user_info" class="navbar-user-info dropdown" style="display: none;">
                        <a class="dropdown-button" href="https://home.cnblogs.com/">
                            <img id="user_icon" class="navbar-avatar" src="./test_files/avatar-default.svg" alt="用户头像">
                        </a>
                        <div class="dropdown-menu">
                            <a id="navblog-myblog-text" href="https://passport.cnblogs.com/GetBlogApplyStatus.aspx">我的博客</a>
                            <a href="https://home.cnblogs.com/">我的园子</a>
                            <a href="https://account.cnblogs.com/settings/account">账号设置</a>
                            <a href="javascript:void(0)" id="navbar_lite_mode_toggle" title="简洁模式会使用简洁款皮肤显示所有博客">
    简洁模式 <img id="navbar_lite_mode_on" src="./test_files/lite-mode-check.svg" class="hide"><span id="navbar_lite_mode_spinner" class="hide">...</span>
</a>
                            <a href="javascript:void(0)" onclick="account.logout();">退出登录</a>
                        </div>
                    </div>
                    <a class="navbar-anonymous" href="https://account.cnblogs.com/signup" style="display: inline;">注册</a>
                    <a class="navbar-anonymous" href="javascript:void(0);" onclick="account.login()" style="display: inline;">登录</a>
                </li>
            </ul>
        </nav>
    </div>

    
    
<!--done-->
<div id="home">
<div id="header">
	<div id="blogTitle">
	<a href="https://www.cnblogs.com/yoyoketang/"><img id="blogLogo" src="./test_files/logo.gif" alt="返回主页"></a>			
		
<!--done-->
<h1><a id="Header1_HeaderTitle" class="headermaintitle HeaderMainTitle" href="https://www.cnblogs.com/yoyoketang/">上海-悠悠</a>
</h1>
<h2>2022年第 11 期《python接口web自动化+测试开发》课程（6月5号开学）  <br> 报名联系weixin/qq：*********</h2>




		
	</div><!--end: blogTitle 博客的标题和副标题 -->
</div><!--end: header 头部 -->

<div id="main">
	<div id="mainContent">
	<div class="forFlow">
		<div id="navigator">
			
<ul id="navList">
	<li><a id="blog_nav_sitehome" class="menu" href="https://www.cnblogs.com/">
博客园</a>
</li>
	<li>
<a id="blog_nav_myhome" class="menu" href="https://www.cnblogs.com/yoyoketang/">
首页</a>
</li>
	<li>

<a id="blog_nav_newpost" class="menu" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">
新随笔</a>
</li>
	<li>
<a id="blog_nav_contact" class="menu" href="https://msg.cnblogs.com/send/%E4%B8%8A%E6%B5%B7-%E6%82%A0%E6%82%A0">
联系</a></li>
	<li>
<a id="blog_nav_admin" class="menu" href="https://i.cnblogs.com/">
管理</a>
</li>
	<li>
<a id="blog_nav_rss" class="menu" href="javascript:void(0)" data-rss="https://www.cnblogs.com/yoyoketang/rss/">
订阅</a>
	
<a id="blog_nav_rss_image" href="https://www.cnblogs.com/yoyoketang/rss/">
    <img src="./test_files/xml.gif" alt="订阅">
</a></li>
</ul>



			<div class="blogStats">
				
				<!--done-->
随笔- 995&nbsp;
文章- 2&nbsp;
评论- 1572&nbsp;
阅读- 
<span title="总阅读数: 5015743">
501万</span>&nbsp;



				
			</div><!--end: blogStats -->
		</div><!--end: navigator 博客导航栏 -->
		<div id="post_detail">
<!--done-->
<div id="topics">
	<div class="post">
		<h1 class="postTitle">
			
<a id="cb_post_title_url" class="postTitle2 vertical-middle" href="https://www.cnblogs.com/yoyoketang/p/********.html">
    <span role="heading" aria-level="2">httprunner 3.x学习17 - 断言字符串包含</span>
    
</a><button class="cnblogs-toc-button" title="显示目录导航" aria-expanded="false"></button>

		</h1>
		<div class="clear"></div>
		<div class="postBody">
			<div id="cnblogs_post_body" class="blogpost-body cnblogs-markdown">
<h1 id="前言">前言<button class="cnblogs-toc-button" title="显示目录导航" aria-expanded="false"></button></h1>
<p>校验接口返回结果，我们习惯校验实际结果和期望结果相等，如果只是部分相等可以用contains包含校验</p>
<h1 id="校验包含">校验包含<button class="cnblogs-toc-button" title="显示目录导航" aria-expanded="false"></button></h1>
<p>先看下httprunner/builtin/comparators.py 关于 contains 和 contained_by 函数定义</p>
<ul>
<li>check_value 是需要校验的返回结果</li>
<li>expect_value 是期望结果，可以是这几种类型：list, tuple, dict, basestring</li>
</ul>
<pre highlighted="true"><code class="hljs language-python"><span class="hljs-keyword">def</span> <span class="hljs-title function_">contains</span>(<span class="hljs-params">check_value: <span class="hljs-type">Any</span>, expect_value: <span class="hljs-type">Any</span>, message: Text = <span class="hljs-string">""</span></span>):
    <span class="hljs-keyword">assert</span> <span class="hljs-built_in">isinstance</span>(
        check_value, (<span class="hljs-built_in">list</span>, <span class="hljs-built_in">tuple</span>, <span class="hljs-built_in">dict</span>, <span class="hljs-built_in">str</span>, <span class="hljs-built_in">bytes</span>)
    ), <span class="hljs-string">"expect_value should be list/tuple/dict/str/bytes type"</span>
    <span class="hljs-keyword">assert</span> expect_value <span class="hljs-keyword">in</span> check_value, message


<span class="hljs-keyword">def</span> <span class="hljs-title function_">contained_by</span>(<span class="hljs-params">check_value: <span class="hljs-type">Any</span>, expect_value: <span class="hljs-type">Any</span>, message: Text = <span class="hljs-string">""</span></span>):
    <span class="hljs-keyword">assert</span> <span class="hljs-built_in">isinstance</span>(
        check_value, (<span class="hljs-built_in">list</span>, <span class="hljs-built_in">tuple</span>, <span class="hljs-built_in">dict</span>, <span class="hljs-built_in">str</span>, <span class="hljs-built_in">bytes</span>)
    ), <span class="hljs-string">"expect_value should be list/tuple/dict/str/bytes type"</span>
    <span class="hljs-keyword">assert</span> check_value <span class="hljs-keyword">in</span> expect_value, message
</code></pre>
<h1 id="使用示例">使用示例<button class="cnblogs-toc-button" title="显示目录导航" aria-expanded="false"></button></h1>
<p>登录结果返回</p>
<pre highlighted="true"><code class="hljs language-bash"><span class="hljs-comment"># 作者-上海悠悠 QQ交流群:717225969</span>
<span class="hljs-comment"># blog地址 https://www.cnblogs.com/yoyoketang/</span>

{
    <span class="hljs-string">"code"</span>:0,
    <span class="hljs-string">"msg"</span>:<span class="hljs-string">"login success!"</span>,
    <span class="hljs-string">"username"</span>:<span class="hljs-string">"test1"</span>,
    <span class="hljs-string">"token"</span>:<span class="hljs-string">"2a05f8e450d590f4ea3aba66294a26ec3fe8e0cf"</span>
}
</code></pre>
<p>校验 msg 包含 success! 字符串</p>
<pre highlighted="true"><code class="hljs language-yaml">    <span class="hljs-attr">validate:</span>
        <span class="hljs-bullet">-</span> <span class="hljs-attr">eq:</span> [<span class="hljs-string">status_code</span>, <span class="hljs-number">200</span>]
        <span class="hljs-bullet">-</span> <span class="hljs-attr">contains:</span> [<span class="hljs-string">body.msg</span>, <span class="hljs-string">success!</span>]
</code></pre>
<p>如果code返回的结果有多种情况，可能为0，也可以为200，这2种情况但是可以的，于是可以用 contained_by 被包含在[0, 200]</p>
<pre highlighted="true"><code class="hljs language-yaml">    <span class="hljs-attr">validate:</span>
        <span class="hljs-bullet">-</span> <span class="hljs-attr">eq:</span> [<span class="hljs-string">status_code</span>, <span class="hljs-number">200</span>]
        <span class="hljs-bullet">-</span> <span class="hljs-attr">contains:</span> [<span class="hljs-string">body.msg</span>, <span class="hljs-string">success!</span>]
        <span class="hljs-bullet">-</span> <span class="hljs-attr">contained_by:</span> [<span class="hljs-string">body.code</span>, [<span class="hljs-number">0</span>, <span class="hljs-number">200</span>]]
</code></pre>
<p>对应pytest代码</p>
<pre highlighted="true"><code class="hljs language-x86asm"><span class="hljs-meta">            .validate</span>()
<span class="hljs-meta">            .assert_equal</span>(<span class="hljs-string">"status_code"</span>, <span class="hljs-number">200</span>)
<span class="hljs-meta">            .assert_contains</span>(<span class="hljs-string">"body.msg"</span>, <span class="hljs-string">"success!"</span>)
<span class="hljs-meta">            .assert_contained_by</span>(<span class="hljs-string">"body.code"</span>, [<span class="hljs-number">0</span>, <span class="hljs-number">200</span>])
</code></pre>
<p>但是会抛出异常：</p>
<pre highlighted="true"><code class="hljs language-yaml"><span class="hljs-attr">E   httprunner.exceptions.ValidationFailure:</span> <span class="hljs-string">assert</span> <span class="hljs-string">body.code</span> <span class="hljs-string">contained_by</span> [<span class="hljs-number">0</span>, <span class="hljs-number">200</span>]<span class="hljs-string">(list)</span>       <span class="hljs-string">==&gt;</span> <span class="hljs-string">fail</span>
<span class="hljs-attr">E   check_item:</span> <span class="hljs-string">body.code</span>
<span class="hljs-attr">E   check_value:</span> <span class="hljs-number">0</span><span class="hljs-string">(int)</span>
<span class="hljs-attr">E   assert_method:</span> <span class="hljs-string">contained_by</span>
<span class="hljs-attr">E   expect_value:</span> [<span class="hljs-number">0</span>, <span class="hljs-number">200</span>]<span class="hljs-string">(list)</span>
<span class="hljs-attr">E   message:</span> <span class="hljs-string">expect_value</span> <span class="hljs-string">should</span> <span class="hljs-string">be</span> <span class="hljs-string">list/tuple/dict/str/bytes</span> <span class="hljs-string">type</span>
</code></pre>
<p>报错说  expect_value 必须是 list/tuple/dict/str/bytes 类型，但确实是给的list 类型[0, 200]</p>
<p>这是因为httprunner3.1.4版本的一个bug,把上面源码里面的contained_by函数check_value改成expect_value，这个bug就可以修复</p>
<pre highlighted="true"><code class="hljs language-python"><span class="hljs-keyword">def</span> <span class="hljs-title function_">contained_by</span>(<span class="hljs-params">check_value: <span class="hljs-type">Any</span>, expect_value: <span class="hljs-type">Any</span>, message: Text = <span class="hljs-string">""</span></span>):
    <span class="hljs-keyword">assert</span> <span class="hljs-built_in">isinstance</span>(
        expect_value, (<span class="hljs-built_in">list</span>, <span class="hljs-built_in">tuple</span>, <span class="hljs-built_in">dict</span>, <span class="hljs-built_in">str</span>, <span class="hljs-built_in">bytes</span>)
    ), <span class="hljs-string">"expect_value should be list/tuple/dict/str/bytes type"</span>
    <span class="hljs-keyword">assert</span> check_value <span class="hljs-keyword">in</span> expect_value, message
</code></pre>
<p>(备注：此问题在httprunner 3.1.5版本已修复，更新时间2021年6月27号)</p>
<h1 id="startswith-和-endswith">startswith 和 endswith<button class="cnblogs-toc-button" title="显示目录导航" aria-expanded="false"></button></h1>
<p>校验字符串以什么和以什么结果</p>
<ul>
<li>startswith  字符串以expect_value开头</li>
<li>endswith  字符串以expect_value结尾</li>
</ul>
<pre highlighted="true"><code class="hljs language-python"><span class="hljs-keyword">def</span> <span class="hljs-title function_">startswith</span>(<span class="hljs-params">check_value: <span class="hljs-type">Any</span>, expect_value: <span class="hljs-type">Any</span>, message: Text = <span class="hljs-string">""</span></span>):
    <span class="hljs-keyword">assert</span> <span class="hljs-built_in">str</span>(check_value).startswith(<span class="hljs-built_in">str</span>(expect_value)), message


<span class="hljs-keyword">def</span> <span class="hljs-title function_">endswith</span>(<span class="hljs-params">check_value: Text, expect_value: <span class="hljs-type">Any</span>, message: Text = <span class="hljs-string">""</span></span>):
    <span class="hljs-keyword">assert</span> <span class="hljs-built_in">str</span>(check_value).endswith(<span class="hljs-built_in">str</span>(expect_value)), message
</code></pre>
<p>使用示例</p>
<pre highlighted="true"><code class="hljs language-yaml">    <span class="hljs-attr">validate:</span>
        <span class="hljs-bullet">-</span> <span class="hljs-attr">eq:</span> [<span class="hljs-string">status_code</span>, <span class="hljs-number">200</span>]
        <span class="hljs-bullet">-</span> <span class="hljs-attr">startswith:</span> [<span class="hljs-string">body.msg</span>, <span class="hljs-string">login</span>]
        <span class="hljs-bullet">-</span> <span class="hljs-attr">endswith:</span> [<span class="hljs-string">body.msg</span>, <span class="hljs-string">success!</span>]
</code></pre>
<p>pytest 示例</p>
<pre highlighted="true"><code class="hljs language-x86asm"><span class="hljs-meta">            .validate</span>()
<span class="hljs-meta">            .assert_equal</span>(<span class="hljs-string">"status_code"</span>, <span class="hljs-number">200</span>)
<span class="hljs-meta">            .assert_startswith</span>(<span class="hljs-string">"body.msg"</span>, <span class="hljs-string">"login"</span>)
<span class="hljs-meta">            .assert_endswith</span>(<span class="hljs-string">"body.msg"</span>, <span class="hljs-string">"success!"</span>)
</code></pre>
<p>网易云课程地址</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1211857821&amp;share=2&amp;shareId=480000002230338" target="_blank" rel="noopener">
           <img src="./test_files/1070438-20211122214430443-1487129324.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana" href="https://study.163.com/course/courseMain.htm?courseId=1211857821&amp;share=2&amp;shareId=480000002230338" rel="noopener">点我 -&gt;立即报名</a>
</p>
</div>
<div class="clear"></div>
<div id="blog_post_info_block" role="contentinfo">
<div id="EntryTag">
    标签: 
            <a href="https://www.cnblogs.com/yoyoketang/tag/httprunner%203.x/">httprunner 3.x</a>,             <a href="https://www.cnblogs.com/yoyoketang/tag/httprunner/">httprunner</a></div>

    <div id="blog_post_info">
<div id="green_channel">
        <a href="javascript:void(0);" id="green_channel_digg" onclick="DiggIt(********,cb_blogId,1);green_channel_success(this,&#39;谢谢推荐！&#39;);">好文要顶</a>
        <a id="green_channel_follow" onclick="follow(&#39;95cb2f22-a6b3-e611-845c-ac853d9f53ac&#39;);" href="javascript:void(0);">关注我</a>
    <a id="green_channel_favorite" onclick="AddToWz(cb_entryId);return false;" href="javascript:void(0);">收藏该文</a>
    <a id="green_channel_weibo" href="javascript:void(0);" title="分享至新浪微博" onclick="ShareToTsina()"><img src="./test_files/icon_weibo_24.png" alt=""></a>
    <a id="green_channel_wechat" href="javascript:void(0);" title="分享至微信" onclick="shareOnWechat()"><img src="./test_files/wechat.png" alt=""></a>
</div>
<div id="author_profile">
    <div id="author_profile_info" class="author_profile_info">
            <a href="https://home.cnblogs.com/u/yoyoketang/" target="_blank"><img src="./test_files/20161126151035.png" class="author_avatar" alt=""></a>
        <div id="author_profile_detail" class="author_profile_info">
            <a href="https://home.cnblogs.com/u/yoyoketang/">上海-悠悠</a><br>
            <a href="https://home.cnblogs.com/u/yoyoketang/followees/">关注 - 73</a><br>
            <a href="https://home.cnblogs.com/u/yoyoketang/followers/">粉丝 - 3853</a>
        </div>
    </div>
    <div class="clear"></div>
    <div id="author_profile_honor"></div>
    <div id="author_profile_follow">
                <a href="javascript:void(0);" onclick="follow(&#39;95cb2f22-a6b3-e611-845c-ac853d9f53ac&#39;);return false;">+加关注</a>
    </div>
</div>
<div id="div_digg">
    <div class="diggit" onclick="votePost(********,&#39;Digg&#39;)">
        <span class="diggnum" id="digg_count">0</span>
    </div>
    <div class="buryit" onclick="votePost(********,&#39;Bury&#39;)">
        <span class="burynum" id="bury_count">0</span>
    </div>
    <div class="clear"></div>
    <div class="diggword" id="digg_tips">
    </div>
</div>

<script type="text/javascript">
    currentDiggType = 0;
</script></div>
    <div class="clear"></div>
    <div id="post_next_prev">

    <a href="https://www.cnblogs.com/yoyoketang/p/14926423.html" class="p_n_p_prefix">« </a> 上一篇：    <a href="https://www.cnblogs.com/yoyoketang/p/14926423.html" data-featured-image="" title="发布于 2021-06-24 12:53">httprunner 2.x学习15 - response 返回 html 页面解码</a>
    <br>
    <a href="https://www.cnblogs.com/yoyoketang/p/14928878.html" class="p_n_p_prefix">» </a> 下一篇：    <a href="https://www.cnblogs.com/yoyoketang/p/14928878.html" data-featured-image="" title="发布于 2021-06-24 22:34">httprunner 3.x学习18 - validate 断言总结</a>

</div>
</div>
		</div>
		<div class="postDesc">posted @ 
<span id="post-date">2021-06-24 19:50</span>&nbsp;
<a href="https://www.cnblogs.com/yoyoketang/">上海-悠悠</a>&nbsp;
阅读(<span id="post_view_count">909</span>)&nbsp;
评论(<span id="post_comment_count">0</span>)&nbsp;
<a href="https://i.cnblogs.com/EditPosts.aspx?postid=********" rel="nofollow">编辑</a>&nbsp;
<a href="javascript:void(0)" onclick="AddToWz(********);return false;">收藏</a>&nbsp;
<a href="javascript:void(0)" onclick="reportManager.report({ currentUserId: &#39;&#39;, targetType: &#39;blogPost&#39;, targetId: &#39;********&#39;, targetLink: &#39;https://www.cnblogs.com/yoyoketang/p/********.html&#39;, title: &#39;httprunner 3.x学习17 - 断言字符串包含&#39; })">举报</a></div>
	</div>
	
	
</div><!--end: topics 文章、评论容器-->

<script>
    var cb_entryId = ********, cb_entryCreatedDate = '2021-06-24 19:50', cb_postType = 1, cb_postTitle = 'httprunner 3.x学习17 - 断言字符串包含';
    var allowComments = true, cb_blogId = 319138, cb_blogApp = 'yoyoketang', cb_blogUserGuid = '95cb2f22-a6b3-e611-845c-ac853d9f53ac';
    mermaidRender.render()
    markdown_highlight()
    zoomManager.apply("#cnblogs_post_body img:not(.code_img_closed):not(.code_img_opened)");
    updatePostStats(
            [cb_entryId],
            function(id, count) { $("#post_view_count").text(count) },
            function(id, count) { $("#post_comment_count").text(count) })
</script>
<a id="!comments"></a>
<div id="blog-comments-placeholder"></div>
<div id="comment_form" class="commentform">
    <a name="commentform"></a>
    <div id="divCommentShow"></div>
    <div id="comment_nav"><span id="span_refresh_tips"></span><a href="javascript:void(0);" onclick="return RefreshCommentList();" id="lnk_RefreshComments" runat="server" clientidmode="Static">刷新评论</a><a href="https://www.cnblogs.com/yoyoketang/p/********.html#" onclick="return RefreshPage();">刷新页面</a><a href="https://www.cnblogs.com/yoyoketang/p/********.html#top">返回顶部</a></div>
    <div id="comment_form_container" style="visibility: visible;"><div class="login_tips">
    登录后才能查看或发表评论，立即 <a rel="nofollow" href="javascript:void(0);" class="underline" onclick="return account.login(&#39;!comments&#39;);">登录</a> 或者
    <a href="https://www.cnblogs.com/">逛逛</a> 博客园首页
</div>
</div>
    <div class="ad_text_commentbox" id="ad_text_under_commentbox"></div>
    <div id="cnblogs_ch"><a href="https://developer.aliyun.com/trainingcamp/88b6444d8728476090ac3aa1338179f2?utm_content=g_1000340106" target="_blank" onclick="gtag(&#39;event&#39;, &#39;click&#39;, {&#39;event_category&#39;: &#39;ad&#39;, &#39;event_label&#39;: &#39;T2-阿里云-数据库训练营&#39;})">【推荐】阿里云数据库训练营，云数据库 MySQL 从入门到高阶</a><br><a href="https://cloud.tencent.com/act/pro/618warmup?fromSource=gwzcw.6398046.6398046.6398046&amp;utm_medium=cps&amp;utm_id=gwzcw.6398046.6398046.6398046&amp;cps_key=6a15b90f1178f38fb09b07f16943cf3e" target="_blank" onclick="gtag(&#39;event&#39;, &#39;click&#39;, {&#39;event_category&#39;: &#39;ad&#39;, &#39;event_label&#39;: &#39;T2-腾讯云-618采购季&#39;})">【推荐】腾讯云618采购季，年中优惠抢先看，早鸟优惠提前享</a><br></div>
    <div id="opt_under_post"></div>
    <div id="cnblogs_c1" class="under-post-card">
            <div id="div-gpt-ad-1592365906576-0" style="width: 300px; height: 250px;" data-google-query-id="COPy5sTi_vcCFQmH6QUdmNQCsg"><div id="google_ads_iframe_/1090369/C1_0__container__" style="border: 0pt none;"><iframe id="google_ads_iframe_/1090369/C1_0" name="google_ads_iframe_/1090369/C1_0" title="3rd party ad content" width="300" height="250" scrolling="no" marginwidth="0" marginheight="0" frameborder="0" role="region" aria-label="Advertisement" tabindex="0" data-google-container-id="1" style="border: 0px; vertical-align: bottom;" data-load-complete="true" src="./test_files/saved_resource.html"></iframe></div></div>
    </div>
    <div id="under_post_card1"><div class="under-post-card">
<b>编辑推荐：</b>
<br>

· <a href="https://www.cnblogs.com/crossoverJie/p/16302199.html" target="_blank">分享一个 SpringCloud Feign 中所埋藏的坑</a>
    <br>
· <a href="https://www.cnblogs.com/coco1s/p/16304660.html" target="_blank">动画还可以这样控制？</a>
    <br>
· <a href="https://www.cnblogs.com/CKExp/p/16306835.html" target="_blank">理解 RESTful Api 设计</a>
    <br>
· <a href="https://www.cnblogs.com/strick/p/16173134.html" target="_blank">从几次事故引起的对项目质量保障的思考</a>
    <br>
· <a href="https://www.cnblogs.com/deatharthas/p/15409455.html" target="_blank">聊聊 C# 中的 Visitor 模式 </a>
    <br>
</div></div>
    <div id="under_post_card2"><div class="itnews under-post-card">
    <b>最新新闻</b>：
    <br>
 ·          <a href="https://news.cnblogs.com/n/721599/" target="_blank">因发表不当言论，开源作者遭OBS项目社区封杀</a>
        <br>
 ·          <a href="https://news.cnblogs.com/n/721598/" target="_blank">实探上海多家快递转运中心：个人散件寄递业务加速恢复</a>
        <br>
 ·          <a href="https://news.cnblogs.com/n/721579/" target="_blank">“王心凌男孩”救不了芒果超媒</a>
        <br>
 ·          <a href="https://news.cnblogs.com/n/721577/" target="_blank">费曼狂想曲：能够进入体内的「外科医生」</a>
        <br>
 ·          <a href="https://news.cnblogs.com/n/721581/" target="_blank">哄一哄能让GPT-3准确率暴涨61%！谷歌&amp;东京大学研究震惊四座</a>
        <br>
    » <a href="https://news.cnblogs.com/" title="IT 新闻" target="_blank">更多新闻...</a>
</div></div>
    <div id="HistoryToday" class="under-post-card"></div>
    <script type="text/javascript">
        var commentManager = new blogCommentManager();
        commentManager.renderComments(0);
        fixPostBody();

                        window.tocManager.displayDisableTocTips = false;
                        window.tocManager.generateToc();
            setTimeout(function() { incrementViewCount(cb_entryId); }, 50);        deliverT2();
        deliverC1C2();
        loadNewsAndKb();
LoadPostCategoriesTags(cb_blogId, cb_entryId);        LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
        GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate, cb_postType);
        loadOptUnderPost();
        GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);
    </script>
</div>

</div>


	</div><!--end: forFlow -->
	</div><!--end: mainContent 主体内容容器-->

	<div id="sideBar">
		<div id="sideBarMain">
			<div id="sidebar_news" class="newsItem"><!--done-->
<div class="newsItem">
<h3 class="catListTitle">公告</h3>
	
<div id="blog-news">
    <h3>Python自动化交流4群</h3>
<h3>QQ群：730246532</h3>
<a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=HPr5PcZl8izP7ElzqP9KW4paIYFu4ioF&amp;jump_from=webapi">
<img border="0" src="./test_files/group.png" alt="Python自动化交流4群" title="Python自动化交流4群">
</a>
<br><br>
<span style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;">精品课程在线学习</span>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1212751823&amp;share=2&amp;shareId=480000002230338" target="_blank">
           <img src="./test_files/1070438-20220418151357900-110400673.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1212751823&amp;share=2&amp;shareId=480000002230338">报名咨询wx:*********</a>
</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1211387804&amp;share=2&amp;shareId=480000002230338" target="_blank">
           <img src="./test_files/1070438-20210326232511777-1457944166.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1211387804&amp;share=2&amp;shareId=480000002230338">点我 -&gt;开始刷面试题(100题)</a>
</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?share=2&amp;shareId=480000002230338&amp;courseId=1210983809" target="_blank">
        <img src="./test_files/1070438-20201214183448557-620677932.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?share=2&amp;shareId=480000002230338&amp;courseId=1210983809">报名咨询wx:*********</a>
</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1211857821&amp;share=2&amp;shareId=480000002230338" target="_blank">
           <img src="./test_files/1070438-20210712204349847-1105702844.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1211857821&amp;share=2&amp;shareId=480000002230338">点我 -&gt;立即报名</a>
</p>

<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1211561801&amp;share=2&amp;shareId=480000002230338" target="_blank">
           <img src="./test_files/1070438-20210517222739450-746447237.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1211561801&amp;share=2&amp;shareId=480000002230338">点我 -&gt;立即报名</a>
</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1210886815&amp;share=2&amp;shareId=480000002230338" target="_blank">
           <img src="./test_files/1070438-20201111223626531-745880644.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1210886815&amp;share=2&amp;shareId=480000002230338">点我 -&gt;立即报名</a>
</p>
<br>
<p>
    <a href="https://study.163.com/course/courseMain.htm?courseId=1211528802&amp;share=2&amp;shareId=480000002230338" target="_blank">
        <img src="./test_files/1070438-20210413215431089-1689662992.png">
    </a>
    <br>
    <a style="font-weight: bold; font-size: 12.5pt; font-family: Verdana;" href="https://study.163.com/course/courseMain.htm?courseId=1211528802&amp;share=2&amp;shareId=480000002230338">点我 -&gt;立即报名</a>
</p>
<br>
<span style="font-weight: bold; font-size: 10.5pt; font-family: Verdana;">个人微信公众号：yoyoketang</span>

<img width="200" height="200" src="./test_files/1070438-20170417224839696-1584175751.jpg">
    <div id="profile_block">
        昵称：
        <a href="https://home.cnblogs.com/u/yoyoketang/">
            上海-悠悠
        </a>
        <br>
        园龄：
        <a href="https://home.cnblogs.com/u/yoyoketang/" title="入园时间：2016-11-26">
            5年6个月
        </a>
        <br>
        粉丝：
        <a href="https://home.cnblogs.com/u/yoyoketang/followers/">
            3853
        </a>
        <br>
        关注：
        <a href="https://home.cnblogs.com/u/yoyoketang/followees/">
            73
        </a>
        <div id="p_b_follow">
<a href="javascript:void(0)" onclick="follow(&#39;95cb2f22-a6b3-e611-845c-ac853d9f53ac&#39;)">+加关注</a></div>
        <script>getFollowStatus('95cb2f22-a6b3-e611-845c-ac853d9f53ac');</script>
    </div>
</div>
</div>

</div>
<div id="sidebar_c3" style="display: block;"><a href="https://developer.aliyun.com/trainingcamp/88b6444d8728476090ac3aa1338179f2?utm_content=g_1000340106" target="_blank"><img width="300" height="250" src="./test_files/35695-20220510211751231-240798822.jpg" alt="阿里云-数据库训练营" onclick="countClicks(&#39;ad&#39;, &#39;C3-阿里云-数据库训练营&#39;);"></a></div>
			<div id="calendar"><div id="blog-calendar" style="">

<table id="blogCalendar" class="Cal" cellspacing="0" cellpadding="0" title="Calendar" border="0">
    <tbody>
        <tr>
            <td colspan="7">
                <table class="CalTitle" cellspacing="0" border="0">
                    <tbody>
                        <tr>
                            <td class="CalNextPrev">
                                <a href="javascript:void(0);" onclick="loadBlogCalendar(&#39;2022/04/27&#39;); return false;">&lt;</a>
                            </td>
                            <td align="center">2022年5月</td>
                            <td align="right" class="CalNextPrev">
                                <a href="javascript:void(0);" onclick="loadBlogCalendar(&#39;2022/06/27&#39;); return false;">&gt;</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    <tr>
        <th class="CalDayHeader" align="center" abbr="日" scope="col">日</th>
        <th class="CalDayHeader" align="center" abbr="一" scope="col">一</th>
        <th class="CalDayHeader" align="center" abbr="二" scope="col">二</th>
        <th class="CalDayHeader" align="center" abbr="三" scope="col">三</th>
        <th class="CalDayHeader" align="center" abbr="四" scope="col">四</th>
        <th class="CalDayHeader" align="center" abbr="五" scope="col">五</th>
        <th class="CalDayHeader" align="center" abbr="六" scope="col">六</th>
    </tr>
            <tr>
                        <td class="CalWeekendDay" align="center">
                            <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/01.html"><u>1</u></a>
                        </td>
                        <td class="" align="center">
                            2
                        </td>
                        <td class="" align="center">
                            3
                        </td>
                        <td class="" align="center">
                            4
                        </td>
                        <td class="" align="center">
                            5
                        </td>
                        <td class="" align="center">
                            6
                        </td>
                    <td class="CalWeekendDay" align="center">
                        <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/07.html"><u>7</u></a>
                    </td>
            </tr>
                <tr>
                        <td class="CalWeekendDay" align="center">
                            8
                        </td>
                            <td class="" align="center">
                                9
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/10.html"><u>10</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/11.html"><u>11</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/12.html"><u>12</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/13.html"><u>13</u></a>
                            </td>
                        <td class="CalWeekendDay" align="center">
                            <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/14.html"><u>14</u></a>
                        </td>
                </tr>
                <tr>
                        <td class="CalWeekendDay" align="center">
                            15
                        </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/16.html"><u>16</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/17.html"><u>17</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/18.html"><u>18</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/19.html"><u>19</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/20.html"><u>20</u></a>
                            </td>
                        <td class="CalWeekendDay" align="center">
                            21
                        </td>
                </tr>
                <tr>
                        <td class="CalWeekendDay" align="center">
                            22
                        </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/23.html"><u>23</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/24.html"><u>24</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/25.html"><u>25</u></a>
                            </td>
                            <td class="" align="center">
                                <a href="https://www.cnblogs.com/yoyoketang/archive/2022/05/26.html"><u>26</u></a>
                            </td>
                            <td class="CalTodayDay" align="center">
                                27
                            </td>
                        <td class="CalWeekendDay" align="center">
                            28
                        </td>
                </tr>
                <tr>
                        <td class="CalWeekendDay" align="center">
                            29
                        </td>
                            <td class="" align="center">
                                30
                            </td>
                            <td class="" align="center">
                                31
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                1
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                2
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                3
                            </td>
                        <td class="CalOtherMonthDay" align="center">
                            4
                        </td>
                </tr>
                <tr>
                        <td class="CalOtherMonthDay" align="center">
                            5
                        </td>
                            <td class="CalOtherMonthDay" align="center">
                                6
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                7
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                8
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                9
                            </td>
                            <td class="CalOtherMonthDay" align="center">
                                10
                            </td>
                        <td class="CalOtherMonthDay" align="center">
                            11
                        </td>
                </tr>
    </tbody>
</table></div><script>loadBlogDefaultCalendar();</script></div>
			
			<div id="leftcontentcontainer">
				<div id="blog-sidecolumn"><!-- 搜索 -->
<div id="sidebar_search" class="sidebar-block">
    <div id="sidebar_search" class="mySearch">
        <h3 class="catListTitle">搜索</h3>
        <div id="sidebar_search_box">
            <div id="widget_my_zzk" class="div_my_zzk">
                <input type="text" id="q" onkeydown="return zzk_go_enter(event);" class="input_my_zzk">&nbsp;<input onclick="zzk_go()" type="button" value="找找看" id="btnZzk" class="btn_my_zzk">
            </div>
            <div id="widget_my_google" class="div_my_zzk">
                <input type="text" name="google_q" id="google_q" onkeydown="return google_go_enter(event);" class="input_my_zzk">&nbsp;<input onclick="google_go()" type="button" value="谷歌搜索" class="btn_my_zzk">
            </div>
        </div>
    </div>
</div>

<!-- 常用链接 -->
<div id="sidebar_shortcut" class="sidebar-block"><div class="catListLink">
<h3 class="catListTitle">
常用链接
</h3>
<ul>
    <li><a href="https://www.cnblogs.com/yoyoketang/p/" title="我的博客的随笔列表">我的随笔</a></li>
<li><a href="https://www.cnblogs.com/yoyoketang/MyComments.html" title="我的发表过的评论列表">我的评论</a></li>
<li><a href="https://www.cnblogs.com/yoyoketang/OtherPosts.html" title="我评论过的随笔列表">我的参与</a></li>
<li><a href="https://www.cnblogs.com/yoyoketang/comments" title="我的博客的评论列表">最新评论</a></li>
<li><a href="https://www.cnblogs.com/yoyoketang/tag/" title="我的博客的标签列表">我的标签</a></li>

    <li><a id="itemListLink" onclick="this.blur();WarpClass(&#39;itemListLink&#39;, &#39;itemListLin_con&#39;);return false;" href="https://www.cnblogs.com/yoyoketang/p/********.html#">更多链接</a></li>
</ul>
</div>

</div>

<!-- 最新随笔 -->


<!-- 我的标签 -->
<div id="sidebar_toptags" class="sidebar-block"><div class="catListTag">
<h3 class="catListTitle">我的标签</h3>
<ul>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/django/">django<span class="tag-count">(196)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/python/">python<span class="tag-count">(112)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/selenium/">selenium<span class="tag-count">(101)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/pytest/">pytest<span class="tag-count">(81)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/httprunner/">httprunner<span class="tag-count">(72)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/appium/">appium<span class="tag-count">(64)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/%E9%9D%A2%E8%AF%95%E9%A2%98/">面试题<span class="tag-count">(54)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/jmeter/">jmeter<span class="tag-count">(49)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/python%E6%8E%A5%E5%8F%A3%E8%87%AA%E5%8A%A8%E5%8C%96/">python接口自动化<span class="tag-count">(46)</span></a>
        </li>
        <li>
            <a href="https://www.cnblogs.com/yoyoketang/tag/bootstrap/">bootstrap<span class="tag-count">(46)</span></a>
        </li>
    <li>
        <a href="https://www.cnblogs.com/yoyoketang/tag/">更多</a>
    </li>

</ul>
</div>

</div>

<!-- 积分与排名 -->
<div id="sidebar_scorerank" class="sidebar-block"><div class="catListBlogRank">
<h3 class="catListTitle">积分与排名</h3>
<ul>
	<li class="liScore">
        积分 -	
1872824
    </li>
	<li class="liRank">
		排名 -	
72
	</li>
</ul>
</div>



</div>

<!-- 随笔分类、随笔档案、文章分类、新闻分类、相册、链接 -->
<div id="sidebar_categories">

    <div class="catListPostArchive">
        <h3 class="catListTitle">
            
随笔档案


        </h3>

        <ul>
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2022/05.html" class="category-item-link" rel="" target="">2022年5月(40)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2022/04.html" class="category-item-link" rel="" target="">2022年4月(18)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2022/03.html" class="category-item-link" rel="" target="">2022年3月(9)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2022/02.html" class="category-item-link" rel="" target="">2022年2月(18)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2022/01.html" class="category-item-link" rel="" target="">2022年1月(2)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/12.html" class="category-item-link" rel="" target="">2021年12月(13)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/11.html" class="category-item-link" rel="" target="">2021年11月(20)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/10.html" class="category-item-link" rel="" target="">2021年10月(22)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/09.html" class="category-item-link" rel="" target="">2021年9月(43)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/08.html" class="category-item-link" rel="" target="">2021年8月(18)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/07.html" class="category-item-link" rel="" target="">2021年7月(17)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/06.html" class="category-item-link" rel="" target="">2021年6月(38)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/05.html" class="category-item-link" rel="" target="">2021年5月(37)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/04.html" class="category-item-link" rel="" target="">2021年4月(10)</a>
 
                </li>                
                <li data-category-list-item-visible="true" style="display: block">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/03.html" class="category-item-link" rel="" target="">2021年3月(8)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/02.html" class="category-item-link" rel="" target="">2021年2月(11)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2021/01.html" class="category-item-link" rel="" target="">2021年1月(33)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/12.html" class="category-item-link" rel="" target="">2020年12月(41)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/11.html" class="category-item-link" rel="" target="">2020年11月(11)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/10.html" class="category-item-link" rel="" target="">2020年10月(3)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/09.html" class="category-item-link" rel="" target="">2020年9月(15)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/08.html" class="category-item-link" rel="" target="">2020年8月(4)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/07.html" class="category-item-link" rel="" target="">2020年7月(9)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/06.html" class="category-item-link" rel="" target="">2020年6月(33)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/05.html" class="category-item-link" rel="" target="">2020年5月(37)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/04.html" class="category-item-link" rel="" target="">2020年4月(6)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/03.html" class="category-item-link" rel="" target="">2020年3月(13)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/02.html" class="category-item-link" rel="" target="">2020年2月(1)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2020/01.html" class="category-item-link" rel="" target="">2020年1月(10)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/12.html" class="category-item-link" rel="" target="">2019年12月(8)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/11.html" class="category-item-link" rel="" target="">2019年11月(17)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/10.html" class="category-item-link" rel="" target="">2019年10月(22)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/09.html" class="category-item-link" rel="" target="">2019年9月(25)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/08.html" class="category-item-link" rel="" target="">2019年8月(5)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/07.html" class="category-item-link" rel="" target="">2019年7月(4)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/06.html" class="category-item-link" rel="" target="">2019年6月(8)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/05.html" class="category-item-link" rel="" target="">2019年5月(9)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/04.html" class="category-item-link" rel="" target="">2019年4月(20)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/03.html" class="category-item-link" rel="" target="">2019年3月(18)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/02.html" class="category-item-link" rel="" target="">2019年2月(7)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2019/01.html" class="category-item-link" rel="" target="">2019年1月(27)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/12.html" class="category-item-link" rel="" target="">2018年12月(16)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/11.html" class="category-item-link" rel="" target="">2018年11月(7)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/10.html" class="category-item-link" rel="" target="">2018年10月(15)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/09.html" class="category-item-link" rel="" target="">2018年9月(13)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/08.html" class="category-item-link" rel="" target="">2018年8月(19)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/07.html" class="category-item-link" rel="" target="">2018年7月(14)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/06.html" class="category-item-link" rel="" target="">2018年6月(11)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/05.html" class="category-item-link" rel="" target="">2018年5月(20)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/04.html" class="category-item-link" rel="" target="">2018年4月(15)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/03.html" class="category-item-link" rel="" target="">2018年3月(5)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/02.html" class="category-item-link" rel="" target="">2018年2月(1)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2018/01.html" class="category-item-link" rel="" target="">2018年1月(10)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/12.html" class="category-item-link" rel="" target="">2017年12月(11)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/11.html" class="category-item-link" rel="" target="">2017年11月(5)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/10.html" class="category-item-link" rel="" target="">2017年10月(4)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/09.html" class="category-item-link" rel="" target="">2017年9月(10)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/08.html" class="category-item-link" rel="" target="">2017年8月(5)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/07.html" class="category-item-link" rel="" target="">2017年7月(9)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/06.html" class="category-item-link" rel="" target="">2017年6月(6)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/05.html" class="category-item-link" rel="" target="">2017年5月(10)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/04.html" class="category-item-link" rel="" target="">2017年4月(20)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/03.html" class="category-item-link" rel="" target="">2017年3月(18)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/02.html" class="category-item-link" rel="" target="">2017年2月(3)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2017/01.html" class="category-item-link" rel="" target="">2017年1月(1)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2016/12.html" class="category-item-link" rel="" target="">2016年12月(33)</a>
 
                </li>                
                <li data-category-list-item-visible="false" style="display: none">
                    
<a href="https://www.cnblogs.com/yoyoketang/archive/2016/11.html" class="category-item-link" rel="" target="">2016年11月(4)</a>
 
                </li>                
            <li><a href="javascript:void(0)" onclick="sideColumnManager.loadMore(this)">更多</a></li>
        </ul>
    </div>    
</div>

<!-- 最新评论 -->
<!-- 阅读排行榜 -->
<div id="sidebar_topviewedposts" class="sidebar-block"><div class="catListView">
    <h3 class="catListTitle">
        <a href="https://www.cnblogs.com/yoyoketang/most-viewed" class="sidebar-card-title-a">
    阅读排行榜
</a>

    </h3>
    <div id="TopViewPostsBlock">
        <ul style="word-break:break-all">
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/10144581.html">
                            1. 关于面试总结9-接口测试面试题(145818)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/9083932.html">
                            2. python笔记16-执行cmd指令（os.system和os.popen）(93612)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/6719717.html">
                            3. Fiddler抓包3-查看get与post请求(89310)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/6557421.html">
                            4. Selenium2+python自动化45-18种定位方法（find_elements）(76877)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/10118924.html">
                            5. 关于面试总结6-SQL经典面试题(64343)
                        </a>
                    </li>
            <li>
                
            </li>
        </ul>
    </div>
</div></div>

<!-- 评论排行榜 -->
<div id="sidebar_topcommentedposts" class="sidebar-block"><div class="catListFeedback">
    <h3 class="catListTitle">
        <a href="https://www.cnblogs.com/yoyoketang/most-commented" class="sidebar-card-title-a">评论排行榜</a>

    </h3>
    <div id="TopFeedbackPostsBlock">
        <ul style="word-break:break-all">
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/6128735.html">
                            1. Appium+python自动化3-启动淘宝app(60)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/8628812.html">
                            2. python+requests+excel+unittest+ddt接口自动化数据驱动并生成html报告(已弃用)(26)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/9450309.html">
                            3. pytest文档8-html报告报错截图+失败重跑(21)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/9390073.html">
                            4. pytest文档5-fixture之conftest.py(19)
                        </a>
                    </li>
                    <li>
                        <a href="https://www.cnblogs.com/yoyoketang/p/6129684.html">
                            5. python笔记1-用python解决小学生数学题(18)
                        </a>
                    </li>
            <li>
                
            </li>
        </ul>
    </div>
</div></div>

<!-- 推荐排行榜 -->
<div id="sidebar_topdiggedposts" class="sidebar-block"><div id="topdigg_posts_wrap">
    <div class="catListView">
        <h3 class="catListTitle">
            <a href="https://www.cnblogs.com/yoyoketang/most-liked" class="sidebar-card-title-a">推荐排行榜</a>

        </h3>
        <div id="TopDiggPostsBlock">
            <ul style="word-break: break-all">
                        <li>
                            <a href="https://www.cnblogs.com/yoyoketang/p/10144581.html">
                                1. 关于面试总结9-接口测试面试题(34)
                            </a>
                        </li>
                        <li>
                            <a href="https://www.cnblogs.com/yoyoketang/p/6719717.html">
                                2. Fiddler抓包3-查看get与post请求(29)
                            </a>
                        </li>
                        <li>
                            <a href="https://www.cnblogs.com/yoyoketang/p/selenium.html">
                                3. Selenium2+python自动化1-环境搭建(20)
                            </a>
                        </li>
                        <li>
                            <a href="https://www.cnblogs.com/yoyoketang/p/6551274.html">
                                4. Selenium2+python自动化44-元素定位参数化（find_element）(19)
                            </a>
                        </li>
                        <li>
                            <a href="https://www.cnblogs.com/yoyoketang/p/6582437.html">
                                5. Fiddler抓包2-只抓APP的请求(18)
                            </a>
                        </li>
                <li>
                    
                </li>
            </ul>
        </div>
    </div>
</div></div><div id="sidebar_recentcomments" class="sidebar-block"><div class="catListComment">
<h3 class="catListTitle"><a href="https://www.cnblogs.com/yoyoketang/comments" class="sidebar-card-title-a">最新评论</a></h3>
    <div class="RecentCommentBlock">
        <ul>
                <li class="recent_comment_title"><a href="https://www.cnblogs.com/yoyoketang/p/15132889.html">1. Re: selenium+python自动化105 - selenium 如何在已打开的浏览器上继续运行自动化脚本？</a></li>
                <li class="recent_comment_body">@少年听雨1 火狐不可以...</li>
                <li class="recent_comment_author">--上海-悠悠</li>
                <li class="recent_comment_title"><a href="https://www.cnblogs.com/yoyoketang/p/15132889.html">2. Re: selenium+python自动化105 - selenium 如何在已打开的浏览器上继续运行自动化脚本？</a></li>
                <li class="recent_comment_body"><p>火狐可以吗</p>
</li>
                <li class="recent_comment_author">--少年听雨1</li>
                <li class="recent_comment_title"><a href="https://www.cnblogs.com/yoyoketang/p/16262638.html">3. Re:httprunner 3.x学习5 - 测试用例引用前面一个用例（call）</a></li>
                <li class="recent_comment_body"><p>太赞了</p>
</li>
                <li class="recent_comment_author">--DSD1</li>
                <li class="recent_comment_title"><a href="https://www.cnblogs.com/yoyoketang/p/8717929.html">4. Re:关于adb devices连不上手机的几种情况</a></li>
                <li class="recent_comment_body"><p>请问一下如果是黑砖是不是无法进行adb操作的哈</p>
</li>
                <li class="recent_comment_author">--胸无、墨</li>
                <li class="recent_comment_title"><a href="https://www.cnblogs.com/yoyoketang/p/12004145.html">5. Re:pytest文档29-allure-pytest(最新最全，保证能搞成功！)</a></li>
                <li class="recent_comment_body"><p>好用耶，感谢老师</p>
</li>
                <li class="recent_comment_author">--Fun0623</li>
        </ul>
    </div>
</div>

</div>


</div>
                    <script>loadBlogSideColumn();</script>
			</div>
			
		</div><!--end: sideBarMain -->
	</div><!--end: sideBar 侧边栏容器 -->
	<div class="clear"></div>
	</div><!--end: main -->
	<div class="clear"></div>
	<div id="footer">
		<!--done-->
Copyright © 2022 上海-悠悠
<br><span id="poweredby">Powered by .NET 6 on Kubernetes</span>



	</div><!--end: footer -->
</div><!--end: home 自定义的最大容器 -->


    

    <input type="hidden" id="antiforgery_token" value="CfDJ8AuMt_3FvyxIgNOR82PHE4k9968KImkQl1yRM6LMSUEaJtJzVGqxgYRkfH0EOyCafYi1vUjUWUMm4PQ2JBHXNAzxiUScloTxniB-0YdYQ3HXqVTNtGrEj1tW5TCE_X8DocvNk8XwA8uHkaozLN2Tlmw">
        <script async="" src="./test_files/js"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        var kv = getGACustom();
        if (kv) {
            gtag('set', kv);
        }
        gtag('config', 'UA-476124-1');
    </script>
<script defer="" src="./test_files/hm.js"></script><iframe src="./test_files/container.html" style="visibility: hidden; display: none;"></iframe>

<iframe src="./test_files/aframe.html" width="0" height="0" style="display: none;"></iframe></body></html>