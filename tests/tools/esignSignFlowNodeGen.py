# -*-coding:utf-8-*-
# <AUTHOR> wenmin
# @Time : 2022/5/18 14:17
# @Description : 签署中心-无节点签署 场景


import sys
from time import sleep

import requests

from tools.RandomGen import generate_random_str


# openapi无节点流程
# 无节点流程-创建
from tools.get_ca_signature import hmac_sha256_encrypt


def createSignFlow(esignSignsOpenApiUrl, projectId,projectSecrect, userCode, organizationCode, businessTypeCode):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/create"
        subject = "无节点-" + generate_random_str(5)
        data = {
            "businessNo": subject,
            "readComplete": True,
            "signFlowExpireTime": "",
            "redirectUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
            "remark": "我是备注",
            "signNotice": "我是签署需知",
            "signNotifyUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
            "subject": subject,
            "businessTypeCode": businessTypeCode,
            "initiatorInfo": {
                "organizationCode": organizationCode,
                "userCode": userCode,
                "userType": 1
            }
        }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 无节点流程-创建-带有抄送人
def createSignFlowCC(esignSignsOpenApiUrl, projectId,projectSecrect, userCode, organizationCode, businessTypeCode, CCInfos):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/create"
        subject = "无节点-带抄送-" + generate_random_str(5)
        data = {
            "businessNo": subject,
            "readComplete": True,
            "signFlowExpireTime": "",
            "redirectUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",
            "remark": "我是备注",
            "signNotice": "我是签署需知",
            "signNotifyUrl": "",
            "subject": subject,
            "businessTypeCode": businessTypeCode,
            "initiatorInfo": {
                "organizationCode": organizationCode,
                "userCode": userCode,
                "userType": 1
            }
        }
        data = {**data, **CCInfos}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 给流程添加签署文件
def addFiles(esignSignsOpenApiUrl, projectId,projectSecrect, signFlowId, fileKey):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/files/add"
        data = {
            "attachmentFiles": [
                {
                    "fileKey": fileKey,
                    "fileName": "附属文件.pdf",
                    "fileOrder": 1
                }
            ],
            "businessNo": "",
            "signFlowId": signFlowId,
            "signFiles": [
                {
                    "fileKey": fileKey,
                    "fileName": "签署文件.pdf",
                    "fileOrder": 1
                }
            ]
        }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[request]: " + str(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 给流程添加多份签署文件
def addFilesMany(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId, signFiles):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/files/add"
        data = {"businessNo": "", "signFlowId": signFlowId}
        data = {**data, **signFiles}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 添加流程签署人，并指定签署区
def addSigners(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId, fileKey, userCode, organizationCode):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/signers/add"
        signConfigs = [{"posX": 100, "posY": 100, "pageNo": "1", "width": 100, "signatureType": "PERSON-SEAL",
                        "signType": "COMMON-SIGN"}]
        if organizationCode:
            signConfigs = [{"posX": 100, "posY": 100, "pageNo": "1", "width": 100, "signatureType": "COMMON-SEAL",
                            "signType": "COMMON-SIGN"}]
        data = {
            "signerInfos": [
                {
                    "signNode": 1,
                    "signMode": 0,
                    "userCode": userCode,
                    "organizationCode": organizationCode,
                    "tspId": "LOCAL_DEFAULT_TSP",
                    "userType": 1,
                    "sealInfos": [
                        {
                            "fileKey": fileKey,
                            "signConfigs": signConfigs
                        }
                    ]
                }
            ],
            "signFlowId": signFlowId
        }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 添加流程签署人，并且静默签署成功
def addSignersAutoSign(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId, fileKey, userCode, organizationCode,sealId):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/signers/add"
        signConfigs = [{"posX": 100, "posY": 100, "pageNo": "1", "width": 100, "signatureType": "PERSON-SEAL",
                        "signType": "COMMON-SIGN","sealId":sealId}]
        if organizationCode:
            signConfigs = [{"posX": 100, "posY": 100, "pageNo": "1", "width": 100, "signatureType": "COMMON-SEAL",
                            "signType": "COMMON-SIGN","sealId":sealId}]
        data = {
            "signerInfos": [
                {
                    "signNode": 1,
                    "signMode": 0,
                    "autoSign": True,
                    "userCode": userCode,
                    "organizationCode": organizationCode,
                    "tspId": "LOCAL_DEFAULT_TSP",
                    "userType": 1,
                    "sealInfos": [
                        {
                            "fileKey": fileKey,
                            "signConfigs": signConfigs
                        }
                    ]
                }
            ],
            "signFlowId": signFlowId
        }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 开启这个无节点流程
def startSignFlow(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/start"
        data = {"signFlowId": signFlowId }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 结束这个无节点流程 （必须所有节点都签署完成之后，才能关闭）
def finishSignFlow(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId):
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/finish"
        data = {"signFlowId": signFlowId }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# <AUTHOR> hechu
# @Time : 2022/5/24 17:21
# @Description : 删除签署方（添加的不删除，不然其他用例没办法再添加）
# 删除签署方（添加的不删除，不然其他用例没办法再添加）
def signersDelete(esignSignsOpenApiUrl, projectId,projectSecrect,signFlowId, organizationCode,userCode):

    print("调用signersDelete:" + esignSignsOpenApiUrl  + ":" +projectId+ ":" +projectSecrect+":" + signFlowId + ":" + organizationCode + ":" + userCode)
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/signers/delete"
        data = {"businessNo": "","signFlowId": signFlowId ,"signerInfos": [{"organizationCode": organizationCode,"userCode": userCode}]}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}

        print("调用signersDelete:" + Url + ":" +signFlowId + ":" + organizationCode + ":" + userCode)

        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用signersDelete异常")
        return None


# 删除流程签署人,同时删除多人
def deleteSigners(esignSignsOpenApiUrl, projectId,projectSecrect,  signFlowId, signerInfos):
    '''
    删除流程签署人,同时删除多人
    :param esignSignsOpenApiUrl:
    :param projectId:
    :param projectSecrect:
    :param signFlowId:
    :param signerInfos: [{"organizationCode": "${ENV(sign01.main.orgCode)}","userCode": "${ENV(sign01.userCode)}"}]
    :return:
    '''
    try:
        Url = esignSignsOpenApiUrl + "/esign-signs/v1/signFlow/signers/delete"
        if signFlowId:
            data = {
              "businessNo": "",
              "signFlowId": signFlowId,
              "signerInfos": signerInfos
            }
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['signFlowId']
    except:
        print(sys._getframe().f_code.co_name + "-调用signersDelete异常")
        return None

if __name__ == '__main__':
    # print(createSignFlow(esignSignsOpenApiUrl,projectId,projectSecrect,userCode,organizationCode,businessTypeCode))
    # signFlowId = createSignFlowCC(esignSignsOpenApiUrl, projectId,projectSecrect, userCode, organizationCode, businessTypeCode, CCInfos)
    # print(addFiles(esignSignsOpenApiUrl, projectId,projectSecrect, signFlowId, fileKey))
    # print(addSignersAutoSign(esignSignsOpenApiUrl, projectId,projectSecrect, signFlowId, fileKey, userCode, organizationCode,sealId))
    # print(startSignFlow(esignSignsOpenApiUrl, projectId,projectSecrect, signFlowId))
    #print(finishSignFlow(esignSignsOpenApiUrl, projectId,projectSecrect, 'e53ac1c31514d028e3abec682037b80c'))
    signersDelete("http://tianyin6-stable.tsign.cn","1000000","l091H1OX8h5lwfML", "1f813be5425c2bf1e0d6dc3b7cc4799e","58bec88e506142b0bf05342c68518827","cesqswby1")
