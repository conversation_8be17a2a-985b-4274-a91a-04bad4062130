# @Time : 2022/5/19 10:55
# @Description :认证 实名 授权接口
import requests


# 内部用户签署实名回调地址
def gen_inner_notifyUrl(notifyUrl, processId, userCode, orgCode,applyId):
    notifyUrl = notifyUrl+"?userCode=" + userCode + "&processId=" + processId+"&applyId="+applyId
    if orgCode is not None and len(orgCode) > 0:
        return notifyUrl + "&type=ORGANIZE&organizeCode=" + orgCode
    else:
        return notifyUrl + "&type=PERSON"


# 获取用户/机构实名状态
# 流程id 签署人必须和登录人是同一个人
# token当前登录人
def gen_inner_realname_status(esignSignWebUrl, processId, headers, orgCode):
    url = esignSignWebUrl + "/esign-signs/seals/list"
    data = {"params": {"processId": processId, "organizeCode": orgCode}}
    res = requests.post(url, json=data, headers=headers)
    try:
        jsonData = res.json()
        if jsonData["status"] == 200:
            if len(orgCode) == 0:
                return jsonData["data"]["personalSeal"]["realNamed"]
            else:
                return jsonData["data"]["officialSeal"]["realNamed"]
        else:
            print("gen_realname_status error: " + res.text)
            return None
    except:
        print("gen_realname_status except:" + res.text)
        return None
