import requests
import json
import ddddocr
import os
# 获取token需要提前安装依赖见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》

"""
获取token需要提前执行安装依赖：pip3 install ddddocr
该依赖较大会有安装失败的风险，可参考：
http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》解决失败问题
"""


"""验证码图片地址"""
current_path = os.path.abspath(__file__)
verify_code_image_path = os.path.join(os.path.dirname(current_path), '../data/verifyCode.png')
openhost = os.environ.get('esign.gatewayHost')
host = os.environ.get('esign.projectHost')

def get_config(config_key):
    """

    :param config_key: json文件中的key
    :return: key对应的value
    """
    current_path = os.path.abspath(__file__)
    config_file_path = os.path.join(os.path.dirname(current_path), '../config')
    with open(config_file_path, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)
    return config_dict[config_key]

def get_verificationCode():
    while True:
        response = requests.get(url=host + '/manage/login/verifyCode')
        # with open(verify_code_image_path, 'wb') as f:
        #     f.write(response.content)
        # with open(verify_code_image_path, 'rb') as fp:
        #         fp.read()
        vertification_code_header = response.headers['verificationCode']
        # verification_code = get_verification_code()
        verification_code = '9999'
        print(verification_code)
        if len(verification_code) == 4 and verification_code.isdigit():
            return verification_code, vertification_code_header


def get_verification_code():
    # ocr = ddddocr.DdddOcr()
    # with open(verify_code_image_path, 'rb') as f:
    #     img_bytes = f.read()
    # verification_code = ocr.classification(img_bytes)
    verification_code = '9999'
    return verification_code


# 获取管理平台的token
def get_manage_token():
    verification_code, vertification_code_header = get_verificationCode()
    data = {
        "params": {"userCode": "admin",
                   "password": "HSnxJw51rcmM9HwzmPaEPXQEuxg4oJuWtfGU9UzEjAm8sHAeocNntoG4R9u+/GufuGv6j1VH1zt7GU68QCwOKcuZ4Sdb0rS5koG0BSh+aEzEzFyzSFRuHwMTKS6u7RrmuUmcUftG7fnQQdQXolLQBzc+8zUgJblzZ7txiM+OXR1s7pR2XNdk+o3HWnDD+Vqy20oJsdZvieJKW5L+BnIgCgMfE1PXEtJOJeI1x5PrZ2l/DBrzQbs01gjPP7YjoxiEBPLaKTtySqUiNn2nqbdHGklxqWG4JSBX4PivdOPUsdj+FsM+GCpVKZAzElh8vQGMP29+xaPAtu1GA9SewKjVnw==",
                   "verificationCode": "9999",
                   "vertificationCodeHeader": vertification_code_header, "rememberMe": False,
                   "tenantCode": 1000}, "domain": "admin_platform"}

    response = requests.post(url=host + '/manage/login/toLogin', json=data,
                             headers={"Content-Type": "application/json;charset=UTF-8",
                                      "tenantCode": "vnyqCnpMi3sLP39RVU85ww==",
                                      "verificationCode": vertification_code_header})
    result = json.loads(response.text)
    print(result)
    if result['message'] == '成功' or result['message'] == '数据正常' or result['message'] == '成功':
        return result['data']['token']
    else:
        return get_manage_token()


token = get_manage_token()

if __name__ == '__main__':
    print(get_verificationCode())
