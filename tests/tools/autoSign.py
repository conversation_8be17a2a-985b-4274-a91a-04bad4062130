#静默签
import os
import debugtalk
from utils import ENV

user_code_signer = ENV('sign01.userCode')
pdf_filekey = ENV('fileKey')
sign01_main_orgCode = ENV('sign01.main.orgCode')
password_encrypt = ENV('passwordEncrypt')
wsignwb01_main_orgNo = ENV('wsignwb01.main.orgNo')
wsignwb01_userCode = ENV('wsignwb01.userCode')
wsignwb01_saasId = ENV('wsignwb01.saasId')
sign01_sealId = ENV('sign01.sealId')
org01_sealId = ENV('org01.sealId')
wsignwb01_sealId = ENV('wsignwb01.sealId')

# 组装多个签署人
def get_signerConfig(arry):
    signConfigs = []
    for i in range(len(arry)):
        itemArry = arry[i]
        for x in range(len(itemArry)):
            item = itemArry[x]
            # userCode-organizeCode-signMode-autoSign-sealId
            userCode = item[0]
            organizeCode = item[1]
            signMode = item[2]
            autoSign = item[3]
            sealId = item[4]

            config = {"sealInfos": [
                {"fileKey": "pdf_filekey",
                 "signConfigs": [
                     {
                         "posX": "200",
                         "posY": "200",
                         "sealId": sealId,
                         "pageNo": "1",
                         "signType": "COMMON-SIGN",
                         "signatureType": "PERSON-SEAL",
                         "addSignDate": True,
                         "sealSignDatePositionInfo": {
                             "fontSize": "30",
                             "posX": "400",
                             "posY": "400",
                             "sealSignDateFormat": 1
                         }
                     }
                 ]
                 }
            ],
                "signNode": (i+1),
                "userType": 1,
                "tspId": "LOCAL_DEFAULT_TSP",
                "autoSign": autoSign,
                "signMode": signMode,
                "userCode": userCode,
                "organizationCode": organizeCode
            }
            signConfigs.append(config)
    return signConfigs


#动态组装一步发起人员信息 弊端无法配置同一签署人不同签署配置
def get_signerInfo(fileArry,arry):
    print("----->"+str(fileArry))
    signConfigs = []
    for x in range(len(arry)):
        item = arry[x]
        itemStr = str(item)

        paramArry = signer_request(item, itemStr, x)

        userCode = paramArry[0]

        organizeCode = paramArry[1]

        signMode = paramArry[2]

        autoSign = paramArry[3]

        sealId = paramArry[4]

        posX = paramArry[5]

        posY = paramArry[6]

        pageNo = paramArry[7]

        signType = paramArry[8]

        signatureType = paramArry[9]

        addSignDate = paramArry[10]

        sealSignDateFormat = paramArry[11]

        userType = paramArry[12]

        lenConfig = paramArry[13]

        edgeScope = paramArry[14]

        keyArry = paramArry[15]

        configArry = []

        print("len:"+str(lenConfig))
        if lenConfig > 1:
            for y in range(lenConfig):

                posXChild = 200 + y * 100
                if itemStr.find("posX") != -1:
                    posXChild = item["posX"]

                posYChild = 200 + y * 100
                if itemStr.find("posY") != -1:
                    posYChild = item["posY"]

                config = {
                        "posX": posXChild,
                        "posY": posYChild,
                        "sealId": sealId,
                        "pageNo": pageNo,
                        "signType": signType,
                        "signatureType": signatureType,
                        "addSignDate": addSignDate,
                        "edgeScope": edgeScope,
                        "sealSignDatePositionInfo": {
                            "fontSize": "30",
                            "posX": "400",
                            "posY": "400",
                            "sealSignDateFormat": sealSignDateFormat
                        },
                        "keywordInfo": keyword_request(signType,keyArry,y)
                    }
                configArry.append(config)
        else:
            config = {
                "posX": posX,
                "posY": posY,
                "sealId": sealId,
                "pageNo": pageNo,
                "signType": signType,
                "signatureType": signatureType,
                "addSignDate": addSignDate,
                "edgeScope": edgeScope,
                "sealSignDatePositionInfo": {
                    "fontSize": "30",
                    "posX": "400",
                    "posY": "400",
                    "sealSignDateFormat": sealSignDateFormat
                },
                "keywordInfo": keyword_request(signType,keyArry,0)
            }

            configArry.append(config)

        sealInfos = []
        for i in range(len(fileArry)):
            fileKey = fileArry[i]["fileKey"]
            sealInfo = {
                "fileKey": fileKey,
                "signConfigs": configArry
            }
            sealInfos.append(sealInfo)

        tsp0 = "LOCAL_DEFAULT_TSP"
        if userType ==2:
            tsp0 = "ESIGN_WITNESS_TSP"
        signerConfig = {
            "sealInfos": sealInfos,
            "signNode": (i+1),
            "userType": userType,
            "tspId": tsp0,
            "autoSign": autoSign,
            "signMode": signMode,
            "userCode": userCode,
            "organizationCode": organizeCode,
            "departmentCode": organizeCode
        }
        signConfigs.append(signerConfig)
    return signConfigs

#动态组装一步发起入参 动态接收
def get_signerInfo_free_config(fileArry,arry):
    signConfigs = []
    for x in range(len(arry)):
        item = arry[x]
        itemStr = str(item)

        paramArry = signer_request(item,itemStr,x)

        userCode = paramArry[0]

        organizeCode = paramArry[1]

        signMode = paramArry[2]

        autoSign = paramArry[3]

        sealId = paramArry[4]

        posX = paramArry[5]

        posY = paramArry[6]

        pageNo = paramArry[7]

        signType = paramArry[8]

        signatureType = paramArry[9]

        addSignDate = paramArry[10]

        sealSignDateFormat = paramArry[11]

        userType = paramArry[12]

        edgeScope = paramArry[14]

        keyArry = paramArry[15]

        configArry = []

        signerConfig = paramArry[16]

        legalSignFlag = paramArry[17]

        if len(signerConfig) > 0:
            for y in range(len(signerConfig)):

                configItem = signerConfig[y]
                configItemStr = str(configItem)

                if configItemStr.find("childSignType") != -1:
                    signType = configItem["childSignType"]

                if configItemStr.find("childSignatureType") != -1:
                    signatureType = configItem["childSignatureType"]

                if configItemStr.find("childSealId") != -1:
                    sealId = configItem["childSealId"]

                posXChild = 200 + y * 100
                if itemStr.find("posX") != -1:
                    posXChild = item["posX"]

                posYChild = 200 + y * 100
                if itemStr.find("posY") != -1:
                    posYChild = item["posY"]
                config = {
                    "posX": posXChild,
                    "posY": posYChild,
                    "sealId": sealId,
                    "pageNo": pageNo,
                    "signType": signType,
                    "signatureType": signatureType,
                    "addSignDate": addSignDate,
                    "edgeScope": edgeScope,
                    "sealSignDatePositionInfo": {
                        "fontSize": "30",
                        "posX": "400",
                        "posY": "400",
                        "sealSignDateFormat": sealSignDateFormat
                    },
                    "keywordInfo": keyword_request(signType,keyArry,y)
                }
                configArry.append(config)
        else:
            config = {
                "posX": posX,
                "posY": posY,
                "sealId": sealId,
                "pageNo": pageNo,
                "signType": signType,
                "signatureType": signatureType,
                "addSignDate": addSignDate,
                "edgeScope": edgeScope,
                "sealSignDatePositionInfo": {
                    "fontSize": "30",
                    "posX": "400",
                    "posY": "400",
                    "sealSignDateFormat": sealSignDateFormat
                },
                "keywordInfo": keyword_request(signType,keyArry,0)
            }
            configArry.append(config)

        sealInfos = []
        for i in range(len(fileArry)):
            fileKey = fileArry[i]["fileKey"]
            sealInfo = {
                "fileKey": fileKey,
                "signConfigs": configArry
            }
            sealInfos.append(sealInfo)

        signerConfig = {
            "sealInfos": sealInfos,
            "signNode": (i + 1),
            "userType": userType,
            "autoSign": autoSign,
            "signMode": signMode,
            "userCode": userCode,
            "legalSignFlag":legalSignFlag,
            "organizationCode": organizeCode,
            "departmentCode": organizeCode
        }
        signConfigs.append(signerConfig)
    return signConfigs

# 签署人签署区配置封装
def signer_request(item , itemStr , index):
    arry =[]
    userCode = user_code_signer
    if itemStr.find("userCode") != -1:
        userCode = item["userCode"]
    arry.append(userCode)

    organizeCode = sign01_main_orgCode
    if itemStr.find("organizeCode") != -1:
        organizeCode = item["organizeCode"]
    arry.append(organizeCode)

    signMode = 0
    if itemStr.find("signMode") != -1:
        signMode = item["signMode"]
    arry.append(signMode)

    autoSign = True
    if itemStr.find("autoSign") != -1:
        autoSign = item[3]
    arry.append(autoSign)

    sealId = org01_sealId
    if itemStr.find("sealId") != -1:
        sealId = item["sealId"]
    arry.append(sealId)

    posX = 200 + index * 100
    if itemStr.find("posX") != -1:
        posX = item["posX"]
    arry.append(posX)

    posY = 200 + index * 100
    if itemStr.find("posY") != -1:
        posY = item["posY"]
    arry.append(posY)

    pageNo = "1"
    if itemStr.find("pageNo") != -1:
        pageNo = item["pageNo"]
    arry.append(pageNo)

    signType = "COMMON-SIGN"
    if itemStr.find("signType") != -1:
        signType = item["signType"]
    arry.append(signType)

    signatureType = "COMMON-SEAL"
    if itemStr.find("signatureType") != -1:
        signatureType = item["signatureType"]
    arry.append(signatureType)

    addSignDate = False
    if itemStr.find("addSignDate") != -1:
        addSignDate = item["addSignDate"]
    arry.append(addSignDate)

    sealSignDateFormat = 1
    if itemStr.find("sealSignDateFormat") != -1:
        sealSignDateFormat = item["sealSignDateFormat"]
    arry.append(sealSignDateFormat)

    userType = 1
    item['tspId'] = "LOCAL_DEFAULT_TSP"
    if itemStr.find("userType") != -1:
        userType = item["userType"]
    arry.append(userType)

    lenConfig = 1
    if itemStr.find("lenConfig") != -1:
        lenConfig = item["lenConfig"]
    keyArry = []
    if itemStr.find("keyArry") != -1:
        keyArry = item["keyArry"]
    if signType == 'KEYWORD-SIGN':
        lenConfig = len(keyArry)
    arry.append(lenConfig)

    edgeScope = 0
    if itemStr.find("edgeScope") != -1:
        edgeScope = item["edgeScope"]
    arry.append(edgeScope)

    arry.append(keyArry)

    signerConfig = []
    if itemStr.find("signerConfig") != -1:
        signerConfig = item["signerConfig"]
    arry.append(signerConfig)

    legalSignFlag = False
    if itemStr.find("legalSignFlag") != -1:
        legalSignFlag = item["legalSignFlag"]
    arry.append(legalSignFlag)

    return arry

# 关键字参数封装
def keyword_request(signType,keyArry,y):
    keyInfo = {}
    if signType == 'KEYWORD-SIGN':
        keyItem = keyArry[y]
        keyItemStr = str(keyItem)

        offsetPosX = 0
        if keyItemStr.find("offsetPosX") != -1:
            offsetPosX = keyItem["offsetPosX"]

        offsetPosY = 0
        if keyItemStr.find("offsetPosY") != -1:
            offsetPosY = keyItem["offsetPosY"]

        keyword = ''
        if keyItemStr.find("keyword") != -1:
            keyword = keyItem["keyword"]

        keywordIndex = 0
        if keyItemStr.find("keywordIndex") != -1:
            keywordIndex = keyItem["keywordIndex"]

        keyInfo = {
            "offsetPosX": offsetPosX,
            "offsetPosY": offsetPosY,
            "keyword": keyword,
            "keywordIndex": keywordIndex
        }

    return keyInfo




# 一步发起关键字入参组装
def keyRequest(keys):
    signConfigs = []
    for i in range(len(keys)):
        config = {
            "posX": "200",
            "posY": "200",
            "sealId": sign01_sealId,
            "pageNo": "",
            "signType": "KEYWORD-SIGN",
            "signatureType": "PERSON-SEAL",
            "addSignDate": True,
            "sealSignDatePositionInfo": {
                "fontSize": "30",
                "posX": "400",
                "posY": "400",
                "sealSignDateFormat": 1
                },
            "keywordInfo": {
                "keyword": keys[i],
                "keywordIndex": "-1",
                "offsetPosX": "10",
                "offsetPosY": "10"
            }
        }
        signConfigs.append(config)
    return  signConfigs

# 验证当前签署人是否已签署 入参是一步发起返回
def check_process_complete(processId):
    print("check_process_complete-----data: " + str(processId))
    result = "0"
    for i in range(10):
        debugtalk.sleep(1)
        signFlowStatus = debugtalk.get_signFlowDetail_signFlowStatus(processId)
        print("signFlowStatus: " + str(processId) + "======"+ str(signFlowStatus))
        if signFlowStatus == 2:
            result = "1"
            break
    return result

def check_process_complete(processId):
    # data = json.loads(response.text)["data"]
    # print("data: " + str(data))
    # processId = data["signFlowId"]
    print("data: " + str(processId))
    result = "0"
    for i in range(20):
        debugtalk.sleep(1)
        signFlowStatus = debugtalk.get_signFlowDetail_signFlowStatus(processId)
        print("signFlowStatus: " + str(signFlowStatus))
        if signFlowStatus == 2:
            result = "1"
            break
    return result

# 校验多节点是否签署完成 并触发签署
# executeArry 数组顺序表示执行顺序，
# 0 表示停顿10s 1表示内部个人 2表示内部机构
# 3 表示发起人个人 4 表示发起人机构
# 5 外部个人  6外部机构
def more_node_check_process_complete(processId,executeArry):
    print("------多节点====" + str(processId) + "------" + str(executeArry))
    for i in range(len(executeArry)):
        type = executeArry[i]
        if type == 0:
            print("more_node_check_process_complete---------type=0")
            debugtalk.sleep(3)
        if type == 1:
            print("more_node_check_process_complete---------type=1")
            internal_sign(processId,0,False,0)
        if type == 2:
            print("more_node_check_process_complete---------type=2")
            internal_sign(processId,0,True,1)
        if type == 3:
            print("more_node_check_process_complete---------type=3")
            internal_sign(processId,2,False,0)
        if type == 4:
            print("more_node_check_process_complete---------type=4")
            internal_sign(processId,2,True,2)
        if type == 5:
            print("more_node_check_process_complete---------type=5")
            outer_sign(processId,0)
        if type == 6:
            print("more_node_check_process_complete---------type=6")
            outer_sign(processId,1)

        debugtalk.sleep(5)



    return check_process_complete(processId)


# 内部签  singerIndex != 2表示
# isorganize 是否机构 true/false
# orgindex 1签署人 2发起人
# singerIndex  2发起人 否者签署人
def internal_sign(processId,singerIndex,isOrganize,orgindex):
    try:
        print("------内部开始签署===="+str(processId)+"------"+str(singerIndex)+"---------"+str(isOrganize))
        accountCode = debugtalk.get_sign_UserCode(1,singerIndex)
        print("accountCode-------->"+str(accountCode))
        headers3 = debugtalk.get_headers_with_accountCode(accountCode)
        willReturn = debugtalk.willing(processId, headers3, isOrganize,orgindex)
        print("willRreturn-------->"+str(willReturn))
        # 验证密码的token解析和组装gen_standardSign_data
        from utils.esignToken import parseToken
        token = parseToken(willReturn['url'])
        print("willRreturn2-------->" + str(token))
        applyId = willReturn["applyId"]
        obj0 = debugtalk.password_auth(applyId, password_encrypt, token)
        print('签署方短信意愿认证结果=====:', accountCode,"========",obj0)
        data = debugtalk.gen_standardSign_data(isOrganize, applyId, processId,0,singerIndex,1)
        print("------内部签署执行" + str(data))
        obj1 = debugtalk.standard_flow_sign(data, headers3)
        print('签署执行结果=====:', accountCode,"========",obj1)

    except:
        print("------内部签署失败")

# 外部签署
def outer_sign(processId,isOrganize):
    print("------外部开始签署====" + str(processId) + "---------" + str(isOrganize))
    organizeCode = ""
    userCode = wsignwb01_userCode
    saasId = debugtalk.get_out_user_sassId(userCode)
    sealId = wsignwb01_sealId
    headers3 = debugtalk.get_sign_headers(2, 1)

    if isOrganize == 1:
        organizeCode = ENV('worg01.orgCode')
        sealId = ENV('worg01.sealId')
        saasId = debugtalk.get_out_org_sassId(organizeCode)

    willReturn = debugtalk.willing_out(processId, headers3, organizeCode)
    print("willRreturn-------->" + str(willReturn))
    applyId = willReturn["applyId"]
    # 请求公有云并选择验证码校验
    createCodeAuthRes = debugtalk.createCodeAuth(saasId, applyId)
    # 请求公有云校验短信验证码
    debugtalk.verifyCodeAuth(createCodeAuthRes['bizId'], createCodeAuthRes['willAuthId'])


    data = debugtalk.gen_standardSign_data(isOrganize, applyId, processId, 0, 1, 2)
    print("data------->"+str(data))
    debugtalk.standard_flow_sign(data, headers3)

# 多节点参数组装
def get_more_signer_node(fileArry,signerArry):
    result = []
    for i in range(len(signerArry)):
        signerItme = signerArry[i]
        signerItmeStr = str(signerItme)

        signConfigs = []
        if signerItmeStr.find("signConfigs") != -1:
            signConfigs = signerItme["signConfigs"]

        sealInfos = []
        for f in range(len(fileArry)):
            fileKey = fileArry[f]["fileKey"]
            configs = []
            if len(signConfigs) > 0:
                for x in range(len(signConfigs)):
                    configs.append(get_signer_config(signConfigs[x],x))

            sealInfo = {
                "fileKey": fileKey,
                "signConfigs": configs
            }

            sealInfos.append(sealInfo)


        baseInfo = get_base_signer_info(signerItme,signerItmeStr)
        baseInfo["sealInfos"] = sealInfos

        result.append(baseInfo)
    return result

# 获取签署人签署区配置
def get_signer_config(configItem,x):
    configItemStr = str(configItem)
    paramArry = signer_request(configItem, configItemStr, x)
    sealId = paramArry[4]
    posX = paramArry[5]
    posY = paramArry[6]
    pageNo = paramArry[7]
    signType = paramArry[8]
    signatureType = paramArry[9]
    addSignDate = paramArry[10]
    sealSignDateFormat = paramArry[11]
    edgeScope = paramArry[14]
    keyArry = paramArry[15]

    if configItemStr.find("childSealId") != -1:
        sealId = configItem["childSealId"]

    config = {
        "addSignDate": addSignDate,
        "edgeScope": edgeScope,
        "handEnable": False,
        "pageNo": pageNo,
        "posX": posX,
        "posY": posY,
        "sealId": sealId,
        "sealSignDatePositionInfo": {
            "fontSize": "",
            "posX": "",
            "posY": "",
            "sealSignDateFormat": sealSignDateFormat
        },
        "keywordInfo": keyword_request(signType, keyArry, 0),
        "signType": signType,
        "signatureType": signatureType,
        "width": 100
    }
    return config


#签署人基础信息
def get_base_signer_info(signerItme,signerItmeStr):

    autoSign = True
    if signerItmeStr.find("autoSign") != -1:
        autoSign = signerItme["autoSign"]

    legalSignFlag = False
    if signerItmeStr.find("legalSignFlag") != -1:
        legalSignFlag = signerItme["legalSignFlag"]

    organizationCode = sign01_main_orgCode
    if signerItmeStr.find("organizeCode") != -1:
        organizationCode = signerItme["organizeCode"]

    departmentCode = organizationCode
    if signerItmeStr.find("departmentCode") != -1:
        departmentCode = signerItme["departmentCode"]

    sealTypeCode = ""
    if signerItmeStr.find("sealTypeCode") != -1:
        sealTypeCode = signerItme["sealTypeCode"]

    signMode = 0
    if signerItmeStr.find("signMode") != -1:
        signMode = signerItme["signMode"]

    signNode = 1
    if signerItmeStr.find("signNode") != -1:
        signNode = signerItme["signNode"]

    userCode = user_code_signer
    if signerItmeStr.find("userCode") != -1:
        userCode = signerItme["userCode"]

    userType = 1
    tsp0 = "LOCAL_DEFAULT_TSP"
    if signerItmeStr.find("userType") != -1:
        userType = signerItme["userType"]
        tsp0 = "ESIGN_WITNESS_TSP"

    baseInfo = {
      "autoSign": autoSign,
      # "departmentCode": departmentCode,
      "legalSignFlag": legalSignFlag,
      "organizationCode": organizationCode,
      "sealTypeCode": sealTypeCode,
      "signMode": signMode,
      "signNode": signNode,
      "ukeySign": False,
      "tspId": tsp0,
      "userCode": userCode,
      "userType": userType,
      "sealInfos": []
    }
    return baseInfo

#外部签
def out_sign(processId):
    try:
        signtureParams = {
            "sealId": ENV('wsignwb01.sealId'),
            "sealTypeCode": None,
            "organizeCode": None,
            "accountSaasId": ENV('wsignwb01.saasId'),
            "userCode": ENV('wsignwb02.approve.userCode')
        }
        headers = debugtalk.get_headers_with_outer_person(ENV('wsignwb02.approve.userCode'))
        debugtalk.sign_wrap_outer_account(processId, signtureParams, headers)
    except:
        print("外部签署失败了")





# 静默签统一始配
def autoAdapter(type,arry):
    print("==========>"+str(type))
    if type == 1:
        return check_process_complete(arry[0])
    if type == 2:
        return keyRequest(arry[0])
    if type == 3:
        return get_signerInfo(arry[0],arry[1])
    if type == 4:
        return get_signerInfo_free_config(arry[0],arry[1])
    if type == 5:
        return get_more_signer_node(arry[0],arry[1])
    if type == 6:
        return get_more_signer_node(arry[0],arry[1])
    if type == 7:
        return more_node_check_process_complete(arry[0],arry[1]);