# import requests
# import json
# import ddddocr
# # 获取token需要提前安装依赖见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》
#
# """
# 获取token需要提前执行安装依赖：pip3 install ddddocr
# 该依赖较大会有安装失败的风险，可参考：
# http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》解决失败问题
# """
#
# # 管理平台接口
# verify_code_image_path = 'verifyCode.png'
#
#
# def get_verificationCode(esignSignsWebUrl):
#     response = requests.get(url=esignSignsWebUrl + '/manage/login/verifyCode')
#     with open(verify_code_image_path, 'wb') as f:
#             f.write(response.content)
#     # with open(verify_code_image_path, 'rb') as fp:
#     #         fp.read()
#     vertification_code_header = response.headers['verificationCode']
#     verification_code = get_verification_code()
#     if len(verification_code) == 4 and verification_code.isdigit():
#         return verification_code, vertification_code_header
#
# def get_verification_code():
#     ocr = ddddocr.DdddOcr()
#     with open(verify_code_image_path, 'rb') as f:
#         img_bytes = f.read()
#     verification_code = ocr.classification(img_bytes)
#     return verification_code
#
# #获取管理平台的token
#
# def get_token(esignSignsWebUrl):
#     verification_code, vertification_code_header = get_verificationCode(esignSignsWebUrl)
#     data = {"params": {"userCode": "admin", "password": "5uTLXaSD4BEB4KsOgem84w==", "verificationCode": verification_code,
#                        "vertificationCodeHeader": vertification_code_header, "rememberMe": False,
#                        "tenantCode": 1000}, "domain": "admin_platform"}
#     response = requests.post(url=esignSignsWebUrl + '/manage/login/toLogin', json=data,
#                              headers={"Content-Type": "application/json;charset=UTF-8",
#                                       "tenantCode": "vnyqCnpMi3sLP39RVU85ww==",
#                                       "verificationCode": vertification_code_header})
#     result = json.loads(response.text)
#     if result['message'] == "成功":
#         return result['data']
#     else:
#         return get_token()
#
#
# # token = get_token("http://172.20.22.23:8122")
#
# if __name__ == '__main__':
#     print()
