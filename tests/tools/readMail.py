import imaplib
import email
from email.parser import Parser
from email.header import decode_header
from email.utils import parseaddr
from bs4 import BeautifulSoup
import re

'''
需安装依赖：
pip install imaplib
pip install email
pip install beautifulsoup4

'''

def get_mail(email_address, password):
    # 这里的服务器根据需要选择
    server = imaplib.IMAP4_SSL("imap.tsign.cn","993")
    server.login(email_address, password)
    # 邮箱中的文件夹，默认为'INBOX'
    inbox = server.select("INBOX")
    # 搜索匹配的邮件，第一个参数是字符集，None默认就是ASCII编码，第二个参数是查询条件，这里的ALL就是查找全部
    type, data = server.search(None, "ALL")
    # 邮件列表,使用空格分割得到邮件索引
    msgList = data[0].split()
    # 最新邮件，第0封邮件为最早的一封邮件
    latest = msgList[len(msgList) - 1]
    type, datas = server.fetch(latest, '(RFC822)')
    # 使用utf-8解码
    text = datas[0][1].decode('utf8')
    # 转化为email.message对象
    message = email.message_from_string(text)
    return message

def decode_str(s):
    value, charset = decode_header(s)[0]
    if charset:
        value = value.decode(charset)
    return value

def guess_charset(msg):
    charset = msg.get_charset()
    if charset is None:
        content_type = msg.get('Content-Type', '').lower()
        pos = content_type.find('charset=')
        if pos >= 0:
            # 去掉尾部不代表编码的字段
            charset = content_type[pos + 8:].strip('; format=flowed; delsp=yes')
    return charset

# 使用全局变量来保存邮件内容
mail_content = '\n'
# indent用于缩进显示:
def print_info(msg, indent=0):
    global mail_content
    if indent == 0:
        for header in ['From', 'To', 'Subject']:
            value = msg.get(header, '')
            if value:
                if header == 'Subject':
                    value = decode_str(value)
                else:
                    hdr, addr = parseaddr(value)
                    name = decode_str(hdr)
                    value = u'%s <%s>' % (name, addr)
            mail_content += '%s%s: %s' % ('  ' * indent, header, value) + '\n'
    parts = msg.get_payload()
    for n, part in enumerate(parts):
        content_type = part.get_content_type()
        # if content_type == 'text/plain' or content_type == 'text/html':
        if  content_type == 'text/html' and value=='设置新密码':
            content = part.get_payload(decode=True)
            # charset = guess_charset(msg)
            charset = 'utf-8'
            if charset:
                content = content.decode(charset)
            soup = BeautifulSoup(content, 'html.parser')
            mail_content = str(re.findall('：.*，',soup.text)[0])[1:-1]
            # mail_content += '%sText:\n %s' % (' ' * indent, content)
        else:
            # 这里没有读取非text/plain类型的内容，只是读取了其格式，一般为text/html
            # mail_content += '%sAttachment: %s' % ('  ' * indent, content_type)
            return False
    return mail_content

if __name__ == '__main__':
    email_addr = "<EMAIL>"
    password = "x"
    test = print_info(get_mail(email_addr, password))
    print("用户密码是: %s" % test)
