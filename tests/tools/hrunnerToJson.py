#!/usr/bin/env python
# -- coding: utf-8 --
# <AUTHOR> gan
# @File : hrunnerToJson.py
import json


def toJson(stra):
    cont = stra.replace("'",'"').replace("False","false").replace("True","true").replace("\n","").replace("None","null")
    jsonD = json.loads(cont)
    res = json.dumps(jsonD, sort_keys=True, indent=4, separators=(', ', ': '),ensure_ascii=False)
    print(res)
if __name__ == '__main__':
    inr = """{'code': 200, 'message': "成功", 'data': {'signFlowId': 'be4d040f98ad03cc3669d6543bb81595', 'businessNo': '我是编码', 'signFlowStatus': 0, 'signFlowCreateTime': None, 'signFlowEndTime': None, 'subject': '我是流程主题啊I am subject', 'businessTypeCode': None, 'remark': '我是备注', 'signNotifyUrl': 'http://192.168.22.90:777/test/testCallBack', 'redirectUrl': 'www.baidu.com', 'signFlowExpireTime': '2022-12-12 12:12:12', 'signFileFormat': 'pdf', 'willTypes': None, 'initiatorInfo': {'userCode': 'ceswme1', 'userType': 1, 'userName': '测试文敏二', 'organizationCode': '17d63daece0046e28947824745658a51'}, 'signFiles': None, 'attachments': None, 'signerInfos': None, 'CCInfos': None}}

"""
    toJson(inr)