# -*-coding:utf-8-*-
# <AUTHOR> wenmin
# @Description : 生成用户或机构相关的证书和印章的公共方法
import sys
import requests
from tools.RandomGen import generate_random_str


def getPersonSealId(esignSealWebUrl, headers, userCode, userName, organizationCode, organizationName):
    try:
        sealId = getPersonSealIdOnly(esignSealWebUrl, headers, userCode)
        if sealId==None:
            sealId = createPersonSealId(esignSealWebUrl, headers, userCode, userName, organizationCode,
                                          organizationName)
        return sealId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 获取内部个人有效的印章Id
def getPersonSealIdOnly(esignSealWebUrl, headers, userCode):
    try:
        if userCode:
            Url = esignSealWebUrl + "/seals/smc/seals/personal/pagePersonalSealList"
            data = {"params": {"currPage": "1", "pageSize": "10", "sealName": "", "sealStatus": "g"},
                    "domain": "seal_system"}
            res = requests.post(Url, json=data, headers=headers)
            print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
            if res.json()['data']:
                # 判定接口调用成功
                list = res.json()['data']['list']
                return list[0]['id']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 生成个人印章预览图
def previewPersonalSeal(esignSealWebUrl, headers, userCode, userName):
    try:
        if userCode:
            Url = esignSealWebUrl + "/seals/smc/seals/personal/previewPersonalSeal"
            data = {"params": {"sealColour": "4", "sealShape": "1", "sealUserName": userName, "sealOpacity": 0.6,
                               "stampRule": "0", "oldStyle": None, "sealHeight": "20", "sealWidth": "20"},
                    "domain": "seal_system"}
            res = requests.post(Url, json=data, headers=headers)
            print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
            if res.json()['data']:
                # 判定接口调用成功
                return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 创建内部个人印章
def createPersonSealId(esignSealWebUrl, headers, userCode, userName, organizationCode, organizationName):
    try:
        if userCode:
            Url = esignSealWebUrl + "/seals/smc/seals/personal/saveOrUpdatePersonalSeal"
            sealThumbnailUrl = previewPersonalSeal(esignSealWebUrl, headers, userCode, userName)
            sealDesc = '自动化测试生成的印章'
            data = {"params": {"id": "", "managerOrgCode": organizationCode, "managerOrgName": organizationName,
                               "sealAngle": "0", "sealColour": "4", "sealDefinition": "20", "sealDesc": sealDesc,
                               "sealHeight": "10", "sealName": '自建印章'+generate_random_str(10), "sealBodyStructure": "1", "sealOpacity": "0.6",
                               "sealScale": "0.5", "sealShape": "1", "sealSource": "2", "sealStatus": "g",
                               "sealThumbnailUrl": sealThumbnailUrl, "sealUserName": userName, "sealWidth": "20",
                               "ownerCode": "ceswm", "ownerName": userName, "ownerOrganizationName": organizationName,
                               "ownerOrganizationCode": organizationCode, "customSealType": "", "handPaintedSealId": "",
                               "stampRule": "0", "oldStyle": None}, "domain": "seal_system"}
            res = requests.post(Url, json=data, headers=headers)
            print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
            if res.json()['message']:
                # 判定接口调用成功
                return res.json()['message']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 通过sealId获取印章的详情
def getEnterpriseSealDetail(esignSignsWebUrl, headers, sealId,sealPattern=None):
    try:
        # Url = esignSignsWebUrl + "/seals/smc/seals/enterprise/electronic/getSealDetailsById"
        # data = {"params": {"id": sealId}, "domain": "seal_system"}
        # businessRes = requests.post(Url, json=data, headers=headers)
        # if businessRes.json()['data']:
        #     # 判定接口调用成功
        #     return businessRes.json()['data']

        ### 查询更换为openapi
        # 获取内部个人有效的印章Id，若无新增
        from utils.esign6Seals import listOrganizationSeals
        obj0 = listOrganizationSeals("", "", None, None, sealId)
        if obj0.get('data').get('total') >0:
            return obj0.get('data').get('records')[0]

    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 通过sealId获取印章的类型
def getSealTypeCode(esignSignsWebUrl, headers, sealId):
    try:
        result = getEnterpriseSealDetail(esignSignsWebUrl, headers, sealId)
        if result:
            # 判定接口调用成功
            # return result['sealTypeCode']
            return result.get('sealTypeCode')
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 获取内部用户的正常状态的证书
def getPersonCertIdOnly(esignSignsWebUrl, headers, userCode):
    try:
        Url = esignSignsWebUrl + "/seals/smc/certs/personal/getPersonalCertList"
        data = {"params": {"currPage": "1", "pageSize": "10", "certName": "", "certType": "1", "certAlgorithm": "",
                           "userName": "", "certStatus": "1"}, "domain": "seal_system"}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            certList = res.json()['data']['list']
            return certList[0]['id']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 通过内部个人查询个人有效证书，如果有则返回certId,如果没有这新建一本有效证书
def getPersonCertId(esignSignsWebUrl, headers, userCode, licenseNumber, certName, userName):
    try:
        certId = getPersonCertIdOnly(esignSignsWebUrl, headers, userCode)
        if certId == None:
            certId = createPersonCertId(esignSignsWebUrl, headers, userCode, licenseNumber, certName, userName)
        return certId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 创建个人证书
def createPersonCertId(esignSignsWebUrl, headers, userCode, licenseNumber, certName, userName):
    try:
        Url = esignSignsWebUrl + "/seals/smc/certs/personal/savePersonalCert"
        data = {"params": {"licenseNumber": licenseNumber, "licenseType": "IDCard", "certAlgorithm": "1",
                           "applyMethod": "1", "certName": certName, "certType": "1", "userCode": userCode,
                           "userName": userName}, "domain": "seal_system"}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            certList = res.json()['data']['list']
            return certList[0]['id']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

if __name__ == '__main__':
    esignSignsWebUrl = 'http://tianyin6-stable.tsign.cn'
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJlc2lnbl9sb2dpbiIsIm9yZ2FuaXphdGlvbk5hbWUiOiJlc2lnbnRlc3TmlofmlY_mtYvor5XkvIHkuJowMDEiLCJ0ZW5hbnRDb2RlIjoiMTAwMCIsInVzZXJOYW1lIjoi5rWL6K-V5paH5pWPIiwidXNlcklkIjoiMTUyNjM5ODc3OTk1MjEzNjE5NCIsInVzZXJDb2RlIjoiY2Vzd20iLCJwbGF0Zm9ybSI6InBjIiwiZXhwaXJlZFRpbWUiOjI0MTAyMjU1NDgsIm9yZ2FuaXphdGlvbklkIjoiMTUyNjQwMDQxODAzNzU2MzM5MyIsIm9yZ2FuaXphdGlvbkNvZGUiOiI2OTQ4M2NjMzlhNTY0M2ZjOTk1Zjk5NzI0ZDllMjQ4MiIsInVzZXJSb2xlIjoiMCIsImV4cCI6MjQxMDIyNTU0OCwiaWF0IjoxNjUyODQzMTQ4fQ.9pk-mnBhycIvclljgMvQiRBkuiFfkcJE4OAxSEnrafo"
    headers = {"X-timevale-project-id": "1000000", "Content-Type": "application/json", "Authorization": token}
    print(getSealTypeCode(esignSignsWebUrl, headers, '1526446870367145986'))
    licenseNumber = "220301194809220027"
    # print(getPersonCertId(esignSignsWebUrl, headers, 'ceswm', licenseNumber, 'test2', '测试文敏'))
    print(getPersonSealId(esignSignsWebUrl, headers, 'ceswm', '测试文敏', '69483cc39a5643fc995f99724d9e2482',
                               'esigntest文敏测试企业001'))
