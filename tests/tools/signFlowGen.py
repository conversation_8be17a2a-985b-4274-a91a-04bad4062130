# 创建各种场景需要的发起流程的数据，适配一步发起接口
import os
import random
import sys
import debugtalk
from tools.SigntureConfig import SigntureConfig

from utils import ENV
from utils.esign6Manage import getInnerUserCustomAccountNo, getInnerOrgCustomAccountNo

csqs_userCode = ENV('csqs.userCode')
ci_orgCode = ENV('csqs.orgCode')
businessTypeCode = ENV('businessTypeCode')
user_code_signer = ENV('sign01.userCode')
sign01_main_orgCode = ENV('sign01.main.orgCode')
sign01_main_orgName = ENV('sign01.main.orgName')
pdf_filekey = ENV('fileKey')
ofd_filekey = ENV('ofdFileKey')
signNotifyUrl0 = "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
# 支持生成发起流程请求数据

def gen_signFlow_data(signFiles, signerInfos, businessTypeCode, userList, orgList):
    return gen_signFlow_data4("", signFiles, signerInfos, businessTypeCode, userList, orgList)

def gen_signFlow_data2(subject, businessNo, signFlowExpireTime,businessTypeCode,userList,orgList,fileKey):

    for key, value in userList.items():
        if key == 'userCodeInitiator':
            userCode = str(value)
        if key == 'userCodeSigner':
            userCodeSigner = str(value)
        if key == 'userCodeOuterSigner':
            userCodeOuterSigner = str(value)

    for key, value in orgList.items():
        if key == 'organizationCodeInitiator':
            organizationCode = str(value)
        if key == 'organizationCodeSigner':
            organizationCodeSigner = str(value)

    signFiles = []
    signerInfos = []
    signFile1 = gen_signFile_data(fileKey)
    signFiles.append(signFile1)
    signerInnerOrganize = gen_signerInfo_data(fileKey, userCodeSigner,
                                              organizationCodeSigner, 1, 1)
    signerInfos.append(signerInnerOrganize)
    signerOuterPerson = gen_signerInfo_data(fileKey, userCodeOuterSigner, "", 2, 1)
    signerInfos.append(signerOuterPerson)

    signFlowData = {
        "advertisement": "",
        "subject": subject,
        "chargingType": 1,
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": signFlowExpireTime,
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "CCInfos": [{"organizationCode": organizationCode, "userCode": userCode, "userType": 1}],
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos
    }
    return signFlowData


def gen_signFlow_data3(signFiles, signerInfos, businessNo):
    organizationCode = ci_orgCode
    userCode = csqs_userCode

    signFlowData = {
        "advertisement": "",
        "subject": "自动化case-" + str(random.randint(10, 100000)),
        "chargingType": 1,
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": "",
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "CCInfos": [{"organizationCode": organizationCode, "userCode": userCode, "userType": 1}],
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos}
    return signFlowData

def gen_signFlow_data4(businessNo, signFiles, signerInfos, businessTypeCode, userList, orgList):
    for key, value in userList.items():
        if key == 'userCodeInitiator':
            userCode = str(value)
            break

    for key, value in orgList.items():
        if key == 'organizationCodeInitiator':
            organizationCode = str(value)
            break

    signFlowData = {
        "advertisement": "",
        "subject": "自动化case-" + str(random.randint(10, 100000)),
        "chargingType": 1,
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": "",
        "signFlowExpireTime": "",
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "CCInfos": [{"organizationCode": organizationCode, "userCode": userCode, "userType": 1}],
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos}
    return signFlowData

def gen_signFlow_data5(signtureConfig: SigntureConfig, userList, orgList):

    for key, value in userList.items():
        if key == 'userCodeInitiator':
            userCode = str(value)
        if key == 'userCodeSigner':
            userCodeSigner = str(value)
        if key == 'userCodeOuterSigner':
            userCodeOuterSigner = str(value)

    for key, value in orgList.items():
        if key == 'organizationCodeInitiator':
            organizationCode = str(value)
        if key == 'organizationCodeSigner':
            organizationCodeSigner = str(value)

    signFiles = []
    signerInfos = []
    fileKey = pdf_filekey
    signFile1 = gen_signFile_data(fileKey)
    signFiles.append(signFile1)
    signerInnerOrganize = gen_signerInfo_data(fileKey, userCodeSigner,
                                              organizationCodeSigner, 1, signtureConfig.signModel)
    signerInfos.append(signerInnerOrganize)
    signerPerson = gen_signerInfo_data(fileKey, userCodeSigner, "", 1, signtureConfig.signModel)
    signerInfos.append(signerPerson)

    signFlowData = {
        "advertisement": "",
        "subject": signtureConfig.subject,
        "chargingType": 1,
        "businessNo": signtureConfig.businessNo,
        "businessTypeCode": signtureConfig.businessTypeCode,
        "signFlowExpireTime": signtureConfig.expireTime,
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "CCInfos": [{"organizationCode": organizationCode, "userCode": userCode, "userType": 1}],
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos
    }
    return signFlowData


# 获取无抄送人流程数据
def gen_signFlow_data_noCC(signFiles, signerInfos, businessTypeCode):
    return gen_signFlow_data_noCC2("", signFiles, signerInfos, businessTypeCode)

def gen_signFlow_data_noCC2(businessNo, signFiles, signerInfos, businessTypeCode):
    organizationCode = ci_orgCode
    userCode = csqs_userCode

    signFlowData = {
        "advertisement": "",
        "subject": "自动化case-" + str(random.randint(10, 100000)),
        "chargingType": 1,
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": "",
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos}
    return signFlowData

# 构建草稿态流程入参数据
def build_sign_flow_data_draft(businessNo,businessTypeCode,userCode,organizationCode,fileKey):
    signFiles = []
    # fileKey = pdf_filekey
    signFile1 = gen_signFile_data(fileKey)
    signFiles.append(signFile1)
    signInfo = []
    # innerPerson = gen_signerInfo_for_web_api()
    request_data = {
        "advertisement": "",
        "subject": "自动化case草稿-" + str(random.randint(10, 100000)),
        "chargingType": "1",
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": "",
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                          "userType": 1},
        "signFiles": signFiles
    }
    return request_data

def gen_sign_flow_data3(businessNo, person, organize, signMode, userType, status,businessTypeCode,userList,orgList,fileKeyList):
    try:
        for key,value in userList.items():
            if key == 'userCodeInitiator':
                userCode = str(value)
            if key == 'userCodeSigner':
                userCodeSigner = str(value)
            if key == 'userCodeSigner2':
                userCodeSigner2 = str(value)
            if key == 'userCodeOuterSigner':
                userCodeOuterSigner = str(value)

        for key,value in orgList.items():
            if key == 'organizationCodeInitiator':
                organizationCode = str(value)
            if key == 'organizationCodeSigner':
                organizationCodeSigner = str(value)
            # if key == 'organizationCodeOuterSigner':
            if key == 'oppositeOrganizationCode':
                oppositeOrganizationCode = str(value)

        for key, value in fileKeyList.items():
            if key == 'fileKey':
                fileKey = str(value)
            if key == 'fileKeyFailed':
                fileKeyFailed = str(value)

        signFiles = []
        signerInfos = []
        # fileKey = pdf_filekey
        if (status == 7):
            fileKey = fileKeyFailed
        signFile1 = gen_signFile_data(fileKey)
        signFiles.append(signFile1)

        # 纯内部
        if userType == 1:
            if organize:
                signerInnerOrganize = gen_signerInfo_data(fileKey, userCodeSigner,
                                                          organizationCodeSigner, 1, signMode)
                signerInfos.append(signerInnerOrganize)
            if person:
                signerInnerPerson = gen_signerInfo_data(fileKey, userCodeSigner, "", 1, signMode)
                signerInfos.append(signerInnerPerson)
                if signMode == 1 or signMode == 2:
                    signerInnerPerson2 = gen_signerInfo_data(fileKey, userCodeSigner2, "", 1, signMode)
                    signerInfos.append(signerInnerPerson2)

        # 纯外部
        if userType == 2:
            if organize:
                signerInnerOrganize = gen_signerInfo_data(fileKey, userCodeOuterSigner,
                                                          oppositeOrganizationCode, 2, signMode)
                signerInfos.append(signerInnerOrganize)
            if person:
                signerOuterPerson = gen_signerInfo_data(fileKey, userCodeOuterSigner, "", 2,
                                                        signMode)
                signerInfos.append(signerOuterPerson)

        # 内外混合
        if userType == 3:
            if organize:
                # 内部组织
                signerInnerOrganize = gen_signerInfo_data(fileKey, userCodeOuterSigner,
                                                          oppositeOrganizationCode, 2, signMode)
                signerInfos.append(signerInnerOrganize)

                # 外部组织
                signerOuterOrganize = gen_signerInfo_data(fileKey, userCodeOuterSigner,
                                                          oppositeOrganizationCode, 2, 1)
                signerInfos.append(signerOuterOrganize)

            if person:
                # 内部个人
                signerInnerPerson = gen_signerInfo_data(fileKey, userCodeSigner, "", 1, signMode)
                signerInfos.append(signerInnerPerson)

                # 外部个人
                signerOuterPerson = gen_signerInfo_data(fileKey, userCodeOuterSigner, "", 2,
                                                        signMode)
                signerInfos.append(signerOuterPerson)

        signFlowData = {
            "advertisement": "",
            "subject": "自动化case-" + str(random.randint(10, 100000)),
            "chargingType": "1",
            "businessNo": businessNo,
            "businessTypeCode": businessTypeCode,
            "businessTypeCode": businessTypeCode,
            "signFlowExpireTime": "",
            "signNotifyUrl": "",
            "redirectUrl": "",
            "remark": "",
            "attachments": signFiles,
            "CCInfos": [{"organizationCode": organizationCode, "userCode": userCode, "userType": 1}],
            "initiatorInfo": {"organizationCode": organizationCode, "userCode": userCode,
                              "userType": 1},
            "signFiles": signFiles,
            "signerInfos": signerInfos
        }
        return signFlowData
    except:
        print('组装流程发起数据失败')
        return None

# useType = 1 内部 2 相对方
# organize True 机构 False 个人
# nodeCount 节点数量
# signModel 0 顺序签 1 无序签 2 或签
# isStart 1 发起签署 0 保存草稿
def gen_save_web_signInfo(singerParamList, isStart):
    nodeList = singerParamList['nodeList']
    # print(nodeList)
    signInfos = []
    for i in range(len(nodeList)):
        signerNode = nodeList[i]
        # print(signerNode)
        processActorListBean = gen_web_processActorListBean(signerNode, isStart,i)
        signInfos.append(processActorListBean)
    return signInfos

# noticeParam {'useType':1,'index':0}
def gen_noticeBean(noticeParam):
    useType = noticeParam['useType']
    index = noticeParam['index']
    useInfo = debugtalk.getUserInfo(useType, 1, index)
    userCode = useInfo['userCode']
    name = useInfo['name']
    departmentCode = useInfo['departmentCode']
    departmentName = useInfo['departmentName']
    telOrEmail = useInfo['telOrEmail']
    noticeInfo = {
            'departmentCode': departmentCode,
            'departmentName': departmentName,
            'organizeCode': departmentCode,
            'organizeName': departmentName,
            'userCode': userCode,
            'userName': name,
            'userType': useType,
            'telOrEmail': telOrEmail,
            'accountList': []
        }
    return noticeInfo

def gen_save_web_noticeInfo(noticeParamList):
    noticeInfos = []
    for i in range(len(noticeParamList)):
        noticeParam = noticeParamList[i]
        noticeInfo = gen_noticeBean(noticeParam)
        noticeInfos.append(noticeInfo)
    return noticeInfos

# 一个节点内的签署信息
# useType = 1 内部 2 相对方
# organize True 机构 False 个人
# signModel 0 顺序签 1 无序签 2 或签
# isStart 1 发起签署 0 保存草稿
def gen_web_processActorListBean(signerNode, isStart,i):
    signModel = signerNode['signModel']
    singerList = signerNode['list']
    #签署方类型
    list = []
    for i in range(len(singerList)):
        singerParam = singerList[i]
        signerType = singerParam['signerType']
        userIndex = singerParam['userIndex']
        # 内部个人
        if signerType == 0:
            useType = 1
            organize = False
        # 内部机构
        if signerType == 1:
            useType = 1
            organize = True
        # 相对方个人
        if signerType == 2:
            useType = 2
            organize = False
        # 相对方机构
        if signerType == 3:
            useType = 2
            organize = True
        processActorBean = gen_web_processActorBean(useType, organize, isStart, i,userIndex)
        list.append(processActorBean)

    if signModel == 0:
        signModel = 1
    if isStart == 0:
        processActorBean = {
            'signModel': signModel,
            'list': list,
            'id': "node-"+str(i)
        }
    if isStart == 1:
        processActorBean = {
            'signModel': signModel,
            'list': list
        }
    return processActorBean

# useType = 1 内部 2 相对方
# isStart 1 发起签署 0 保存草稿
# organize  false 个人 True 机构
def gen_web_processActorBean(useType,organize,isStart,i,userIndex):
    sealType = 1
    if organize:
        sealType = 2

    useInfo = debugtalk.getUserInfo(useType, sealType, userIndex)
    userCode = useInfo['userCode']
    departmentCode = useInfo['departmentCode']
    departmentName = useInfo['departmentName']

    tsp0 = "LOCAL_DEFAULT_TSP"
    if useType == 2:
        tsp0 = "ESIGN_WITNESS_TSP"

    if isStart == 0:

        id = "add-"+str(i)
        if organize is False:
            uuid = userCode + "-" + departmentCode
            signer = {
                'isUkeySign': 0,
                'userCode': userCode,
                'userType': useType,
                "tspId": tsp0,
                'organizeCode': "",
                'legalSignFlag': 0,
                'legalSealAuthFlag': 0,
                'sealTypeId': "",
                'departmentCode': departmentCode,
                'departmentName': departmentName,
                'uuid': uuid,
                'signerType': 1,
                'id': id,
                'draggable': True
                # "accountList":[]
            }
            return signer
        if organize:
            uuid = departmentCode + "-" + departmentCode
            signer = {
                'isUkeySign': 0,
                'userCode': userCode,
                'userType': useType,
                "tspId": tsp0,
                'organizeCode': departmentCode,
                'legalSignFlag': 0,
                'sealTypeId': "",
                'departmentCode': departmentCode,
                'departmentName': departmentName,
                'uuid': uuid,
                'signerType': 2,
                'id': id,
                'draggable': True
                # "accountList":[]
            }
            return signer
    if isStart == 1:
        if organize is False:
            signer = {
                'isUkeySign': 0,
                'userCode': userCode,
                'userType': useType,
                "tspId": tsp0,
                'organizeCode': "",
                'legalSignFlag': 0,
                'sealTypeId': "",
                'departmentCode': departmentCode,
                'departmentName': departmentName,
                'legalSealAuthFlag': None
            }
            return signer
        if organize:
            signer = {
                'isUkeySign': 0,
                'userCode': userCode,
                'userType': useType,
                "tspId": tsp0,
                'organizeCode': departmentCode,
                'sealTypeId': "",
                'departmentCode': departmentCode,
                # 没有这个配置
                'departmentName': departmentName,
                'legalSealAuthFlag': 1
            }
            return signer

# isStart 1 发起签署 0 保存草稿
# isSort 1 顺序签署 0 非顺序签署
# signInfo 签署方列表
# docReturn 签署文件信息(含signFlowId)
# attach 1 附件 0 无
def gen_save_data(isStart, isSort, signInfo,docReturn,condition,subject):
    signFlowId = docReturn.get("signFlowId")
    '''
    header 中的token 用的是签署人的账号登录信息，所以发起机构必须是签署人的机构，而非发起人机构
    '''
    organizationCode = sign01_main_orgCode
    docInfos =[]
    signerUid = ""
    if isStart == 0:
        uid = 1650813102219
        file = [uid]
        fileKeys = docReturn.get("fileKeys")
        fileName = docReturn.get("fileName")
        docFormat = docReturn.get("docFormat")
        docInfos = []
        for i in range(len(fileKeys)):

            docInfo = {
                "docCount": 1,
                "docFormat": docFormat,
                "docUrl": "/esign-signs/signFile/pageDownload?fileKey=" + fileKeys[i] + "&pageNo=1",
                "fileKey": fileKeys[i],
                "fileName": fileName,
                "fileStatus": 2,
                "fileType": 1,
                "isSplit": True,
                "loading": False,
                "pageSize": 10,
                "uid": uid,
                "file": file
            }
            docInfos.append(docInfo)
        signerUid = organizationCode + organizationCode

    # 签署区配置信息
    sealInfo = debugtalk.get_save_detail(signFlowId)['signConfigInfo']
    attachmentDocInfo = []
    if condition == 1:
        attachmentDocInfo =[{
            "fileKey": fileKeys[0],
            "fileName": fileName,
            "loading": False,
            "name": fileName,
            "percentage": 100,
            "uid": 1653036705373
        }]
    noticeInfo = []
    if condition == 2 or condition == 3:
        useType1 = 1
        index = 0
        if condition == 3:
            useType1 = 2
            index = 5
        noticeParam = {'useType': useType1, 'index': index}
        noticeBean = gen_noticeBean(noticeParam)
        noticeInfo = [noticeBean]

    isReadComplete = 0
    if condition == 4:
        isReadComplete = 1
    signNotice = ""
    if condition == 5:
        signNotice = "1"
    if not subject:
        subject = "case-subject-" + random.randint(1, 999999)
    data = {
        "params":
            {
                "processId": signFlowId,
                "subject": subject,
                "businessNo": debugtalk.get_randomNo_str(),
                "remark": "",
                "isStart": isStart,
                "signNotice": signNotice,
                "signOffTime": "",
                "isReadComplete": isReadComplete,
                "businessTypeId": businessTypeCode,
                "signInfo": signInfo,
                "noticeInfo": noticeInfo,
                "organizeCode": organizationCode,
                # "organizeName": organizeName,
                "parentOrganizationCode": organizationCode,
                "chargingType": 1,
                "otherCostEnable": 1,
                "attachmentDocInfo": attachmentDocInfo,
                "isSort": isSort,
                "signerUid": signerUid,
                "docInfo": docInfos,
                "sealInfo": sealInfo
            }
    }
    return data

# person 个人 organize  机构
# userType 1 内部 2 相对方(外部)
# signMode 0 顺序签 1 无序签 2 或签
def gen_signerInfos(person, organize, userType,signMode,fileKey,userList,orgList):
    signerInfos = []

    for key, value in userList.items():
        if key == 'userCodeSigner2':
            userCodeSigner2 = str(value)
        if key == 'userCodeSigner':
            userCodeSigner = str(value)
        if key == 'userCodeOuterSigner':
            userCodeOuterSigner = str(value)

    for key, value in orgList.items():
        if key == 'organizationCodeSigner':
            organizationCodeSigner = str(value)
        if key == 'oppositeOrganizationCode':
            oppositeOrganizationCode = str(value)

    # 纯内部
    if userType == 1:
        if organize:
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeSigner, organizationCodeSigner, 1, signMode))
        if person:
            # 个人签署1
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeSigner, "", 1, signMode))
            # 个人签署人2
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeSigner2, "", 1, signMode))

    # 纯外部
    if userType == 2:
        if organize:
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeOuterSigner, oppositeOrganizationCode, 2, signMode))
        if person:
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeOuterSigner, "", 2,signMode))

    # 内外混合
    if userType == 3:
        if organize:
            # 内部组织
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeSigner,organizationCodeSigner, 1, signMode))
            # 外部组织
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeOuterSigner,oppositeOrganizationCode, 2, signMode))

        if person:
            # 内部个人
            signerInfos.append(gen_signerInfo_data(fileKey, userCodeSigner, "", 1, signMode))
            # 外部个人
            signerInfos.append( gen_signerInfo_data(fileKey, userCodeOuterSigner, "", 2, signMode))



def gen_draft_data(signFlowId,fileKey,fileName,businessTypeCode,userList,orgList):
    for key,value in orgList.items():
        if key == 'organizationCodeInitiator':
            organizationCode = str(value)
            break

    docInfos = []
    uid = 1650273269233
    file = [uid]
    docInfo1 = {
        "docCount": 1,
        "docFormat": "pdf",
        "docUrl": "/esign-signs/signFile/pageDownload?fileKey="+fileKey+"&pageNo=1",
        "fileKey": fileKey,
        "fileName": fileName,
        "fileStatus": 2,
        "fileType": 1,
        "isSplit": True,
        "loading": False,
        "pageSize": 10,
        "uid": uid,
        "file": file
    }
    docInfos.append(docInfo1)

    signFlowData = {
        "params":
        {
            "attachmentDocInfo": [],
            "subject": "case-subject-" + str(random.randint(10, 100000)),
            "chargingType": 1,
            "businessNo": random.randint(1, 999999),
            "businessTypeId":  businessTypeCode,
            "docInfo": docInfos,
            "isReadComplete": 0,
            "isSort": None,
            "isStart": 0,
            "noticeInfo": [],
            "organizeCode": organizationCode,
            "otherCostEnable": 1,
            "parentOrganizationCode": organizationCode,
            "processId": signFlowId,
            "remark": "",
            "signInfo": gen_signerInfos(True, False, 1, 1, fileKey,userList,orgList),
            "signNotice": "",
            "signOffTime": "",
            "signerUid": organizationCode+organizationCode
        }
    }
    return signFlowData

def gen_signFile_data(fileKey):
    return {"fileKey": fileKey, "fileOrder": 1}


def gen_signerInfo_data(fileKey, userCode, organizationCode, userType, signMode):
    customAccountNo = ""
    customOrgNo = ""
    signNode = 1
    signConfig = {"posY": "250", "posX": str(random.randint(100, 500)), "pageNo": "1-3", "signType": "COMMON-SIGN",
                  "signatureType": "PERSON-SEAL"}
    if organizationCode:
        signConfig = {"posY": "150", "posX": "150", "pageNo": "1-3", "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL"}

    if signMode == 0:
        signNode = random.randint(3, 100)

    if signMode == 2:
        signNode = 2

    tsp0 = "LOCAL_DEFAULT_TSP"
    if userType == 2:
        tsp0 = "ESIGN_WITNESS_TSP"

    if userType != 2:
        customAccountNo = getInnerUserCustomAccountNo(userCode)
        userCode = ""
        if organizationCode and len(organizationCode)>1:
            customOrgNo = getInnerOrgCustomAccountNo(organizationCode)
            organizationCode = ""

    signerInfo = {
        "sealInfos": [
            {
                "fileKey": fileKey,
                "signConfigs": [signConfig]
            }
        ],
        "signNode": signNode,
        "signMode": signMode,
        "userType": userType,
        "tspId": tsp0,
        "userCode": userCode,
        "organizationCode": organizationCode,
        "customAccountNo": customAccountNo,
        "customOrgNo": customOrgNo,
        "departmentCode": ""
    }
    return signerInfo


def gen_signerInfo_data2(fileKey, userType, userCode, organizationCode, signMode, signNode):
    customAccountNo = ""
    customOrgNo = ""
    signConfig = {"posY": "250", "posX": "250", "pageNo": "1-3", "signType": "COMMON-SIGN",
                  "signatureType": "PERSON-SEAL"}
    if organizationCode:
        signConfig = {"posY": "150", "posX": "150", "pageNo": "1-3", "signType": "EDGE-SIGN",
                      "signatureType": "COMMON-SEAL"}

    if signMode == 0:
        signNode = random.randint(1, 100)

    if userType != 2:
        customAccountNo = getInnerUserCustomAccountNo(userCode)
        userCode = ""
        if organizationCode and len(organizationCode)>1:
            customOrgNo = getInnerOrgCustomAccountNo(organizationCode)
            organizationCode = ""

    tsp0 = "LOCAL_DEFAULT_TSP"
    if userType == 2:
        tsp0 = "ESIGN_WITNESS_TSP"
    signerInfo = {
        "sealInfos": [
            {
                "fileKey": fileKey,
                "signConfigs": [signConfig]
            }
        ],
        "signNode": signNode,
        "signMode": signMode,
        "userType": userType,
        "tspId": tsp0,
        "userCode": userCode,
        "organizationCode": organizationCode,
        "customAccountNo": customAccountNo,
        "customOrgNo": customOrgNo,
        "departmentCode": ""
    }
    return signerInfo

def gen_signerInfo_autoSign_data(sealInfos, userCode, organizationCode, signMode, signNode):
    customAccountNo = getInnerUserCustomAccountNo(userCode)
    userCode = ""
    customOrgNo = ""
    if organizationCode:
        customOrgNo = getInnerOrgCustomAccountNo(organizationCode)
        organizationCode = ""
    signerInfo = {
        "sealInfos": sealInfos,
        "signNode": signNode,
        "signMode": signMode,
        "autoSign": True,
        "tspId": "LOCAL_DEFAULT_TSP",
        "userType": 1,
        "userCode": userCode,
        "customAccountNo": customAccountNo,
        "customOrgNo": customOrgNo,
        "organizationCode": organizationCode
    }
    return signerInfo

# 获取自由签的签署人数据
def gen_signerInfo_freeSign_data(userType, userCode, organizationCode, signMode, signNode):
    tsp0 = "LOCAL_DEFAULT_TSP"
    if userType == 2:
        tsp0 = "ESIGN_WITNESS_TSP"
    signerInfo = {
        "signNode": signNode,
        "signMode": signMode,
        "autoSign": False,
        "userType": userType,
        "tspId": tsp0,
        "userCode": userCode,
        "organizationCode": organizationCode
    }
    return signerInfo


def gen_sealInfo_data(fileKey, signType, userCodeSealId, organizationCodeSealId, keyword):
    sealId = userCodeSealId
    sealInfo = {}
    signConfigs = []

    if organizationCodeSealId:
        sealId = organizationCodeSealId
    # print("获取印章数据signType：" + signType)
    if signType == 'COMMON-SIGN':
        signConfig = {"sealId": userCodeSealId, "posY": "250", "posX": "250", "pageNo": "1-3",
                      "signType": "COMMON-SIGN",
                      "signatureType": "PERSON-SEAL"}
    elif signType == 'EDGE-SIGN':
        signConfig = {"sealId": sealId, "posY": "150", "posX": "150", "pageNo": "1-3", "signType": "EDGE-SIGN",
                      "signatureType": "COMMON-SEAL"}

    elif signType == 'KEYWORD-SIGN':
        signConfig = {"sealId": sealId, "posY": "150", "posX": "150", "pageNo": "1-99", "signType": "KEYWORD-SIGN",
                      "signatureType": "COMMON-SEAL",
                      "keywordInfo": {"keyword": keyword, "keywordIndex": "1", "offsetPosX": "130.09",
                                      "offsetPosY": "30.09"}}

    signConfigs.append(signConfig)

    sealInfo = {
        "fileKey": fileKey,
        "signConfigs": signConfigs
    }

    return sealInfo
#获取一个内部机构签署人，入参可以是None
#暂时只支持获取固定企业下的人员，企业：esigntest天印测试企业
def get_inner_org_signer(sealTypeId, userCode):
    signer = {
        'departmentCode': sign01_main_orgCode,
        'departmentName': sign01_main_orgName,
        'draggable': True,
        'isUkeySign': 0,
        'legalSealAuthFlag': 0,
        'legalSignFlag': 0,
        'organizeCode': sign01_main_orgCode,
        'userCode': userCode,
        "tspId": "LOCAL_DEFAULT_TSP",
        'userType': 1,
        'sealTypeId': sealTypeId
    }
    return signer
#获取一个内部个人签署人
def get_inner_person_signer(userCode):
    signer = {
        'departmentCode': sign01_main_orgCode,
        'departmentName': sign01_main_orgName,
        'draggable': True,
        'isUkeySign': 0,
        'legalSealAuthFlag': 0,
        'legalSignFlag': 0,
        'userCode': userCode,
        "tspId": "LOCAL_DEFAULT_TSP",
        'userType': 1
    }
    return signer
#获取一个相对方个人签署人
def get_opposite_person_signer(userCode, orgCode, orgName):
    signer = {
        'departmentCode': orgCode,
        'departmentName': orgName,
        'draggable': True,
        'isUkeySign': 0,
        'legalSealAuthFlag': 0,
        'legalSignFlag': 0,
        'userCode': userCode,
        "tspId": "ESIGN_WITNESS_TSP",
        'userType': 2
    }
    return signer
#获取一个相对方机构签署
def get_opposite_org_signer(userCode,organizeCode, departmentCode, departmentName):
    signer = {
        'departmentCode': departmentCode,
        'departmentName': departmentName,
        'organizeCode': organizeCode,
        'draggable': True,
        'isUkeySign': 0,
        'legalSealAuthFlag': 0,
        'legalSignFlag': 0,
        'userCode': userCode,
        "tspId": "ESIGN_WITNESS_TSP",
        'userType': 2
    }
    return signer

# configParam 传入参数
def gen_standardSign_data(organize, applyId, detail, configParam):
    sealId = configParam['sealId']
    sealTypeCode = configParam['sealTypeCode']
    accountCode = configParam['accountCode']
    organizeCode = configParam['organizeCode']
    condition = configParam['condition']
    if detail.get('fileFormat') == 2:
        addSignDate = False
        isAddSealSignTime = 0
    else:
        addSignDate = True
        isAddSealSignTime = 1
    signDocInfoRequests = []
    for i in range(len(detail["signConfigInfo"])):
        signConfig = detail["signConfigInfo"][i]
        processDocId = signConfig["docId"]
        taskUUID = None
        for i in range(len(detail["docInfo"])):
            docInfo = detail["docInfo"][i]
            docId = docInfo["id"]
            if docId == processDocId:
                taskUUID = docInfo["taskId"]

        actorSignConfigList = signConfig["actorSignConfigList"]
        detailRequests = get_person_detail_request(organize,actorSignConfigList, sealId,sealTypeCode,condition)

        signDocInfoRequest = {
            "taskUUID": taskUUID,
            "processDocId": processDocId,
            "addSignDate": addSignDate,
            "detailRequests": detailRequests
        }
        signDocInfoRequests.append(signDocInfoRequest)

    signData = {"params":
        {
            "accountCode": accountCode,
            "applyId": applyId,
            "signDocInfoRequests": signDocInfoRequests,
            "processUUId": detail["processId"],
            "organizeCode": organizeCode,
            "willAuthFlag": False
        }
    }
    print('[gen_standardSign_data]:',signData)
    return signData

def gen_org_detail_request(actorSignConfigList,sealId,sealTypeCode):
    detailRequests = []
    detailUUID = None
    if len(actorSignConfigList) != 0:
        actorSignConfigList0 = actorSignConfigList[0]
        detailUUID = actorSignConfigList0["detailId"]
    detailRequest = {
        "isAddSealSignTime": 0,
        "assignPosition": 0,
        "detailUUID": detailUUID,
        "isHandEnable": 0,
        "rotate": 0,
        "sealId": sealId,
        "signType": 2,
        "sealType": 3,
        "sealTypeCode": sealTypeCode,
        "pageNo": "1",
        "posX": 150,
        "posY": 150,
        "edgeScope": 0,
        "timePosX": 160,
        "timePosY": 160,
        "sealSignTimeFormat": 1
                    }
    detailRequests.append(detailRequest)
    return detailRequests

# sealIdentityType 1手绘 2个人 3机构 4法人
# edgeScope 骑缝签范围 0全部 1奇数 2偶数，默认0
# signIdentity 签署身份1个人 2机构
# signType 签署类型,1单页签,2多页签,3骑缝签,4关键字签
def get_person_detail_request(organize,actorSignConfigList, sealId,sealTypeCode,condition):
    detailRequests = []
    rotate = 0
    if condition == 5:
        rotate = 57
    sealType = 2
    if organize:
        sealType = 3
    if len(actorSignConfigList) != 0:
        for i in range(len(actorSignConfigList)):
            actorSignConfig = actorSignConfigList[i]
            if actorSignConfig.get('freeMode') == 0: #指定签署
                # signType 签署类型,1单页签,2多页签,3骑缝签,4关键字签
                edgeScope = 0
                signType = 2
                if actorSignConfig["isKeyword"] is not None and actorSignConfig["isKeyword"] ==1:
                    signType = 4
                if actorSignConfig["signType"] == 2:
                    signType = 3
                    edgeScope = actorSignConfig["edgeScope"]
                isAddSealSignTime = actorSignConfig["isAddSealSignTime"]
                if isAddSealSignTime == None:
                    isAddSealSignTime = 0
                sealSignTimeFormat = 1
                if isAddSealSignTime == 1:
                    sealSignTimeFormat = actorSignConfig["sealSignTimeFormat"]
                detailRequest = {
                            "isAddSealSignTime": isAddSealSignTime,
                            "assignPosition": 1,
                            "detailUUID": actorSignConfig["detailId"],
                            "isHandEnable": 0,
                            "rotate": rotate,
                            "sealId": sealId,
                            "signType": signType,
                            "sealType": sealType,
                            "sealTypeCode": sealTypeCode,
                            "pageNo": actorSignConfig["pageNo"],
                            "posX": actorSignConfig["posX"],
                            "posY": actorSignConfig["posY"],
                            "timePosX": actorSignConfig["timePosX"],
                            "timePosY": actorSignConfig["timePosY"],
                            "sealSignTimeFormat": sealSignTimeFormat,
                            "edgeScope": edgeScope
                    }
                detailRequests.append(detailRequest)
            else: #自由签署，默认添加一个普通签署区
                detailRequest = {
                    "isAddSealSignTime": 0,
                    "assignPosition": 1,
                    "detailUUID": actorSignConfig["detailId"],
                    "isHandEnable": 0,
                    "rotate": rotate,
                    "sealId": sealId,
                    "signType": 2,
                    "sealType": 2,
                    "sealTypeCode": sealTypeCode,
                    "pageNo": 1,
                    "posX": 200,
                    "posY": 200,
                    "timePosX": 100,
                    "timePosY": 100,
                    "sealSignTimeFormat": 1,
                    "edgeScope": 0
                }
                detailRequests.append(detailRequest)


    if len(detailRequests) == 0:
        detailRequest = {
            "isAddSealSignTime": 0,
            "assignPosition": 0,
            "detailUUID": None,
            "isHandEnable": 0,
            "rotate": rotate,
            "sealId": sealId,
            "signType": 1,
            "sealType": sealType,
            "sealTypeCode": sealTypeCode,
            "pageNo": "1",
            "posX": 250,
            "posY": 250,
            "timePosX": 250,
            "timePosY": 151,
            "sealSignTimeFormat": 1,
            "edgeScope": 0
        }
        detailRequests.append(detailRequest)
    return detailRequests


def gen_fieName(fileFormat):
    fileName = '三页文档.pdf'
    if fileFormat == "png":
        fileName = "1.png"
    if fileFormat == "docx":
        fileName = "关键字.docx"
    if fileFormat == "jpg":
        fileName = "psb.jpg"
    if fileFormat == "doc":
        fileName = "普通doc文件.doc"
    if fileFormat == "ofd":
        fileName = "key30page.ofd"
    if fileFormat == "PDF":
        fileName = "test1.PDF"
    if fileFormat == "pdf-more":
        fileName = "劳动合同书.pdf.PDF.PDF.pdf"
    if fileFormat == "pdf-edit-pwd":
        fileName = "编辑密码-PI3.1415926.pdf"
    if fileFormat == "pdf-query-pwd":
        fileName = "加密文件密码123qwe.pdf"
    if fileFormat == "pdf-page30":
        fileName = "key30page.pdf"
    if fileFormat == '100-pdf':
        fileName = "99MB文档.pdf"
    if fileFormat == "sm-pdf":
        fileName = "扫描件.pdf"
    if fileFormat == 'shu-pdf':
        fileName = "500页横竖PDF.pdf"

    return fileName

def get_sealInfos(fileKey,organizationCode):
    signConfig = {"posY": "250", "posX": "250", "pageNo": "1-3", "signType": "COMMON-SIGN",
                  "signatureType": "PERSON-SEAL"}
    if organizationCode:
        signConfig = {"posY": "150", "posX": "150", "pageNo": "1-3", "signType": "COMMON-SIGN",
                      "signatureType": "COMMON-SEAL"}
    
    sealInfo = {
                "fileKey": fileKey,
                "signConfigs": [signConfig]
            }
    
    return sealInfo

def autoSignByFile(businessTypeCode,fileKey,userCode,organizationCode):
    try:
        sealInfos = [get_sealInfos(fileKey,organizationCode)]
        signerInfos = [gen_signerInfo_autoSign_data(sealInfos, userCode, organizationCode, 0, 1)]
        signFiles = [gen_signFile_data(fileKey)]
        data = gen_signFlow_data_noCC2('', signFiles, signerInfos, businessTypeCode)
        return data
    except :
        print(sys._getframe().f_code.co_name + "-调用异常")
        
def signCharging(businessTypeCode,fileKey,organizationCodeInitiator,userCodeInitiator,userCodeOuter,organizationCodeOuter):
    '''
    创建相对方计费的流程（签署主体一定包含相对方机构，一定不包含相对方个人）
    :param businessTypeCode:
    :param fileKey:
    :param userCode:
    :param organizationCode:
    :return:
    '''
    try:
        signerInfos = [gen_signerInfo_data2(fileKey, 2, userCodeOuter, organizationCodeOuter, 0, 1)]
        signFiles = [gen_signFile_data(fileKey)]
        
        data = {
        "advertisement": "",
        "subject": "自动化case-计费-" + str(random.randint(10, 100000)),
        "chargingType": 2,
        "businessNo": "",
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": "",
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": [],
        "initiatorInfo": {"organizationCode": organizationCodeInitiator, "userCode": userCodeInitiator,
                          "userType": 1},
        "signFiles": signFiles,
        "signerInfos": signerInfos}
        
        return data
    except :
        print(sys._getframe().f_code.co_name + "-调用异常")


def getOrSignOrNoSequeueRequst(businessTypeCode, fileKey, organizationCodeInitiator, userCodeInitiator, userCode,
                 organizationCode,userType,signMode):
    '''
    创建 无序或是 或签的流程
    :param businessTypeCode:
    :param fileKey:
    :param organizationCodeInitiator:
    :param userCodeInitiator:
    :param userCode:
    :param organizationCode:
    :param userType: 1-内部 2-相对方
    :param signMode: 1-无序， 2-或签， 3-只有一个节点且或签模式，4-单节点个人签署
    :return:
    '''
    try:
        if signMode == 1 or signMode == 2:
            p1 = gen_signerInfo_data2(fileKey, userType, userCode, organizationCode, signMode, 1)
            p2 = gen_signerInfo_data2(fileKey, userType, userCode, '', signMode, 1)
            p3 = gen_signerInfo_data2(fileKey, 1, userCodeInitiator, '', 0, 2)
            signerInfos = [p1,p2,p3]
        else:
            if signMode == 3:
                p1 = gen_signerInfo_data2(fileKey, userType, userCode, organizationCode, 2, 1)
                p2 = gen_signerInfo_data2(fileKey, userType, userCode, '', 2, 1)
                signerInfos = [p1, p2]
            if signMode == 4:
                p1 = gen_signerInfo_data2(fileKey, userType, userCode, organizationCode, 0, 1)
                signerInfos = [p1]
                
        signFiles = [gen_signFile_data(fileKey)]
        
        data = {
            "subject": "pythonTC-scene-" + str(random.randint(10, 100000)),
            "businessNo": "scene",
            "signNotifyUrl": signNotifyUrl0,
            "businessTypeCode": businessTypeCode,
            "initiatorInfo": {"organizationCode": organizationCodeInitiator, "userCode": userCodeInitiator,
                              "userType": 1},
            "signFiles": signFiles,
            "signerInfos": signerInfos
        }
        
        return data
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        

#为了兼容v6.0.4.0版本删除接口的，临时构造的方法
def gen_signFlow_data6(subject, businessNo, businessTypeCode, initiatorInfo, signFiles, signerInfos, CCInfos, signFlowExpireTime):
    signFlowData = {
        "subject": subject,
        "chargingType": 1,
        "businessNo": businessNo,
        "businessTypeCode": businessTypeCode,
        "signFlowExpireTime": signFlowExpireTime,
        "signNotifyUrl": "",
        "redirectUrl": "",
        "remark": "",
        "attachments": signFiles,
        "CCInfos": CCInfos,
        "initiatorInfo": initiatorInfo,
        "signFiles": signFiles,
        "signerInfos": signerInfos
    }
    return signFlowData

        
            

if __name__ == '__main__':
    print(autoSignByFile('test','xxxx.pdf','userCode','orgXXX'))