# -*-coding:utf-8-*-
# <AUTHOR> wenmin
# @Time : 2022/8/2 14:01
# @Description : 实现MQ的监听
# pip install rocketmq-client-python(采用方法二不需要安装)

# from rocketmq.client import PullConsumer
import json
import sys
import re

# def pullConsumer():
#     consumer = PullConsumer('PID-001')
#     # consumer.set_namesrv_addr('ip:port')
#     consumer.set_namesrv_addr('************:9876')
#     consumer.start()
#     for msg in consumer.pull('businessTypeUpdateTopic'):
#         print(msg.tags)
#         print(msg.keys)
#         print(msg.id, msg.body)
#         print(msg.topic)
#         print(msg)
#         data = json.loads(str(msg))  # dict
#     consumer.shutdown()

# 方法二: 借用MQ的控制台，使用http接口获取信息
import requests

#获取所有topic信息
def getTopicsList(host):
    try:
        Url = host + '/topic/list.query'
        res = requests.get(url=Url)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            topicList = res.json()['data']['topicList']
            topicListV1 = []
            topicListV2 = []
            result = {}
            for item in topicList:
                if item.startswith('%RETRY%'):
                    topicListV1.append(item)
                    continue
                if item.startswith('%DLQ%') | item.startswith('%SYS%'):
                    continue
                topicListV2.append(item)
            # 判定接口调用成功
            result['topicListRETRY'] = topicListV1
            result['topicList'] = topicListV2
            print('xxxx: ',result)
            return topicListV1
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#查询topic的consumer管理信息，主要看差值是否为0
def queryConsumerByTopic(host,topic):
    try:
        Url = host + '/topic/queryConsumerByTopic.query?topic=' + topic
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.get(url=Url)
        # print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#给topic发送消息
#message:   {"key": "key111", "tag": "tag111", "messageBody": "messageBody1111"}
def sendTopicMessage(host,topic,message):
    try:
        Url = host + '/topic/sendTopicMessage.do'
        message["topic"] = topic
        message["traceEnabled"] = False
        headers = {"Content-Type": "application/json"}
        
        res = requests.post(Url, json=message, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None
    
#获取所有的消费者
def getGroupList(host):
    try:
        Url = host + '/consumer/groupList.query'
        res = requests.get(url=Url)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#获取消费者的详情信息
def queryTopicByConsumer(host,group):
    try:
        Url = host + '/consumer/queryTopicByConsumer.query?consumerGroup=' + group
        res = requests.get(url=Url)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#通过topic查询发送的消息
def queryMessagePageByTopic(host,topic):
    try:
        Url = host + '/message/queryMessagePageByTopic.query'
        data = {"begin":"","end":"","pageNum":1,"pageSize":20,"taskId":""}
        data["topic"] = topic
        headers = {"Content-Type": "application/json"}

        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#通过topic和key精确查询发送的消息
def queryMessageByTopicAndKey(host,topic,key):
    try:
        tmp = '/message/queryMessageByTopicAndKey.query?key='+key+'&topic=' + topic
        Url = host + tmp
        res = requests.get(url=Url)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#通过topic和msgId精确查询发送的消息
def viewMessage(host,topic,msgId):
    try:
        tmp = '/message/viewMessage.query?msgId='+msgId+'&topic=' + topic
        Url = host + tmp
        res = requests.get(url=Url)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

#检查消费者的详情
def consumerConnection(host,consumerGroup):
    try:
        tmp = '/consumer/consumerConnection.query?consumerGroup=' + consumerGroup
        Url = host + tmp
        res = requests.get(url=Url)
        print(consumerGroup)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None
    
#巡检所有的topic都不存在延时
def checkTopicDiffTotal(host):
    try:
        topicList = getTopicsList(host)['topicList']
        for item in topicList:
            if item.startswith('%RETRY%') | item.startswith('%DLQ%')  | item.startswith('%SYS%') :
                continue
            tmp = queryConsumerByTopic(host,item)
            if tmp:
                for key in tmp.keys():
                    diffTotal = tmp[key]['diffTotal']
                    if diffTotal == 0:
                        # print('巡检topic= %s 正常 ' % key)
                        continue;
                    else:
                        print('topic: %s 存在延迟' % key)
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None
    

#巡检所有的consumer下都不会有多个topic
def checkconsumerGroups(host):
    try:
        groupList = getGroupList(host)
        for item in groupList:
            item = item['group']
            if item.startswith('%RETRY%') | item.startswith('%DLQ%')  | item.startswith('%SYS%') :
                continue
            tmp = consumerConnection(host,item)
            if tmp:
                keys1 = tmp['subscriptionTable'].keys()
                if len(keys1)>2:
                    print('group: %s 存在多个topic' % item)
                else:
                    count = 0
                    for k in keys1:
                        
                        if k.startswith('%RETRY%'):
                            continue
                        else:
                            count = count+1
                    if count != 1:
                        print('group: %s 存在异常' % item)
                    # else:
                    #     print('group: %s 正常' % item)
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None
    

def tianyin6TopicLists(groupName):
    mqlist = ["evidence-batch-issue-self","mq_topic_evidence_apply","evidence-link-create","evidence-hand-cloud",
              "evidence-sync-cloud","mq_topic_ele_seal_user_relation_change","mq_topic_legal_seal_user_relation_change",
              "docs_HandleBatchTemplateInitiationTopic","docsSignedFileHistoryImportTopic","mq_topic_org","mq_topic_org_cache",
              "mq_topic_user","mq_topic_pw","manage_schedule_job_change_mq","signsWfTodoTask","signsSendMessageTopic",
              "signsCallBackTopic","signsAfterCreateProcessTopic","signsProcessDownLoadTaskTopic","signsWfTodoTaskAlreadyTopic",
              "updateNextSignStatusAndAuthSignTopic","signsLargeExeStandardSignTopic","signsApprovalCallBackTopic",
              "signEvidenceForQuickSignTopic","signsLargeExeApprovalStandardSignTopic","mq_topic_backend_task","signMessageUndisturbTopic",
              "subStepUpdateSignerTopic","signsProcessCancelNotifyTopic","signsProcessStatusChangeNotifyTopic","businessTypeUpdateTopic",
              "wf_flowable_model_change_topic","mq_topic_flow_del","mq_topic_flow_status","mq_topic_flow_cancel_sign",
              "mq_topic_flow_integration"]
    if groupName:
        return None
    return mqlist


    
if __name__ == '__main__':
    host = '************:9876'
    port = '9876'
    # host = 'http://************:8080'
    host = 'http://test-nodeport.tsign.cn:31087'
    msg = {"key":"key111","tag":"tag111","messageBody":"messageBody1111","traceEnabled":False}
    topic = 'businessTypeUpdateTopic'
    group = 'signsAfterCreateProcessGroup'
    key = 'key111'
    msgId = '7F0000017DCF21B8D17C0941D7C10008'
    consumerGroup = 'esign-seals-password'
    getTopicsList(host)
    
    # queryConsumerByTopic(host,topic)
    sendTopicMessage(host, topic, msg)
    # queryMessageByTopicAndKey(host, topic, key)
    # viewMessage(host, topic, msgId)
    # queryMessagePageByTopic(host, topic)
    # checkTopicDiffTotal(host,[])
    # checkconsumerGroups(host)
