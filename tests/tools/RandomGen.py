import random


def generate_random_str(randomlength):
    """
    生成一个指定长度的随机字符串
    """
    random_str = ''
    base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return random_str



# f = generate_random_str(24)
# print(f)

def generate_random(randomlength):
    """
    生成一个指定长度的随机数字
    """
    random_str = ''
    base_str = '0123456789'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return random_str

def generate_random_chinese(randomlength):
    """
    生成一个指定长度的中文名
    """
    random_str = ''
    first_str = '李王张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁於董余苏叶吕魏蒋田杜丁沈' \
               '姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚'
    length = len(first_str) - 1
    s1 = first_str[random.randint(0, length)]
    
    base_str = '李王张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁於董余苏叶吕魏蒋田杜丁沈' \
               '姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚' \
               '豪言玉意泽彦轩景正程诚宇澄安青泽轩旭恒思宇嘉宏皓成宇轩玮桦宇达韵磊泽博昌信彤逸柏新劲鸿文恩远翰圣哲家林' \
               '景行律本乐康昊宇麦冬景武茂才军林茂飞昊明天伦峰志辰佳彤自怡颖宸雅微羽馨思纾欣元凡晴玥宁佳蕾桑妍萱宛欣灵' \
               '烟文柏艺以如雪璐言婷青安昕淑雅颖云艺忻梓江丽梦雪沁思羽羽雅访烟萱忆慧娅茹嘉幻辰妍雨蕊欣芸亦'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return s1+random_str

