# 签署回调校验相关工具类
import datetime
import json
import time

import pymysql

#
# def dataFactory():
#     try:
#         db = pymysql.connect(
#             host='*************',
#             port=3306,
#             user='root',
#             passwd='root123',
#             db='dataFactory',
#             charset='utf8',
#             cursorclass=pymysql.cursors.DictCursor
#         )
#         return db
#     except Exception:
#         raise Exception("数据工厂数据库连接失败")
#
#
# # 执行sql语句
# def execute_callback_sql(processId):
#     db = dataFactory()
#     cur = db.cursor()
#     try:
#         sql = "SELECT content FROM callback  WHERE content LIKE" + " \"%" + processId + "%\""
#         cur.execute(sql)
#         result = cur.fetchall()
#         db.commit()
#         # print('查询结果：', result)
#
#         return result
#     except pymysql.Error as e:
#         db.rollback()
#         print("数据库连接失败：" + str(e))
#     except pymysql.ProgrammingError as e:
#         db.rollback()
#         print("sql语法执行失败：" + str(e))
import requests


def execute_callback(processId):
    try:
        time.sleep(5)
        now = datetime.datetime.now()
        t = now + datetime.timedelta(hours=-2)
        t2  = now + datetime.timedelta(hours=2)
        st = datetime.datetime.strftime(t, '%Y-%m-%d %H:%M:%S')
        et = datetime.datetime.strftime(t2, '%Y-%m-%d %H:%M:%S')
        Url = "http://datafactory.smlk8s.esign.cn/simpleTools/callbackList/?pageNum=1&pageSize=10&env=test&flowId="+processId+"&startTime="+st+"&endTime="+et
        cbs = requests.get(url=Url, params=None, headers=None)
        cb = cbs.json()['callbacks']

        print('[execute_callback-request]:',Url)
        print('[execute_callback]:',cb)
        return cb
    except:
        print('查询回调信息失败')
        return None


# status = 6 签署失败，流程状态仍未签署中， 签署状态为失败
# status = 5 (status = 1签署中被作废时，status =5已作废)
# status = 4 拒签 调用方式get_signFlowId_status(4, get_business_no(), False, True, 0,1,0)
# status = 3 过期/有过期时间
# status = 2 已完成
# status = 1 签署中
# status = 0 流程未开启

def callback_check(status, processId):
    print('xxxx====',status, processId)
    # global cbstatus
    cb = execute_callback(processId)
    if status == 0:
        if len(cb) == 0:
            return True

    if status == 1:
        # start = 0
        if len(cb) > 0:
            # res = []

            for item in cb:
                content = json.loads(item['content'])
                if (content['callBackDesc']) == "SIGN_FLOW_START":
                    print(type(content['callBackProcessVO']))
                    callBackProcessVO = content['callBackProcessVO']
                    print(callBackProcessVO)
                    if callBackProcessVO['flowStatus'] == 1:
                        # if callBackProcessVO['signerList'] is not None and
                        #     callBackProcessVO['nextSignerList'] is not None:
                        # start = 1
                        print("回调校验成功")
                        return True


        else:
            print("回调校验失败")
            return False

    if status == 2:
        if len(cb) > 0:
            start = 0
            finish = 0
            signed = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START":
                    # print(type(content['callBackProcessVO']))
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO['signerList'] is not None and \
                            callBackProcessVO['nextSignerList'] is not None:
                        print("发起签署回调校验成功")
                        start = 1
                        continue
                if (content['callBackDesc']) == "SIGN_FLOW_FINISH":
                    if callBackProcessVO['flowStatus'] == 2 and callBackProcessVO['signerList'] is not None and \
                            callBackProcessVO['processBeginTime'] is not None:
                        print("签署完成回调校验成功")
                        finish = 1
                        continue
                if (content['callBackDesc']) == "SIGN_SIGNER_SIGNED":
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO['signerList'] is not None:
                        print("签署人签署完成回调校验成功")
                        signed = 1
                        continue
            if start == 1 and finish == 1 and signed == 1:
                print("回调校验成功")
                return True
            else:
                print("签署完成回调校验失败")
                return False
        else:
            print("签署完成回调校验失败")
            return False

    # 过期
    if status == 3:
        if len(cb) > 0:
            start = 0
            overtime = 0
            remind = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START" and callBackProcessVO['flowStatus'] == 1 and \
                        callBackProcessVO[
                            'signerList'] is not None and callBackProcessVO['nextSignerList'] is not None:
                    start = 1
                    print("发起签署回调校验成功")
                    continue
                if (content['callBackDesc']) == "SIGN_FLOW_OVERTIME":
                    if callBackProcessVO['flowStatus'] == 3 and callBackProcessVO[
                        'signerList'] is not None and \
                            callBackProcessVO['signValidity'] is not None:
                        overtime = 1
                        print("流程过期校验成功")
                        continue
                # if (content['callBackDesc']) == "SIGN_FLOW_EXPIRE_REMIND":
                #     if callBackProcessVO['flowStatus'] == 2 and callBackProcessVO[
                #         'signerList'] is not None and \
                #             callBackProcessVO['processBeginTime'] is not None:
                #         print("签署人签署完成回调校验成功")
                #         remind = 1
                #         break
            if start == 1 and overtime == 1 and remind == 1:
                print("过期回调校验成功")
                return True
            else:
                print("过期回调校验失败")
                return False

    # 截止前通知 7,5,3,1天时发送截止前通知
    if status == 4:
        if len(cb) > 0:
            start = 0
            overtime = 0
            remind = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START" and callBackProcessVO['flowStatus'] == 1 and \
                        callBackProcessVO[
                            'signerList'] is not None and callBackProcessVO['nextSignerList'] is not None:
                    start = 1
                    print("发起签署回调校验成功")
                    continue
                # if (content['callBackDesc']) == "SIGN_FLOW_OVERTIME":
                #     if callBackProcessVO['flowStatus'] == 3 and callBackProcessVO[
                #         'signerList'] is not None and \
                #             callBackProcessVO['signValidity'] is not None:
                #         overtime = 1
                #         print("流程过期校验成功")
                #         break
                if (content['callBackDesc']) == "SIGN_FLOW_EXPIRE_REMIND":
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO[
                        'signerList'] is not None and \
                            callBackProcessVO['processBeginTime'] is not None:
                        print("签署人签署完成回调校验成功")
                        remind = 1
                        continue
            if start == 1 and remind == 1:
                print("签署截止前通知回调校验成功")
                return True
            else:
                print("签署截止前通知回调校验失败")
                return False

    # 拒签
    if status == 5:
        if len(cb) > 0:
            start = 0
            refuse = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START":
                    # print(type(content['callBackProcessVO']))
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO[
                        'signerList'] is not None and \
                            callBackProcessVO['nextSignerList'] is not None:
                        start = 1
                        print("发起签署回调校验成功")
                    continue
                if (content['callBackDesc']) == "SIGN_FLOW_REFUSE":
                    if callBackProcessVO['flowStatus'] == 4 and callBackProcessVO['refuseSigner'] is not None:
                        refuse = 1
                        print("签署人拒签回调校验成功")
                    continue
            if start == 1 and refuse == 1:
                return True
    # 作废
    if status == 6:
        if len(cb) > 0:
            # res = []
            start = 0
            cancel = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START":
                    # print(type(content['callBackProcessVO']))
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO[
                        'signerList'] is not None and \
                            callBackProcessVO['nextSignerList'] is not None:
                        start = 1
                        print("发起签署回调校验成功")
                    continue
                if (content['callBackDesc']) == "SIGN_FLOW_CANCEL":
                    if callBackProcessVO['flowStatus'] == 5 or callBackProcessVO['flowStatus'] == 6:
                        if callBackProcessVO['cancelActorVO'] is not None:
                            print("签署人作废回调校验成功")
                            cancel = 1
                    continue
            if start == 1 and cancel == 1:
                return True
            else:
                return False

    # 签署失败
    if status == 7:
        if len(cb) > 0:
            start = 0
            failed = 0
            for item in cb:
                content = json.loads(item['content'])
                callBackProcessVO = content['callBackProcessVO']
                if (content['callBackDesc']) == "SIGN_FLOW_START":
                    # print(type(content['callBackProcessVO']))
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO[
                        'signerList'] is not None and \
                            callBackProcessVO['nextSignerList'] is not None:
                        start = 1
                        print("发起签署回调校验成功")
                    continue
                if (content['callBackDesc']) == "SIGNED_FLOW_SIGN_FAILED":
                    if callBackProcessVO['flowStatus'] == 1 and callBackProcessVO['signFailedSignerList'] is not None:
                        print("签署失败回调校验成功")
                        failed = 1
                    continue
            if start == 1 and failed == 1:
                print("签署失败回调校验成功")
                return True
            else:
                print("签署失败回调校验失败")
                return False
    

if __name__ == '__main__':
    callback_check(2, "7378af4ff73ecdd4b0f6f2e2e331ac11")
