import sys
import requests
from tools.get_ca_signature import hmac_sha256_encrypt
from tools.RandomGen import generate_random_str, generate_random, generate_random_chinese
from tools.cardGen import get_idNo
import json
import hmac
from hashlib import sha256

from utils import ENV

PROJECT_SECRET = ENV('esign.projectSecret')
PROJECT_ID = ENV('esign.projectId')

def hmac_sha256_encrypt(data, secret):
    appsecret = secret.encode('utf-8')  # 秘钥
    if type(data) == dict or type(data) == list:
        data = json.dumps(data)
    str0 = data.encode('utf-8')  # 加密数据
    signature = hmac.new(appsecret, str0, digestmod=sha256).hexdigest()
    return signature


def gen_headers_signature(data):
    signature = hmac_sha256_encrypt(data, PROJECT_SECRET)
    headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
               "x-timevale-signature": signature}
    return headers

# 查询内部用户详情-通过userCode精确查询
def detailInnerUsers(esignManageOpenApiUrl, projectId, projectSecrect, userCode):
     try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/detail"
        data = {"userCode": userCode}

        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data'][0]
     except:
         print(sys._getframe().f_code.co_name + "-调用异常")
         return None

# 创建内部用户
def createInnerUserCode(esignManageOpenApiUrl, projectId, projectSecrect, name, organizationCode):
    '''
    生成内部用户并返回userCode
    :param organizationCode: 内部用户所属机构
    :return: userCode
    '''
    # try:
    Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/create"
    s1 = generate_random(9)
    s2 = generate_random_str(5)
    licenseNo = get_idNo()
    print("Url == 》" + Url)
    if name is None:
        name = '测试' + generate_random_chinese(1)
    if organizationCode:
        data = [{
                "bankCardNo": "",
                "customAccountNo": 'cs' + s2,
                "email": s1 + "@tsign.cn",
                "licenseNo": None,
                "licenseType": "ID_CARD",
                "mainOrganizationCode": organizationCode,
                "mobile": "12" + s1,
                "name": name
            }]
    print('创建内部用户:' + str(data))
    # signature = hmac_sha256_encrypt(str(data), projectSecrect)
    # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
    #            "x-timevale-signature": signature}
    # headers = gen_headers_signature(data)
    # print(Url)
    res = requests.post(Url, json=data, headers=gen_headers_signature(data))
    # print(data)
    # print(gen_headers_signature(data))
    print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
    if res.json()['data']:
        # 判定接口调用成功
        return res.json()['data']['successData'][0]['userCode']
    # except:
    #     print(sys._getframe().f_code.co_name + "-调用异常")
    #     return None

# 获取一个删除的内部个人用户
# param:None 则获取一个新用户并删除； param 有值，则将这个用户修改为删除状态
def getDeleteInnerUserCode(esignManageOpenApiUrl, projectId, projectSecrect, userCode, organizationCode):
    '''
    获取一个删除的内部个人用户
    :param userCode: 需要删除的用户的编码
    :param organizationCode: 未指定删除用户的时候，新增用户需要的所属组织编码
    :return:
    '''
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/delete"
        if userCode is None:
            userCode = createInnerUserCode(esignManageOpenApiUrl, projectId, projectSecrect, None, organizationCode)
        data = {"userCode": userCode, "customAccountNo": ""}
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['code'] == 200:
            print("userCode:" + userCode)
            return userCode
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

def deleteInnerUserCode(esignManageOpenApiUrl, projectId, projectSecrect, userCode):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/delete"
        data = {"userCode": userCode, "customAccountNo": ""}
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['code'] == 200:
            print("delete user success")
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")

#更新用户名称
def updateInnerUserName(esignManageOpenApiUrl, projectId, projectSecrect, userCode,updateName):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/update"
        if updateName is None:
            updateName = '测试' + generate_random_chinese(1)
        data = {
            "userCode": userCode,
            "name": updateName
        }
        print('xxxx:' + str(data))
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")

# 创建内部机构
def createInnerOrgCode(esignManageOpenApiUrl, projectId, projectSecrect,name):
    '''
    生成内部用户并返回userCode
    :param organizationCode: 内部用户所属机构
    :return: userCode
    '''
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerOrganizations/create"
        s2 = generate_random_str(5)
        if name is None:
            name = 'esigntest自动化创建机构' + generate_random_chinese(4)
        data = {
                "customOrgNo": 'customOrgNo' + s2,
                "licenseNo": "",
                "licenseType": "CREDIT_CODE",
                "organizationType": "COMPANY",
                "parentCode": "0",
                "name": name
            }

        print('创建机构:' + str(data))
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['organizationCode']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

def updateInnerOrgName(esignManageOpenApiUrl, projectId, projectSecrect,orgCode,updateOrgName):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerOrganizations/update"
        if updateOrgName is None:
            updateOrgName = '测试' + generate_random_chinese(1)
        data = {
            "organizationCode": orgCode,
            "name": updateOrgName
        }
        print('updateInnerOrgName:' + str(data))
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")

def deleteInnerOrg(esignManageOpenApiUrl, projectId, projectSecrect,orgCode):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerOrganizations/delete"
        data = {"organizationCode": orgCode, "customAccountNo": ""}
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['code'] == 200:
            print("delete org success")
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")


# 创建内部机构带法人
def createInnerLegalOrgCode(esignManageOpenApiUrl, projectId, projectSecrect,name,legalRepUserCode):
    '''
    生成内部用户并返回userCode
    :param organizationCode: 内部用户所属机构
    :return: userCode
    '''
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerOrganizations/create"
        s2 = generate_random_str(5)
        if name is None:
            name = 'esigntest自动化创建机构' + generate_random_chinese(4)
        data = {
                "customOrgNo": 'customOrgNo' + s2,
                "licenseNo": "",
                "licenseType": "CREDIT_CODE",
                "organizationType": "COMPANY",
                "parentCode": "0",
                "name": name,
                "legalRepUserCode": legalRepUserCode
            }

        print('创建机构:' + str(data))
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['organizationCode']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 创建兼职企业内部用户
def createInnerUserCodeOtherOrg(esignManageOpenApiUrl, projectId, projectSecrect, name, organizationCode,otherOrganization):
    '''
    生成内部用户并返回userCode
    :param organizationCode: 内部用户所属机构
    :return: userCode
    '''
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerUsers/create"
        s1 = generate_random(9)
        s2 = generate_random_str(5)
        licenseNo = get_idNo()
        if name is None:
            name = '测试' + generate_random_chinese(1)
        if organizationCode:
            data = [
                {
                    "bankCardNo": "",
                    "customAccountNo": 'cs' + s2,
                    "email": s1 + "@tsign.cn",
                    "licenseNo": None,
                    "licenseType": "ID_CARD",
                    "mainOrganizationCode": organizationCode,
                    "mobile": "12" + s1,
                    "name": name,
                    "otherOrganization": otherOrganization
                }
            ]
        print('创建内部用户:' + str(data))
        # signature = hmac_sha256_encrypt(str(data), projectSecrect)
        # headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
        #            "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=gen_headers_signature(data))
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data']['successData'][0]['userCode']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# 查询内部组织详情-通过organizationCode精确查询
def detailInnerOrganizations(esignManageOpenApiUrl, projectId, projectSecrect, organizationCode):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/innerOrganizations/detail"
        data = {"organizationCode": organizationCode}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data'][0]
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 查询外部用户详情-通过userCode精确查询
def detailOuterUsers(esignManageOpenApiUrl, projectId, projectSecrect, userCode):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/outerUsers/detail"
        data = {"userCode": userCode}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data'][0]
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 查询外部组织详情-通过organizationCode精确查询
def detailOuterOrganizations(esignManageOpenApiUrl, projectId, projectSecrect, organizationCode):
    try:
        Url = esignManageOpenApiUrl + "/manage/v1/outerOrganizations/detail"
        data = {"organizationCode": organizationCode}
        signature = hmac_sha256_encrypt(data, projectSecrect)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
                   "x-timevale-signature": signature}
        res = requests.post(Url, json=data, headers=headers)
        # print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        if res.json()['data']:
            # 判定接口调用成功
            return res.json()['data'][0]
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


if __name__ == '__main__':
    # esignManageOpenApiUrl = "http://tianyin6-stable.tsign.cn"
    # projectId = "1000000"
    # projectSecrect = ""
    # print(getDeleteInnerUserCode(esignManageOpenApiUrl, projectId, projectSecrect, None, "30fb6c46ca544b73acc5488d83f59550"))
    # print(createInnerOrgCode(esignManageOpenApiUrl, projectId, projectSecrect ))
    # print("哈哈" + detailInnerUsers(esignManageOpenApiUrl,projectId, projectSecrect,'ceshyylly'))
    json.dumps({"2":"1"})