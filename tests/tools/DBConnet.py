# -*-coding:utf-8-*-
# <AUTHOR> wenmin
# @Time : 2022/5/26 15:54
# @Description : mysql数据库的连接
from sqlite3 import Cursor
import pymysql

def connect(host,port,user,passwd,dbname):
    """
    创建数据库连接
    :param host,prot,user,passwd,dbname
    :return: db 数据库连接
    """
    try:
        port = int(port)
        db = pymysql.connect(
            host=host,
            port=port,
            user=user,
            passwd=passwd,
            db=dbname,
            charset='utf8'
        )
        return db
    except Exception:
        raise Exception("数据库连接失败")

def execute_sql_open(connection: pymysql.Connection, sql: str):
    """
    执行sql语句 不关闭连接
    :param connection: 数据库连接
    :type connection: object
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        result = cursor.fetchone()
        connection.commit()
        # print("查询结果:", result)
        return result
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()

def execute_sql_close(connection: pymysql.Connection, sql: str):
    """
    执行sql语句 并关闭连接
    :param connection: 数据库连接
    :type connection: pymysql.Connection
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    execute_sql_open(connection, sql)
    connection.close()

def execute_sql_convert_object(connection: pymysql.Connection, sql: str):
    """
    执行sql，返回唯一记录，并根据表中字段名，将tuple格式转化成dict格式
    :param connection: 数据库连接
    :type connection: object
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        data = cursor.fetchone()
        print("查询结果:", data)
        pojo = dict()
        if data:
            index = 0
            for desc in cursor.description:
                pojo[desc[0]] = data[index]
                index = index + 1
        connection.commit()
        return pojo
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()

def execute_sql_convert_array(connection: pymysql.Connection, sql: str):
    """
    执行sql，返回多条记录，并根据表中字段名，将tuple格式转化成dict格式
    :param connection: 数据库连接
    :type connection: pymysql.Connection
    :param sql: sql语句
    :type sql: str
    :return: res 结果集
    :rtype: list
    """
    print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        data = cursor.fetchall()
        
        # 转换结果类型
        index_dict = get_index_dict(cursor)
        res = []
        for datai in data:
            resi = dict()
            for indexi in index_dict:
                resi[indexi] = datai[index_dict[indexi]]
            res.append(resi)
        connection.commit()
        print("查询结果:", res)
        return res
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()

def get_index_dict(cursor: Cursor):
    """
    获取数据库对应表中的字段名
    :param cursor: 游标
    :type cursor: object
    :return: index_dict 字段名集合
    :rtype: list
    """
    index_dict = dict()
    index = 0
    for desc in cursor.description:
        index_dict[desc[0]] = index
        index = index + 1
    return index_dict


if __name__ == '__main__':
    host = 'test-nodeport.tsign.cn'
    port = 32103
    user = 'root'
    passwd = 'Q0ct4ab3gP1'
    dbname = 'esign6_manage'
    sql = "select * from wf_todo_task where workflow_config_code = 'd30871d92e836e24be9dbe8bf569ffcb'"

    connect0 = connect(host,port,user,passwd,dbname)
    res = execute_sql_convert_object(connect0,sql)
    print(str(res))
    res = execute_sql_convert_array(connect0, sql)
    print(str(res))
    
    
    

