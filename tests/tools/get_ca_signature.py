import hashlib
import hmac
import base64
import json


def hmac_sha256(app_secret,message):
    message = bytes(message.encode('utf-8'))
    secret = bytes(app_secret.encode('utf-8'))

    signature = base64.b64encode(hmac.new(secret, message, digestmod=hashlib.sha256).digest())
    return signature.decode()


def gen_signature(app_secret,httpMethod,accept,content_md5,content_type,date,headers,url):
    str_sign = "%s\n%s\n%s\n%s\n%s\n%s%s" % (httpMethod,accept,content_md5,content_type,date,headers,url)
    signature = hmac_sha256(app_secret,str_sign)

    return signature

def hmac_sha256_encrypt(data, secret):
    appsecret = secret.encode('utf-8')  # 秘钥
    if type(data) == dict:
        data = json.dumps(data)
    str0 = data.encode('utf-8')  # 加密数据
    signature = hmac.new(appsecret, str0, digestmod=hashlib.sha256).hexdigest()
    return signature
