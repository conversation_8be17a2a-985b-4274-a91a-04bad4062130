# 静默签
import os

import ddddocr
import debugtalk
import requests
import json
import re

from httpRequest.service.loginService import LoginService
from utils import ENV

env = ENV('env')
esign_web_url = ENV('esign.projectHost')
esign_open_api_url = ENV('esign.gatewayHost')
project_id = ENV('esign.projectId')
project_secrect = ENV('esign.projectSecret')
dbHost = ENV('esign.dbHost')
dbPort = ENV('esign.dbPort')
dbUser = ENV('esign.dbUser')
dbPassword = ENV('esign.dbPassword')
db_signs = ENV('esign.db.signs')


# def getTokenWeb(account=None, password=None):
#     """
#     获取内部用户token
#     :param account:
#     :param password:
#     :return:
#     """
#     loginService = LoginService(ENV('esign.projectHost'))
#     if not (account or password):
#         account = ENV('sign01.accountNo')
#         password = ENV('passwordEncrypt')
#     token = loginService.createToken(ENV('esign.projectId'), account, password)
#     return token


def create_outerSigner_token2(phoneOrMail):
    """
    创建外部用户token
    :param phoneOrMail: 加密后的手机号，或者邮箱
    :return:
    """
    loginService = LoginService(ENV('esign.projectHost'))
    # token = loginService.getOuterSignerToken(phoneOrMail)
    token = loginService.verCodeLogin(phoneOrMail, 2)
    return token


# 获取token
def getPortalToken(userCode, password):
    # 先从数据库获取
    token = get_token(userCode)

    # 若不存在则生成
    if token == None:
        # 生成token
        token = create_token(userCode, password)
        # 验证token 是否有效
        if check_token(token):
            # 新token落库
            save_token(userCode, token)
            return token

    # 若存在，防止过期，验证是否有效
    if check_token(token):
        return token
    else:
        # 失效 则表示需要重新生成 并更新数据库
        token = create_token(userCode, password)
        # 再次验证token 是否有效
        if check_token(token):
            # 更新数据库
            save_token(userCode, token)
            return token

    return None


# 获取图形验证码:type:0-只获取图形验证码；1-只获取头信息code;2-全部的信息
def get_verificationCode_new(type):
    try:
        response = requests.get(url=esign_web_url + '/sso/sendVerifyCode', headers=None)
        if type == 1:
            return response.headers['verificationCode']
        else:
            with open(verify_code_image_path, 'wb') as f:
                f.write(response.content)
            # verification_code = get_verification_code()
            verification_code = '9999'
            if len(verification_code) == 4 and verification_code.isdigit():
                if type == 0:
                    return verification_code
                if type == 2:
                    return verification_code, response.headers['verificationCode']
    except:
        print('图形验证码获取失败')
        return 9999


# 生成token
def create_token(account_number, password):
    # 触发获取图形验证码
    verificationCodeHeadr = get_verificationCode_new(1)
    url = esign_web_url + "/sso/accountLogin"
    data = {"platform": "pc", "referer": "", "account": account_number, "password": password, "type": 1,
            "verificationCode": "9999"}
    headers = {"Content-Type": "application/json", "x-timevale-project-id": project_id,
               "verificationCode": verificationCodeHeadr}
    res = requests.post(url, json=data, headers=headers)
    try:
        if res is None:
            create_token(account_number, password)
        else:
            if json.loads(res.text)["data"] is not None:
                jump_url = json.loads(res.text)["data"]["jumpUrl"]
                token = re.match(r'.*token=(.*?)&', jump_url, re.M | re.I)
                return token.group(1)

    except:
        return None


# 检测token 是否生效
def check_token(token):
    try:
        # 获取相对方是否付费配置，接口逻辑简单故选此作为校验，只需要验证token是否有效
        Url = esign_web_url + "/esign-signs/opposite/organization/info?organizationCode=1"
        headers = {"Content-Type": "application/json", "x-timevale-project-id": project_id,
                   "authorization": token}
        res = requests.get(url=Url, headers=headers)
        status0 = json.loads(res.text)["status"]
        if status0 == 401 or status0 == 402 or status0 == 404:
            return False
        else:
            return True
    except:
        return False


# 从获取token
def get_token(userCode):
    try:
        key = "AUTO_TEST_" + userCode

        sql = "select message_config from " + db_signs + ".business_type where uuid = '%s'" % key
        return debugtalk.get_execute_sql(sql)
    except:
        print("获取token出错了----------！")
        return None


# 存入token
def save_token(userCode, token):
    oldToken = get_token(userCode)
    key = "AUTO_TEST_" + userCode
    sql = ""
    # 历史是否存在，若存在则更新，否则添加
    if oldToken != None:
        sql = "update " + db_signs + ".business_type " + "set message_config='" + token + "' where uuid = '%s'" % key
    else:
        sql = " INSERT INTO " + db_signs + ".business_type(UUID,business_type_name,modified_user_code,creator_organize_code,business_enable,signer_range,evidence_type,seal_type,hand_enable,ai_hand_enable,sign_end_time_enable,download_enable,deleted,gmt_create,gmt_modified,un_disturb_flag,message_config) VALUE ('" + key + "','自动化测试token存储勿动','11','22',0,1,1,1,0,0,0,0,1,NOW(),NOW(),0,'%s')" % token

    debugtalk.executeSql(sql)


def get_outerSigner_token(account, userCode, esignSignsWebUrl, referer):
    # 先从数据库获取
    # token = get_token(account)
    token = get_token(userCode)

    # 若不存在则生成
    if not token:
        # 生成token
        token = create_outerSigner_token(account)
        # 验证token 是否有效
        if check_token(token):
            # 新token落库
            # save_token(account,token)
            save_token(userCode, token)
            return token

    # 若存在，防止过期，验证是否有效
    if check_token(token):
        return token
    else:
        # 失效 则表示需要重新生成 并更新数据库
        token = create_outerSigner_token(account)
        # 再次验证token 是否有效
        if check_token(token):
            # 更新数据库
            save_token(account, token)
            return token

    return None


def create_outerSigner_token(account, userCode, esignSignsWebUrl, referer):
    url = esignSignsWebUrl + "/sso/login"
    verification_code, vertification_code_header = get_verificationCode(esignSignsWebUrl)
    DynamicCode = get_DynamicCode(verification_code, vertification_code_header, account, esignSignsWebUrl)
    # DynamicCode = get_DynamicCode('9999', vertification_code_header,account, esignSignsWebUrl)
    data = {"account": account,
            "verificationCode": verification_code,
            "dynamicCode": "123456",
            "accountType": 4,
            "platform": "pc",
            "referer": referer,
            "userCode": userCode,
            "userTerritory": "2"}
    headers = {"Content-Type": "application/json", "verificationCode": vertification_code_header}
    res = requests.post(url, json=data, headers=headers)
    print('create_outerSigner_token-获取相对方用户的token----->', userCode, ':', res.text)
    try:
        if json.loads(res.text)["data"] is not None:
            jump_url = json.loads(res.text)["data"]["jumpUrl"]
            token = re.match(r'.*token=(.*?)&', jump_url, re.M | re.I)
            return token.group(1)
        else:
            create_outerSigner_token(account, userCode, esignSignsWebUrl, referer)

    except:
        return None


verify_code_image_path = 'verifyCode.png'


def get_verificationCode(esignSignsWebUrl):
    try:
        response = requests.get(url=esignSignsWebUrl + '/sso/sendVerifyCode')

        print('=====获取图形验证码====')
        with open(verify_code_image_path, 'wb') as f:
            f.write(response.content)
        vertification_code_header = response.headers['verificationCode']
        # verification_code = get_verification_code()
        verification_code = '9999'
        print(verification_code)
        if len(verification_code) == 4 and verification_code.isdigit():
            return verification_code, vertification_code_header
        else:
            get_verificationCode(esignSignsWebUrl)

    except:
        return None


def get_verification_code():
    # ocr = ddddocr.DdddOcr()
    # with open(verify_code_image_path, 'rb') as f:
    #     img_bytes = f.read()
    # verification_code = ocr.classification(img_bytes)
    verification_code = '9999'
    return verification_code

def get_token_web(account=None, password=None):
    """
    获取内部用户token
    :param account:
    :param password:
    :return:
    """
    loginService = LoginService(esign_web_url)
    if account == 'ceswdzxzdhyhwgd1.account' or not (account or password):
        account = ENV('ceswdzxzdhyhwgd1.account')
        password = ENV('ceswdzxzdhyhwgd1.password')
    token = loginService.createToken(project_id, account, password).token
    return token


def get_DynamicCode(verification_code, vertification_code_header, account, esignSignsWebUrl):
    url = esignSignsWebUrl + "/sso/sendDynamicCode"
    data = {
        "account": account,
        "platform": "pc",
        "accountType": 4,
        "verificationCode": verification_code,
        "scene": 7,
        "userTerritory": "2"
    }
    headers = {"Content-Type": "application/json", "verificationCode": vertification_code_header}
    res = requests.post(url, json=data, headers=headers)
    print(res)


def get_manage_token():
    """
    管理平台获取token
    :return:
    """
    loginService = LoginService(esign_web_url)
    token = loginService.getManageToken(ENV('manage.account'), ENV('manage.password'))
    return token
