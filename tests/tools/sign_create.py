import requests
import random


from utils import ENV

env = ENV('env')
projectHost = ENV('esign.projectHost')
project_id = ENV('esign.projectId')


def createBusiness(authorization: str):
    businessTypeName = "自动化测试业务类型" + str(random.randint(100000, 999999))

    data = {
        "params": {
            "businessTypeName": businessTypeName
        }
    }
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "x-timevale-project-id": project_id,
        "authorization": authorization
    }
    response = requests.post(
        url=projectHost + '/esign-signs/businessType/addBusinessType', json=data, headers=headers)
    jsonResponse = response.json()
    status = jsonResponse['status']
    assert status == 200, "创建签署业务类型失败"
    businessTypeId = jsonResponse['data']['businessTypeId']
    return businessTypeId


def updateBusinessType(authorization: str, businessTypeId: str, isOpen: int):
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "x-timevale-project-id": project_id,
        "authorization": authorization
    }
    data = {
        "params": {
            "businessTypeId": businessTypeId
        }
    }

    response = requests.post(
        url=projectHost + '/esign-signs/businessType/getBusinessTypeDetail', json=data, headers=headers)
    jsonResponse = response.json()
    status = jsonResponse['status']
    assert status == 200, "获取业务详情失败"
    data = jsonResponse['data']
    data["businessEnable"] = str(isOpen)

    params = {"params": data}
    response = requests.post(
        url=projectHost + '/esign-signs/businessType/updateBusinessType', json=params, headers=headers)

    jsonResponse = response.json()
    status = jsonResponse['status']
    assert status == 200, "更新签署业务类型失败:" + jsonResponse['message']
    return True
