import hashlib
import os
import sys
from functools import lru_cache
from time import sleep, time
import requests
import debugtalk

from utils import ENV

PROJECT_SECRET = ENV('esign.projectSecret')
PROJECT_ID = ENV('esign.projectId')

sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep
esignSignsOpenApiUrl = ENV('esign.gatewayHost')

@lru_cache(128)
def getFilekeyByType2(param):
    if param == 1:
        return get_filekey_by_filename("一页文档.pdf")
    if param == 2:
        return get_filekey_by_filename("二页文档.pdf")
    if param == 3:
        return get_filekey_by_filename("三页文档.pdf")
    if param == 4:
        return get_filekey_by_filename("扫描件.pdf")
    if param == 5:
        return get_filekey_by_filename("横版PDF.pdf")
    if param == 6:
        return get_filekey_by_filename("500页横竖PDF.pdf")
    if param == 8:
        return get_filekey_by_filename("编辑密码-PI3.1415926.pdf")
    if param == 9:
        return get_filekey_by_filename("3000页.pdf")
    if param == 10:
        return get_filekey_by_filename("key30page.pdf")
    if param == 11:
        return get_filekey_by_filename("劳动合同书.pdf.PDF.PDF.pdf")
    if param == 12:
        return get_filekey_by_filename("后缀.PDF")
    if param == 13:
        return get_filekey_by_filename("key30page.ofd")


# def file_header(form_texts,form_size, project_id, project_secret):
#     req_body_data = '&'.join(f"{key}={value}" for key, value in form_texts.items())
#
#     req_signature = hashlib.sha256((req_body_data + project_secret).encode()).hexdigest()
#     boundary = f"---{int(time() * 1000)}---"
#     header = {
#         "X-timevale-project-id": project_id,
#         "X-timevale-signature": req_signature,
#         "Content-Length": str(form_size),
#         # "Content-Type": f"multipart/form-data; boundary={boundary}",
#         "Accept": "*/*"
#     }
#
#     print(f"Header: {header}")
#     return header

def file_header(form_texts, project_id ):
    # req_body_data = '&'.join(f"{key}={value}" for key, value in form_texts.items())

    req_signature = debugtalk.get_signature_with_fileUpload(form_texts)

    header = {
        "X-timevale-project-id": project_id,
        "X-timevale-signature": req_signature
    }

    print(f"Header: {header}")
    return header

@lru_cache(128)
def calculate_file_hash(file_path):
    """Calculate the SHA-256 hash of the file."""
    with open(file_path, 'rb') as f:
        return hashlib.sha256(f.read()).hexdigest()

@lru_cache(128)
def get_absolutePath_by_filename(file_name):
    try:
        file_name_path = os.path.basename(file_name)
        if cur_dir.endswith('tests\\') or cur_dir.endswith('tests/'):
            file_path = os.path.join('data', file_name_path)
        else:
            basePath = os.path.dirname(__file__)
            if basePath.endswith('tools'):
                basePath = basePath[:len(basePath)-5]
            file_path = os.path.join(basePath, 'data', file_name_path)
        return os.path.join(cur_dir, file_path)
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

# # 根据文件名上传文件获取fileKey
# @lru_cache(128)
# def get_filekey_by_filename(file_name):
#     try:
#         file_path2 = get_absolutePath_by_filename(file_name)
#         print("上传文件-绝对路径：", file_path2)
#         url = esignSignsOpenApiUrl + '/file/v1/uploadWithAuth'
#         bodys = {"file": file_path2}
#         file_hash = calculate_file_hash(file_path2)
#         form_texts = {"hash": file_hash}
#         form_size = os.path.getsize(file_path2)
#         headers = file_header(form_texts, form_size, PROJECT_ID, PROJECT_SECRET)
#
#         with open(bodys['file'], 'rb') as file:
#             files = {'file': file}
#             res = requests.post(url, headers=headers, data=form_texts, files=files)
#         # file = {'file': open(file_path2, 'rb')}
#         # res = requests.post(url=url, headers=headers, data=form_texts, files=file)
#         print("upload-[respone]：", res.json())
#         if res.json()['data']:
#             # 判定接口调用成功
#             print("XXXXX=====:",res.json()['data']['fileKey'])
#             return res.json()['data']['fileKey']
#     except:
#         print(sys._getframe().f_code.co_name + "-调用异常")
#         return None

def get_filekey_by_filename(file_name):
    try:
        file_path2 = get_absolutePath_by_filename(file_name)
        print("上传文件-绝对路径：", file_path2)
        url = esignSignsOpenApiUrl + '/file/v1/uploadWithAuth'
        bodys = {"file": file_path2}
        file_hash = calculate_file_hash(file_path2)
        form_texts = {"hash": file_hash, "filePwd": ""}
        headers = file_header(form_texts, PROJECT_ID)

        with open(bodys['file'], 'rb') as file:
            files = {'file': file}
            res = requests.post(url, headers=headers, data=form_texts, files=files)
        # file = {'file': open(file_path2, 'rb')}
        # res = requests.post(url=url, headers=headers, data=form_texts, files=file)
        print("upload-[respone]：", res.json())
        if res.json()['data']:
            # 判定接口调用成功
            print("XXXXX=====:",res.json()['data']['fileKey'])
            return res.json()['data']['fileKey']
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None