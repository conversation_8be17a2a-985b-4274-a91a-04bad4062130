- config:
    name: "创建单方业务模板:权限为所有,内部个人(非自动签署),有内容域"
    variables:
      presetNameCommon: "自动化测试业务模板${get_randomNo()}"
    output:
      - presetIdCommon
      - presetNameCommon
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon
      - businessTypeIdCommon


- test:
    name: "TC1-创建企业模板"
    testcase: common/template/buildTemplate5.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "TC2-获取用户详情"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC3-添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $presetNameCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-setup-获取业务模版PresetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $presetNameCommon
      - page: 1
      - size: 10
    extract:
      - presetIdCommon: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC6-业务模板添加详情"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - presetId: $presetIdCommon
      - presetName: $presetNameCommon
      - initiatorAll: 1
      - multiSigner: 0
      - templateId: $newTemplateUuidCommon
      - templateName: $templateNameCommon
      - version: $newVersionCommon
      - checkRepetition: 1
      - initiatorList: []
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC10-添加业务模板配置签署人"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $presetIdCommon,
        "status": 1,
        "needGather": 0,
        "needAudit": 0,
        "allowAddSigner": 0,
        "sort": 0,
        "signerList": [
          {
            "autoSign": 0,
            "organizeCode": "",
            "sealTypeCode": "",
            "signatoryList": [
            ],
            "signerTerritory": 1,
            "signerType": 1,
            "userCode": "",
            "sealTypeName": "",
            "departmentCode": ""
          }
        ],
        "signerNodeList": [{
            "signerList": $signerList,
            "signMode": 0,
            "id": "node-1"
        }]
      }

    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]