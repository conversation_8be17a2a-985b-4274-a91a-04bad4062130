- config:
    name: 创建业务模板配置-电子业务模板
    variables:
      sealCodeCommon: ${getDateTime()}
      initiatorAll: $initiatorAll
      initiatorList: $initiatorList
      templateList: $templateList
      #其他需要传的参数,不要放开注释
      presetStatus_common_addSigners: 1 #自动启用
      contractExpireSetting: {\"enabled\":0}
#      allowAddFile: 1
#      queryPresetName: "1682663282632"
#      fileFormat: 2 #1-PFD;2-OFD
#      signAreaSignEnable: 1 #1-必签；0-非必签
#      signAreaMoveEnable: 1 #1-可移动；0-不可移动
#      handEnable: 1
#      aiHandEnable: 1
#      signEndTimeEnable: 1
#      forceReadingTime: 0
#      downloadEnable: 1
#      allowAddSigner: 1   #允许发起时设置签署方 0不允许 1允许
#      signerNodeList: null

    export:
      - presetId00
      - businessTypeId00

- test:
    name: TC1-创建
    api: api/esignDocs/businessPreset/create.yml
    variables:
      presetName: $queryPresetName

- test:
    name: TC2-列表查询-通过名称查询
    api: api/esignDocs/businessPreset/list.yml
    variables:
        queryPresetName: $queryPresetName
        status:
    extract:
      - presetId00: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $queryPresetName ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetId00
    extract:
      - businessTypeId00: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.signBusinessType.businessTypeName, $queryPresetName ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: TC4-列表查询-停用
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $presetId00
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC5-step1:填写基本信息
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId: $presetId00
      presetName: $queryPresetName
      fileFormat: $fileFormat
      initiatorAll: $initiatorAll
      initiatorList: $initiatorList
      templateList: []
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC6-step2:签署方式
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      presetId: $presetId00
      businessTypeName: $queryPresetName
      businessTypeId: $businessTypeId00
      signAreaSignEnable: $signAreaSignEnable
      signAreaMoveEnable: $signAreaMoveEnable
      handEnable: $handEnable
      aiHandEnable: $aiHandEnable
      signEndTimeEnable: $signEndTimeEnable
      forceReadingTime: $forceReadingTime
      downloadEnable: $downloadEnable
      contractExpireSetting: $contractExpireSetting
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC7-step3:设置签署方式
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId00
      presetName: $queryPresetName
      fileFormat: $fileFormat
      presetVersion: 0
      allowAddSigner: $allowAddSigner
      signerNodeList: $signerNodeList
      status: 0
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

#
#############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
#- test:
#    name: "templateDetail"
#    api: api/esignDocs/template/owner/templateDetail.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#    extract:
#      - _editUrl: content.data.editUrl
#      - _previewUrl: content.data.previewUrl
#      - _templateName: content.data.templateName
#      - autoTestDocUuid1Common: content.data.docUid
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#      - ne: ["content.data",""]
#
#- test:
#    name: "businessPreset_detail-业务模板详情"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      - getPresetId: $getPresetIdCommon
#    extract:
#      - _presetName_00: content.data.presetName
#      - _presetId_00: content.data.presetId
#      - _editUrl_00: content.data.editUrl
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status", 200]
#      - ne: ["content.data.presetId",""]
#
#- test:
#    name: "epaasTemplate-service-config"
#    api: api/esignDocs/epaasTemplate/service-config.yml
#    variables:
#      tplToken_config: ${getTplToken($_editUrl_00)}
#      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
#    extract:
#      - _contentId: content.data.contents.0.id
#      - _entityId: content.data.contents.0.entityId
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.code",0]
#      - ne: ["content.data",""]
#
#- test:
#    name: "epaasTemplate-detail"
#    api: api/esignDocs/epaasTemplate/detail.yml
#    variables:
#      tplToken_detail: ${ENV(_tmp_biz_template_001)}
#      contentId_detail: $_contentId
#      entityId_detail: $_entityId
#    extract:
#      - _baseFile: content.data.baseFile
#      - _originFile: content.data.originFile
#      - _fields: content.data.fields
#      - _label_0: content.data.fields.0.label
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.code",0]
#      - ne: ["content.data.originFile.resourceId",""]
#
#- test:
#    name: "获取参与方列表"
#    api: api/esignDocs/epaasTemplate/list-template-role.yml
#    variables:
#      - tplToken_role: ${ENV(_tmp_biz_template_001)}
#    extract:
#      - _signerId0: content.data.0.id
#      - _signerId1: content.data.1.id
#      - _signerName0: content.data.0.name
#      - _signerName1: content.data.1.name
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.code", 0]
#      - eq: ["content.data.0.id","0"]
#      - eq: ["content.data.0.roleTypes.0","FILL"]
#
#- test:
#    name: "epaasTemplate-batch-save-draft"
#    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
#    variables:
#      tplToken_draft: ${ENV(_tmp_biz_template_001)}
#      baseFile_draft: $_baseFile
#      originFile_draft: $_originFile
#      pageFormatInfoParam_draft: null
#      name_draft: $_templateName
#      contentId_draft: $_contentId
#      entityId_draft: $_entityId
#      _tmp_label_0: "${dataArraySort($_fields, type, 0, label)}"
#      _tmp_label_1: "${dataArraySort($_fields, type, 1, label)}"
#      _tmp_label_2: "${dataArraySort($_fields, type, 2, label)}"
#      _tmp_label_3: "${dataArraySort($_fields, type, 3, label)}"
#      _tmp_sign: [ {"label": "$_tmp_label_3","templateRoleId": "$_signerId1" }]
#      _tmp_fill: [ {"label": "$_tmp_label_1","templateRoleId": "$_signerId0" }]
#      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,$_tmp_fill,$_tmp_sign)}"
#    extract:
#      - _contentId: content.data.0.contentId
#      - _contentVersionId: content.data.0.contentVersionId
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.code",0]
#      - ne: ["content.data",""]

- test:
    name: setup-业务模板配置的第4步：设置签署方填写信息，将第一个内容域填写方设置为节点1签署方1填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $presetId00
      - status: $presetStatus_common_addSigners
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]