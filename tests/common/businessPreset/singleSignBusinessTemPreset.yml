#单方签署业务模板-两个签署区
- config:
    variables:
      autoPresetName: "单方签署自动化测试业务模板${get_randomNo()}"
      page: 1
      size: 20
      multiSigner: 0
      initiatorAll: 1
      checkRepetition: 0
    output:
      - signerIdACommon
      - getPresetIdCommon
      - organizationCodeCommon
      - organizationNameCommon
      - initiatorUserNameCommon
      - contentNameCommon
      - newTemplateUuidCommon
      - newVersionCommon

- test:
    name: "引用公共用例创建模板"
    testcase: common/template/buildTemplate5.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "TC1-setup-添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup-获取业务模版PresetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
    extract:
      - getPresetIdCommon: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup-业务模板添加详情"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - presetId: $getPresetIdCommon
      - presetName: $autoPresetName
      - signBusinessTypeId: $getBusinessTypeId
      - initiatorList: null
      - templateId: $newTemplateUuidCommon
      - templateName: $templateNameCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC4-setup-通过业务模板配置id获取签署区列表"
    api: api/esignDocs/businessPreset/getSignatoryList.yml
    variables:
      - presetId: $getPresetIdCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data.templateList.id",""]

- test:
    name: "TC5-setup-添加业务模板配置签署人--非自动签署--无采集--两个签署区"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $getPresetIdCommon,
        "status": 1,
        "needGather": 0,
        "needAudit": 0,
        "allowAddSigner": 0,
        "sort": 0,
        "signerList": [
          {
            "autoSign": 0,
            "organizeCode": "",
            "sealTypeCode": "",
            "signatoryList": [],
            "signerTerritory": 1,
            "signerType": 1,
            "userCode": "",
            "sealTypeName": "",
            "departmentCode": ""
          }
        ],
        "signerNodeList": [{
            "signerList": $signerList,
            "signMode": 0,
            "id": "node-1"
        }]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC6-setup-获取发起人信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $getPresetIdCommon
    extract:
      - departmentCode: content.data.list.0.departmentCode
      - departmentName: content.data.list.0.departmentName
      - organizationCodeCommon: content.data.list.0.organizationCode
      - organizationNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC7-setup-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $getPresetIdCommon
    extract:
      - signerIdACommon: content.data.signerList.0.signerId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]