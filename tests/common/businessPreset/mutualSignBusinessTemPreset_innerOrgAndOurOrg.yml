#创建业务模板-双方签署-内部企业指定签署方+外部企业签署-两个签署区
- config:
    variables:
      autoPresetName: "自动化测试业务模板${get_randomNo()}"
      page: 1
      size: 20
      multiSigner: 1
      initiatorAll: 1
      checkRepetition: 0
    output:
      - signerIdACommon
      - signerIdBCommon
      - getPresetIdCommon
      - contentNameCommon
      - newTemplateUuidCommon
      - newVersionCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例创建模板-2填写2签署"
    testcase: common/template/buildTemplate3.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common

      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup-添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup-获取业务模版PresetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
    extract:
      - getPresetIdCommon: content.data.list.0.presetId
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $getPresetIdCommon
    extract:
      - getBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "TC3-setup-业务模板添加详情"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - presetId: $getPresetIdCommon
      - presetName: $autoPresetName
      - signBusinessTypeId: $getBusinessTypeId
      - initiatorList: null
      - templateId: $newTemplateUuidCommon
      - templateName: $templateNameCommon
      - version: $newVersionCommon
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - eq: ["content.data", null]

- test:
    name: "TC5-setup-添加业务模板配置签署人--双方签署（内部企业指定签署方+外部企业签署）"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $getPresetIdCommon,
        "status": 0,
        "allowAddSigner": 0,
        "needGather": 0,
        "needAudit": 0,
        "sort": 0,
        "signerList": [
          {
            "autoSign": 0,
            "organizeCode": $organizationCodeCommon,
            "sealTypeCode": "",
            "signatoryList": [],
            "signerTerritory": 1,
            "signerType": 2,
            "userCode": $userCodeCommon,
            "sealTypeName": "",
            "departmentCode": $organizationCodeCommon
          },
          {
            "autoSign": 0,
            "organizeCode": "",
            "sealTypeCode": "",
            "signatoryList": [],
            "signerTerritory": 2,
            "signerType": 2,
            "userCode": "",
            "sealTypeName": "",
            "departmentCode": ""
          }
        ],
        "signerNodeList": [{
            "signerList": $signerList,
            "signMode": 0,
            "id": "node-1"
        }]
      }
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "businessPreset_detail-业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $getPresetIdCommon
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
      - _label_0: content.data.fields.0.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      _tmp_label_0: "${dataArraySort($_fields, type, 0, label)}"
      _tmp_label_1: "${dataArraySort($_fields, type, 1, label)}"
      _tmp_label_2: "${dataArraySort($_fields, type, 2, label)}"
      _tmp_label_3: "${dataArraySort($_fields, type, 3, label)}"
      _tmp_sign: [ {"label": "$_tmp_label_3","templateRoleId": "$_signerId1" }]
      _tmp_fill: [ {"label": "$_tmp_label_1","templateRoleId": "$_signerId0" }]
      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,$_tmp_fill,$_tmp_sign)}"
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: setup-业务模板配置的第4步：设置签署方填写信息，将第一个内容域填写方设置为节点1签署方1填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetIdCommon
      - status: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data", null ]

- test:
    name: "TC7-setup-获取批量发起选择页业务模板配置详情"
    api: api/esignDocs/businessPreset/choseDetail.yml
    variables:
      - presetId: $getPresetIdCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.presetId", $getPresetIdCommon ]