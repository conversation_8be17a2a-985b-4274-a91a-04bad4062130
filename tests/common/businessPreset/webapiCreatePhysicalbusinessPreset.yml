#说明：用印方式都是使用默认值，后续如果需要，可以再加
- config:
    name: 创建物理用印业务模板并启用
    variables:
#      调试使用参数，真实在用的时候需要注释掉；调用该公共用例时需要传入以下这些参数
      presetName_physical_commonCase: "测试业务模板物理-${get_randomNo_16()}"  #业务模板名称
      fileFormat_physical_commonCase: 1 #1-PFD;2-OFD    #签署文件类型
      supplementModelKey_physical_commonCase: "WDYT-BQSQ-SGT"  #补签审批流程
      workFlowModelKey_physical_commonCase: "WDYT-YYSQ-SGT"   #物理用印流程
      sealerList_physical_commonCase: [] #用印方列表
      allowAddSealer_physical_commonCase: 1 #是否允许发起时添加用印方
      allowSupplement: 1

    export:
      - presetId_physical_commonCase
      - businessTypeCode_physical_commonCase

- test:
    name: TC1-创建
    api: api/esignDocs/businessPreset/create.yml
    variables:
      presetName: $presetName_physical_commonCase
      presetType_businessPresetCreate: 1

- test:
    name: TC2-列表查询-通过名称查询
    api: api/esignDocs/businessPreset/list.yml
    variables:
        queryPresetName: $presetName_physical_commonCase
    extract:
      - presetId_physical_commonCase: content.data.list.0.presetId
      - businessTypeCode_physical_commonCase: content.data.list.0.businessTypeId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName_physical_commonCase ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC3-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetId_physical_commonCase
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 1 ]

- test:
    name: TC4-列表查询-停用
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $presetId_physical_commonCase
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step1:填写基本信息
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId: $presetId_physical_commonCase
      presetName: $presetName_physical_commonCase
      fileFormat: $fileFormat_physical_commonCase
      supplementModelKey: $supplementModelKey_physical_commonCase
      workFlowModelKey: $workFlowModelKey_physical_commonCase
      templateList: []
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step2:用印方式
    api: api/esignDocs/businessPreset/addSealConfig.yml
    variables:
      presetId_addSealConfig: $presetId_physical_commonCase
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step3:添加业务模板配置用印方
    api: api/esignDocs/businessPreset/addSealers.yml
    variables:
      presetId_addSealers: $presetId_physical_commonCase
      sealerList_addSealers: $sealerList_physical_commonCase
      allowAddSealer_addSealers: $allowAddSealer_physical_commonCase
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]
