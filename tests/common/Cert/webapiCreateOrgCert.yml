- config:
    name: 通过统一门户印控中心接口创建企业证书
    variables:
      organizationCodeInner: '4a0ea22d6ad44675b21ae3516e87a96f'  #这个参数需要调用方入参传入
      certAlgorithm: 1  #算法类型(1-RSA 2-SM2)调用方传入
    export:
      - CertIdOrganization
#
##- test:
##    name: TC2-查询内部用户-当前登录人的信息
##    api: api/esignManage/InnerUsers/detail.yml
##    variables:
##      customAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
##    extract:
##      - userCode2: content.data.0.userCode
##      - userName2: content.data.0.name
##      - mainOrganizationCode2: content.data.0.mainOrganizationCode
##      - mainCustomOrgName2: content.data.0.mainCustomOrgName
##    validate:
##      - eq: [ content.code,200 ]
##      - ne: [ content.data.0.userCode, "" ]
##      - gt: [ content.data.0.customAccountNo, "1" ]
##      - gt: [ content.data.0.name, "1" ]
##      - gt: [ content.data.0.mainCustomOrgNo, "1" ]
##      - gt: [ content.data.0.mainCustomOrgName, "1" ]
##      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: "TC2-查询内部组织"
#    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
#    variables:
#      apiOrganizationName: $orgNameInnerParam
#      apiOrganizationTerritory: 1
#    extract:
#      - organizationIdInner: content.data.0.id
#      - organizationCodeInner: content.data.0.organizationCode
#      - organizationNameInner: content.data.0.organizationName
#      - parentOrganizationNameInner: content.data.0.parentOrganizationName
#      - parentOrganizationIdInner: content.data.0.parentOrganizationId
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ "content.status",200 ]
#      - gt: [ content.data.0.id, "1" ]
#      - gt: [ content.data.0.organizationName, "1" ]
#      - gt: [ content.data.0.organizationCode, "1" ]
#      - eq: [ content.data.0.organizationTerritory, "1" ]
#      - eq: [ content.data.0.organizationType, "1" ]
#
#- test:
#    name: "TC2-查询内部组织"
#    api: api/esignManage/OrgUser/org/getOrganizationById.yml
#    variables:
#      id: $organizationIdInner
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ "content.status",200 ]
#    extract:
#      - organizationNameInner: content.data.organizationName
#      - licNo: content.data.licenseNumber
#      - organizationCodeInner: content.data.organizationCode
#      - licenseType: content.data.licenseType
#
#
#- test:
#    name: TC4-创建企业证书
#    api: api/esignSeals/enterprise/saveEnterpriseCert.yml
#    variables:
#      organizationCode: $organizationCodeInner
#      organizationName: $organizationNameInner
#      licenseNumber: ${get_decrypt($licNo)}  #证件号
#      licenseType: $licenseType #证件类型(11组织机构代码 12社会信用代码 13工商注册代码)
#      certAlgorithm: $certAlgorithm #算法类型(1-RSA 2-SM2)
#      applyMethod: 1 #申请方式(1在线申请 2离线申请)
#      certName:  $organizationNameInner #证书名称
#      certType: 1
#    validate:
#      - eq: [ content.status,200 ]
#      - eq: [ content.success,true ]
#      - ne: [ content.message, "" ]
#

- test:
    name: TC1-查询企业生效的云证书（无则创建）
    api: api/esignSeals/v1/sealcontrols/organizationCerts/organizationCertsList.yml
    variables:
      tmp00: '${getEnterpriseCert($organizationCodeInner, $certAlgorithm)}'
      organizationCode: $organizationCodeInner
      customOrgNo: ""
      algorithm: $certAlgorithm
      certPattern: 1
      certSN: ""
    extract:
      - CertIdOrganization: content.data.0.certId
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.code,200 ]
      - len_gt: [ content.data.0.certId, 1 ]
      - len_gt: [ content.data.0.signSn, 1 ]
