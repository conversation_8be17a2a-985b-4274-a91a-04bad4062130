- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
      userCodeQueryByCerd: cesqlly2  #这个参数需要调用方入参传入
      certAlgorithm: 1  #算法类型(1-RSA 2-SM2)调用方传入
    export:
      - CertIdPerson

- test:
    name: TC1-查询个人生效的云证书（无则创建）
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
      tmp00: '${getPersonCert($userCodeQueryByCerd, $certAlgorithm)}'
      userCode_userCertsList: $userCodeQueryByCerd
      customAccountNo_userCertsList: ''
      certPattern_userCertsList: 1
      algorithm_userCertsList: $certAlgorithm
    extract:
      - CertIdPerson: content.data.0.certId
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.code,200 ]
      - len_gt: [ content.data.0.certId, 1 ]
      - len_gt: [ content.data.0.signSn, 1 ]

#- test:
#    name: TC2-查询内部用户-当前登录人的信息
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      detailData:
#        customAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - gt: [ content.data.0.name, "1" ]
#      - gt: [ content.data.0.mainCustomOrgNo, "1" ]
#      - gt: [ content.data.0.mainCustomOrgName, "1" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: TC3-查询内部用户-需要创建个人证书的用户
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      userCodeDetail: $userCodeQueryByCerd
#    extract:
#      - userCode3: content.data.0.userCode
#      - userName3: content.data.0.name
#      - customAccountNo3: content.data.0.customAccountNo
#      - mainOrganizationCode3: content.data.0.mainOrganizationCode
#      - mainCustomOrgName3: content.data.0.mainCustomOrgName
#      - licenseNo: content.data.0.licenseNo
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - contains: [ content.data.0, "email" ]
#      - contains: [ content.data.0, "mobile" ]
#      - contains: [ content.data.0, "licenseType" ]
#      - contains: [ content.data.0, "licenseNo" ]
#      - contains: [ content.data.0, "bankCardNo" ]
#      - contains: [ content.data.0, "otherOrganization" ]
#
#- test:
#    name: TC4-创建个人证书
#    api: api/esignSeals/personal/savePersonalCert.yml
#    variables:
#      licenseType: "IDCard"   #证件类型
#      certType: 1   #证书类型(1云证书 2ukey)
#      certName: $userName3   #证书名称
#      certAlgorithm: $certAlgorithm  #算法类型(1-RSA 2-SM2)
#      applyMethod: 1   #申请方式(1在线申请 2离线申请)
#      licenseNumber: ${get_encrypt($licenseNo)}   #证件号
#      userCode:  $userCode3
##      userCode: $userCode2  #用户编码
#    validate:
#      - eq: [ content.status,200 ]
#      - eq: [ content.success,true ]
#      - ne: [ content.message, "" ]
#

