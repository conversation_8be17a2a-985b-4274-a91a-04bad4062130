- config:
    name: "系统参数修改-公用方法"
    variables:
      moduleId1: "fe9053c9992311ec87635254002d4645"
      keyId1: ${getCmcKeyId($keyIdCode1,171001)}
      value1: "18"


- test:
    name: SETUP-修改管理平台-系统参数
    variables:
      moduleId: $moduleId1
      thirdId: ""
      configValues: [
            {
              "keyId": $keyId1,
              "value": $value1
            }]
    api: api/esignManage/cmc/updateValues.yml
    validate:
      - eq: [ json.code, 10000000 ]
      - eq: [ json.data, True ]
      - eq: [ json.message, "执行成功" ]