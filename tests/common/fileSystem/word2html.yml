- config:
    variables:
      - fileName: "word-测试模版.docx"
    output:
      - fileKey


- test:
    name: "word转html接口"
    variables:
     file_path: "data/$fileName"
     multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/template/convertor/owner/word2html
      method: POST
      headers:
        Content-Type: ${multipart_content_type($multipart_encoder)}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(template_mine_navId)}
        authorization: ${getPortalToken()}
      files:
        uploadFile: $multipart_encoder
      data:
        fileName: $fileName
    extract:
        fileKey: "content.data.fileKey"