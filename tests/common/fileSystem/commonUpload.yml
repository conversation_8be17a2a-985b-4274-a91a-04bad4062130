- config:
    variables:
      - fileName: "testppp.pdf"


- test:
    name: "文件上传，切割文件上传，返回fileKey相关信息"
    variables:
     file_path: "data/$fileName"
     multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/fileSystem/commonUpload
      method: POST
      headers:
        Content-Type: ${multipart_content_type($multipart_encoder)}
        X-timevale-project-id: ${ENV(esign.projectId)}
        authorization: ${getPortalToken()}
      files:
        uploadFile: $multipart_encoder
      data:
        fileName: $fileName
    extract:
        fileKey: "content.data.fileKey"