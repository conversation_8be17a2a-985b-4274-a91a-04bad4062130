- config:
    name: "页面选择word模板，无内容域和签名域"
    variables:
      presetIdCommon: ${getPreset(0,0)}
      templateIdCommon: ${getTemplateId(0,0,1)}
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}

- test:
    name: "获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetIdCommon
    extract:
      presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]


- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      businessPresetUuid: $presetIdCommon
    extract:
      initiatorOrgCodeCommon: content.data.list.0.organizationCode
      initiatorOrgNameCommon: content.data.list.0.organizationName
      initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取模板信息"
    variables:
      templateUuid: $templateIdCommon
      version: 1
    api: api/esignDocs/template/owner/templateInfo.yml
    extract:
      templateNameCommon: content.data.templateName
      templateFileKeyCommon: content.data.fileKey
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "提交单次任务-内部个人-单文档自由签"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 1,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 2,
          "templateInfo": {
            "templateId": $templateIdCommon,
            "fileKey": $templateFileKeyCommon,
            "fileName": $templateNameCommon,
            "templateType": 2,
            "version": 1
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
