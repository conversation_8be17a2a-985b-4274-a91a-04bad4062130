- config:
    name: "页面发起单次签署，增加签署网络节点"
    variables:
      presetIdCommon: ${getPreset(0,0)}
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(wsignwb01.userCode)}
      signerUserNameCommon: ${ENV(wsignwb01.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      organizationCodeCommon: ${ENV(wsignwb01.main.orgCode)}
      organizationNameCommon: ${ENV(wsignwb01.main.orgName)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - presetNameCommon

- test:
    name: "业务业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]


- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "提交单次任务-外部企业-单文档自由签"
    variables:
      "params": {
        "chargingType": 2,
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "draggable": true,
        "legalSign": 0,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "estpNode": "${ENV(estpNode_stable)}",
            "assignSigner": 1,
            "signerType": 2,
            "signerTerritory": 2,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "organizationCode": $organizationCodeCommon,
            "organizationName": $organizationNameCommon,
            "organizeCode": $organizationCodeCommon,
            "organizeName": $organizationNameCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ "content.message","签署网络任务仅支持顺序签" ]
      - eq: [ "content.status",1621005 ]
      - eq: [ "content.data",null ]


- test:
    name: "提交单次任务-外部企业-付费方校验"
    variables:
      "params": {
        "chargingType": 2,
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "draggable": true,
        "legalSign": 0,
        "type": 3,
        "signersList": [
        {
          "signMode": 0,
          "signerList": [
          {
            "estpNode": "${ENV(estpNode_stable)}",
            "assignSigner": 1,
            "signerType": 2,
            "signerTerritory": 2,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "organizationCode": $organizationCodeCommon,
            "organizationName": $organizationNameCommon,
            "organizeCode": $organizationCodeCommon,
            "organizeName": $organizationNameCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ "content.message","签署网络流程只支持发起方付费" ]
      - eq: [ "content.status",1621006 ]
      - eq: [ "content.data",null ]

- test:
    name: "提交单次任务-外部企业-成功"
    variables:
      "params": {
        "chargingType": 1,
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "draggable": true,
        "legalSign": 0,
        "type": 3,
        "signersList": [
        {
          "signMode": 0,
          "signerList": [
          {
            "estpNode": "${ENV(estpNode_stable)}",
            "assignSigner": 1,
            "signerType": 2,
            "signerTerritory": 2,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "organizationCode": $organizationCodeCommon,
            "organizationName": $organizationNameCommon,
            "organizeCode": $organizationCodeCommon,
            "organizeName": $organizationNameCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data",null ]