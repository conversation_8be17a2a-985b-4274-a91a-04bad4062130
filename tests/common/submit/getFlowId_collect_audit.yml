- config:
    name: "页面发起单次有流程有采集的单次任务，审批人为发起人:上传1个文件，签署方为内部个人，自由签"
    variables:
      presetIdCommon: ${getPreset_flowModel()}
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(wsignwb01.userCode)}
      signerUserNameCommon: ${ENV(wsignwb01.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      mainHost: ${ENV(esign.projectHost)}
      auditUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.account)}
      newSignerSnapshotId: ${get_randomNo_32()}

- test:
    name: "获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
      - auditOrgCodeCommon: content.data.list.0.organizationCode
      - auditOrgNameCommon: content.data.list.0.organizationName
      - auditUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存接口"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "signersList": [],
        "appendList": []
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取presetSnapshotId"
    variables:
      params:
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
    extract:
      - presetSnapshotId1: content.data.businessPresetDetail.presetSnapshotId
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "新建采集单"
    variables:
        formJson: "{\"list\":[{\"type\":\"input\",\"icon\":\"icon-input\",\"options\":{\"width\":\"\",\"defaultValue\":\"\",\"required\":false,\"requiredMessage\":\"\",\"dataType\":\"\",\"dataTypeCheck\":false,\"dataTypeMessage\":\"\",\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"validatorCheck\":false,\"validator\":\"\",\"placeholder\":\"\",\"customClass\":\"\",\"disabled\":false,\"labelWidth\":100,\"isLabelWidth\":false,\"hidden\":false,\"dataBind\":true,\"showPassword\":false,\"remoteFunc\":\"func_wgv6gtlj\",\"remoteOption\":\"option_wgv6gtlj\",\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"单行文本\",\"key\":\"wgv6gtlj\",\"model\":\"input_wgv6gtlj\",\"rules\":[]}],\"config\":{\"labelWidth\":100,\"formName\":\"\",\"labelPosition\":\"right\",\"size\":\"small\",\"customClass\":\"\",\"ui\":\"element\",\"layout\":\"horizontal\",\"labelCol\":3,\"width\":\"100%\",\"hideLabel\":false,\"hideErrorMessage\":false,\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]}}"
        formName: ""
        presetUuid: $presetSnapshotId1
        signerSnapshotId: $newSignerSnapshotId
        signerId: ""
    api: api/esignDocs/docGatherForm/buildForm.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "提交单次任务-内部个人-单文档自由签"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 2,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "signerSnapshotId": $newSignerSnapshotId
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [],
          "nextAssigneeList": [
          {
            "nextAssignee": $auditUserCodeCommon,
            "nextAssigneeOrganizationCode": $auditOrgCodeCommon,
            "nextAssigneeName": $auditUserNameCommon,
            "nextAssigneeOrganizationName": $auditOrgNameCommon
          }
          ],
          "nodeConfigCode": "BMLDSP",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid1",
          "sendNotice": "0",
          "variables": {}
        }
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
