- config:
    name: "页面发起单次签署:上传1个文件，签署方为外部个人，有采集，自由签"
    variables:
      presetIdCommon: ${getPreset(0,0)}
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.userCode)}
      signerUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      attachmentFileKeyCommon: ${ENV(fileKey)}
      CCOutUserNameCommon: ${ENV(wsignwb01.userName)}
      newSignerSnapshotId: ${get_randomNo_32()}
      signFlowExpireTimeCommon: ${getTomorrowTime()}
      signFlowExpireDateCommon: ${substring($signFlowExpireTimeCommon,0,10)}

- test:
    name: "业务业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]


- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取抄送人"
    variables:
      userType: 2
      userName: $CCOutUserNameCommon
    api: api/esignDocs/user/getUserInfo.yml
    extract:
      - CCOutUserCodeCommon: content.data.signerList.0.id
      - CCOutOrgNameCommon: content.data.signerList.0.organizeName
      - CCOutOrgCodeCommon: content.data.signerList.0.organizeCode
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]


- test:
    name: "提交单次任务-内部个人-单文档自由签"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "remark": "自动化测试-备注",
        "signFlowExpireTime": $signFlowExpireTimeCommon,
        "businessNo": "自动化测试-业务编码",
        "advertisement": true,
        "readComplete": true,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 1,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ],
        "attachments": [
        {
          "fileName": "测试附件.pdf",
          "fileKey": $attachmentFileKeyCommon
        }
        ],
        "ccInfos": [
        {
          "userType": 2,
          "userName": $CCOutUserNameCommon,
          "userCode": $CCOutUserCodeCommon,
          "organizeCode": $CCOutOrgCodeCommon,
          "organizeName": $CCOutOrgNameCommon
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

