- config:
    name: "页面发起单次有流程的单次任务，审批人为发起人:上传1个文件，签署方为内部个人，自由签"
    variables:
#      presetIdCommon: ${getPreset_flowModel()}
      presetIdCommon: "${getPresetTypeId(11)}"
      batchTemplateTaskNameCommon: "文档自动化测试-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      mainHost: ${ENV(esign.projectHost)}
      auditUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.account)}
    output:
      - batchTemplateTaskNameCommon
      - signerUserNameCommon
      - signerUserCodeCommon
      - initiatorUserNameCommon
      - initiatorOrgNameCommon
      - initiatorOrgCodeCommon
      - auditUserCodeCommon
      - presetNameCommon
      - batchTemplateInitiationUuid1

- test:
    name: "获取业务模板名称"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetIdCommon
    extract:
      - presetNameCommon: content.data.presetName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetIdCommon
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
      - auditOrgCodeCommon: content.data.list.0.organizationCode
      - auditOrgNameCommon: content.data.list.0.organizationName
      - auditUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: ["content.message","成功"]

- test:
    name: "获取batchTemplateInitiationUuid"
    variables:
      params: {}
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "提交单次任务-内部个人-单文档自由签"
    variables:
      "params": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetIdCommon,
        "saveSigners": null,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 1,
            "signerTerritory": 1,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": "测试"
          }
        }
        ],
        "auditInfo": {
          "auditResult": "",
          "carbonCopyList": [],
          "nextAssigneeList": [
          {
            "nextAssignee": $auditUserCodeCommon,
            "nextAssigneeOrganizationCode": $auditOrgCodeCommon,
            "nextAssigneeName": $auditUserNameCommon,
            "nextAssigneeOrganizationName": $auditOrgNameCommon
          }
          ],
          "nodeConfigCode": "BMLDSP",
          "requestUrl": "$mainHost/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=FQDZQS&batchTemplateInitiationUuid=$batchTemplateInitiationUuid1",
          "sendNotice": "0",
          "variables": {}
        }
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    extract:
      - _flowId: content.data
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ "content.data","" ]
    teardown_hooks:
      - ${sleep(3)}

- test:
    name: submitResult-查询发起的结果-V6.0.12.0-审批(发起人审批人签署人是同一个)
    api: api/esignDocs/batchTemplateInitiation/submitResult.yml
    variables:
      flowId_submitResult: "$_flowId"
#    extract:
#      - code0: content.code
    validate:
      - eq: [content.status, 200]
      - eq: [content.message, "成功"]
#      - contains: [content.data.jumpUrl, "http"] #流程引擎实例生成的数据比较缓慢有可能无法获取到url
      - eq: [content.data.flowStatus, 2]
      - eq: [content.data.hasPermission, true]
      - eq: [content.data.hasWorkflow, true]
      - eq: [content.data.message, null]
      - eq: [content.data.initiator.initiatorName, "$initiatorUserNameCommon"]
      - len_eq: [content.data.fillingUserList, 0]
      - len_eq: [content.data.signerList, 1]
      - eq: [content.data.signerList.0.signerName, "$initiatorUserNameCommon"]