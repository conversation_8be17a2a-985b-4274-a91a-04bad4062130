#适用流程图：
#开始->业务模板配置-保存配置详情信息-配置签署方信息-非自动签署--无采集无审核--甲乙个人
- config:
    variables:
      - autoPresetName: "自动化模板配置测试${get_randomNo()}"
    output:
      - getPresetIdCommon
      - getPresetNameCommon
      - contentNameCommon
- test:
    name: "添加业务模板-成功"
    api: api/esignDocs/businessPreset/create.yml
    variables:
      - presetName: $autoPresetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "setup-获取业务模版PresetId"
    api: api/esignDocs/businessPreset/list.yml
    variables:
      - queryPresetName: $autoPresetName
      - page: 1
      - size: 10
    extract:
      - getPresetIdCommon: content.data.list.0.presetId
      - getPresetNameCommon: content.data.list.0.presetName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "webapi-业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $getPresetIdCommon
    extract:
      - getBusinessTypeId: content.data.signBusinessType.businessTypeId
    validate:
      - eq: ["content.status", 200]
      - eq: ["content.message","成功"]
      - ne: ["content.data.presetId",""]
      - ne: ["content.data.signBusinessType.businessTypeId",""]

- test:
    name: "引用公共用例获取已发布好的企业模板信息"
    testcase: common/template/buildTemplate5.yml
    extract:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "业务模板第1步-addDetail：关联1个文档模板+不指定发起人+允许追加文件"
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      - presetId: $getPresetIdCommon
      - presetName: $autoPresetName
      - signBusinessTypeId: $getBusinessTypeId
      - multiSigner: 1
      - initiatorAll: 1
      - initiatorList: []
      - checkRepetition: 1
      - templateId: $newTemplateUuidCommon
      - templateName: $templateNameCommon
      - version: $newVersionCommon
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "业务模板第3步-addSigners: 添加业务模板配置签署人--非自动签署--无采集无审核--甲乙个人"
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      "params": {
        "presetId": $getPresetIdCommon,
        "presetName": $getPresetNameCommon,
        "status": 0,
        "initiatorAll": 1,
        "needGather": 0,
        "needAudit": 0,
        "allowAddSigner": 0,
        "sort": 0,
        "editGather": 1,
        "initiatorEdit": 1,
        "changeReason": null,
        "signerList": [ {
          "assignSigner": 1,
          "autoSign": 0,
          "organizeCode": "",
          "organizeName": "",
          "sealTypeCode": "",
          "signatoryList": [],
          "signerTerritory": 1,
          "signerType": 1,
          "userAccount": "",
          "userCode": $userCodeCommon,
          "userName": "",
          "id": 0,
          "draggable": false,
          "sealTypeName": "",
          "departmentCode": "",
          "departmentName": "",
          "sealTypeList": [ ],
          "organizeList": [ ]
        }, {
          "autoSign": 0,
          "organizeCode": "",
          "organizeName": "",
          "sealTypeCode": "",
          "signatoryList": [],
          "signerTerritory": 2,
          "signerType": 1,
          "userAccount": "",
          "userCode": "",
          "userName": "",
          "id": 1,
          "draggable": false,
          "sealTypeName": "",
          "departmentCode": "",
          "departmentName": "",
          "sealTypeList": [ ],
          "organizeList": [ ]
        } ],
        "signerNodeList": [{
            "signerList": $signerList,
            "signMode": 0,
            "id": "node-1"
        }]
      }
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $getPresetIdCommon
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_001: ${putTempEnv(_tmp_biz_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
      - _label_0: content.data.fields.0.label #签署控件
      - _label_1: content.data.fields.1.label
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_001)}
    extract:
      - _signerId0: content.data.0.id
      - _signerId1: content.data.1.id
      - _signerName0: content.data.0.name
      - _signerName1: content.data.1.name
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      _tmp_sign: [ {"label": "$_label_1","templateRoleId": "$_signerId1" }]
      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,,$_tmp_sign)}"
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: setup-业务模板配置的第4步：设置签署方填写信息，将第一个内容域填写方设置为节点1签署方1填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      - presetId: $getPresetIdCommon
      - status: 1
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]