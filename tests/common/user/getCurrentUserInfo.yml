- config:
    output:
      - createUserOrgCodeCommon
      - createUser<PERSON><PERSON><PERSON>ommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-创建人名称-组织自动带出"
    api: api/esignDocs/user/getUserOrg.yml
    variables:
      - userCode: null
    extract:
      - createUserOrgCodeCommon: content.data.organizationCode
      - createUserCodeCommon: content.data.userCode
      - userNameCommon: content.data.userName
    validate:
      - length_greater_than_or_equals: ["content.data.organizationCode",1]
      - length_greater_than_or_equals: ["content.data.userCode",1]
      - length_greater_than_or_equals: ["content.data.orgList",1]
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup-用户信息"
    api: api/esignDocs/user/getUserListByUserCodeName.yml
    variables:
      - userName: $userNameCommon
    extract:
      - userIdCommon: content.data.userList.0.id
      - userCodeCommon: content.data.userList.0.userCode
      - userNameCommon: content.data.userList.0.userName
      - organizationIdCommon: content.data.userList.0.organizationId
      - organizationCodeCommon: content.data.userList.0.organizationCode
      - getOrganizationNameCommon: content.data.userList.0.companyName
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]