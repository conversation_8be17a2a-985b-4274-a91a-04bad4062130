- name: "指定印章为动态编码章自动签署"
- config:
    variables:
#      initiatorUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
#      initiatorUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.userCode)}
#      initiatorOrgCodeCommon: ${getUserMainOrg($initiatorUserCodeCommon)}
#      initiatorOrgNameCommon: ${get_orgInfo(None,$initiatorOrgCodeCommon)}
      initiatorUserNameCommon: ${ENV(sign01.userName)}
      initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      initiatorOrgCodeCommon: ${ENV(sign01.main.orgCode)}
      initiatorOrgNameCommon: ${ENV(sign01.main.orgName)}
      businessNoCommon: "文档自动化测试-编码-${get_randomNo_16()}"
      subjectCommon: "文档自动化测试-主题-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      signerOrgCodeCommon: ${ENV(sign01.main.orgNo)}
      signerOrgNameCommon: ${ENV(sign01.main.orgName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      businessTypeCodeCommon: ${ENV(businessTypeCode)}
      orgSealIdDynamic: ${ENV(sign01_orgSealIdDynamic)}
    output:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon
      - dynamicCodeCommon
      - signerOrgNameCommon

- test:
    name: "调用签署openapi创建并发起流程"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": $subjectCommon,
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "customOrgNo": $signerOrgCodeCommon,
          "userType": "1",
          "signNode": 1,
          "autoSign": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,
            "signConfigs": [
            {
              "pageNo": 1,
              "posX": 200,
              "posY": 100,
              "sealId": $orgSealIdDynamic,
              "signType": "COMMON-SIGN ",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon: content.data.signFlowId
    teardown_hooks:
      - ${sleep(10)}
    validate:
      - eq: [ "content.code",200 ]
#########目前静默签署太慢了，所以需要等待很长时间###########

- test:
    name: "调用签署openapi创建并发起流程"
    api: api/esignDocs/signOpenapi/signDetail.yml
    variables:
      signFlowId: $signFlowIdCommon
      businessNo: ""
    extract:
      dynamicCodeCommon: content.data.signerInfos.0.dynamicCode
    validate:
      - eq: [ "content.code",200 ]
      - eq: [ "content.message","成功" ]
      - ne: [ "content.data.signFlowId","" ]
