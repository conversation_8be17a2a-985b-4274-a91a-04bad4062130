- config:
    variables:
#      initiatorUserNameCommon: ${ENV(ceswdzxzdhyhwgd1.userName)}
#      initiatorUserCodeCommon: ${ENV(ceswdzxzdhyhwgd1.userCode)}
#      initiatorOrgCodeCommon: ${getUserMainOrg($initiatorUserCodeCommon)}
#      initiatorOrgNameCommon: ${get_orgInfo(None,$initiatorOrgCodeCommon)}
      initiatorUserNameCommon: ${ENV(sign01.userName)}
      initiatorUserCodeCommon: ${ENV(sign01.userCode)}
      initiatorOrgCodeCommon: ${ENV(sign01.main.orgCode)}
      initiatorOrgNameCommon: ${ENV(sign01.main.orgName)}
      businessNoCommon: "文档自动化测试-编码-${get_randomNo_16()}"
      subjectCommon: "文档自动化测试-主题-${get_randomNo_16()}"
      signerUserCodeCommon: ${ENV(sign01.userCode)}
      signerUserNameCommon: ${ENV(sign01.userName)}
      toSignFileKeyCommon: ${ENV(fileKey)}
      businessTypeCodeCommon: ${ENV(businessTypeCode)}

    output:
      - initiatorUserNameCommon
      - subjectCommon
      - businessNoCommon
      - signerUserNameCommon
      - signFlowIdCommon
      - initiatorOrgCodeCommon
      - initiatorOrgNameCommon

- test:
    name: "调用签署openapi创建并发起流程"
    api: api/esignDocs/signOpenapi/createAndStart.yml
    variables:
      params: {
        "businessNo": $businessNoCommon,
        "businessTypeCode": $businessTypeCodeCommon,
        "subject": $subjectCommon,
        "initiatorInfo": {
          "userCode": $initiatorUserCodeCommon,
          "organizationCode": $initiatorOrgCodeCommon,
          "userType": 1
        },
        "signFiles": [
        {
          "fileKey": $toSignFileKeyCommon
        }
        ],
        "signerInfos": [
        {
          "userCode": $signerUserCodeCommon,
          "userType": "1",
          "signNode": 1,
          "signMode": 0,
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon
          }
          ]
        }
        ]
      }
    extract:
      signFlowIdCommon: content.data.signFlowId
    validate:
      - eq: [ "content.code",200 ]