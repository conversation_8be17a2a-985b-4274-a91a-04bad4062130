-   config:
        name: "openapi上传pdf文件case,文件大小100M内"
        export:
            - exportFileKey


-   test:
        name: "TC1-生成上传链接"
        api: api/esignFileSystem/generateUploadUrl-api.yml
        variables:
          type: 1    # ofd文件不支持分片，只能用直接上传
        validate:
            -   eq: [content.message,"成功"]
            -   eq: [content.code,200]
            -   len_gt: [content.data.url,0]

-   test:
        name: "TC2-同步上传并分割文件ofd文件"
        api: api/esignFileSystem/uploadV2-api.yml
        variables:
            fileName: $fileNameIn     # case入参，需要上传的文件名称
        extract:
            -   exportFileKey: content.data.fileKey
        validate:
            -   eq: [content.message,"成功"]
            -   eq: [content.code,200]
            -   len_gt: [content.data.fileKey,0]



