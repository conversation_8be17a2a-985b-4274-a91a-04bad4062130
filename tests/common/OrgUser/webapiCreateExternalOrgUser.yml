- config:
    name: "通过管理平台的页面接口去新建外部企业和内部用户--固定的测试数据"
    variables: #这个公共case的入参需要
      cutsomAccountNumberExternalParam: "wflinkCase01"
      organizationName0: "esigntest混合云全链路外部一测试公司"
      cutsomOrgNo0: "WORG-FLINK-01"
      orgLicenseNumber: "9100000063926831K8"
      orgLicenseNumberCrypt: "${encrypt($orgLicenseNumber)}"
      userName0: "测试全链路外部一"
      userMobile0: "***********"
      userMobileCrypt00: "${encrypt($userMobile0)}"
      userLicenseNumber0: "220301193604270034"
      userLicenseNumberCrypt: "${encrypt($userLicenseNumber0)}"
      ####调试数据:
#      orgLicenseNumberCrypt: "Lnmldk5PD+pf4k/q7fIP1f1SX+IUK26MrdzFggWceE0="
#      userLicenseNumberCrypt: "r5b7LdsdwGOJf1oWBvnsKIugxOhk3/yyXtCFjd/js+I="
#      userMobileCrypt00: "GD2SuHp5wlEj0be7OI9nfA=="

    output:
      - organizationIdExternal
      - organizationCodeExternal
      - organizationNameExternal
      - userIdExternal
      - userCodeExternal
      - userNameExternal
      - userMobileExternal
      - cutsomAccountNumberExternalParam
      - cutsomOrgNo0
      - userMobileCrypt
      - orgLicenseNumberCrypt
      - userLicenseNumberCrypt
      - orgLicenseNoExternal
      - legalRepAccountNoExternal
      - legalRepUserCodeExternal
      - legalRepNameExternal
      - legalRepUserCodeExternal

- test:
    name: "TC1-创建外部组织"
    api: api/esignManage/OrgUser/org/saveOrganization.yml
    variables:
      data:
        params:
          accountNumber: $cutsomOrgNo0
          licenseNumber:  $orgLicenseNumberCrypt
          licenseType: 12
          orderNum: 100
          organizationName: $organizationName0
          organizationStatus: 1
          organizationTerritory: 2
          organizationType: 1
          parentOrganizationCode: "WBXDF"
          parentOrganizationId: 0
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "TC2-查询外部组织"
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    variables:
      apiOrganizationName: $organizationName0
      apiOrganizationTerritory: 2
    extract:
      - organizationIdExternal: content.data.0.id
      - organizationCodeExternal: content.data.0.organizationCode
      - organizationNameExternal: content.data.0.organizationName
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - gt: [ content.data.0.organizationCode, "1" ]
      - eq: [ content.data.0.organizationTerritory, "2" ]
      - eq: [ content.data.0.organizationType, "1" ]

- test:
    name: "TC3-创建外部用户"
    api: api/esignManage/OrgUser/user/saveUser.yml
    variables:
      userName: $userName0         #用户姓名不能为空
      userType: 2          #用户类型不能为空    [12]用户类型(1管理员、2普通用户)
      userMobile:  $userMobileCrypt00
      licenseType: 19          #$|[1][3789]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
      licenseNumber:  $userLicenseNumberCrypt   #明文:220301193604270034
      userStatus: 3        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
      userTerritory: 2  #用户组织类型不能为空   用户组织类型(1内部 2外部)
      userCode: $cutsomAccountNumberExternalParam            #用户编码支持输入2-30字 不能为空
      accountNumber: $cutsomAccountNumberExternalParam   #账号支持输入2-30字     不能为空
      organizationId: $organizationIdExternal   #所属组织支持输入1-36字
      organizationCode: $organizationCodeExternal  #所属组织编码支持输入1-36字 所属组织编码不能为空
#    validate:
#      - contains: [ content.message,"成功" ]
#      - eq: [ "content.status",200 ]

- test:
    name: "TC4-查询外部用户"
    api: api/esignManage/OrgUser/user/pageOutsideUserList.yml
    variables:
      accountNumber: $cutsomAccountNumberExternalParam
      userName: ''
    extract:
      - userIdExternal: content.data.list.0.id
      - userCodeExternal: content.data.list.0.userCode
      - userNameExternal: content.data.list.0.userName
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - ne: [ content.data.list.0.userCode, "" ]
      - gt: [ content.data.list.0.accountNumber, "1" ]
      - gt: [ content.data.list.0.userName, "1" ]
      - gt: [ content.data.list.0.organizationName, "1" ]

- test:
    name: "TC5-查询外部用户-获取手机号"
    api: api/esignManage/outerUsers/detail.yml
    variables:
      outerUsersDetail: {
        userCode: "",
        customAccountNo: $cutsomAccountNumberExternalParam,
        name: "",
        email: "",
        mobile: "",
        licenseNo: ""
      }
    extract:
      - userMobileExternal: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - contains: [ content.data.0, "email" ]
      - contains: [ content.data.0, "mobile" ]
      - contains: [ content.data.0, "licenseType" ]
      - contains: [ content.data.0, "licenseNo" ]

- test:
    name: "TC6-更新外部组织添加法人信息"
    api: api/esignManage/OrgUser/org/updateOrganization.yml
    setup_hooks:
      - ${hook_sleep_n_secs(5)}
    variables:
      organizationId: $organizationIdExternal
      organizationCode: $organizationCodeExternal
      accountNumber: $cutsomOrgNo0
      licenseNumber: $orgLicenseNumberCrypt  #明文信息"9100000063926831K8"
      licenseType: 12
      orderNum: 100
      organizationName: $organizationName0
      organizationStatus: 1
      organizationTerritory: 2
      organizationType: 1
      parentOrganizationId: 0
      legalPersonId: $userIdExternal
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC7-查询外部企业信息"
    api: api/esignManage/outerOrganizations/detail.yml
    variables:
      organizationCode: $organizationCodeExternal
    extract:
      - orgLicenseNoExternal: content.data.0.licenseNo
      - legalRepAccountNoExternal: content.data.0.legalRepAccountNo
      - legalRepUserCodeExternal: content.data.0.legalRepUserCode
      - legalRepNameExternal: content.data.0.legalRepName
      - legalRepUserCodeExternal: content.data.0.legalRepUserCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]