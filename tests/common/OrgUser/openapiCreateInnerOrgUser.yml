- config:
    name: "管理平台openapi新增的内部组织用户、实名、授权"
    variables:
      OrgName: "esigntest混合云全链路五测试公司"
      OrgNo: "ORG-FLINK-05"
      OrgLicenseNo: "9100000063867490EX"
      userNo: "flinkCase05"
      userName: "测试全链路五"
      userMobile: "19112100005"
      userIDCard: "******************"

    export:
      - userMobile
      - userName
      - userNo
      - userId
      - userCode
      - userIDCard
      - orgId
      - OrgLicenseNo
      - OrgNo
      - OrgName
      - orgCode

- test:
    name: 创建内部组织
    api: api/esignManage/InnerOrganizations/create.yml
    variables:
      name: $OrgName
      customOrgNo: $OrgNo
      organizationType: COMPANY
      parentOrgNo:
      parentCode: 0
      licenseType: CREDIT_CODE
      licenseNo: $OrgLicenseNo
      legalRepAccountNo:
      legalRepUserCode:
#    validate:
#        - eq: [json.code, 200]
#        - eq: [json.message, 成功]
#        - eq: [json.data.customOrgNo, $OrgNo]


- test:
    name: 创建内部用户-完成用户实名授权
    api: api/esignManage/InnerUsers/create.yml
    variables:
      customAccountNo: $userNo
      name: $userName
      mobile: $userMobile
      email:
      licenseType: ID_CARD
      licenseNo: $userIDCard
      bankCardNo:
      mainOrganizationCode:
      mainCustomOrgNo: $OrgNo
      otherOrganization: [ ]

#    validate:
#      - eq: [ json.code, 200 ]
#      - eq: [ json.message, 成功 ]
#      - eq: [ json.data.successCount, 1 ]
#      - eq: [ json.data.successData.0.customAccountNo, $userNo ]



- test:
    name: 更新内部组织法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      organizationCode:
      customOrgNo: $OrgNo
      name: $OrgName
      parentCode: 0
      parentOrgNo:
      licenseType: CREDIT_CODE
      licenseNo: $OrgLicenseNo
      legalRepUserCode:
      legalRepAccountNo: $userNo
#    validate:
#        - eq: [json.code, 200]
#        - eq: [json.message, 成功]
#        - eq: [json.data.customOrgNo, $OrgNo]

#- test:
#    name: 个人和企业实名并授权
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      tmp0: {"mobile": $userMobile,"orgName": $OrgName}
#      tmp1: '${getCloudRealNameAndAuth(4,$tmp0)}'
#      detailData:
#        customAccountNo: ${ENV(userCodeNoSeal)}
#    validate:
#      - eq: [ content.code,200 ]
#      - eq: [ content.message, '成功' ]
#      - ne: [ content.data, '' ]

# todo 有bug
- test:
    name: 获取用户详情
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    variables:
      accountNumber: $userNo
    extract:
      - userId: content.data.list.0.id
      - userCode: content.data.list.0.userCode
#      - orgId: content.data.list.0.organizationId
      - orgId: content.data.list.0.ecUserParttimeDOList.0.organizationId
      - orgCode: content.data.list.0.ecUserParttimeDOList.0.organizationCode
#    validate:
#      - eq: [ json.success, true ]
#      - eq: [ json.status, 200 ]
#      - contains: [ content.message,'成功' ]
#      - eq: [ json.data.list.0.accountNumber, $userNo ]


- test:
    name: 查询角色权限
    api: api/esignManage/rolePermission/roleAuthorize.yml
    variables:
      data2: { "params": { "id": "1598607216130727937","roleName": "自动化测试专用权限最大","roleCode": "ZDHCSZYQXZD","roleClass": "2","roleDesc": "","loading": false },"domain": "admin_platform" }
    extract:
      - userIdList: json.data.userIdList
#    validate:
#      - eq: [ json.success, true ]
#      - eq: [ json.status, 200 ]
#      - contains: [ content.message,'成功' ]

- test:
    name: 授权用户最大权限
    api: api/esignManage/rolePermission/saveRoleAuthorize.yml
    variables:
      userdata:
        id: $userId
        userCode: $userCode
        userName: $userName
        organizationId: $orgId
        organizationName: $OrgName
      userList: ${listaddvalue($userIdList,$userdata)}
#    validate:
#      - eq: [ json.success, true ]
#      - eq: [ json.status, 200 ]
#      - eq: [ json.message, 保存授权信息成功! ]

