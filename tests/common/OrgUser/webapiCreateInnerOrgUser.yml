- config:
    name: "通过管理平台的页面接口去新建内部企业和内部用户"
    variables: #这个公共case的入参需要
      cutsomAccountNoInnerParam: "flinkCase01"
      orgNameInnerParam: "esigntest混合云全链路一测试公司"
      cutsomOrgNoInnerParam: "ORG-FLINK-01"
      orgLicenseNumberCrypt0: "${encrypt(91000000639268300M)}"
      userNameInnerParam: "测试全链路一"
      userMobileCryptInnerParam: "${encrypt(***********)}"
      userLicenseNoCryptInnerParam: "${encrypt(220301193004270014)}"
    output:
      - organizationIdInner
      - organizationCodeInner
      - organizationNameInner
      - parentOrganizationNameInner
      - parentOrganizationIdInner
      - orgLicenseNoInner
      - legalRepAccountNoInner
      - legalRepUserCodeInner
      - legalRepNameInner
      - legalRepUserCodeInner
      - userIdInner
      - userCodeInner
      - userNameInner
      - userMobileInner
      - cutsomAccountNoInnerParam
      - cutsomOrgNoInnerParam
      - userMobileCryptInnerParam
      - userLicenseNoCryptInnerParam

- test:
    name: "TC1-创建内部组织"
    api: api/esignManage/OrgUser/org/saveOrganization.yml
    variables:
      data:
        params:
          accountNumber: $cutsomOrgNoInnerParam
          licenseNumber: $orgLicenseNumberCrypt0  #明文信息"91000000639268300M"
          licenseType: 12
          orderNum: 100
          organizationName: $orgNameInnerParam
          organizationStatus: 1
          organizationTerritory: 1
          organizationType: 1
          parentOrganizationCode: 0
          parentOrganizationId: 0
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ "content.status",200 ]

- test:
    name: "TC2-查询内部组织"
    api: api/esignManage/OrgUser/org/getOrganizationListByOrgCodeName.yml
    variables:
      apiOrganizationName: $orgNameInnerParam
      apiOrganizationTerritory: 1
    extract:
      - organizationIdInner: content.data.0.id
      - organizationCodeInner: content.data.0.organizationCode
      - organizationNameInner: content.data.0.organizationName
      - parentOrganizationNameInner: content.data.0.parentOrganizationName
      - parentOrganizationIdInner: content.data.0.parentOrganizationId
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - gt: [ content.data.0.organizationCode, "1" ]
      - eq: [ content.data.0.organizationTerritory, "1" ]
      - eq: [ content.data.0.organizationType, "1" ]

- test:
    name: "TC3-创建内部用户"
    api: api/esignManage/OrgUser/user/saveUser.yml
    variables:
      userName: $userNameInnerParam        #用户姓名不能为空   ^[a-zA-Z\u4e00-\u9fa5\s·]{226}$
      userType: 2          #用户类型不能为空    [12]用户类型(1管理员、2普通用户)
      userMobile: $userMobileCryptInnerParam
      licenseType: 19          #$|[1][3789]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
      licenseNumber: $userLicenseNoCryptInnerParam
      userStatus: 1        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
      userTerritory: 1  #用户组织类型不能为空   用户组织类型(1内部 2外部)
      userCode: $cutsomAccountNoInnerParam           #用户编码支持输入2-30字 不能为空
      accountNumber: $cutsomAccountNoInnerParam   #账号支持输入2-30字     不能为空
      organizationId: $organizationIdInner   #所属组织支持输入1-36字
      organizationCode: $organizationCodeInner  #所属组织编码支持输入1-36字 所属组织编码不能为空
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ "content.status",200 ]

- test:
    name: "TC4-查询内部用户"
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    variables:
      accountNumber: $cutsomAccountNoInnerParam
      organizationId: $organizationIdInner
    extract:
      - userIdInner: content.data.list.0.id
      - userCodeInner: content.data.list.0.userCode
      - userNameInner: content.data.list.0.userName
      - userMobileInner: content.data.list.0.userMobile
    validate:
      - contains: [ content.message,'成功' ]
      - eq: [ "content.status",200 ]
      - ne: [ content.data.list.0.userCode, "" ]
      - gt: [ content.data.list.0.accountNumber, "1" ]
      - gt: [ content.data.list.0.userName, "1" ]
      - gt: [ content.data.list.0.organizationName, "1" ]

- test:
    name: "TC5-查询内部用户-获取手机号"
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      customAccountNoMIDetail: $cutsomAccountNoInnerParam
    extract:
      - userMobileInner: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - contains: [ content.data.0, "email" ]
      - contains: [ content.data.0, "mobile" ]
      - contains: [ content.data.0, "licenseType" ]
      - contains: [ content.data.0, "licenseNo" ]

- test:
    name: "TC6-更新内部组织添加法人信息"
    api: api/esignManage/OrgUser/org/updateOrganization.yml
    variables:
      organizationId: $organizationIdInner
      organizationCode: $organizationCodeInner
      accountNumber: $cutsomOrgNoInnerParam
      licenseNumber: $orgLicenseNumberCrypt0  #明文信息"91000000639268300M"
      licenseType: 12
      orderNum: 100
      organizationName: $orgNameInnerParam
      organizationStatus: 1
      organizationTerritory: 1
      organizationType: 1
      parentOrganizationId: 0
      legalPersonId: $userIdInner
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ "content.status",200 ]
#      - eq: [ "content.data", "" ]

- test:
    name: "TC7-查询内部企业信息"
    api: api/esignManage/InnerOrganizations/detail.yml
    variables:
      organizationCode: $organizationCodeInner
    extract:
      - orgLicenseNoInner: content.data.0.licenseNo
      - legalRepAccountNoInner: content.data.0.legalRepAccountNo
      - legalRepUserCodeInner: content.data.0.legalRepUserCode
      - legalRepNameInner: content.data.0.legalRepName
      - legalRepUserCodeInner: content.data.0.legalRepUserCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]

- test:
    name: 查询角色权限
    api: api/esignManage/rolePermission/roleAuthorize.yml
    variables:
      data2: { "params": { "id": "1598607216130727937","roleName": "自动化测试专用权限最大","roleCode": "ZDHCSZYQXZD","roleClass": "2","roleDesc": "","loading": false },"domain": "admin_platform" }
    extract:
      - userIdList: json.data.userIdList
#    validate:
#      - eq: [ json.success, true ]
#      - eq: [ json.status, 200 ]
#      - contains: [ content.message,'成功' ]

- test:
    name: 授权用户最大权限
    api: api/esignManage/rolePermission/saveRoleAuthorize.yml
    variables:
      userdata:
        id: $userIdInner
        userCode: $userCodeInner
        userName: $userNameInner
        organizationId: $organizationIdInner
        organizationName: $orgNameInnerParam
      userList: ${listaddvalue($userIdList,$userdata)}
#    validate:
#      - eq: [ json.success, true ]
#      - eq: [ json.status, 200 ]
#      - eq: [ json.message, 保存授权信息成功! ]