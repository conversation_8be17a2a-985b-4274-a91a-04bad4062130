- config:
    name: "管理平台excel导入内部组织用户、实名、授权"
    variables:
      organizationNameInner06: 'esigntest混合云全链路六测试公司'
      organizationNoInner06: 'ORG-FLINK-06'
      organizationLicenseNoInner06: '9100000029822268JN'
      userNoInner04: 'flinkCase04'
      userNameInner04: '测试全链路四'
      userMobileInner04: '19112100004'
      userIDCardInner04: '******************'
      filetype: "multipart/form-data;"
      language: zh-CN

    export:
      - userMobileInner04
      - userNameInner04
      - userNoInner04
      - userIdInner04
      - userCodeInner04
      - userIDCardInner04
      - organizationIdInner06
      - organizationNameInner06
      - 0rganizationNoInner06
      - organizationLicenseNoInner06

- test:
    name: 导入内部组织【esigntest混合云全链路六测试公司】
    api: api/esignManage/OrgUser/org/importOrganizationExcel.yml
    variables:
      filename: 管理平台-内部组织导入模板.xlsx
      fileType: $filetype
      filePath: 管理平台-内部组织导入模板.xlsx
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
#      - eq: [ json.message, 导入成功条数：1条 ]

- test:
    name: 导入内部用户【flinkCase04】
    api: api/esignManage/OrgUser/user/importInsideUserExcel.yml
    variables:
      filename: 管理平台-内部用户导入模板.xlsx
      fileType: $filetype
      filePath: 管理平台-内部用户导入模板.xlsx
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
#      - eq: [ json.message, 导入成功条数：1条 ]

- test:
    name: 更新内部组织法人信息
    api: api/esignManage/InnerOrganizations/update.yml
    variables:
      organizationCode:
      customOrgNo: $organizationNoInner06
      name: $organizationNameInner06
      parentCode: 0
      parentOrgNo:
      licenseType: CREDIT_CODE
      licenseNo: $organizationLicenseNoInner06
      legalRepUserCode:
      legalRepAccountNo: $userNoInner04
    validate:
      - eq: [ json.code, 200 ]
      - eq: [ json.message, 成功 ]
      - eq: [ json.data.customOrgNo, $organizationNoInner06 ]

- test:
    name: 个人和企业实名并授权
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      tmp0: { "mobile": $userMobileInner04,"orgName": $organizationNameInner06 }
      tmp1: '${getCloudRealNameAndAuth(4,$tmp0)}'
      detailData:
        customAccountNo: ${ENV(userCodeNoSeal)}
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message, '成功' ]
      - ne: [ content.data, '' ]

# todo 有bug
- test:
    name: 获取用户详情
    api: api/esignManage/OrgUser/user/getUserByOrganization.yml
    variables:
        accountNumber: $userNoInner04
    extract:
      - userIdInner04: content.data.list.0.id
      - userCodeInner04: content.data.list.0.userCode
#      - organizationIdInner06: content.data.list.0.organizationId  #有Bug
      - organizationIdInner06: content.data.list.0.ecUserParttimeDOList.0.organizationId
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
      - contains: [ content.message,"成功" ]
      - eq: [ json.data.list.0.accountNumber, $userNoInner04 ]


- test:
    name: 查询角色权限
    api: api/esignManage/rolePermission/roleAuthorize.yml
    variables:
      data2: { "params": { "id": "1598607216130727937","roleName": "自动化测试专用权限最大","roleCode": "ZDHCSZYQXZD","roleClass": "2","roleDesc": "","loading": false },"domain": "admin_platform" }
    extract:
      - userIdList: json.data.userIdList
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
      - contains: [ content.message,"成功" ]

- test:
    name: 授权用户最大权限
    api: api/esignManage/rolePermission/saveRoleAuthorize.yml
    variables:
      userdata:
        id: $userIdInner04
        userCode: $userCodeInner04
        userName: $userNameInner04
        organizationId: $organizationIdInner06
        organizationName: $organizationNameInner06
      userList: ${listaddvalue($userIdList,$userdata)}
    validate:
      - eq: [ json.success, true ]
      - eq: [ json.status, 200 ]
      - eq: [ json.message, 保存授权信息成功! ]
