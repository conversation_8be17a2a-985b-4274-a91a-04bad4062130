- config:
    name: 创建业务模板配置-电子业务模板(带有PDF文档模板，且内部个人签署+内容域为签署方填写)
    variables:
#      sealCodeCommon: ${getDateTime()}
#      userCode_BPD0: "zhengml"

      #默认个人页面签署方式以下方式都开启，如果有不支持的方式，调用该公共case时将对应的变量值改为0
      AISignature_innerPerson_webapiCreatebusinessPresetWithDoc: 1 #内部个人AI手绘
      AISignature_outerPerson_webapiCreatebusinessPresetWithDoc: 1 #个人相对方AI手绘
      handwrittenSignature_innerPerson_webapiCreatebusinessPresetWithDoc: 1  #内部个人普通手绘
      handwrittenSignature_outerPerson_webapiCreatebusinessPresetWithDoc: 1  #个人相对方普通手绘
      templateSeal_innerPerson_webapiCreatebusinessPresetWithDoc: 1  #内部个人模板印章
      templateSeal_outerPerson_webapiCreatebusinessPresetWithDoc: 1  #个人相对方模板印章
      handOrImageSeal_innerPerson_webapiCreatebusinessPresetWithDoc: 1 #内部个人手绘印章/图片印章
      handOrImageSeal_outerPerson_webapiCreatebusinessPresetWithDoc: 1  #个人相对方手绘印章/图片印章

      signerId_BPD0: "${getRandomNo(32)}"
      #调用该公共case时下面这些变量得传你需要的值
      presetName: "1682663282632"
      signAreaSignEnable: 1 #1-必签；0-非必签
      signAreaMoveEnable: 1 #1-可移动；0-不可移动
      handEnable: 1
      aiHandEnable: 1
      signEndTimeEnable: 1
      forceReadingTime: 0
      downloadEnable: 1
      allowAddSigner: 0
      template_webapiCreatebusinessPresetWithDoc: ${getTemplateId(2,2,pdf,0)}
    export:
      - presetId_BPD
      - businessTypeId_BPD
      - contentName_BPD0
      - contentName_BPD1

- test:
    name: TC1-创建
    api: api/esignDocs/businessPreset/create.yml
    variables:
      presetName: $presetName

- test:
    name: TC2-列表查询-通过名称查询
    api: api/esignDocs/businessPreset/list.yml
    variables:
      queryPresetName: $presetName
    extract:
      - presetId00: content.data.list.0.presetId
      - presetId_BPD: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC2-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetId_BPD
    extract:
      - businessTypeId00: content.data.signBusinessType.businessTypeId
      - businessTypeId_BPD: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.signBusinessType.businessTypeName, $presetName ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: TC3-列表查询-停用
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId_blockUp: $presetId00
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-查询企业模板-1
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      templateId: $template_webapiCreatebusinessPresetWithDoc
    extract:
      - templateName_BPD: content.data.templateName
      - contentId_BPD0: content.data.contentsControl.0.contentId
      - contentName_BPD0: content.data.contentsControl.0.contentName
      - contentId_BPD1: content.data.contentsControl.1.contentId
      - contentName_BPD1: content.data.contentsControl.1.contentName
      - sealControlId_BPD0: content.data.sealControl.0.sealControlId
      - sealControlName_BPD0: content.data.sealControl.0.sealControlName
      - sealControlType_BPD0: content.data.sealControl.0.sealControlType
      - sealControlId_BPD1: content.data.sealControl.1.sealControlId
      - sealControlName_BPD1: content.data.sealControl.1.sealControlName
      - sealControlType_BPD1: content.data.sealControl.1.sealControlType
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - len_gt: [ content.data.templateId, 1 ]

- test:
    name: TC5-查询企业模板-2
    api: api/esignDocs/template/templateList.yml
    variables:
      templateName_templateList: $templateName_BPD
    extract:
      - contentDomainCount_BPD: content.data.list.0.contentDomainCount
      - fileKey_BPD: content.data.list.0.fileKey
      - includeSpecialContent_BPD: content.data.list.0.includeSpecialContent
      - version_BPD: content.data.list.0.version
      - templateType_BPD: content.data.list.0.templateType
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data,null ]

- test:
    name: TC6-step1:填写基本信息 (带有文件模板只能采用PDF格式)
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $presetId00
      presetName: $presetName
      fileFormat: 1
      templateList: [{
        "templateId": $template_webapiCreatebusinessPresetWithDoc ,
        "templateType": $templateType_BPD,
        "templateName": $templateName_BPD,
        "contentDomainCount": $contentDomainCount_BPD,
        "fileKey": $fileKey_BPD,
        "includeSpecialContent": $includeSpecialContent_BPD,
        "version": $version_BPD
      }]
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC7-step2:签署方式
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      presetId_addBusinessType: $presetId00
      businessTypeName: $presetName
      businessTypeId: $businessTypeId00
      signAreaSignEnable: $signAreaSignEnable
      signAreaMoveEnable: $signAreaMoveEnable
      AISignature_innerPerson_addBusinessType: $AISignature_innerPerson_webapiCreatebusinessPresetWithDoc
      AISignature_outerPerson_addBusinessType: $AISignature_outerPerson_webapiCreatebusinessPresetWithDoc
      handwrittenSignature_innerPerson_addBusinessType: $handwrittenSignature_innerPerson_webapiCreatebusinessPresetWithDoc
      handwrittenSignature_outerPerson_addBusinessType: $handwrittenSignature_outerPerson_webapiCreatebusinessPresetWithDoc
      templateSeal_innerPerson_addBusinessType: $templateSeal_innerPerson_webapiCreatebusinessPresetWithDoc
      templateSeal_outerPerson_addBusinessType: $templateSeal_outerPerson_webapiCreatebusinessPresetWithDoc
      handOrImageSeal_innerPerson_addBusinessType: $handOrImageSeal_innerPerson_webapiCreatebusinessPresetWithDoc
      handOrImageSeal_outerPerson_addBusinessType: $handOrImageSeal_outerPerson_webapiCreatebusinessPresetWithDoc
      signEndTimeEnable: $signEndTimeEnable
      forceReadingTime: $forceReadingTime
      downloadEnable: $downloadEnable
      subjectEnable: 0 #发起人填写
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC8-step3:设置签署方式
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId00
      presetName: $presetName
      fileFormat: 1
      presetVersion: 0
      initiatorAll: 1
      initiatorEdit: 1
      status: 0
      allowAddSigner: $allowAddSigner
      signerNodeList:
        - signNode: 2
          signMode: 0
          signerList:
            - signerId: "$signerId_BPD0"
              userCode: "$userCode_BPD0"
              signerType: 1
              onlyUkeySign: 2
              legalSign: 0
              signerTerritory: 1
              needGather: 0
              assignSigner: 1
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（6.0.14.0）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $template_webapiCreatebusinessPresetWithDoc
      - version: $version_BPD
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "业务模板详情"
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      - getPresetId: $presetId00
    extract:
      - _presetName_00: content.data.presetName
      - _presetId_00: content.data.presetId
      - _editUrl_00: content.data.editUrl
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status", 200]
      - ne: ["content.data.presetId",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl_00)}
      tmp_common_template_1: ${putTempEnv(_tmp_biz_template_1, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_biz_template_1)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
      - _fields: content.data.fields
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "获取参与方列表"
    api: api/esignDocs/epaasTemplate/list-template-role.yml
    variables:
      - tplToken_role: ${ENV(_tmp_biz_template_1)}
    extract:
      - _signerId0: content.data.0.id #发起方填写
      - _signerId1: content.data.1.id #签署方
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.data.0.id","0"]
      - eq: ["content.data.0.roleTypes.0","FILL"]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_biz_template_1)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
      _tmp_label_0: "${dataArraySort($_fields, type, 0, label)}"
      _tmp_label_1: "${dataArraySort($_fields, type, 1, label)}"
      _tmp_label_2: "${dataArraySort($_fields, type, 2, label)}"
      _tmp_label_3: "${dataArraySort($_fields, type, 3, label)}"
      _tmp_fill: [ { "label": "$_tmp_label_0", "templateRoleId": "$_signerId1" },
                   { "label": "$_tmp_label_1", "templateRoleId": "$_signerId1" }]
      _tmp_sign: [ { "label": "$_tmp_label_2", "templateRoleId": "$_signerId1" },
                   { "label": "$_tmp_label_3", "templateRoleId": "$_signerId1" }]
      fields_draft: "${getEpaasTemplateContentWithRoleId($_fields,$_tmp_fill,$_tmp_sign)}"
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: TC9-step4:设置内容域填写
    api: api/esignDocs/businessPreset/editTemplateContentDomain.yml
    variables:
      presetId_eTCD: $presetId00
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]