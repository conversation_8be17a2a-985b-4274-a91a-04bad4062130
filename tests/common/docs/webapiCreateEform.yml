- config:
    name: (低代码版本)采集业务：创建信息登记表+创建采集任务（公开，不需要审核）+查询信息库已采信息
    variables:
      formJsonCommon: "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"测试全链路一\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"姓名\",\"key\":\"r105pwcg\",\"model\":\"input_r105pwcg\",\"alias\":\"input_r105pwcg\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"19112100001\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"手机号\",\"key\":\"y9sirtub\",\"model\":\"cellphone_y9sirtub\",\"alias\":\"cellphone_y9sirtub\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\"}}"
      formNameCommon: "全链路登记表_COMMON(勿动)"
      arg1: ${getDateTime()}
      #调试使用参数，真实在用的时候需要注释掉
      formKeyCommon: "form653a6199e4b0f2536b94af31"
      formTaskKeyCommon: "task653a619ae4b0f2536b94af34"
      fieldKey_name01: "input_r105pwcg"
      fieldKey_phone01: "cellphone_y9sirtub"
      ######调试数据
      userCode_BPD0: "${ENV(sign01.userCode)}"
      presetName: "Flink采集专用Eform${getDateTime()}"
      account1: "h1XVxFq95P2SHQ32VGSkR3ZpWo8WRPiDFsZcgp3v/3uSzSFenvegztJl9MIF6BspOJcWY1CaDLXAb2cVnaptcXgDgNB8ng4p5ALDy8E8VfU0sbQDIeYHWk3jPbhw7rxicYvsFI6w41YFKYJATM8plcahaS1PnZfQmZgpFcb8yvk0iypEf0rWE9VILTFbulMwaK/Li829TGTT9KCoYaduxpFJMStrpWw+UuQdDcjRefH81CqZHc9xIOiEcK87eLg1wD0PTzoRHs5fmEFA4LXx3l/xKPrarNwh1X5BGwqTztu+YQs91/8dIfoM2ctLWMGCwV9v4ZRCiQsOJ32VrV5Asw=="

    export:
      - formKeyCommon  #采集登记表Id
      - dataIdCommon
#      - formTaskId #采集任务
      - presetId_BPD
      - businessTypeId_BPD
- test:
    name: TC1-配置登记表的控件
    api: api/esignDocs/eform/formConfig.yml
    variables:
      formJsonConfig: $formJsonCommon
    extract:
      - fieIdsFrom: content.data.fields
      - operationsFrom: content.data.operations
      - fieldKey_name01: content.data.fields.1.fieldKey
      - fieldKey_phone01: content.data.fields.2.fieldKey
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.fields, 1 ]
      - eq: [ content.data.operations, null ]


- test:
    name: TC2-创建采集登记表单
    api: api/esignDocs/eform/formCollectionCreate.yml
    variables:
      formJsonCreate: $formJsonCommon
      formNameCreate: $formNameCommon
      formOperations: $operationsFrom
      formFields: $fieIdsFrom
    extract:
      - formKeyCommon: content.data.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.key, 1 ]
      - startswith: [ content.data.key, "form" ]

- test:
    name: TC3-通过表单ID查询登记表
    api: api/esignDocs/eform/formQuery.yml
    variables:
      formKeyQuery: $formKeyCommon
    extract:
      - formKey2: content.data.data.0.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC4-创建这个采集表单的采集任务
    api: api/esignDocs/eform/formTask.yml
    variables:
      formKeyTask: $formKeyCommon
      nameTask: $formNameCommon
      creatorTask: "${ENV(ceswdzxzdhyhwgd1.account)}"
      organizationCodeTask: "${ENV(ORG-DOCS.orgCode)}"
    extract:
      - formTaskKeyCommon: content.data.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.key, 1 ]

- test:
    name: TC5-查询采集任务
    api: api/esignDocs/eform/formTaskQuery.yml
    variables:
      formTaskKey: $formTaskKeyCommon
    extract:
      - formKeyCommon2: content.data.formKey
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - ne: [ content.data, null ]

#- test:
#    name: 登录获取token
#    api: api/esignDocs/eform/formTaskTokenBuild.yml
#    variables:
#      contactTokenBuild: "${ENV(sign01.mobile)}"
#      taskKeyTokenBuild: $formTaskKeyCommon
#    validate:
#    - eq: [ content.message, "操作成功" ]
#    - eq: [ content.code, 0]
#    - ne: [ content.data, null ]
#    extract:
#       - token001: content.data

- test:
    name: 发送动态验证码-正确
    variables:
        - account: $account1
        - accountType: 4
        - verificationCode: 9999
        - headerVerificationCode: ${get_2()}
        - scene: 8
    api: api/esignLogin/sendDynamicCode.yml
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", True]

- test:
    name: 采集账号校验
    variables:
      account: $account1
      verificationDict: ${get_verification_dict()}
      taskKey: $formTaskKeyCommon
      headerVerificationCode: ${get_value($verificationDict,vertification_code_header)}
    api: api/esignLogin/gatherAccountVerify.yml
    extract:
      token001: content.data.lowCodeToken
    validate:
        - eq: [ "content.status",200]
        - eq: [ "content.message", 成功]
        - eq: [ "content.success", True]

- test:
    name: TC6-填写采集表单并提交
    api: api/esignDocs/eform/formTaskData.yml
    variables:
      authorizationformData: $token001
      tmp001: [$fieldKey_name01,$fieldKey_phone01]
      tmp002:  ["${ENV(sign01.userName)}","${ENV(sign01.mobile)}"]
      tmp003: ${compareDict($tmp001,$tmp002)}
      formData: $tmp003
      formDataKey: $formKeyCommon2
      taskKeyData: $formTaskKeyCommon
    extract:
      - dataIdCommon: content.data.dataId
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.dataId, 1 ]

- test:
    name: "TC8-创建这个场景专用的业务模板-用于采集"
    testcase: common/docs/webapiCreatebusinessPresetWithDoc.yml
    variables:
      presetName: "$presetName"
      userCode_BPD0: "$userCode_BPD0"
    extract:
      - presetId_BPD
      - businessTypeId_BPD
      - contentName_BPD0
      - contentName_BPD1

- test:
    name: TC9-关联业务模板-第一步
    api: api/esignDocs/eform/relationBatchPreset1.yml
    variables:
      params_relationBP1:
        formKey: $formKeyCommon
        presetId: $presetId_BPD
        relationFile:
            annexFileFiledKeys: null
            signFileFiledKeys: null
        relationContentDomains:
            - contentName: $contentName_BPD0
              fieldKey: $fieldKey_name01
            - contentName: $contentName_BPD1
              fieldKey: $fieldKey_name01
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, null ]

- test:
    name: TC10-关联业务模板-第二步
    api: api/esignDocs/eform/relationBatchPreset2.yml
    variables:
      params_relationBP2:
        formKey: $formKeyCommon
        presetId: $presetId_BPD
        relationSigners: []
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data, null ]