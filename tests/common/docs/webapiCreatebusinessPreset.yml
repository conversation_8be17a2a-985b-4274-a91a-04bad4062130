- config:
    name: 创建业务模板配置-电子业务模板
    variables:
      sealCodeCommon: ${getDateTime()}

      #默认个人页面签署方式以下方式都开启，如果有不支持的方式，调用该公共case时将对应的变量值改为0
      AISignature_innerPerson_webapiCreatebusinessPreset: 1 #内部个人AI手绘
      AISignature_outerPerson_webapiCreatebusinessPreset: 1 #个人相对方AI手绘
      handwrittenSignature_innerPerson_webapiCreatebusinessPreset: 1  #内部个人普通手绘
      handwrittenSignature_outerPerson_webapiCreatebusinessPreset: 1  #个人相对方普通手绘
      templateSeal_innerPerson_webapiCreatebusinessPreset: 1  #内部个人模板印章
      templateSeal_outerPerson_webapiCreatebusinessPreset: 1  #个人相对方模板印章
      handOrImageSeal_innerPerson_webapiCreatebusinessPreset: 1 #内部个人手绘印章/图片印章
      handOrImageSeal_outerPerson_webapiCreatebusinessPreset: 1  #个人相对方手绘印章/图片印章
      #调用该公共case时下面这些变量得传你需要的值
#      presetName: "1682663282632"
#      fileFormat: 2 #1-PFD;2-OFD
#      signAreaSignEnable: 1 #1-必签；0-非必签
#      signAreaMoveEnable: 1 #1-可移动；0-不可移动
#      handEnable: 1
#      aiHandEnable: 1
#      signEndTimeEnable: 1
#      forceReadingTime: 0
#      downloadEnable: 1
#      allowAddSigner: 1
#      signerNodeList: null
    export:
      - presetId00
      - businessTypeId00

- test:
    name: TC1-创建
    api: api/esignDocs/businessPreset/create.yml
    variables:
      presetName: $presetName

- test:
    name: TC2-列表查询-通过名称查询
    api: api/esignDocs/businessPreset/list.yml
    variables:
      queryPresetName: $presetName
    extract:
      - presetId00: content.data.list.0.presetId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.presetId, "" ]
      - eq: [ content.data.list.0.presetName, $presetName ]
      - eq: [ content.data.total, 1 ]

- test:
    name: TC2-查询业务模板详情
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetId00
    extract:
      - businessTypeId00: content.data.signBusinessType.businessTypeId
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.signBusinessType.businessTypeName, $presetName ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: TC3-列表查询-停用
    api: api/esignDocs/businessPreset/blockUp.yml
    variables:
      presetId: $presetId00
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step1:填写基本信息
    api: api/esignDocs/businessPreset/addDetail.yml
    variables:
      presetId_addDetail: $presetId00
      presetName: $presetName
      fileFormat: $fileFormat
      templateList: []
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step2:签署方式
    api: api/esignDocs/businessPreset/addBusinessType.yml
    variables:
      presetId_addBusinessType: $presetId00
      businessTypeName: $presetName
      businessTypeId: $businessTypeId00
      signAreaSignEnable: $signAreaSignEnable
      signAreaMoveEnable: $signAreaMoveEnable
      AISignature_innerPerson_addBusinessType: $AISignature_innerPerson_webapiCreatebusinessPreset
      AISignature_outerPerson_addBusinessType: $AISignature_outerPerson_webapiCreatebusinessPreset
      handwrittenSignature_innerPerson_addBusinessType: $handwrittenSignature_innerPerson_webapiCreatebusinessPreset
      handwrittenSignature_outerPerson_addBusinessType: $handwrittenSignature_outerPerson_webapiCreatebusinessPreset
      templateSeal_innerPerson_addBusinessType: $templateSeal_innerPerson_webapiCreatebusinessPreset
      templateSeal_outerPerson_addBusinessType: $templateSeal_outerPerson_webapiCreatebusinessPreset
      handOrImageSeal_innerPerson_addBusinessType: $handOrImageSeal_innerPerson_webapiCreatebusinessPreset
      handOrImageSeal_outerPerson_addBusinessType: $handOrImageSeal_outerPerson_webapiCreatebusinessPreset
      signEndTimeEnable: $signEndTimeEnable
      forceReadingTime: $forceReadingTime
      downloadEnable: $downloadEnable
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]

- test:
    name: TC4-step3:设置签署方式
    api: api/esignDocs/businessPreset/addSigners.yml
    variables:
      presetId: $presetId00
      presetName: $presetName
      fileFormat: $fileFormat
      presetVersion: 0
      allowAddSigner: $allowAddSigner
      signerNodeList: $signerNodeList
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,null ]
