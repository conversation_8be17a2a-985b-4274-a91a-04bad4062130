- config:
    name: 页面发起电子签署:自由签--（可以自定义签署方；可以自定义不同业务类型；支持OFD和PDF）
    variables:
      subject: 'Flink-SSO-${getDateTime()}' #默认标题格式,也可以自定义
      chargingType: 1 #默认1-发起方扣费, 2-相对方扣费
      free: 0 #默认0-自由签署;1-指定签署
      signersList: $signersList
      presetId: $presetId
      fileFormat: $fileFormat
      appendList: $appendList
#      ###调试数据
#      presetId: "fe974cc721d6f967b86556d188082a2f"
#      #pdf
#      fileFormat: 1
##      fileKey0: '${ENV(fileKey)}'
##      fileKey1: '${ENV(1PageFileKey)}'
#      #ofd
##      fileFormat: 2
#      fileKey0: '${ENV(ofdFileKey)}'
#      fileKey1: '${ENV(1PageOFDFileKey)}'
#      appendList: [{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey0"}}]
#      appendList1: [{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey0"}},{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey1"}}]
#      # 如签署方设置3个组成2个节点，2个无序，1个顺序。如下：
#      tmpSigner001: [
#              {"signNode":1,"signMode":1,"userType":1,"userCode":"zhengml","organizationCode":"f877ea68f18b47f29f775eb66363938b","departmentCode":"f877ea68f18b47f29f775eb66363938b"},
#              {"signNode": 1, "signMode": 1, "userType": 1, "userCode": "zhengml"},
#              {"signNode": 2, "signMode": 0, "userType": 2, "userCode": "zhengml2"}
#          ]
#
#      tmpSigner002: [
#        { "signNode": 1,"signMode": 1,"userType": 1,"userCode": "zhengml","organizationCode": "f877ea68f18b47f29f775eb66363938b","departmentCode": "f877ea68f18b47f29f775eb66363938b" }
#      ]
#
#      tmpSigner003: [
#        { "signNode": 1,"signMode": 0,"userType": 1,"userCode": "sign01","organizationCode": "4a0ea22d6ad44675b21ae3516e87a96f" },
#        { "signNode": 2, "signMode": 1, "userType": 1, "userCode": "cesqlly2","organizationCode": "4a0ea22d6ad44675b21ae3516e87a96f" },
#        { "signNode": 2, "signMode": 1, "userType": 2, "userCode": "cesqllwby6","organizationCode": "7411e3dfc40442f88bf432ae58c43759" },
#        { "signNode": 3, "signMode": 2, "userType": 1, "userCode": "sign01" },
#        { "signNode": 3, "signMode": 2, "userType": 1, "userCode": "cesqlly2" }
#      ]
#
#      signersList: ${docSignerListTosubmit($tmpSigner003)}


    export:
      - signFlowId

- test:
    name: 获取发起人关联的组织信息
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - eq: [content.message,成功]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: TC2-查询业务模板详情-获取业务模板名称
    api: api/esignDocs/businessPreset/detail.yml
    variables:
      getPresetId: $presetId
    extract:
      - businessTypeId00: content.data.signBusinessType.businessTypeId
      - batchTemplateTaskNameCommon: content.data.signBusinessType.businessTypeName
    validate:
      - eq: [ content.status,200 ]
      - ne: [ content.data.presetId, "" ]
      - eq: [ content.data.presetType, 0 ]

- test:
    name: 获取batchTemplateInitiationUuid
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: 暂存任务
    variables:
      params:
        fileFormat: $fileFormat
        batchTemplateInitiationName: $batchTemplateTaskNameCommon
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
        initiatorOrganizeCode: $initiatorOrgCodeCommon
        initiatorOrganizeName: $initiatorOrgNameCommon
        initiatorUserName: $initiatorUserNameCommon
        businessPresetUuid: $presetId
        saveSigners: null
        readComplete: null
        type: 3
        sort: 0
        chargingType: 1
        remark: null
        signFlowExpireTime:
        businessNo:
        signersList: $signersList
        appendList: $appendList
        ccInfos: []
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - eq: [ content.data,null ]

- test:
    name: "保存盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      params: "${saveSignature($batchTemplateInitiationUuid1,$free,$signersList,$appendList)}"
    validate:
      - contains: ["content","message"]
      - contains: ["content","status"]
      - eq: ["content.data",null]
      - eq: ["content.status",200]

- test:
    name: 获取batchTemplateInitiationUuid2
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    variables:
      batchTemplateInitiationUuid: "$batchTemplateInitiationUuid1"
    extract:
      - businessPresetSnapshotUuid: content.data.businessPresetDetail.presetSnapshotId
      - signerNodeList: content.data.businessPresetDetail.signerNodeList
      - appendList00: content.data.appendList
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.initiatorUserName,"" ]

- test:
    name: 提交签署
    variables:
      params:
        fileFormat: $fileFormat
        batchTemplateInitiationName: $subject
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
        initiatorOrganizeCode: $initiatorOrgCodeCommon
        initiatorOrganizeName: $initiatorOrgNameCommon
        initiatorUserName: $initiatorUserNameCommon
        businessPresetUuid: $presetId
        businessPresetSnapshotUuid: $businessPresetSnapshotUuid
        saveSigners: null
        readComplete: null
        type: 3
        sort: 0
        chargingType: $chargingType
        remark: null
        signFlowExpireTime:
        businessNo:
        appendList: $appendList00
        ccInfos: [ ]
        signersList: ${getInfoDealToSubmit($signerNodeList)}
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]

#通过电子签署-我发起的列表查询，查询到流程信息
- test:
    name: 通过电子签署-我发起的列表查询，查询到流程信息
    api: api/esignDocs/docFlow/owner_getDocFlowLists.yml
    variables:
      flowName: $subject
      startTime: '${getDateTime(-1, 1)}'
      endTime: '${getDateTime(1, 1)}'
    extract:
      - signFlowId: content.data.list.0.flowId
#      - signFlowId: content.data.list.0.processId
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.status,200 ]
      - ne: [ content.data.list.0.flowId,"" ]
      - eq: [ content.data.list.0.flowStatus, "1" ]
      - eq: [ content.data.list.0.signStatus,"0" ]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}