- config:
    name: "页面发起单次签署:上传1个文件，签署方为内部企业，自由签"
    variables:
      presetId: $presetId
      batchTemplateTaskNameCommon: $signerName
      signerUserCodeCommon: $signerUserCodeCommon
      signerUserNameCommon: $signerUserNameCommon
      toSignFileKeyCommon: ${ENV(fileKey)}
      fileName: ${ENV(fileNamePdf)}
      signerOrganizationCode: $signerOrganizationCode
      signerOrganizationName: $signerOrganizationName
      signerDepartmentCode: $signerDepartmentCode
      signerDepartmentName: $signerDepartmentName

#    output:
#      - batchTemplateTaskNameCommon
#      - signerUserNameCommon
#      - signerUserCodeCommon
#      - initiatorUserNameCommon
#      - initiatorOrgNameCommon
#      - initiatorOrgCodeCommon
#      - presetNameCommon

#- test:
#    name: "业务业务模板名称"
#    api: api/esignDocs/businessPreset/detail.yml
#    variables:
#      - getPresetId: $presetId
#    extract:
#      - presetNameCommon: content.data.presetName
#    validate:
#      - contains: [ content.message,'成功' ]


- test:
    name: "获取发起人关联的组织信息"
    api: api/esignDocs/batchTemplateInitiation/getInitiatorUserInfo.yml
    variables:
      - businessPresetUuid: $presetId
    extract:
      - initiatorOrgCodeCommon: content.data.list.0.organizationCode
      - initiatorOrgNameCommon: content.data.list.0.organizationName
      - initiatorUserNameCommon: content.data.initiatorUserName
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "获取batchTemplateInitiationUuid"
    api: api/esignDocs/batchTemplateInitiation/getInfo.yml
    extract:
      - batchTemplateInitiationUuid1: content.data.batchTemplateInitiationUuid
    validate:
      - contains: [ content.message,'成功' ]
      - eq: [ "content.status",200 ]

- test:
    name: "暂存任务"
    variables:
      data: {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetId,
        "saveSigners": null,
        "readComplete": 0,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 2,
            "signerTerritory": 1,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "organizationCode": $signerOrganizationCode,
            "organizationName": $signerOrganizationName,
            "departmentCode": $signerDepartmentCode,
            "departmentName": $signerDepartmentName
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": $fileName
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/staging.yml
    validate:
      - contains: [ content.message,'成功' ]
      - eq: [ "content.status",200 ]


- test:
    name: "获取盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/detail.yml
    variables:
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid1
    extract:
      - signConfigsNew: content.data.filePreTaskInfos.0.sealInfos.0.signConfigs
      - signerIdNew: content.data.filePreTaskInfos.0.signerId
#      - signPosName1: content.data.docList.0.templateSignConfigs.0.signPosName
    validate:
      - contains: [ content.message,'成功' ]
      - eq: ["content.status",200]

- test:
    name: "保存盖章配置详情"
    api: api/esignDocs/batchTemplateInitiation/signature/save.yml
    variables:
      savedata: {
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "filePreTaskInfos": [
        {
          "sealInfos": [
          {
            "fileKey": $toSignFileKeyCommon,   #$templateFileKeyCommon
            "signConfigs": [
            {
              "addSignDate": false,
              "keywordInfo": null,
              "pageNo": "1",
              "posX": 188,
              "posY": 703,
              "edgeScope": null,
              "sealSignDatePositionInfo": null,
              "signPosName": "",
              "signType": "COMMON-SIGN",
              "signatureType": "COMMON-SEAL"
            }
            ]
          }
          ],
          "signerId": $signerIdNew,
          "userType": 1,
          "userCode": $signerUserCodeCommon,
          "userName": $signerUserNameCommon,
          "signerType": 1,
          "legalSignFlag": 0
        }
        ]
      }
    validate:
      - contains: [ content.message,'成功' ]
      - eq: ["content.status",200]


- test:
    name: "提交签署"
    variables:
      "submitdata": {
        "fileFormat": 1,
        "batchTemplateInitiationName": $batchTemplateTaskNameCommon,
        "batchTemplateInitiationUuid": $batchTemplateInitiationUuid1,
        "initiatorOrganizeCode": $initiatorOrgCodeCommon,
        "initiatorOrganizeName": $initiatorOrgNameCommon,
        "initiatorUserName": $initiatorUserNameCommon,
        "businessPresetUuid": $presetId,
        "saveSigners": null,
        "readComplete": 0,
        "type": 3,
        "signersList": [
        {
          "signMode": 1,
          "signerList": [
          {
            "assignSigner": 1,
            "signerType": 2,
            "signerTerritory": 1,
            "userName": $signerUserNameCommon,
            "userCode": $signerUserCodeCommon,
            "organizationCode": $signerOrganizationCode,
            "organizationName": $signerOrganizationName,
            "departmentCode": $signerDepartmentCode,
            "departmentName": $signerDepartmentName
          }
          ]
        }
        ],
        "appendList": [
        {
          "appendType": 1,
          "attachmentInfo": {
            "fileKey": $toSignFileKeyCommon,
            "fileName": $fileName
          }
        }
        ]
      }
    api: api/esignDocs/batchTemplateInitiation/submit.yml
    validate:
      - contains: [ content.message,'成功' ]
      - eq: [ "content.status",200 ]

