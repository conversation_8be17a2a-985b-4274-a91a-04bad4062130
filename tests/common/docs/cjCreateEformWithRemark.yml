- config:
    name: (低代码版本)采集业务：创建信息登记表+创建备注采集任务
    variables:
      formJsonCommon: "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"测试全链路一\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"姓名\",\"key\":\"r105pwcg\",\"model\":\"input_r105pwcg\",\"alias\":\"input_r105pwcg\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"19112100001\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"手机号\",\"key\":\"y9sirtub\",\"model\":\"cellphone_y9sirtub\",\"alias\":\"cellphone_y9sirtub\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"txt2\",\"key\":\"kxpis4jg\",\"model\":\"input_kxpis4jg\",\"alias\":\"input_kxpis4jg\",\"rules\":[],\"row\":2,\"col\":0},{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"txt3-不支持模糊搜索\",\"key\":\"djdgq47p\",\"model\":\"input_djdgq47p\",\"alias\":\"input_djdgq47p\",\"rules\":[],\"row\":3,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://tianyin6-stable.tsign.cn/esign-docs/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"https://tianyin6-stable.tsign.cn/esign-docs/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\"}}"
      index0: ${get_randomNo_16()}
      formNameCommon: "全链路备注采集模板_${index0}"
      arg1: ${getDateTime()}
      userCode_BPD0: ${ENV(sign01.userCode)}
      userNo_BPD0: ${ENV(sign01.accountNo)}
      org01No: ${ENV(sign01.main.orgNo)}
      org01Code: ${ENV(sign01.main.orgCode)}
      _contactMobile_cj: ***********

    export:
      # 采集模板信息
      - _templateId_cj
      - _collectionTemplateName_cj
      # 备注采集任务信息
      - _collectionTaskId_remark_0
      - _collectionTaskName_remark_0
      # 指定采集任务信息
      - _collectionTaskName_assign_0
      - _collectionTaskId_assign_0
      - _collectionTaskShortUrl_assign_0
      - _collectionTaskUrl_assign_0
      - _customAccountNo_cj
      - _userCode_cj
      - _userName_cj
      - _userMobile_cj
      # 公开采集任务信息
      - _collectionTaskName_public_0
      - _collectionTaskId_public_0
      - _collectionTaskShortUrl_public_0
      - _collectionTaskUrl_public_0


- test:
    name: TC1-配置登记表的控件
    api: api/esignDocs/eform/formConfig.yml
    variables:
      formJsonConfig: $formJsonCommon
    extract:
      - fieIdsFrom: content.data.fields
      - operationsFrom: content.data.operations
      - fieldKey_name01: content.data.fields.1.fieldKey
      - fieldKey_phone01: content.data.fields.2.fieldKey
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.fields, 1 ]
      - eq: [ content.data.operations, null ]


- test:
    name: TC2-创建采集登记表单
    api: api/esignDocs/eform/formCollectionCreate.yml
    variables:
      formJsonCreate: $formJsonCommon
      formNameCreate: $formNameCommon
      formOperations: $operationsFrom
      formFields: $fieIdsFrom
    extract:
      - formKeyCommon: content.data.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.key, 1 ]
      - startswith: [ content.data.key, "form" ]

- test:
    name: TC3-通过表单ID查询登记表
    api: api/esignDocs/eform/formQuery.yml
    variables:
      formKeyQuery: $formKeyCommon
    extract:
      - formKey2: content.data.data.0.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - eq: [ content.data.total, 1 ]

- test:
    name: step1-查询采集模板列表
    api: api/esignDocs/collection/templateList.yml
    variables:
      json:
        collectionTemplateName: ""
        collectionTemplateId: "$formKey2"
        collectionTemplateStartCreateTime: "${getDateTime(-365,1)}"
        collectionTemplateEndCreateTime: "${getDateTime(365,1)}"
        pageNo: 1
        pageSize: 50
    extract:
      - _templateId_cj: content.data.collectionTemplateInfos.0.collectionTemplateId
      - _collectionTemplateName_cj: content.data.collectionTemplateInfos.0.collectionTemplateName
      - collectionTemplateCreatorUserCode0: content.data.collectionTemplateInfos.0.collectionTemplateCreatorUserCode
      - collectionTemplateCreatorCustomAccountNo0: content.data.collectionTemplateInfos.0.collectionTemplateCreatorCustomAccountNo
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - ne: [ content.data.collectionTemplateInfos, ""]
      - ne: [ content.data.collectionTemplateInfos.0.collectionTemplateCreatorUserCode, ""]
      - ne: [ content.data.collectionTemplateInfos.0.collectionTemplateCreatorCustomAccountNo, ""]

- test:
    name: step2-查询采集模板组件
    api: api/esignDocs/collection/templateModuleInfos.yml
    variables:
      collectionTemplateId: $_templateId_cj
    extract:
      - collectionTemplateName1:  content.data.collectionTemplateName
      - moduleId0: content.data.moduleInfos.0.moduleId
      - moduleInfos0:  content.data.moduleInfos
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功"]
      - eq: [ content.data.collectionTemplateName, $_collectionTemplateName_cj]
      - eq: [ content.data.collectionTemplateId, $_templateId_cj]
      - ne: [ content.data.moduleInfos, ""]

- test:
    name: 创建公开采集任务-无需要审核
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $_templateId_cj
      collectionTaskName: "自动化创建公开采集任务无需要审核_${index0}"
      collectionTaskType: 0
      collectionTaskCreatorUserCode: $userCode_BPD0
      collectionTaskCreatorCustomAccountNo: $userNo_BPD0
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 0
      automaticInitiation: 0
      businessTemplateCode: ""
      assignUsers:  []
    extract:
      - _collectionTaskName_public_0: content.data.collectionTaskName
      - _collectionTaskId_public_0: content.data.collectionTaskId
      - _collectionTaskShortUrl_public_0: content.data.collectionTaskShortUrl
      - _collectionTaskUrl_public_0: content.data.collectionTaskUrl
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.collectionTaskName, "自动化创建公开采集任务无需要审核_${index0}" ]
      - ne: [ content.data.collectionTaskId, "" ]
      - ne: [content.data.collectionTaskShortUrl, ""]
      - ne: [content.data.collectionTaskUrl, ""]

- test:
    name: "detail(指定采集任务信息)"
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      detailData:
        customAccountNo: ${ENV(sign01.accountNo)}
    extract:
      - _customAccountNo_cj: content.data.0.customAccountNo
      - _userCode_cj: content.data.0.userCode
      - _userName_cj: content.data.0.name
      - _userMobile_cj: content.data.0.mobile
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]

- test:
    name: 创建指定采集任务-无需要审核
    api: api/esignDocs/collection/taskCreate.yml
    variables:
      collectionTemplateId: $_templateId_cj
      collectionTaskName:  "自动化创建指定采集任务_${index0}"
      collectionTaskType: 1
      collectionTaskCreatorUserCode: $userCode_BPD0
      collectionTaskCreatorCustomAccountNo: $userNo_BPD0
      collectionTaskCreatorOrganizationCode: $org01Code
      collectionTaskCreatorCustomOrgNo: $org01No
      collectionTaskExpireTime: "${getDateTime(5,1)}"
      collectionTaskApprove: 1
      automaticInitiation: 0
      businessTemplateCode: ""
      assignUsers:  [{"assignUserName":"测试采集一","contactContent":"$_contactMobile_cj","contactType": 0},
                     {"assignUserName":"$_userCode_cj","contactContent":"$_userMobile_cj","contactType": 0},
                     {"assignUserName":"测试采集二","contactContent":"<EMAIL>","contactType": 1}]
    extract:
      - _collectionTaskName_assign_0: content.data.collectionTaskName
      - _collectionTaskId_assign_0: content.data.collectionTaskId
      - _collectionTaskShortUrl_assign_0: content.data.collectionTaskShortUrl
      - _collectionTaskUrl_assign_0: content.data.collectionTaskUrl
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.collectionTaskName, "自动化创建指定采集任务_${index0}" ]
      - ne: [ content.data.collectionTaskId, "" ]
      - ne: [content.data.collectionTaskShortUrl, ""]
      - ne: [content.data.collectionTaskUrl, ""]

- test:
    name: 创建备注采集任务
    api: api/esignDocs/eform/formTask.yml
    variables:
      formKeyTask: $formKeyCommon
      nameTask: "备注采集任务COMMON_${index0}"
      creatorTask: "$userNo_BPD0"
      organizationCodeTask: "$org01Code"
      auditTask: false
      endTimeTask: ""
      startTask: false
      taskType: "REMARK_SIGN"
      assignUsers: ""
    extract:
      - _formTaskKey_remark_common: content.data.key
    validate:
      - eq: [ content.code,0 ]
      - eq: [ content.message, "操作成功" ]
      - len_gt: [ content.data.key, 1 ]

- test:
    name: "根据采集任务名称获取备注采集任务id"
    api: api/esignDocs/batchTemplateInitiation/remarkCollectionTaskList.yml
    variables:
      collectionTaskNameRemarkCollectionTaskList: "备注采集任务COMMON_${index0}"
    extract:
      - _collectionTaskId_remark_0: content.data.0.collectionTaskId
      - _collectionTaskName_remark_0: content.data.0.collectionTaskName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.collectionTaskName", "备注采集任务COMMON_${index0}"]
