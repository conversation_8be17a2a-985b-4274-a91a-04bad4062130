- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
      sealUserName: $sealUserName  #这个参数需要调用方入参传入
    export:
      - sealIdPerson
      - fileKeySealImgPsn

- test:
    name: TC1-新建个人印章的图片
    api: api/esignSeals/seals/personal/previewPersonalSeal.yml
    variables:
      sealUserName: $sealUserName
    extract:
      - fileKeySealImgPsn: content.data.fileKey
      - fileKeySealImgPsnCrypto: content.data.cryptoFileKey
    validate:
      - contains: [ content.message,"成功" ]
      - eq: [ content.status,200 ]
      - contains: [ content.data, "fileKey" ]
      - contains: [ content.data, "cryptoFileKey" ]

- test:
    name: TC2-查询内部用户-当前登录人的信息
    api: api/esignManage/InnerUsers/detail.yml
    variables:
      customAccountNoMIDetail: ${ENV(ceswdzxzdhyhwgd1.account)}
    extract:
      - userCode2: content.data.0.userCode
      - userName2: content.data.0.name
      - mainOrganizationCode2: content.data.0.mainOrganizationCode
      - mainCustomOrgName2: content.data.0.mainCustomOrgName
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]
      - gt: [ content.data.0.mainCustomOrgNo, "1" ]
      - gt: [ content.data.0.mainCustomOrgName, "1" ]
      - gt: [ content.data.0.mainOrganizationCode, "1" ]

- test:
    name: TC3-查询内部用户-需要创建个人印章的用户
    api: api/esignSigns/esignManage/InnerUsers/detail.yml
    variables:
      detail_data:
        name: $sealUserName
    extract:
      - userCode3: content.data.0.userCode
      - userName3: content.data.0.name
      - mainOrganizationCode3: content.data.0.mainOrganizationCode
      - mainCustomOrgName3: content.data.0.mainCustomOrgName
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - contains: [ content.data.0, "email" ]
      - contains: [ content.data.0, "mobile" ]
      - contains: [ content.data.0, "licenseType" ]
      - contains: [ content.data.0, "licenseNo" ]
      - contains: [ content.data.0, "bankCardNo" ]
      - contains: [ content.data.0, "otherOrganization" ]

- test:
    name: TC4-创建个人印章
    api: api/esignSeals/personal/savePersonalSeal.yml
    variables:
      sealUserName: $sealUserName
      managerOrgCode: $mainOrganizationCode2
      managerOrgName: $mainCustomOrgName2
      sealName: $sealUserName
      ownerCode: $userCode3
      ownerName: $sealUserName
      ownerOrganizationName: $mainCustomOrgName3
      ownerOrganizationCode: $mainOrganizationCode3
      cryptoFileKey: $fileKeySealImgPsnCrypto
      sealThumbnailUrl: $fileKeySealImgPsn
      ukeyInfoVO1:
      ukeyAsn1InfoVO1:

- test:
    name: TC5-查询个人商密印章-通过印章名称
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      userCode_usersealsList: $userCode3
    extract:
      - sealIdPerson: content.data.records.0.sealId
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.code,200 ]
      - ge: [ content.data.total, 1 ]
      - contains: [ content.data.records.0, "sealId" ]
      - contains: [ content.data.records.0, "base64img" ]
      - contains: [ content.data.records.0, "sealName" ]
