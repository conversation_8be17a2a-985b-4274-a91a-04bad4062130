- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：sign01(主职企业有SM2证书）
    variables:
      sealTypeCodeCommon: ************* #自动化通用印章类型
      organizationName1: $organizationName1  #这个参数需要调用方入参传入
      accountCommon1: ${ENV(sign01.accountNo)}
      passwordCommon1: ${ENV(passwordEncrypt)}
    #      organizationName: "esigntest天印PO测试企业"  #这个参数需要调用方入参传入
#      sealCodeCommon: ${getDateTime()}
#      sealTypeCodeCommon: ${getTimestamp(0)}
    export:
      - sealIdOrgAsn1
      - sealTypeCodeCommon
      - orgCerIdSM2
      - orgCerIdSM2_ORGDOCS

## 创建SM2证书 #####
### 制章者证书 ceswdzxzdhyhwgd1 所属企业 esigntest文档中心自动化测试公司勿改动 的企业SM2证书
### 签章者证书：企业证书
#- test:
#    name: "TC1-查询内部用户:ceswdzxzdhyhwgd1 获取所属组织"
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      detailData:
#        customAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
#        userCodeDetail: ''
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - userMobile2: content.data.0.mobile
#      - customAccountNo2: content.data.0.customAccountNo
#      - licenseNo2: content.data.0.licenseNo
#      - mainCustomOrgNo2: content.data.0.mainCustomOrgNo
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - contains: [ content.data.0, "mobile" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: "TC2-查询内部企业信息"
#    api: api/esignManage/InnerOrganizations/detail.yml
#    variables:
#      organizationCode: $mainOrganizationCode2
#    extract:
#      - orgName001_1: content.data.0.name
#      - organizationCode001_1: content.data.0.organizationCode
#      - licenseNo001_1: content.data.0.licenseNo
#      - legalRepAccountNo001_1: content.data.0.legalRepAccountNo
#      - legalRepUserCode001_1: content.data.0.legalRepUserCode
#      - legalRepName001_1: content.data.0.legalRepName
#      - legalRepUserCode001_1: content.data.0.legalRepUserCode
#    validate:
#      - eq: [ content.code,200 ]
#      - eq: [ content.message,'成功' ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: "TC3-创建内部企业的SM2证书:esigntest文档中心自动化测试公司勿改动"
#    api: api/esignSeals/enterprise/saveEnterpriseCert.yml
#    variables:
#      organizationName: $orgName001_1
#      organizationCode: $organizationCode001_1
#      certName: $orgName001_1
#      userCode: $userCode2
#      certAlgorithm: 2
#    extract:
#      - orgCerIdSM2_ORGDOCS: content.message
#    validate:
#      - eq: [ content.status,200 ]
#      - ne: [ content.message, "" ]
#      - eq: [ content.data, "" ]

- test:
    name: "TC4-查询内部企业信息"
    api: api/esignManage/InnerOrganizations/detail.yml
    variables:
      nameOrgDetail: $organizationName1
    extract:
      - orgName002: content.data.0.name
      - organizationCode002: content.data.0.organizationCode
      - licenseNo002: content.data.0.licenseNo
      - legalRepAccountNo002: content.data.0.legalRepAccountNo
      - legalRepUserCode002: content.data.0.legalRepUserCode
      - legalRepName002: content.data.0.legalRepName
      - legalRepUserCode002: content.data.0.legalRepUserCode
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message,'成功' ]
      - ne: [ content.data, "" ]

- test:
    name: "TC5-创建内部企业的SM2证书"
    api: api/esignSeals/enterprise/saveEnterpriseCert.yml
    variables:
      organizationName: $orgName002
      organizationCode: $organizationCode002
      certName: $orgName002
      certAlgorithm: 2
    extract:
      - orgCerIdSM2: content.message
    validate:
      - eq: [ content.status,200 ]
      - eq: [ content.message, "服务器成功返回" ]
      - ne: [ content.data, "" ]
##############################################################
#- test:
#    name: TC6-新建印章类型
#    api: api/esignSeals/sealType/saveSealType.yml
#    variables:
#      sealTypeCode: $sealTypeCodeCommon
#      sealTypeName: $sealTypeCodeCommon
#
#- test:
#    name: TC7-分配印章管理员-通过企业印章查询企业
#    api: api/esignSeals/manage/permissions/data/searchCompanyByName.yml
#    variables:
#      organizationName: $organizationName
#    extract:
#      organizationIdSeal01: content.data.0.id
#      organizationNameSeal01: content.data.0.organizationName
#      organizationCodeSeal01: content.data.0.organizationCode
#    validate:
#      - eq: [ content.status,200 ]
#      - ne: [ content.data.0.id, "" ]
#      - eq: [ content.data.0.organizationName, $organizationName ]
#
#- test:
#    name: TC8-分配印章管理员-保存分配设置
#    api: api/esignSeals/sealmanager/saveSealManager.yml
#    variables:
#      organizationId: $organizationIdSeal01   #机构ID
#      sealTypeName: $sealTypeCodeCommon   #印章类型名称
#      organizationName: $organizationNameSeal01   #机构名称
#      organizationCode: $organizationCodeSeal01   #机构编码
#      sealTypeCode: $sealTypeCodeCommon   #印章类型编码
#      enableMultipleSeals: 1   #是否允许创建多个印章（0否 1是）
#      sealManagerCode: $userCode2   #印章管理员编码
#      sealManagerName: $userName2   #印章管理员名称
#      userCode:
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC81-分配印章管理员-更新已有的管理员信息
#    api: api/esignSeals/sealmanager/updateSealManager.yml
#    variables:
#      organizationId: $organizationIdSeal01   #机构ID
#      sealTypeName: $sealTypeCodeCommon   #印章类型名称
#      organizationName: $organizationNameSeal01   #机构名称
#      organizationCode: $organizationCodeSeal01   #机构编码
#      sealTypeCode: $sealTypeCodeCommon   #印章类型编码
#      enableMultipleSeals: 1   #是否允许创建多个印章（0否 1是）
#      sealManagerCode: $userCode2   #印章管理员编码
#      sealManagerName: $userName2   #印章管理员名称
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC9-创建企业印章-形成印章图片
#    api: api/esignSeals/seals/sealmodel/previewElectronicSeal.yml
#    variables:
#      sealSurroundword: $organizationName
#    extract:
#      - fileKeySealImg: content.data.fileKey
#      - fileKeySealImgCrypto: content.data.cryptoFileKey
#    validate:
#      - eq: [ content.status,200 ]
#      - contains: [ content.message,'成功' ]
#      - ne: [ content.data.fileKey, "" ]
#
#- test:
#    name: TC10-创建企业印章
#    api: api/esignSeals/enterprise/saveElectronicSeal.yml
#    variables:
#      organizationCode: $organizationCodeSeal01
#      organizationName: $organizationNameSeal01
#      organizationId: $organizationIdSeal01
#      sealCode: $sealCodeCommon
#      sealName: $sealCode
#      sealSurroundword: $organizationNameSeal01
#      sealThumbnailUrl: $fileKeySealImg
#      sealTypeCode: $sealTypeCodeCommon
#      sealTypeName: $sealTypeCodeCommon
#      sealChargeOrganizationCode: $mainOrganizationCode2
#      sealChargeOrganizationName: $mainCustomOrgName2
#      managerOrgCode: $organizationCodeSeal01
#      managerOrgName: $organizationNameSeal01
#      sealUserList2: []
#      sealBodyStructure: 2
#      sealPatternSlected: "1,2"
#      makeSealCertId: $orgCerIdSM2_ORGDOCS
#      signSealCertIds: [ "$orgCerIdSM2" ]
#    extract:
#      - sealIdOrgAsn1: content.message
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC11-授权用印人为全部
#    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
#    variables:
#      id: $sealIdOrgAsn1
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#

- test:
    name: TC2-查询企业印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      tmp00: {'organizationCode': $organizationCode002 ,'sealTypeCode': $sealTypeCodeCommon, 'sealPatternSlected': 3}
      tmp01: '${getEnterpriseSeal($tmp00)}'
      organizationSeals_list_sealTypeCode: $sealTypeCodeCommon
      organizationSeals_list_organizationCode: $organizationCode002
      organizationSeals_list_sealPattern: 3
    extract:
      - sealIdOrgAsn1: content.data.records.0.sealId
      - sealTypeCodeCommon: content.data.records.0.sealTypeCode
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]

- test:
    name: TC-根据sealId查询sealGroupId
    api: api/esignSigns/seals/groupPageList.yml
    variables:
      json:
        params:
          remoteSealId: $sealIdOrgAsn1
          defaultSeal: false
          myChargeSealGroup: false
          currPage: 1
          pageSize: 10
        domian: "seal_system"
    extract:
        sealGroupIdCommon1: content.data.list.0.sealGroupId
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.success, true ]


- test:
    name: TC-查询印章分组中印章id
    api: api/esignSigns/seals/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupIdCommon1
      remoteSealId: $sealIdOrgAsn1
    extract:
      ex_SealIdCommon1: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

-   test:
      name: TC-企业印章授权可用项目为1000000
      api: api/esignSigns/seals/authSealProject.yml
      variables:
        authorization0: ${getPortalToken($accountCommon1,$passwordCommon1)}
        sealId: $ex_SealIdCommon1
        sealProjectRange: 2
        projectIds: ['${ENV(esign.projectId)}']
      validate:
        - eq: [content.status, 200]
        - eq: [content.message, '服务器成功返回']
        - eq: [content.success, True]

-   test:
      name: TC-企业印章授权1000000项目自动签署
      api: api/esignSigns/seals/authAutoSignSealProject.yml
      variables:
        authorization0: ${getPortalToken($accountCommon1,$passwordCommon1)}
        sealId: $ex_SealIdCommon1
        projectIds: ['${ENV(esign.projectId)}']
      validate:
        - eq: [content.status, 200]
        - eq: [content.message, '服务器成功返回']
        - eq: [content.success, True]