- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
      sealTypeCodeCommon: *************
#      sealTypeCodeCommon: "COMMON-SEAL"
      organizationName: $organizationName  #这个参数需要调用方入参传入
#      sealCodeCommon: ${getDateTime()}
      accountCommon1: ${ENV(sign01.accountNo)}
      passwordCommon1: ${ENV(passwordEncrypt)}

      ###调试用的参数
#      organizationName: "esigntest天印PO测试企业"
#      sealTypeCodeCommon: WMG
    export:
      - sealIdOrg
      - sealTypeCodeCommon

#- test:
#    name: TC1-新建印章类型
#    api: api/esignSeals/sealType/saveSealType.yml
#    variables:
#      sealTypeCode: $sealTypeCodeCommon
#      sealTypeName: $sealTypeCodeCommon
#
- test:
    name: TC1-分配印章管理员-通过企业印章查询企业
    api: api/esignSeals/manage/permissions/data/searchCompanyByName.yml
    variables:
      organizationName: $organizationName
    extract:
      organizationIdSeal01: content.data.0.id
      organizationNameSeal01: content.data.0.organizationName
      organizationCodeSeal01: content.data.0.organizationCode
    validate:
      - contains: [ content, "message" ]
      - contains: [ content, "status" ]
      - contains: [ content, "data" ]
#
#- test:
#    name: TC3-查询内部用户
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      customAccountNoMIDetail: ${ENV(ceswdzxzdhyhwgd1.account)}
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - gt: [ content.data.0.name, "1" ]
#      - contains: [ content.data.0, "otherOrganization" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: TC4-分配印章管理员-保存分配设置
#    api: api/esignSeals/sealmanager/saveSealManager.yml
#    variables:
#      organizationId: $organizationIdSeal01   #机构ID
#      sealTypeName: $sealTypeCodeCommon   #印章类型名称
#      organizationName: $organizationNameSeal01   #机构名称
#      organizationCode: $organizationCodeSeal01   #机构编码
#      sealTypeCode: $sealTypeCodeCommon   #印章类型编码
#      enableMultipleSeals: 1   #是否允许创建多个印章（0否 1是）
#      sealManagerCode: $userCode2   #印章管理员编码
#      sealManagerName: $userName2   #印章管理员名称
#      userCode:
#
#- test:
#    name: TC5-创建企业印章-形成印章图片
#    api: api/esignSeals/seals/sealmodel/previewElectronicSeal.yml
#    variables:
#      sealSurroundword: $organizationName
#    extract:
#      - fileKeySealImg: content.data.fileKey
#      - fileKeySealImgCrypto: content.data.cryptoFileKey
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC6-创建企业印章
#    api: api/esignSeals/enterprise/saveElectronicSeal.yml
#    variables:
#      organizationCode: $organizationCodeSeal01
#      organizationName: $organizationNameSeal01
#      organizationId: $organizationIdSeal01
#      sealCode: $sealCodeCommon
#      sealName: $sealCode
#      sealSurroundword: $organizationNameSeal01
#      sealThumbnailUrl: $fileKeySealImg
#      sealTypeCode: $sealTypeCodeCommon
#      sealTypeName: $sealTypeCodeCommon
#      sealChargeOrganizationCode: $mainOrganizationCode2
#      sealChargeOrganizationName: $mainCustomOrgName2
#      managerOrgCode: $organizationCodeSeal01
#      managerOrgName: $organizationNameSeal01
#      sealUserList2: []
#    extract:
#      - sealIdOrg: content.message
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC7-授权用印人为全部
#    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
#    variables:
#      id: $sealIdOrg
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]

- test:
    name: TC2-查询企业印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      tmp00: {'organizationCode': $organizationCodeSeal01 ,'sealTypeCode': $sealTypeCodeCommon}
      tmp01: '${getEnterpriseSeal($tmp00)}'
      organizationSeals_list_sealTypeCode: $sealTypeCodeCommon
      organizationSeals_list_organizationCode: $organizationCodeSeal01
      organizationSeals_list_sealPattern: 1
    extract:
      - sealIdOrg: content.data.records.0.sealId
      - sealTypeCodeCommon: content.data.records.0.sealTypeCode
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]

- test:
    name: TC-根据sealId查询sealGroupId
    api: api/esignSigns/seals/groupPageList.yml
    variables:
      json:
        params:
          remoteSealId: $sealIdOrg
          defaultSeal: false
          myChargeSealGroup: false
          currPage: 1
          pageSize: 10
        domian: "seal_system"
    extract:
        sealGroupIdCommon1: content.data.list.0.sealGroupId
    validate:
        - eq: [ content.status, 200 ]
        - eq: [ content.success, true ]


- test:
    name: TC-查询印章分组中印章id
    api: api/esignSigns/seals/listPageEnterprise.yml
    variables:
      sealGroupId: $sealGroupIdCommon1
      remoteSealId: $sealIdOrg
    extract:
      ex_SealIdCommon1: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message, "成功" ]

-   test:
      name: TC-企业印章授权可用项目为1000000
      api: api/esignSigns/seals/authSealProject.yml
      variables:
        authorization0: ${getPortalToken($accountCommon1,$passwordCommon1)}
        sealId: $ex_SealIdCommon1
        sealProjectRange: 2
        projectIds: ['${ENV(esign.projectId)}']
      validate:
        - eq: [content.status, 200]
        - eq: [content.message, '服务器成功返回']
        - eq: [content.success, True]

-   test:
      name: TC-企业印章授权1000000项目自动签署
      api: api/esignSigns/seals/authAutoSignSealProject.yml
      variables:
        authorization0: ${getPortalToken($accountCommon1,$passwordCommon1)}
        sealId: $ex_SealIdCommon1
        projectIds: ['${ENV(esign.projectId)}']
      validate:
        - eq: [content.status, 200]
        - eq: [content.message, '服务器成功返回']
        - eq: [content.success, True]