- config:
    name: 通过统一门户印控中心接口创建法人印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
#      sealUserName: $sealUserName  #这个参数需要调用方入参传入-企业法人的姓名
#      organizationName: $organizationName  #这个参数需要调用方入参传入-需要创建法人印章的企业名称
#      sealCodeCommon: ${getDateTime()}
      organizationName1: $organizationName1

      sealIdPersonAsn1: $sealIdPersonAsn1
#      sealUserName: '测试全链路一'
#      organizationName: 'esigntest混合云全链路一测试公司'
#      sealIdPersonAsn1: "1666264052323549185"
#      fileKeySealImgPsn: "1686103282151_lQ74G2yg.png"
#      orgCerIdSM2_ORGDOCS: "1666264045520388098"
#      userCerIdSM2: "1666264050016681986"
#      sealUserName: $sealUserName  #这个参数需要调用方入参传入-企业法人的姓名
#      organizationName: $organizationName  #这个参数需要调用方入参传入-需要创建法人印章的企业名称
    export:
      - sealIdLegal

## 创建SM2证书 #####
## 制章者证书 ceswdzxzdhyhwgd1 所属企业 esigntest文档中心自动化测试公司勿改动 的企业SM2证书
## 签章者证书：个人证书
#- test:
#    name: "TC1-创建内部个人印章ASN1"
#    testcase: common/Seal/webapiCreatePersonSealAsn1.yml
#    variables:
#      sealUserName: $sealUserName
#    extract:
#      - sealIdPersonAsn1
##      - fileKeySealImgPsn
#      - orgCerIdSM2_ORGDOCS
#      - userCerIdSM2

- test:
    name: TC2-分配印章管理员-通过企业印章查询企业
    api: api/esignSeals/manage/permissions/data/searchCompanyByName.yml
    variables:
      organizationName: $organizationName1
    extract:
      organizationIdSeal01: content.data.0.id
      organizationNameSeal01: content.data.0.organizationName
      organizationCodeSeal01: content.data.0.organizationCode
    validate:
      - eq: [ content.status,200 ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - ne: [ content.data.0.organizationCode, null ]

#- test:
#    name: "TC3-查询内部用户:ceswdzxzdhyhwgd1 获取所属组织"
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      customAccountNoMIDetail: ${ENV(ceswdzxzdhyhwgd1.account)}
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - userMobile2: content.data.0.mobile
#      - customAccountNo2: content.data.0.customAccountNo
#      - licenseNo2: content.data.0.licenseNo
#      - mainCustomOrgNo2: content.data.0.mainCustomOrgNo
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - contains: [ content.data.0, "mobile" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]

- test:
    name: TC4-查询企业法人印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      tmp00: {'organizationCode': $organizationCodeSeal01 ,'personalSealId': $sealIdPersonAsn1, 'sealPatternSlected': 3}
      tmp01: '${getEnterpriseSeal($tmp00)}'
      organizationSeals_list_sealTypeCode: "LEGAL-PERSON-SEAL"
      organizationSeals_list_organizationCode: $organizationCodeSeal01
      organizationSeals_list_sealPattern: 3
    extract:
      - sealIdLegal: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]

#- test:
#    name: TC4-分配印章管理员-保存分配设置
#    api: api/esignSeals/sealmanager/saveSealManager.yml
#    variables:
#      organizationId: $organizationIdSeal01   #机构ID
#      sealTypeName: "法人章"   #印章类型名称
#      organizationName: $organizationNameSeal01   #机构名称
#      organizationCode: $organizationCodeSeal01   #机构编码
#      sealTypeCode: "LEGAL-PERSON-SEAL"   #印章类型编码
#      enableMultipleSeals: 0   #是否允许创建多个印章（0否 1是）
#      sealManagerCode: $userCode2   #印章管理员编码
#      sealManagerName: $userName2   #印章管理员名称
#      userCode:
#
#- test:
#    name: TC5-创建企业法人印章印章
#    api: api/esignSeals/enterprise/saveElectronicSeal.yml
#    variables:
#      organizationCode: $organizationCodeSeal01
#      organizationName: $organizationNameSeal01
#      organizationId: $organizationIdSeal01
#      sealCode: $sealCodeCommon
#      sealName: $sealCode
#      sealSurroundword: $organizationNameSeal01
#      sealThumbnailUrl: $fileKeySealImgPsn
#      sealTypeCode: "LEGAL-PERSON-SEAL"
#      sealTypeName: "法人章"
#      sealChargeOrganizationCode: $mainOrganizationCode2
#      sealChargeOrganizationName: $mainCustomOrgName2
#      managerOrgCode: $organizationCodeSeal01
#      managerOrgName: $organizationNameSeal01
#      sealUserList2: []
#      personalSealId: $sealIdPersonAsn1
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC7-查询企业的法人印章印章
#    api: api/esignSeals/electronic/pageElectronicSealList.yml
#    variables:
#      organizationName: $organizationNameSeal01
#      sealStatus: "g"
#    extract:
#      - sealIdLegal: content.data.list.0.sealId
#    validate:
#      - eq: [ content.status, 200 ]
#      - contains: [ content.message,'成功' ]
#      - ge: [ content.data.totalCount, 1 ]
#
#- test:
#    name: TC5-停用法人印章
#    api: api/esignSeals/enterprise/disableSeal.yml
#    variables:
#      sealIdDisable: $sealIdLegal
#    extract:
#      - sealIdLegal: content.message
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC6-更新企业法人印章印章
#    api: api/esignSeals/enterprise/updateElectronicSeal.yml
#    variables:
#      id: $sealIdLegal
#      organizationCode: $organizationCodeSeal01
#      organizationName: $organizationNameSeal01
#      organizationId: $organizationIdSeal01
#      sealCode: $sealCodeCommon
#      sealName: $sealCode
#      sealSurroundword: $organizationNameSeal01
#      sealThumbnailUrl: $fileKeySealImgPsn
#      sealTypeCode: "LEGAL-PERSON-SEAL"
#      sealTypeName: "法人章"
#      sealPatternSlected: "1,2"
#      sealChargeOrganizationCode: $mainOrganizationCode2
#      sealChargeOrganizationName: $mainCustomOrgName2
#      managerOrgCode: $organizationCodeSeal01
#      managerOrgName: $organizationNameSeal01
#      sealUserList2: []
#      personalSealId: $sealIdPersonAsn1
#      makeSealCertId: $orgCerIdSM2_ORGDOCS
#      signSealCertIds: [ "$userCerIdSM2" ]
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC7-发布法人印章
#    api: api/esignSeals/enterprise/publishSeal.yml
#    variables:
#      sealId: $sealIdLegal
#    extract:
#      - sealIdLegal: content.message
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC8-授权用印人为全部
#    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
#    variables:
#      id: $sealIdLegal
#    validate:
#      - ne: [ content.message, "" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.success,true ]
#      - eq: [ content.data, "" ]


