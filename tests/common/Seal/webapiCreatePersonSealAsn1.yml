- config:
    name: 通过统一门户印控中心接口创建个人国密印章（自带商密形态）-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
      sealUserName1: $sealUserName1  #这个参数需要调用方入参传入
#      sealUserName: '测试全链路一'  #这个参数需要调用方入参传入
    export:
      - sealIdPersonAsn1
      - fileKeySealImgPsn
      - orgCerIdSM2_ORGDOCS
      - userCerIdSM2

### 创建SM2证书 #####
### 制章者证书 ceswdzxzdhyhwgd1 所属企业 esigntest文档中心自动化测试公司勿改动 的企业SM2证书
### 签章者证书：个人证书
#- test:
#    name: "TC1-查询内部用户:ceswdzxzdhyhwgd1 获取所属组织"
#    api: api/esignManage/InnerUsers/detail.yml

#    variables:
#      detailData:
#        customAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - userMobile2: content.data.0.mobile
#      - customAccountNo2: content.data.0.customAccountNo
#      - licenseNo2: content.data.0.licenseNo
#      - mainCustomOrgNo2: content.data.0.mainCustomOrgNo
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - gt: [ content.data.0.name, "1" ]
#      - contains: [ content.data.0, "email" ]
#      - contains: [ content.data.0, "mobile" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: "TC2-查询内部企业信息"
#    api: api/esignManage/InnerOrganizations/detail.yml
#    variables:
#      organizationCode: $mainOrganizationCode2
#    extract:
#      - orgName001_1: content.data.0.name
#      - organizationCode001_1: content.data.0.organizationCode
#      - licenseNo001_1: content.data.0.licenseNo
#      - legalRepAccountNo001_1: content.data.0.legalRepAccountNo
#      - legalRepUserCode001_1: content.data.0.legalRepUserCode
#      - legalRepName001_1: content.data.0.legalRepName
#      - legalRepUserCode001_1: content.data.0.legalRepUserCode
#    validate:
#      - eq: [ content.code,200 ]
#      - eq: [ content.message,'成功' ]
#      - ne: [ content.data, "" ]
#
#- test:
#    name: "TC3-创建内部企业的SM2证书:esigntest文档中心自动化测试公司勿改动"
#    api: api/esignSeals/enterprise/saveEnterpriseCert.yml
#    variables:
#      organizationName: $orgName001_1
#      organizationCode: $organizationCode001_1
#      certName: $orgName001_1
#      userCode: $userCode2
#      certAlgorithm: 2
#    extract:
#      - orgCerIdSM2_ORGDOCS: content.data
#    validate:
#      - eq: [ content.status,200 ]
#      - contains: [ content.message,"成功" ]
#      - ne: [ content.data, null ]
#      - eq: [ content.success, True ]
#
- test:
    name: "TC4-查询内部用户"
    api: api/esignSigns/esignManage/InnerUsers/detail.yml
    variables:
        nameManageDetail: $sealUserName1
    extract:
      - userCode3: content.data.0.userCode
      - userName3: content.data.0.name
      - customAccountNo3: content.data.0.customAccountNo
      - licenseNo3: content.data.0.licenseNo
      - mainCustomOrgNo3: content.data.0.mainCustomOrgNo
      - mainCustomOrgName3: content.data.0.mainCustomOrgName
      - mainOrganizationCode3: content.data.0.mainOrganizationCode
    validate:
      - eq: [ content.code,200 ]
      - ne: [ content.data.0.userCode, "" ]
      - gt: [ content.data.0.customAccountNo, "1" ]
      - gt: [ content.data.0.name, "1" ]
      - contains: [ content.data.0, "email" ]
      - contains: [ content.data.0, "mobile" ]
      - ne: [ content.data.0.mainOrganizationCode, null ]

#- test:
#    name: "TC5-创建内部个人的SM2证书"
#    api: api/esignSeals/personal/savePersonalCert.yml
#    variables:
#      certName: $userName3   #证书名称
#      licenseNumber: ${get_encrypt($licenseNo3)}   #证件号
#      userCode: $userCode3  #用户编码
#      userName: $userName3
#      certAlgorithm: 2
#    extract:
#      - userCerIdSM2: content.data
#    validate:
#      - eq: [ content.status,200 ]
#      - len_eq: [ content.data, 19 ]
#      - eq: [ content.success, True ]
#    teardown_hooks:
#      - ${hook_sleep_n_secs(2)}

- test:
    name: "TC5-创建内部个人的SM2证书"
    api: api/esignSeals/v1/sealcontrols/userCerts/userCertsList.yml
    variables:
      tmp: "${getUserCert($userCode3,2)}"
      algorithm_userCertsList: 2
      userCode_userCertsList: $userCode3
      customAccountNo_userCertsList: ""
    extract:
      - userCerIdSM2: content.data.0.certId
    validate:
      - eq: [ content.code,200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data, 1 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
################################################

#- test:
#    name: TC6-新建个人印章的图片
#    api: api/esignSeals/seals/psersonal/previewPersonalSeal.yml
#    variables:
#      sealUserName: $sealUserName
#    extract:
#      - fileKeySealImgPsn: content.data.fileKey
#      - fileKeySealImgPsnCrypto: content.data.cryptoFileKey
#    validate:
#      - contains: [ content.message,'成功' ]
#      - eq: [ content.status,200 ]
#      - contains: [ content.data, "fileKey" ]
#      - contains: [ content.data, "cryptoFileKey" ]
#
#- test:
#    name: TC7-创建个人印章
#    api: api/esignSeals/personal/savePersonalSeal.yml
#    variables:
#      sealUserName: $sealUserName
#      managerOrgCode: $mainOrganizationCode2
#      managerOrgName: $mainCustomOrgName2
#      sealName: "ASN1-$sealUserName"
#      ownerCode: $userCode3
#      ownerName: $sealUserName
#      ownerOrganizationName: $mainCustomOrgName3
#      ownerOrganizationCode: $mainOrganizationCode3
#      cryptoFileKey: $fileKeySealImgPsnCrypto
#      sealThumbnailUrl: $fileKeySealImgPsn
#      makeSealCertId: $orgCerIdSM2_ORGDOCS
#      signSealCertIds: ["$userCerIdSM2"]
#      sealBodyStructure: 2
#      ukeyInfoVO1:
#      ukeyAsn1InfoVO1:

- test:
    name: TC8-查询个人国密印章-通过印章名称
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      tmp00: ${getUserSeal($userCode3,3)}
      sealName: ""
      userCode_usersealsList: $userCode3
    extract:
      - sealIdPersonAsn1: content.data.records.0.sealId
    validate:
      - eq: [ content.message,成功 ]
      - eq: [ content.code,200 ]
      - ge: [ content.data.total, 1 ]
      - contains: [ content.data.records.0, "sealId" ]
      - contains: [ content.data.records.0, "base64img" ]
      - contains: [ content.data.records.0, "sealName" ]
