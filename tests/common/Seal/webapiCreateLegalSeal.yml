- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
      sealUserName: $sealUserName  #这个参数需要调用方入参传入
      organizationName: $organizationName  #这个参数需要调用方入参传入
#      sealCodeCommon: ${getDateTime()}
    export:
      - sealIdLegal

- test:
    name: TC1-创建内部个人印章
    testcase: common/Seal/webapiCreatePersonSeal.yml
    variables:
      sealUserName: $sealUserName
    extract:
      - sealIdPerson
      - fileKeySealImgPsn

- test:
    name: TC2-分配印章管理员-通过企业印章查询企业
    api: api/esignSeals/manage/permissions/data/searchCompanyByName.yml
    variables:
      organizationName: $organizationName
    extract:
      organizationIdSeal01: content.data.0.id
      organizationNameSeal01: content.data.0.organizationName
      organizationCodeSeal01: content.data.0.organizationCode
    validate:
      - contains: [ content.message,'成功' ]
      - eq: [ content.status,200 ]
      - contains: [ content.data.0, "orderNum" ]
      - gt: [ content.data.0.id, "1" ]
      - gt: [ content.data.0.organizationName, "1" ]
      - len_gt: [ content.data.0.organizationCode, 1 ]
      - eq: [ content.data.0.organizationTerritory, "1" ]
      - eq: [ content.data.0.organizationType, "1" ]

#- test:
#    name: TC3-查询内部用户
#    api: api/esignManage/InnerUsers/detail.yml
#    variables:
#      detailData:
#        customAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
#        mobile: "${ENV(ceswdzxzdhyhwgd1.mobile)}"
#    extract:
#      - userCode2: content.data.0.userCode
#      - userName2: content.data.0.name
#      - mainOrganizationCode2: content.data.0.mainOrganizationCode
#      - mainCustomOrgName2: content.data.0.mainCustomOrgName
#    validate:
#      - eq: [ content.code,200 ]
#      - ne: [ content.data.0.userCode, "" ]
#      - gt: [ content.data.0.customAccountNo, "1" ]
#      - gt: [ content.data.0.name, "1" ]
#      - contains: [ content.data.0, "email" ]
#      - contains: [ content.data.0, "mobile" ]
#      - contains: [ content.data.0, "licenseType" ]
#      - contains: [ content.data.0, "licenseNo" ]
#      - contains: [ content.data.0, "bankCardNo" ]
#      - contains: [ content.data.0, "otherOrganization" ]
#      - gt: [ content.data.0.mainCustomOrgNo, "1" ]
#      - gt: [ content.data.0.mainCustomOrgName, "1" ]
#      - gt: [ content.data.0.mainOrganizationCode, "1" ]
#
#- test:
#    name: TC4-分配印章管理员-保存分配设置
#    api: api/esignSeals/sealmanager/saveSealManager.yml
#    variables:
#      organizationId: $organizationIdSeal01   #机构ID
#      sealTypeName: "法人章"   #印章类型名称
#      organizationName: $organizationNameSeal01   #机构名称
#      organizationCode: $organizationCodeSeal01   #机构编码
#      sealTypeCode: "LEGAL-PERSON-SEAL"   #印章类型编码
#      enableMultipleSeals: 0   #是否允许创建多个印章（0否 1是）
#      sealManagerCode: $userCode2   #印章管理员编码
#      sealManagerName: $userName2   #印章管理员名称
#      userCode:
#
#- test:
#    name: TC5-创建企业法人印章印章
#    api: api/esignSeals/enterprise/saveElectronicSeal.yml
#    variables:
#      organizationCode: $organizationCodeSeal01
#      organizationName: $organizationNameSeal01
#      organizationId: $organizationIdSeal01
#      sealCode: $sealCodeCommon
#      sealName: $sealCode
#      sealSurroundword: $organizationNameSeal01
#      sealThumbnailUrl: $fileKeySealImgPsn
#      sealTypeCode: "LEGAL-PERSON-SEAL"
#      sealTypeName: "法人章"
#      sealChargeOrganizationCode: $mainOrganizationCode2
#      sealChargeOrganizationName: $mainCustomOrgName2
#      managerOrgCode: $organizationCodeSeal01
#      managerOrgName: $organizationNameSeal01
#      sealUserList2: []
#      personalSealId: $sealIdPerson
#    extract:
#      - sealIdLegal: content.message
#    validate:
#      - contains: [ content, "message" ]
#      - contains: [ content, "status" ]
#      - contains: [ content, "data" ]
#
#- test:
#    name: TC7-查询企业的法人印章印章
#    api: api/esignSeals/electronic/pageElectronicSealList.yml
#    variables:
#      organizationName: $organizationNameSeal01
#    extract:
#      - sealIdLegal: content.data.list.0.sealId
#    validate:
#      - eq: [ content.status, 200 ]
#      - contains: [ content.message,'成功' ]
#      - ge: [ content.data.totalCount, 1 ]
#
#- test:
#    name: TC8-授权用印人为全部
#    api: api/esignSeals/seals/enterprise/electronic/authSealUser.yml
#    variables:
#      id: $sealIdLegal
#    validate:
#      - ne: [ content.message, "" ]
#      - eq: [ content.status,200 ]
#      - eq: [ content.success,true ]
#      - eq: [ content.data, "" ]

#- test:
#    name: TC7-查询企业印章
#    api: api/esignSeals/electronic/pageElectronicSealList.yml
#    variables:
#      tmp00: {'organizationCode': $organizationCodeSeal01 , 'personalSealId': $sealIdPerson}
#      tmp01: '${getEnterpriseSeal($tmp00)}'
#      sealTypeCode: "LEGAL-PERSON-SEAL"
#      organizationName: $organizationName
#      sealStatus: 'g'
#      sealPatternElectronicSealList: 1
#    extract:
#      - sealIdLegal: content.data.list.0.sealId
#    validate:
#      - eq: [ content.status, 200 ]
#      - contains: [ content.message,'成功' ]
#      - ge: [ content.data.totalCount, 1 ]

- test:
    name: TC4-查询企业印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      tmp00: {'organizationCode': $organizationCodeSeal01 ,'personalSealId': $sealIdPerson}
      tmp01: '${getEnterpriseSeal($tmp00)}'
      organizationSeals_list_sealTypeCode: "LEGAL-PERSON-SEAL"
      organizationSeals_list_organizationCode: $organizationCodeSeal01
      organizationSeals_list_sealPattern: 1
    extract:
      - sealIdLegal: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]