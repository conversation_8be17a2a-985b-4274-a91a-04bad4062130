- config:
    name: openapi创建国际标准印章
    variables:
      sealTypeCodeCommon: "COMMON-SEAL"
      organizationName: $organizationName  #这个参数需要调用方入参传入
      sealName_open_0: ${getDateTime()}
      sealCreater_open_0: ${ENV(csqs.accountNo)}
      sealCodeCommon: ${getDateTime()}

      ###调试用的参数
#      organizationName: "esigntest天印PO测试企业"
#      sealTypeCodeCommon: WMG
    export:
      - _sealId_org
      - sealTypeCodeCommon
      - _sealGroupId_open_0

- test:
    name: "InnerOrganizations_detail"
    api:  api/esignManage/InnerOrganizations/detail.yml
    variables:
        nameOrgDetail: $organizationName
    extract:
      _organizationCode_0: content.data.0.organizationCode
      _customOrgNo_0: content.data.0.customOrgNo
    validate:
        - eq: [ content.code, 200 ]
        - eq: [ content.message, "成功" ]
        - ne: [ content.data.0.customOrgNo, "" ]
        - ne: [ content.data.0.organizationCode, "" ]
        - eq: [ content.data.0.name, $organizationName ]

#####001-创建一枚CI企业的电子印章
- test:
    name: org_seals_create
    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
    variables:
      organizationSeals_create_json:
        customAccountNo: $sealCreater_open_0
        organizationCode: $_organizationCode_0
        sealGroupName: "自动化测试印章（允许删除）"
        sealTypeCode: "$sealTypeCodeCommon"
        sealRelease: 1
        sealInfos:
          - sealUpperText: "印章场景测试-SCENE-TC1-模板印章"
            sealOldStyle: 0
            signerCertInfos:
              - sealSignercertId: ""
            sealPattern: 1
            defaultSeal: 0
            sealHeight: 50
            sealTemplateStyle: 1
            sealName: "$sealName_open_0"
            sealShape: 2
            sealColour: 3
            sealOpacity: 80
            sealSource: 2
            sealWidth: 50
    extract:
      _sealId_org: content.data.sealInfos.0.sealId
      _sealGroupId_open_0: content.data.sealGroupId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealGroupId, 1 ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]

- test:
    name: org_seals_sealSignersAuthorization
    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealSignersAuthorization.yml
    variables:
      authorizationScope: 2
      sealId: $_sealId_org
      sealsignerType: 1
      sealsignersInfos: []
    validate:
      - eq: [ content.code, 200 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data.sealId, 1 ]

- test:
    name: authPage-批量发起页面-获取印章的uuid
    api: api/esignSeals/seals/enterprise/electronic/authPage.yml
    variables:
      sealId_auth: $_sealId_org
    extract:
      - _sealuuid: content.data.list.0.sealId
    validate:
      - eq: [ content.status, 200 ]
      - contains: [ content.message, "成功" ]
      - eq: [ content.data.list.0.remoteSealId, $_sealId_org ]
      - eq: [ content.data.totalCount, 1 ]

- test:
    name: authAutoSignSealProject-授权自动签署
    api: api/esignSeals/seals/enterprise/electronic/authAutoSignSealProject.yml
    variables:
      sealId: $_sealuuid
      projectIds: ["${ENV(esign.projectId)}"]
    validate:
      - eq: [ content.status, 200 ]
      - eq: [ content.success, true ]
      - eq: [ content.message, "服务器成功返回"]

- test:
    name: org_seals_list
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      organizationSeals_list_sealTypeCode: $sealTypeCodeCommon
      organizationSeals_list_organizationCode: $_organizationCode_0
      organizationSeals_list_sealPattern: 1
      organizationSeals_list_sealId: $_sealId_org
    extract:
      - sealIdOrg: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]
