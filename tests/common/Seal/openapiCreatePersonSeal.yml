- config:
    name: openapi创建国际标准印章
    variables:
      _customAccountNo_0: $_customAccountNo_0
      index0: ${getDateTime()}

      ###调试用的参数
#      organizationName: "esigntest天印PO测试企业"
#      sealTypeCodeCommon: WMG
    export:
      - _sealId_person

- test:
    name: "user_seals_create"
    api: api/esignSeals/v1/userseals/create.yml
    variables:
      userSeals_create_json:
        customAccountNo: $_customAccountNo_0
        sealName: "个人章O-${index0}"
        sealInfos:
          - sealPattern: 1
            sealColour: 1
    extract:
      _sealId_person: content.data.sealInfos.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.sealInfos, 1 ]
      - eq: [ content.data.sealInfos.0.sealStatus, "1" ]

- test:
    name: "user_seals__list"
    api: api/esignSeals/v1/userseals/list.yml
    variables:
      jsonUserSealsList:
        customAccountNo: $_customAccountNo_0
        pageNo: 1
        pageSize: 10
#    extract:
#      _sealId_person: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, "成功" ]
      - len_ge: [ content.data.records, 1 ]