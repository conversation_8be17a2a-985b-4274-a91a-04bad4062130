#---创建模板：包含控件：6填写3签署控件。4个动态表格控件（DYNAMIC_TABLE）+2个数字控件
- config:
    name: "自动化测试-word模板-4个动态表格控件（DYNAMIC_TABLE）+2个数字控件+3签署控件"
    variables:
      htmlFileName: "动态表格-6-3-WORD_2025-07-04.zip"
      indexFlag: "${get_randomNo_16()}"
      templateNameCommon: "自动化动态表格-6-3-WORD-${indexFlag}"
      description: "自动化测试-word模板-4个动态表格控件（DYNAMIC_TABLE）+2个数字控件+3签署控件"
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${indexFlag}
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon


- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC2-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: ""
      - zipFileKey: "${common_upload_fileKey($htmlFileName)}"
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]



- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
        "isPublish": True
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
