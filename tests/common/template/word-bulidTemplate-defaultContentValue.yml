- config:
    name: "word模板：各种内容域有默认值"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模版有默认值.html"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"
      description: ""
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      signNameA: "甲方企业"
      signNameB: "乙方企业"
      jpegFileKey: "${ENV(jpegFileKey)}"
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - fileKey

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]


- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents":
          [
            {
              "contentCode": "tupian",
              "contentName": "图片默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "3",
              "formatType": 11,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734428129752",
              "templateContentOrder": 1,
              "defaultContentValue": "$jpegFileKey",
              "allowEdit": 0
            },
            {
              "contentCode": "tupian",
              "contentName": "图片",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "3",
              "formatType": 11,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734487798498",
              "templateContentOrder": 2,
              "defaultContentValue": null,
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "单选默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "1",
              "formatType": 9,
              "formatSelect": [
                "单选1",
                "单选2",
                "单选3"
              ],
              "required": 1,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734487817161",
              "templateContentOrder": 3,
              "defaultContentValue": "单选1",
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "多选默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "1",
              "formatType": 10,
              "formatSelect": [
                "多选1",
                "多选2",
                "多选3"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734487835331",
              "templateContentOrder": 4,
              "defaultContentValue": "[\"多选1\",\"多选2\",\"多选3\"]",
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "表格",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 12,
              "formatSelect": [
                "column1",
                "column2",
                "column3",
                "column4",
                "column5"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "tableNames": [ ],
              "thirdKey": "ele-1734487877660",
              "templateContentOrder": 5,
              "defaultContentValue": null,
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "文本默认值关联数据源",
              "dataSource": "user",
              "encrypted": 0,
              "sourceField": "userName",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 0,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488738772",
              "templateContentOrder": 6,
              "defaultContentValue": "文本",
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "数字必填默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "0",
              "formatType": 6,
              "required": 1,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488776835",
              "templateContentOrder": 7,
              "defaultContentValue": "123456",
              "allowEdit": 0
            },
            {
              "contentCode": "shoujihao",
              "contentName": "手机号默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 1,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488818888",
              "templateContentOrder": 8,
              "defaultContentValue": "19827658400",
              "allowEdit": 0
            },
            {
              "contentCode": "riq",
              "contentName": "日期默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "yyyy/MM/dd",
              "formatType": 7,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488922595",
              "templateContentOrder": 9,
              "defaultContentValue": "2024/12/18",
              "allowEdit": 0
            },
            {
              "contentCode": "",
              "contentName": "邮箱默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 3,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488848447",
              "templateContentOrder": 10,
              "defaultContentValue": "<EMAIL>",
              "allowEdit": 0
            },
            {
              "contentCode": "zhengjianhao",
              "contentName": "证件号默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 2,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488872576",
              "templateContentOrder": 11,
              "defaultContentValue": "432427188707052631",
              "allowEdit": 0
            },
            {
              "contentCode": "xinyngdaim",
              "contentName": "信用代码默认值",
              "dataSource": "",
              "encrypted": 0,
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 10.5,
              "fontStyle": "Normal",
              "space": 2,
              "formatRule": "",
              "formatType": 4,
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1734488937595",
              "templateContentOrder": 12,
              "defaultContentValue": "9100000088953282WF",
              "allowEdit": 0
            }
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1,
          }
        ],
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
