- config:
    name: "创建业务模板-无内容域 1个签名区+3个备注签署区-键盘（无抄录）、手绘、表单"
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用含1个签名区+3个备注区模版-${get_randomNo_16()}"
      description: "自动化测试描述"
      initiatorAll: 1
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      width: 216
      height: 36
      signNameA: "甲方"
      signNameB: "备注1"
      signNameC: "备注2"
      signNameD: "备注3"
      collectionTaskName0: "${getRemarkCollectionTaskName()}"
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC2-setup_添加签名区1"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - addSignTime: 0
      - signType: 1
      - posX: 188
      - posY: 703
      - dateFormat: "yyyy-MM-dd"
      - edgeScope: null
      - name: $signNameA
      - signFieldType: 0
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup_添加备注区1-键盘输入（无抄录）"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - addSignTime: 0
      - dateFormat:
      - signType: 1
      - edgeScope: null
      - pageNo: "1"
      - posX: 300
      - posY: 703
      - name: $signNameB
      - signFieldType: 1
      - remarkSignatory:
          aiCheck:
          collectionTaskId: ""
          inputType: 2
          remarkContent: ""
          remarkFontSize: 14
          remarkPrompt: ""
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC3-setup_添加备注区2-手绘（不开启校验）"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - addSignTime: 0
      - dateFormat:
      - signType: 1
      - edgeScope: null
      - pageNo: "1"
      - posX: 400
      - posY: 703
      - name: $signNameC
      - signFieldType: 1
      - remarkSignatory:
          aiCheck: 0
          collectionTaskId: ""
          inputType: 1
          remarkContent: "我同意"
          remarkFontSize: 14
          remarkPrompt: ""
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "TC-2-4-根据采集任务名称获取采集任务id"
    api: api/esignDocs/batchTemplateInitiation/remarkCollectionTaskList.yml
    variables:
      collectionTaskNameRemarkCollectionTaskList: "$collectionTaskName0"
    extract:
      - collectionTaskId01: content.data.0.collectionTaskId
      - collectionTaskName01: content.data.0.collectionTaskName
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.success", True ]
      - eq: [ "content.status",200 ]
      - eq: [ "content.data.0.collectionTaskName", $collectionTaskName0]

- test:
    name: "TC3-setup_添加备注区3"
    api: api/esignDocs/template/owner/signatoryAdd.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - addSignTime: 0
      - dateFormat:
      - signType: 1
      - edgeScope: null
      - pageNo: "1"
      - posX: 500
      - posY: 703
      - name: $signNameD
      - signFieldType: 1
      - remarkSignatory:
          aiCheck:
          collectionTaskId: $collectionTaskId01
          inputType: 3
          remarkContent: ""
          remarkFontSize: 14
          remarkPrompt: ""
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


- test:
    name: "TC4-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]