- config:
    name: "创建文档模板-各种有默认值内容域-1个签署区"
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试PDF模板-有默认值内容域大全-${get_randomNo_16()}"
      description: ""
      initiatorAll: 1
      timePosX: 10
      timePosY: 10
      signNameA: "甲方个人"
      signNameB: "乙方企业"
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      pageNo: 1
      posX: 34
      posY: 603
      width: 216
      height: 36
      contentUuid: ""
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - autoTestDocUuid1Common
      - signatoryDomainUUid_common1
      - signNameA
#      - templateContentUuidtextCommon
#      - templateContentUuidSjytextCommon
#      - templateContentUuidPhoneCommon
#      - templateContentUuidNumCommon
#      - templateContentUuidCodeCommon
#      - templateContentUuidSFZCommon
#      - templateContentUuidDate1Common
#      - templateContentUuidDate2Common
#      - templateContentUuidDate3Common
#      - templateContentUuidEmail1Common
#      - templateContentUuidEmail2Common


- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common



- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      fields_tmp: [{
            "fieldId": "",
            "label": "单行文本1",
            "custom": false,
            "type": "TEXT",
            "subType": null,
            "sort": 1,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 160,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "单行文本1",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "20",
              "overflowType": "1",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-text",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "ac83be163778446da6bb0b3bf23091e0",
            "bizId": "ac83be163778446da6bb0b3bf23091e0",
            "fieldKey": "wenben",
            "fillGroupKey": "",
            "fieldValue": "单行文本1",
            "defaultValue": "单行文本1",
            "position": {
              "x": 187.9239636854289,
              "y": 728.9330077356301,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "单行文本2",
            "custom": false,
            "type": "TEXT",
            "subType": null,
            "sort": 2,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 160,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [
                {
                  "datasource": "tsignDataSourceStableCode",
                  "field": "user.userName"
                }
              ],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "20",
              "overflowType": "1",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-text",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "f5baa00333754e429c97e86bfdf386c6",
            "bizId": "f5baa00333754e429c97e86bfdf386c6",
            "fieldKey": "wenbensjy",
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": 187.37084785133564,
              "y": 689.6163315645631,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "手机号1",
            "custom": false,
            "type": "PHONE_NUM",
            "subType": null,
            "sort": 3,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 105,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "14321538586",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "11",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-telephone",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "c33b7f25d32f41c3833392b54a0617f3",
            "bizId": "c33b7f25d32f41c3833392b54a0617f3",
            "fieldKey": "sjh",
            "fillGroupKey": "",
            "fieldValue": "14321538586",
            "defaultValue": "14321538586",
            "position": {
              "x": 196.07392948163925,
              "y": 633.2425019863452,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "数字1",
            "custom": false,
            "type": "NUM",
            "subType": null,
            "sort": 4,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 160,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "12343456123456",
              "required": false,
              "dateFormat": "0",
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": 0,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "22",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-number",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "b89cde86d5ab4c5a935fee8c02e8facd",
            "bizId": "b89cde86d5ab4c5a935fee8c02e8facd",
            "fieldKey": "numnum",
            "fillGroupKey": "",
            "fieldValue": "12343456123456",
            "defaultValue": "12343456123456",
            "position": {
              "x": 193.8614239452331,
              "y": 589.2691179754012,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "日期1",
            "custom": false,
            "type": "DATE",
            "subType": null,
            "sort": 5,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 112,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "2025年09月25日",
              "required": true,
              "dateFormat": "yyyy年MM月dd日",
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-date",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "0f02521eac6845b3801ffd3dfb6ae840",
            "bizId": "0f02521eac6845b3801ffd3dfb6ae840",
            "fieldKey": "date",
            "fillGroupKey": "",
            "fieldValue": "2025年09月25日",
            "defaultValue": "2025年09月25日",
            "position": {
              "x": 395.4838559814169,
              "y": 607.3390957922054,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "日期2",
            "custom": false,
            "type": "DATE",
            "subType": null,
            "sort": 6,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 98,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "2025-09-26",
              "required": false,
              "dateFormat": "yyyy-MM-dd",
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-date",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "5135f1b4edf441d3ade8222b7be99fbc",
            "bizId": "5135f1b4edf441d3ade8222b7be99fbc",
            "fieldKey": "date2",
            "fillGroupKey": "",
            "fieldValue": "2025-09-26",
            "defaultValue": "2025-09-26",
            "position": {
              "x": 396.0369718155102,
              "y": 577.4925868330101,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "日期3",
            "custom": false,
            "type": "DATE",
            "subType": null,
            "sort": 7,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 98,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "2025/09/26",
              "required": false,
              "dateFormat": "yyyy/MM/dd",
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-date",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "f9ec1d22e5764e9ab025d97df2b542ce",
            "bizId": "f9ec1d22e5764e9ab025d97df2b542ce",
            "fieldKey": "date3",
            "fillGroupKey": "",
            "fieldValue": "2025/09/26",
            "defaultValue": "2025/09/26",
            "position": {
              "x": 398.9408826945412,
              "y": 516.0110084532585,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "社会信用代码1",
            "custom": false,
            "type": "UNIFY_THE_SOCIAL_CREDIT_CODE",
            "subType": null,
            "sort": 8,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 187,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "9111000079RYBUK2XQ",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "18",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-uscc",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "8eefeafe48f04a39b172af92c6febbd4",
            "bizId": "8eefeafe48f04a39b172af92c6febbd4",
            "fieldKey": "xycode",
            "fillGroupKey": "",
            "fieldValue": "9111000079RYBUK2XQ",
            "defaultValue": "9111000079RYBUK2XQ",
            "position": {
              "x": 293.8472706155633,
              "y": 436.*************,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "身份证号1",
            "custom": false,
            "type": "ID_CARD",
            "subType": null,
            "sort": 9,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 145,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "******************",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "18",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-id-card",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "b16642e473004cdda20709eece5867f4",
            "bizId": "b16642e473004cdda20709eece5867f4",
            "fieldKey": "sfzcode",
            "fillGroupKey": "",
            "fieldValue": "******************",
            "defaultValue": "******************",
            "position": {
              "x": 308.0815949868657,
              "y": 390.1439361990952,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "邮箱1",
            "custom": false,
            "type": "EMAIL",
            "subType": null,
            "sort": 10,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 160,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "<EMAIL>",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "22",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-mail",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "f6ee533a01e6403d86585aceb1ba7558",
            "bizId": "f6ee533a01e6403d86585aceb1ba7558",
            "fieldKey": "email",
            "fillGroupKey": "",
            "fieldValue": "<EMAIL>",
            "defaultValue": "<EMAIL>",
            "position": {
              "x": 183.77553162967976,
              "y": 334.4788009122966,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          },
          {
            "fieldId": "",
            "label": "手机号1",
            "custom": false,
            "type": "PHONE_NUM",
            "subType": null,
            "sort": 11,
            "formula": null,
            "style": {
              "font": "1",
              "fontSize": 14,
              "textColor": "#000",
              "width": 105,
              "height": 17,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "signDatePos": null,
                "units": "px",
                "imgType": null,
                "usePageTypeGroupId": "",
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "keyword": "",
                "groupKey": "",
                "tickOptions": null,
                "elementId": "",
                "posKey": "",
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": "14321538586",
              "required": false,
              "dateFormat": null,
              "validation": null,
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": "11",
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": null,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-telephone",
                "fastCheck": null,
                "addSealRule": "",
                "keyPosX": null,
                "keyPosY": null,
                "ext": "{}",
                "version": null,
                "mergeId": null,
                "signatureStandard": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": null,
              "cellEditableOnFilling": true,
              "signDatePosition": null,
              "defaultValueExt": null
            },
            "options": null,
            "instructions": "",
            "contentFieldId": "dfbdb4e98c07425dbe32b29b11ce534f",
            "bizId": "dfbdb4e98c07425dbe32b29b11ce534f",
            "fieldKey": "sjh",
            "fillGroupKey": "",
            "fieldValue": "14321538586",
            "defaultValue": "14321538586",
            "position": {
              "x": 405.8462851140003,
              "y": 337.8596987656739,
              "page": "1",
              "scope": "default",
              "intervalType": null
            },
            "formField": false
          }]
      signContent001: ${generate_field_object(普通签名区, SIGN)}
      fillContent001: ${generate_field_object(单行文本1, TEXT)}
      fillContent002: ${generate_field_object(单行文本2, TEXT)}
      fillContent003: ${generate_field_object(手机号码1, PHONE_NUM)}
      fillContent004: ${generate_field_object(数字1, NUM)}
      fillContent005: ${generate_field_object(日期1, DATE)}
      fillContent006: ${generate_field_object(日期2, DATE)}
      fillContent007: ${generate_field_object(日期3, DATE)}
      fillContent008: ${generate_field_object(信用代码, UNIFY_THE_SOCIAL_CREDIT_CODE)}
      fillContent009: ${generate_field_object(身份证号码, ID_CARD)}
      fields_draft: $fields_tmp
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

#- test:
#    name: "TC2-setup_添加签名区1"
#    api: api/esignDocs/template/owner/signatoryAdd.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - addSignTime: 0
#      - signType: 1
#      - dateFormat: "yyyy-MM-dd"
#      - edgeScope: null
#      - pageNo: "1"
#      - posX: 200
#      - posY: 100
#      - name: $signNameA
#    extract:
#      signatoryDomainUUid_common1: content.data.list.0
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#
#
#- test:
#    name: "TC2-setup-添加可修改、有默认值内容域-文本"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "文本"
#      - formatType: 0
#      - formatRule: ""
#      - edgeScope: 0
#      - contentUuid:
#      - contentCode: "wenben"
#      - length: 28
#      - allowEdit: 1
#      - dataSource: null
#      - defaultContentValue: "text文本"
#      - required: 0
#    extract:
#      templateContentUuidtextCommon: content.data.list.0
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC2-setup-添加可修改、有默认值内容域-关联数据源的文本"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "关联数据源文本"
#      - formatType: 0
#      - formatRule: ""
#      - edgeScope: 0
#      - contentUuid:
#      - contentCode: "wenbensjy"
#      - length: 28
#      - allowEdit: 1
#      - dataSource: "user"
#      - defaultContentValue: "text文本sjy"
#      - required: 0
#    extract:
#      templateContentUuidtextCommon: content.data.list.0
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC2-setup_添加内容域完全相同的自动化测试-手机号"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "手机号"
#      - formatType: 1
#      - allowEdit: 1
#      - formatRule: ""
#      - edgeScope: 0
#      - length: 11
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "14321538586"
#      - contentCode: "sjh"
#    extract:
#      templateContentUuidPhoneCommon: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC2-setup_添加内容域 自动化测试-数字"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "数字必填"
#      - formatType: 6
#      - allowEdit: 1
#      - formatRule: "2"
#      - edgeScope: 0
#      - length: 15
#      - required: 1
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "12343456123456"
#      - contentCode: "numnum"
#      - required: 1
#    extract:
#      templateContentUuidNum: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#
#
#
#- test:
#    name: "TC2-setup_添加内容域日期1"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "日期1必填"
#      - formatType: 7
#      - allowEdit: 1
#      - formatRule: "yyyy年MM月dd日"
#      - edgeScope: 0
#      - length: 11
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "2024年12月30日"
#      - contentCode: "date"
#      - required: 1
#    extract:
#      templateContentUuidDate1Common: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC2-setup_添加内容域日期2"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "日期2"
#      - formatType: 7
#      - allowEdit: 0
#      - formatRule: "yyyy-MM-dd"
#      - edgeScope: 0
#      - length: 10
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "2024-12-30"
#      - contentCode: "date2"
#    extract:
#      templateContentUuidDate2Common: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#
#- test:
#    name: "TC2-setup_添加内容域日期3"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "日期3"
#      - formatType: 7
#      - allowEdit: 1
#      - formatRule: "yyyy/MM/dd"
#      - edgeScope: 0
#      - length: 10
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "2024/12/30"
#      - contentCode: "date3"
#    extract:
#        templateContentUuidDateCommon: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC2-setup_添加内容域 自动化测试-统一社会信用代码"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "社会信用代码"
#      - formatType: 4
#      - allowEdit: 1
#      - formatRule: ""
#      - edgeScope: 0
#      - length: 18
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "9111000079RYBUK2XQ"
#      - contentCode: "xycode"
#    extract:
#      templateContentUuidCodeCommon: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC2-setup_添加内容域 自动化测试-身份证"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "身份证"
#      - formatType: 2
#      - allowEdit: 1
#      - formatRule: ""
#      - edgeScope: 0
#      - length: 18
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "******************"
#      - contentCode: "sfzcode"
#    extract:
#      templateContentUuidSFZCommon: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC2-setup_添加内容域 自动化测试-邮箱"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "邮箱"
#      - formatType: 3
#      - allowEdit: 0
#      - formatRule: ""
#      - edgeScope: 0
#      - length: 18
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "<EMAIL>"
#      - contentCode: "email"
#    extract:
#      templateContentUuidEmail1Common: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]
#
#- test:
#    name: "TC2-setup_添加内容域完全相同的自动化测试-手机号"
#    api: api/esignDocs/template/owner/contentAddAllowEdit.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentName: "手机号"
#      - formatType: 1
#      - allowEdit: 1
#      - formatRule: ""
#      - edgeScope: 0
#      - length: 11
#      - dataSource: null
#      - allowEdit: 1
#      - defaultContentValue: "14321538586"
#      - contentCode: "sjh"
#    extract:
#      templateContentUuidEmail2Common: content.data.list.0
#    validate:
#        - eq: [ "content.message","成功" ]
#        - eq: [ "content.status",200 ]


- test:
    name: "TC3-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]