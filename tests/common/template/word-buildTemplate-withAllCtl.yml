#---创建模板：包含所有控件：单选 多选 动态表格 图片、文本、数字、日期、邮箱、统一信用代码、身份证号、手机号基础控件
- config:
    name: "自动化测试-word模板-所有控件"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      signNameA: "甲方企业"
      signNameB: "乙方企业"
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

#- test:
#    name: "引用公共用例获取文件类型"
#    testcase: common/docType/buildDocType.yml
#    extract:
#      - autoTestDocUuid1Common

- test:
    name: "上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - fileKey

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": "",
            "contentName": "文本test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 0,
            "required": 0,
            "thirdKey": "ele-1655955946951"
          },
          {
            "contentCode": "",
            "contentName": "数字test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "0",
            "formatType": 6,
            "required": 0,
            "thirdKey": "ele-1655956027382"
          },
          {
            "contentCode": "",
            "contentName": "手机号test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 1,
            "required": 0,
            "thirdKey": "ele-1655956031994"
          },
          {
            "contentCode": "",
            "contentName": "邮箱test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 3,
            "required": 0,
            "thirdKey": "ele-1655963770820"
          },
          {
            "contentCode": "",
            "contentName": "身份证test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 2,
            "required": 0,
            "thirdKey": "ele-1655963773320"
          },
          {
            "contentCode": "",
            "contentName": "日期test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "yyyy/MM/dd",
            "formatType": 7,
            "required": 0,
            "thirdKey": "ele-1655963775600"
          },
          {
            "contentCode": "",
            "contentName": "统一社会信用代码test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 4,
            "required": 0,
            "thirdKey": "ele-1655963777225"
          },
          {
            "contentCode": "",
            "contentName": "单选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "单选1"
            ],
            "required": 0,
            "thirdKey": "ele-1655963781753"
          },
          {
            "contentCode": "",
            "contentName": "多选test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 10,
            "formatSelect": [
              "多选1",
              "多选2",
              "多选3"
            ],
            "required": 0,
            "thirdKey": "ele-1655963783528"
          },
          {
            "contentCode": "",
            "contentName": "动态表格test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "",
            "formatType": 12,
            "formatSelect": [
              "columns1",
              "columns2",
              "columns3",
              "columns4",
              "columns5"
            ],
            "required": 0,
            "tableNames": [ ],
            "thirdKey": "ele-1655963785240"
          },
          {
            "contentCode": "",
            "contentName": "图片test",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "3",
            "formatType": 11,
            "required": 0,
            "thirdKey": "ele-1655964283202"
          }
        ],
        "isPublish": true,
        "signatories": [
          {
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1,
            "allowMove": false
          }
        ],
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
