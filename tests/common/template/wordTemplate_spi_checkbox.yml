- config:
    name: "word模板：特殊多选"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "特殊多选.html"
      templateNameCommon: "自动化测试通用模版-word模板-${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      signNameA: "甲方企业"
      signNameB: "乙方企业"
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - fileKey

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: {
        "params": {
          "contents": [
            {
              "contentCode": "选项个数最多2",
              "contentName": "选项个数最多2",
              "dataSource": "",
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 5,
              "fontStyle": "Normal",
              "formatRule": "1",
              "formatType": 9,
              "formatSelect": [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "0",
                "a",
                "b",
                "c",
                "d",
                "e",
                "f",
                "g",
                "h",
                "i",
                "j",
                "k",
                "l",
                "m",
                "n",
                "o",
                "p",
                "q",
                "r",
                "s",
                "t",
                "u",
                "v",
                "w",
                "x",
                "y",
                "z",
                "A",
                "B",
                "C",
                "D",
                "E",
                "F",
                "G",
                "H",
                "I",
                "J",
                "K",
                "L",
                "M",
                "N",
                "O",
                "P",
                "Q",
                "R",
                "S",
                "T",
                "U",
                "V",
                "W",
                "X",
                "Y",
                "Z",
                "啊",
                "阿",
                "嗄",
                "吖",
                "吧",
                "不",
                "呗",
                "把",
                "传",
                "次",
                "测",
                "才",
                "吃",
                "从",
                "的",
                "都",
                "点",
                "得",
                "额",
                "饿",
                "讹",
                "恶",
                "非",
                "发",
                "分",
                "个",
                "该",
                "给",
                "改",
                "过",
                "好",
                "和",
                "话",
                "后",
                "会",
                "号",
                "哈",
                "就"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1655364465719"
            },
            {
              "contentCode": "选项个数最多",
              "contentName": "选项个数最多",
              "dataSource": "",
              "sourceField": "",
              "description": "柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 42,
              "fontStyle": "Normal",
              "formatRule": "2",
              "formatType": 9,
              "formatSelect": [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "0",
                "a",
                "b",
                "c",
                "d",
                "e",
                "f",
                "g",
                "h",
                "i",
                "j",
                "k",
                "l",
                "m",
                "n",
                "o",
                "p",
                "q",
                "r",
                "s",
                "t",
                "u",
                "v",
                "w",
                "x",
                "y",
                "z",
                "A",
                "B",
                "C",
                "D",
                "E",
                "F",
                "G",
                "H",
                "I",
                "J",
                "K",
                "L",
                "M",
                "N",
                "O",
                "P",
                "Q",
                "R",
                "S",
                "T",
                "U",
                "V",
                "W",
                "X",
                "Y",
                "Z"
              ],
              "required": 0,
              "keyword": "",
              "keyGroup": "",
              "keywordGroup": null,
              "thirdKey": "ele-1655365113200"
            },
            {
              "contentCode": null,
              "contentName": "自定义1",
              "dataSource": "",
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 24,
              "fontStyle": "Normal",
              "formatRule": "1",
              "formatType": 9,
              "formatSelect": [
                "1",
                "2",
                "3"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1655533262675"
            },
            {
              "contentCode": "超长选项",
              "contentName": "多选",
              "dataSource": "",
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLACK",
              "fontSize": 14,
              "fontStyle": "Normal",
              "formatRule": "1",
              "formatType": 10,
              "formatSelect": [
                "1红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红",
                "2红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳红柳红测试柳红测试柳",
                "123"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1655533463015"
            },
            {
              "contentCode": "100个选项",
              "contentName": "多选2",
              "dataSource": "",
              "sourceField": "",
              "description": "",
              "font": "SimSun",
              "fontColor": "BLUE",
              "fontSize": 24,
              "fontStyle": "Normal",
              "formatRule": "1",
              "formatType": 10,
              "formatSelect": [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
                "14",
                "15",
                "16",
                "17",
                "18",
                "19",
                "20",
                "21",
                "22",
                "23",
                "24",
                "25",
                "26",
                "27",
                "28",
                "29",
                "30",
                "31",
                "32",
                "33",
                "34",
                "35",
                "36",
                "37",
                "38",
                "39",
                "40",
                "41",
                "42",
                "43",
                "44",
                "45",
                "46",
                "47",
                "48",
                "49",
                "50",
                "51",
                "52",
                "53",
                "54",
                "55",
                "56",
                "57",
                "58",
                "59",
                "K",
                "L",
                "M",
                "N",
                "O",
                "P",
                "Q",
                "R",
                "S",
                "T",
                "A",
                "B",
                "C",
                "D",
                "E",
                "F",
                "G",
                "H",
                "I",
                "J",
                "a",
                "b",
                "c",
                "d",
                "e",
                "f",
                "g",
                "h",
                "i",
                "j",
                "k",
                "l",
                "m",
                "n",
                "o",
                "p",
                "q",
                "r",
                "s",
                "t",
                "u"
              ],
              "required": 0,
              "keyword": null,
              "keyGroup": null,
              "keywordGroup": null,
              "thirdKey": "ele-1655534342973"
            }
          ],
          "isPublish": true,
          "signatories": [
            {
              "allowMove": false,
              "addSignTime": 0,
              "name": "甲方企业",
              "pageNo": null,
              "edgeScope": null,
              "offsetX": 0,
              "offsetY": 0,
              "thirdKey": "ele-1655365907545",
              "keyword": null,
              "keywordGroup": null,
              "keyGroup": null,
              "keywordOrder": "",
              "keywordType": 0,
              "signatoryUuid": "45bc509d9e970d3b8d9c26b511b1ae4d",
              "signType": 1
            }
          ],
          "templateUuid": $newTemplateUuidCommon,
          "version": $newVersionCommon,
          "fileKey": $htmlFileKey
        }
      }
      validate:
        - eq: [ "content.status",200 ]
        - eq: [ "content.message","成功" ]
