- config:
    name: "创建业务模板-无内容域 无签署区"
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon_template1: "自动化测试通用模版-${get_randomNo_16()}"
      description: "自动化测试描述"
      initiatorAll: 1
      signNameA: "甲方"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
    output:
      - newTemplateUuidCommon_template1
      - newVersionCommon_template1
      - templateNameCommon_template1

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon_template1
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon_template1: content.data.templateUuid
      - newVersionCommon_template1: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]



- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon_template1
      - version: $newVersionCommon_template1
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]