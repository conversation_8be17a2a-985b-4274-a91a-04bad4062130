- config:
    name: "创建企业模板-各种内容域-2个签署区(甲方个人+乙方企业)"
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用模版-内容域大全-${get_randomNo_16()}"
      description: "自动化测试描述"
      initiatorAll: 1
      contentUuid: ""
      autoTestDocUuid1Common: ${get_a_docConfig_type()}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]


############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_006: ${putTempEnv(_tmp_common_template_006, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_006)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_006)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
#      fields_draft: [$signfiled,$textField,$multilineTextField,$numField,$dateField,$phoneField,$idCardField,$tickBoxField,$pullDownField,$radioField,$checkboxField,$imageField,$rmbField,$emailField,$creditCodeField]
      fields_draft: ${getEpaasTemplateContent(1,2,0)}
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

- test:
    name: "content-查询文档模板的控件"
    api: api/esignDocs/template/openapi/template-content.yml
    variables:
      - templateId: $newTemplateUuidCommon
    extract:
      - _templateId: content.data.templateId
      - _templateName: content.data.templateName
      - _docTypeName: content.data.docTypeName
      - _sealControlName_0: content.data.sealControl.0.sealControlName
      - _sealControlId_0: content.data.sealControl.0.sealControlId
      - _sealControlName_1: content.data.sealControl.1.sealControlName
      - _sealControlId_1: content.data.sealControl.1.sealControlId
      - _docTypeName: content.data.docTypeName
    validate:
      - len_gt: ["content.data.contentsControl",10]
      - len_eq: ["content.data.sealControl",2]
      - eq: ["content.message","成功"]
      - eq: ["content.code",200]