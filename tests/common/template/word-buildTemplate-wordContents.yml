#---创建word模板：包含的控件支持自定义
- config:
    name: "自动化测试-word模板-自定义内容域控件"
    variables:
      wordFileName_commonBuildWordTemplate: "word-测试模版.docx"
      contents_commonBuildWordTemplate: []
      signatories_commonBuildWordTemplate: []
      htmlFileName_contentDomain: "word-测试模板.html" #实际调用最好需要传自己的添加了内容域和签署区后的html文件的fileKey,否则，虽然不影响自动化运行结果，但是在页面预览文件报关键字找不到；
      templateName_commonBuildWordTemplate: "自动化测试通用模版-word模板-${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      pageHeaderAndFooterData_commonBuildWordTemplate: null
      pageMarginsData_commonBuildWordTemplate: null
      docUuid_commonBuildWordTemplate: ${get_a_docConfig_type()}
    output:
      - templateUuid_commonBuildWordTemplate
      - version_commonBuildWordTemplate
      - templateName_commonBuildWordTemplate
      - docUuid_commonBuildWordTemplate

- test:
    name: "TC-新建word模板-上传word文件转html"
    api: api/esignDocs/template/owner/word2html.yml
    variables:
         fileName: $wordFileName_commonBuildWordTemplate
    extract:
         htmlFileKey_commonBuildWordTemplate: content.data.fileKey
    validate:
       - eq: ["content.message","成功"]
       - eq: ["content.success",True]
       - ne: [content.data.fileKey,'']

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $htmlFileKey_commonBuildWordTemplate
      - zipFileKey: null
      - templateName: $templateName_commonBuildWordTemplate
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $docUuid_commonBuildWordTemplate
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - templateUuid_commonBuildWordTemplate: content.data.templateUuid
      - version_commonBuildWordTemplate: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC3-添加内容域名/签署区，发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      contents: $contents_commonBuildWordTemplate
      fileKey: "${attachment_upload($htmlFileName_contentDomain)}"
      version: $version_commonBuildWordTemplate
      pageHeaderAndFooterData: $pageHeaderAndFooterData_commonBuildWordTemplate
      pageMarginsData: $pageMarginsData_commonBuildWordTemplate
      templateUuid: $templateUuid_commonBuildWordTemplate
      signatories: $signatories_commonBuildWordTemplate
      isPublish: true
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
