- config:
    name: "创建企业模板-无内容域-2个签署区(甲方个人+乙方企业)"
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用模版-${get_randomNo_16()}"
      description: "自动化测试描述"
      initiatorAll: 1
      contentFieldId001: ${get_randomNo_32()}
      contentFieldId002: ${get_randomNo_32()}
      styleCommon: {
          "font": 1,
          "fontSize": 12,
          "textColor": "#000",
          "width": 150,
          "height": 150,
          "bold": false,
          "italic": false,
          "underLine": false,
          "lineThrough": false,
          "verticalAlignment": "TOP",
          "horizontalAlignment": "LEFT",
          "styleExt": {
              "units": "px",
              "imgType": null,
              "hideTHeader": null,
              "selectLayout": null,
              "borderWidth": "1",
              "borderColor": "#000",
              "groupKey": "",
              "tickOptions": null
          }
      }
      numberFormatCommon: {
          "integerDigits": null,
          "fractionDigits": null,
          "thousandsSeparator": ""
      }
#      signContent001: {
#                    "fieldId": null,
#                    "label": "甲方个人",
#                    "custom": false,
#                    "type": "SIGN",
#                    "subType": null,
#                    "sort": 1,
#                    "formula": null,
#                    "style": $styleCommon,
#                    "settings": {
#                        "defaultValue": null,
#                        "required": true,
#                        "dateFormat": "yyyy-MM-dd HH:mm:ss",
#                        "validation": {
#                            "type": "REGEXP",
#                            "pattern": ""
#                        },
#                        "selectableDataSource": [],
#                        "numberFormat": $numberFormatCommon,
#                        "editable": true,
#                        "encryptAlgorithm": "",
#                        "fillLengthLimit": null,
#                        "overflowType": 2,
#                        "minFontSize": 8,
#                        "remarkInputType": null,
#                        "content": null,
#                        "remarkAICheck": null,
#                        "dateRule": "1",
#                        "tickOptions": null,
#                        "configExt": {
#                            "cooperationerSubjectType": "",
#                            "icon": "epaas-icon-stamp",
#                            "fastCheck": false,
#                            "addSealRule": "followSeal",
#                            "ext": "{}",
#                            "mergeId": null
#                        },
#                        "sealTypes": [],
#                        "positionMovable": false
#                    },
#                    "options": null,
#                    "instructions": null,
#                    "contentFieldId": $contentFieldId001,
#                    "bizId": $contentFieldId001,
#                    "fieldKey": null,
#                    "fillGroupKey": "",
#                    "fieldValue": null,
#                    "defaultValue": null,
#                    "position": {
#                        "x": 333.08582574772436,
#                        "y": 368.3255851755527,
#                        "page": 1,
#                        "scope": "default",
#                        "intervalType": null
#                    },
#                    "formField": null
#                }
#      signContent002: {
#                    "fieldId": "",
#                    "label": "乙方企业",
#                    "custom": false,
#                    "type": "QF_SIGN",
#                    "subType": null,
#                    "sort": 2,
#                    "formula": null,
#                    "style": $styleCommon,
#                    "settings": {
#                        "defaultValue": null,
#                        "required": true,
#                        "dateFormat": null,
#                        "validation": {
#                            "type": "REGEXP",
#                            "pattern": ""
#                        },
#                        "selectableDataSource": [],
#                        "numberFormat":  $numberFormatCommon,
#                        "editable": true,
#                        "encryptAlgorithm": "",
#                        "fillLengthLimit": null,
#                        "overflowType": 2,
#                        "minFontSize": 8,
#                        "remarkInputType": null,
#                        "content": null,
#                        "remarkAICheck": null,
#                        "dateRule": null,
#                        "tickOptions": null,
#                        "configExt": {
#                            "cooperationerSubjectType": "",
#                            "icon": "epaas-icon-paging-seal",
#                            "fastCheck": true,
#                            "addSealRule": "",
#                            "ext": "{}",
#                            "version": null,
#                            "mergeId": null
#                        },
#                        "sealTypes": [],
#                        "positionMovable": false
#                    },
#                    "options": null,
#                    "instructions": "",
#                    "contentFieldId": $contentFieldId002,
#                    "bizId": $contentFieldId002,
#                    "fieldKey": null,
#                    "fillGroupKey": "",
#                    "fieldValue": null,
#                    "defaultValue": null,
#                    "position": {
#                        "x": 536,
#                        "y": 465.7899057217165,
#                        "page": "1-30",
#                        "scope": "all",
#                        "intervalType": null
#                    },
#                    "formField": false
#                }
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - autoTestDocUuid1Commons

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      signField1: ${generate_field_object(普通签名区, SIGN)}
      signField2: ${generate_field_object(普通签名区, SIGN)}
      fields_draft: [ $signField1,$signField2]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]