- config:
    name: "word模板：一个内容域：图片，一个签署区"
    variables:
      fileName: "word-测试模版.docx"
      htmlFileName: "word-测试模板.html"
      templateNameCommon: "自动化测试通用模版-word模板-单图片非必填-${get_randomNo_16()}"
      description: "自动化测试描述-word模板"
      timePosX: 10
      timePosY: 10
      formatType: 0
      formatRule: "28"
      dataSource: null
      sourceField: null
      font: "SimSun"
      fontSize: 14
      fontColor: "BLACK"
      fontStyle: "Normal"
      textAlign: "Left"
      required: 0
      length: 28
      pageNo: 1
      posX: 188
      posY: 703
      width: 216
      height: 36
      initiatorAll: 1
      checkRepetition: 0
      contentCodeCommon: ${get_randomNo_16()}
      contentNameCommon: "自动化测试-文本0507"
      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
      signNameA: "甲方企业"
      signNameB: "乙方企业"
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "上传word文档"
    testcase: common/fileSystem/word2html.yml
    extract:
      - fileKey

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
      - templateType: 2
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: [ "content.message","成功" ]
      - eq: [ "content.status",200 ]

- test:
    name: "TC2-上传html文件"
    api: api/esignDocs/fileSystem/attachmentUpload.yml
    variables:
      file_path: "data/$htmlFileName"
      multipart_encoder: ${multipart_encoder(uploadFile=$file_path)}
      fileName: $htmlFileName
    extract:
      - htmlFileKey: content.data.fileKey
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.success, true ]
      - contains: [ content.message,"成功" ]

- test:
    name: "TC3-发布模板"
    api: api/esignDocs/template/owner/word_saveOrPublish.yml
    variables:
      json: { "params": {
        "contents": [
          {
            "contentCode": null,
            "contentName": "图片",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "3",
            "formatType": 11,
            "required": 0,
            "thirdKey": "ele-1655434936873"
          },
          {
            "contentCode": "danxuan",
            "contentName": "单选",
            "dataSource": "",
            "sourceField": "",
            "description": "",
            "font": "SimSun",
            "fontColor": "BLACK",
            "fontSize": 14,
            "fontStyle": "Normal",
            "formatRule": "1",
            "formatType": 9,
            "formatSelect": [
              "A",
              "B",
              "C"
            ],
            "required": 0,
            "thirdKey": "ele-1655434944976"
          },
        ],
        "isPublish": true,
        "signatories": [
          {
            "allowMove": false,
            "addSignTime": 0,
            "name": "甲方企业",
            "edgeScope": null,
            "thirdKey": "ele-1655434999481",
            "keywordOrder": "",
            "keywordType": 0,
            "signType": 1
          }
        ],
        "templateUuid": $newTemplateUuidCommon,
        "version": $newVersionCommon,
        "fileKey": $htmlFileKey
      } }
    validate:
      - eq: [ "content.status",200 ]
      - eq: [ "content.message","成功" ]
