# 公共文档模板-"创建文档模板-2填写控件0签署控件"
- config:
    variables:
      fileKey: ${ENV(fileKey)}
      templateNameCommon: "自动化测试通用模版-${get_randomNo_16()}"
      description: "自动化测试描述"
      contentFieldId003: ${get_randomNo_32()}
      contentFieldId004: ${get_randomNo_32()}
      styleCommon: {
        "font": 1,
        "fontSize": 12,
        "textColor": "#000",
        "width": 150,
        "height": 150,
        "bold": false,
        "italic": false,
        "underLine": false,
        "lineThrough": false,
        "verticalAlignment": "TOP",
        "horizontalAlignment": "LEFT",
        "styleExt": {
          "units": "px",
          "imgType": null,
          "hideTHeader": null,
          "selectLayout": null,
          "borderWidth": "1",
          "borderColor": "#000",
          "groupKey": "",
          "tickOptions": null
        }
      }
      numberFormatCommon: {
        "integerDigits": null,
        "fractionDigits": null,
        "thousandsSeparator": ""
      }
#      fillContent001: {
#                    "fieldId": "",
#                    "label": "自动化测试-文本0507",
#                    "custom": false,
#                    "type": "MULTILINE_TEXT",
#                    "subType": null,
#                    "sort": 3,
#                    "formula": null,
#                    "style": {
#                        "font": 1,
#                        "fontSize": 16,
#                        "textColor": "#2969B0",
#                        "width": 272.01,
#                        "height": 37.67,
#                        "bold": true,
#                        "italic": false,
#                        "underLine": false,
#                        "lineThrough": false,
#                        "leadingRate": 1,
#                        "verticalAlignment": "TOP",
#                        "horizontalAlignment": "LEFT",
#                        "styleExt": {
#                            "signDatePos": null,
#                            "units": "px",
#                            "imgType": null,
#                            "usePageTypeGroupId": "",
#                            "hideTHeader": null,
#                            "selectLayout": null,
#                            "borderWidth": 1,
#                            "borderColor": "#000",
#                            "keyword": "",
#                            "groupKey": "",
#                            "tickOptions": null,
#                            "elementId": "",
#                            "posKey": ""
#                        }
#                    },
#                    "settings": {
#                        "defaultValue": "文本-COMMON-001",
#                        "required": true,
#                        "dateFormat": null,
#                        "validation": {
#                            "type": "REGEXP",
#                            "pattern": ""
#                        },
#                        "selectableDataSource": [],
#                        "numberFormat":  $numberFormatCommon,
#                        "editable": true,
#                        "encryptAlgorithm": "",
#                        "fillLengthLimit": 54,
#                        "overflowType": 1,
#                        "minFontSize": 10,
#                        "remarkInputType": null,
#                        "content": null,
#                        "remarkAICheck": null,
#                        "dateRule": null,
#                        "tickOptions": null,
#                        "configExt": {
#                            "cooperationerSubjectType": "",
#                            "icon": "epaas-icon-multiline",
#                            "fastCheck": null,
#                            "addSealRule": "",
#                            "ext": "{}",
#                            "version": null,
#                            "mergeId": null
#                        },
#                        "sealTypes": [],
#                        "positionMovable": null
#                    },
#                    "options": null,
#                    "instructions": "COMMON",
#                    "contentFieldId": $contentFieldId003,
#                    "bizId": $contentFieldId003,
#                    "fieldKey": null,
#                    "fillGroupKey": "",
#                    "fieldValue": "文本-COMMON-001",
#                    "defaultValue": "文本-COMMON-001",
#                    "position": {
#                        "x": 186.00780234070223,
#                        "y": "674.90",
#                        "page": 1,
#                        "scope": "default",
#                        "intervalType": null
#                    },
#                    "formField": false
#                }
#      fillContent002: {
#                    "fieldId": "",
#                    "label": "自动化测试-文本0508",
#                    "custom": false,
#                    "type": "MULTILINE_TEXT",
#                    "subType": null,
#                    "sort": 4,
#                    "formula": null,
#                    "style": {
#                        "font": 2,
#                        "fontSize": 16,
#                        "textColor": "#F37934",
#                        "width": 272.01,
#                        "height": 37.67,
#                        "bold": true,
#                        "italic": false,
#                        "underLine": false,
#                        "lineThrough": false,
#                        "leadingRate": 1.5,
#                        "verticalAlignment": "TOP",
#                        "horizontalAlignment": "CENTER",
#                        "styleExt": {
#                            "signDatePos": null,
#                            "units": "px",
#                            "imgType": null,
#                            "usePageTypeGroupId": "",
#                            "hideTHeader": null,
#                            "selectLayout": null,
#                            "borderWidth": 1,
#                            "borderColor": "#000",
#                            "keyword": "",
#                            "groupKey": "",
#                            "tickOptions": null,
#                            "elementId": "",
#                            "posKey": ""
#                        }
#                    },
#                    "settings": {
#                        "defaultValue": "文本2",
#                        "required": false,
#                        "dateFormat": null,
#                        "validation": {
#                            "type": "REGEXP",
#                            "pattern": ""
#                        },
#                        "selectableDataSource": [],
#                        "numberFormat": $numberFormatCommon,
#                        "editable": true,
#                        "encryptAlgorithm": "",
#                        "fillLengthLimit": 17,
#                        "overflowType": 2,
#                        "minFontSize": 10,
#                        "remarkInputType": null,
#                        "content": null,
#                        "remarkAICheck": null,
#                        "dateRule": null,
#                        "tickOptions": null,
#                        "configExt": {
#                            "cooperationerSubjectType": "",
#                            "icon": "epaas-icon-multiline",
#                            "fastCheck": null,
#                            "addSealRule": "",
#                            "ext": "{}",
#                            "version": null,
#                            "mergeId": null
#                        },
#                        "sealTypes": [],
#                        "positionMovable": null
#                    },
#                    "options": null,
#                    "instructions": "限制输入",
#                    "contentFieldId": $contentFieldId004,
#                    "bizId": $contentFieldId004,
#                    "fieldKey": null,
#                    "fillGroupKey": "",
#                    "fieldValue": "文本2",
#                    "defaultValue": "文本2",
#                    "position": {
#                        "x": 186.78283485045515,
#                        "y": 579.2641417425228,
#                        "page": 1,
#                        "scope": "default",
#                        "intervalType": null
#                    },
#                    "formField": false
#                }
#      initiatorAll: 1
#      checkRepetition: 0
#      contentCodeCommon: ${get_randomNo_16()}
#      contentNameCommon: "自动化测试-文本0507"
#      contentUuidCommon: ${addContentDomain($contentNameCommon,$contentCodeCommon)}
    output:
      - newTemplateUuidCommon
      - newVersionCommon
      - templateNameCommon
      - contentNameCommon
      - autoTestDocUuid1Common
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "引用公共用例获取文件类型"
    testcase: common/docType/buildDocType.yml
    extract:
      - autoTestDocUuid1Common

- test:
    name: "引用公共用例获取当前登录用户详情信息"
    testcase: common/user/getCurrentUserInfo.yml
    extract:
      - createUserOrgCodeCommon
      - createUserCodeCommon
      - userNameCommon
      - userIdCommon
      - userCodeCommon
      - organizationIdCommon
      - organizationCodeCommon
      - getOrganizationNameCommon

- test:
    name: "TC1-setup_模版新建"
    api: api/esignDocs/template/owner/templateAdd.yml
    variables:
      - fileKey: $fileKey
      - templateType: 1
      - zipFileKey: null
      - templateName: $templateNameCommon
      - createUserOrg: $createUserOrgCodeCommon
      - description: $description
      - docUuid: $autoTestDocUuid1Common
      - allRange: 1
      - organizeRange: null
    extract:
      - newTemplateUuidCommon: content.data.templateUuid
      - newVersionCommon: content.data.version
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]

#- test:
#    name: "TC2-setup_添加签名区1"
#    api: api/esignDocs/template/owner/signatoryAdd.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - addSignTime: 0
#      - signType: 1
#      - dateFormat: "yyyy-MM-dd"
#      - edgeScope: null
#      - pageNo: "1"
#      - posX: 10
#      - posY: 10
#      - name: $signNameA
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC3-setup_添加签名区2"
#    api: api/esignDocs/template/owner/signatoryAdd.yml
#    variables:
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - addSignTime: 0
#      - signType: 1
#      - dateFormat: "yyyy-MM-dd"
#      - edgeScope: null
#      - pageNo: "1"
#      - posX: 10
#      - posY: 10
#      - name: $signNameB
#    validate:
#      - eq: ["content.message","成功"]
#      - eq: ["content.status",200]
#
#- test:
#    name: "TC4-setup-模板关联内容域"
#    api: api/esignDocs/template/owner/contentAdd.yml
#    variables:
#      - description: null
#      - templateUuid: $newTemplateUuidCommon
#      - version: $newVersionCommon
#      - contentUuid: $contentUuidCommon
#      - contentCode: $contentCodeCommon
#      - contentName: $contentNameCommon
#      - edgeScope: 0
#    extract:
#      - templateContentUuidCommon: content.data.list.0
#    validate:
#      - eq: [ "content.message","成功" ]
#      - eq: [ "content.status",200 ]

############历史业务模板的第四步/文档模板的第二步，都变更成下方的接口调用（********）###############
- test:
    name: "templateDetail"
    api: api/esignDocs/template/owner/templateDetail.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
    extract:
      - _editUrl: content.data.editUrl
      - _previewUrl: content.data.previewUrl
      - _templateName: content.data.templateName
      - autoTestDocUuid1Common: content.data.docUid
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-service-config"
    api: api/esignDocs/epaasTemplate/service-config.yml
    variables:
      tplToken_config: ${getTplToken($_editUrl)}
      tmp_common_template_001: ${putTempEnv(_tmp_common_template_001, $tplToken_config)}
    extract:
      - _contentId: content.data.contents.0.id
      - _entityId: content.data.contents.0.entityId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "epaasTemplate-detail"
    api: api/esignDocs/epaasTemplate/detail.yml
    variables:
      tplToken_detail: ${ENV(_tmp_common_template_001)}
      contentId_detail: $_contentId
      entityId_detail: $_entityId
    extract:
      - _baseFile: content.data.baseFile
      - _originFile: content.data.originFile
    validate:
      - eq: ["content.code",0]
      - ne: ["content.data.originFile.resourceId",""]

- test:
    name: "epaasTemplate-batch-save-draft"
    api: api/esignDocs/epaasTemplate/batch-save-draft.yml
    variables:
      tplToken_draft: ${ENV(_tmp_common_template_001)}
      baseFile_draft: $_baseFile
      originFile_draft: $_originFile
      fillContent001: ${generate_field_object(单行文本1, TEXT)}
      fillContent002: ${generate_field_object(单行文本2, TEXT)}
      fields_draft: [ $fillContent001,$fillContent002 ]
      pageFormatInfoParam_draft: null
      name_draft: $_templateName
      contentId_draft: $_contentId
      entityId_draft: $_entityId
    extract:
      - _contentId: content.data.0.contentId
      - _contentVersionId: content.data.0.contentVersionId
    validate:
#      - eq: ["content.message","成功"]
      - eq: ["content.code",0]
      - ne: ["content.data",""]

- test:
    name: "TC5-setup-发布模板"
    api: api/esignDocs/template/owner/templatePublish.yml
    variables:
      - templateUuid: $newTemplateUuidCommon
      - version: $newVersionCommon
      - isPublish: true
    validate:
      - eq: ["content.message","成功"]
      - eq: ["content.status",200]