- config:
    name: 通过统一门户印控中心接口创建企业印章-统一的登录账号：ceswdzxzdhyhwgd1
    variables:
#      sealTypeCodeCommon: 1682663282631
      sealTypeCodeCommon: "COMMON-SEAL"
      organizationCodeSeal: $organizationCodeSeal #这个参数需要调用方入参传入
#      sealCodeCommon: ${getDateTime()}

      ###调试用的参数
#      organizationName: "esigntest天印PO测试企业"
#      sealTypeCodeCommon: WMG
    export:
      - sealIdOrg
      - sealTypeCodeCommon

#- test:
#    name: TC1-新建印章类型
#    api: api/esignSeals/sealType/saveSealType.yml
#    variables:
#      sealTypeCode: $sealTypeCodeCommon
#      sealTypeName: $sealTypeCodeCommon
#
#####创建一枚模板印章
#- test:
#    name: TC1-创建一枚自定义印章和一枚模板印章
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/create.yml
#    variables:
#      organizationSeals_create_json:
#        userCode: ${ENV(sign01.userCode)}
#        organizationCode: $organizationCodeSeal
#        sealGroupName: "印章场景测试-$sealTypeCodeCommon"
#        sealTypeCode: $sealTypeCodeCommon
#        sealInfos:
#          - sealUpperText: "自动化-SCENE-模板印章"
#            sealPattern: 1
#            sealHeight: 50
#            sealTemplateStyle: 1
#            sealName: "SCENE-TC1-模板印章"
#            sealShape: 2
#            sealSource: 2
#            sealWidth: 50
#    extract:
#      sealTC001: content.data.sealInfos.0.sealId
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - len_ge: [ content.data.sealGroupId, 1 ]
#      - len_ge: [ content.data.sealInfos, 1 ]
#      - eq: [ content.data.sealInfos.0.sealStatus, "2" ]
#
##授权用印人
#- test:
#    name: TC1-授权用印人所有人
#    api: api/esignSeals/v1/sealcontrols/organizationSeals/sealsigners.yml
#    variables:
#      sealsigners_json:
#        authorizationScope: 2
#        sealId: $sealTC001
#        sealsignerType:  1
#        sealsignersInfos:
#          - customAccountNo: ${ENV(csqs.accountNo)}
#            customOrgNo: ${ENV(csqs.orgNo)}
#    validate:
#      - eq: [ content.code, 200 ]
#      - eq: [ content.message, "成功" ]
#      - len_gt: [ content.data.sealId, 1 ]

- test:
    name: TC2-查询企业印章(查询为空则创建)
    api: api/esignSeals/v1/sealcontrols/organizationSeals/organizationSealsList.yml
    variables:
      tmp00: {'organizationCode': $organizationCodeSeal ,'sealTypeCode': $sealTypeCodeCommon}
      tmp01: '${getEnterpriseSeal($tmp00)}'
      organizationSeals_list_sealTypeCode: $sealTypeCodeCommon
      organizationSeals_list_organizationCode: $organizationCodeSeal01
      organizationSeals_list_sealPattern: 1
    extract:
      - sealIdOrg: content.data.records.0.sealId
    validate:
      - eq: [ content.code, 200 ]
      - eq: [ content.message, '成功' ]
      - ge: [ content.data.total, 1 ]
