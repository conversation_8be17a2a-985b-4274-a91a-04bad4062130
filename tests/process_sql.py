#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to remove the id column from INSERT statements in SQL file
"""
import re

def process_sql_file(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_lines = []

    for line in lines:
        # Check if this is an INSERT statement line with id column
        if 'INSERT INTO' in line and '(id,' in line:
            # Remove the id column from the INSERT statement
            line = re.sub(r'\(id,([^)]+)\)', r'(\1)', line)

        # Check if this is a VALUES line that starts with a quoted id value
        elif line.strip().startswith("('") and line.count("',") > 0:
            # Remove the first quoted value (the id value)
            # Pattern: ('id_value', -> (
            line = re.sub(r"(\s*)\('([^']+)',", r'\1(', line)

        processed_lines.append(line)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)

    print(f"Processed {input_file} -> {output_file}")

if __name__ == "__main__":
    process_sql_file("temp/tmp1.sql", "./tmp3_processed.sql")
