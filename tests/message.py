"""
@author:ming<PERSON>i
@file:message.py
@time:2022/05/16
@desc: 消息自动化公用方法
"""

import os
import json
import random
import sys
import time
import pymysql
from sqlite3 import Cursor
from messageConfig import const


class MessageFactory(object):
    """
    消息工厂类
    """
    message = {}

    @classmethod
    def get_message_by_id(cls, templateId):
        """类方法:通过templateId获取具体的消息类"""
        return cls.message.get(templateId)

    @classmethod
    def register(cls, templateId, message):
        """类方法:注册消息类型"""
        if templateId == "":
            raise Exception("消息模板id不能为空")
        cls.message[templateId] = message


class AbstractMessage(object):
    """
    消息抽象类
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        pass

    def get_type(self):
        pass


class InviteMessage(AbstractMessage):
    """
    邀请签署通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.校验流程状态
        processStatus = messageSubject["process"]["sign_status"]
        # if processStatus != 1:
        #     raise Exception("任务状态不正确")
        # 2.根据策略筛选主体
        waitingSignerList = []
        signerList = messageSubject["message_signer_list"]
        for item in signerList:
            if item["isAutoSign"] != 0:
                continue
            if item["roleType"] != 1:
                continue
            if signNode and item["signNode"] != signNode:
                continue
            waitingSignerList.append(item)
        messageSubject["message_signer_list"] = waitingSignerList
        messageSubject["owner_list"] = []
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_INVITE"

    def collect_context(self):
        MessageFactory.register(self.get_type(), InviteMessage)


class UndersignMessage(AbstractMessage):
    """
    签署人签署完成
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.根据策略筛选主体
        signedList = []
        needNotSignList = []
        ccList = []
        for signer in messageSubject["message_signer_list"]:
            if signer["roleType"] == 2:
                ccList.append(signer)
                continue
            if signer["isAutoSign"] != 0:
                continue
            if signer["signNode"] == signNode and signer["signOrder"] == signOrder and signer["status"] == 2:
                signedList.append(signer)
                continue
            if signer["signNode"] == signNode and signer["status"] == 4:
                needNotSignList.append(signer)
        if messageSubject["process"]["initiator_organize_name"]:
            messageSubject["sign_name"] = messageSubject["process"]["initiator_organize_name"]
        else:
            messageSubject["sign_name"] = messageSubject["process"]["initiator_user_name"]
        messageSubject["message_signer_list"] = signedList
        messageSubject["message_type"] = self.get_type()
        messageSubject["other_signer_list"] = needNotSignList
        messageSubject["cc_list"] = ccList
        # 2.获取当前签署人
        currentSigner = dict()
        for item in messageSubject["all_actor_list"]:
            if item["roleType"] != 1:
                continue
            if item["status"] != 2:
                continue
            if signNode and item["signNode"] != signNode:
                continue
            if signOrder and item["signOrder"] != signOrder:
                continue
            currentSigner = item
            break
        if currentSigner and currentSigner["userName"]:
            messageSubject["user_name"] = currentSigner["userName"]
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_SIGNER_SIGNED"

    def collect_context(self):
        MessageFactory.register(self.get_type(), UndersignMessage)


class SignOverMessage(AbstractMessage):
    """
    签署流程完结通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.组装主体
        signedList = []
        needNotSignList = []
        ccList = []
        for signer in messageSubject["message_signer_list"]:
            if signer["roleType"] == 1 and signer["status"] == 2:
                signedList.append(signer)
            if signer["roleType"] == 1 and signer["status"] == 4:
                needNotSignList.append(signer)
        for cc in messageSubject["cc_list"]:
            if cc["roleType"] == 2:
                ccList.append(cc)
        if messageSubject["process"]["initiator_organize_name"]:
            messageSubject["sign_name"] = messageSubject["process"]["initiator_organize_name"]
        else:
            messageSubject["sign_name"] = messageSubject["process"]["initiator_user_name"]
        messageSubject["message_signer_list"] = signedList
        messageSubject["other_signer_list"] = needNotSignList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = ccList
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_FLOW_FINISH"

    def collect_context(self):
        MessageFactory.register(self.get_type(), SignOverMessage)


class SignExpireMessage(AbstractMessage):
    """
    流程过期通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.校验流程状态
        # if messageSubject["process"]["sign_status"] != 3:
        #     raise Exception("流程状态不匹配")
        # # 2.组装主体
        signedAndNoticedList = []
        for item in messageSubject["message_signer_list"]:
            if item["roleType"] != 1:
                continue
            signedAndNoticedList.append(item)
        messageSubject["message_signer_list"] = signedAndNoticedList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_FLOW_OVERTIME"

    def collect_context(self):
        MessageFactory.register(self.get_type(), SignExpireMessage)


class SignCutMessage(AbstractMessage):
    """
    流程截止通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 为了避免流程没来得及发送消息。需要等待
        time.sleep(40)
        # 1.校验流程状态
        # if messageSubject["process"]["sign_status"] != 1:
        #     raise Exception("流程状态不匹配")
        # 2.组装主体
        waitingSignerList = []
        for item in messageSubject["message_signer_list"]:
            if item["roleType"] != 1:
                continue
            if item["status"] != 1 and item["status"] != 5:
                continue
            if signNode and signNode != item["signNode"]:
                continue
            if signOrder and signOrder != item["signOrder"]:
                continue
            waitingSignerList.append(item)
        processConfig = get_process_config_by_process_id(messageSubject["process"]["id"])
        if processConfig and hasattr(messageSubject["process"], "sign_off_time"):
            messageSubject["sign_off_time"] = messageSubject["process"]["sign_off_time"]
        messageSubject["message_signer_list"] = waitingSignerList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_FLOW_EXPIRE_REMIND"

    def collect_context(self):
        MessageFactory.register(self.get_type(), SignCutMessage)


class RefusalMessage(AbstractMessage):
    """
    签署人拒签通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.校验流程状态
        # if messageSubject["process"]["sign_status"] != 4:
        #     raise Exception("流程状态不匹配")
        # 2.组装主体
        signedAndNoticedList = []
        for item in messageSubject["message_signer_list"]:
            if item["roleType"] != 1:
                continue
            if not messageSubject["refuse_signer"] and item["status"] == 3:
                messageSubject["refuse_signer"] = item
                continue
            signedAndNoticedList.append(item)
        messageSubject["message_signer_list"] = signedAndNoticedList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_FLOW_REFUSE"

    def collect_context(self):
        MessageFactory.register(self.get_type(), RefusalMessage)


class SignAbrogationMessage(AbstractMessage):
    """
    签署中作废通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.校验流程状态
        # if messageSubject["process"]["sign_status"] != 5:
        #     raise Exception("流程状态不匹配")
        # 2.组装主体
        signedAndNoticedList = []
        for item in messageSubject["message_signer_list"]:
            if item["roleType"] != 1:
                continue
            signedAndNoticedList.append(item)
        messageSubject["message_signer_list"] = signedAndNoticedList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGN_FLOW_CANCEL"

    def collect_context(self):
        MessageFactory.register(self.get_type(), SignAbrogationMessage)


class SignedAbrogationMessage(AbstractMessage):
    """
    签署完成作废通知
    """

    def get_message_content(self, messageSubject, signNode, signOrder):
        # 1.组装主体
        waitingSignerList = []
        for item in messageSubject["message_signer_list"]:
            if item["roleType"] != 1:
                continue
            if item["status"] != 2:
                continue
            waitingSignerList.append(item)
        # 2.查询相关的作废协议
        invalidProcess = get_process_by_invalid_id(messageSubject["process"]["id"])
        if invalidProcess:
            messageSubject["invalid_process"] = invalidProcess
        messageSubject["message_signer_list"] = waitingSignerList
        messageSubject["message_type"] = self.get_type()
        messageSubject["cc_list"] = []
        # 3.构建最终消息体
        final = build_message_final(messageSubject, self.get_type())
        return final

    def get_type(self):
        return "SIGNED_FLOW_CANCEL"

    def collect_context(self):
        MessageFactory.register(self.get_type(), SignedAbrogationMessage)


def init_messages():
    """初始化方法:收集消息函数"""
    InviteMessage().collect_context()
    RefusalMessage().collect_context()
    SignAbrogationMessage().collect_context()
    SignCutMessage().collect_context()
    SignedAbrogationMessage().collect_context()
    SignExpireMessage().collect_context()
    SignOverMessage().collect_context()
    UndersignMessage().collect_context()


def check_message(processUuid: str, messageType: str, signNode: int, signOrder: int, timestamp: str):
    """
    校验签署消息
    :param processUuid: 流程uuid
    :type processUuid:  str
    :param messageType: 消息模板id
    :type messageType: str
    :param signNode: 签署节点
    :type signNode: int
    :param signOrder: 签署顺序
    :type signOrder: int
    :param timestamp: 时间戳
    :type timestamp: str
    """

    # 1.获取预发送消息内容
    currentMessageConfig = get_current_message_config(processUuid, messageType)
    content = get_message_content(processUuid, messageType, signNode, signOrder)
    # 2.比对预发送和实际发送
    ownerMap = content["owner"]
    signerMap = content["signer"]
    ccMap = content["cc"]
    # if (not signerMap) and currentMessageConfig["singerFlag"] == "1":
    #     raise Exception("预发送的签署人消息不应为空，请检查相关数据")
    # if (not ownerMap) and currentMessageConfig["ownerFlag"] == "1":
    #     raise Exception("预发送的发起人消息不应为空，请检查相关数据")
    # if (not ccMap) and currentMessageConfig["ccFlag"] == "1":
    #     raise Exception("预发送的抄送人消息不应为空，请检查相关数据")
    for userCode, signer in signerMap.items():
        notice = get_notice_by_condition(userCode, signer["contact"]["notice_user_info"]["receive_organization_code"],
                                         signer["template_id"], timestamp)
        if not notice:
            print("签署人消息比对失败:", userCode)
            return False
    for userCode, owner in ownerMap.items():
        notice = get_notice_by_condition(userCode, owner["contact"]["notice_user_info"]["receive_organization_code"],
                                         owner["template_id"], timestamp)
        if not notice:
            print("发起人消息比对失败:", userCode)
            return False
    for userCode, cc in ccMap.items():
        notice = get_notice_by_condition(userCode, cc["contact"]["notice_user_info"]["receive_organization_code"],
                                         cc["template_id"], timestamp)
        if not notice:
            print("抄送人消息比对失败:", userCode)
            return False
    return True


def check_message_not_send_invite(processUuid: str, messageType: str, signNode: int, signOrder: int, timestamp: str,
                                  flag: bool):
    """
    校验签署消息
    :param processUuid: 流程uuid
    :type processUuid:  str
    :param messageType: 消息模板id
    :type messageType: str
    :param signNode: 签署节点
    :type signNode: int
    :param signOrder: 签署顺序
    :type signOrder: int
    :param timestamp: 时间戳
    :type timestamp: str
    :param flag: 是否发送标记
    :type flag: bool
    """
    # 1.获取预发送消息内容
    content = get_message_content(processUuid, messageType, signNode, signOrder)
    # 2.比对预发送和实际发送
    signerMap = content["signer"]
    for userCode, signer in signerMap.items():
        notice = get_notice_by_condition(userCode, signer["contact"]["notice_user_info"]["receive_organization_code"],
                                         signer["template_id"], timestamp)
        if not (flag and notice):
            return False


def check_message_is_auto_sign(processUuid: str, messageType: str, signNode: int, signOrder: int, timestamp: str):
    """
    静默签消息校验
    :param processUuid:
    :type processUuid:
    :param messageType:
    :type messageType:
    :param signNode:
    :type signNode:
    :param signOrder:
    :type signOrder:
    :param timestamp:
    :type timestamp:
    :return:
    :rtype:
    """
    # 1.获取预发送消息内容
    currentMessageConfig = get_current_message_config(processUuid, messageType)
    content = get_message_content(processUuid, messageType, signNode, signOrder)
    # 2.比对预发送和实际发送
    ownerMap = content["owner"]
    signerMap = content["signer"]
    ccMap = content["cc"]
    flag = (messageType == "SIGN_INVITE")
    if not flag:
        if not signerMap:
            if (not signerMap) and currentMessageConfig["singerFlag"] == "1":
                raise Exception("预发送的签署人消息不应为空，请检查相关数据")
        if (not ownerMap) and currentMessageConfig["ownerFlag"] == "1":
            raise Exception("预发送的发起人消息不应为空，请检查相关数据")
        if (not ccMap) and currentMessageConfig["ccFlag"] == "1":
            raise Exception("预发送的抄送人消息不应为空，请检查相关数据")
    for userCode, signer in signerMap.items():
        notice = get_notice_by_condition(userCode, signer["contact"]["notice_user_info"]["receive_organization_code"],
                                         signer["template_id"], timestamp)
        if not notice:
            if not flag:
                return False
        else:
            if flag:
                return False

    for userCode, owner in ownerMap.items():
        notice = get_notice_by_condition(userCode, owner["contact"]["notice_user_info"]["receive_organization_code"],
                                         owner["template_id"], timestamp)
        if not notice:
            if not flag:
                return False
        else:
            if flag:
                return False
    for userCode, cc in ccMap.items():
        notice = get_notice_by_condition(userCode, cc["contact"]["notice_user_info"]["receive_organization_code"],
                                         cc["template_id"], timestamp)
        if not notice:
            if not flag:
                return False
        else:
            if flag:
                return False
    return True


def get_current_message_config(processUuid: str, templateId: str):
    # 1.获取消息配置
    processDo = get_process_by_uuid(processUuid)
    if processDo["id"]:
        processConfig = get_process_config_by_process_id(processDo["id"])
    else:
        raise Exception("流程id不存在")
    if not processConfig:
        raise Exception("流程配置不存在")
    if not processConfig["message_config"]:
        print("流程快照表消息配置为空，取业务类型中的消息配置")
        business = get_business_by_id(processConfig["business_type_id"])
        messageConfig = business["message_config"]
    else:
        messageConfig = processConfig["message_config"]
    if not messageConfig:
        raise Exception("业务类型未关联消息配置")
    configArray = json.loads(messageConfig)
    currentConfig = dict()
    for config in configArray:
        if config["templateCode"] == templateId:
            currentConfig = config
            break
    if not currentConfig:
        raise Exception("未查询到模板对应的消息配置")
    return currentConfig


def get_message_content(processUuid: str, messageType: str, signNode: int, signOrder: int):
    """
    根据业务节点获取对应消息内容
    :param processUuid: 流程uuid
    :type processUuid: str
    :param messageType: 消息模板id
    :type messageType: str
    :param signNode: 签署节点
    :type signNode: int
    :param signOrder: 签署顺序
    :type signOrder: int
    :return: content
    :rtype: object
    """
    init_messages()
    messageImpl = MessageFactory.get_message_by_id(messageType)
    if not messageImpl:
        raise Exception("未查询到此类型的消息")
    subject = get_message_subject(messageType, processUuid)
    content = messageImpl().get_message_content(subject, signNode, signOrder)
    return content


def get_message_subject(templateId: str, processUuid: str):
    """
    根据业务获取消息主体
    :param templateId: 消息模板id
    :type templateId: str
    :param processUuid: 流程uuid
    :type processUuid: str
    :return:
    :rtype:
    """
    # 1.查询流程信息
    process = get_process_complete(processUuid)
    if not process:
        raise Exception("流程信息不存在")
    # 2.获取消息配置
    processConfig = get_process_config_by_process_id(process["id"])
    if not processConfig:
        raise Exception("流程消息快照不存在")
    messageConfig = processConfig["message_config"]
    if not messageConfig:
        print("流程快照表消息配置为空，取业务类型中的消息配置")
        business = get_business_by_id(processConfig["business_type_id"])
        messageConfig = business["message_config"]
    if not messageConfig:
        raise Exception("业务类型未关联消息配置")
    configArray = json.loads(messageConfig)
    currentConfig = dict()
    for config in configArray:
        if config["templateCode"] == templateId:
            currentConfig = config
            break
    if not currentConfig:
        raise Exception("未查询到模板对应的消息配置")
    # 3.根据策略筛选
    ccList = []
    userCodeList = []
    todoSingerList = []
    refusalActor = dict()
    messageSingerList = []
    actorList = get_actor_by_process_id(process["id"])
    ownerList = build_process_actor_for_owner(process, currentConfig)
    for actor in actorList:
        userCodeList.append(actor["userCode"])
        if actor["status"] == 3:
            refusalActor = actor
        if actor["roleType"] == 1:
            todoSingerList.append(actor)
            if currentConfig["singerFlag"] == "1":
                messageSingerList.append(actor)
        if currentConfig["ccFlag"] == "1" and actor["roleType"] == 2:
            ccList.append(actor)
    if len(ownerList) != 0:
        userCodeList.append(ownerList[0]["user_code"])
        # 4.获取用户信息
    userList = get_user_by_code_list(userCodeList)
    userMap = dict()
    for user in userList:
        userMap[user["user_code"]] = user
    # 5.组装消息主体
    messageSubject = dict()
    messageSubject["cc_list"] = ccList
    messageSubject["todo_singer_list"] = todoSingerList
    messageSubject["message_signer_list"] = messageSingerList
    messageSubject["owner_list"] = ownerList
    messageSubject["all_actor_list"] = actorList
    messageSubject["process"] = process
    messageSubject["refuse_signer"] = refusalActor
    messageSubject["user_map"] = userMap
    return messageSubject


def build_message_final(messageSubject: object, messageType: str):
    """
    根据业务节点构建消息内容
    :param messageSubject: messageSubject
    :type messageSubject: object
    :param messageType: messageType
    :type messageType: str
    :return: messageInfo
    :rtype: object
    """
    messageInfo = dict()
    signerMap = dict()
    ownerMap = dict()
    ccMap = dict()
    signatoryName = None
    if "signatory_name" in messageSubject:
        signatoryName = messageSubject["signatory_name"]
    # 1.签署人
    for signer in messageSubject["message_signer_list"]:
        message = dict()
        message["template_id"] = messageType
        if hasattr(messageSubject, "sign_off_time"):
            message["end_time"] = messageSubject["sign_off_time"]
        message["actor"] = signer
        if hasattr(messageSubject, "refuse_signer"):
            message["reject_user_name"] = messageSubject["refuse_signer"]["user_name"]
        message["role_type"] = 1
        message["signatory_name"] = signer["userName"]
        if hasattr(messageSubject, "invalid_process"):
            message["void_agreement_title"] = messageSubject["invalid_process"]["subject"]
        if signatoryName:
            signatoryName = signer["userName"]
        mqRequest = build_mq_message(message, messageSubject["user_map"][signer["userCode"]], messageSubject["process"])
        signerMap[signer["userCode"]] = mqRequest
    # 2.发起人
    for owner in messageSubject["owner_list"]:
        messageRequest = dict()
        messageRequest["template_id"] = messageType
        if hasattr(messageSubject, "sign_off_time"):
            messageRequest["end_time"] = messageSubject["sign_off_time"]
        messageRequest["actor"] = owner
        if hasattr(messageSubject, "refuse_signer"):
            messageRequest["reject_user_name"] = messageSubject["refuse_signer"]["user_name"]
        messageRequest["role_type"] = 0
        messageRequest["signatory_name"] = signatoryName
        if hasattr(messageSubject, "invalid_process"):
            messageRequest["void_agreement_title"] = messageSubject["invalid_process"]["subject"]
        mqRequest = build_mq_message(messageRequest, messageSubject["user_map"][owner["user_code"]],
                                     messageSubject["process"])
        ownerMap[owner["user_code"]] = mqRequest
    # 3.抄送人
    for cc in messageSubject["cc_list"]:
        messageDo = dict()
        messageDo["template_id"] = messageType
        if hasattr(messageSubject, "sign_off_time"):
            messageDo["end_time"] = messageSubject["sign_off_time"]
        messageDo["actor"] = cc
        if hasattr(messageSubject, "refuse_signer"):
            messageDo["reject_user_name"] = messageSubject["refuse_signer"]["user_name"]
        messageDo["role_type"] = 2
        if hasattr(messageSubject, "invalid_process"):
            messageDo["void_agreement_title"] = messageSubject["invalid_process"]["subject"]
        mqRequest = build_mq_message(messageDo, messageSubject["user_map"][cc["userCode"]], messageSubject["process"])
        ccMap[cc["userCode"]] = mqRequest
    messageInfo["signer"] = signerMap
    messageInfo["owner"] = ownerMap
    messageInfo["cc"] = ccMap
    return messageInfo


def build_mq_message(request: object, user: object, process: object):
    """
    构建mq请求
    :param process: 流程
    :type process: object
    :param request: request请求
    :type request: object
    :param user: 用户信息
    :type user: object
    :return: rpcSendMessageRequestVO
    :rtype: object
    """
    # 1.获取签署连接
    getSignUrlBO = dict()
    uuid = process["uuid"]
    projectId = process["project_id"]
    if request["role_type"] == 1 or request["role_type"] == 2:
        if hasattr(request, "invalid_process"):
            uuid = request["invalid_process"]["uuid"]
        userCode = request["actor"]["userCode"]
        userType = request["actor"]["userType"]
        organizeCode = request["actor"]["organizeCode"]
    else:
        userType = process["initiator_user_type"]
        userCode = process["initiator_user_code"]
        organizeCode = process["initiator_organize_code"]
    getSignUrlBO["project_id"] = projectId
    getSignUrlBO["user_type"] = userType
    getSignUrlBO["process_id"] = uuid
    getSignUrlBO["user_code"] = userCode
    getSignUrlBO["organize_code"] = organizeCode
    signLink = get_sign_link(getSignUrlBO)

    # 2.构建消息模板
    rpcSendMessageRequestVO = dict()
    contactRequestVO = dict()
    param = dict()
    param["flow_title"] = process["subject"]
    param["sign_link"] = signLink["sign_short_url"]
    param["long_link"] = signLink["sign_url"]
    param["signatory_name"] = request["signatory_name"]
    if hasattr(request, "sign_name"):
        param["sign_name"] = request["sign_name"]
    if hasattr(request, "void_agreement_title"):
        param["void_agreement_title"] = request["void_agreement_title"]
    if hasattr(request, "user_code"):
        param["user_code"] = request["user_code"]
    if hasattr(request, "end_time"):
        param["end_time"] = request["end_time"]
    if hasattr(request, "reject_user_name"):
        param["reject_name"] = request["reject_user_name"]
    rpcSendMessageRequestVO["template_param"] = param
    rpcSendMessageRequestVO["template_id"] = request["template_id"]
    noticeRequest = dict()
    noticeRequest["receive_organization_name"] = user["organization_name"]
    noticeRequest["receive_user_code"] = user["user_code"]
    noticeRequest["receive_user_name"] = user["user_name"]
    noticeRequest["receive_status"] = "1"
    noticeRequest["receive_organization_code"] = user["organization_code"]
    contactRequestVO["notice_user_info"] = noticeRequest
    contactRequestVO["mobile"] = user["user_mobile"]
    contactRequestVO["email"] = user["user_email"]
    rpcSendMessageRequestVO["contact"] = contactRequestVO
    rpcSendMessageRequestVO["process_uuid"] = uuid
    rpcSendMessageRequestVO["process_id"] = process["id"]
    return rpcSendMessageRequestVO


def get_sign_link(getSignUrlBO: object):
    """
    获取签署连接
    :param getSignUrlBO: getSignUrlBO
    :type getSignUrlBO: object
    :return: signUrlVO
    :rtype: object
    """
    signUrlVO = dict()
    internal = False
    if getSignUrlBO["user_type"] != 2:
        internal = True
    url = build_sign_long_url(
        getSignUrlBO["process_id"], getSignUrlBO["user_code"],
        getSignUrlBO["organize_code"], internal)
    shortUrl = encrypted_long_link(23)
    signUrlVO["sign_url"] = url
    signUrlVO["sign_short_url"] = shortUrl
    return signUrlVO


def encrypted_long_link(index: int):
    """
    模拟生成加密短链，但不支持访问
    支持访问的短链，需要文件中心开口子
    :param index: 索引，随便填
    :type index: int
    :return: shortLink
    :rtype: str
    """
    join = ''.join([get_random_str(2), convert_dec_to_62(index)])
    return join


def get_random_str(length: int):
    """
    获取随机字符串
    :param length: 长度
    :type length: int
    :return: string
    :rtype: str
    """
    string = ""
    while length > 0:
        i = random.randint(0, 61)
        string = ''.join([string, const.CHARS[i]])
        length = length - 1
    return string


def convert_dec_to_62(num: int):
    """
    将10进制数转为62进制字符串
    :param num:
    :type num:
    :return:
    :rtype:
    """
    desc = ""
    while num > 0:
        num = (int)(num % 62)
        desc = ''.join([desc, const.CHARS[num]])
        num /= 62
    return desc


def build_sign_long_url(processId: str, userCode: str, organizeCode: str, internal: bool):
    """
    拼接签署长链
    :param processId: 流程id
    :type processId: str
    :param userCode: 用户编码
    :type userCode: str
    :param organizeCode: 组织编码
    :type organizeCode: str
    :param internal: 是否外网
    :type internal: bool
    :return: url
    :rtype: str
    """
    if internal:
        signWebUrl = const.SIGN_WEB_DOMAIN_URL
    else:
        signWebUrl = const.SIGN_WEB_URL
    url = ''.join([signWebUrl, const.SIGN_PAGE, "?processId=", processId, "&userCode=", userCode])
    if organizeCode:
        url = ''.join([url, "&organizeCode=", organizeCode])
    return url


def build_process_actor_for_owner(process: object, messageConfig: object):
    """
    根据消息配置构建流程发起人列表
    :param process: 流程信息
    :type process: object
    :param messageConfig: 流程配置
    :type messageConfig: object
    :return:
    :rtype:
    """
    ownerList = []
    if messageConfig["ownerFlag"] == "1":
        owner = dict()
        owner["user_code"] = process["initiator_user_code"]
        owner["organize_code"] = process["initiator_organize_code"]
        owner["user_name"] = process["initiator_user_name"]
        owner["organize_name"] = process["initiator_organize_name"]
        ownerList.append(owner)
    return ownerList


def get_timestamp1():
    """
    获取当前13位时间戳
    """
    t = time.time()
    return int(round(t * 1000))


# ########################################################  数据持久层  ##################################################

def get_process_by_uuid(processUuid: str):
    """
    根据流程uuid获取流程信息
    :param processUuid: 流程uuid
    :type processUuid: str
    :return: process
    :rtype: object
    """
    # 查询流程
    signConnection = connect(1)
    try:
        processParam = {"processUuid": processUuid}
        processSql = const.GET_PROCESS_BY_UUID.format(**processParam)
        processInfo = execute_sql_convert_object(signConnection, processSql)
        return processInfo
    finally:
        signConnection.close()


def get_notice_by_condition(userCode: str, orgCode: str, messageType: str, timestamp: str):
    """
    获取站内信
    :param timestamp: 创建流程的时间戳
    :type timestamp: str
    :param userCode: 用户编码
    :type userCode: str
    :param orgCode: 组织编码
    :type orgCode: str
    :param messageType: 消息类型编码
    :type messageType: str
    :return: notice 站内信
    :rtype: object
    """
    manageConnection = connect(3)
    try:
        noticeParam = \
            {
                "userCode": userCode,
                "orgCode": orgCode,
                "messageTypeDesc": const.MESSAGE_TYPE[messageType],
                "timestamp": timestamp
            }
        noticeSql = const.GET_NOTICE_BY_USER_CODE.format(**noticeParam)
        notice = execute_sql_convert_object(manageConnection, noticeSql)
        return notice
    finally:
        manageConnection.close()


def get_process_initiator(processId: int):
    """
    查询流程发起人
    :param processId:
    :type processId:
    :return:
    :rtype:
    """
    # 查询流程
    signConnection = connect(1)
    try:
        processInitiatorParam = {"processId": processId, "roleType": const.ACTOR_ROLE_TYPE["owner"]}
        processInitiatorSql = const.GET_PROCESS_INITIATOR.format(**processInitiatorParam)
        process = execute_sql_convert_object(signConnection, processInitiatorSql)
        return process
    finally:
        signConnection.close()


def get_business_by_id(businessTypeId: int):
    """
    根据业务类型id获取业务类型详情
    :param businessTypeId: 业务类型id
    :type businessTypeId: int
    :return: business
    :rtype: object
    """
    signConnection = connect(1)
    try:
        businessParam = {"businessId": businessTypeId}
        businessSql = const.GET_BUSINESS_BY_ID.format(**businessParam)
        business = execute_sql_convert_object(signConnection, businessSql)
        return business
    finally:
        signConnection.close()


def get_process_config_by_process_id(processId: int):
    """
    根据流程id获取业务类型配置
    :param processId: 流程id
    :type processId: int
    :return: processConfig
    :rtype: object
    """
    signConnection = connect(1)
    try:
        configParam = {"processId": processId}
        configSql = const.GET_PROCESS_CONFIG_BY_PROCESS_ID.format(**configParam)
        processConfig = execute_sql_convert_object(signConnection, configSql)
        return processConfig
    finally:
        signConnection.close()


def get_process_complete(processUuid: str):
    """
    根据流程uuid获取流程信息,包含发起人
    :param processUuid: 流程uuid
    :type processUuid: str
    :return: process 流程信息
    :rtype: object
    """
    processBase = get_process_by_uuid(processUuid)
    initiator = get_process_initiator(processBase["id"])
    processBase["initiator_user_type"] = initiator["user_type"]
    processBase["initiator_user_code"] = initiator["user_code"]
    processBase["initiator_user_name"] = initiator["user_name"]
    processBase["initiator_organize_code"] = initiator["organize_code"]
    processBase["initiator_organize_name"] = initiator["organize_name"]
    processBase["initiator_department_code"] = initiator["department_code"]
    return processBase


def get_actor_by_process_id(processId: int):
    """
    根据流程id获取流程参与人
    :param processId: 流程id
    :type processId: int
    :return: actorList
    :rtype: collections.Iterable
    """
    signConnection = connect(1)
    try:
        actorParam = {"processId": processId}
        actorSql = const.GET_ACTOR_LIST_BY_PROCESS_ID.format(**actorParam)
        actorList = execute_sql_convert_array(signConnection, actorSql)
        return actorList
    finally:
        signConnection.close()


def get_process_by_invalid_id(invalidId: int):
    """
    查询签署完成作废流程关联的作废协议
    :param invalidId: 协议流程id
    :type invalidId: int
    :return: invalidProcess
    :rtype: object
    """
    if not invalidId:
        return None
    signConnection = connect(1)
    try:
        invalidParam = {"invalidId": invalidId}
        invalidSql = const.GET_BY_INVALID_PROCESS_ID.format(**invalidParam)
        invalidProcess = execute_sql_convert_object(signConnection, invalidSql)
        initiator = get_process_initiator(invalidProcess["id"])
        if initiator:
            invalidProcess["initiator_user_type"] = initiator["user_type"]
            invalidProcess["initiator_user_code"] = initiator["user_code"]
            invalidProcess["initiator_user_name"] = initiator["user_name"]
            invalidProcess["initiator_organize_code"] = initiator["organize_code"]
            invalidProcess["initiator_organize_name"] = initiator["organize_name"]
        return invalidProcess
    finally:
        signConnection.close()


def get_user_by_code_list(userCodeList: list):
    """
    根据用户编码批量获取用户信息
    :param userCodeList: 用户编码列表
    :type userCodeList: list
    :return: userList
    :rtype: list
    """
    userList = []
    for userCode in userCodeList:
        user = get_user_by_code(userCode)
        userList.append(user)
    return userList


def get_user_by_code(userCode: str):
    """
    根据用户编码获取用户信息
    :param userCode: 用户编码
    :type userCode: str
    :return: user
    :rtype: object
    """
    manageConnection = connect(3)
    try:
        userParam = {"userCode": userCode}
        userSql = const.GET_USER_BY_CODE.format(**userParam)
        user = execute_sql_convert_object(manageConnection, userSql)
        return user
    finally:
        manageConnection.close()


def connect(dbType: int):
    """
    创建数据库连接
    :param dbType: 1:签署中心数据库 2:公有云数据库 3:管理平台数据库
    :type dbType: int
    :return: db 数据库连接
    :rtype: pymysql.Connection
    """

    def signs():
        try:
            db = pymysql.connect(
                host='test-nodeport.tsign.cn',
                port=32103,
                user='root',
                passwd='Q0ct4ab3gP1',
                db='stable_esign6_signs',
                charset='utf8'
            )
            return db
        except Exception:
            raise Exception("签署中心数据库连接失败")

    def cloud():
        return None

    def manage():
        try:
            db = pymysql.connect(
                host='test-nodeport.tsign.cn',
                port=32103,
                user='root',
                passwd='Q0ct4ab3gP1',
                db='stable_esign6_manage',
                charset='utf8'
            )
            return db
        except Exception:
            raise Exception("签署中心数据库连接失败")

    def default1():
        raise Exception("未查询到对应的数据库类型")

    dbTypes = {
        1: signs,
        2: cloud,
        3: manage
    }
    method = dbTypes.get(dbType, default1)
    if method:
        return method()


def execute_sql_open(connection: pymysql.Connection, sql: str):
    """
    执行sql语句 不关闭连接
    :param connection: 数据库连接
    :type connection: object
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    # print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        result = cursor.fetchone()
        connection.commit()
        # print("查询结果:", result)
        return result
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()


def execute_sql_close(connection: pymysql.Connection, sql: str):
    """
    执行sql语句 并关闭连接
    :param connection: 数据库连接
    :type connection: pymysql.Connection
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    execute_sql_open(connection, sql)
    connection.close()


def execute_sql_convert_object(connection: pymysql.connections.Connection, sql: str):
    """
    执行sql，返回唯一记录，并根据表中字段名，将tuple格式转化成dict格式
    :param connection: 数据库连接
    :type connection: pymysql.connections.Connection
    :param sql: sql语句
    :type sql: str
    :return: result 数据库查询结果 tuple格式
    :rtype: object
    """
    # print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        data = cursor.fetchone()
        # print("查询结果:", data)
        pojo = dict()
        if data:
            index = 0
            for desc in cursor.description:
                pojo[desc[0]] = data[index]
                index = index + 1
        connection.commit()
        return pojo
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()


def execute_sql_convert_array(connection: pymysql.Connection, sql: str):
    """
    执行sql，返回多条记录，并根据表中字段名，将tuple格式转化成dict格式
    :param connection: 数据库连接
    :type connection: pymysql.Connection
    :param sql: sql语句
    :type sql: str
    :return: res 结果集
    :rtype: list
    """
    # print("执行sql:", sql)
    cursor = connection.cursor()
    try:
        cursor.execute(sql)
        data = cursor.fetchall()
        # print("查询结果:", data)
        # 转换结果类型
        index_dict = get_index_dict(cursor)
        res = []
        for datai in data:
            resi = dict()
            for indexi in index_dict:
                resi[indexi] = datai[index_dict[indexi]]
            res.append(resi)
        connection.commit()
        return res
    except pymysql.Error as e:
        connection.rollback()
        print("数据库查询失败：" + str(e))
    except pymysql.ProgrammingError as e:
        connection.rollback()
        print("sql语法执行失败：" + str(e))
    cursor.close()


def get_index_dict(cursor: Cursor):
    """
    获取数据库对应表中的字段名
    :param cursor: 游标
    :type cursor: object
    :return: index_dict 字段名集合
    :rtype: list
    """
    index_dict = dict()
    index = 0
    for desc in cursor.description:
        index_dict[desc[0]] = index
        index = index + 1
    return index_dict


# ##########################################################  工具类  ###################################################
# def get_config(config_key: str):
#     """
#     通过json文件存储数据，在debugtalk中添加以下函数，yml文件中调用  ${ENV(name)} ，即可获取name对应的值
#     :param config_key: 变量名
#     :type config_key: str
#     :return: 变量值
#     :rtype: str
#     """
#     config_file_path = PathUtil.rootPath + "tests/config"
#     with open(config_file_path, 'r', encoding="utf-8") as f:
#         config_dict = json.load(f)
#     return config_dict[config_key]


class PathUtil(object):
    """
    路径工具类
    """

    def __init__(self):
        # 判断调试模式
        debug_vars = dict((a, b) for a, b in os.environ.items() if a.find('IPYTHONENABLE') >= 0)
        # 根据不同场景获取根目录
        if len(debug_vars) > 0:
            """当前为debug运行时"""
            print("debug执行")
            self.rootPath = sys.path[2]
        elif getattr(sys, 'frozen', False):
            """当前为exe运行时"""
            print("exe执行")
            self.rootPath = os.getcwd()
        else:
            """正常执行"""
            print("正常执行")
            self.rootPath = sys.path[1]
        # 替换斜杠
        self.rootPath = self.rootPath.replace("\\", "/") + "/"


PathUtil = PathUtil()

if __name__ == '__main__':
    # get_process_by_uuid("a9be5258a066f6aaba5f860dfc5d8d33")
    # get_actor_by_process_id(153549)
    # get_process_initiator(153549)
    # process = get_process("a9be5258a066f6aaba5f860dfc5d8d33")
    # get_process_complete("a9be5258a066f6aaba5f860dfc5d8d33")
    get_message_content("ce26952952791e58c00ab9ba3577b5d2", "SIGN_INVITE", 1, 1)
    # pass
