# 物理用印支持密码及动态密码验证-测试用例

## 功能测试

### 动态密码管理

#### P1TL-设备在线时点击动态密码按钮获取动态密码验证

##### PD-前置条件：1、用户已登录系统；2、设备处于在线状态；3、具有用印权限；

##### 操作步骤：1、进入云玺章桶管理页面；2、选择在线设备；3、点击【动态密码】按钮；4、等待系统响应；5、查看动态密码显示结果；

##### ER-预期结果：1、系统成功向设备发送获取动态密码指令；2、动态密码成功获取并显示在界面上；3、动态密码格式正确且可读；4、获取过程无异常提示；

#### P1TL-设备离线时点击动态密码按钮错误提示验证

##### PD-前置条件：1、用户已登录系统；2、设备处于离线状态；3、具有用印权限；

##### 操作步骤：1、进入云玺章桶管理页面；2、选择离线设备；3、点击【动态密码】按钮；4、查看系统响应结果；

##### ER-预期结果：1、系统检测到设备离线状态；2、显示错误提示"获取动态密码失败：设备不在线"；3、动态密码获取失败；4、用户能够清楚了解失败原因；

#### P1TL-动态密码按钮状态根据设备在线状态变化验证

##### PD-前置条件：1、用户已登录系统；2、具有用印权限；3、设备状态可切换；

##### 操作步骤：1、进入云玺章桶管理页面；2、观察设备在线时动态密码按钮状态；3、将设备切换为离线状态；4、观察动态密码按钮状态变化；5、重新将设备切换为在线状态；6、再次观察按钮状态；

##### ER-预期结果：1、设备在线时动态密码按钮可点击；2、设备离线时按钮状态相应变化（禁用或提示）；3、设备状态变化时按钮状态实时更新；4、按钮状态变化符合用户体验预期；

### 应急用印记录管理

#### P1TL-使用密码进行应急用印后记录显示验证

##### PD-前置条件：1、系统支持密码核验方式；2、具有应急用印权限；3、印章设备正常；

##### 操作步骤：1、使用密码方式进行应急用印操作；2、完成用印流程；3、进入应急用印记录列表；4、查看新增的用印记录；5、检查记录中的用印人字段；6、检查用户核验方式字段；

##### ER-预期结果：1、应急用印操作成功完成；2、用印记录成功生成；3、用印人字段显示为空；4、用户核验方式字段显示"密码核验"；5、记录按合并规则正确显示；

#### P1TL-使用动态密码进行应急用印后记录显示验证

##### PD-前置条件：1、设备在线状态；2、动态密码获取成功；3、具有应急用印权限；

##### 操作步骤：1、获取动态密码；2、使用动态密码进行应急用印操作；3、完成用印流程；4、进入应急用印记录列表；5、查看新增的用印记录；6、检查记录详情；

##### ER-预期结果：1、应急用印操作成功完成；2、用印记录成功生成；3、用印人字段显示为空；4、用户核验方式字段显示"密码核验"；5、记录与密码核验记录合并显示；

#### P1TL-应急用印记录合并规则验证

##### PD-前置条件：1、存在多条相同条件的应急用印记录；2、记录包含不同核验方式；

##### 操作步骤：1、进入应急用印记录列表；2、查看记录合并显示情况；3、检查合并规则是否包含用户核验方式；4、验证相同用印人、印章、设备、日期、核验方式的记录是否正确合并；

##### ER-预期结果：1、记录按用印人、印章、设备、日期、核验方式进行合并；2、相同条件的记录合并为一条显示；3、用印次数统计准确；4、不同核验方式的记录分别显示；

### 用印详情管理

#### P1TL-密码核验用印详情显示验证

##### PD-前置条件：1、存在密码核验的用印记录；2、具有查看详情权限；

##### 操作步骤：1、进入应急用印记录列表；2、选择密码核验的用印记录；3、点击查看详情；4、检查用印详情页面信息；5、重点查看用印人和用户核验方式字段；

##### ER-预期结果：1、用印详情页面正常打开；2、用印人字段显示"-"；3、用户核验方式字段显示"密码核验"；4、其他详情信息显示正确；5、页面布局和字段显示符合设计要求；

## 边界测试

### 设备状态边界

#### P1TL-设备在线离线状态快速切换时动态密码功能验证

##### PD-前置条件：1、设备状态可快速切换；2、用户正在操作动态密码功能；

##### 操作步骤：1、设备在线时点击动态密码按钮；2、在获取过程中将设备切换为离线；3、观察系统处理结果；4、重新将设备切换为在线；5、再次尝试获取动态密码；

##### ER-预期结果：1、状态切换时系统能正确识别；2、获取过程中断时给出合适提示；3、设备重新在线后功能恢复正常；4、不会出现系统异常或数据错误；

### 数据边界

#### P1TL-历史数据更新边界验证

##### PD-前置条件：1、系统中存在大量历史应急用印数据；2、具有数据更新权限；

##### 操作步骤：1、执行历史数据更新操作；2、检查更新范围和数量；3、验证更新后的数据准确性；4、检查更新过程中的系统性能；

##### ER-预期结果：1、所有存量应急用印记录成功更新；2、用户核验方式统一更新为"密码核验"；3、更新过程不影响系统正常运行；4、数据完整性保持不变；

## 异常测试

### 设备异常

#### P1TL-设备通信异常时动态密码获取处理验证

##### PD-前置条件：1、设备显示在线但通信异常；2、用户尝试获取动态密码；

##### 操作步骤：1、模拟设备通信异常情况；2、点击动态密码按钮；3、等待系统响应；4、观察错误处理机制；

##### ER-预期结果：1、系统检测到通信异常；2、显示相应的错误提示信息；3、不会导致系统崩溃或长时间无响应；4、用户能够理解异常原因并采取相应措施；

### 系统异常

#### P1TL-用印记录服务异常时数据处理验证

##### PD-前置条件：1、用印记录服务出现异常；2、用户进行应急用印操作；

##### 操作步骤：1、模拟用印记录服务异常；2、执行应急用印操作；3、观察系统处理结果；4、检查数据一致性；

##### ER-预期结果：1、系统能够检测到服务异常；2、给出合适的错误提示；3、不会丢失用印操作数据；4、服务恢复后能够正常处理积压数据；

## 性能测试

### 响应时间测试

#### P1TL-动态密码获取响应时间验证

##### PD-前置条件：1、设备在线且通信正常；2、网络环境稳定；

##### 操作步骤：1、记录开始时间；2、点击动态密码按钮；3、等待动态密码显示；4、记录结束时间；5、计算响应时间；6、重复测试多次；

##### ER-预期结果：1、动态密码获取响应时间在3秒以内；2、响应时间稳定，波动不超过1秒；3、用户体验良好；4、符合性能要求标准；

### 并发性能测试

#### P1TL-多用户同时获取动态密码性能验证

##### PD-前置条件：1、多个用户账号；2、多台设备在线；3、系统负载正常；

##### 操作步骤：1、模拟多用户同时登录；2、同时点击动态密码按钮；3、观察系统响应情况；4、检查每个用户的获取结果；5、监控系统资源使用情况；

##### ER-预期结果：1、所有用户都能成功获取动态密码；2、系统响应时间不会显著增加；3、不会出现系统卡顿或崩溃；4、资源使用在合理范围内；

## 安全测试

### 密码安全测试

#### P1TL-动态密码安全性验证

##### PD-前置条件：1、动态密码功能正常；2、具有安全测试环境；

##### 操作步骤：1、获取多个动态密码；2、分析密码生成规律；3、检查密码复杂度；4、验证密码有效期；5、测试密码重复使用；

##### ER-预期结果：1、动态密码具有足够的随机性；2、密码复杂度符合安全要求；3、密码有合理的有效期限制；4、不能重复使用相同密码；5、符合安全规范要求；

### 权限验证测试

#### P1TL-无用印权限用户动态密码功能访问控制验证

##### PD-前置条件：1、创建无用印权限的测试用户；2、系统权限控制正常；

##### 操作步骤：1、使用无权限用户登录；2、尝试访问云玺章桶页面；3、尝试点击动态密码按钮；4、观察系统权限控制效果；

##### ER-预期结果：1、无权限用户无法访问相关功能；2、系统显示权限不足提示；3、不会泄露敏感信息；4、权限控制机制有效；

## 兼容性测试

### 多端兼容性

#### P1TL-PC端动态密码功能兼容性验证

##### PD-前置条件：1、不同浏览器环境；2、不同操作系统；

##### 操作步骤：1、在Chrome浏览器中测试动态密码功能；2、在Firefox浏览器中测试；3、在Edge浏览器中测试；4、在不同操作系统上测试；5、对比功能表现；

##### ER-预期结果：1、所有主流浏览器都能正常使用功能；2、界面显示一致；3、功能操作流程相同；4、性能表现稳定；

#### P1TL-移动端动态密码功能兼容性验证

##### PD-前置条件：1、移动端访问环境；2、不同设备型号；

##### 操作步骤：1、使用手机浏览器访问系统；2、测试动态密码功能；3、检查界面适配情况；4、验证操作便捷性；5、测试不同屏幕尺寸的适配；

##### ER-预期结果：1、移动端能够正常使用动态密码功能；2、界面适配良好；3、操作体验符合移动端习惯；4、功能完整性不受影响；

## 数据迁移测试

### 历史数据处理

#### P1TL-历史数据批量更新核验方式验证

##### PD-前置条件：1、系统中存在大量历史应急用印数据；2、历史数据核验方式为指纹核验；3、具有数据更新权限；

##### 操作步骤：1、统计更新前历史数据总量；2、执行批量数据更新操作；3、监控更新进度；4、验证更新后数据准确性；5、检查数据完整性；

##### ER-预期结果：1、所有历史应急用印记录成功更新；2、用户核验方式统一更新为"密码核验"；3、数据总量保持不变；4、更新过程无数据丢失；5、更新操作可回滚；

#### P1TL-数据更新过程中系统稳定性验证

##### PD-前置条件：1、大量历史数据待更新；2、系统正常运行中；3、有其他用户在线操作；

##### 操作步骤：1、启动历史数据更新任务；2、同时进行正常业务操作；3、监控系统性能指标；4、观察用户操作是否受影响；5、检查更新任务执行情况；

##### ER-预期结果：1、数据更新不影响系统正常运行；2、在线用户操作不受影响；3、系统响应时间保持正常；4、更新任务按预期完成；5、系统资源使用合理；

## 批量操作测试

### 批量动态密码获取

#### P1TL-多设备批量获取动态密码验证

##### PD-前置条件：1、多台设备处于在线状态；2、用户具有多设备操作权限；

##### 操作步骤：1、选择多台在线设备；2、批量点击动态密码按钮；3、观察各设备响应情况；4、检查动态密码获取结果；5、验证系统处理能力；

##### ER-预期结果：1、所有在线设备都能成功获取动态密码；2、批量操作不会导致系统卡顿；3、各设备动态密码独立生成；4、操作响应时间在可接受范围内；

### 批量记录处理

#### P1TL-批量应急用印记录查询性能验证

##### PD-前置条件：1、系统中存在大量应急用印记录；2、记录包含不同核验方式；

##### 操作步骤：1、设置较大的查询时间范围；2、执行批量记录查询；3、观察查询响应时间；4、检查查询结果准确性；5、验证分页功能；

##### ER-预期结果：1、批量查询能够正常执行；2、查询响应时间在10秒以内；3、查询结果准确完整；4、分页功能正常工作；5、系统性能稳定；

## 冒烟测试用例

### 核心功能验证

#### MYTL-设备在线时动态密码基本功能验证

##### PD-前置条件：1、用户已登录系统；2、设备处于在线状态；

##### 操作步骤：1、进入云玺章桶管理页面；2、点击【动态密码】按钮；3、查看动态密码获取结果；

##### ER-预期结果：1、动态密码成功获取并显示；2、获取过程无异常；3、密码格式正确；

#### MYTL-设备离线时动态密码错误提示验证

##### PD-前置条件：1、用户已登录系统；2、设备处于离线状态；

##### 操作步骤：1、进入云玺章桶管理页面；2、点击【动态密码】按钮；3、查看系统响应；

##### ER-预期结果：1、显示错误提示"获取动态密码失败：设备不在线"；2、动态密码获取失败；

#### MYTL-密码核验应急用印记录显示验证

##### PD-前置条件：1、系统支持密码核验；2、具有应急用印权限；

##### 操作步骤：1、使用密码进行应急用印；2、查看应急用印记录列表；3、检查记录显示内容；

##### ER-预期结果：1、用印记录成功生成；2、用印人显示为空；3、用户核验方式显示"密码核验"；

#### MYTL-用印详情密码核验信息显示验证

##### PD-前置条件：1、存在密码核验的用印记录；

##### 操作步骤：1、进入应急用印记录列表；2、点击查看用印详情；3、检查详情页面信息；

##### ER-预期结果：1、用印人显示"-"；2、用户核验方式显示"密码核验"；3、其他信息显示正确；

#### MYTL-历史数据核验方式更新验证

##### PD-前置条件：1、系统中存在历史应急用印数据；

##### 操作步骤：1、查看历史数据更新前状态；2、执行数据更新操作；3、检查更新后数据状态；

##### ER-预期结果：1、历史数据成功更新；2、用户核验方式统一更新为"密码核验"；3、数据完整性保持；

#### MYTL-应急用印记录合并规则基本验证

##### PD-前置条件：1、存在相同条件的多条用印记录；

##### 操作步骤：1、查看应急用印记录列表；2、检查记录合并显示情况；

##### ER-预期结果：1、相同条件记录正确合并；2、合并规则包含核验方式；3、用印次数统计准确；

#### MYTL-动态密码响应时间基本验证

##### PD-前置条件：1、设备在线且通信正常；

##### 操作步骤：1、点击动态密码按钮；2、记录响应时间；3、验证用户体验；

##### ER-预期结果：1、响应时间在3秒以内；2、用户体验良好；3、功能操作流畅；

#### MYTL-权限控制基本验证

##### PD-前置条件：1、无用印权限的测试用户；

##### 操作步骤：1、使用无权限用户登录；2、尝试访问动态密码功能；

##### ER-预期结果：1、无法访问相关功能；2、显示权限不足提示；3、权限控制有效；

## 线上验证用例

### 核心业务流程验证

#### PATL-动态密码完整业务流程验证

##### PD-前置条件：1、生产环境设备在线；2、用户具有用印权限；

##### 步骤一：登录系统并进入云玺章桶管理页面

##### 步骤二：选择在线设备并点击【动态密码】按钮

##### 步骤三：使用获取的动态密码完成应急用印操作

##### 步骤四：查看应急用印记录列表和详情

##### ER-预期结果1：动态密码成功获取并可正常使用

##### 2：应急用印操作成功完成

##### 3：用印记录正确生成且信息准确显示

#### PATL-设备离线异常处理验证

##### PD-前置条件：1、生产环境中存在离线设备；

##### 步骤一：进入云玺章桶管理页面

##### 步骤二：选择离线设备并尝试获取动态密码

##### 步骤三：观察系统错误提示和处理机制

##### ER-预期结果1：系统正确识别设备离线状态

##### 2：显示准确的错误提示信息

##### 3：不影响其他正常功能使用

#### PATL-应急用印记录管理完整验证

##### PD-前置条件：1、生产环境中存在各种核验方式的用印记录；

##### 步骤一：查看应急用印记录列表

##### 步骤二：验证记录合并规则和显示效果

##### 步骤三：查看不同核验方式的用印详情

##### 步骤四：检查历史数据更新情况

##### ER-预期结果1：记录列表按合并规则正确显示

##### 2：用印详情信息准确完整

##### 3：历史数据更新正确无误

#### PATL-系统性能和稳定性验证

##### PD-前置条件：1、生产环境正常运行；2、有一定的用户访问量；

##### 步骤一：在业务高峰期测试动态密码功能

##### 步骤二：执行批量用印记录查询操作

##### 步骤三：监控系统响应时间和稳定性

##### ER-预期结果1：功能在高负载下正常工作

##### 2：响应时间符合性能要求

##### 3：系统运行稳定无异常

#### PATL-多端兼容性线上验证

##### PD-前置条件：1、不同终端设备和浏览器环境；

##### 步骤一：使用PC端主流浏览器测试功能

##### 步骤二：使用移动端浏览器测试功能

##### 步骤三：对比不同环境下的功能表现

##### ER-预期结果1：所有环境下功能正常可用

##### 2：界面显示和交互体验一致

##### 3：功能完整性不受环境影响

#### PATL-安全性线上验证

##### PD-前置条件：1、生产环境安全配置正常；

##### 步骤一：测试动态密码的安全性特征

##### 步骤二：验证权限控制机制有效性

##### 步骤三：检查敏感信息保护情况

##### ER-预期结果1：动态密码具有足够安全性

##### 2：权限控制严格有效

##### 3：敏感信息得到妥善保护
