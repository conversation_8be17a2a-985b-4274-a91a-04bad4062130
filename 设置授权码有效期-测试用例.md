# 设置授权码有效期-测试用例

## 功能测试

### 授权码有效期设置

#### TL-业务模板新增授权码有效期开关默认状态验证

##### PD-前置条件：用户已登录；具有业务模板配置权限；物理用印业务模板存在；

##### 操作步骤：1、进入物理用印业务模板配置页面；2、查看用印方式-用印流程配置区域；3、检查授权码有效期参数显示状态；

##### ER-预期结果：1：授权码有效期开关显示且默认为关闭状态；2：开关下方显示参数说明"关闭时授权码永久有效，开启后若过期则失效，需重新获取授权码"；3：有效期输入框处于禁用状态；

#### TL-授权码有效期开关开启后输入框状态验证

##### PD-前置条件：用户已登录；具有业务模板配置权限；物理用印业务模板存在；授权码有效期开关为关闭状态；

##### 操作步骤：1、点击授权码有效期开关，将其开启；2、检查有效期输入框状态变化；3、检查输入框的提示信息和单位显示；

##### ER-预期结果：1：有效期输入框变为可编辑状态；2：输入框显示单位"分钟"；3：输入框显示范围提示"1-9999"；

#### TL-授权码有效期边界值输入验证

##### PD-前置条件：用户已登录；具有业务模板配置权限；授权码有效期开关已开启；

##### 操作步骤：1、在有效期输入框中输入最小值"1"，保存配置；2、修改有效期为最大值"9999"，保存配置；3、尝试输入超出范围的值"0"和"10000"；

##### ER-预期结果：1：输入"1"时保存成功；2：输入"9999"时保存成功；3：输入"0"时提示"有效期范围为1-9999分钟"；4：输入"10000"时提示"有效期范围为1-9999分钟"；

#### TL-授权码有效期非法输入验证

##### PD-前置条件：用户已登录；具有业务模板配置权限；授权码有效期开关已开启；

##### 操作步骤：1、在有效期输入框中输入小数"1.5"；2、输入负数"-1"；3、输入非数字字符"abc"；4、输入空值后尝试保存；

##### ER-预期结果：1：输入小数时提示"请输入整数"；2：输入负数时提示"有效期范围为1-9999分钟"；3：输入非数字时提示"请输入数字"；4：空值保存时提示"请输入有效期时长"；

### 授权码刷新功能

#### TL-用印中任务授权码和有效期显示验证

##### PD-前置条件：用户已登录；存在开启授权码有效期的物理用印流程；流程状态为用印中；存在用印中的用印任务；

##### 操作步骤：1、进入授权用印页面（PC端）；2、查看用印中任务的授权码信息显示；3、检查授权码有效期截止时间显示；

##### ER-预期结果：1：用印中任务显示6位数字授权码；2：显示授权码有效期截止时间，格式为"YYYY-MM-DD HH:mm:ss"；3：显示【刷新授权码】按钮；

#### TL-授权码主动刷新成功验证

##### PD-前置条件：用户已登录；存在开启授权码有效期的用印中流程；用印任务状态为用印中；距离上次刷新超过10秒；

##### 操作步骤：1、在授权用印页面点击【刷新授权码】按钮；2、等待刷新完成；3、检查授权码和有效期变化；

##### ER-预期结果：1：刷新操作成功执行；2：授权码更新为新的6位数字；3：授权码有效期截止时间更新为当前时间+设置的有效期时长；4：页面提示"授权码刷新成功"；

#### TL-授权码频繁刷新限制验证

##### PD-前置条件：用户已登录；存在开启授权码有效期的用印中流程；用印任务状态为用印中；

##### 操作步骤：1、点击【刷新授权码】按钮完成一次刷新；2、立即再次点击【刷新授权码】按钮（10秒内）；3、等待10秒后再次点击【刷新授权码】按钮；

##### ER-预期结果：1：第一次刷新成功；2：10秒内再次刷新时提示"请求过于频繁，请稍后刷新"；3：等待10秒后刷新成功；

#### TL-H5端授权码刷新功能验证

##### PD-前置条件：用户已登录；使用H5端访问；存在开启授权码有效期的用印中流程；

##### 操作步骤：1、在H5端进入授权用印页面；2、查看授权码和有效期显示；3、点击【刷新授权码】按钮；

##### ER-预期结果：1：H5端正确显示授权码和有效期；2：【刷新授权码】按钮正常显示；3：刷新功能正常工作；4：页面适配良好；

### 页面显示功能

#### TL-未开启授权码有效期流程页面显示验证

##### PD-前置条件：用户已登录；存在未开启授权码有效期的物理用印流程；流程状态为用印中；

##### 操作步骤：1、进入授权用印页面；2、查看授权码相关字段显示；3、检查刷新按钮显示状态；

##### ER-预期结果：1：不显示授权码有效期字段；2：不显示【刷新授权码】按钮；3：仅显示授权码信息；

#### TL-流程详情页面授权码有效期显示验证

##### PD-前置条件：用户已登录；存在开启授权码有效期的物理用印流程；

##### 操作步骤：1、进入流程详情页面；2、查看用印任务的授权码信息；3、检查有效期信息显示；

##### ER-预期结果：1：流程详情页面显示授权码；2：显示授权码有效期截止时间；3：信息显示格式正确；

#### TL-审批页面授权码有效期显示验证

##### PD-前置条件：用户已登录；具有审批权限；存在开启授权码有效期的物理用印流程；流程处于审批状态；

##### 操作步骤：1、进入审批页面；2、查看流程信息中的授权码相关信息；3、检查有效期信息显示完整性；

##### ER-预期结果：1：审批页面显示授权码信息；2：显示授权码有效期截止时间；3：信息展示清晰易读；

### OpenAPI接口测试

#### TL-查询物理用印流程详情接口返回授权码过期时间验证

##### PD-前置条件：接口调用权限正常；存在开启授权码有效期的物理用印流程；流程ID有效；

##### 操作步骤：1、调用/esign-docs/v1/sealcontrols/physicalSealFlowDetail接口；2、传入有效的流程ID参数；3、检查返回参数中的authCodeExpireTime字段；

##### ER-预期结果：1：接口调用成功，返回200状态码；2：返回参数包含authCodeExpireTime字段；3：authCodeExpireTime值为有效的时间戳格式；

#### TL-刷新授权码接口基本功能验证

##### PD-前置条件：接口调用权限正常；存在用印中的物理用印流程；sealFlowId有效；

##### 操作步骤：1、调用/esign-docs/v1/sealcontrols/physicalSealFlow/authCode/refresh接口；2、传入有效的sealFlowId参数；3、检查返回结果；

##### ER-预期结果：1：接口调用成功，返回200状态码；2：返回sealFlowId、businessNo、sealDetailsInfos字段；3：sealDetailsInfos包含taskId、authCode、authCodeExpireTime信息；

#### TL-刷新授权码接口参数校验-流程不存在

##### PD-前置条件：接口调用权限正常；

##### 操作步骤：1、调用刷新授权码接口；2、传入不存在的sealFlowId；3、检查接口返回结果；

##### ER-预期结果：1：接口返回错误状态码；2：错误信息为"物理用印流程不存在"；

#### TL-刷新授权码接口参数校验-流程状态非用印中

##### PD-前置条件：接口调用权限正常；存在非用印中状态的物理用印流程；

##### 操作步骤：1、调用刷新授权码接口；2、传入非用印中状态的流程ID；3、检查接口返回结果；

##### ER-预期结果：1：接口返回错误状态码；2：错误信息为"物理用印流程不是用印中状态"；

#### TL-刷新授权码接口参数校验-任务不存在

##### PD-前置条件：接口调用权限正常；存在用印中的物理用印流程；

##### 操作步骤：1、调用刷新授权码接口；2、传入有效流程ID和不存在的taskId；3、检查接口返回结果；

##### ER-预期结果：1：接口返回错误状态码；2：错误信息为"物理用印任务(任务id)不存在"；

#### TL-刷新授权码接口参数校验-任务状态非用印中

##### PD-前置条件：接口调用权限正常；存在物理用印流程；存在非用印中状态的任务；

##### 操作步骤：1、调用刷新授权码接口；2、传入有效流程ID和非用印中状态的taskId；3、检查接口返回结果；

##### ER-预期结果：1：接口返回错误状态码；2：错误信息为"物理用印任务(任务id)不是用印中状态"；

### 授权码开关控制

#### TL-用印中流程关闭授权码功能验证

##### PD-前置条件：用户已登录；具有管理权限；存在用印中的物理用印流程；授权码当前为启用状态；

##### 操作步骤：1、进入【我管理的】物理用印流程列表；2、找到用印中流程，点击【关闭授权码】按钮；3、确认关闭操作；4、检查按钮状态变化；

##### ER-预期结果：1：关闭操作执行成功；2：【关闭授权码】按钮变为【启用授权码】按钮；3：页面提示"授权码已关闭"；

#### TL-授权码关闭后页面显示状态验证

##### PD-前置条件：用户已登录；存在已关闭授权码的用印中流程；

##### 操作步骤：1、进入授权用印页面；2、查看授权码显示状态；3、进入用印详情表单页面检查；4、进入审批页面检查；

##### ER-预期结果：1：授权用印页面授权码显示"未启用"；2：用印详情表单授权码显示"未启用"；3：审批页面授权码显示"未启用"；

#### TL-授权码关闭后接口返回值验证

##### PD-前置条件：接口调用权限正常；存在已关闭授权码的物理用印流程；

##### 操作步骤：1、调用查询物理用印流程详情接口；2、检查返回的授权码相关字段；3、调用获取授权码接口；

##### ER-预期结果：1：流程详情接口返回授权码为空；2：流程详情接口返回授权有效期为空；3：获取授权码接口提示"流程已关闭授权码"；

#### TL-授权码启用后新授权码生成验证

##### PD-前置条件：用户已登录；具有管理权限；存在已关闭授权码的用印中流程；

##### 操作步骤：1、点击【启用授权码】按钮；2、确认启用操作；3、进入授权用印页面查看；4、检查用印详情页面；

##### ER-预期结果：1：启用操作执行成功；2：【启用授权码】按钮变为【关闭授权码】按钮；3：授权用印页面显示新的授权码；4：用印详情页面显示新的授权码；

### 权限管理

#### TL-前台角色授权码管理按钮权限默认状态验证

##### PD-前置条件：系统管理员已登录；存在前台角色配置；

##### 操作步骤：1、进入角色权限配置页面；2、查看【物理用印】-【我管理的】菜单权限配置；3、查看【物理用印】-【我参与的】菜单权限配置；

##### ER-预期结果：1：【我管理的】菜单下【关闭授权码】按钮权限默认不勾选；2：【我参与的】菜单下【关闭授权码】按钮权限默认不勾选；3：需要手动勾选才能获得权限；

#### TL-无权限用户授权码管理按钮显示验证

##### PD-前置条件：用户已登录；用户角色未配置授权码管理权限；存在用印中流程；

##### 操作步骤：1、进入【我管理的】物理用印流程列表；2、查看流程操作按钮；3、进入【我参与的】物理用印流程列表检查；

##### ER-预期结果：1：【我管理的】列表中不显示【关闭授权码】/【启用授权码】按钮；2：【我参与的】列表中不显示【关闭授权码】/【启用授权码】按钮；

## 性能测试

### 并发刷新测试

#### TL-多用户同时刷新授权码性能验证

##### PD-前置条件：存在开启授权码有效期的用印中流程；准备10个并发用户；

##### 操作步骤：1、10个用户同时登录系统；2、同时进入授权用印页面；3、同时点击【刷新授权码】按钮；

##### ER-预期结果：1：所有用户刷新操作均在3秒内完成；2：每个用户获得不同的授权码；3：系统响应稳定，无异常；

#### TL-大量任务授权码刷新性能验证

##### PD-前置条件：存在包含50个用印中任务的物理用印流程；开启授权码有效期；

##### 操作步骤：1、调用刷新授权码接口；2、不传taskIdList参数，刷新所有任务授权码；3、记录响应时间；

##### ER-预期结果：1：接口响应时间不超过5秒；2：所有50个任务授权码均成功刷新；3：返回数据完整准确；

## 安全测试

### 接口安全测试

#### TL-刷新授权码接口权限验证

##### PD-前置条件：准备无接口调用权限的用户token；

##### 操作步骤：1、使用无权限token调用刷新授权码接口；2、检查接口返回结果；

##### ER-预期结果：1：接口返回401或403状态码；2：返回权限不足错误信息；3：不执行授权码刷新操作；

#### TL-授权码数据传输安全验证

##### PD-前置条件：开启网络抓包工具；存在用印中流程；

##### 操作步骤：1、执行授权码刷新操作；2、抓取网络请求数据；3、检查数据传输方式；

##### ER-预期结果：1：所有授权码相关数据通过HTTPS传输；2：敏感数据在传输过程中加密；3：无明文传输授权码信息；

## 兼容性测试

### 浏览器兼容性

#### TL-Chrome浏览器授权码功能兼容性验证

##### PD-前置条件：使用Chrome浏览器；用户已登录；存在开启授权码有效期的流程；

##### 操作步骤：1、在Chrome浏览器中进入授权用印页面；2、执行授权码刷新操作；3、测试授权码开关控制功能；

##### ER-预期结果：1：页面显示正常，无样式错乱；2：授权码刷新功能正常；3：开关控制功能正常；

#### TL-Firefox浏览器授权码功能兼容性验证

##### PD-前置条件：使用Firefox浏览器；用户已登录；存在开启授权码有效期的流程；

##### 操作步骤：1、在Firefox浏览器中进入授权用印页面；2、执行授权码刷新操作；3、测试授权码开关控制功能；

##### ER-预期结果：1：页面显示正常，无样式错乱；2：授权码刷新功能正常；3：开关控制功能正常；

### 移动端兼容性

#### TL-移动端H5授权码功能适配验证

##### PD-前置条件：使用移动设备访问H5页面；用户已登录；存在开启授权码有效期的流程；

##### 操作步骤：1、在移动端进入授权用印页面；2、查看授权码和有效期显示效果；3、测试刷新授权码按钮点击；

##### ER-预期结果：1：页面在移动端显示适配良好；2：授权码和有效期信息清晰可见；3：刷新按钮大小适中，易于点击；

## 业务异常测试

### 授权码过期处理

#### TL-授权码过期后用印操作验证

##### PD-前置条件：存在已过期的授权码；用印任务状态为用印中；

##### 操作步骤：1、使用过期的授权码尝试进行用印操作；2、检查系统响应；3、查看错误提示信息；

##### ER-预期结果：1：用印操作被拒绝；2：系统提示"授权码已过期，请重新获取"；3：引导用户刷新授权码；

#### TL-授权码即将过期提醒验证

##### PD-前置条件：存在即将过期的授权码（剩余时间少于5分钟）；

##### 操作步骤：1、进入授权用印页面；2、查看授权码有效期显示；3、检查是否有过期提醒；

##### ER-预期结果：1：授权码有效期以红色或警告色显示；2：显示"即将过期"提醒信息；3：建议用户及时刷新授权码；

### 状态冲突处理

#### TL-流程状态变更时授权码处理验证

##### PD-前置条件：存在用印中的流程；流程包含有效授权码；

##### 操作步骤：1、将流程状态从用印中变更为其他状态（如已完成）；2、尝试使用原有授权码；3、尝试刷新授权码；

##### ER-预期结果：1：原有授权码自动失效；2：使用失效授权码时提示"流程状态已变更，授权码失效"；3：无法刷新非用印中流程的授权码；

#### TL-任务状态变更时授权码处理验证

##### PD-前置条件：存在用印中的任务；任务包含有效授权码；

##### 操作步骤：1、将任务状态从用印中变更为已用印；2、检查授权码状态；3、尝试刷新该任务授权码；

##### ER-预期结果：1：已用印任务的授权码自动失效；2：刷新授权码时不返回已用印任务信息；3：页面不显示已用印任务的授权码；

## 系统异常测试

### 服务异常处理

#### TL-授权码服务异常时的降级处理验证

##### PD-前置条件：模拟授权码生成服务异常；存在用印中流程；

##### 操作步骤：1、尝试刷新授权码；2、检查系统响应；3、查看错误处理机制；

##### ER-预期结果：1：系统提示"授权码服务暂时不可用，请稍后重试"；2：不影响其他功能正常使用；3：服务恢复后功能自动恢复；

#### TL-数据库异常时授权码功能处理验证

##### PD-前置条件：模拟数据库连接异常；

##### 操作步骤：1、尝试查询授权码信息；2、尝试刷新授权码；3、检查系统稳定性；

##### ER-预期结果：1：系统提示"系统繁忙，请稍后重试"；2：不出现系统崩溃或白屏；3：其他不依赖数据库的功能正常；

### 网络异常处理

#### TL-网络超时时授权码刷新处理验证

##### PD-前置条件：模拟网络超时环境；存在用印中流程；

##### 操作步骤：1、点击刷新授权码按钮；2、等待网络超时；3、检查页面响应；

##### ER-预期结果：1：页面显示加载状态；2：超时后提示"网络超时，请检查网络连接后重试"；3：用户可以重新尝试刷新操作；

#### TL-网络断开时授权码功能处理验证

##### PD-前置条件：模拟网络断开环境；

##### 操作步骤：1、尝试进入授权用印页面；2、尝试刷新授权码；3、检查离线提示；

##### ER-预期结果：1：页面提示"网络连接已断开"；2：禁用刷新授权码等网络相关功能；3：网络恢复后功能自动可用；

## 冒烟测试用例

### 核心功能冒烟测试

#### MYTL-业务模板授权码有效期基本配置验证

##### PD-前置条件：用户已登录；具有业务模板配置权限；

##### 操作步骤：1、进入物理用印业务模板配置页面；2、开启授权码有效期开关；3、设置有效期为60分钟并保存；

##### ER-预期结果：1：开关开启成功；2：有效期设置保存成功；3：配置生效；

#### MYTL-授权码刷新基本功能验证

##### PD-前置条件：存在开启授权码有效期的用印中流程；

##### 操作步骤：1、进入授权用印页面；2、查看授权码和有效期显示；3、点击刷新授权码按钮；

##### ER-预期结果：1：显示授权码和有效期；2：刷新成功；3：授权码更新；

#### MYTL-授权码开关控制基本功能验证

##### PD-前置条件：用户已登录；具有管理权限；存在用印中流程；

##### 操作步骤：1、进入我管理的流程列表；2、点击关闭授权码按钮；3、检查授权用印页面显示；

##### ER-预期结果：1：关闭操作成功；2：按钮变为启用授权码；3：页面显示未启用；

#### MYTL-刷新授权码接口基本调用验证

##### PD-前置条件：接口调用权限正常；存在用印中流程；

##### 操作步骤：1、调用刷新授权码接口；2、传入有效流程ID；3、检查返回结果；

##### ER-预期结果：1：接口调用成功；2：返回新授权码；3：返回有效期信息；

#### MYTL-查询流程详情接口授权码信息验证

##### PD-前置条件：接口调用权限正常；存在开启授权码有效期的流程；

##### 操作步骤：1、调用查询流程详情接口；2、检查返回的授权码过期时间字段；

##### ER-预期结果：1：接口调用成功；2：返回authCodeExpireTime字段；3：时间格式正确；

#### MYTL-权限控制基本验证

##### PD-前置条件：系统管理员已登录；存在前台角色；

##### 操作步骤：1、查看角色权限配置；2、检查授权码管理按钮权限默认状态；

##### ER-预期结果：1：权限配置页面正常；2：授权码管理按钮默认不勾选；3：需手动勾选获得权限；

## 线上验证用例

### 核心业务流程验证

#### PATL-完整授权码有效期业务流程验证

##### PD-前置条件：生产环境；用户已登录；具有完整权限；

##### 操作步骤：1、创建开启授权码有效期的物理用印流程；2、流程进入用印中状态后查看授权码；3、执行授权码刷新操作；4、测试授权码关闭和启用功能；

##### ER-预期结果：1：流程创建成功；2：授权码正常显示和刷新；3：开关控制功能正常；4：整个业务流程闭环正常；

#### PATL-授权码有效期到期处理流程验证

##### PD-前置条件：生产环境；存在即将过期的授权码；

##### 操作步骤：1、等待授权码过期；2、尝试使用过期授权码进行用印；3、刷新获取新授权码；4、使用新授权码完成用印；

##### ER-预期结果：1：过期授权码被拒绝；2：提示信息准确；3：新授权码获取成功；4：用印操作正常完成；

#### PATL-多端授权码功能一致性验证

##### PD-前置条件：生产环境；同一用户账号；PC和H5环境；

##### 操作步骤：1、PC端创建开启授权码有效期的流程；2、H5端查看授权码信息；3、H5端执行刷新操作；4、PC端验证刷新结果；

##### ER-预期结果：1：PC和H5显示信息一致；2：H5刷新功能正常；3：PC端能看到H5的刷新结果；4：多端数据同步正常；

#### PATL-OpenAPI接口生产环境验证

##### PD-前置条件：生产环境；API调用权限；真实业务数据；

##### 操作步骤：1、调用查询流程详情接口；2、调用刷新授权码接口；3、验证接口返回数据准确性；4、测试接口异常处理；

##### ER-预期结果：1：接口响应时间正常；2：返回数据准确完整；3：异常处理机制有效；4：接口稳定性良好；

#### PATL-权限控制生产环境验证

##### PD-前置条件：生产环境；不同权限级别用户；

##### 操作步骤：1、管理员用户测试授权码管理功能；2、普通用户测试功能访问限制；3、验证权限配置生效情况；

##### ER-预期结果：1：管理员功能正常；2：普通用户权限限制有效；3：权限配置按预期生效；4：安全控制机制正常；

#### PATL-系统性能和稳定性验证

##### PD-前置条件：生产环境；正常业务负载；

##### 操作步骤：1、在业务高峰期测试授权码功能；2、执行批量授权码刷新操作；3、监控系统响应时间和资源使用；

##### ER-预期结果：1：高峰期功能响应正常；2：批量操作性能稳定；3：系统资源使用合理；4：无异常错误或崩溃；
