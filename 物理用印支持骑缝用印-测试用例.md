# 物理用印支持骑缝用印-测试用例

## 功能测试

### 骑缝用印申请功能

#### P1TL-安装云玺应用时骑缝用印次数字段显示验证

##### PD-前置条件：1、用户已登录系统；2、物理印控应用已安装云玺应用；3、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、填写用印方信息；3、查看用印次数设置区域；4、检查普通用印次数和骑缝用印次数字段显示；5、分别设置普通用印次数和骑缝用印次数；

##### ER-预期结果：1、页面显示普通用印次数字段；2、页面显示骑缝用印次数字段；3、两个字段都可以正常输入和设置；4、字段标签和说明清晰易懂；

#### P1TL-未安装云玺应用时骑缝用印次数字段隐藏验证

##### PD-前置条件：1、用户已登录系统；2、物理印控应用未安装云玺应用；3、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、填写用印方信息；3、查看用印次数设置区域；4、检查字段显示情况；

##### ER-预期结果：1、页面显示普通用印次数字段；2、页面隐藏骑缝用印次数字段；3、只能设置普通用印次数；4、界面布局合理美观；

#### P1TL-骑缝用印次数有效范围验证

##### PD-前置条件：1、用户已登录系统；2、物理印控应用已安装云玺应用；3、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、在骑缝用印次数字段输入0；3、尝试提交；4、输入1000；5、尝试提交；6、输入有效范围内的数值；7、提交申请；

##### ER-预期结果：1、输入0时显示校验提示"用印次数为1～999之间的整数"；2、输入1000时显示相同校验提示；3、输入有效范围数值时校验通过；4、申请成功提交；

#### P1TL-普通用印和骑缝用印总次数必须大于0验证

##### PD-前置条件：1、用户已登录系统；2、物理印控应用已安装云玺应用；3、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、设置普通用印次数为0；3、设置骑缝用印次数为0；4、尝试提交申请；5、设置普通用印次数为2，骑缝用印次数为3；6、提交申请；

##### ER-预期结果：1、两个次数都为0时提交失败；2、显示"普通用印+骑缝用印总次数必须大于0"提示；3、设置有效次数后提交成功；4、申请流程正常创建；

### 骑缝用印详情显示

#### P1TL-安装云玺应用时详情页面骑缝次数显示验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、存在包含骑缝用印次数的申请；3、具有查看权限；

##### 操作步骤：1、进入物理用印详情页面；2、查看用印次数信息显示；3、检查普通用印次数和骑缝用印次数字段；4、验证数据准确性；

##### ER-预期结果：1、详情页面显示普通用印次数；2、详情页面显示骑缝用印次数；3、显示的次数与申请时设置一致；4、字段标签清晰准确；

#### P1TL-未安装云玺应用时详情页面骑缝次数隐藏验证

##### PD-前置条件：1、物理印控应用未安装云玺应用；2、存在物理用印申请；3、具有查看权限；

##### 操作步骤：1、进入物理用印详情页面；2、查看用印次数信息显示；3、检查字段显示情况；

##### ER-预期结果：1、详情页面显示普通用印次数；2、详情页面隐藏骑缝用印次数；3、页面布局正常；4、信息显示完整；

#### P1TL-存量任务骑缝次数显示0次验证

##### PD-前置条件：1、系统中存在功能上线前的存量任务；2、物理印控应用已安装云玺应用；3、具有查看权限；

##### 操作步骤：1、查看存量物理用印任务详情；2、检查骑缝用印次数显示；3、对比新任务的显示情况；

##### ER-预期结果：1、存量任务骑缝用印次数显示为0；2、普通用印次数显示正常；3、新任务可以正常设置和显示骑缝次数；4、数据兼容性良好；

### 审批页面骑缝次数显示

#### P1TL-审批页面骑缝用印次数显示验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、存在待审批的骑缝用印申请；3、具有审批权限；

##### 操作步骤：1、进入待审批列表；2、选择包含骑缝用印的申请；3、查看审批页面详情；4、检查用印次数信息显示；

##### ER-预期结果：1、审批页面显示普通用印次数；2、审批页面显示骑缝用印次数；3、审批人能够清楚了解用印需求；4、信息显示准确完整；

### 授权用印页面骑缝次数显示

#### P1TL-授权用印页面骑缝用印次数显示验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、存在已审批的骑缝用印申请；3、具有授权用印权限；

##### 操作步骤：1、进入授权用印页面；2、查看用印申请详情；3、检查用印次数信息显示；4、验证信息准确性；

##### ER-预期结果：1、授权页面显示普通用印次数；2、授权页面显示骑缝用印次数；3、授权人能够了解具体用印要求；4、信息与申请时一致；

### 云玺设备骑缝用印执行

#### P1TL-云玺设备骑缝用印执行验证

##### PD-前置条件：1、云玺章桶设备在线；2、存在包含骑缝用印次数的已审批申请；3、具有用印执行权限；

##### 操作步骤：1、进入授权用印页面；2、选择包含骑缝用印的申请；3、执行用印操作；4、在设备上进行普通用印和骑缝用印；5、查看用印结果和次数统计；

##### ER-预期结果：1、系统调用云玺SDK传入普通和骑缝用印次数；2、设备支持骑缝用印操作；3、用印次数正确统计；4、用印操作成功完成；

#### P1TL-云玺设备总次数用完任务状态更新验证

##### PD-前置条件：1、云玺章桶设备在线；2、存在骑缝用印申请（如普通3次+骑缝2次）；3、具有用印执行权限；

##### 操作步骤：1、执行用印操作；2、使用完所有普通用印次数（3次）；3、使用完所有骑缝用印次数（2次）；4、查看任务状态；5、尝试继续用印；

##### ER-预期结果：1、总次数用完后任务自动更新为已用印；2、无法继续进行用印操作；3、次数统计准确；4、任务状态同步及时；

#### P1TL-云玺设备普通用印超次但总次数未用完验证

##### PD-前置条件：1、云玺章桶设备在线；2、存在骑缝用印申请（如普通2次+骑缝3次）；3、具有用印执行权限；

##### 操作步骤：1、执行用印操作；2、使用完所有普通用印次数（2次）；3、骑缝用印还有剩余次数（1次未用）；4、查看任务状态；

##### ER-预期结果：1、普通用印超次但总次数未用完时任务保持进行中；2、仍可继续使用剩余骑缝用印次数；3、次数统计准确；4、任务状态正确；

### 思格特设备骑缝用印执行

#### P1TL-思格特设备骑缝用印执行验证

##### PD-前置条件：1、思格特章桶设备在线；2、存在包含骑缝用印次数的已审批申请；3、具有用印执行权限；

##### 操作步骤：1、进入授权用印页面；2、选择包含骑缝用印的申请；3、执行用印操作；4、在思格特设备上进行用印；5、查看用印结果和次数统计；

##### ER-预期结果：1、系统调用思格特指令传入总次数（普通+骑缝）；2、设备支持用印操作；3、用印次数计入普通用印；4、用印操作成功完成；

#### P1TL-思格特设备普通用印次数可能超过申请次数验证

##### PD-前置条件：1、思格特章桶设备在线；2、存在骑缝用印申请（如普通2次+骑缝3次，总5次）；3、具有用印执行权限；

##### 操作步骤：1、执行用印操作；2、在思格特设备上连续用印5次；3、查看普通用印次数统计；4、查看总次数统计；5、验证任务状态；

##### ER-预期结果：1、所有用印次数都计入普通用印；2、普通用印实际次数（5次）超过申请次数（2次）；3、总次数用完后任务更新为已用印；4、次数统计逻辑正确；

## 边界测试

### 用印次数边界

#### P1TL-骑缝用印次数边界值验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、设置骑缝用印次数为1；2、提交申请；3、设置骑缝用印次数为999；4、提交申请；5、设置骑缝用印次数为0；6、尝试提交；7、设置骑缝用印次数为1000；8、尝试提交；

##### ER-预期结果：1、设置1次时申请成功；2、设置999次时申请成功；3、设置0次时提交失败并提示；4、设置1000次时提交失败并提示；

#### P1TL-普通用印和骑缝用印次数组合边界验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、设置普通用印1次，骑缝用印0次；2、提交申请；3、设置普通用印0次，骑缝用印1次；4、提交申请；5、设置普通用印999次，骑缝用印999次；6、提交申请；

##### ER-预期结果：1、普通1次+骑缝0次申请成功；2、普通0次+骑缝1次申请成功；3、普通999次+骑缝999次申请成功；4、总次数计算正确；

## 异常测试

### 应用安装异常

#### P1TL-云玺应用安装状态检测异常处理验证

##### PD-前置条件：1、模拟云玺应用安装状态检测异常；2、具有发起物理用印权限；

##### 操作步骤：1、模拟应用状态检测服务异常；2、进入发起物理用印页面；3、观察页面字段显示；4、尝试提交申请；

##### ER-预期结果：1、应用状态检测异常时有合理的默认处理；2、页面功能不会完全不可用；3、显示相应的错误提示；4、用户能够理解异常情况；

### 设备通信异常

#### P1TL-云玺设备通信异常时骑缝用印处理验证

##### PD-前置条件：1、云玺章桶设备通信异常；2、存在骑缝用印申请；3、具有用印执行权限；

##### 操作步骤：1、模拟云玺设备通信异常；2、尝试执行骑缝用印操作；3、观察系统错误处理；4、检查次数统计是否受影响；

##### ER-预期结果：1、系统检测到设备通信异常；2、显示相应的错误提示；3、用印操作被中断；4、次数统计保持准确；

## 性能测试

### 响应时间测试

#### P1TL-骑缝用印操作响应时间验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络环境稳定；3、存在骑缝用印申请；

##### 操作步骤：1、记录开始时间；2、执行骑缝用印操作；3、等待操作完成；4、记录结束时间；5、计算响应时间；6、重复测试多次；

##### ER-预期结果：1、骑缝用印响应时间与普通用印相当；2、响应时间稳定；3、用户体验良好；4、性能符合要求；

### 并发性能测试

#### P1TL-多用户同时进行骑缝用印性能验证

##### PD-前置条件：1、多台设备在线；2、多个骑缝用印申请；3、多个用户账号；

##### 操作步骤：1、模拟多用户同时登录；2、同时执行骑缝用印操作；3、观察系统响应情况；4、检查次数统计准确性；5、监控系统资源使用；

##### ER-预期结果：1、所有用户操作都能正常完成；2、系统响应时间不会显著增加；3、次数统计准确无误；4、系统性能稳定；

## 安全测试

### 权限验证测试

#### P1TL-骑缝用印权限控制验证

##### PD-前置条件：1、创建无骑缝用印权限的测试用户；2、存在骑缝用印申请；

##### 操作步骤：1、使用无权限用户登录；2、尝试发起包含骑缝用印的申请；3、尝试执行骑缝用印操作；4、观察权限控制效果；

##### ER-预期结果：1、无权限用户无法设置骑缝用印次数；2、无法执行骑缝用印操作；3、显示权限不足提示；4、权限控制机制有效；

### 数据安全测试

#### P1TL-骑缝用印次数数据安全性验证

##### PD-前置条件：1、存在骑缝用印申请和执行记录；2、具有数据查看权限；

##### 操作步骤：1、查看骑缝用印次数数据；2、验证数据传输安全性；3、检查数据存储安全性；4、测试数据访问控制；

##### ER-预期结果：1、骑缝用印次数数据传输加密；2、数据存储安全可靠；3、数据访问控制有效；4、敏感信息得到保护；

## 兼容性测试

### 设备兼容性

#### P1TL-云玺章桶与思格特章桶骑缝用印差异化处理验证

##### PD-前置条件：1、系统中同时存在云玺章桶和思格特章桶；2、存在骑缝用印申请；

##### 操作步骤：1、分别在两种设备上执行骑缝用印；2、对比用印处理方式；3、检查次数统计差异；4、验证设备兼容性；

##### ER-预期结果：1、云玺设备支持区分普通和骑缝用印；2、思格特设备将所有次数计入普通用印；3、次数统计逻辑符合设备特性；4、兼容性处理正确；

### API兼容性

#### P1TL-新增骑缝用印字段向后兼容性验证

##### PD-前置条件：1、存在使用旧版API的客户端；2、新版API已部署；

##### 操作步骤：1、使用旧版API调用创建物理用印接口；2、检查接口响应；3、使用新版API调用接口；4、对比接口行为；

##### ER-预期结果：1、旧版API调用仍然正常；2、新增字段有合理的默认值；3、向后兼容性良好；4、不影响现有功能；

## OpenAPI接口测试

### 创建物理用印流程接口

#### P1TL-创建物理用印流程接口新增骑缝用印次数字段验证

##### PD-前置条件：1、具有API调用权限；2、API接口正常可用；3、测试环境配置正确；

##### 操作步骤：1、构造包含applyEdgeSealCount字段的请求参数；2、调用/esign-docs/v1/sealcontrols/physicalSealApply接口；3、检查接口响应；4、验证创建的流程信息；

##### ER-预期结果：1、接口成功接收applyEdgeSealCount参数；2、返回成功响应；3、创建的流程包含骑缝用印次数信息；4、字段值与请求参数一致；

#### P1TL-创建物理用印流程接口骑缝用印次数校验验证

##### PD-前置条件：1、具有API调用权限；2、API接口正常可用；

##### 操作步骤：1、设置applyEdgeSealCount为0；2、调用接口；3、设置applyEdgeSealCount为1000；4、调用接口；5、设置applyEdgeSealCount为有效值；6、调用接口；

##### ER-预期结果：1、设置0时返回校验错误"用印次数为1～999之间的整数"；2、设置1000时返回相同校验错误；3、设置有效值时接口调用成功；4、校验逻辑与页面一致；

#### P1TL-创建物理用印流程接口总次数校验验证

##### PD-前置条件：1、具有API调用权限；2、API接口正常可用；

##### 操作步骤：1、设置applyCount为0，applyEdgeSealCount为0；2、调用接口；3、设置applyCount为2，applyEdgeSealCount为3；4、调用接口；

##### ER-预期结果：1、总次数为0时返回校验错误；2、总次数大于0时接口调用成功；3、校验提示信息准确；4、业务逻辑正确；

### 查询物理用印流程详情接口

#### P1TL-查询物理用印流程详情接口骑缝用印字段返回验证

##### PD-前置条件：1、存在包含骑缝用印次数的物理用印流程；2、具有API调用权限；

##### 操作步骤：1、调用/esign-docs/v1/sealcontrols/physicalSealFlowDetail接口；2、传入流程ID；3、检查返回的sealDetailsInfos信息；4、验证骑缝用印相关字段；

##### ER-预期结果：1、返回applyEdgeSealCount字段；2、返回usedEdgeSealCount字段；3、字段值准确反映申请和已用次数；4、数据结构完整；

#### P1TL-查询物理用印流程详情接口存量数据兼容性验证

##### PD-前置条件：1、存在功能上线前的存量物理用印流程；2、具有API调用权限；

##### 操作步骤：1、调用查询详情接口；2、传入存量流程ID；3、检查返回的骑缝用印字段；4、验证数据兼容性；

##### ER-预期结果：1、存量数据的applyEdgeSealCount返回0；2、存量数据的usedEdgeSealCount返回0；3、接口调用正常；4、数据兼容性良好；

### 物理用印流程回调接口

#### P1TL-物理用印流程回调接口骑缝用印字段推送验证

##### PD-前置条件：1、配置了回调地址；2、存在骑缝用印流程；3、发生了用印事件；

##### 操作步骤：1、执行骑缝用印操作；2、触发用印人用印通知回调；3、检查回调数据；4、验证骑缝用印字段；

##### ER-预期结果：1、回调数据包含applyEdgeSealCount字段；2、回调数据包含usedEdgeSealCount字段；3、字段值准确反映当前状态；4、回调数据完整；

#### P1TL-物理用印任务完成通知回调骑缝用印字段验证

##### PD-前置条件：1、配置了回调地址；2、存在骑缝用印任务；3、任务即将完成；

##### 操作步骤：1、完成所有用印操作；2、触发任务完成通知回调；3、检查回调数据；4、验证最终的次数统计；

##### ER-预期结果：1、回调数据包含最终的骑缝用印次数统计；2、applyEdgeSealCount显示申请次数；3、usedEdgeSealCount显示实际使用次数；4、数据准确无误；

## 数据迁移测试

### 存量数据处理

#### P1TL-存量物理用印数据骑缝用印字段初始化验证

##### PD-前置条件：1、系统中存在功能上线前的存量数据；2、具有数据查看权限；

##### 操作步骤：1、查看存量物理用印申请；2、检查骑缝用印次数字段；3、验证数据初始化情况；4、对比新数据的处理；

##### ER-预期结果：1、存量数据骑缝用印次数初始化为0；2、数据结构完整；3、不影响原有功能；4、数据迁移无损失；

#### P1TL-存量数据与新数据混合查询验证

##### PD-前置条件：1、系统中同时存在存量数据和新数据；2、具有查询权限；

##### 操作步骤：1、执行混合数据查询；2、检查查询结果；3、验证数据显示一致性；4、测试排序和筛选功能；

##### ER-预期结果：1、存量数据和新数据都能正常查询；2、骑缝用印字段显示一致；3、查询功能正常；4、数据兼容性良好；

## 集成测试

### 端到端流程测试

#### P1TL-完整骑缝用印端到端流程验证

##### PD-前置条件：1、云玺章桶设备在线；2、用户具有完整权限；3、系统功能正常；

##### 操作步骤：1、发起包含骑缝用印的申请；2、审批通过申请；3、执行授权用印；4、进行普通用印和骑缝用印；5、查看完整流程记录；6、验证API接口数据；

##### ER-预期结果：1、申请流程正常；2、审批流程正常；3、用印执行成功；4、次数统计准确；5、API数据一致；6、整个流程无缝衔接；

#### P1TL-多设备类型骑缝用印集成验证

##### PD-前置条件：1、系统中同时存在云玺和思格特设备；2、具有完整权限；

##### 操作步骤：1、分别为两种设备创建骑缝用印申请；2、执行审批流程；3、在不同设备上执行用印；4、对比用印结果和统计；5、验证设备差异化处理；

##### ER-预期结果：1、两种设备都能处理骑缝用印申请；2、用印执行方式符合设备特性；3、次数统计逻辑正确；4、设备兼容性良好；

## 用户体验测试

### 界面交互体验

#### P1TL-骑缝用印功能用户引导验证

##### PD-前置条件：1、用户首次使用骑缝用印功能；2、物理印控应用已安装云玺应用；

##### 操作步骤：1、进入发起物理用印页面；2、查看骑缝用印次数字段说明；3、尝试设置不同的次数值；4、观察系统提示和引导；

##### ER-预期结果：1、骑缝用印字段有清晰的说明；2、输入校验提示友好；3、用户能够轻松理解功能；4、操作引导合理；

#### P1TL-骑缝用印次数显示一致性验证

##### PD-前置条件：1、存在骑缝用印申请；2、用户具有相应权限；

##### 操作步骤：1、在申请页面查看次数设置；2、在详情页面查看次数显示；3、在审批页面查看次数信息；4、在授权页面查看次数信息；5、对比各页面显示一致性；

##### ER-预期结果：1、各页面骑缝用印次数显示一致；2、字段标签统一；3、数据格式一致；4、用户体验统一；

## 冒烟测试用例

### 核心功能验证

#### MYTL-安装云玺应用时骑缝用印字段显示验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、查看用印次数设置区域；3、检查字段显示情况；

##### ER-预期结果：1、显示普通用印次数字段；2、显示骑缝用印次数字段；3、两个字段都可正常设置；

#### MYTL-未安装云玺应用时骑缝用印字段隐藏验证

##### PD-前置条件：1、物理印控应用未安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、进入发起物理用印页面；2、查看用印次数设置区域；3、检查字段显示情况；

##### ER-预期结果：1、显示普通用印次数字段；2、隐藏骑缝用印次数字段；3、界面布局正常；

#### MYTL-骑缝用印次数有效范围基本验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、设置骑缝用印次数为0；2、尝试提交；3、设置有效范围数值；4、提交申请；

##### ER-预期结果：1、设置0时显示校验提示；2、设置有效值时提交成功；3、校验提示准确；

#### MYTL-总次数必须大于0基本验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起物理用印权限；

##### 操作步骤：1、设置普通和骑缝用印次数都为0；2、尝试提交；3、设置有效次数；4、提交申请；

##### ER-预期结果：1、总次数为0时提交失败；2、显示相应提示；3、设置有效次数后成功；

#### MYTL-详情页面骑缝次数显示基本验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、存在骑缝用印申请；

##### 操作步骤：1、进入物理用印详情页面；2、查看用印次数信息；3、检查字段显示；

##### ER-预期结果：1、显示普通用印次数；2、显示骑缝用印次数；3、数据准确；

#### MYTL-存量任务骑缝次数显示0次验证

##### PD-前置条件：1、存在功能上线前的存量任务；2、物理印控应用已安装云玺应用；

##### 操作步骤：1、查看存量任务详情；2、检查骑缝用印次数显示；

##### ER-预期结果：1、存量任务骑缝次数显示为0；2、普通用印次数正常显示；

#### MYTL-云玺设备骑缝用印执行基本验证

##### PD-前置条件：1、云玺章桶设备在线；2、存在骑缝用印申请；3、具有用印权限；

##### 操作步骤：1、执行用印操作；2、进行普通用印和骑缝用印；3、查看用印结果；

##### ER-预期结果：1、设备支持骑缝用印；2、次数统计正确；3、用印操作成功；

#### MYTL-思格特设备骑缝用印执行基本验证

##### PD-前置条件：1、思格特章桶设备在线；2、存在骑缝用印申请；3、具有用印权限；

##### 操作步骤：1、执行用印操作；2、在思格特设备上用印；3、查看次数统计；

##### ER-预期结果：1、设备支持用印操作；2、所有次数计入普通用印；3、总次数统计正确；

#### MYTL-总次数用完任务状态更新验证

##### PD-前置条件：1、存在骑缝用印申请；2、设备在线；3、具有用印权限；

##### 操作步骤：1、使用完所有普通用印次数；2、使用完所有骑缝用印次数；3、查看任务状态；

##### ER-预期结果：1、总次数用完后任务更新为已用印；2、无法继续用印；3、状态同步及时；

#### MYTL-创建物理用印API接口骑缝字段验证

##### PD-前置条件：1、具有API调用权限；2、API接口正常；

##### 操作步骤：1、构造包含applyEdgeSealCount字段的请求；2、调用创建接口；3、检查响应；

##### ER-预期结果：1、接口成功接收骑缝用印次数参数；2、返回成功响应；3、流程创建成功；

#### MYTL-查询物理用印详情API接口骑缝字段验证

##### PD-前置条件：1、存在骑缝用印流程；2、具有API调用权限；

##### 操作步骤：1、调用查询详情接口；2、检查返回数据；3、验证骑缝用印字段；

##### ER-预期结果：1、返回applyEdgeSealCount字段；2、返回usedEdgeSealCount字段；3、字段值准确；

#### MYTL-骑缝用印次数边界值基本验证

##### PD-前置条件：1、物理印控应用已安装云玺应用；2、具有发起权限；

##### 操作步骤：1、设置骑缝用印次数为1；2、提交申请；3、设置为999；4、提交申请；

##### ER-预期结果：1、设置1次申请成功；2、设置999次申请成功；3、边界值处理正确；

#### MYTL-设备兼容性基本验证

##### PD-前置条件：1、系统中存在云玺和思格特设备；2、存在骑缝用印申请；

##### 操作步骤：1、分别在两种设备上执行骑缝用印；2、对比处理方式；3、检查次数统计；

##### ER-预期结果：1、云玺设备区分普通和骑缝用印；2、思格特设备统一计入普通用印；3、兼容性处理正确；

## 线上验证用例

### 核心业务流程验证

#### PATL-骑缝用印完整业务流程验证

##### PD-前置条件：1、生产环境云玺章桶设备在线；2、用户具有完整权限；

##### 步骤一：发起包含骑缝用印次数的物理用印申请

##### 步骤二：审批通过用印申请

##### 步骤三：执行授权用印并进行骑缝用印操作

##### 步骤四：查看用印记录和次数统计

##### ER-预期结果1：申请和审批流程正常

##### 2：骑缝用印执行成功

##### 3：次数统计准确无误

#### PATL-不同设备类型骑缝用印兼容性验证

##### PD-前置条件：1、生产环境同时存在云玺和思格特设备；2、具有完整权限；

##### 步骤一：分别为两种设备创建骑缝用印申请

##### 步骤二：在不同设备上执行骑缝用印

##### 步骤三：对比用印结果和次数统计

##### ER-预期结果1：两种设备都能处理骑缝用印

##### 2：次数统计逻辑符合设备特性

##### 3：设备兼容性处理正确

#### PATL-云玺应用安装状态检测验证

##### PD-前置条件：1、生产环境中存在不同应用安装状态的系统；

##### 步骤一：在已安装云玺应用的系统中测试功能

##### 步骤二：在未安装云玺应用的系统中测试功能

##### 步骤三：验证字段显示和隐藏逻辑

##### ER-预期结果1：应用安装状态检测准确

##### 2：字段显示逻辑正确

##### 3：功能差异化处理合理

#### PATL-OpenAPI接口生产环境验证

##### PD-前置条件：1、生产环境API接口正常；2、具有API调用权限；

##### 步骤一：调用创建物理用印流程接口

##### 步骤二：调用查询流程详情接口

##### 步骤三：验证回调接口数据推送

##### ER-预期结果1：API接口功能正常

##### 2：骑缝用印字段处理正确

##### 3：数据传输准确完整

#### PATL-存量数据兼容性线上验证

##### PD-前置条件：1、生产环境中存在功能上线前的存量数据；

##### 步骤一：查看存量物理用印数据

##### 步骤二：验证骑缝用印字段显示

##### 步骤三：测试存量数据与新数据混合查询

##### ER-预期结果1：存量数据兼容性良好

##### 2：骑缝用印字段正确初始化

##### 3：数据查询功能正常

#### PATL-系统性能和稳定性验证

##### PD-前置条件：1、生产环境正常运行；2、有一定用户访问量；

##### 步骤一：在业务高峰期测试骑缝用印功能

##### 步骤二：执行批量骑缝用印操作

##### 步骤三：监控系统响应和稳定性

##### ER-预期结果1：高负载下功能正常

##### 2：响应时间符合要求

##### 3：系统运行稳定

#### PATL-数据安全性线上验证

##### PD-前置条件：1、生产环境安全配置正常；

##### 步骤一：测试骑缝用印数据传输安全性

##### 步骤二：验证数据存储安全性

##### 步骤三：检查数据访问控制

##### ER-预期结果1：数据传输安全可靠

##### 2：数据存储符合安全要求

##### 3：访问控制机制有效
