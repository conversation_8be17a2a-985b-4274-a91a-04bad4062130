# 云玺章桶支持远程锁定-测试用例

## 功能测试

### 远程锁定功能

#### P1TL-云玺章桶解锁状态下执行锁定操作验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有设备锁定权限；4、章桶处于解锁状态；5、设备当前未被使用；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、查看设备卡片上的锁定按钮显示；4、点击【锁定】按钮；5、查看二次确认弹窗内容；6、点击确认提交锁定操作；7、等待操作结果；8、查看设备状态更新；

##### ER-预期结果：1、设备卡片显示【锁定】按钮；2、二次确认弹窗说明锁定效果；3、调用云玺SDK的deviceLock指令；4、返回锁定成功结果；5、章桶状态更新为已锁定；6、UI卡片显示锁定标签；

#### P1TL-云玺章桶锁定状态下执行解锁操作验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有设备锁定权限；4、章桶处于锁定状态；5、设备当前未被使用；

##### 操作步骤：1、进入印控设备页面；2、选择已锁定的云玺章桶设备；3、查看设备卡片上的按钮显示；4、点击【解锁】按钮；5、查看二次确认弹窗内容；6、点击确认提交解锁操作；7、等待操作结果；8、查看设备状态更新；

##### ER-预期结果：1、设备卡片显示【解锁】按钮；2、二次确认弹窗说明解锁效果；3、调用云玺SDK的deviceLock解锁指令；4、返回解锁成功结果；5、章桶状态更新为已解锁；6、UI卡片移除锁定标签；

#### P1TL-云玺章桶出厂默认状态验证

##### PD-前置条件：1、用户已登录系统；2、新出厂的云玺章桶设备；3、设备首次连接系统；

##### 操作步骤：1、将新出厂的云玺章桶连接到系统；2、进入印控设备页面；3、查看新设备的状态显示；4、检查锁定按钮的显示状态；5、验证设备的初始锁定状态；

##### ER-预期结果：1、新出厂设备默认为解锁状态；2、设备卡片显示【锁定】按钮；3、UI卡片不显示锁定标签；4、设备可以正常进行用印操作；

#### P1TL-锁定状态章桶功能限制验证

##### PD-前置条件：1、云玺章桶设备已被锁定；2、用户具有用印权限；3、设备在线；

##### 操作步骤：1、尝试使用已锁定的章桶进行用印操作；2、尝试进行换章操作；3、观察系统的响应和提示；4、验证功能限制的有效性；

##### ER-预期结果：1、锁定状态下无法进行用印操作；2、锁定状态下无法进行换章操作；3、系统显示相应的限制提示；4、功能限制生效；

### 异常情况处理

#### P1TL-设备离线时执行锁定操作验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备离线；3、具有设备锁定权限；4、章桶处于解锁状态；

##### 操作步骤：1、进入印控设备页面；2、选择离线的云玺章桶设备；3、点击【锁定】按钮；4、查看系统提示信息；5、观察操作处理结果；

##### ER-预期结果：1、系统检测到设备离线状态；2、显示提示"设备不在线，将在章桶开机后锁定章桶"；3、锁定指令被记录待执行；4、设备上线后自动执行锁定操作；

#### P1TL-设备使用中时执行解锁操作验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有设备锁定权限；4、章桶处于锁定状态；5、设备正在被其他用户使用；

##### 操作步骤：1、进入印控设备页面；2、选择使用中的锁定章桶设备；3、点击【解锁】按钮；4、确认解锁操作；5、观察系统响应结果；

##### ER-预期结果：1、系统检测到设备使用中状态；2、显示提示"解锁失败：设备使用中"；3、解锁操作被拒绝；4、设备保持锁定状态；

#### P1TL-SDK调用失败时的错误处理验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有设备锁定权限；3、模拟SDK调用异常；

##### 操作步骤：1、模拟云玺SDK通信异常；2、执行锁定或解锁操作；3、观察系统错误处理；4、检查设备状态是否变化；

##### ER-预期结果：1、系统检测到SDK调用失败；2、显示相应的错误提示信息；3、设备状态保持不变；4、用户能够理解失败原因；

### 状态显示和UI交互

#### P1TL-锁定状态标签显示验证

##### PD-前置条件：1、系统中存在不同锁定状态的云玺章桶设备；2、用户具有查看权限；

##### 操作步骤：1、进入印控设备页面；2、查看已锁定设备的卡片显示；3、查看解锁设备的卡片显示；4、对比状态标签的显示差异；

##### ER-预期结果：1、已锁定设备卡片显示锁定标签；2、解锁设备卡片不显示锁定标签；3、状态标签清晰易识别；4、状态显示准确无误；

#### P1TL-锁定按钮状态切换验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有设备锁定权限；

##### 操作步骤：1、查看解锁状态设备的按钮显示；2、执行锁定操作；3、查看锁定后的按钮显示；4、执行解锁操作；5、查看解锁后的按钮显示；

##### ER-预期结果：1、解锁状态显示【锁定】按钮；2、锁定状态显示【解锁】按钮；3、按钮状态与设备状态同步；4、按钮切换及时准确；

#### P1TL-二次确认弹窗内容验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有设备锁定权限；

##### 操作步骤：1、点击【锁定】按钮；2、查看锁定确认弹窗内容；3、取消操作；4、点击【解锁】按钮；5、查看解锁确认弹窗内容；6、取消操作；

##### ER-预期结果：1、锁定确认弹窗说明锁定效果；2、解锁确认弹窗说明解锁效果；3、弹窗内容清晰易懂；4、取消操作不执行锁定/解锁；

## 边界测试

### 设备状态边界

#### P1TL-设备在线离线状态快速切换时锁定操作验证

##### PD-前置条件：1、云玺章桶设备状态可快速切换；2、用户正在执行锁定操作；

##### 操作步骤：1、设备在线时开始锁定操作；2、在操作过程中将设备切换为离线；3、观察系统处理结果；4、设备重新上线后检查状态；

##### ER-预期结果：1、状态切换时系统能正确识别；2、操作中断时给出合适提示；3、设备重新上线后状态正确；4、不会出现状态不一致；

#### P1TL-设备使用状态边界切换验证

##### PD-前置条件：1、云玺章桶设备在线；2、设备使用状态可变化；

##### 操作步骤：1、在设备空闲时尝试锁定操作；2、在操作过程中模拟设备被使用；3、观察系统处理结果；4、设备空闲后重新尝试操作；

##### ER-预期结果：1、空闲状态下操作正常执行；2、使用中状态下操作被阻止；3、状态变化时系统及时响应；4、操作结果符合预期；

## 异常测试

### 网络异常

#### P1TL-网络中断时锁定操作处理验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络连接正常；3、用户执行锁定操作；

##### 操作步骤：1、开始锁定操作；2、在操作过程中断开网络连接；3、观察系统处理结果；4、恢复网络连接后检查状态；

##### ER-预期结果：1、网络中断时系统检测到连接异常；2、显示相应的错误提示；3、操作被中断或重试；4、网络恢复后状态同步正确；

### 系统异常

#### P1TL-锁定服务异常时操作处理验证

##### PD-前置条件：1、云玺章桶设备在线；2、模拟锁定服务异常；

##### 操作步骤：1、模拟锁定服务不可用；2、执行锁定或解锁操作；3、观察系统错误处理；4、检查设备状态变化；

##### ER-预期结果：1、系统检测到服务异常；2、显示服务不可用提示；3、操作被拒绝执行；4、设备状态保持不变；

## 性能测试

### 响应时间测试

#### P1TL-锁定解锁操作响应时间验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络环境稳定；

##### 操作步骤：1、记录开始时间；2、执行锁定操作；3、等待操作完成；4、记录结束时间；5、计算响应时间；6、重复测试解锁操作；

##### ER-预期结果：1、锁定操作响应时间在5秒以内；2、解锁操作响应时间在5秒以内；3、响应时间稳定；4、用户体验良好；

### 并发性能测试

#### P1TL-多设备同时锁定操作性能验证

##### PD-前置条件：1、多台云玺章桶设备在线；2、具有锁定权限；

##### 操作步骤：1、同时对多台设备执行锁定操作；2、观察系统响应情况；3、检查每台设备的操作结果；4、监控系统资源使用；

##### ER-预期结果：1、所有设备锁定操作都能正常执行；2、系统响应时间不会显著增加；3、不会出现操作冲突；4、系统性能稳定；

## 安全测试

### 权限验证测试

#### P1TL-无锁定权限用户访问控制验证

##### PD-前置条件：1、创建无设备锁定权限的测试用户；

##### 操作步骤：1、使用无权限用户登录；2、尝试访问设备锁定功能；3、观察权限控制效果；

##### ER-预期结果：1、无权限用户无法看到锁定按钮；2、或显示权限不足提示；3、权限控制机制有效；4、不会泄露敏感功能；

### 操作安全测试

#### P1TL-二次确认机制安全性验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有锁定权限；

##### 操作步骤：1、点击锁定按钮；2、在确认弹窗中点击取消；3、验证操作是否执行；4、重新点击锁定并确认；5、验证操作执行结果；

##### ER-预期结果：1、取消操作时不执行锁定；2、确认操作时正常执行锁定；3、二次确认机制有效防止误操作；4、操作安全性得到保障；

## 兼容性测试

### 设备兼容性

#### P1TL-云玺章桶与其他设备锁定功能差异验证

##### PD-前置条件：1、系统中同时存在云玺章桶和其他类型设备；

##### 操作步骤：1、查看云玺章桶设备的功能按钮；2、查看其他类型设备的功能按钮；3、对比功能差异；4、验证功能限制正确性；

##### ER-预期结果：1、云玺章桶显示锁定功能按钮；2、其他设备不显示锁定功能；3、功能差异化处理正确；4、符合设备能力限制；

### 多端兼容性

#### P1TL-PC端和移动端锁定功能兼容性验证

##### PD-前置条件：1、PC端和移动端环境正常；2、具有锁定权限；

##### 操作步骤：1、在PC端测试锁定功能；2、在移动端测试相同功能；3、对比功能表现和界面显示；4、验证操作流程一致性；

##### ER-预期结果：1、PC端和移动端功能完整性一致；2、界面适配良好；3、操作流程相同；4、用户体验统一；

## 批量操作测试

### 批量锁定管理

#### P1TL-多设备批量锁定操作验证

##### PD-前置条件：1、系统中存在多台云玺章桶设备；2、设备均处于在线解锁状态；3、具有批量锁定权限；

##### 操作步骤：1、进入印控设备页面；2、选择多台云玺章桶设备；3、执行批量锁定操作；4、观察批量处理过程；5、检查每台设备的锁定结果；

##### ER-预期结果：1、支持批量选择设备；2、批量锁定操作能够正常执行；3、每台设备锁定都有相应提示；4、操作完成后状态正确更新；

#### P1TL-批量操作中部分设备异常处理验证

##### PD-前置条件：1、选择了多台设备进行批量锁定；2、其中部分设备离线或使用中；

##### 操作步骤：1、选择包含异常状态设备的批量操作；2、执行批量锁定；3、观察异常设备的处理结果；4、检查正常设备的操作结果；

##### ER-预期结果：1、正常设备成功执行锁定；2、异常设备显示相应错误提示；3、批量操作结果明确区分成功和失败；4、不会因部分失败影响整体操作；

## 状态同步测试

### 设备状态同步

#### P1TL-锁定状态跨系统同步验证

##### PD-前置条件：1、云玺章桶设备在多个系统模块中显示；2、设备锁定状态发生变化；

##### 操作步骤：1、在设备管理模块执行锁定操作；2、检查其他相关模块的状态显示；3、验证状态同步的及时性；4、测试状态同步的准确性；

##### ER-预期结果：1、锁定状态在所有模块中同步更新；2、状态同步及时准确；3、不会出现状态不一致；4、用户在任何模块都能看到正确状态；

#### P1TL-设备重启后状态保持验证

##### PD-前置条件：1、云玺章桶设备已被锁定；2、设备需要重启；

##### 操作步骤：1、确认设备锁定状态；2、重启云玺章桶设备；3、设备重新上线后检查状态；4、验证锁定功能是否保持；

##### ER-预期结果：1、设备重启后锁定状态保持不变；2、锁定功能继续生效；3、系统正确识别设备状态；4、状态持久化正常；

## 日志记录测试

### 操作日志记录

#### P1TL-锁定解锁操作日志记录验证

##### PD-前置条件：1、系统日志功能正常；2、具有锁定权限；3、云玺章桶设备在线；

##### 操作步骤：1、执行设备锁定操作；2、查看系统操作日志；3、执行设备解锁操作；4、再次查看操作日志；5、验证日志记录完整性；

##### ER-预期结果：1、锁定操作被完整记录在日志中；2、解锁操作被完整记录在日志中；3、日志包含操作时间、操作人、设备信息；4、日志记录准确详细；

#### P1TL-异常操作日志记录验证

##### PD-前置条件：1、系统日志功能正常；2、模拟各种异常情况；

##### 操作步骤：1、执行设备离线时的锁定操作；2、执行设备使用中的解锁操作；3、执行SDK调用失败的操作；4、查看异常操作日志记录；

##### ER-预期结果：1、异常操作被记录在日志中；2、日志包含异常原因和错误信息；3、日志有助于问题排查；4、异常日志记录完整；

## 集成测试

### 端到端流程测试

#### P1TL-完整锁定管理端到端流程验证

##### PD-前置条件：1、云玺章桶设备正常；2、用户具有完整权限；3、系统功能正常；

##### 操作步骤：1、查看设备初始状态；2、执行锁定操作；3、验证锁定后的功能限制；4、执行解锁操作；5、验证解锁后功能恢复；6、查看完整操作记录；

##### ER-预期结果：1、设备状态变化正确；2、功能限制生效和解除正常；3、操作记录完整准确；4、整个流程无缝衔接；

#### P1TL-锁定状态下用印限制集成验证

##### PD-前置条件：1、云玺章桶设备已锁定；2、用户具有用印权限；3、存在待用印文件；

##### 操作步骤：1、尝试发起物理用印申请；2、尝试执行授权用印操作；3、尝试进行应急用印；4、观察系统限制效果；5、解锁设备后重新尝试用印；

##### ER-预期结果：1、锁定状态下所有用印操作被阻止；2、系统显示设备锁定提示；3、解锁后用印功能恢复正常；4、功能集成正确；

## 用户体验测试

### 交互体验

#### P1TL-锁定操作用户引导验证

##### PD-前置条件：1、用户首次使用锁定功能；2、设备在线；

##### 操作步骤：1、查看锁定按钮的提示信息；2、点击锁定按钮查看确认弹窗；3、观察操作过程中的用户提示；4、检查操作完成后的反馈；

##### ER-预期结果：1、按钮功能说明清晰；2、确认弹窗内容易于理解；3、操作过程有适当的进度提示；4、操作结果反馈明确；

#### P1TL-锁定状态视觉标识验证

##### PD-前置条件：1、系统中存在不同锁定状态的设备；2、用户具有查看权限；

##### 操作步骤：1、查看锁定设备的视觉标识；2、查看解锁设备的视觉标识；3、对比标识的区分度；4、验证标识的一致性；

##### ER-预期结果：1、锁定状态有明显的视觉标识；2、标识易于识别和理解；3、不同状态区分明显；4、视觉设计统一美观；

## 冒烟测试用例

### 核心功能验证

#### MYTL-云玺章桶基本锁定功能验证

##### PD-前置条件：1、云玺章桶设备在线；2、设备处于解锁状态；3、具有锁定权限；

##### 操作步骤：1、进入印控设备页面；2、点击【锁定】按钮；3、确认锁定操作；4、查看锁定结果；

##### ER-预期结果：1、锁定操作成功执行；2、设备状态更新为已锁定；3、UI显示锁定标签；4、按钮变为【解锁】；

#### MYTL-云玺章桶基本解锁功能验证

##### PD-前置条件：1、云玺章桶设备在线；2、设备处于锁定状态；3、具有锁定权限；

##### 操作步骤：1、进入印控设备页面；2、点击【解锁】按钮；3、确认解锁操作；4、查看解锁结果；

##### ER-预期结果：1、解锁操作成功执行；2、设备状态更新为已解锁；3、UI移除锁定标签；4、按钮变为【锁定】；

#### MYTL-设备离线时锁定操作提示验证

##### PD-前置条件：1、云玺章桶设备离线；2、具有锁定权限；

##### 操作步骤：1、选择离线设备；2、点击【锁定】按钮；3、观察系统提示；

##### ER-预期结果：1、显示"设备不在线，将在章桶开机后锁定章桶"提示；2、操作被记录待执行；

#### MYTL-设备使用中时解锁操作限制验证

##### PD-前置条件：1、云玺章桶设备锁定且使用中；2、具有锁定权限；

##### 操作步骤：1、选择使用中的锁定设备；2、点击【解锁】按钮；3、确认操作；4、观察结果；

##### ER-预期结果：1、显示"解锁失败：设备使用中"提示；2、解锁操作被拒绝；3、设备保持锁定状态；

#### MYTL-锁定状态功能限制验证

##### PD-前置条件：1、云玺章桶设备已锁定；2、具有用印权限；

##### 操作步骤：1、尝试使用锁定设备进行用印；2、尝试进行换章操作；3、观察限制效果；

##### ER-预期结果：1、用印操作被阻止；2、换章操作被阻止；3、显示相应限制提示；

#### MYTL-出厂默认状态验证

##### PD-前置条件：1、新出厂的云玺章桶设备；

##### 操作步骤：1、查看新设备状态；2、检查按钮显示；3、验证初始状态；

##### ER-预期结果：1、设备默认为解锁状态；2、显示【锁定】按钮；3、无锁定标签显示；

#### MYTL-二次确认弹窗基本验证

##### PD-前置条件：1、云玺章桶设备在线；2、具有锁定权限；

##### 操作步骤：1、点击【锁定】按钮；2、查看确认弹窗；3、点击取消；4、验证操作结果；

##### ER-预期结果：1、显示锁定效果说明弹窗；2、取消操作不执行锁定；3、弹窗内容清晰易懂；

#### MYTL-锁定状态标签显示验证

##### PD-前置条件：1、存在锁定和解锁状态的设备；

##### 操作步骤：1、查看锁定设备卡片；2、查看解锁设备卡片；3、对比标签显示；

##### ER-预期结果：1、锁定设备显示锁定标签；2、解锁设备不显示标签；3、标签清晰易识别；

#### MYTL-锁定操作响应时间验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络正常；

##### 操作步骤：1、执行锁定操作；2、记录响应时间；3、验证用户体验；

##### ER-预期结果：1、操作响应时间在5秒以内；2、用户体验良好；3、操作流程顺畅；

#### MYTL-权限控制基本验证

##### PD-前置条件：1、无锁定权限的测试用户；

##### 操作步骤：1、使用无权限用户登录；2、查看设备管理页面；3、检查锁定功能访问；

##### ER-预期结果：1、无法看到锁定按钮；2、或显示权限不足提示；3、权限控制有效；

## 线上验证用例

### 核心业务流程验证

#### PATL-远程锁定完整业务流程验证

##### PD-前置条件：1、生产环境云玺章桶设备在线；2、用户具有锁定权限；

##### 步骤一：登录系统并进入印控设备页面

##### 步骤二：选择云玺章桶设备并执行锁定操作

##### 步骤三：验证锁定后的功能限制效果

##### 步骤四：执行解锁操作并验证功能恢复

##### ER-预期结果1：锁定和解锁操作流程完整顺畅

##### 2：功能限制和恢复机制正常

##### 3：设备状态变化准确及时

#### PATL-设备异常情况处理验证

##### PD-前置条件：1、生产环境中存在不同状态的设备；

##### 步骤一：测试离线设备的锁定操作处理

##### 步骤二：测试使用中设备的解锁操作处理

##### 步骤三：验证异常提示信息的准确性

##### ER-预期结果1：离线设备操作提示准确

##### 2：使用中设备操作被正确阻止

##### 3：异常处理机制有效

#### PATL-锁定状态下用印限制验证

##### PD-前置条件：1、生产环境设备已锁定；2、存在实际用印需求；

##### 步骤一：尝试发起物理用印申请

##### 步骤二：尝试执行授权用印操作

##### 步骤三：验证功能限制的有效性

##### 步骤四：解锁后验证功能恢复

##### ER-预期结果1：锁定状态下用印功能被有效阻止

##### 2：解锁后用印功能正常恢复

##### 3：功能集成正确无误

#### PATL-系统性能和稳定性验证

##### PD-前置条件：1、生产环境正常运行；2、有一定用户访问量；

##### 步骤一：在业务高峰期测试锁定功能

##### 步骤二：执行批量设备锁定操作

##### 步骤三：监控系统响应和稳定性

##### ER-预期结果1：高负载下功能正常

##### 2：批量操作性能稳定

##### 3：系统运行无异常

#### PATL-权限控制线上验证

##### PD-前置条件：1、生产环境权限配置正常；2、不同权限级别用户；

##### 步骤一：测试不同权限用户的功能访问

##### 步骤二：验证权限控制的严格性

##### 步骤三：检查敏感操作的保护机制

##### ER-预期结果1：权限控制严格有效

##### 2：无权限用户无法访问锁定功能

##### 3：操作安全性得到保障

#### PATL-状态同步和持久化验证

##### PD-前置条件：1、生产环境多模块系统；2、设备状态会发生变化；

##### 步骤一：在一个模块中执行锁定操作

##### 步骤二：检查其他模块的状态同步

##### 步骤三：验证设备重启后状态保持

##### ER-预期结果1：状态在各模块间同步准确

##### 2：状态持久化机制正常

##### 3：数据一致性得到保证

#### PATL-日志记录和审计验证

##### PD-前置条件：1、生产环境日志系统正常；

##### 步骤一：执行各种锁定解锁操作

##### 步骤二：检查操作日志记录完整性

##### 步骤三：验证异常操作的日志记录

##### ER-预期结果1：所有操作都有完整日志记录

##### 2：日志信息准确详细

##### 3：便于审计和问题排查
