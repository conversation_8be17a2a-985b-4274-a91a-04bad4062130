# 云玺章桶支持录入人脸、人脸识别用印-测试用例

## 功能测试

### 人脸录入管理

#### P1TL-云玺章桶首次录入人脸成功验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有指纹/人脸管理权限；4、用户在当前设备无人脸记录；5、设备人脸数量未达上限；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、点击【指纹/人脸】按钮；4、在人脸管理页面点击【录入人脸】；5、选择用户并点击【确定】；6、等待设备连接成功；7、在设备上录入人脸；8、等待录入结果；

##### ER-预期结果：1、成功跳转到连接设备页面；2、设备连接成功后提示"请在设备上录入人脸"；3、人脸录入成功后提示"人脸录入成功"；4、弹窗自动关闭并刷新人脸列表；5、人脸列表中显示新录入的人脸记录；

#### P1TL-云玺章桶重新录入人脸验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有指纹/人脸管理权限；4、用户在当前设备已有人脸记录；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、点击【指纹/人脸】按钮；4、在人脸管理页面点击【录入人脸】；5、选择已有人脸的用户并点击【确定】；6、在二次确认弹窗中点击【重新录入】；7、等待设备连接和人脸注销；8、在设备上录入新人脸；

##### ER-预期结果：1、显示二次确认提示"{用户姓名}已在当前设备录入人脸，是否重新录入人脸？"；2、点击重新录入后跳转连接设备页面；3、提示"正在注销已有人脸"；4、注销成功后提示"人脸注销成功"；5、提示"请在设备上录入人脸"；6、新人脸录入成功；

#### P1TL-云玺章桶人脸数量达上限录入验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有指纹/人脸管理权限；4、设备已录入128个人脸；

##### 操作步骤：1、进入印控设备页面；2、选择云玺章桶设备；3、点击【指纹/人脸】按钮；4、在人脸管理页面点击【录入人脸】；5、选择用户并点击【确定】；

##### ER-预期结果：1、系统检测到人脸数量已达上限；2、显示提示"该设备人脸数量已达上限，请先清除不常用的人脸"；3、录入操作被阻止；4、用户能够理解限制原因；

#### P1TL-思格特章桶人脸功能不显示验证

##### PD-前置条件：1、用户已登录系统；2、思格特章桶设备在线；3、具有指纹/人脸管理权限；

##### 操作步骤：1、进入印控设备页面；2、选择思格特章桶设备；3、查看设备卡面按钮显示；4、点击【指纹管理】按钮；5、查看管理页面功能；

##### ER-预期结果：1、思格特章桶卡面显示【指纹管理】按钮；2、不显示人脸相关功能；3、管理页面只有指纹管理功能；4、符合设备能力限制；

### 人脸注销管理

#### P1TL-云玺章桶手动注销人脸验证

##### PD-前置条件：1、用户已登录系统；2、云玺章桶设备在线；3、具有指纹/人脸管理权限；4、设备中存在人脸记录；

##### 操作步骤：1、进入人脸管理页面；2、选择要注销的人脸记录；3、点击【注销】按钮；4、在二次确认弹窗中点击【确定】；5、等待设备连接和注销处理；6、查看注销结果；

##### ER-预期结果：1、显示二次确认提示"确定注销用户人脸？"；2、点击确定后跳转连接设备页面；3、提示"正在注销已有人脸"；4、注销成功后提示"人脸注销成功"；5、弹窗自动关闭；6、人脸列表中该记录被移除；

#### P1TL-离职用户人脸记录显示状态验证

##### PD-前置条件：1、用户已登录系统；2、系统中存在离职用户的人脸记录；3、具有查看权限；

##### 操作步骤：1、进入人脸管理页面；2、查看人脸记录列表；3、检查离职用户记录的显示状态；4、验证用户状态字段显示；

##### ER-预期结果：1、离职用户的人脸记录仍然显示在列表中；2、用户状态字段显示为"离职"或相应状态；3、记录不会自动注销；4、需要手动操作才能注销；

### 指纹管理优化

#### P1TL-云玺章桶指纹列表用户状态显示验证

##### PD-前置条件：1、用户已登录系统；2、系统中存在不同状态用户的指纹记录；

##### 操作步骤：1、进入指纹管理页面；2、查看指纹记录列表；3、检查各用户状态显示；4、验证离职用户记录处理；

##### ER-预期结果：1、指纹记录列表显示用户状态字段；2、用户状态来源于管理平台组织用户状态；3、离职用户记录不自动注销；4、状态显示准确；

#### P1TL-思格特章桶指纹直接注销验证

##### PD-前置条件：1、用户已登录系统；2、思格特章桶设备；3、具有指纹管理权限；4、存在指纹记录；

##### 操作步骤：1、进入思格特章桶指纹管理页面；2、选择要注销的指纹记录；3、点击【注销】按钮；4、观察注销处理过程；

##### ER-预期结果：1、点击注销后直接执行注销操作；2、不需要连接设备页面；3、注销操作立即完成；4、指纹记录从列表中移除；

### 物理用印人脸验证

#### P1TL-发起物理用印设置人脸验证选项验证

##### PD-前置条件：1、用户已登录系统；2、具有发起物理用印权限；3、选择云玺章桶设备；

##### 操作步骤：1、进入发起物理用印页面；2、填写用印方信息；3、展开高级设置；4、查看验证方式选项；5、选择指纹/人脸验证；

##### ER-预期结果：1、高级设置中显示"指纹/人脸验证"选项；2、选项说明用印时需使用指纹或人脸解锁；3、当包含思格特设备时显示说明"思格特章桶只支持指纹验证"；4、选项可正常选择；

#### P1TL-物理用印审批表单人脸验证显示验证

##### PD-前置条件：1、存在待审批的物理用印申请；2、申请中设置了指纹/人脸验证；3、具有审批权限；

##### 操作步骤：1、进入待审批列表；2、选择物理用印申请；3、查看审批表单内容；4、检查验证方式显示；

##### ER-预期结果：1、审批表单中显示"指纹/人脸验证"字段；2、字段值正确显示申请时的设置；3、审批人能够清楚了解验证要求；4、表单显示完整准确；

#### P1TL-授权用印页面人脸验证显示验证

##### PD-前置条件：1、存在需要授权的物理用印；2、用印设置了指纹/人脸验证；3、具有授权权限；

##### 操作步骤：1、进入授权用印页面（PC端）；2、查看用印详情；3、检查验证方式显示；4、切换到H5端重复验证；

##### ER-预期结果：1、PC端和H5端都显示"指纹/人脸验证"字段；2、字段内容与申请时设置一致；3、授权人能够了解验证要求；4、多端显示一致；

### 授权用印人脸验证

#### P1TL-云玺章桶人脸验证授权用印验证

##### PD-前置条件：1、物理用印申请已审批通过；2、设置了指纹/人脸验证；3、用印人已录入人脸；4、云玺章桶设备在线；

##### 操作步骤：1、进入授权用印页面；2、点击授权用印；3、在设备上使用人脸验证；4、等待验证结果；5、查看用印授权结果；

##### ER-预期结果：1、系统向云玺SDK传入用印人信息和验证方式；2、设备成功识别用印人人脸；3、人脸验证通过后设备解锁；4、用印授权成功完成；5、生成相应的用印记录；

### 应急用印人脸验证

#### P1TL-应急用印人脸识别列表同步验证

##### PD-前置条件：1、设置了应急用印人员；2、应急用印人员已录入人脸；3、云玺章桶设备在线；

##### 操作步骤：1、修改应急用印人员配置；2、观察设备同步情况；3、在设备离线时进行人脸变更；4、设备重新上线后检查同步；

##### ER-预期结果：1、应急用印人员变化时及时同步到设备；2、人脸列表变化时及时同步到设备；3、设备离线时记录同步任务；4、设备上线后立即执行同步；

#### P1TL-人脸应急用印记录显示验证

##### PD-前置条件：1、应急用印人员已录入人脸；2、使用人脸进行应急用印；3、具有查看应急用印记录权限；

##### 操作步骤：1、使用人脸进行应急用印操作；2、进入应急用印记录列表；3、查看新增的用印记录；4、点击查看用印详情；5、检查验证方式显示；

##### ER-预期结果：1、应急用印记录成功生成；2、用印列表显示"人脸验证"；3、用印详情中用户验证方式显示"人脸验证"；4、记录信息准确完整；

## 边界测试

### 人脸数量边界

#### P1TL-云玺章桶人脸数量127到128边界验证

##### PD-前置条件：1、云玺章桶设备已录入127个人脸；2、设备在线；3、具有管理权限；

##### 操作步骤：1、录入第128个人脸；2、验证录入成功；3、尝试录入第129个人脸；4、观察系统处理结果；

##### ER-预期结果：1、第128个人脸成功录入；2、尝试录入第129个人脸时被阻止；3、显示数量上限提示；4、系统行为符合预期；

### 设备状态边界

#### P1TL-人脸录入过程中设备离线处理验证

##### PD-前置条件：1、用户正在进行人脸录入操作；2、设备连接正常；

##### 操作步骤：1、开始人脸录入流程；2、在录入过程中断开设备连接；3、观察系统处理结果；4、重新连接设备；5、检查录入状态；

##### ER-预期结果：1、设备离线时系统检测到连接中断；2、显示相应的错误提示；3、录入流程被中断；4、设备重新连接后可以重新开始录入；

## 异常测试

### 设备异常

#### P1TL-云玺章桶人脸录入失败处理验证

##### PD-前置条件：1、设备在线但人脸录入功能异常；2、用户尝试录入人脸；

##### 操作步骤：1、模拟设备人脸录入功能异常；2、执行人脸录入操作；3、观察系统错误处理；4、检查用户提示信息；

##### ER-预期结果：1、系统检测到录入失败；2、显示明确的错误提示信息；3、不会导致系统崩溃；4、用户能够理解失败原因并重试；

### 系统异常

#### P1TL-人脸数据同步失败处理验证

##### PD-前置条件：1、应急用印人脸数据需要同步；2、网络或服务异常导致同步失败；

##### 操作步骤：1、模拟数据同步服务异常；2、触发人脸数据同步；3、观察系统处理结果；4、检查重试机制；

##### ER-预期结果：1、系统检测到同步失败；2、记录失败日志；3、具有重试机制；4、服务恢复后能够自动重新同步；

## 性能测试

### 响应时间测试

#### P1TL-人脸录入响应时间验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络环境稳定；

##### 操作步骤：1、记录开始时间；2、执行人脸录入操作；3、等待录入完成；4、记录结束时间；5、计算总响应时间；

##### ER-预期结果：1、人脸录入总时间在30秒以内；2、设备连接时间在5秒以内；3、录入过程响应及时；4、用户体验良好；

### 并发性能测试

#### P1TL-多用户同时进行人脸操作性能验证

##### PD-前置条件：1、多台云玺章桶设备在线；2、多个用户账号；

##### 操作步骤：1、模拟多用户同时登录；2、同时进行人脸录入/注销操作；3、监控系统响应情况；4、检查操作结果；

##### ER-预期结果：1、所有用户操作都能正常完成；2、系统响应时间不会显著增加；3、不会出现操作冲突；4、系统性能稳定；

## 安全测试

### 人脸数据安全

#### P1TL-人脸数据传输安全性验证

##### PD-前置条件：1、人脸录入和验证功能正常；2、具有安全测试环境；

##### 操作步骤：1、监控人脸数据传输过程；2、检查数据加密情况；3、验证数据完整性；4、测试数据篡改检测；

##### ER-预期结果：1、人脸数据传输过程加密；2、数据完整性得到保护；3、具有篡改检测机制；4、符合安全规范要求；

### 权限验证测试

#### P1TL-无人脸管理权限用户访问控制验证

##### PD-前置条件：1、创建无人脸管理权限的测试用户；

##### 操作步骤：1、使用无权限用户登录；2、尝试访问人脸管理功能；3、观察权限控制效果；

##### ER-预期结果：1、无权限用户无法访问人脸管理功能；2、显示权限不足提示；3、权限控制机制有效；4、不会泄露敏感信息；

## 兼容性测试

### 设备兼容性

#### P1TL-云玺章桶与思格特章桶功能差异化验证

##### PD-前置条件：1、同时配置云玺章桶和思格特章桶；2、具有管理权限；

##### 操作步骤：1、分别访问两种设备的管理页面；2、对比功能按钮显示；3、验证功能可用性差异；4、检查提示信息准确性；

##### ER-预期结果：1、云玺章桶显示【指纹/人脸】功能；2、思格特章桶只显示【指纹管理】功能；3、功能限制提示准确；4、差异化处理正确；

### 多端兼容性

#### P1TL-PC端和H5端人脸功能兼容性验证

##### PD-前置条件：1、PC端和H5端环境正常；2、具有相应权限；

##### 操作步骤：1、在PC端测试人脸管理功能；2、在H5端测试相同功能；3、对比功能表现和界面显示；4、验证操作流程一致性；

##### ER-预期结果：1、PC端和H5端功能完整性一致；2、界面适配良好；3、操作流程相同；4、用户体验统一；

## 权限管理测试

### 权限菜单显示

#### P1TL-管理平台权限菜单按钮文案更新验证

##### PD-前置条件：1、用户具有管理平台访问权限；2、配置了章桶相关权限；

##### 操作步骤：1、登录管理平台；2、进入权限菜单配置页面；3、查找章桶相关权限按钮；4、检查按钮文案显示；

##### ER-预期结果：1、原【指纹管理】按钮文案更新为【指纹/人脸】；2、按钮权限配置功能正常；3、权限描述准确；4、配置界面显示正确；

#### P1TL-业务平台设备卡面按钮差异化显示验证

##### PD-前置条件：1、配置了指纹/人脸按钮权限；2、系统中同时存在云玺章桶和思格特章桶；

##### 操作步骤：1、进入业务平台印控设备页面；2、查看云玺章桶设备卡面；3、查看思格特章桶设备卡面；4、对比按钮显示差异；

##### ER-预期结果：1、云玺章桶卡面显示【指纹/人脸】按钮；2、思格特章桶卡面显示【指纹管理】按钮；3、按钮显示与设备能力匹配；4、权限控制生效；

## 批量操作测试

### 批量人脸管理

#### P1TL-批量人脸注销操作验证

##### PD-前置条件：1、云玺章桶设备中存在多个人脸记录；2、具有人脸管理权限；3、设备在线；

##### 操作步骤：1、进入人脸管理页面；2、选择多个人脸记录；3、执行批量注销操作；4、观察处理过程；5、检查注销结果；

##### ER-预期结果：1、支持批量选择人脸记录；2、批量注销操作能够正常执行；3、每个人脸注销都有相应提示；4、操作完成后列表正确更新；

#### P1TL-离职用户人脸批量处理验证

##### PD-前置条件：1、系统中存在多个离职用户的人脸记录；2、具有管理权限；

##### 操作步骤：1、筛选显示离职用户的人脸记录；2、批量选择离职用户记录；3、执行批量注销操作；4、验证处理结果；

##### ER-预期结果：1、能够筛选出离职用户记录；2、支持批量选择和操作；3、批量注销功能正常；4、离职用户人脸记录被正确清理；

## 数据迁移测试

### 历史数据处理

#### P1TL-指纹管理功能升级后历史数据兼容性验证

##### PD-前置条件：1、系统中存在升级前的指纹管理数据；2、功能升级为指纹/人脸管理；

##### 操作步骤：1、查看升级前的指纹记录；2、验证升级后数据完整性；3、检查功能兼容性；4、测试原有指纹功能；

##### ER-预期结果：1、历史指纹数据完整保留；2、原有指纹功能正常使用；3、新增人脸功能不影响指纹功能；4、数据迁移无损失；

#### P1TL-用印记录中验证方式字段更新验证

##### PD-前置条件：1、系统中存在历史用印记录；2、记录中包含指纹验证信息；

##### 操作步骤：1、查看历史用印记录；2、检查验证方式字段显示；3、验证新记录的字段显示；4、对比字段变化；

##### ER-预期结果：1、历史记录中验证方式字段正确显示；2、新记录支持指纹/人脸验证显示；3、字段更新不影响历史数据；4、显示逻辑正确；

## 集成测试

### 端到端流程测试

#### P1TL-完整人脸用印端到端流程验证

##### PD-前置条件：1、云玺章桶设备在线；2、用户具有完整权限；3、系统功能正常；

##### 操作步骤：1、录入用户人脸；2、发起物理用印申请并设置人脸验证；3、审批通过申请；4、执行授权用印并使用人脸验证；5、查看完整用印记录；

##### ER-预期结果：1、人脸录入成功；2、用印申请流程正常；3、人脸验证授权成功；4、用印记录完整准确；5、整个流程无缝衔接；

#### P1TL-应急用印人脸验证端到端流程验证

##### PD-前置条件：1、配置了应急用印人员；2、应急用印人员已录入人脸；3、云玺章桶设备在线；

##### 操作步骤：1、配置应急用印人员；2、同步人脸数据到设备；3、使用人脸进行应急用印；4、查看应急用印记录；5、验证记录准确性；

##### ER-预期结果：1、应急用印人员配置成功；2、人脸数据同步正常；3、人脸应急用印成功；4、应急用印记录准确显示人脸验证；5、流程完整可追溯；

## 用户体验测试

### 交互体验

#### P1TL-人脸录入过程用户引导验证

##### PD-前置条件：1、用户首次使用人脸录入功能；2、设备在线；

##### 操作步骤：1、开始人脸录入流程；2、观察每个步骤的用户提示；3、检查引导信息的清晰度；4、验证异常情况的提示；

##### ER-预期结果：1、每个步骤都有清晰的用户提示；2、引导信息易于理解；3、异常情况有明确的错误提示；4、用户能够顺利完成操作；

#### P1TL-人脸管理页面用户体验验证

##### PD-前置条件：1、人脸管理页面功能正常；2、存在多条人脸记录；

##### 操作步骤：1、进入人脸管理页面；2、测试页面加载速度；3、验证列表显示效果；4、测试操作按钮响应；5、检查页面布局合理性；

##### ER-预期结果：1、页面加载速度快；2、列表显示清晰美观；3、操作按钮响应及时；4、页面布局合理易用；5、整体用户体验良好；

## 冒烟测试用例

### 核心功能验证

#### MYTL-云玺章桶人脸录入基本功能验证

##### PD-前置条件：1、云玺章桶设备在线；2、用户具有人脸管理权限；3、用户在设备上无人脸记录；

##### 操作步骤：1、进入人脸管理页面；2、点击录入人脸；3、选择用户并确定；4、在设备上录入人脸；

##### ER-预期结果：1、成功跳转连接设备页面；2、人脸录入成功；3、提示"人脸录入成功"；4、人脸列表更新；

#### MYTL-云玺章桶人脸注销基本功能验证

##### PD-前置条件：1、云玺章桶设备在线；2、存在人脸记录；3、具有管理权限；

##### 操作步骤：1、进入人脸管理页面；2、点击注销按钮；3、确认注销操作；4、等待注销完成；

##### ER-预期结果：1、显示确认提示；2、注销操作成功；3、提示"人脸注销成功"；4、记录从列表移除；

#### MYTL-思格特章桶人脸功能限制验证

##### PD-前置条件：1、思格特章桶设备；2、具有管理权限；

##### 操作步骤：1、查看思格特章桶设备卡面；2、点击管理按钮；3、检查功能列表；

##### ER-预期结果：1、卡面显示【指纹管理】；2、不显示人脸相关功能；3、功能限制正确；

#### MYTL-物理用印人脸验证选项显示验证

##### PD-前置条件：1、具有发起用印权限；2、选择云玺章桶设备；

##### 操作步骤：1、进入发起物理用印页面；2、展开高级设置；3、查看验证方式选项；

##### ER-预期结果：1、显示"指纹/人脸验证"选项；2、包含思格特设备时显示限制说明；3、选项可正常选择；

#### MYTL-人脸验证授权用印基本验证

##### PD-前置条件：1、用印申请设置了人脸验证；2、用印人已录入人脸；3、云玺章桶在线；

##### 操作步骤：1、进入授权用印页面；2、执行授权用印；3、使用人脸验证；4、查看授权结果；

##### ER-预期结果：1、人脸验证成功；2、设备解锁用印；3、授权操作完成；4、生成用印记录；

#### MYTL-应急用印人脸验证记录显示验证

##### PD-前置条件：1、使用人脸进行应急用印；2、具有记录查看权限；

##### 操作步骤：1、查看应急用印记录列表；2、检查验证方式显示；3、查看用印详情；

##### ER-预期结果：1、列表显示"人脸验证"；2、详情显示"人脸验证"；3、记录信息准确；

#### MYTL-人脸数量上限限制验证

##### PD-前置条件：1、云玺章桶已录入128个人脸；2、具有管理权限；

##### 操作步骤：1、尝试录入新的人脸；2、观察系统提示；

##### ER-预期结果：1、显示数量上限提示；2、录入操作被阻止；3、提示信息准确；

#### MYTL-离职用户人脸记录状态显示验证

##### PD-前置条件：1、存在离职用户的人脸记录；

##### 操作步骤：1、查看人脸管理列表；2、检查离职用户记录显示；

##### ER-预期结果：1、离职用户记录仍显示；2、用户状态显示"离职"；3、记录不自动注销；

#### MYTL-权限菜单按钮文案更新验证

##### PD-前置条件：1、具有管理平台访问权限；

##### 操作步骤：1、进入权限菜单配置；2、查看章桶相关权限按钮；

##### ER-预期结果：1、按钮文案显示【指纹/人脸】；2、权限配置功能正常；

#### MYTL-人脸录入响应时间基本验证

##### PD-前置条件：1、云玺章桶设备在线；2、网络正常；

##### 操作步骤：1、执行人脸录入操作；2、记录响应时间；3、验证用户体验；

##### ER-预期结果：1、录入时间在30秒以内；2、连接时间在5秒以内；3、用户体验良好；

## 线上验证用例

### 核心业务流程验证

#### PATL-人脸录入完整业务流程验证

##### PD-前置条件：1、生产环境云玺章桶设备在线；2、用户具有人脸管理权限；

##### 步骤一：登录系统并进入印控设备页面

##### 步骤二：选择云玺章桶并进入人脸管理

##### 步骤三：执行人脸录入操作并在设备上完成录入

##### 步骤四：验证人脸录入结果和列表更新

##### ER-预期结果1：人脸录入流程完整顺畅

##### 2：设备连接和录入功能正常

##### 3：人脸列表正确更新显示

#### PATL-人脸验证用印完整流程验证

##### PD-前置条件：1、生产环境设备正常；2、用印人已录入人脸；3、具有完整用印权限；

##### 步骤一：发起物理用印申请并设置人脸验证

##### 步骤二：审批通过用印申请

##### 步骤三：执行授权用印并使用人脸验证

##### 步骤四：查看用印记录和验证方式显示

##### ER-预期结果1：用印申请和审批流程正常

##### 2：人脸验证授权成功

##### 3：用印记录准确显示人脸验证

#### PATL-应急用印人脸验证流程验证

##### PD-前置条件：1、生产环境配置了应急用印；2、应急用印人员已录入人脸；

##### 步骤一：配置应急用印人员并同步人脸数据

##### 步骤二：使用人脸进行应急用印操作

##### 步骤三：查看应急用印记录和验证方式

##### ER-预期结果1：应急用印人脸数据同步正常

##### 2：人脸应急用印功能正常

##### 3：应急记录正确显示人脸验证

#### PATL-设备兼容性线上验证

##### PD-前置条件：1、生产环境同时存在云玺章桶和思格特章桶；

##### 步骤一：分别测试两种设备的管理功能

##### 步骤二：验证功能差异化显示

##### 步骤三：测试各自的用印功能

##### ER-预期结果1：云玺章桶人脸功能正常

##### 2：思格特章桶只显示指纹功能

##### 3：设备差异化处理正确

#### PATL-系统性能和稳定性验证

##### PD-前置条件：1、生产环境正常运行；2、有一定用户访问量；

##### 步骤一：在业务高峰期测试人脸功能

##### 步骤二：执行批量人脸管理操作

##### 步骤三：监控系统响应和稳定性

##### ER-预期结果1：高负载下功能正常

##### 2：响应时间符合要求

##### 3：系统运行稳定

#### PATL-权限控制线上验证

##### PD-前置条件：1、生产环境权限配置正常；2、不同权限级别用户；

##### 步骤一：测试不同权限用户的功能访问

##### 步骤二：验证权限控制有效性

##### 步骤三：检查敏感操作的权限保护

##### ER-预期结果1：权限控制严格有效

##### 2：无权限用户无法访问相关功能

##### 3：敏感操作得到保护

#### PATL-数据安全性线上验证

##### PD-前置条件：1、生产环境安全配置正常；

##### 步骤一：测试人脸数据传输安全性

##### 步骤二：验证数据存储安全性

##### 步骤三：检查数据访问控制

##### ER-预期结果1：人脸数据传输加密

##### 2：数据存储安全可靠

##### 3：数据访问控制有效
